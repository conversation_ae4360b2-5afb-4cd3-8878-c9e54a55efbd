import { <PERSON>, CardContent, CardHeader, CardTitle } from "@/components/ui/card";
import { useQuery } from "@tanstack/react-query";
import { api, type Antecipacao } from "@/lib/api";
import { Table, TableBody, TableCell, TableHead, TableHeader, TableRow } from "@/components/ui/table";
import { createFileRoute } from "@tanstack/react-router";

async function fetchProfissionalAntecipacoes(): Promise<Antecipacao[]> {
  return api.get("/profissionais/me/antecipacoes");
}

function ProfissionalAntecipacoes() {
  const { data: antecipacoes, isLoading } = useQuery({
    queryKey: ["profissional-antecipacoes"],
    queryFn: fetchProfissionalAntecipacoes,
  });

  return (
    <div className="space-y-6">
      <h1 className="text-3xl font-bold">Minhas Antecipações</h1>
      <Card>
        <CardHeader>
          <CardTitle>Histórico de Antecipações</CardTitle>
        </CardHeader>
        <CardContent>
          {isLoading ? (
            <p>Carregando...</p>
          ) : (
            <Table>
              <TableHeader>
                <TableRow>
                  <TableHead>Valor Solicitado</TableHead>
                  <TableHead>Valor Aprovado</TableHead>
                  <TableHead>Status</TableHead>
                  <TableHead>Data</TableHead>
                </TableRow>
              </TableHeader>
              <TableBody>
                {antecipacoes?.map((antecipacao) => (
                  <TableRow key={antecipacao.id}>
                    <TableCell>
                      {new Intl.NumberFormat("pt-BR", { style: "currency", currency: "BRL" }).format(
                        antecipacao.valorSolicitado
                      )}
                    </TableCell>
                    <TableCell>
                      {antecipacao.valorAprovado
                        ? new Intl.NumberFormat("pt-BR", { style: "currency", currency: "BRL" }).format(
                            antecipacao.valorAprovado
                          )
                        : "-"}
                    </TableCell>
                    <TableCell>{antecipacao.status}</TableCell>
                    <TableCell>{new Date(antecipacao.createdAt).toLocaleDateString()}</TableCell>
                  </TableRow>
                ))}
              </TableBody>
            </Table>
          )}
        </CardContent>
      </Card>
    </div>
  );
}

export const Route = createFileRoute("/profissional/antecipacoes")({ component: ProfissionalAntecipacoes });
