import { z } from "zod";
import { prisma, withAudit } from "../../lib/prisma";
import {
  createLocalDate,
  toISOString,
  parseUTCDate,
  getMesFromDate,
  getAnoFromDate,
  getCurrentYear,
  formatDateToDateTime,
} from "@shared/date";
import {
  createLocalDateInTimezone,
  getCurrentDateInTimezone,
  parseISOInTimezone,
  getStartOfMonthInTimezone,
  getEndOfMonthInTimezone,
} from "@shared/date";
import {
  createPlantaoSchema,
  updatePlantaoSchema,
  plantaoQuerySchema,
  type CreatePlantaoInput,
  type UpdatePlantaoInput,
  type PlantaoQuery,
} from "../../schemas/plantao.schema";
import type { FastifyTypedInstance, FastifyTypedRequest } from "@/types";
import { authorize } from "@/middlewares/auth.middleware";

export async function disponiveisAntecipacaoRouters(fastify: FastifyTypedInstance) {
  // Listar plantões com fechamentos aprovados disponíveis para antecipação
  fastify.get<{
    Querystring: {
      page?: string;
      limit?: string;
      dataInicial?: string;
      dataFinal?: string;
      search?: string;
    };
  }>(
    "/plantoes/disponiveis-antecipacao",
    withAudit(async (request, reply) => {
      const { page = 1, limit = 10, dataInicial, dataFinal, search } = request.query;
      const pageNum = Number(page);
      const limitNum = Number(limit);
      const skip = (pageNum - 1) * limitNum;
      const clienteId = request.clienteId;

      // Construir filtros
      const where: any = {
        // Tem fechamentos aprovados
        fechamentos: {
          some: {
            status: "APROVADO",
            // Que não tenham antecipação associada ainda
            antecipacaoId: null,
          },
        },
        clienteId,
      };

      // Filtros por data do plantão
      if (dataInicial || dataFinal) {
        const dateFilter: any = {};
        if (dataInicial) {
          dateFilter.gte = parseUTCDate(dataInicial);
        }
        if (dataFinal) {
          dateFilter.lte = parseUTCDate(dataFinal);
        }
        where.dataInicial = dateFilter;
      }

      // Filtro de busca por texto
      if (search) {
        where.OR = [
          {
            profissional: {
              usuario: {
                nome: {
                  contains: search,
                },
              },
            },
          },
          {
            cliente: {
              nome: {
                contains: search,
              },
            },
          },
          {
            localAtendimento: {
              nome: {
                contains: search,
              },
            },
          },
        ];
      }

      const [plantoes, total] = await Promise.all([
        prisma.plantao.findMany({
          where,
          skip,
          take: limitNum,
          include: {
            profissional: {
              include: {
                usuario: {
                  select: {
                    nome: true,
                    cpf: true,
                  },
                },
              },
            },
            cliente: true,
            localAtendimento: true,
            fechamentos: {
              where: {
                status: "APROVADO",
                antecipacaoId: null,
              },
              select: {
                id: true,
                totalValor: true,
                totalHoras: true,
                diasTrabalhados: true,
                status: true,
              },
            },
            _count: {
              select: {
                fechamentos: {
                  where: {
                    status: "APROVADO",
                    antecipacaoId: null,
                  },
                },
              },
            },
          },
          orderBy: { createdAt: "desc" },
        }),
        prisma.plantao.count({ where }),
      ]);

      return reply.send({
        data: plantoes,
        meta: {
          page: pageNum,
          limit: limitNum,
          total,
          totalPages: Math.ceil(total / limitNum),
        },
      });
    })
  );
}
