# AWS Lambda Deployment Guide

Esta API foi preparada para ser executada como uma função AWS Lambda usando Fastify com o adaptador `@fastify/aws-lambda`.

## Arquivos Criados

1. **`src/lambda.ts`** - Handler principal do Lambda com configurações adaptadas
2. **Scripts no `package.json`** - Comandos para build e deploy

### Deploy

```bash
# Build do projeto
yarn build:lambda

# Criar arquivo ZIP
cd apps/server
zip -r function.zip dist/ node_modules/ package.json

# Upload via AWS Console ou CLI
aws lambda create-function \
  --function-name gs2-api \
  --runtime nodejs20.x \
  --handler dist/lambda.handler \
  --zip-file fileb://function.zip \
  --role arn:aws:iam::YOUR_ACCOUNT:role/lambda-execution-role
```

## Variáveis de Ambiente

Configure as seguintes variáveis no Lambda:

- `DATABASE_URL` - URL de conexão MySQL
- `JWT_SECRET` - Secret para tokens JWT
- `FRONT_END_URL` - Origem permitida para CORS (ou "\*" para qualquer)
- `NODE_ENV` - Environment (development/staging/production)
- `LOG_LEVEL` - Nível de log (debug/info/warn/error)

## Configurações Importantes

### Timeout e Memória

- Timeout: 30 segundos (configurável)
- Memória: 512MB (ajuste conforme necessidade)

## Custos Estimados

- **Lambda**: ~$0.20 por milhão de requisições + tempo de execução
- **API Gateway**: ~$3.50 por milhão de chamadas
- **CloudWatch Logs**: ~$0.50/GB ingerido

### Cold Start Lento

- Use provisioned concurrency
- Minimize tamanho do bundle
- Considere usar Lambda@Edge para distribuição global
