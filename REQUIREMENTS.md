# Sistema de Gestão de Escalas e Antecipações

## Visão Geral

Sistema completo para gestão de escalas de profissionais de saúde, fechamentos e solicitações de antecipação financeira. Desenvolvido com foco em simplificar drasticamente o fluxo de trabalho e otimizar a experiência do usuário.

## Tecnologias Utilizadas

- **Banco de Dados**: knex quando possivel
- **UI Framework**: Tailwind CSS 4 com shadcn/ui
- **Componentes**: Biblioteca completa shadcn/ui com Lucide icons
- **Estado**: Zustand para client state, TanStack Query para server state

## Modelo de Dados

### Entidades Principais

1. **Cliente** (Unificado)
   - Representa tanto "Cliente" quanto "Instituição"
   - Campos: nome, tipo (PF/PJ), cpf/cnpj, email, telefone, endereço
   - Relacionamento: múltiplos Locais de Atendimento

2. **Profissional** (Unificado)
   - Representa "Profissionais Alocados" e "Novos Usuários"
   - Campos: nome, cpf, email, especialidade, dados bancários
   - Relacionamento: escalas, fechamentos, antecipações

3. **Local de Atendimento**
   - Associado a um Cliente
   - Campos: nome, endereço, latitude/longitude
   - Relacionamento: escalas

4. **Escala** (Substitui Atendimento e Contrato)
   - Cada escala é um contrato
   - Campos: mês/ano, tipo pagamento, valores, horários, percentual antecipação
   - Relacionamento: cliente, local, profissional, dias da escala

5. **Fechamento**
   - Verificação mensal das escalas
   - Campos: mês/ano, status, total horas/valor, observações
   - Relacionamento: escala, profissional, antecipações

6. **Antecipação**
   - Solicitações de antecipação financeira
   - Campos: valor solicitado/aprovado, percentual, status, tipo negócio
   - Relacionamento: fechamento, escala, profissional

## Funcionalidades Implementadas

### 1. Dashboard

- Visão geral com cards de estatísticas
- Lista de escalas recentes
- Ações rápidas para acesso rápido às funcionalidades

### 2. Módulo de Cadastros

- **Interface Unificada**: Cadastro completo em uma única tela
- **Clientes**: Gestão de clientes e instituições com múltiplos locais
- **Profissionais**: Cadastro rápido com dados bancários
- **Locais de Atendimento**: Associação com clientes
- **Importação em Massa**: Botão para importar dados via Excel

### 3. Módulo de Escalas

- **Criação Intuitiva**: Interface com abas para dados básicos, calendário e valores
- **Calendário Visual**: Seleção de dias de trabalho
- **Configuração de Horários**: Horários padrão e customizados
- **Tipos de Pagamento**: Plantonista, Mensalista, Coordenador, etc.
- **Replicação**: Opção para replicar escala para próximos meses
- **Filtros**: Por mês, ano, profissional ou cliente

### 4. Módulo de Fechamento

- **Verificação de Check-in/checkout**: Controle de presença
- **Aprovação Massiva**: Botão "selecionar todos" para aprovar múltiplos fechamentos
- **Filtros**: Por status, mês, ano, profissional ou cliente
- **Detalhes**: Visualização completa de cada fechamento
- **Status**: Pendente, Aprovado, Rejeitado

### 5. Módulo de Antecipação

- **Solicitação Simplificada**: Seleção de fechamentos disponíveis
- **Percentual Automático**: 80% para terceiros, 100% para negócios Aura
- **Seleção Massiva**: Botão "selecionar todos" para múltiplas solicitações
- **Geração de CCB**: Transformação de itens selecionados em único PDF
- **Botão Destacado**: "Solicitar Antecipação" em negrito e verde

## Destaques de UI/UX

### Navegação

- **Sidebar Colapsável**: Menu lateral com opção de recolher
- **Navegação Intuitiva**: Ícones claros e organização lógica
- **Responsividade**: Design mobile-first

### Interface

- **Cores Consistentes**: Uso do sistema de cores do Tailwind
- **Feedback Visual**: Estados claros para ações e status
- **Carregamento**: Indicadores para operações assíncronas
- **Acessibilidade**: Componentes semânticos e navegação por teclado

### Formulários

- **Validação em Tempo Real**: Feedback imediato para o usuário
- **Organização Lógica**: Agrupamento de campos relacionados
- **Botões Claros**: Ações bem definidas e visíveis

## Próximos Passos

**Funcionalidades Adicionais**

- OCR para leitura de documentos
- Exportação de relatórios
- Notificações em tempo real
- Integração com APIs externas
