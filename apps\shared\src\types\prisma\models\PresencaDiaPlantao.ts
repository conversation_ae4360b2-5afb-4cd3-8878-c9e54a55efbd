
/* !!! This is code generated by Prisma. Do not edit directly. !!! */
/* eslint-disable */
// @ts-nocheck 
/*
 * This file exports the `PresencaDiaPlantao` model and its related types.
 *
 * 🟢 You can import this file directly.
 */
import * as runtime from "@prisma/client/runtime/library"
import type * as $Enums from "../enums.ts"
import type * as Prisma from "../internal/prismaNamespace.ts"

/**
 * Model PresencaDiaPlantao
 * 
 */
export type PresencaDiaPlantaoModel = runtime.Types.Result.DefaultSelection<Prisma.$PresencaDiaPlantaoPayload>

export type AggregatePresencaDiaPlantao = {
  _count: PresencaDiaPlantaoCountAggregateOutputType | null
  _avg: PresencaDiaPlantaoAvgAggregateOutputType | null
  _sum: PresencaDiaPlantaoSumAggregateOutputType | null
  _min: PresencaDiaPlantaoMinAggregateOutputType | null
  _max: PresencaDiaPlantaoMaxAggregateOutputType | null
}

export type PresencaDiaPlantaoAvgAggregateOutputType = {
  id: number | null
  diaPlantaoId: number | null
  fechamentoId: number | null
  horasTrabalhadas: number | null
  valorEstimado: number | null
  tempoGlosado: number | null
}

export type PresencaDiaPlantaoSumAggregateOutputType = {
  id: number | null
  diaPlantaoId: number | null
  fechamentoId: number | null
  horasTrabalhadas: number | null
  valorEstimado: number | null
  tempoGlosado: number | null
}

export type PresencaDiaPlantaoMinAggregateOutputType = {
  id: number | null
  diaPlantaoId: number | null
  fechamentoId: number | null
  horaEntrada: Date | null
  horaSaida: Date | null
  intervalo: string | null
  horasTrabalhadas: number | null
  valorEstimado: number | null
  status: string | null
  tempoGlosado: number | null
  justificativaGlosa: string | null
  observacao: string | null
  createdAt: Date | null
  updatedAt: Date | null
}

export type PresencaDiaPlantaoMaxAggregateOutputType = {
  id: number | null
  diaPlantaoId: number | null
  fechamentoId: number | null
  horaEntrada: Date | null
  horaSaida: Date | null
  intervalo: string | null
  horasTrabalhadas: number | null
  valorEstimado: number | null
  status: string | null
  tempoGlosado: number | null
  justificativaGlosa: string | null
  observacao: string | null
  createdAt: Date | null
  updatedAt: Date | null
}

export type PresencaDiaPlantaoCountAggregateOutputType = {
  id: number
  diaPlantaoId: number
  fechamentoId: number
  horaEntrada: number
  horaSaida: number
  intervalo: number
  horasTrabalhadas: number
  valorEstimado: number
  status: number
  historicoAprovacoes: number
  tempoGlosado: number
  justificativaGlosa: number
  observacao: number
  metadata: number
  createdAt: number
  updatedAt: number
  _all: number
}


export type PresencaDiaPlantaoAvgAggregateInputType = {
  id?: true
  diaPlantaoId?: true
  fechamentoId?: true
  horasTrabalhadas?: true
  valorEstimado?: true
  tempoGlosado?: true
}

export type PresencaDiaPlantaoSumAggregateInputType = {
  id?: true
  diaPlantaoId?: true
  fechamentoId?: true
  horasTrabalhadas?: true
  valorEstimado?: true
  tempoGlosado?: true
}

export type PresencaDiaPlantaoMinAggregateInputType = {
  id?: true
  diaPlantaoId?: true
  fechamentoId?: true
  horaEntrada?: true
  horaSaida?: true
  intervalo?: true
  horasTrabalhadas?: true
  valorEstimado?: true
  status?: true
  tempoGlosado?: true
  justificativaGlosa?: true
  observacao?: true
  createdAt?: true
  updatedAt?: true
}

export type PresencaDiaPlantaoMaxAggregateInputType = {
  id?: true
  diaPlantaoId?: true
  fechamentoId?: true
  horaEntrada?: true
  horaSaida?: true
  intervalo?: true
  horasTrabalhadas?: true
  valorEstimado?: true
  status?: true
  tempoGlosado?: true
  justificativaGlosa?: true
  observacao?: true
  createdAt?: true
  updatedAt?: true
}

export type PresencaDiaPlantaoCountAggregateInputType = {
  id?: true
  diaPlantaoId?: true
  fechamentoId?: true
  horaEntrada?: true
  horaSaida?: true
  intervalo?: true
  horasTrabalhadas?: true
  valorEstimado?: true
  status?: true
  historicoAprovacoes?: true
  tempoGlosado?: true
  justificativaGlosa?: true
  observacao?: true
  metadata?: true
  createdAt?: true
  updatedAt?: true
  _all?: true
}

export type PresencaDiaPlantaoAggregateArgs<ExtArgs extends runtime.Types.Extensions.InternalArgs = runtime.Types.Extensions.DefaultArgs> = {
  /**
   * Filter which PresencaDiaPlantao to aggregate.
   */
  where?: Prisma.PresencaDiaPlantaoWhereInput
  /**
   * {@link https://www.prisma.io/docs/concepts/components/prisma-client/sorting Sorting Docs}
   * 
   * Determine the order of PresencaDiaPlantaos to fetch.
   */
  orderBy?: Prisma.PresencaDiaPlantaoOrderByWithRelationInput | Prisma.PresencaDiaPlantaoOrderByWithRelationInput[]
  /**
   * {@link https://www.prisma.io/docs/concepts/components/prisma-client/pagination#cursor-based-pagination Cursor Docs}
   * 
   * Sets the start position
   */
  cursor?: Prisma.PresencaDiaPlantaoWhereUniqueInput
  /**
   * {@link https://www.prisma.io/docs/concepts/components/prisma-client/pagination Pagination Docs}
   * 
   * Take `±n` PresencaDiaPlantaos from the position of the cursor.
   */
  take?: number
  /**
   * {@link https://www.prisma.io/docs/concepts/components/prisma-client/pagination Pagination Docs}
   * 
   * Skip the first `n` PresencaDiaPlantaos.
   */
  skip?: number
  /**
   * {@link https://www.prisma.io/docs/concepts/components/prisma-client/aggregations Aggregation Docs}
   * 
   * Count returned PresencaDiaPlantaos
  **/
  _count?: true | PresencaDiaPlantaoCountAggregateInputType
  /**
   * {@link https://www.prisma.io/docs/concepts/components/prisma-client/aggregations Aggregation Docs}
   * 
   * Select which fields to average
  **/
  _avg?: PresencaDiaPlantaoAvgAggregateInputType
  /**
   * {@link https://www.prisma.io/docs/concepts/components/prisma-client/aggregations Aggregation Docs}
   * 
   * Select which fields to sum
  **/
  _sum?: PresencaDiaPlantaoSumAggregateInputType
  /**
   * {@link https://www.prisma.io/docs/concepts/components/prisma-client/aggregations Aggregation Docs}
   * 
   * Select which fields to find the minimum value
  **/
  _min?: PresencaDiaPlantaoMinAggregateInputType
  /**
   * {@link https://www.prisma.io/docs/concepts/components/prisma-client/aggregations Aggregation Docs}
   * 
   * Select which fields to find the maximum value
  **/
  _max?: PresencaDiaPlantaoMaxAggregateInputType
}

export type GetPresencaDiaPlantaoAggregateType<T extends PresencaDiaPlantaoAggregateArgs> = {
      [P in keyof T & keyof AggregatePresencaDiaPlantao]: P extends '_count' | 'count'
    ? T[P] extends true
      ? number
      : Prisma.GetScalarType<T[P], AggregatePresencaDiaPlantao[P]>
    : Prisma.GetScalarType<T[P], AggregatePresencaDiaPlantao[P]>
}




export type PresencaDiaPlantaoGroupByArgs<ExtArgs extends runtime.Types.Extensions.InternalArgs = runtime.Types.Extensions.DefaultArgs> = {
  where?: Prisma.PresencaDiaPlantaoWhereInput
  orderBy?: Prisma.PresencaDiaPlantaoOrderByWithAggregationInput | Prisma.PresencaDiaPlantaoOrderByWithAggregationInput[]
  by: Prisma.PresencaDiaPlantaoScalarFieldEnum[] | Prisma.PresencaDiaPlantaoScalarFieldEnum
  having?: Prisma.PresencaDiaPlantaoScalarWhereWithAggregatesInput
  take?: number
  skip?: number
  _count?: PresencaDiaPlantaoCountAggregateInputType | true
  _avg?: PresencaDiaPlantaoAvgAggregateInputType
  _sum?: PresencaDiaPlantaoSumAggregateInputType
  _min?: PresencaDiaPlantaoMinAggregateInputType
  _max?: PresencaDiaPlantaoMaxAggregateInputType
}

export type PresencaDiaPlantaoGroupByOutputType = {
  id: number
  diaPlantaoId: number
  fechamentoId: number | null
  horaEntrada: Date | null
  horaSaida: Date | null
  intervalo: string | null
  horasTrabalhadas: number | null
  valorEstimado: number | null
  status: string
  historicoAprovacoes: runtime.JsonValue | null
  tempoGlosado: number | null
  justificativaGlosa: string | null
  observacao: string | null
  metadata: runtime.JsonValue | null
  createdAt: Date
  updatedAt: Date
  _count: PresencaDiaPlantaoCountAggregateOutputType | null
  _avg: PresencaDiaPlantaoAvgAggregateOutputType | null
  _sum: PresencaDiaPlantaoSumAggregateOutputType | null
  _min: PresencaDiaPlantaoMinAggregateOutputType | null
  _max: PresencaDiaPlantaoMaxAggregateOutputType | null
}

type GetPresencaDiaPlantaoGroupByPayload<T extends PresencaDiaPlantaoGroupByArgs> = Prisma.PrismaPromise<
  Array<
    Prisma.PickEnumerable<PresencaDiaPlantaoGroupByOutputType, T['by']> &
      {
        [P in ((keyof T) & (keyof PresencaDiaPlantaoGroupByOutputType))]: P extends '_count'
          ? T[P] extends boolean
            ? number
            : Prisma.GetScalarType<T[P], PresencaDiaPlantaoGroupByOutputType[P]>
          : Prisma.GetScalarType<T[P], PresencaDiaPlantaoGroupByOutputType[P]>
      }
    >
  >



export type PresencaDiaPlantaoWhereInput = {
  AND?: Prisma.PresencaDiaPlantaoWhereInput | Prisma.PresencaDiaPlantaoWhereInput[]
  OR?: Prisma.PresencaDiaPlantaoWhereInput[]
  NOT?: Prisma.PresencaDiaPlantaoWhereInput | Prisma.PresencaDiaPlantaoWhereInput[]
  id?: Prisma.IntFilter<"PresencaDiaPlantao"> | number
  diaPlantaoId?: Prisma.IntFilter<"PresencaDiaPlantao"> | number
  fechamentoId?: Prisma.IntNullableFilter<"PresencaDiaPlantao"> | number | null
  horaEntrada?: Prisma.DateTimeNullableFilter<"PresencaDiaPlantao"> | Date | string | null
  horaSaida?: Prisma.DateTimeNullableFilter<"PresencaDiaPlantao"> | Date | string | null
  intervalo?: Prisma.StringNullableFilter<"PresencaDiaPlantao"> | string | null
  horasTrabalhadas?: Prisma.FloatNullableFilter<"PresencaDiaPlantao"> | number | null
  valorEstimado?: Prisma.FloatNullableFilter<"PresencaDiaPlantao"> | number | null
  status?: Prisma.StringFilter<"PresencaDiaPlantao"> | string
  historicoAprovacoes?: Prisma.JsonNullableFilter<"PresencaDiaPlantao">
  tempoGlosado?: Prisma.FloatNullableFilter<"PresencaDiaPlantao"> | number | null
  justificativaGlosa?: Prisma.StringNullableFilter<"PresencaDiaPlantao"> | string | null
  observacao?: Prisma.StringNullableFilter<"PresencaDiaPlantao"> | string | null
  metadata?: Prisma.JsonNullableFilter<"PresencaDiaPlantao">
  createdAt?: Prisma.DateTimeFilter<"PresencaDiaPlantao"> | Date | string
  updatedAt?: Prisma.DateTimeFilter<"PresencaDiaPlantao"> | Date | string
  diaPlantao?: Prisma.XOR<Prisma.DiaPlantaoScalarRelationFilter, Prisma.DiaPlantaoWhereInput>
  fechamento?: Prisma.XOR<Prisma.FechamentoNullableScalarRelationFilter, Prisma.FechamentoWhereInput> | null
}

export type PresencaDiaPlantaoOrderByWithRelationInput = {
  id?: Prisma.SortOrder
  diaPlantaoId?: Prisma.SortOrder
  fechamentoId?: Prisma.SortOrderInput | Prisma.SortOrder
  horaEntrada?: Prisma.SortOrderInput | Prisma.SortOrder
  horaSaida?: Prisma.SortOrderInput | Prisma.SortOrder
  intervalo?: Prisma.SortOrderInput | Prisma.SortOrder
  horasTrabalhadas?: Prisma.SortOrderInput | Prisma.SortOrder
  valorEstimado?: Prisma.SortOrderInput | Prisma.SortOrder
  status?: Prisma.SortOrder
  historicoAprovacoes?: Prisma.SortOrderInput | Prisma.SortOrder
  tempoGlosado?: Prisma.SortOrderInput | Prisma.SortOrder
  justificativaGlosa?: Prisma.SortOrderInput | Prisma.SortOrder
  observacao?: Prisma.SortOrderInput | Prisma.SortOrder
  metadata?: Prisma.SortOrderInput | Prisma.SortOrder
  createdAt?: Prisma.SortOrder
  updatedAt?: Prisma.SortOrder
  diaPlantao?: Prisma.DiaPlantaoOrderByWithRelationInput
  fechamento?: Prisma.FechamentoOrderByWithRelationInput
  _relevance?: Prisma.PresencaDiaPlantaoOrderByRelevanceInput
}

export type PresencaDiaPlantaoWhereUniqueInput = Prisma.AtLeast<{
  id?: number
  diaPlantaoId_horaEntrada_horaSaida?: Prisma.PresencaDiaPlantaoDiaPlantaoIdHoraEntradaHoraSaidaCompoundUniqueInput
  AND?: Prisma.PresencaDiaPlantaoWhereInput | Prisma.PresencaDiaPlantaoWhereInput[]
  OR?: Prisma.PresencaDiaPlantaoWhereInput[]
  NOT?: Prisma.PresencaDiaPlantaoWhereInput | Prisma.PresencaDiaPlantaoWhereInput[]
  diaPlantaoId?: Prisma.IntFilter<"PresencaDiaPlantao"> | number
  fechamentoId?: Prisma.IntNullableFilter<"PresencaDiaPlantao"> | number | null
  horaEntrada?: Prisma.DateTimeNullableFilter<"PresencaDiaPlantao"> | Date | string | null
  horaSaida?: Prisma.DateTimeNullableFilter<"PresencaDiaPlantao"> | Date | string | null
  intervalo?: Prisma.StringNullableFilter<"PresencaDiaPlantao"> | string | null
  horasTrabalhadas?: Prisma.FloatNullableFilter<"PresencaDiaPlantao"> | number | null
  valorEstimado?: Prisma.FloatNullableFilter<"PresencaDiaPlantao"> | number | null
  status?: Prisma.StringFilter<"PresencaDiaPlantao"> | string
  historicoAprovacoes?: Prisma.JsonNullableFilter<"PresencaDiaPlantao">
  tempoGlosado?: Prisma.FloatNullableFilter<"PresencaDiaPlantao"> | number | null
  justificativaGlosa?: Prisma.StringNullableFilter<"PresencaDiaPlantao"> | string | null
  observacao?: Prisma.StringNullableFilter<"PresencaDiaPlantao"> | string | null
  metadata?: Prisma.JsonNullableFilter<"PresencaDiaPlantao">
  createdAt?: Prisma.DateTimeFilter<"PresencaDiaPlantao"> | Date | string
  updatedAt?: Prisma.DateTimeFilter<"PresencaDiaPlantao"> | Date | string
  diaPlantao?: Prisma.XOR<Prisma.DiaPlantaoScalarRelationFilter, Prisma.DiaPlantaoWhereInput>
  fechamento?: Prisma.XOR<Prisma.FechamentoNullableScalarRelationFilter, Prisma.FechamentoWhereInput> | null
}, "id" | "diaPlantaoId_horaEntrada_horaSaida">

export type PresencaDiaPlantaoOrderByWithAggregationInput = {
  id?: Prisma.SortOrder
  diaPlantaoId?: Prisma.SortOrder
  fechamentoId?: Prisma.SortOrderInput | Prisma.SortOrder
  horaEntrada?: Prisma.SortOrderInput | Prisma.SortOrder
  horaSaida?: Prisma.SortOrderInput | Prisma.SortOrder
  intervalo?: Prisma.SortOrderInput | Prisma.SortOrder
  horasTrabalhadas?: Prisma.SortOrderInput | Prisma.SortOrder
  valorEstimado?: Prisma.SortOrderInput | Prisma.SortOrder
  status?: Prisma.SortOrder
  historicoAprovacoes?: Prisma.SortOrderInput | Prisma.SortOrder
  tempoGlosado?: Prisma.SortOrderInput | Prisma.SortOrder
  justificativaGlosa?: Prisma.SortOrderInput | Prisma.SortOrder
  observacao?: Prisma.SortOrderInput | Prisma.SortOrder
  metadata?: Prisma.SortOrderInput | Prisma.SortOrder
  createdAt?: Prisma.SortOrder
  updatedAt?: Prisma.SortOrder
  _count?: Prisma.PresencaDiaPlantaoCountOrderByAggregateInput
  _avg?: Prisma.PresencaDiaPlantaoAvgOrderByAggregateInput
  _max?: Prisma.PresencaDiaPlantaoMaxOrderByAggregateInput
  _min?: Prisma.PresencaDiaPlantaoMinOrderByAggregateInput
  _sum?: Prisma.PresencaDiaPlantaoSumOrderByAggregateInput
}

export type PresencaDiaPlantaoScalarWhereWithAggregatesInput = {
  AND?: Prisma.PresencaDiaPlantaoScalarWhereWithAggregatesInput | Prisma.PresencaDiaPlantaoScalarWhereWithAggregatesInput[]
  OR?: Prisma.PresencaDiaPlantaoScalarWhereWithAggregatesInput[]
  NOT?: Prisma.PresencaDiaPlantaoScalarWhereWithAggregatesInput | Prisma.PresencaDiaPlantaoScalarWhereWithAggregatesInput[]
  id?: Prisma.IntWithAggregatesFilter<"PresencaDiaPlantao"> | number
  diaPlantaoId?: Prisma.IntWithAggregatesFilter<"PresencaDiaPlantao"> | number
  fechamentoId?: Prisma.IntNullableWithAggregatesFilter<"PresencaDiaPlantao"> | number | null
  horaEntrada?: Prisma.DateTimeNullableWithAggregatesFilter<"PresencaDiaPlantao"> | Date | string | null
  horaSaida?: Prisma.DateTimeNullableWithAggregatesFilter<"PresencaDiaPlantao"> | Date | string | null
  intervalo?: Prisma.StringNullableWithAggregatesFilter<"PresencaDiaPlantao"> | string | null
  horasTrabalhadas?: Prisma.FloatNullableWithAggregatesFilter<"PresencaDiaPlantao"> | number | null
  valorEstimado?: Prisma.FloatNullableWithAggregatesFilter<"PresencaDiaPlantao"> | number | null
  status?: Prisma.StringWithAggregatesFilter<"PresencaDiaPlantao"> | string
  historicoAprovacoes?: Prisma.JsonNullableWithAggregatesFilter<"PresencaDiaPlantao">
  tempoGlosado?: Prisma.FloatNullableWithAggregatesFilter<"PresencaDiaPlantao"> | number | null
  justificativaGlosa?: Prisma.StringNullableWithAggregatesFilter<"PresencaDiaPlantao"> | string | null
  observacao?: Prisma.StringNullableWithAggregatesFilter<"PresencaDiaPlantao"> | string | null
  metadata?: Prisma.JsonNullableWithAggregatesFilter<"PresencaDiaPlantao">
  createdAt?: Prisma.DateTimeWithAggregatesFilter<"PresencaDiaPlantao"> | Date | string
  updatedAt?: Prisma.DateTimeWithAggregatesFilter<"PresencaDiaPlantao"> | Date | string
}

export type PresencaDiaPlantaoCreateInput = {
  horaEntrada?: Date | string | null
  horaSaida?: Date | string | null
  intervalo?: string | null
  horasTrabalhadas?: number | null
  valorEstimado?: number | null
  status?: string
  historicoAprovacoes?: Prisma.NullableJsonNullValueInput | runtime.InputJsonValue
  tempoGlosado?: number | null
  justificativaGlosa?: string | null
  observacao?: string | null
  metadata?: Prisma.NullableJsonNullValueInput | runtime.InputJsonValue
  createdAt?: Date | string
  updatedAt?: Date | string
  diaPlantao: Prisma.DiaPlantaoCreateNestedOneWithoutPresencaDiaPlantaoInput
  fechamento?: Prisma.FechamentoCreateNestedOneWithoutPresencaDiaPlantaoInput
}

export type PresencaDiaPlantaoUncheckedCreateInput = {
  id?: number
  diaPlantaoId: number
  fechamentoId?: number | null
  horaEntrada?: Date | string | null
  horaSaida?: Date | string | null
  intervalo?: string | null
  horasTrabalhadas?: number | null
  valorEstimado?: number | null
  status?: string
  historicoAprovacoes?: Prisma.NullableJsonNullValueInput | runtime.InputJsonValue
  tempoGlosado?: number | null
  justificativaGlosa?: string | null
  observacao?: string | null
  metadata?: Prisma.NullableJsonNullValueInput | runtime.InputJsonValue
  createdAt?: Date | string
  updatedAt?: Date | string
}

export type PresencaDiaPlantaoUpdateInput = {
  horaEntrada?: Prisma.NullableDateTimeFieldUpdateOperationsInput | Date | string | null
  horaSaida?: Prisma.NullableDateTimeFieldUpdateOperationsInput | Date | string | null
  intervalo?: Prisma.NullableStringFieldUpdateOperationsInput | string | null
  horasTrabalhadas?: Prisma.NullableFloatFieldUpdateOperationsInput | number | null
  valorEstimado?: Prisma.NullableFloatFieldUpdateOperationsInput | number | null
  status?: Prisma.StringFieldUpdateOperationsInput | string
  historicoAprovacoes?: Prisma.NullableJsonNullValueInput | runtime.InputJsonValue
  tempoGlosado?: Prisma.NullableFloatFieldUpdateOperationsInput | number | null
  justificativaGlosa?: Prisma.NullableStringFieldUpdateOperationsInput | string | null
  observacao?: Prisma.NullableStringFieldUpdateOperationsInput | string | null
  metadata?: Prisma.NullableJsonNullValueInput | runtime.InputJsonValue
  createdAt?: Prisma.DateTimeFieldUpdateOperationsInput | Date | string
  updatedAt?: Prisma.DateTimeFieldUpdateOperationsInput | Date | string
  diaPlantao?: Prisma.DiaPlantaoUpdateOneRequiredWithoutPresencaDiaPlantaoNestedInput
  fechamento?: Prisma.FechamentoUpdateOneWithoutPresencaDiaPlantaoNestedInput
}

export type PresencaDiaPlantaoUncheckedUpdateInput = {
  id?: Prisma.IntFieldUpdateOperationsInput | number
  diaPlantaoId?: Prisma.IntFieldUpdateOperationsInput | number
  fechamentoId?: Prisma.NullableIntFieldUpdateOperationsInput | number | null
  horaEntrada?: Prisma.NullableDateTimeFieldUpdateOperationsInput | Date | string | null
  horaSaida?: Prisma.NullableDateTimeFieldUpdateOperationsInput | Date | string | null
  intervalo?: Prisma.NullableStringFieldUpdateOperationsInput | string | null
  horasTrabalhadas?: Prisma.NullableFloatFieldUpdateOperationsInput | number | null
  valorEstimado?: Prisma.NullableFloatFieldUpdateOperationsInput | number | null
  status?: Prisma.StringFieldUpdateOperationsInput | string
  historicoAprovacoes?: Prisma.NullableJsonNullValueInput | runtime.InputJsonValue
  tempoGlosado?: Prisma.NullableFloatFieldUpdateOperationsInput | number | null
  justificativaGlosa?: Prisma.NullableStringFieldUpdateOperationsInput | string | null
  observacao?: Prisma.NullableStringFieldUpdateOperationsInput | string | null
  metadata?: Prisma.NullableJsonNullValueInput | runtime.InputJsonValue
  createdAt?: Prisma.DateTimeFieldUpdateOperationsInput | Date | string
  updatedAt?: Prisma.DateTimeFieldUpdateOperationsInput | Date | string
}

export type PresencaDiaPlantaoCreateManyInput = {
  id?: number
  diaPlantaoId: number
  fechamentoId?: number | null
  horaEntrada?: Date | string | null
  horaSaida?: Date | string | null
  intervalo?: string | null
  horasTrabalhadas?: number | null
  valorEstimado?: number | null
  status?: string
  historicoAprovacoes?: Prisma.NullableJsonNullValueInput | runtime.InputJsonValue
  tempoGlosado?: number | null
  justificativaGlosa?: string | null
  observacao?: string | null
  metadata?: Prisma.NullableJsonNullValueInput | runtime.InputJsonValue
  createdAt?: Date | string
  updatedAt?: Date | string
}

export type PresencaDiaPlantaoUpdateManyMutationInput = {
  horaEntrada?: Prisma.NullableDateTimeFieldUpdateOperationsInput | Date | string | null
  horaSaida?: Prisma.NullableDateTimeFieldUpdateOperationsInput | Date | string | null
  intervalo?: Prisma.NullableStringFieldUpdateOperationsInput | string | null
  horasTrabalhadas?: Prisma.NullableFloatFieldUpdateOperationsInput | number | null
  valorEstimado?: Prisma.NullableFloatFieldUpdateOperationsInput | number | null
  status?: Prisma.StringFieldUpdateOperationsInput | string
  historicoAprovacoes?: Prisma.NullableJsonNullValueInput | runtime.InputJsonValue
  tempoGlosado?: Prisma.NullableFloatFieldUpdateOperationsInput | number | null
  justificativaGlosa?: Prisma.NullableStringFieldUpdateOperationsInput | string | null
  observacao?: Prisma.NullableStringFieldUpdateOperationsInput | string | null
  metadata?: Prisma.NullableJsonNullValueInput | runtime.InputJsonValue
  createdAt?: Prisma.DateTimeFieldUpdateOperationsInput | Date | string
  updatedAt?: Prisma.DateTimeFieldUpdateOperationsInput | Date | string
}

export type PresencaDiaPlantaoUncheckedUpdateManyInput = {
  id?: Prisma.IntFieldUpdateOperationsInput | number
  diaPlantaoId?: Prisma.IntFieldUpdateOperationsInput | number
  fechamentoId?: Prisma.NullableIntFieldUpdateOperationsInput | number | null
  horaEntrada?: Prisma.NullableDateTimeFieldUpdateOperationsInput | Date | string | null
  horaSaida?: Prisma.NullableDateTimeFieldUpdateOperationsInput | Date | string | null
  intervalo?: Prisma.NullableStringFieldUpdateOperationsInput | string | null
  horasTrabalhadas?: Prisma.NullableFloatFieldUpdateOperationsInput | number | null
  valorEstimado?: Prisma.NullableFloatFieldUpdateOperationsInput | number | null
  status?: Prisma.StringFieldUpdateOperationsInput | string
  historicoAprovacoes?: Prisma.NullableJsonNullValueInput | runtime.InputJsonValue
  tempoGlosado?: Prisma.NullableFloatFieldUpdateOperationsInput | number | null
  justificativaGlosa?: Prisma.NullableStringFieldUpdateOperationsInput | string | null
  observacao?: Prisma.NullableStringFieldUpdateOperationsInput | string | null
  metadata?: Prisma.NullableJsonNullValueInput | runtime.InputJsonValue
  createdAt?: Prisma.DateTimeFieldUpdateOperationsInput | Date | string
  updatedAt?: Prisma.DateTimeFieldUpdateOperationsInput | Date | string
}

export type PresencaDiaPlantaoListRelationFilter = {
  every?: Prisma.PresencaDiaPlantaoWhereInput
  some?: Prisma.PresencaDiaPlantaoWhereInput
  none?: Prisma.PresencaDiaPlantaoWhereInput
}

export type PresencaDiaPlantaoOrderByRelationAggregateInput = {
  _count?: Prisma.SortOrder
}

export type PresencaDiaPlantaoOrderByRelevanceInput = {
  fields: Prisma.PresencaDiaPlantaoOrderByRelevanceFieldEnum | Prisma.PresencaDiaPlantaoOrderByRelevanceFieldEnum[]
  sort: Prisma.SortOrder
  search: string
}

export type PresencaDiaPlantaoDiaPlantaoIdHoraEntradaHoraSaidaCompoundUniqueInput = {
  diaPlantaoId: number
  horaEntrada: Date | string
  horaSaida: Date | string
}

export type PresencaDiaPlantaoCountOrderByAggregateInput = {
  id?: Prisma.SortOrder
  diaPlantaoId?: Prisma.SortOrder
  fechamentoId?: Prisma.SortOrder
  horaEntrada?: Prisma.SortOrder
  horaSaida?: Prisma.SortOrder
  intervalo?: Prisma.SortOrder
  horasTrabalhadas?: Prisma.SortOrder
  valorEstimado?: Prisma.SortOrder
  status?: Prisma.SortOrder
  historicoAprovacoes?: Prisma.SortOrder
  tempoGlosado?: Prisma.SortOrder
  justificativaGlosa?: Prisma.SortOrder
  observacao?: Prisma.SortOrder
  metadata?: Prisma.SortOrder
  createdAt?: Prisma.SortOrder
  updatedAt?: Prisma.SortOrder
}

export type PresencaDiaPlantaoAvgOrderByAggregateInput = {
  id?: Prisma.SortOrder
  diaPlantaoId?: Prisma.SortOrder
  fechamentoId?: Prisma.SortOrder
  horasTrabalhadas?: Prisma.SortOrder
  valorEstimado?: Prisma.SortOrder
  tempoGlosado?: Prisma.SortOrder
}

export type PresencaDiaPlantaoMaxOrderByAggregateInput = {
  id?: Prisma.SortOrder
  diaPlantaoId?: Prisma.SortOrder
  fechamentoId?: Prisma.SortOrder
  horaEntrada?: Prisma.SortOrder
  horaSaida?: Prisma.SortOrder
  intervalo?: Prisma.SortOrder
  horasTrabalhadas?: Prisma.SortOrder
  valorEstimado?: Prisma.SortOrder
  status?: Prisma.SortOrder
  tempoGlosado?: Prisma.SortOrder
  justificativaGlosa?: Prisma.SortOrder
  observacao?: Prisma.SortOrder
  createdAt?: Prisma.SortOrder
  updatedAt?: Prisma.SortOrder
}

export type PresencaDiaPlantaoMinOrderByAggregateInput = {
  id?: Prisma.SortOrder
  diaPlantaoId?: Prisma.SortOrder
  fechamentoId?: Prisma.SortOrder
  horaEntrada?: Prisma.SortOrder
  horaSaida?: Prisma.SortOrder
  intervalo?: Prisma.SortOrder
  horasTrabalhadas?: Prisma.SortOrder
  valorEstimado?: Prisma.SortOrder
  status?: Prisma.SortOrder
  tempoGlosado?: Prisma.SortOrder
  justificativaGlosa?: Prisma.SortOrder
  observacao?: Prisma.SortOrder
  createdAt?: Prisma.SortOrder
  updatedAt?: Prisma.SortOrder
}

export type PresencaDiaPlantaoSumOrderByAggregateInput = {
  id?: Prisma.SortOrder
  diaPlantaoId?: Prisma.SortOrder
  fechamentoId?: Prisma.SortOrder
  horasTrabalhadas?: Prisma.SortOrder
  valorEstimado?: Prisma.SortOrder
  tempoGlosado?: Prisma.SortOrder
}

export type PresencaDiaPlantaoCreateNestedManyWithoutDiaPlantaoInput = {
  create?: Prisma.XOR<Prisma.PresencaDiaPlantaoCreateWithoutDiaPlantaoInput, Prisma.PresencaDiaPlantaoUncheckedCreateWithoutDiaPlantaoInput> | Prisma.PresencaDiaPlantaoCreateWithoutDiaPlantaoInput[] | Prisma.PresencaDiaPlantaoUncheckedCreateWithoutDiaPlantaoInput[]
  connectOrCreate?: Prisma.PresencaDiaPlantaoCreateOrConnectWithoutDiaPlantaoInput | Prisma.PresencaDiaPlantaoCreateOrConnectWithoutDiaPlantaoInput[]
  createMany?: Prisma.PresencaDiaPlantaoCreateManyDiaPlantaoInputEnvelope
  connect?: Prisma.PresencaDiaPlantaoWhereUniqueInput | Prisma.PresencaDiaPlantaoWhereUniqueInput[]
}

export type PresencaDiaPlantaoUncheckedCreateNestedManyWithoutDiaPlantaoInput = {
  create?: Prisma.XOR<Prisma.PresencaDiaPlantaoCreateWithoutDiaPlantaoInput, Prisma.PresencaDiaPlantaoUncheckedCreateWithoutDiaPlantaoInput> | Prisma.PresencaDiaPlantaoCreateWithoutDiaPlantaoInput[] | Prisma.PresencaDiaPlantaoUncheckedCreateWithoutDiaPlantaoInput[]
  connectOrCreate?: Prisma.PresencaDiaPlantaoCreateOrConnectWithoutDiaPlantaoInput | Prisma.PresencaDiaPlantaoCreateOrConnectWithoutDiaPlantaoInput[]
  createMany?: Prisma.PresencaDiaPlantaoCreateManyDiaPlantaoInputEnvelope
  connect?: Prisma.PresencaDiaPlantaoWhereUniqueInput | Prisma.PresencaDiaPlantaoWhereUniqueInput[]
}

export type PresencaDiaPlantaoUpdateManyWithoutDiaPlantaoNestedInput = {
  create?: Prisma.XOR<Prisma.PresencaDiaPlantaoCreateWithoutDiaPlantaoInput, Prisma.PresencaDiaPlantaoUncheckedCreateWithoutDiaPlantaoInput> | Prisma.PresencaDiaPlantaoCreateWithoutDiaPlantaoInput[] | Prisma.PresencaDiaPlantaoUncheckedCreateWithoutDiaPlantaoInput[]
  connectOrCreate?: Prisma.PresencaDiaPlantaoCreateOrConnectWithoutDiaPlantaoInput | Prisma.PresencaDiaPlantaoCreateOrConnectWithoutDiaPlantaoInput[]
  upsert?: Prisma.PresencaDiaPlantaoUpsertWithWhereUniqueWithoutDiaPlantaoInput | Prisma.PresencaDiaPlantaoUpsertWithWhereUniqueWithoutDiaPlantaoInput[]
  createMany?: Prisma.PresencaDiaPlantaoCreateManyDiaPlantaoInputEnvelope
  set?: Prisma.PresencaDiaPlantaoWhereUniqueInput | Prisma.PresencaDiaPlantaoWhereUniqueInput[]
  disconnect?: Prisma.PresencaDiaPlantaoWhereUniqueInput | Prisma.PresencaDiaPlantaoWhereUniqueInput[]
  delete?: Prisma.PresencaDiaPlantaoWhereUniqueInput | Prisma.PresencaDiaPlantaoWhereUniqueInput[]
  connect?: Prisma.PresencaDiaPlantaoWhereUniqueInput | Prisma.PresencaDiaPlantaoWhereUniqueInput[]
  update?: Prisma.PresencaDiaPlantaoUpdateWithWhereUniqueWithoutDiaPlantaoInput | Prisma.PresencaDiaPlantaoUpdateWithWhereUniqueWithoutDiaPlantaoInput[]
  updateMany?: Prisma.PresencaDiaPlantaoUpdateManyWithWhereWithoutDiaPlantaoInput | Prisma.PresencaDiaPlantaoUpdateManyWithWhereWithoutDiaPlantaoInput[]
  deleteMany?: Prisma.PresencaDiaPlantaoScalarWhereInput | Prisma.PresencaDiaPlantaoScalarWhereInput[]
}

export type PresencaDiaPlantaoUncheckedUpdateManyWithoutDiaPlantaoNestedInput = {
  create?: Prisma.XOR<Prisma.PresencaDiaPlantaoCreateWithoutDiaPlantaoInput, Prisma.PresencaDiaPlantaoUncheckedCreateWithoutDiaPlantaoInput> | Prisma.PresencaDiaPlantaoCreateWithoutDiaPlantaoInput[] | Prisma.PresencaDiaPlantaoUncheckedCreateWithoutDiaPlantaoInput[]
  connectOrCreate?: Prisma.PresencaDiaPlantaoCreateOrConnectWithoutDiaPlantaoInput | Prisma.PresencaDiaPlantaoCreateOrConnectWithoutDiaPlantaoInput[]
  upsert?: Prisma.PresencaDiaPlantaoUpsertWithWhereUniqueWithoutDiaPlantaoInput | Prisma.PresencaDiaPlantaoUpsertWithWhereUniqueWithoutDiaPlantaoInput[]
  createMany?: Prisma.PresencaDiaPlantaoCreateManyDiaPlantaoInputEnvelope
  set?: Prisma.PresencaDiaPlantaoWhereUniqueInput | Prisma.PresencaDiaPlantaoWhereUniqueInput[]
  disconnect?: Prisma.PresencaDiaPlantaoWhereUniqueInput | Prisma.PresencaDiaPlantaoWhereUniqueInput[]
  delete?: Prisma.PresencaDiaPlantaoWhereUniqueInput | Prisma.PresencaDiaPlantaoWhereUniqueInput[]
  connect?: Prisma.PresencaDiaPlantaoWhereUniqueInput | Prisma.PresencaDiaPlantaoWhereUniqueInput[]
  update?: Prisma.PresencaDiaPlantaoUpdateWithWhereUniqueWithoutDiaPlantaoInput | Prisma.PresencaDiaPlantaoUpdateWithWhereUniqueWithoutDiaPlantaoInput[]
  updateMany?: Prisma.PresencaDiaPlantaoUpdateManyWithWhereWithoutDiaPlantaoInput | Prisma.PresencaDiaPlantaoUpdateManyWithWhereWithoutDiaPlantaoInput[]
  deleteMany?: Prisma.PresencaDiaPlantaoScalarWhereInput | Prisma.PresencaDiaPlantaoScalarWhereInput[]
}

export type PresencaDiaPlantaoCreateNestedManyWithoutFechamentoInput = {
  create?: Prisma.XOR<Prisma.PresencaDiaPlantaoCreateWithoutFechamentoInput, Prisma.PresencaDiaPlantaoUncheckedCreateWithoutFechamentoInput> | Prisma.PresencaDiaPlantaoCreateWithoutFechamentoInput[] | Prisma.PresencaDiaPlantaoUncheckedCreateWithoutFechamentoInput[]
  connectOrCreate?: Prisma.PresencaDiaPlantaoCreateOrConnectWithoutFechamentoInput | Prisma.PresencaDiaPlantaoCreateOrConnectWithoutFechamentoInput[]
  createMany?: Prisma.PresencaDiaPlantaoCreateManyFechamentoInputEnvelope
  connect?: Prisma.PresencaDiaPlantaoWhereUniqueInput | Prisma.PresencaDiaPlantaoWhereUniqueInput[]
}

export type PresencaDiaPlantaoUncheckedCreateNestedManyWithoutFechamentoInput = {
  create?: Prisma.XOR<Prisma.PresencaDiaPlantaoCreateWithoutFechamentoInput, Prisma.PresencaDiaPlantaoUncheckedCreateWithoutFechamentoInput> | Prisma.PresencaDiaPlantaoCreateWithoutFechamentoInput[] | Prisma.PresencaDiaPlantaoUncheckedCreateWithoutFechamentoInput[]
  connectOrCreate?: Prisma.PresencaDiaPlantaoCreateOrConnectWithoutFechamentoInput | Prisma.PresencaDiaPlantaoCreateOrConnectWithoutFechamentoInput[]
  createMany?: Prisma.PresencaDiaPlantaoCreateManyFechamentoInputEnvelope
  connect?: Prisma.PresencaDiaPlantaoWhereUniqueInput | Prisma.PresencaDiaPlantaoWhereUniqueInput[]
}

export type PresencaDiaPlantaoUpdateManyWithoutFechamentoNestedInput = {
  create?: Prisma.XOR<Prisma.PresencaDiaPlantaoCreateWithoutFechamentoInput, Prisma.PresencaDiaPlantaoUncheckedCreateWithoutFechamentoInput> | Prisma.PresencaDiaPlantaoCreateWithoutFechamentoInput[] | Prisma.PresencaDiaPlantaoUncheckedCreateWithoutFechamentoInput[]
  connectOrCreate?: Prisma.PresencaDiaPlantaoCreateOrConnectWithoutFechamentoInput | Prisma.PresencaDiaPlantaoCreateOrConnectWithoutFechamentoInput[]
  upsert?: Prisma.PresencaDiaPlantaoUpsertWithWhereUniqueWithoutFechamentoInput | Prisma.PresencaDiaPlantaoUpsertWithWhereUniqueWithoutFechamentoInput[]
  createMany?: Prisma.PresencaDiaPlantaoCreateManyFechamentoInputEnvelope
  set?: Prisma.PresencaDiaPlantaoWhereUniqueInput | Prisma.PresencaDiaPlantaoWhereUniqueInput[]
  disconnect?: Prisma.PresencaDiaPlantaoWhereUniqueInput | Prisma.PresencaDiaPlantaoWhereUniqueInput[]
  delete?: Prisma.PresencaDiaPlantaoWhereUniqueInput | Prisma.PresencaDiaPlantaoWhereUniqueInput[]
  connect?: Prisma.PresencaDiaPlantaoWhereUniqueInput | Prisma.PresencaDiaPlantaoWhereUniqueInput[]
  update?: Prisma.PresencaDiaPlantaoUpdateWithWhereUniqueWithoutFechamentoInput | Prisma.PresencaDiaPlantaoUpdateWithWhereUniqueWithoutFechamentoInput[]
  updateMany?: Prisma.PresencaDiaPlantaoUpdateManyWithWhereWithoutFechamentoInput | Prisma.PresencaDiaPlantaoUpdateManyWithWhereWithoutFechamentoInput[]
  deleteMany?: Prisma.PresencaDiaPlantaoScalarWhereInput | Prisma.PresencaDiaPlantaoScalarWhereInput[]
}

export type PresencaDiaPlantaoUncheckedUpdateManyWithoutFechamentoNestedInput = {
  create?: Prisma.XOR<Prisma.PresencaDiaPlantaoCreateWithoutFechamentoInput, Prisma.PresencaDiaPlantaoUncheckedCreateWithoutFechamentoInput> | Prisma.PresencaDiaPlantaoCreateWithoutFechamentoInput[] | Prisma.PresencaDiaPlantaoUncheckedCreateWithoutFechamentoInput[]
  connectOrCreate?: Prisma.PresencaDiaPlantaoCreateOrConnectWithoutFechamentoInput | Prisma.PresencaDiaPlantaoCreateOrConnectWithoutFechamentoInput[]
  upsert?: Prisma.PresencaDiaPlantaoUpsertWithWhereUniqueWithoutFechamentoInput | Prisma.PresencaDiaPlantaoUpsertWithWhereUniqueWithoutFechamentoInput[]
  createMany?: Prisma.PresencaDiaPlantaoCreateManyFechamentoInputEnvelope
  set?: Prisma.PresencaDiaPlantaoWhereUniqueInput | Prisma.PresencaDiaPlantaoWhereUniqueInput[]
  disconnect?: Prisma.PresencaDiaPlantaoWhereUniqueInput | Prisma.PresencaDiaPlantaoWhereUniqueInput[]
  delete?: Prisma.PresencaDiaPlantaoWhereUniqueInput | Prisma.PresencaDiaPlantaoWhereUniqueInput[]
  connect?: Prisma.PresencaDiaPlantaoWhereUniqueInput | Prisma.PresencaDiaPlantaoWhereUniqueInput[]
  update?: Prisma.PresencaDiaPlantaoUpdateWithWhereUniqueWithoutFechamentoInput | Prisma.PresencaDiaPlantaoUpdateWithWhereUniqueWithoutFechamentoInput[]
  updateMany?: Prisma.PresencaDiaPlantaoUpdateManyWithWhereWithoutFechamentoInput | Prisma.PresencaDiaPlantaoUpdateManyWithWhereWithoutFechamentoInput[]
  deleteMany?: Prisma.PresencaDiaPlantaoScalarWhereInput | Prisma.PresencaDiaPlantaoScalarWhereInput[]
}

export type PresencaDiaPlantaoCreateWithoutDiaPlantaoInput = {
  horaEntrada?: Date | string | null
  horaSaida?: Date | string | null
  intervalo?: string | null
  horasTrabalhadas?: number | null
  valorEstimado?: number | null
  status?: string
  historicoAprovacoes?: Prisma.NullableJsonNullValueInput | runtime.InputJsonValue
  tempoGlosado?: number | null
  justificativaGlosa?: string | null
  observacao?: string | null
  metadata?: Prisma.NullableJsonNullValueInput | runtime.InputJsonValue
  createdAt?: Date | string
  updatedAt?: Date | string
  fechamento?: Prisma.FechamentoCreateNestedOneWithoutPresencaDiaPlantaoInput
}

export type PresencaDiaPlantaoUncheckedCreateWithoutDiaPlantaoInput = {
  id?: number
  fechamentoId?: number | null
  horaEntrada?: Date | string | null
  horaSaida?: Date | string | null
  intervalo?: string | null
  horasTrabalhadas?: number | null
  valorEstimado?: number | null
  status?: string
  historicoAprovacoes?: Prisma.NullableJsonNullValueInput | runtime.InputJsonValue
  tempoGlosado?: number | null
  justificativaGlosa?: string | null
  observacao?: string | null
  metadata?: Prisma.NullableJsonNullValueInput | runtime.InputJsonValue
  createdAt?: Date | string
  updatedAt?: Date | string
}

export type PresencaDiaPlantaoCreateOrConnectWithoutDiaPlantaoInput = {
  where: Prisma.PresencaDiaPlantaoWhereUniqueInput
  create: Prisma.XOR<Prisma.PresencaDiaPlantaoCreateWithoutDiaPlantaoInput, Prisma.PresencaDiaPlantaoUncheckedCreateWithoutDiaPlantaoInput>
}

export type PresencaDiaPlantaoCreateManyDiaPlantaoInputEnvelope = {
  data: Prisma.PresencaDiaPlantaoCreateManyDiaPlantaoInput | Prisma.PresencaDiaPlantaoCreateManyDiaPlantaoInput[]
  skipDuplicates?: boolean
}

export type PresencaDiaPlantaoUpsertWithWhereUniqueWithoutDiaPlantaoInput = {
  where: Prisma.PresencaDiaPlantaoWhereUniqueInput
  update: Prisma.XOR<Prisma.PresencaDiaPlantaoUpdateWithoutDiaPlantaoInput, Prisma.PresencaDiaPlantaoUncheckedUpdateWithoutDiaPlantaoInput>
  create: Prisma.XOR<Prisma.PresencaDiaPlantaoCreateWithoutDiaPlantaoInput, Prisma.PresencaDiaPlantaoUncheckedCreateWithoutDiaPlantaoInput>
}

export type PresencaDiaPlantaoUpdateWithWhereUniqueWithoutDiaPlantaoInput = {
  where: Prisma.PresencaDiaPlantaoWhereUniqueInput
  data: Prisma.XOR<Prisma.PresencaDiaPlantaoUpdateWithoutDiaPlantaoInput, Prisma.PresencaDiaPlantaoUncheckedUpdateWithoutDiaPlantaoInput>
}

export type PresencaDiaPlantaoUpdateManyWithWhereWithoutDiaPlantaoInput = {
  where: Prisma.PresencaDiaPlantaoScalarWhereInput
  data: Prisma.XOR<Prisma.PresencaDiaPlantaoUpdateManyMutationInput, Prisma.PresencaDiaPlantaoUncheckedUpdateManyWithoutDiaPlantaoInput>
}

export type PresencaDiaPlantaoScalarWhereInput = {
  AND?: Prisma.PresencaDiaPlantaoScalarWhereInput | Prisma.PresencaDiaPlantaoScalarWhereInput[]
  OR?: Prisma.PresencaDiaPlantaoScalarWhereInput[]
  NOT?: Prisma.PresencaDiaPlantaoScalarWhereInput | Prisma.PresencaDiaPlantaoScalarWhereInput[]
  id?: Prisma.IntFilter<"PresencaDiaPlantao"> | number
  diaPlantaoId?: Prisma.IntFilter<"PresencaDiaPlantao"> | number
  fechamentoId?: Prisma.IntNullableFilter<"PresencaDiaPlantao"> | number | null
  horaEntrada?: Prisma.DateTimeNullableFilter<"PresencaDiaPlantao"> | Date | string | null
  horaSaida?: Prisma.DateTimeNullableFilter<"PresencaDiaPlantao"> | Date | string | null
  intervalo?: Prisma.StringNullableFilter<"PresencaDiaPlantao"> | string | null
  horasTrabalhadas?: Prisma.FloatNullableFilter<"PresencaDiaPlantao"> | number | null
  valorEstimado?: Prisma.FloatNullableFilter<"PresencaDiaPlantao"> | number | null
  status?: Prisma.StringFilter<"PresencaDiaPlantao"> | string
  historicoAprovacoes?: Prisma.JsonNullableFilter<"PresencaDiaPlantao">
  tempoGlosado?: Prisma.FloatNullableFilter<"PresencaDiaPlantao"> | number | null
  justificativaGlosa?: Prisma.StringNullableFilter<"PresencaDiaPlantao"> | string | null
  observacao?: Prisma.StringNullableFilter<"PresencaDiaPlantao"> | string | null
  metadata?: Prisma.JsonNullableFilter<"PresencaDiaPlantao">
  createdAt?: Prisma.DateTimeFilter<"PresencaDiaPlantao"> | Date | string
  updatedAt?: Prisma.DateTimeFilter<"PresencaDiaPlantao"> | Date | string
}

export type PresencaDiaPlantaoCreateWithoutFechamentoInput = {
  horaEntrada?: Date | string | null
  horaSaida?: Date | string | null
  intervalo?: string | null
  horasTrabalhadas?: number | null
  valorEstimado?: number | null
  status?: string
  historicoAprovacoes?: Prisma.NullableJsonNullValueInput | runtime.InputJsonValue
  tempoGlosado?: number | null
  justificativaGlosa?: string | null
  observacao?: string | null
  metadata?: Prisma.NullableJsonNullValueInput | runtime.InputJsonValue
  createdAt?: Date | string
  updatedAt?: Date | string
  diaPlantao: Prisma.DiaPlantaoCreateNestedOneWithoutPresencaDiaPlantaoInput
}

export type PresencaDiaPlantaoUncheckedCreateWithoutFechamentoInput = {
  id?: number
  diaPlantaoId: number
  horaEntrada?: Date | string | null
  horaSaida?: Date | string | null
  intervalo?: string | null
  horasTrabalhadas?: number | null
  valorEstimado?: number | null
  status?: string
  historicoAprovacoes?: Prisma.NullableJsonNullValueInput | runtime.InputJsonValue
  tempoGlosado?: number | null
  justificativaGlosa?: string | null
  observacao?: string | null
  metadata?: Prisma.NullableJsonNullValueInput | runtime.InputJsonValue
  createdAt?: Date | string
  updatedAt?: Date | string
}

export type PresencaDiaPlantaoCreateOrConnectWithoutFechamentoInput = {
  where: Prisma.PresencaDiaPlantaoWhereUniqueInput
  create: Prisma.XOR<Prisma.PresencaDiaPlantaoCreateWithoutFechamentoInput, Prisma.PresencaDiaPlantaoUncheckedCreateWithoutFechamentoInput>
}

export type PresencaDiaPlantaoCreateManyFechamentoInputEnvelope = {
  data: Prisma.PresencaDiaPlantaoCreateManyFechamentoInput | Prisma.PresencaDiaPlantaoCreateManyFechamentoInput[]
  skipDuplicates?: boolean
}

export type PresencaDiaPlantaoUpsertWithWhereUniqueWithoutFechamentoInput = {
  where: Prisma.PresencaDiaPlantaoWhereUniqueInput
  update: Prisma.XOR<Prisma.PresencaDiaPlantaoUpdateWithoutFechamentoInput, Prisma.PresencaDiaPlantaoUncheckedUpdateWithoutFechamentoInput>
  create: Prisma.XOR<Prisma.PresencaDiaPlantaoCreateWithoutFechamentoInput, Prisma.PresencaDiaPlantaoUncheckedCreateWithoutFechamentoInput>
}

export type PresencaDiaPlantaoUpdateWithWhereUniqueWithoutFechamentoInput = {
  where: Prisma.PresencaDiaPlantaoWhereUniqueInput
  data: Prisma.XOR<Prisma.PresencaDiaPlantaoUpdateWithoutFechamentoInput, Prisma.PresencaDiaPlantaoUncheckedUpdateWithoutFechamentoInput>
}

export type PresencaDiaPlantaoUpdateManyWithWhereWithoutFechamentoInput = {
  where: Prisma.PresencaDiaPlantaoScalarWhereInput
  data: Prisma.XOR<Prisma.PresencaDiaPlantaoUpdateManyMutationInput, Prisma.PresencaDiaPlantaoUncheckedUpdateManyWithoutFechamentoInput>
}

export type PresencaDiaPlantaoCreateManyDiaPlantaoInput = {
  id?: number
  fechamentoId?: number | null
  horaEntrada?: Date | string | null
  horaSaida?: Date | string | null
  intervalo?: string | null
  horasTrabalhadas?: number | null
  valorEstimado?: number | null
  status?: string
  historicoAprovacoes?: Prisma.NullableJsonNullValueInput | runtime.InputJsonValue
  tempoGlosado?: number | null
  justificativaGlosa?: string | null
  observacao?: string | null
  metadata?: Prisma.NullableJsonNullValueInput | runtime.InputJsonValue
  createdAt?: Date | string
  updatedAt?: Date | string
}

export type PresencaDiaPlantaoUpdateWithoutDiaPlantaoInput = {
  horaEntrada?: Prisma.NullableDateTimeFieldUpdateOperationsInput | Date | string | null
  horaSaida?: Prisma.NullableDateTimeFieldUpdateOperationsInput | Date | string | null
  intervalo?: Prisma.NullableStringFieldUpdateOperationsInput | string | null
  horasTrabalhadas?: Prisma.NullableFloatFieldUpdateOperationsInput | number | null
  valorEstimado?: Prisma.NullableFloatFieldUpdateOperationsInput | number | null
  status?: Prisma.StringFieldUpdateOperationsInput | string
  historicoAprovacoes?: Prisma.NullableJsonNullValueInput | runtime.InputJsonValue
  tempoGlosado?: Prisma.NullableFloatFieldUpdateOperationsInput | number | null
  justificativaGlosa?: Prisma.NullableStringFieldUpdateOperationsInput | string | null
  observacao?: Prisma.NullableStringFieldUpdateOperationsInput | string | null
  metadata?: Prisma.NullableJsonNullValueInput | runtime.InputJsonValue
  createdAt?: Prisma.DateTimeFieldUpdateOperationsInput | Date | string
  updatedAt?: Prisma.DateTimeFieldUpdateOperationsInput | Date | string
  fechamento?: Prisma.FechamentoUpdateOneWithoutPresencaDiaPlantaoNestedInput
}

export type PresencaDiaPlantaoUncheckedUpdateWithoutDiaPlantaoInput = {
  id?: Prisma.IntFieldUpdateOperationsInput | number
  fechamentoId?: Prisma.NullableIntFieldUpdateOperationsInput | number | null
  horaEntrada?: Prisma.NullableDateTimeFieldUpdateOperationsInput | Date | string | null
  horaSaida?: Prisma.NullableDateTimeFieldUpdateOperationsInput | Date | string | null
  intervalo?: Prisma.NullableStringFieldUpdateOperationsInput | string | null
  horasTrabalhadas?: Prisma.NullableFloatFieldUpdateOperationsInput | number | null
  valorEstimado?: Prisma.NullableFloatFieldUpdateOperationsInput | number | null
  status?: Prisma.StringFieldUpdateOperationsInput | string
  historicoAprovacoes?: Prisma.NullableJsonNullValueInput | runtime.InputJsonValue
  tempoGlosado?: Prisma.NullableFloatFieldUpdateOperationsInput | number | null
  justificativaGlosa?: Prisma.NullableStringFieldUpdateOperationsInput | string | null
  observacao?: Prisma.NullableStringFieldUpdateOperationsInput | string | null
  metadata?: Prisma.NullableJsonNullValueInput | runtime.InputJsonValue
  createdAt?: Prisma.DateTimeFieldUpdateOperationsInput | Date | string
  updatedAt?: Prisma.DateTimeFieldUpdateOperationsInput | Date | string
}

export type PresencaDiaPlantaoUncheckedUpdateManyWithoutDiaPlantaoInput = {
  id?: Prisma.IntFieldUpdateOperationsInput | number
  fechamentoId?: Prisma.NullableIntFieldUpdateOperationsInput | number | null
  horaEntrada?: Prisma.NullableDateTimeFieldUpdateOperationsInput | Date | string | null
  horaSaida?: Prisma.NullableDateTimeFieldUpdateOperationsInput | Date | string | null
  intervalo?: Prisma.NullableStringFieldUpdateOperationsInput | string | null
  horasTrabalhadas?: Prisma.NullableFloatFieldUpdateOperationsInput | number | null
  valorEstimado?: Prisma.NullableFloatFieldUpdateOperationsInput | number | null
  status?: Prisma.StringFieldUpdateOperationsInput | string
  historicoAprovacoes?: Prisma.NullableJsonNullValueInput | runtime.InputJsonValue
  tempoGlosado?: Prisma.NullableFloatFieldUpdateOperationsInput | number | null
  justificativaGlosa?: Prisma.NullableStringFieldUpdateOperationsInput | string | null
  observacao?: Prisma.NullableStringFieldUpdateOperationsInput | string | null
  metadata?: Prisma.NullableJsonNullValueInput | runtime.InputJsonValue
  createdAt?: Prisma.DateTimeFieldUpdateOperationsInput | Date | string
  updatedAt?: Prisma.DateTimeFieldUpdateOperationsInput | Date | string
}

export type PresencaDiaPlantaoCreateManyFechamentoInput = {
  id?: number
  diaPlantaoId: number
  horaEntrada?: Date | string | null
  horaSaida?: Date | string | null
  intervalo?: string | null
  horasTrabalhadas?: number | null
  valorEstimado?: number | null
  status?: string
  historicoAprovacoes?: Prisma.NullableJsonNullValueInput | runtime.InputJsonValue
  tempoGlosado?: number | null
  justificativaGlosa?: string | null
  observacao?: string | null
  metadata?: Prisma.NullableJsonNullValueInput | runtime.InputJsonValue
  createdAt?: Date | string
  updatedAt?: Date | string
}

export type PresencaDiaPlantaoUpdateWithoutFechamentoInput = {
  horaEntrada?: Prisma.NullableDateTimeFieldUpdateOperationsInput | Date | string | null
  horaSaida?: Prisma.NullableDateTimeFieldUpdateOperationsInput | Date | string | null
  intervalo?: Prisma.NullableStringFieldUpdateOperationsInput | string | null
  horasTrabalhadas?: Prisma.NullableFloatFieldUpdateOperationsInput | number | null
  valorEstimado?: Prisma.NullableFloatFieldUpdateOperationsInput | number | null
  status?: Prisma.StringFieldUpdateOperationsInput | string
  historicoAprovacoes?: Prisma.NullableJsonNullValueInput | runtime.InputJsonValue
  tempoGlosado?: Prisma.NullableFloatFieldUpdateOperationsInput | number | null
  justificativaGlosa?: Prisma.NullableStringFieldUpdateOperationsInput | string | null
  observacao?: Prisma.NullableStringFieldUpdateOperationsInput | string | null
  metadata?: Prisma.NullableJsonNullValueInput | runtime.InputJsonValue
  createdAt?: Prisma.DateTimeFieldUpdateOperationsInput | Date | string
  updatedAt?: Prisma.DateTimeFieldUpdateOperationsInput | Date | string
  diaPlantao?: Prisma.DiaPlantaoUpdateOneRequiredWithoutPresencaDiaPlantaoNestedInput
}

export type PresencaDiaPlantaoUncheckedUpdateWithoutFechamentoInput = {
  id?: Prisma.IntFieldUpdateOperationsInput | number
  diaPlantaoId?: Prisma.IntFieldUpdateOperationsInput | number
  horaEntrada?: Prisma.NullableDateTimeFieldUpdateOperationsInput | Date | string | null
  horaSaida?: Prisma.NullableDateTimeFieldUpdateOperationsInput | Date | string | null
  intervalo?: Prisma.NullableStringFieldUpdateOperationsInput | string | null
  horasTrabalhadas?: Prisma.NullableFloatFieldUpdateOperationsInput | number | null
  valorEstimado?: Prisma.NullableFloatFieldUpdateOperationsInput | number | null
  status?: Prisma.StringFieldUpdateOperationsInput | string
  historicoAprovacoes?: Prisma.NullableJsonNullValueInput | runtime.InputJsonValue
  tempoGlosado?: Prisma.NullableFloatFieldUpdateOperationsInput | number | null
  justificativaGlosa?: Prisma.NullableStringFieldUpdateOperationsInput | string | null
  observacao?: Prisma.NullableStringFieldUpdateOperationsInput | string | null
  metadata?: Prisma.NullableJsonNullValueInput | runtime.InputJsonValue
  createdAt?: Prisma.DateTimeFieldUpdateOperationsInput | Date | string
  updatedAt?: Prisma.DateTimeFieldUpdateOperationsInput | Date | string
}

export type PresencaDiaPlantaoUncheckedUpdateManyWithoutFechamentoInput = {
  id?: Prisma.IntFieldUpdateOperationsInput | number
  diaPlantaoId?: Prisma.IntFieldUpdateOperationsInput | number
  horaEntrada?: Prisma.NullableDateTimeFieldUpdateOperationsInput | Date | string | null
  horaSaida?: Prisma.NullableDateTimeFieldUpdateOperationsInput | Date | string | null
  intervalo?: Prisma.NullableStringFieldUpdateOperationsInput | string | null
  horasTrabalhadas?: Prisma.NullableFloatFieldUpdateOperationsInput | number | null
  valorEstimado?: Prisma.NullableFloatFieldUpdateOperationsInput | number | null
  status?: Prisma.StringFieldUpdateOperationsInput | string
  historicoAprovacoes?: Prisma.NullableJsonNullValueInput | runtime.InputJsonValue
  tempoGlosado?: Prisma.NullableFloatFieldUpdateOperationsInput | number | null
  justificativaGlosa?: Prisma.NullableStringFieldUpdateOperationsInput | string | null
  observacao?: Prisma.NullableStringFieldUpdateOperationsInput | string | null
  metadata?: Prisma.NullableJsonNullValueInput | runtime.InputJsonValue
  createdAt?: Prisma.DateTimeFieldUpdateOperationsInput | Date | string
  updatedAt?: Prisma.DateTimeFieldUpdateOperationsInput | Date | string
}



export type PresencaDiaPlantaoSelect<ExtArgs extends runtime.Types.Extensions.InternalArgs = runtime.Types.Extensions.DefaultArgs> = runtime.Types.Extensions.GetSelect<{
  id?: boolean
  diaPlantaoId?: boolean
  fechamentoId?: boolean
  horaEntrada?: boolean
  horaSaida?: boolean
  intervalo?: boolean
  horasTrabalhadas?: boolean
  valorEstimado?: boolean
  status?: boolean
  historicoAprovacoes?: boolean
  tempoGlosado?: boolean
  justificativaGlosa?: boolean
  observacao?: boolean
  metadata?: boolean
  createdAt?: boolean
  updatedAt?: boolean
  diaPlantao?: boolean | Prisma.DiaPlantaoDefaultArgs<ExtArgs>
  fechamento?: boolean | Prisma.PresencaDiaPlantao$fechamentoArgs<ExtArgs>
}, ExtArgs["result"]["presencaDiaPlantao"]>



export type PresencaDiaPlantaoSelectScalar = {
  id?: boolean
  diaPlantaoId?: boolean
  fechamentoId?: boolean
  horaEntrada?: boolean
  horaSaida?: boolean
  intervalo?: boolean
  horasTrabalhadas?: boolean
  valorEstimado?: boolean
  status?: boolean
  historicoAprovacoes?: boolean
  tempoGlosado?: boolean
  justificativaGlosa?: boolean
  observacao?: boolean
  metadata?: boolean
  createdAt?: boolean
  updatedAt?: boolean
}

export type PresencaDiaPlantaoOmit<ExtArgs extends runtime.Types.Extensions.InternalArgs = runtime.Types.Extensions.DefaultArgs> = runtime.Types.Extensions.GetOmit<"id" | "diaPlantaoId" | "fechamentoId" | "horaEntrada" | "horaSaida" | "intervalo" | "horasTrabalhadas" | "valorEstimado" | "status" | "historicoAprovacoes" | "tempoGlosado" | "justificativaGlosa" | "observacao" | "metadata" | "createdAt" | "updatedAt", ExtArgs["result"]["presencaDiaPlantao"]>
export type PresencaDiaPlantaoInclude<ExtArgs extends runtime.Types.Extensions.InternalArgs = runtime.Types.Extensions.DefaultArgs> = {
  diaPlantao?: boolean | Prisma.DiaPlantaoDefaultArgs<ExtArgs>
  fechamento?: boolean | Prisma.PresencaDiaPlantao$fechamentoArgs<ExtArgs>
}

export type $PresencaDiaPlantaoPayload<ExtArgs extends runtime.Types.Extensions.InternalArgs = runtime.Types.Extensions.DefaultArgs> = {
  name: "PresencaDiaPlantao"
  objects: {
    diaPlantao: Prisma.$DiaPlantaoPayload<ExtArgs>
    fechamento: Prisma.$FechamentoPayload<ExtArgs> | null
  }
  scalars: runtime.Types.Extensions.GetPayloadResult<{
    id: number
    diaPlantaoId: number
    fechamentoId: number | null
    horaEntrada: Date | null
    horaSaida: Date | null
    intervalo: string | null
    horasTrabalhadas: number | null
    valorEstimado: number | null
    status: string
    historicoAprovacoes: runtime.JsonValue | null
    tempoGlosado: number | null
    justificativaGlosa: string | null
    observacao: string | null
    metadata: runtime.JsonValue | null
    createdAt: Date
    updatedAt: Date
  }, ExtArgs["result"]["presencaDiaPlantao"]>
  composites: {}
}

export type PresencaDiaPlantaoGetPayload<S extends boolean | null | undefined | PresencaDiaPlantaoDefaultArgs> = runtime.Types.Result.GetResult<Prisma.$PresencaDiaPlantaoPayload, S>

export type PresencaDiaPlantaoCountArgs<ExtArgs extends runtime.Types.Extensions.InternalArgs = runtime.Types.Extensions.DefaultArgs> =
  Omit<PresencaDiaPlantaoFindManyArgs, 'select' | 'include' | 'distinct' | 'omit'> & {
    select?: PresencaDiaPlantaoCountAggregateInputType | true
  }

export interface PresencaDiaPlantaoDelegate<ExtArgs extends runtime.Types.Extensions.InternalArgs = runtime.Types.Extensions.DefaultArgs, GlobalOmitOptions = {}> {
  [K: symbol]: { types: Prisma.TypeMap<ExtArgs>['model']['PresencaDiaPlantao'], meta: { name: 'PresencaDiaPlantao' } }
  /**
   * Find zero or one PresencaDiaPlantao that matches the filter.
   * @param {PresencaDiaPlantaoFindUniqueArgs} args - Arguments to find a PresencaDiaPlantao
   * @example
   * // Get one PresencaDiaPlantao
   * const presencaDiaPlantao = await prisma.presencaDiaPlantao.findUnique({
   *   where: {
   *     // ... provide filter here
   *   }
   * })
   */
  findUnique<T extends PresencaDiaPlantaoFindUniqueArgs>(args: Prisma.SelectSubset<T, PresencaDiaPlantaoFindUniqueArgs<ExtArgs>>): Prisma.Prisma__PresencaDiaPlantaoClient<runtime.Types.Result.GetResult<Prisma.$PresencaDiaPlantaoPayload<ExtArgs>, T, "findUnique", GlobalOmitOptions> | null, null, ExtArgs, GlobalOmitOptions>

  /**
   * Find one PresencaDiaPlantao that matches the filter or throw an error with `error.code='P2025'`
   * if no matches were found.
   * @param {PresencaDiaPlantaoFindUniqueOrThrowArgs} args - Arguments to find a PresencaDiaPlantao
   * @example
   * // Get one PresencaDiaPlantao
   * const presencaDiaPlantao = await prisma.presencaDiaPlantao.findUniqueOrThrow({
   *   where: {
   *     // ... provide filter here
   *   }
   * })
   */
  findUniqueOrThrow<T extends PresencaDiaPlantaoFindUniqueOrThrowArgs>(args: Prisma.SelectSubset<T, PresencaDiaPlantaoFindUniqueOrThrowArgs<ExtArgs>>): Prisma.Prisma__PresencaDiaPlantaoClient<runtime.Types.Result.GetResult<Prisma.$PresencaDiaPlantaoPayload<ExtArgs>, T, "findUniqueOrThrow", GlobalOmitOptions>, never, ExtArgs, GlobalOmitOptions>

  /**
   * Find the first PresencaDiaPlantao that matches the filter.
   * Note, that providing `undefined` is treated as the value not being there.
   * Read more here: https://pris.ly/d/null-undefined
   * @param {PresencaDiaPlantaoFindFirstArgs} args - Arguments to find a PresencaDiaPlantao
   * @example
   * // Get one PresencaDiaPlantao
   * const presencaDiaPlantao = await prisma.presencaDiaPlantao.findFirst({
   *   where: {
   *     // ... provide filter here
   *   }
   * })
   */
  findFirst<T extends PresencaDiaPlantaoFindFirstArgs>(args?: Prisma.SelectSubset<T, PresencaDiaPlantaoFindFirstArgs<ExtArgs>>): Prisma.Prisma__PresencaDiaPlantaoClient<runtime.Types.Result.GetResult<Prisma.$PresencaDiaPlantaoPayload<ExtArgs>, T, "findFirst", GlobalOmitOptions> | null, null, ExtArgs, GlobalOmitOptions>

  /**
   * Find the first PresencaDiaPlantao that matches the filter or
   * throw `PrismaKnownClientError` with `P2025` code if no matches were found.
   * Note, that providing `undefined` is treated as the value not being there.
   * Read more here: https://pris.ly/d/null-undefined
   * @param {PresencaDiaPlantaoFindFirstOrThrowArgs} args - Arguments to find a PresencaDiaPlantao
   * @example
   * // Get one PresencaDiaPlantao
   * const presencaDiaPlantao = await prisma.presencaDiaPlantao.findFirstOrThrow({
   *   where: {
   *     // ... provide filter here
   *   }
   * })
   */
  findFirstOrThrow<T extends PresencaDiaPlantaoFindFirstOrThrowArgs>(args?: Prisma.SelectSubset<T, PresencaDiaPlantaoFindFirstOrThrowArgs<ExtArgs>>): Prisma.Prisma__PresencaDiaPlantaoClient<runtime.Types.Result.GetResult<Prisma.$PresencaDiaPlantaoPayload<ExtArgs>, T, "findFirstOrThrow", GlobalOmitOptions>, never, ExtArgs, GlobalOmitOptions>

  /**
   * Find zero or more PresencaDiaPlantaos that matches the filter.
   * Note, that providing `undefined` is treated as the value not being there.
   * Read more here: https://pris.ly/d/null-undefined
   * @param {PresencaDiaPlantaoFindManyArgs} args - Arguments to filter and select certain fields only.
   * @example
   * // Get all PresencaDiaPlantaos
   * const presencaDiaPlantaos = await prisma.presencaDiaPlantao.findMany()
   * 
   * // Get first 10 PresencaDiaPlantaos
   * const presencaDiaPlantaos = await prisma.presencaDiaPlantao.findMany({ take: 10 })
   * 
   * // Only select the `id`
   * const presencaDiaPlantaoWithIdOnly = await prisma.presencaDiaPlantao.findMany({ select: { id: true } })
   * 
   */
  findMany<T extends PresencaDiaPlantaoFindManyArgs>(args?: Prisma.SelectSubset<T, PresencaDiaPlantaoFindManyArgs<ExtArgs>>): Prisma.PrismaPromise<runtime.Types.Result.GetResult<Prisma.$PresencaDiaPlantaoPayload<ExtArgs>, T, "findMany", GlobalOmitOptions>>

  /**
   * Create a PresencaDiaPlantao.
   * @param {PresencaDiaPlantaoCreateArgs} args - Arguments to create a PresencaDiaPlantao.
   * @example
   * // Create one PresencaDiaPlantao
   * const PresencaDiaPlantao = await prisma.presencaDiaPlantao.create({
   *   data: {
   *     // ... data to create a PresencaDiaPlantao
   *   }
   * })
   * 
   */
  create<T extends PresencaDiaPlantaoCreateArgs>(args: Prisma.SelectSubset<T, PresencaDiaPlantaoCreateArgs<ExtArgs>>): Prisma.Prisma__PresencaDiaPlantaoClient<runtime.Types.Result.GetResult<Prisma.$PresencaDiaPlantaoPayload<ExtArgs>, T, "create", GlobalOmitOptions>, never, ExtArgs, GlobalOmitOptions>

  /**
   * Create many PresencaDiaPlantaos.
   * @param {PresencaDiaPlantaoCreateManyArgs} args - Arguments to create many PresencaDiaPlantaos.
   * @example
   * // Create many PresencaDiaPlantaos
   * const presencaDiaPlantao = await prisma.presencaDiaPlantao.createMany({
   *   data: [
   *     // ... provide data here
   *   ]
   * })
   *     
   */
  createMany<T extends PresencaDiaPlantaoCreateManyArgs>(args?: Prisma.SelectSubset<T, PresencaDiaPlantaoCreateManyArgs<ExtArgs>>): Prisma.PrismaPromise<Prisma.BatchPayload>

  /**
   * Delete a PresencaDiaPlantao.
   * @param {PresencaDiaPlantaoDeleteArgs} args - Arguments to delete one PresencaDiaPlantao.
   * @example
   * // Delete one PresencaDiaPlantao
   * const PresencaDiaPlantao = await prisma.presencaDiaPlantao.delete({
   *   where: {
   *     // ... filter to delete one PresencaDiaPlantao
   *   }
   * })
   * 
   */
  delete<T extends PresencaDiaPlantaoDeleteArgs>(args: Prisma.SelectSubset<T, PresencaDiaPlantaoDeleteArgs<ExtArgs>>): Prisma.Prisma__PresencaDiaPlantaoClient<runtime.Types.Result.GetResult<Prisma.$PresencaDiaPlantaoPayload<ExtArgs>, T, "delete", GlobalOmitOptions>, never, ExtArgs, GlobalOmitOptions>

  /**
   * Update one PresencaDiaPlantao.
   * @param {PresencaDiaPlantaoUpdateArgs} args - Arguments to update one PresencaDiaPlantao.
   * @example
   * // Update one PresencaDiaPlantao
   * const presencaDiaPlantao = await prisma.presencaDiaPlantao.update({
   *   where: {
   *     // ... provide filter here
   *   },
   *   data: {
   *     // ... provide data here
   *   }
   * })
   * 
   */
  update<T extends PresencaDiaPlantaoUpdateArgs>(args: Prisma.SelectSubset<T, PresencaDiaPlantaoUpdateArgs<ExtArgs>>): Prisma.Prisma__PresencaDiaPlantaoClient<runtime.Types.Result.GetResult<Prisma.$PresencaDiaPlantaoPayload<ExtArgs>, T, "update", GlobalOmitOptions>, never, ExtArgs, GlobalOmitOptions>

  /**
   * Delete zero or more PresencaDiaPlantaos.
   * @param {PresencaDiaPlantaoDeleteManyArgs} args - Arguments to filter PresencaDiaPlantaos to delete.
   * @example
   * // Delete a few PresencaDiaPlantaos
   * const { count } = await prisma.presencaDiaPlantao.deleteMany({
   *   where: {
   *     // ... provide filter here
   *   }
   * })
   * 
   */
  deleteMany<T extends PresencaDiaPlantaoDeleteManyArgs>(args?: Prisma.SelectSubset<T, PresencaDiaPlantaoDeleteManyArgs<ExtArgs>>): Prisma.PrismaPromise<Prisma.BatchPayload>

  /**
   * Update zero or more PresencaDiaPlantaos.
   * Note, that providing `undefined` is treated as the value not being there.
   * Read more here: https://pris.ly/d/null-undefined
   * @param {PresencaDiaPlantaoUpdateManyArgs} args - Arguments to update one or more rows.
   * @example
   * // Update many PresencaDiaPlantaos
   * const presencaDiaPlantao = await prisma.presencaDiaPlantao.updateMany({
   *   where: {
   *     // ... provide filter here
   *   },
   *   data: {
   *     // ... provide data here
   *   }
   * })
   * 
   */
  updateMany<T extends PresencaDiaPlantaoUpdateManyArgs>(args: Prisma.SelectSubset<T, PresencaDiaPlantaoUpdateManyArgs<ExtArgs>>): Prisma.PrismaPromise<Prisma.BatchPayload>

  /**
   * Create or update one PresencaDiaPlantao.
   * @param {PresencaDiaPlantaoUpsertArgs} args - Arguments to update or create a PresencaDiaPlantao.
   * @example
   * // Update or create a PresencaDiaPlantao
   * const presencaDiaPlantao = await prisma.presencaDiaPlantao.upsert({
   *   create: {
   *     // ... data to create a PresencaDiaPlantao
   *   },
   *   update: {
   *     // ... in case it already exists, update
   *   },
   *   where: {
   *     // ... the filter for the PresencaDiaPlantao we want to update
   *   }
   * })
   */
  upsert<T extends PresencaDiaPlantaoUpsertArgs>(args: Prisma.SelectSubset<T, PresencaDiaPlantaoUpsertArgs<ExtArgs>>): Prisma.Prisma__PresencaDiaPlantaoClient<runtime.Types.Result.GetResult<Prisma.$PresencaDiaPlantaoPayload<ExtArgs>, T, "upsert", GlobalOmitOptions>, never, ExtArgs, GlobalOmitOptions>


  /**
   * Count the number of PresencaDiaPlantaos.
   * Note, that providing `undefined` is treated as the value not being there.
   * Read more here: https://pris.ly/d/null-undefined
   * @param {PresencaDiaPlantaoCountArgs} args - Arguments to filter PresencaDiaPlantaos to count.
   * @example
   * // Count the number of PresencaDiaPlantaos
   * const count = await prisma.presencaDiaPlantao.count({
   *   where: {
   *     // ... the filter for the PresencaDiaPlantaos we want to count
   *   }
   * })
  **/
  count<T extends PresencaDiaPlantaoCountArgs>(
    args?: Prisma.Subset<T, PresencaDiaPlantaoCountArgs>,
  ): Prisma.PrismaPromise<
    T extends runtime.Types.Utils.Record<'select', any>
      ? T['select'] extends true
        ? number
        : Prisma.GetScalarType<T['select'], PresencaDiaPlantaoCountAggregateOutputType>
      : number
  >

  /**
   * Allows you to perform aggregations operations on a PresencaDiaPlantao.
   * Note, that providing `undefined` is treated as the value not being there.
   * Read more here: https://pris.ly/d/null-undefined
   * @param {PresencaDiaPlantaoAggregateArgs} args - Select which aggregations you would like to apply and on what fields.
   * @example
   * // Ordered by age ascending
   * // Where email contains prisma.io
   * // Limited to the 10 users
   * const aggregations = await prisma.user.aggregate({
   *   _avg: {
   *     age: true,
   *   },
   *   where: {
   *     email: {
   *       contains: "prisma.io",
   *     },
   *   },
   *   orderBy: {
   *     age: "asc",
   *   },
   *   take: 10,
   * })
  **/
  aggregate<T extends PresencaDiaPlantaoAggregateArgs>(args: Prisma.Subset<T, PresencaDiaPlantaoAggregateArgs>): Prisma.PrismaPromise<GetPresencaDiaPlantaoAggregateType<T>>

  /**
   * Group by PresencaDiaPlantao.
   * Note, that providing `undefined` is treated as the value not being there.
   * Read more here: https://pris.ly/d/null-undefined
   * @param {PresencaDiaPlantaoGroupByArgs} args - Group by arguments.
   * @example
   * // Group by city, order by createdAt, get count
   * const result = await prisma.user.groupBy({
   *   by: ['city', 'createdAt'],
   *   orderBy: {
   *     createdAt: true
   *   },
   *   _count: {
   *     _all: true
   *   },
   * })
   * 
  **/
  groupBy<
    T extends PresencaDiaPlantaoGroupByArgs,
    HasSelectOrTake extends Prisma.Or<
      Prisma.Extends<'skip', Prisma.Keys<T>>,
      Prisma.Extends<'take', Prisma.Keys<T>>
    >,
    OrderByArg extends Prisma.True extends HasSelectOrTake
      ? { orderBy: PresencaDiaPlantaoGroupByArgs['orderBy'] }
      : { orderBy?: PresencaDiaPlantaoGroupByArgs['orderBy'] },
    OrderFields extends Prisma.ExcludeUnderscoreKeys<Prisma.Keys<Prisma.MaybeTupleToUnion<T['orderBy']>>>,
    ByFields extends Prisma.MaybeTupleToUnion<T['by']>,
    ByValid extends Prisma.Has<ByFields, OrderFields>,
    HavingFields extends Prisma.GetHavingFields<T['having']>,
    HavingValid extends Prisma.Has<ByFields, HavingFields>,
    ByEmpty extends T['by'] extends never[] ? Prisma.True : Prisma.False,
    InputErrors extends ByEmpty extends Prisma.True
    ? `Error: "by" must not be empty.`
    : HavingValid extends Prisma.False
    ? {
        [P in HavingFields]: P extends ByFields
          ? never
          : P extends string
          ? `Error: Field "${P}" used in "having" needs to be provided in "by".`
          : [
              Error,
              'Field ',
              P,
              ` in "having" needs to be provided in "by"`,
            ]
      }[HavingFields]
    : 'take' extends Prisma.Keys<T>
    ? 'orderBy' extends Prisma.Keys<T>
      ? ByValid extends Prisma.True
        ? {}
        : {
            [P in OrderFields]: P extends ByFields
              ? never
              : `Error: Field "${P}" in "orderBy" needs to be provided in "by"`
          }[OrderFields]
      : 'Error: If you provide "take", you also need to provide "orderBy"'
    : 'skip' extends Prisma.Keys<T>
    ? 'orderBy' extends Prisma.Keys<T>
      ? ByValid extends Prisma.True
        ? {}
        : {
            [P in OrderFields]: P extends ByFields
              ? never
              : `Error: Field "${P}" in "orderBy" needs to be provided in "by"`
          }[OrderFields]
      : 'Error: If you provide "skip", you also need to provide "orderBy"'
    : ByValid extends Prisma.True
    ? {}
    : {
        [P in OrderFields]: P extends ByFields
          ? never
          : `Error: Field "${P}" in "orderBy" needs to be provided in "by"`
      }[OrderFields]
  >(args: Prisma.SubsetIntersection<T, PresencaDiaPlantaoGroupByArgs, OrderByArg> & InputErrors): {} extends InputErrors ? GetPresencaDiaPlantaoGroupByPayload<T> : Prisma.PrismaPromise<InputErrors>
/**
 * Fields of the PresencaDiaPlantao model
 */
readonly fields: PresencaDiaPlantaoFieldRefs;
}

/**
 * The delegate class that acts as a "Promise-like" for PresencaDiaPlantao.
 * Why is this prefixed with `Prisma__`?
 * Because we want to prevent naming conflicts as mentioned in
 * https://github.com/prisma/prisma-client-js/issues/707
 */
export interface Prisma__PresencaDiaPlantaoClient<T, Null = never, ExtArgs extends runtime.Types.Extensions.InternalArgs = runtime.Types.Extensions.DefaultArgs, GlobalOmitOptions = {}> extends Prisma.PrismaPromise<T> {
  readonly [Symbol.toStringTag]: "PrismaPromise"
  diaPlantao<T extends Prisma.DiaPlantaoDefaultArgs<ExtArgs> = {}>(args?: Prisma.Subset<T, Prisma.DiaPlantaoDefaultArgs<ExtArgs>>): Prisma.Prisma__DiaPlantaoClient<runtime.Types.Result.GetResult<Prisma.$DiaPlantaoPayload<ExtArgs>, T, "findUniqueOrThrow", GlobalOmitOptions> | Null, Null, ExtArgs, GlobalOmitOptions>
  fechamento<T extends Prisma.PresencaDiaPlantao$fechamentoArgs<ExtArgs> = {}>(args?: Prisma.Subset<T, Prisma.PresencaDiaPlantao$fechamentoArgs<ExtArgs>>): Prisma.Prisma__FechamentoClient<runtime.Types.Result.GetResult<Prisma.$FechamentoPayload<ExtArgs>, T, "findUniqueOrThrow", GlobalOmitOptions> | null, null, ExtArgs, GlobalOmitOptions>
  /**
   * Attaches callbacks for the resolution and/or rejection of the Promise.
   * @param onfulfilled The callback to execute when the Promise is resolved.
   * @param onrejected The callback to execute when the Promise is rejected.
   * @returns A Promise for the completion of which ever callback is executed.
   */
  then<TResult1 = T, TResult2 = never>(onfulfilled?: ((value: T) => TResult1 | PromiseLike<TResult1>) | undefined | null, onrejected?: ((reason: any) => TResult2 | PromiseLike<TResult2>) | undefined | null): runtime.Types.Utils.JsPromise<TResult1 | TResult2>
  /**
   * Attaches a callback for only the rejection of the Promise.
   * @param onrejected The callback to execute when the Promise is rejected.
   * @returns A Promise for the completion of the callback.
   */
  catch<TResult = never>(onrejected?: ((reason: any) => TResult | PromiseLike<TResult>) | undefined | null): runtime.Types.Utils.JsPromise<T | TResult>
  /**
   * Attaches a callback that is invoked when the Promise is settled (fulfilled or rejected). The
   * resolved value cannot be modified from the callback.
   * @param onfinally The callback to execute when the Promise is settled (fulfilled or rejected).
   * @returns A Promise for the completion of the callback.
   */
  finally(onfinally?: (() => void) | undefined | null): runtime.Types.Utils.JsPromise<T>
}




/**
 * Fields of the PresencaDiaPlantao model
 */
export interface PresencaDiaPlantaoFieldRefs {
  readonly id: Prisma.FieldRef<"PresencaDiaPlantao", 'Int'>
  readonly diaPlantaoId: Prisma.FieldRef<"PresencaDiaPlantao", 'Int'>
  readonly fechamentoId: Prisma.FieldRef<"PresencaDiaPlantao", 'Int'>
  readonly horaEntrada: Prisma.FieldRef<"PresencaDiaPlantao", 'DateTime'>
  readonly horaSaida: Prisma.FieldRef<"PresencaDiaPlantao", 'DateTime'>
  readonly intervalo: Prisma.FieldRef<"PresencaDiaPlantao", 'String'>
  readonly horasTrabalhadas: Prisma.FieldRef<"PresencaDiaPlantao", 'Float'>
  readonly valorEstimado: Prisma.FieldRef<"PresencaDiaPlantao", 'Float'>
  readonly status: Prisma.FieldRef<"PresencaDiaPlantao", 'String'>
  readonly historicoAprovacoes: Prisma.FieldRef<"PresencaDiaPlantao", 'Json'>
  readonly tempoGlosado: Prisma.FieldRef<"PresencaDiaPlantao", 'Float'>
  readonly justificativaGlosa: Prisma.FieldRef<"PresencaDiaPlantao", 'String'>
  readonly observacao: Prisma.FieldRef<"PresencaDiaPlantao", 'String'>
  readonly metadata: Prisma.FieldRef<"PresencaDiaPlantao", 'Json'>
  readonly createdAt: Prisma.FieldRef<"PresencaDiaPlantao", 'DateTime'>
  readonly updatedAt: Prisma.FieldRef<"PresencaDiaPlantao", 'DateTime'>
}
    

// Custom InputTypes
/**
 * PresencaDiaPlantao findUnique
 */
export type PresencaDiaPlantaoFindUniqueArgs<ExtArgs extends runtime.Types.Extensions.InternalArgs = runtime.Types.Extensions.DefaultArgs> = {
  /**
   * Select specific fields to fetch from the PresencaDiaPlantao
   */
  select?: Prisma.PresencaDiaPlantaoSelect<ExtArgs> | null
  /**
   * Omit specific fields from the PresencaDiaPlantao
   */
  omit?: Prisma.PresencaDiaPlantaoOmit<ExtArgs> | null
  /**
   * Choose, which related nodes to fetch as well
   */
  include?: Prisma.PresencaDiaPlantaoInclude<ExtArgs> | null
  /**
   * Filter, which PresencaDiaPlantao to fetch.
   */
  where: Prisma.PresencaDiaPlantaoWhereUniqueInput
}

/**
 * PresencaDiaPlantao findUniqueOrThrow
 */
export type PresencaDiaPlantaoFindUniqueOrThrowArgs<ExtArgs extends runtime.Types.Extensions.InternalArgs = runtime.Types.Extensions.DefaultArgs> = {
  /**
   * Select specific fields to fetch from the PresencaDiaPlantao
   */
  select?: Prisma.PresencaDiaPlantaoSelect<ExtArgs> | null
  /**
   * Omit specific fields from the PresencaDiaPlantao
   */
  omit?: Prisma.PresencaDiaPlantaoOmit<ExtArgs> | null
  /**
   * Choose, which related nodes to fetch as well
   */
  include?: Prisma.PresencaDiaPlantaoInclude<ExtArgs> | null
  /**
   * Filter, which PresencaDiaPlantao to fetch.
   */
  where: Prisma.PresencaDiaPlantaoWhereUniqueInput
}

/**
 * PresencaDiaPlantao findFirst
 */
export type PresencaDiaPlantaoFindFirstArgs<ExtArgs extends runtime.Types.Extensions.InternalArgs = runtime.Types.Extensions.DefaultArgs> = {
  /**
   * Select specific fields to fetch from the PresencaDiaPlantao
   */
  select?: Prisma.PresencaDiaPlantaoSelect<ExtArgs> | null
  /**
   * Omit specific fields from the PresencaDiaPlantao
   */
  omit?: Prisma.PresencaDiaPlantaoOmit<ExtArgs> | null
  /**
   * Choose, which related nodes to fetch as well
   */
  include?: Prisma.PresencaDiaPlantaoInclude<ExtArgs> | null
  /**
   * Filter, which PresencaDiaPlantao to fetch.
   */
  where?: Prisma.PresencaDiaPlantaoWhereInput
  /**
   * {@link https://www.prisma.io/docs/concepts/components/prisma-client/sorting Sorting Docs}
   * 
   * Determine the order of PresencaDiaPlantaos to fetch.
   */
  orderBy?: Prisma.PresencaDiaPlantaoOrderByWithRelationInput | Prisma.PresencaDiaPlantaoOrderByWithRelationInput[]
  /**
   * {@link https://www.prisma.io/docs/concepts/components/prisma-client/pagination#cursor-based-pagination Cursor Docs}
   * 
   * Sets the position for searching for PresencaDiaPlantaos.
   */
  cursor?: Prisma.PresencaDiaPlantaoWhereUniqueInput
  /**
   * {@link https://www.prisma.io/docs/concepts/components/prisma-client/pagination Pagination Docs}
   * 
   * Take `±n` PresencaDiaPlantaos from the position of the cursor.
   */
  take?: number
  /**
   * {@link https://www.prisma.io/docs/concepts/components/prisma-client/pagination Pagination Docs}
   * 
   * Skip the first `n` PresencaDiaPlantaos.
   */
  skip?: number
  /**
   * {@link https://www.prisma.io/docs/concepts/components/prisma-client/distinct Distinct Docs}
   * 
   * Filter by unique combinations of PresencaDiaPlantaos.
   */
  distinct?: Prisma.PresencaDiaPlantaoScalarFieldEnum | Prisma.PresencaDiaPlantaoScalarFieldEnum[]
}

/**
 * PresencaDiaPlantao findFirstOrThrow
 */
export type PresencaDiaPlantaoFindFirstOrThrowArgs<ExtArgs extends runtime.Types.Extensions.InternalArgs = runtime.Types.Extensions.DefaultArgs> = {
  /**
   * Select specific fields to fetch from the PresencaDiaPlantao
   */
  select?: Prisma.PresencaDiaPlantaoSelect<ExtArgs> | null
  /**
   * Omit specific fields from the PresencaDiaPlantao
   */
  omit?: Prisma.PresencaDiaPlantaoOmit<ExtArgs> | null
  /**
   * Choose, which related nodes to fetch as well
   */
  include?: Prisma.PresencaDiaPlantaoInclude<ExtArgs> | null
  /**
   * Filter, which PresencaDiaPlantao to fetch.
   */
  where?: Prisma.PresencaDiaPlantaoWhereInput
  /**
   * {@link https://www.prisma.io/docs/concepts/components/prisma-client/sorting Sorting Docs}
   * 
   * Determine the order of PresencaDiaPlantaos to fetch.
   */
  orderBy?: Prisma.PresencaDiaPlantaoOrderByWithRelationInput | Prisma.PresencaDiaPlantaoOrderByWithRelationInput[]
  /**
   * {@link https://www.prisma.io/docs/concepts/components/prisma-client/pagination#cursor-based-pagination Cursor Docs}
   * 
   * Sets the position for searching for PresencaDiaPlantaos.
   */
  cursor?: Prisma.PresencaDiaPlantaoWhereUniqueInput
  /**
   * {@link https://www.prisma.io/docs/concepts/components/prisma-client/pagination Pagination Docs}
   * 
   * Take `±n` PresencaDiaPlantaos from the position of the cursor.
   */
  take?: number
  /**
   * {@link https://www.prisma.io/docs/concepts/components/prisma-client/pagination Pagination Docs}
   * 
   * Skip the first `n` PresencaDiaPlantaos.
   */
  skip?: number
  /**
   * {@link https://www.prisma.io/docs/concepts/components/prisma-client/distinct Distinct Docs}
   * 
   * Filter by unique combinations of PresencaDiaPlantaos.
   */
  distinct?: Prisma.PresencaDiaPlantaoScalarFieldEnum | Prisma.PresencaDiaPlantaoScalarFieldEnum[]
}

/**
 * PresencaDiaPlantao findMany
 */
export type PresencaDiaPlantaoFindManyArgs<ExtArgs extends runtime.Types.Extensions.InternalArgs = runtime.Types.Extensions.DefaultArgs> = {
  /**
   * Select specific fields to fetch from the PresencaDiaPlantao
   */
  select?: Prisma.PresencaDiaPlantaoSelect<ExtArgs> | null
  /**
   * Omit specific fields from the PresencaDiaPlantao
   */
  omit?: Prisma.PresencaDiaPlantaoOmit<ExtArgs> | null
  /**
   * Choose, which related nodes to fetch as well
   */
  include?: Prisma.PresencaDiaPlantaoInclude<ExtArgs> | null
  /**
   * Filter, which PresencaDiaPlantaos to fetch.
   */
  where?: Prisma.PresencaDiaPlantaoWhereInput
  /**
   * {@link https://www.prisma.io/docs/concepts/components/prisma-client/sorting Sorting Docs}
   * 
   * Determine the order of PresencaDiaPlantaos to fetch.
   */
  orderBy?: Prisma.PresencaDiaPlantaoOrderByWithRelationInput | Prisma.PresencaDiaPlantaoOrderByWithRelationInput[]
  /**
   * {@link https://www.prisma.io/docs/concepts/components/prisma-client/pagination#cursor-based-pagination Cursor Docs}
   * 
   * Sets the position for listing PresencaDiaPlantaos.
   */
  cursor?: Prisma.PresencaDiaPlantaoWhereUniqueInput
  /**
   * {@link https://www.prisma.io/docs/concepts/components/prisma-client/pagination Pagination Docs}
   * 
   * Take `±n` PresencaDiaPlantaos from the position of the cursor.
   */
  take?: number
  /**
   * {@link https://www.prisma.io/docs/concepts/components/prisma-client/pagination Pagination Docs}
   * 
   * Skip the first `n` PresencaDiaPlantaos.
   */
  skip?: number
  distinct?: Prisma.PresencaDiaPlantaoScalarFieldEnum | Prisma.PresencaDiaPlantaoScalarFieldEnum[]
}

/**
 * PresencaDiaPlantao create
 */
export type PresencaDiaPlantaoCreateArgs<ExtArgs extends runtime.Types.Extensions.InternalArgs = runtime.Types.Extensions.DefaultArgs> = {
  /**
   * Select specific fields to fetch from the PresencaDiaPlantao
   */
  select?: Prisma.PresencaDiaPlantaoSelect<ExtArgs> | null
  /**
   * Omit specific fields from the PresencaDiaPlantao
   */
  omit?: Prisma.PresencaDiaPlantaoOmit<ExtArgs> | null
  /**
   * Choose, which related nodes to fetch as well
   */
  include?: Prisma.PresencaDiaPlantaoInclude<ExtArgs> | null
  /**
   * The data needed to create a PresencaDiaPlantao.
   */
  data: Prisma.XOR<Prisma.PresencaDiaPlantaoCreateInput, Prisma.PresencaDiaPlantaoUncheckedCreateInput>
}

/**
 * PresencaDiaPlantao createMany
 */
export type PresencaDiaPlantaoCreateManyArgs<ExtArgs extends runtime.Types.Extensions.InternalArgs = runtime.Types.Extensions.DefaultArgs> = {
  /**
   * The data used to create many PresencaDiaPlantaos.
   */
  data: Prisma.PresencaDiaPlantaoCreateManyInput | Prisma.PresencaDiaPlantaoCreateManyInput[]
  skipDuplicates?: boolean
}

/**
 * PresencaDiaPlantao update
 */
export type PresencaDiaPlantaoUpdateArgs<ExtArgs extends runtime.Types.Extensions.InternalArgs = runtime.Types.Extensions.DefaultArgs> = {
  /**
   * Select specific fields to fetch from the PresencaDiaPlantao
   */
  select?: Prisma.PresencaDiaPlantaoSelect<ExtArgs> | null
  /**
   * Omit specific fields from the PresencaDiaPlantao
   */
  omit?: Prisma.PresencaDiaPlantaoOmit<ExtArgs> | null
  /**
   * Choose, which related nodes to fetch as well
   */
  include?: Prisma.PresencaDiaPlantaoInclude<ExtArgs> | null
  /**
   * The data needed to update a PresencaDiaPlantao.
   */
  data: Prisma.XOR<Prisma.PresencaDiaPlantaoUpdateInput, Prisma.PresencaDiaPlantaoUncheckedUpdateInput>
  /**
   * Choose, which PresencaDiaPlantao to update.
   */
  where: Prisma.PresencaDiaPlantaoWhereUniqueInput
}

/**
 * PresencaDiaPlantao updateMany
 */
export type PresencaDiaPlantaoUpdateManyArgs<ExtArgs extends runtime.Types.Extensions.InternalArgs = runtime.Types.Extensions.DefaultArgs> = {
  /**
   * The data used to update PresencaDiaPlantaos.
   */
  data: Prisma.XOR<Prisma.PresencaDiaPlantaoUpdateManyMutationInput, Prisma.PresencaDiaPlantaoUncheckedUpdateManyInput>
  /**
   * Filter which PresencaDiaPlantaos to update
   */
  where?: Prisma.PresencaDiaPlantaoWhereInput
  /**
   * Limit how many PresencaDiaPlantaos to update.
   */
  limit?: number
}

/**
 * PresencaDiaPlantao upsert
 */
export type PresencaDiaPlantaoUpsertArgs<ExtArgs extends runtime.Types.Extensions.InternalArgs = runtime.Types.Extensions.DefaultArgs> = {
  /**
   * Select specific fields to fetch from the PresencaDiaPlantao
   */
  select?: Prisma.PresencaDiaPlantaoSelect<ExtArgs> | null
  /**
   * Omit specific fields from the PresencaDiaPlantao
   */
  omit?: Prisma.PresencaDiaPlantaoOmit<ExtArgs> | null
  /**
   * Choose, which related nodes to fetch as well
   */
  include?: Prisma.PresencaDiaPlantaoInclude<ExtArgs> | null
  /**
   * The filter to search for the PresencaDiaPlantao to update in case it exists.
   */
  where: Prisma.PresencaDiaPlantaoWhereUniqueInput
  /**
   * In case the PresencaDiaPlantao found by the `where` argument doesn't exist, create a new PresencaDiaPlantao with this data.
   */
  create: Prisma.XOR<Prisma.PresencaDiaPlantaoCreateInput, Prisma.PresencaDiaPlantaoUncheckedCreateInput>
  /**
   * In case the PresencaDiaPlantao was found with the provided `where` argument, update it with this data.
   */
  update: Prisma.XOR<Prisma.PresencaDiaPlantaoUpdateInput, Prisma.PresencaDiaPlantaoUncheckedUpdateInput>
}

/**
 * PresencaDiaPlantao delete
 */
export type PresencaDiaPlantaoDeleteArgs<ExtArgs extends runtime.Types.Extensions.InternalArgs = runtime.Types.Extensions.DefaultArgs> = {
  /**
   * Select specific fields to fetch from the PresencaDiaPlantao
   */
  select?: Prisma.PresencaDiaPlantaoSelect<ExtArgs> | null
  /**
   * Omit specific fields from the PresencaDiaPlantao
   */
  omit?: Prisma.PresencaDiaPlantaoOmit<ExtArgs> | null
  /**
   * Choose, which related nodes to fetch as well
   */
  include?: Prisma.PresencaDiaPlantaoInclude<ExtArgs> | null
  /**
   * Filter which PresencaDiaPlantao to delete.
   */
  where: Prisma.PresencaDiaPlantaoWhereUniqueInput
}

/**
 * PresencaDiaPlantao deleteMany
 */
export type PresencaDiaPlantaoDeleteManyArgs<ExtArgs extends runtime.Types.Extensions.InternalArgs = runtime.Types.Extensions.DefaultArgs> = {
  /**
   * Filter which PresencaDiaPlantaos to delete
   */
  where?: Prisma.PresencaDiaPlantaoWhereInput
  /**
   * Limit how many PresencaDiaPlantaos to delete.
   */
  limit?: number
}

/**
 * PresencaDiaPlantao.fechamento
 */
export type PresencaDiaPlantao$fechamentoArgs<ExtArgs extends runtime.Types.Extensions.InternalArgs = runtime.Types.Extensions.DefaultArgs> = {
  /**
   * Select specific fields to fetch from the Fechamento
   */
  select?: Prisma.FechamentoSelect<ExtArgs> | null
  /**
   * Omit specific fields from the Fechamento
   */
  omit?: Prisma.FechamentoOmit<ExtArgs> | null
  /**
   * Choose, which related nodes to fetch as well
   */
  include?: Prisma.FechamentoInclude<ExtArgs> | null
  where?: Prisma.FechamentoWhereInput
}

/**
 * PresencaDiaPlantao without action
 */
export type PresencaDiaPlantaoDefaultArgs<ExtArgs extends runtime.Types.Extensions.InternalArgs = runtime.Types.Extensions.DefaultArgs> = {
  /**
   * Select specific fields to fetch from the PresencaDiaPlantao
   */
  select?: Prisma.PresencaDiaPlantaoSelect<ExtArgs> | null
  /**
   * Omit specific fields from the PresencaDiaPlantao
   */
  omit?: Prisma.PresencaDiaPlantaoOmit<ExtArgs> | null
  /**
   * Choose, which related nodes to fetch as well
   */
  include?: Prisma.PresencaDiaPlantaoInclude<ExtArgs> | null
}
