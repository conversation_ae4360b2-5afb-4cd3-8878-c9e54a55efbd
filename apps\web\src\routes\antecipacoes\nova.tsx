import { useNavigate, useSearch } from "@tanstack/react-router";
import { createFileRoute } from "@tanstack/react-router";
import { useMutation, useQuery } from "@tanstack/react-query";
import { requireAdminRole } from "@/lib/route-guards";
import { useForm } from "@tanstack/react-form";
import { api, type Fechamento, type Plantao } from "@/lib/api";
import { Button } from "@/components/ui/button";
import { ArrowLeft, Save, Loader2, ChevronRight } from "lucide-react";
import { toast } from "sonner";
import { Link } from "@tanstack/react-router";
import { useState, useEffect, useMemo } from "react";
import { getCurrentDate, addDaysToDate, formatDateForInput } from "@shared/date";
import { PlantaoSelectionStep } from "@/components/antecipacao/plantao-selection-step";
import { FechamentoSelectionCard } from "@/components/antecipacao/fechamento-selection-card";
import { AntecipacaoConfigCard } from "@/components/antecipacao/antecipacao-config-card";
import { AntecipacaoSummaryCard } from "@/components/antecipacao/antecipacao-summary-card";

interface AntecipacaoForm {
  plantaoId: string;
  fechamentoIds: string[];
  valorSolicitado: number;
  valorAprovado?: number;
  percentual: number;
  taxaPadrao: number; // Taxa padrão mensal (editável)
  taxaAntecipacao: number; // Taxa aplicada (editável)
  valorDesconto: number; // Campo calculado (UI only)
  valorLiquido: number; // Campo calculado (UI only)
  dataPagamentoPrevista: string; // Calculada automaticamente
  observacoes?: string;
}

// Função para calcular taxa com juros compostos
function calcularTaxa(taxaSugeridaPercent: number, dias: number): number {
  const taxaSugerida = taxaSugeridaPercent / 100;

  if (dias <= 30) {
    return Number(taxaSugeridaPercent.toFixed(2));
  } else {
    const taxaEfetivaDecimal = Math.pow(1 + taxaSugerida, dias / 30) - 1;
    const taxaEfetivaPercent = taxaEfetivaDecimal * 100;
    return Number(taxaEfetivaPercent.toFixed(2));
  }
}

function AntecipacaoNova() {
  const navigate = useNavigate({ from: "/antecipacoes/nova" });
  const searchParams = useSearch({ from: "/antecipacoes/nova" });
  const plantaoId = searchParams.plantaoId;

  // Step control
  const currentStep = plantaoId ? 2 : 1;

  const [filters, setFilters] = useState({
    dataInicial: "",
    dataFinal: "",
    search: "",
  });

  // Step 1: List plantões available for antecipação
  // Also fetch when we have a plantaoId to find the selected plantao
  const { data: plantoes } = useQuery({
    queryKey: ["plantoes-disponiveis-antecipacao", filters],
    queryFn: () =>
      api.get<{ data: Plantao[] }>("/plantoes/disponiveis-antecipacao", {
        limit: 1000,
        ...filters,
      }),
    enabled: currentStep === 1 || !!plantaoId,
  });

  // Step 2: Get fechamentos for selected plantão
  const { data: fechamentos } = useQuery({
    queryKey: ["fechamentos-antecipacao", plantaoId],
    queryFn: () =>
      api.get<{ data: Fechamento[] }>("/fechamentos", {
        limit: 1000,
        status: "APROVADO",
        semAntecipacao: true,
        plantaoUuid: plantaoId, // Changed from plantaoId to plantaoUuid
      }),
    enabled: currentStep === 2 && !!plantaoId,
  });

  const mutation = useMutation({
    mutationFn: async (data: AntecipacaoForm) => {
      // Send only the fields that the backend expects
      const payload = {
        plantaoId: data.plantaoId,
        fechamentoIds: data.fechamentoIds,
        valorSolicitado: data.valorSolicitado,
        valorAprovado: data.valorAprovado,
        percentual: data.percentual,
        taxaAntecipacao: data.taxaAntecipacao,
        dataPagamentoPrevista: data.dataPagamentoPrevista,
        status: "PENDENTE",
        observacoes: data.observacoes,
      };
      return api.post("/antecipacoes", payload);
    },
    onSuccess: () => {
      toast.success("Solicitação de antecipação criada com sucesso!");
      navigate({ to: "/antecipacoes" });
    },
    onError: (error: any) => {
      toast.error(error.message || "Erro ao criar antecipação");
    },
  });

  const form = useForm({
    defaultValues: {
      plantaoId: plantaoId || "",
      fechamentoIds: [],
      valorSolicitado: 0,
      valorAprovado: 0,
      percentual: 100,
      taxaPadrao: 6.5, // Taxa padrão inicial
      taxaAntecipacao: 0,
      valorDesconto: 0,
      valorLiquido: 0,
      dataPagamentoPrevista: formatDateForInput(addDaysToDate(getCurrentDate(), 30)),
      observacoes: "",
    } as AntecipacaoForm,
    onSubmit: async ({ value }) => {
      if (!value.plantaoId) {
        toast.error("Selecione um plantão");
        return;
      }
      if (!value.fechamentoIds || value.fechamentoIds.length === 0) {
        toast.error("Selecione pelo menos um fechamento para antecipar");
        return;
      }
      if (value.valorSolicitado <= 0) {
        toast.error("Informe o valor a ser antecipado");
        return;
      }
      if (value.percentual > percentualMaximo) {
        toast.error(`Percentual máximo de antecipação para este cliente é ${percentualMaximo}%`);
        return;
      }
      mutation.mutate(value);
    },
  });

  const handlePlantaoSelect = (plantaoId: string) => {
    navigate({ to: "/antecipacoes/nova", search: { plantaoId } });
    form.setFieldValue("plantaoId", plantaoId);
  };

  const handleBackToStep1 = () => {
    navigate({ to: "/antecipacoes/nova", search: {} });
    form.reset();
  };

  // Step 2 logic for fechamentos selection
  const [selectedFechamentos, setSelectedFechamentos] = useState<string[]>([]);

  const selectedPlantao = plantoes?.data?.find((p) => p.uuid === plantaoId);

  // Percentual máximo baseado no cliente
  const percentualMaximo = (selectedPlantao?.cliente as any)?.prcentagemMaxima ?? 100;

  // Calcular dias até o vencimento e taxa com juros compostos
  const diasAteVencimento = useMemo(() => {
    if (!selectedPlantao?.prazoPagamentoDias) return 30; // Default 30 dias
    return selectedPlantao.prazoPagamentoDias;
  }, [selectedPlantao]);

  const taxaCalculada = useMemo(() => {
    const taxaPadrao = form.state.values.taxaPadrao || selectedPlantao?.cliente?.taxaPadrao || 5;
    return calcularTaxa(taxaPadrao, diasAteVencimento);
  }, [form.state.values.taxaPadrao, selectedPlantao, diasAteVencimento]);

  useEffect(() => {
    if (currentStep === 2 && fechamentos?.data && selectedPlantao) {
      // Start with no fechamentos selected - let user choose
      setSelectedFechamentos([]);
      form.setFieldValue("fechamentoIds", []);

      // Set initial configuration based on cliente and plantão
      const percentualAntecipacao = 80; // 80% para terceiros por padrão

      // Calcular data de pagamento prevista
      const diasPagamento = selectedPlantao.prazoPagamentoDias || 30;
      const dataPrevista = formatDateForInput(addDaysToDate(getCurrentDate(), diasPagamento));

      form.setFieldValue("valorSolicitado", 0);
      form.setFieldValue("valorAprovado", 0);
      form.setFieldValue("percentual", percentualAntecipacao);
      form.setFieldValue("taxaPadrao", selectedPlantao.cliente?.taxaPadrao);
      form.setFieldValue("taxaAntecipacao", taxaCalculada);
      form.setFieldValue("dataPagamentoPrevista", dataPrevista);
      calculateValues(0, taxaCalculada);
    }
  }, [currentStep, fechamentos?.data, selectedPlantao, taxaCalculada]);

  // Sincronizar taxa de antecipação quando taxa calculada mudar
  useEffect(() => {
    if (taxaCalculada > 0) {
      form.setFieldValue("taxaAntecipacao", taxaCalculada);
      calculateValues(form.state.values.valorSolicitado, taxaCalculada);
    }
  }, [taxaCalculada]);

  const calculateValues = (valor: number, taxa: number) => {
    const desconto = (valor * taxa) / 100;
    const liquido = valor - desconto;

    form.setFieldValue("valorDesconto", desconto);
    form.setFieldValue("valorLiquido", liquido);
  };

  const handleValorChange = (valor: number) => {
    const maxValue = totalSelectedValue;
    const clampedValue = Math.min(valor, maxValue);

    form.setFieldValue("valorSolicitado", clampedValue);
    form.setFieldValue("valorAprovado", clampedValue);

    if (valor > maxValue) {
      toast.warning("Valor solicitado ajustado ao máximo permitido");
    }

    calculateValues(clampedValue, form.state.values.taxaAntecipacao);
  };

  const handlePercentualChange = (percentual: number) => {
    const clampedPercentual = Math.min(percentualMaximo, Math.max(1, percentual));
    form.setFieldValue("percentual", clampedPercentual);

    // Recalcular valor sugerido baseado no novo percentual
    if (totalSelectedValue > 0) {
      const valorSugerido = (totalSelectedValue * clampedPercentual) / 100;
      form.setFieldValue("valorSolicitado", valorSugerido);
      form.setFieldValue("valorAprovado", valorSugerido);

      // Use the current tax rate from form state
      const taxaAtual = form.state.values.taxaAntecipacao;
      calculateValues(valorSugerido, taxaAtual);

      // Force form re-render to update summary
      form.validateAllFields("change");
    }
  };

  const handleTaxaChange = (taxa: number) => {
    form.setFieldValue("taxaAntecipacao", taxa);
    calculateValues(form.state.values.valorSolicitado, taxa);
  };

  const handleTaxaPadraoChange = (taxa: number) => {
    form.setFieldValue("taxaPadrao", taxa);
    // A taxa calculada será recalculada automaticamente pelo useMemo
    // Quando a taxa calculada mudar, precisamos atualizar a taxa de antecipação
  };

  const totalSelectedValue = useMemo(() => {
    if (!fechamentos?.data || selectedFechamentos.length === 0) return 0;
    return fechamentos.data
      .filter((f) => selectedFechamentos.includes(f.uuid))
      .reduce((acc, f) => acc + f.totalValor, 0);
  }, [fechamentos?.data, selectedFechamentos]);

  const handleFechamentoToggle = (fechamentoId: string) => {
    const newSelected = selectedFechamentos.includes(fechamentoId)
      ? selectedFechamentos.filter((id) => id !== fechamentoId)
      : [...selectedFechamentos, fechamentoId];

    setSelectedFechamentos(newSelected);
    form.setFieldValue("fechamentoIds", newSelected);

    // Recalculate suggested value based on selected fechamentos
    if (newSelected.length > 0 && fechamentos?.data) {
      const selectedValue = fechamentos.data
        .filter((f) => newSelected.includes(f.uuid))
        .reduce((acc, f) => acc + f.totalValor, 0);

      const percentualAntecipacao = form.state.values.percentual || 80;
      const valorSugerido = (selectedValue * percentualAntecipacao) / 100;

      form.setFieldValue("valorSolicitado", valorSugerido);
      form.setFieldValue("valorAprovado", valorSugerido);
      calculateValues(valorSugerido, form.state.values.taxaAntecipacao);
    } else {
      form.setFieldValue("valorSolicitado", 0);
      form.setFieldValue("valorAprovado", 0);
      calculateValues(0, form.state.values.taxaAntecipacao);
    }
  };

  return (
    <div className="space-y-6">
      <div className="flex items-center gap-4">
        <Link to="/antecipacoes">
          <Button variant="ghost" size="icon">
            <ArrowLeft className="h-4 w-4" />
          </Button>
        </Link>
        <div>
          <h1 className="text-3xl font-bold">Nova Antecipação</h1>
          <p className="text-muted-foreground">
            {currentStep === 1 ? "Selecione o plantão para antecipação" : "Configure a antecipação"}
          </p>
        </div>
      </div>

      {/* Step indicator */}
      <div className="flex items-center space-x-4">
        <div className={`flex items-center space-x-2 ${currentStep >= 1 ? "text-blue-600" : "text-gray-400"}`}>
          <div
            className={`w-8 h-8 rounded-full flex items-center justify-center ${currentStep >= 1 ? "bg-blue-600 text-white" : "bg-gray-200"}`}
          >
            1
          </div>
          <span className="font-medium">Selecionar Plantão</span>
        </div>
        <ChevronRight className="h-4 w-4 text-gray-400" />
        <div className={`flex items-center space-x-2 ${currentStep >= 2 ? "text-blue-600" : "text-gray-400"}`}>
          <div
            className={`w-8 h-8 rounded-full flex items-center justify-center ${currentStep >= 2 ? "bg-blue-600 text-white" : "bg-gray-200"}`}
          >
            2
          </div>
          <span className="font-medium">Configurar Antecipação</span>
        </div>
      </div>

      {currentStep === 1 && (
        <PlantaoSelectionStep
          plantoes={plantoes}
          filters={filters}
          onFiltersChange={setFilters}
          onPlantaoSelect={handlePlantaoSelect}
        />
      )}

      {currentStep === 2 && selectedPlantao && (
        <form
          onSubmit={(e) => {
            e.preventDefault();
            e.stopPropagation();
            form.handleSubmit();
          }}
        >
          <div className="grid gap-6 lg:grid-cols-2">
            <div className="space-y-6">
              <FechamentoSelectionCard
                selectedPlantao={selectedPlantao}
                fechamentos={fechamentos}
                selectedFechamentos={selectedFechamentos}
                totalSelectedValue={totalSelectedValue}
                onBackToStep1={handleBackToStep1}
                onFechamentoToggle={handleFechamentoToggle}
                onSelectAll={() => {
                  const allIds = fechamentos?.data?.map((f) => f.uuid) || [];
                  setSelectedFechamentos(allIds);
                  form.setFieldValue("fechamentoIds", allIds);

                  const totalValue = fechamentos?.data?.reduce((acc, f) => acc + f.totalValor, 0) || 0;
                  const percentualAntecipacao = form.state.values.percentual || 80;
                  const valorSugerido = (totalValue * percentualAntecipacao) / 100;

                  form.setFieldValue("valorSolicitado", valorSugerido);
                  form.setFieldValue("valorAprovado", valorSugerido);
                  calculateValues(valorSugerido, form.state.values.taxaAntecipacao);
                }}
                onClearSelection={() => {
                  setSelectedFechamentos([]);
                  form.setFieldValue("fechamentoIds", []);
                  form.setFieldValue("valorSolicitado", 0);
                  form.setFieldValue("valorAprovado", 0);
                  calculateValues(0, form.state.values.taxaAntecipacao);
                }}
              />
            </div>

            <div className="space-y-6">
              <form.Field name="valorSolicitado">
                {(valorSolicitadoField) => (
                  <form.Field name="dataPagamentoPrevista">
                    {(dataPagamentoPrevistaField) => (
                      <form.Field name="observacoes">
                        {(observacoesField) => (
                          <form.Field name="percentual">
                            {(percentualField) => (
                              <form.Field name="taxaPadrao">
                                {(taxaPadraoField) => (
                                  <form.Field name="taxaAntecipacao">
                                    {(taxaAntecipacaoField) => (
                                      <AntecipacaoConfigCard
                                        valorSolicitadoField={valorSolicitadoField}
                                        dataPagamentoPrevistaField={dataPagamentoPrevistaField}
                                        observacoesField={observacoesField}
                                        percentualField={percentualField}
                                        taxaPadraoField={taxaPadraoField}
                                        taxaAntecipacaoField={taxaAntecipacaoField}
                                        formValues={form.state.values}
                                        totalSelectedValue={totalSelectedValue}
                                        onValorChange={handleValorChange}
                                        onTaxaChange={handleTaxaChange}
                                        onTaxaPadraoChange={handleTaxaPadraoChange}
                                        onPercentualChange={handlePercentualChange}
                                        diasAteVencimento={diasAteVencimento}
                                        taxaCalculada={taxaCalculada}
                                        percentualMaximo={percentualMaximo}
                                      />
                                    )}
                                  </form.Field>
                                )}
                              </form.Field>
                            )}
                          </form.Field>
                        )}
                      </form.Field>
                    )}
                  </form.Field>
                )}
              </form.Field>

              <AntecipacaoSummaryCard
                totalSelectedValue={totalSelectedValue}
                valorSolicitado={form.state.values.valorSolicitado}
                taxaAntecipacao={form.state.values.taxaAntecipacao}
                valorDesconto={form.state.values.valorDesconto}
                valorLiquido={form.state.values.valorLiquido}
              />

              <div className="flex gap-4">
                <Button type="button" variant="outline" onClick={handleBackToStep1} className="flex-1">
                  Voltar
                </Button>
                <Button type="submit" disabled={mutation.isPending} className="flex-1">
                  {mutation.isPending && <Loader2 className="mr-2 h-4 w-4 animate-spin" />}
                  <Save className="mr-2 h-4 w-4" />
                  Criar Antecipação
                </Button>
              </div>
            </div>
          </div>
        </form>
      )}
    </div>
  );
}

export const Route = createFileRoute("/antecipacoes/nova")({
  component: AntecipacaoNova,
  beforeLoad: async () => {
    await requireAdminRole();
  },
  validateSearch: (search: Record<string, unknown>): { plantaoId?: string } => {
    return {
      plantaoId: typeof search.plantaoId === "string" ? search.plantaoId : undefined,
    };
  },
});
