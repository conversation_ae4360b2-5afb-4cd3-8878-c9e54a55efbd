import { Link, useLocation, useNavigate } from "@tanstack/react-router";
import { cn } from "@/lib/utils";
import { useUIStore } from "@/stores/use-ui.store";
import { useAuthStore } from "@/stores/use-auth.store";
import {
  LayoutDashboard,
  Users,
  Calendar,
  FileCheck,
  DollarSign,
  ChevronLeft,
  ChevronRight,
  Building2,
  MapPin,
  Menu,
  X,
  Shield,
  UserCog,
  LogOut,
  User,
  SwitchCamera,
  UserCircle,
} from "lucide-react";
import { Button } from "./ui/button";
import {
  DropdownMenu,
  DropdownMenuContent,
  DropdownMenuItem,
  DropdownMenuLabel,
  DropdownMenuSeparator,
  DropdownMenuTrigger,
} from "@/components/ui/dropdown-menu";
import { useState } from "react";
import { ProfileSelectionModal } from "@/components/profile-selection-modal";
import { ClientSelectionModal } from "@/components/client-selection-modal";

type MenuItem = {
  title: string;
  icon: React.ComponentType<any>;
  href: string;
  notLink?: boolean;
  subItems?: Array<{ title: string; href: string; icon: React.ComponentType<any> }>;
};

const adminMenuItems: MenuItem[] = [
  {
    title: "Dashboard",
    icon: LayoutDashboard,
    href: "/",
  },
  {
    title: "Cadastros",
    icon: Users,
    href: "/cadastros",
    notLink: true,
    subItems: [
      { title: "Clientes", href: "/cadastros/clientes", icon: Building2 },
      { title: "Profissionais", href: "/cadastros/profissionais", icon: Users },
      { title: "Usuários", icon: UserCog, href: "/cadastros/usuarios" },
    ],
  },
  {
    title: "Plantões",
    icon: Calendar,
    href: "/plantoes",
  },
  {
    title: "Fechamentos",
    icon: FileCheck,
    href: "/fechamentos",
  },
  {
    title: "Antecipações",
    icon: DollarSign,
    href: "/antecipacoes",
  },
  {
    title: "Auditoria",
    icon: Shield,
    href: "/auditoria",
  },
];

const profissionalMenuItems: MenuItem[] = [
  {
    title: "Dashboard",
    icon: LayoutDashboard,
    href: "/profissional/dashboard",
  },
  {
    title: "Plantões",
    icon: Calendar,
    href: "/profissional/plantoes",
  },
  {
    title: "Meus Fechamentos",
    icon: FileCheck,
    href: "/profissional/fechamentos",
  },
  {
    title: "Minhas Antecipações",
    icon: DollarSign,
    href: "/profissional/antecipacoes",
  },
];

const gestorMenuItems: MenuItem[] = [
  {
    title: "Dashboard",
    icon: LayoutDashboard,
    href: "/",
  },
  {
    title: "Profissionais",
    icon: Users,
    href: "/cadastros/profissionais",
  },
  {
    title: "Plantões",
    icon: Calendar,
    href: "/plantoes",
  },
  {
    title: "Fechamentos",
    icon: FileCheck,
    href: "/fechamentos",
  },
  {
    title: "Antecipações",
    icon: DollarSign,
    href: "/antecipacoes",
  },
];

const coordenadorMenuItems: MenuItem[] = [
  {
    title: "Dashboard",
    icon: LayoutDashboard,
    href: "/",
  },
  {
    title: "Plantões",
    icon: Calendar,
    href: "/plantoes",
  },
  {
    title: "Fechamentos",
    icon: FileCheck,
    href: "/fechamentos",
  },
  {
    title: "Profissionais",
    icon: Users,
    href: "/cadastros/profissionais",
  },
];

interface SidebarProps {
  onMobileClose?: () => void;
}

export function Sidebar({ onMobileClose }: SidebarProps) {
  const location = useLocation();
  const navigate = useNavigate();
  const { sidebarOpen, sidebarCollapsed, toggleSidebar, toggleSidebarCollapse } = useUIStore();
  const { logout, user, perfilSelecionado, clienteSelecionado, perfis, clientes, selecionarPerfil, selecionarCliente } =
    useAuthStore();
  const [showProfileModal, setShowProfileModal] = useState(false);
  const [showClientModal, setShowClientModal] = useState(false);

  // Determinar tipo de menu baseado no perfil selecionado ou roles do usuário
  const getMenuItems = () => {
    // Priorizar perfil selecionado
    const perfilNome = perfilSelecionado?.nome?.toLowerCase();

    if (perfilNome) {
      switch (perfilNome) {
        case "profissional":
          return profissionalMenuItems;
        case "gestor":
          return gestorMenuItems;
        case "coordenador":
          return coordenadorMenuItems;
        case "admin":
        case "master":
        case "financeiro":
          return adminMenuItems;
        default:
          return adminMenuItems;
      }
    }

    // Se não tem perfil selecionado, usa as roles
    if (user?.roles?.includes("profissional")) {
      return profissionalMenuItems;
    }

    // Default para admin
    return adminMenuItems;
  };

  const termosAceitos = user?.termosAceitos ?? false;
  const onboardingPendente = user?.metaData?.onboardingPendente ?? false;
  const menuItems = getMenuItems();

  const isActive = (href: string) => {
    if (href === "/") {
      return location.pathname === href;
    }
    return location.pathname.startsWith(href);
  };

  const handleLogout = () => {
    logout();
    navigate({ to: "/login", replace: true });
  };

  const handleProfileChange = () => {
    setShowProfileModal(true);
  };

  const handleClientChange = () => {
    setShowClientModal(true);
  };

  const handleProfileSelect = (perfil: any) => {
    selecionarPerfil(perfil);
    setShowProfileModal(false);

    // Redirecionar para a página correta baseado no perfil
    const perfilNome = perfil.nome?.toLowerCase();

    if (perfilNome === "profissional") {
      navigate({ to: "/profissional/dashboard", reloadDocument: true });
    } else {
      // Gestor, Coordenador, Admin, Master, Financeiro vão para dashboard admin
      navigate({ to: "/", reloadDocument: true });
    }
  };

  const handleClientSelect = (cliente: any) => {
    selecionarCliente(cliente);
    // setShowClientModal(false);

    window.location.reload();
  };

  const hideSideBar = !termosAceitos || onboardingPendente;

  return (
    <>
      {/* Mobile overlay */}
      {sidebarOpen && <div className="fixed inset-0 bg-black/50 z-40 lg:hidden" onClick={toggleSidebar} />}

      {/* Sidebar */}
      <aside
        className={cn(
          "fixed top-0 left-0 z-50 h-full bg-background border-r transition-all duration-300",
          sidebarOpen ? "translate-x-0" : "-translate-x-full lg:translate-x-0",
          sidebarCollapsed ? "lg:w-16" : "lg:w-64",
          "w-64"
        )}
      >
        <div className="flex h-16 items-center justify-between px-4 border-b">
          <div className="flex items-center gap-2">
            {/* <img src="/logo.png" alt="GS2" className="h-8 w-8" /> */}
            <img src="/logo-fundo-branco.png" alt="GS2" className="h-8 w-8" />
            {/* {!sidebarCollapsed && <h2 className="text-lg font-semibold">GS2</h2>} */}
          </div>
          <div className="flex items-center gap-2">
            <Button variant="ghost" size="icon" onClick={onMobileClose || toggleSidebar} className="lg:hidden">
              <X className="h-4 w-4" />
            </Button>
            <Button variant="ghost" size="icon" onClick={toggleSidebarCollapse} className="hidden lg:flex">
              {sidebarCollapsed ? <ChevronRight className="h-4 w-4" /> : <ChevronLeft className="h-4 w-4" />}
            </Button>
          </div>
        </div>

        <div className="flex flex-col h-[calc(100%-4rem)]">
          {!hideSideBar && (
            <nav className="flex-1 overflow-y-auto p-4">
              <ul className="space-y-2">
                {menuItems.map((item) => (
                  <li key={item.href}>
                    {item.subItems ? (
                      <div className="space-y-1">
                        {item.notLink ? (
                          <span
                            className={cn(
                              "flex items-center gap-3 rounded-lg px-3 py-2 text-sm transition-colors",
                              isActive(item.href) ? "bg-primary text-primary-foreground" : "hover:bg-muted"
                            )}
                          >
                            <item.icon className="h-4 w-4 shrink-0" />
                            {!sidebarCollapsed && <span>{item.title}</span>}
                          </span>
                        ) : (
                          <Link
                            to={item.href}
                            className={cn(
                              "flex items-center gap-3 rounded-lg px-3 py-2 text-sm transition-colors",
                              hideSideBar && item.href !== "/termos" && "opacity-50 cursor-not-allowed",
                              isActive(item.href) ? "bg-primary text-primary-foreground" : "hover:bg-muted"
                            )}
                          >
                            <item.icon className="h-4 w-4 shrink-0" />
                            {!sidebarCollapsed && <span>{item.title}</span>}
                          </Link>
                        )}

                        {!sidebarCollapsed && (
                          <ul className="ml-7 space-y-1">
                            {item.subItems.map((subItem) => (
                              <li key={subItem.href}>
                                <Link
                                  to={subItem.href}
                                  className={cn(
                                    "flex items-center gap-2 rounded-lg px-3 py-1.5 text-sm transition-colors",
                                    hideSideBar && subItem.href !== "/termos" && "opacity-50 cursor-not-allowed",
                                    isActive(subItem.href) ? "bg-primary/10 text-primary" : "hover:bg-muted"
                                  )}
                                >
                                  <subItem.icon className="h-3 w-3" />
                                  <span>{subItem.title}</span>
                                </Link>
                              </li>
                            ))}
                          </ul>
                        )}
                      </div>
                    ) : (
                      <Link
                        to={item.href}
                        className={cn(
                          "flex items-center gap-3 rounded-lg px-3 py-2 text-sm transition-colors",
                          hideSideBar && item.href !== "/termos" && "opacity-50 cursor-not-allowed",
                          isActive(item.href) ? "bg-primary text-primary-foreground" : "hover:bg-muted"
                        )}
                      >
                        <item.icon className="h-4 w-4 shrink-0" />
                        {!sidebarCollapsed && <span>{item.title}</span>}
                      </Link>
                    )}
                  </li>
                ))}
              </ul>
            </nav>
          )}

          {/* User info and logout */}
          <div className="border-t p-4">
            {user && (
              <div className="space-y-3">
                {!sidebarCollapsed && (
                  <div className="space-y-2">
                    <div className="text-sm">
                      <p className="font-medium">{user.nome}</p>
                      <p className="text-xs text-muted-foreground">{user.email}</p>
                    </div>

                    {/* Perfil e Cliente selecionados */}
                    {(perfis.length > 1 || clientes.length > 1) && (
                      <div className="space-y-1 text-xs">
                        {perfis.length > 1 && perfilSelecionado && (
                          <button
                            onClick={handleProfileChange}
                            className="flex items-center gap-1 text-muted-foreground hover:text-foreground transition-colors w-full text-left"
                          >
                            <UserCircle className="h-3 w-3" />
                            <span>Perfil: {perfilSelecionado.nome}</span>
                            <SwitchCamera className="h-3 w-3 ml-auto" />
                          </button>
                        )}
                        {clientes.length > 1 && clienteSelecionado && (
                          <button
                            onClick={handleClientChange}
                            className="flex items-center gap-1 text-muted-foreground hover:text-foreground transition-colors w-full text-left"
                          >
                            <Building2 className="h-3 w-3" />
                            <span>Cliente: {clienteSelecionado.nome}</span>
                            <SwitchCamera className="h-3 w-3 ml-auto" />
                          </button>
                        )}
                      </div>
                    )}
                  </div>
                )}

                {sidebarCollapsed && (perfis.length > 1 || clientes.length > 1) && (
                  <DropdownMenu>
                    <DropdownMenuTrigger asChild>
                      <Button variant="ghost" size="icon" className="w-full">
                        <SwitchCamera className="h-4 w-4" />
                      </Button>
                    </DropdownMenuTrigger>
                    <DropdownMenuContent side="right" align="end" className="w-56">
                      <DropdownMenuLabel>Configurações</DropdownMenuLabel>
                      <DropdownMenuSeparator />
                      {perfis.length > 1 && (
                        <DropdownMenuItem onClick={handleProfileChange}>
                          <UserCircle className="mr-2 h-4 w-4" />
                          Trocar Perfil
                        </DropdownMenuItem>
                      )}
                      {clientes.length > 1 && (
                        <DropdownMenuItem onClick={handleClientChange}>
                          <Building2 className="mr-2 h-4 w-4" />
                          Trocar Cliente
                        </DropdownMenuItem>
                      )}
                    </DropdownMenuContent>
                  </DropdownMenu>
                )}

                <div className="space-y-2">
                  <Link
                    to="/perfil"
                    className={cn(
                      "flex items-center justify-center gap-2 rounded-lg px-3 py-2 text-sm transition-colors hover:bg-muted",
                      sidebarCollapsed && "px-2"
                    )}
                  >
                    <User className="h-4 w-4" />
                    {!sidebarCollapsed && <span>Meu Perfil</span>}
                  </Link>
                  <Button
                    onClick={handleLogout}
                    variant="outline"
                    size="sm"
                    className={cn("w-full", sidebarCollapsed && "px-2")}
                  >
                    <LogOut className="h-4 w-4" />
                    {!sidebarCollapsed && <span className="ml-2">Sair</span>}
                  </Button>
                </div>
              </div>
            )}
          </div>
        </div>
      </aside>

      {/* Mobile menu button */}
      <Button variant="ghost" size="icon" onClick={toggleSidebar} className="fixed top-4 left-4 z-30 lg:hidden">
        <Menu className="h-5 w-5" />
      </Button>

      {/* Modais de seleção */}
      <ProfileSelectionModal open={showProfileModal} perfis={perfis} onSelect={handleProfileSelect} />

      <ClientSelectionModal open={showClientModal} clientes={clientes} onSelect={handleClientSelect} />
    </>
  );
}
