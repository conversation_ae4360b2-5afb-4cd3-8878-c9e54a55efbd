import { defineConfig } from "tsdown";
import fs from "fs";
import { execSync } from "child_process";

export default defineConfig({
  entry: ["./src"],
  minify: true,
  outDir: "./dist",
  external: ["@prisma/client"],
  hooks: {
    "build:done": () => {
      fs.copyFileSync("./package.json", "./dist/package.json");

      // Copy Prisma schema
      if (!fs.existsSync("./dist/prisma")) {
        fs.mkdirSync("./dist/prisma", { recursive: true });
      }
      execSync("cp -r ./prisma ./dist/", { stdio: "inherit" });

      // Fix Prisma schema for production
      const schemaPath = "./dist/prisma/schema/schema.prisma";
      let schema = fs.readFileSync(schemaPath, "utf-8");
      schema = schema.replace(
        /generator client \{[\s\S]*?\}/,
        `generator client {
          provider = "prisma-client-js"
        }`
      );
      fs.writeFileSync(schemaPath, schema);

      execSync("npm install --omit=dev", {
        cwd: "./dist",
        stdio: "inherit",
      });

      // Generate Prisma client
      execSync("npx prisma generate --schema=./prisma/schema/schema.prisma", {
        cwd: "./dist",
        stdio: "inherit",
      });
    },
  },
});
