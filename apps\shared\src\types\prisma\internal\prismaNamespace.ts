
/* !!! This is code generated by <PERSON>risma. Do not edit directly. !!! */
/* eslint-disable */
// @ts-nocheck 
/*
 * WARNING: This is an internal file that is subject to change!
 *
 * 🛑 Under no circumstances should you import this file directly! 🛑
 *
 * All exports from this file are wrapped under a `Prisma` namespace object in the client.ts file.
 * While this enables partial backward compatibility, it is not part of the stable public API.
 *
 * If you are looking for your Models, Enums, and Input Types, please import them from the respective
 * model files in the `model` directory!
 */

import * as runtime from "@prisma/client/runtime/library"
import type * as Prisma from "../models.ts"
import { type PrismaClient } from "./class.ts"

export type * from '../models.ts'

export type DMMF = typeof runtime.DMMF

export type PrismaPromise<T> = runtime.Types.Public.PrismaPromise<T>

/**
 * Validator
 */
export const validator = runtime.Public.validator

/**
 * Prisma Errors
 */

export const PrismaClientKnownRequestError = runtime.PrismaClientKnownRequestError
export type PrismaClientKnownRequestError = runtime.PrismaClientKnownRequestError

export const PrismaClientUnknownRequestError = runtime.PrismaClientUnknownRequestError
export type PrismaClientUnknownRequestError = runtime.PrismaClientUnknownRequestError

export const PrismaClientRustPanicError = runtime.PrismaClientRustPanicError
export type PrismaClientRustPanicError = runtime.PrismaClientRustPanicError

export const PrismaClientInitializationError = runtime.PrismaClientInitializationError
export type PrismaClientInitializationError = runtime.PrismaClientInitializationError

export const PrismaClientValidationError = runtime.PrismaClientValidationError
export type PrismaClientValidationError = runtime.PrismaClientValidationError

/**
 * Re-export of sql-template-tag
 */
export const sql = runtime.sqltag
export const empty = runtime.empty
export const join = runtime.join
export const raw = runtime.raw
export const Sql = runtime.Sql
export type Sql = runtime.Sql



/**
 * Decimal.js
 */
export const Decimal = runtime.Decimal
export type Decimal = runtime.Decimal

export type DecimalJsLike = runtime.DecimalJsLike

/**
 * Metrics
 */
export type Metrics = runtime.Metrics
export type Metric<T> = runtime.Metric<T>
export type MetricHistogram = runtime.MetricHistogram
export type MetricHistogramBucket = runtime.MetricHistogramBucket

/**
* Extensions
*/
export type Extension = runtime.Types.Extensions.UserArgs
export const getExtensionContext = runtime.Extensions.getExtensionContext
export type Args<T, F extends runtime.Operation> = runtime.Types.Public.Args<T, F>
export type Payload<T, F extends runtime.Operation = never> = runtime.Types.Public.Payload<T, F>
export type Result<T, A, F extends runtime.Operation> = runtime.Types.Public.Result<T, A, F>
export type Exact<A, W> = runtime.Types.Public.Exact<A, W>

export type PrismaVersion = {
  client: string
  engine: string
}

/**
 * Prisma Client JS version: 6.15.0
 * Query Engine version: 85179d7826409ee107a6ba334b5e305ae3fba9fb
 */
export const prismaVersion: PrismaVersion = {
  client: "6.15.0",
  engine: "85179d7826409ee107a6ba334b5e305ae3fba9fb"
}

/**
 * Utility Types
 */

export type JsonObject = runtime.JsonObject
export type JsonArray = runtime.JsonArray
export type JsonValue = runtime.JsonValue
export type InputJsonObject = runtime.InputJsonObject
export type InputJsonArray = runtime.InputJsonArray
export type InputJsonValue = runtime.InputJsonValue

export const NullTypes = {
  DbNull: runtime.objectEnumValues.classes.DbNull as (new (secret: never) => typeof runtime.objectEnumValues.instances.DbNull),
  JsonNull: runtime.objectEnumValues.classes.JsonNull as (new (secret: never) => typeof runtime.objectEnumValues.instances.JsonNull),
  AnyNull: runtime.objectEnumValues.classes.AnyNull as (new (secret: never) => typeof runtime.objectEnumValues.instances.AnyNull),
}

/**
 * Helper for filtering JSON entries that have `null` on the database (empty on the db)
 *
 * @see https://www.prisma.io/docs/concepts/components/prisma-client/working-with-fields/working-with-json-fields#filtering-on-a-json-field
 */
export const DbNull = runtime.objectEnumValues.instances.DbNull

/**
 * Helper for filtering JSON entries that have JSON `null` values (not empty on the db)
 *
 * @see https://www.prisma.io/docs/concepts/components/prisma-client/working-with-fields/working-with-json-fields#filtering-on-a-json-field
 */
export const JsonNull = runtime.objectEnumValues.instances.JsonNull

/**
 * Helper for filtering JSON entries that are `Prisma.DbNull` or `Prisma.JsonNull`
 *
 * @see https://www.prisma.io/docs/concepts/components/prisma-client/working-with-fields/working-with-json-fields#filtering-on-a-json-field
 */
export const AnyNull = runtime.objectEnumValues.instances.AnyNull

type SelectAndInclude = {
  select: any
  include: any
}

type SelectAndOmit = {
  select: any
  omit: any
}

/**
 * From T, pick a set of properties whose keys are in the union K
 */
type Prisma__Pick<T, K extends keyof T> = {
    [P in K]: T[P];
};

export type Enumerable<T> = T | Array<T>;

/**
 * Subset
 * @desc From `T` pick properties that exist in `U`. Simple version of Intersection
 */
export type Subset<T, U> = {
  [key in keyof T]: key extends keyof U ? T[key] : never;
};

/**
 * SelectSubset
 * @desc From `T` pick properties that exist in `U`. Simple version of Intersection.
 * Additionally, it validates, if both select and include are present. If the case, it errors.
 */
export type SelectSubset<T, U> = {
  [key in keyof T]: key extends keyof U ? T[key] : never
} &
  (T extends SelectAndInclude
    ? 'Please either choose `select` or `include`.'
    : T extends SelectAndOmit
      ? 'Please either choose `select` or `omit`.'
      : {})

/**
 * Subset + Intersection
 * @desc From `T` pick properties that exist in `U` and intersect `K`
 */
export type SubsetIntersection<T, U, K> = {
  [key in keyof T]: key extends keyof U ? T[key] : never
} &
  K

type Without<T, U> = { [P in Exclude<keyof T, keyof U>]?: never };

/**
 * XOR is needed to have a real mutually exclusive union type
 * https://stackoverflow.com/questions/42123407/does-typescript-support-mutually-exclusive-types
 */
export type XOR<T, U> =
  T extends object ?
  U extends object ?
    (Without<T, U> & U) | (Without<U, T> & T)
  : U : T


/**
 * Is T a Record?
 */
type IsObject<T extends any> = T extends Array<any>
? False
: T extends Date
? False
: T extends Uint8Array
? False
: T extends BigInt
? False
: T extends object
? True
: False


/**
 * If it's T[], return T
 */
export type UnEnumerate<T extends unknown> = T extends Array<infer U> ? U : T

/**
 * From ts-toolbelt
 */

type __Either<O extends object, K extends Key> = Omit<O, K> &
  {
    // Merge all but K
    [P in K]: Prisma__Pick<O, P & keyof O> // With K possibilities
  }[K]

type EitherStrict<O extends object, K extends Key> = Strict<__Either<O, K>>

type EitherLoose<O extends object, K extends Key> = ComputeRaw<__Either<O, K>>

type _Either<
  O extends object,
  K extends Key,
  strict extends Boolean
> = {
  1: EitherStrict<O, K>
  0: EitherLoose<O, K>
}[strict]

export type Either<
  O extends object,
  K extends Key,
  strict extends Boolean = 1
> = O extends unknown ? _Either<O, K, strict> : never

export type Union = any

export type PatchUndefined<O extends object, O1 extends object> = {
  [K in keyof O]: O[K] extends undefined ? At<O1, K> : O[K]
} & {}

/** Helper Types for "Merge" **/
export type IntersectOf<U extends Union> = (
  U extends unknown ? (k: U) => void : never
) extends (k: infer I) => void
  ? I
  : never

export type Overwrite<O extends object, O1 extends object> = {
    [K in keyof O]: K extends keyof O1 ? O1[K] : O[K];
} & {};

type _Merge<U extends object> = IntersectOf<Overwrite<U, {
    [K in keyof U]-?: At<U, K>;
}>>;

type Key = string | number | symbol;
type AtStrict<O extends object, K extends Key> = O[K & keyof O];
type AtLoose<O extends object, K extends Key> = O extends unknown ? AtStrict<O, K> : never;
export type At<O extends object, K extends Key, strict extends Boolean = 1> = {
    1: AtStrict<O, K>;
    0: AtLoose<O, K>;
}[strict];

export type ComputeRaw<A extends any> = A extends Function ? A : {
  [K in keyof A]: A[K];
} & {};

export type OptionalFlat<O> = {
  [K in keyof O]?: O[K];
} & {};

type _Record<K extends keyof any, T> = {
  [P in K]: T;
};

// cause typescript not to expand types and preserve names
type NoExpand<T> = T extends unknown ? T : never;

// this type assumes the passed object is entirely optional
export type AtLeast<O extends object, K extends string> = NoExpand<
  O extends unknown
  ? | (K extends keyof O ? { [P in K]: O[P] } & O : O)
    | {[P in keyof O as P extends K ? P : never]-?: O[P]} & O
  : never>;

type _Strict<U, _U = U> = U extends unknown ? U & OptionalFlat<_Record<Exclude<Keys<_U>, keyof U>, never>> : never;

export type Strict<U extends object> = ComputeRaw<_Strict<U>>;
/** End Helper Types for "Merge" **/

export type Merge<U extends object> = ComputeRaw<_Merge<Strict<U>>>;

export type Boolean = True | False

export type True = 1

export type False = 0

export type Not<B extends Boolean> = {
  0: 1
  1: 0
}[B]

export type Extends<A1 extends any, A2 extends any> = [A1] extends [never]
  ? 0 // anything `never` is false
  : A1 extends A2
  ? 1
  : 0

export type Has<U extends Union, U1 extends Union> = Not<
  Extends<Exclude<U1, U>, U1>
>

export type Or<B1 extends Boolean, B2 extends Boolean> = {
  0: {
    0: 0
    1: 1
  }
  1: {
    0: 1
    1: 1
  }
}[B1][B2]

export type Keys<U extends Union> = U extends unknown ? keyof U : never

export type GetScalarType<T, O> = O extends object ? {
  [P in keyof T]: P extends keyof O
    ? O[P]
    : never
} : never

type FieldPaths<
  T,
  U = Omit<T, '_avg' | '_sum' | '_count' | '_min' | '_max'>
> = IsObject<T> extends True ? U : T

export type GetHavingFields<T> = {
  [K in keyof T]: Or<
    Or<Extends<'OR', K>, Extends<'AND', K>>,
    Extends<'NOT', K>
  > extends True
    ? // infer is only needed to not hit TS limit
      // based on the brilliant idea of Pierre-Antoine Mills
      // https://github.com/microsoft/TypeScript/issues/30188#issuecomment-478938437
      T[K] extends infer TK
      ? GetHavingFields<UnEnumerate<TK> extends object ? Merge<UnEnumerate<TK>> : never>
      : never
    : {} extends FieldPaths<T[K]>
    ? never
    : K
}[keyof T]

/**
 * Convert tuple to union
 */
type _TupleToUnion<T> = T extends (infer E)[] ? E : never
type TupleToUnion<K extends readonly any[]> = _TupleToUnion<K>
export type MaybeTupleToUnion<T> = T extends any[] ? TupleToUnion<T> : T

/**
 * Like `Pick`, but additionally can also accept an array of keys
 */
export type PickEnumerable<T, K extends Enumerable<keyof T> | keyof T> = Prisma__Pick<T, MaybeTupleToUnion<K>>

/**
 * Exclude all keys with underscores
 */
export type ExcludeUnderscoreKeys<T extends string> = T extends `_${string}` ? never : T


export type FieldRef<Model, FieldType> = runtime.FieldRef<Model, FieldType>

type FieldRefInputType<Model, FieldType> = Model extends never ? never : FieldRef<Model, FieldType>


export const ModelName = {
  Usuario: 'Usuario',
  Role: 'Role',
  UsuarioRole: 'UsuarioRole',
  Cliente: 'Cliente',
  Especialidade: 'Especialidade',
  Profissional: 'Profissional',
  ProfissionalEspecialidade: 'ProfissionalEspecialidade',
  UsuarioCliente: 'UsuarioCliente',
  LocalAtendimento: 'LocalAtendimento',
  LocalAtendimentoEspecialidades: 'LocalAtendimentoEspecialidades',
  Plantao: 'Plantao',
  DiaPlantao: 'DiaPlantao',
  PresencaDiaPlantao: 'PresencaDiaPlantao',
  Fechamento: 'Fechamento',
  Antecipacao: 'Antecipacao',
  AntecipacaoHistorico: 'AntecipacaoHistorico',
  AuditLog: 'AuditLog',
  GerenciamentoTermosLgpd: 'GerenciamentoTermosLgpd',
  AceiteTermosLgpd: 'AceiteTermosLgpd'
} as const

export type ModelName = (typeof ModelName)[keyof typeof ModelName]



export interface TypeMapCb<GlobalOmitOptions = {}> extends runtime.Types.Utils.Fn<{extArgs: runtime.Types.Extensions.InternalArgs }, runtime.Types.Utils.Record<string, any>> {
  returns: TypeMap<this['params']['extArgs'], GlobalOmitOptions>
}

export type TypeMap<ExtArgs extends runtime.Types.Extensions.InternalArgs = runtime.Types.Extensions.DefaultArgs, GlobalOmitOptions = {}> = {
  globalOmitOptions: {
    omit: GlobalOmitOptions
  }
  meta: {
    modelProps: "usuario" | "role" | "usuarioRole" | "cliente" | "especialidade" | "profissional" | "profissionalEspecialidade" | "usuarioCliente" | "localAtendimento" | "localAtendimentoEspecialidades" | "plantao" | "diaPlantao" | "presencaDiaPlantao" | "fechamento" | "antecipacao" | "antecipacaoHistorico" | "auditLog" | "gerenciamentoTermosLgpd" | "aceiteTermosLgpd"
    txIsolationLevel: TransactionIsolationLevel
  }
  model: {
    Usuario: {
      payload: Prisma.$UsuarioPayload<ExtArgs>
      fields: Prisma.UsuarioFieldRefs
      operations: {
        findUnique: {
          args: Prisma.UsuarioFindUniqueArgs<ExtArgs>
          result: runtime.Types.Utils.PayloadToResult<Prisma.$UsuarioPayload> | null
        }
        findUniqueOrThrow: {
          args: Prisma.UsuarioFindUniqueOrThrowArgs<ExtArgs>
          result: runtime.Types.Utils.PayloadToResult<Prisma.$UsuarioPayload>
        }
        findFirst: {
          args: Prisma.UsuarioFindFirstArgs<ExtArgs>
          result: runtime.Types.Utils.PayloadToResult<Prisma.$UsuarioPayload> | null
        }
        findFirstOrThrow: {
          args: Prisma.UsuarioFindFirstOrThrowArgs<ExtArgs>
          result: runtime.Types.Utils.PayloadToResult<Prisma.$UsuarioPayload>
        }
        findMany: {
          args: Prisma.UsuarioFindManyArgs<ExtArgs>
          result: runtime.Types.Utils.PayloadToResult<Prisma.$UsuarioPayload>[]
        }
        create: {
          args: Prisma.UsuarioCreateArgs<ExtArgs>
          result: runtime.Types.Utils.PayloadToResult<Prisma.$UsuarioPayload>
        }
        createMany: {
          args: Prisma.UsuarioCreateManyArgs<ExtArgs>
          result: BatchPayload
        }
        delete: {
          args: Prisma.UsuarioDeleteArgs<ExtArgs>
          result: runtime.Types.Utils.PayloadToResult<Prisma.$UsuarioPayload>
        }
        update: {
          args: Prisma.UsuarioUpdateArgs<ExtArgs>
          result: runtime.Types.Utils.PayloadToResult<Prisma.$UsuarioPayload>
        }
        deleteMany: {
          args: Prisma.UsuarioDeleteManyArgs<ExtArgs>
          result: BatchPayload
        }
        updateMany: {
          args: Prisma.UsuarioUpdateManyArgs<ExtArgs>
          result: BatchPayload
        }
        upsert: {
          args: Prisma.UsuarioUpsertArgs<ExtArgs>
          result: runtime.Types.Utils.PayloadToResult<Prisma.$UsuarioPayload>
        }
        aggregate: {
          args: Prisma.UsuarioAggregateArgs<ExtArgs>
          result: runtime.Types.Utils.Optional<Prisma.AggregateUsuario>
        }
        groupBy: {
          args: Prisma.UsuarioGroupByArgs<ExtArgs>
          result: runtime.Types.Utils.Optional<Prisma.UsuarioGroupByOutputType>[]
        }
        count: {
          args: Prisma.UsuarioCountArgs<ExtArgs>
          result: runtime.Types.Utils.Optional<Prisma.UsuarioCountAggregateOutputType> | number
        }
      }
    }
    Role: {
      payload: Prisma.$RolePayload<ExtArgs>
      fields: Prisma.RoleFieldRefs
      operations: {
        findUnique: {
          args: Prisma.RoleFindUniqueArgs<ExtArgs>
          result: runtime.Types.Utils.PayloadToResult<Prisma.$RolePayload> | null
        }
        findUniqueOrThrow: {
          args: Prisma.RoleFindUniqueOrThrowArgs<ExtArgs>
          result: runtime.Types.Utils.PayloadToResult<Prisma.$RolePayload>
        }
        findFirst: {
          args: Prisma.RoleFindFirstArgs<ExtArgs>
          result: runtime.Types.Utils.PayloadToResult<Prisma.$RolePayload> | null
        }
        findFirstOrThrow: {
          args: Prisma.RoleFindFirstOrThrowArgs<ExtArgs>
          result: runtime.Types.Utils.PayloadToResult<Prisma.$RolePayload>
        }
        findMany: {
          args: Prisma.RoleFindManyArgs<ExtArgs>
          result: runtime.Types.Utils.PayloadToResult<Prisma.$RolePayload>[]
        }
        create: {
          args: Prisma.RoleCreateArgs<ExtArgs>
          result: runtime.Types.Utils.PayloadToResult<Prisma.$RolePayload>
        }
        createMany: {
          args: Prisma.RoleCreateManyArgs<ExtArgs>
          result: BatchPayload
        }
        delete: {
          args: Prisma.RoleDeleteArgs<ExtArgs>
          result: runtime.Types.Utils.PayloadToResult<Prisma.$RolePayload>
        }
        update: {
          args: Prisma.RoleUpdateArgs<ExtArgs>
          result: runtime.Types.Utils.PayloadToResult<Prisma.$RolePayload>
        }
        deleteMany: {
          args: Prisma.RoleDeleteManyArgs<ExtArgs>
          result: BatchPayload
        }
        updateMany: {
          args: Prisma.RoleUpdateManyArgs<ExtArgs>
          result: BatchPayload
        }
        upsert: {
          args: Prisma.RoleUpsertArgs<ExtArgs>
          result: runtime.Types.Utils.PayloadToResult<Prisma.$RolePayload>
        }
        aggregate: {
          args: Prisma.RoleAggregateArgs<ExtArgs>
          result: runtime.Types.Utils.Optional<Prisma.AggregateRole>
        }
        groupBy: {
          args: Prisma.RoleGroupByArgs<ExtArgs>
          result: runtime.Types.Utils.Optional<Prisma.RoleGroupByOutputType>[]
        }
        count: {
          args: Prisma.RoleCountArgs<ExtArgs>
          result: runtime.Types.Utils.Optional<Prisma.RoleCountAggregateOutputType> | number
        }
      }
    }
    UsuarioRole: {
      payload: Prisma.$UsuarioRolePayload<ExtArgs>
      fields: Prisma.UsuarioRoleFieldRefs
      operations: {
        findUnique: {
          args: Prisma.UsuarioRoleFindUniqueArgs<ExtArgs>
          result: runtime.Types.Utils.PayloadToResult<Prisma.$UsuarioRolePayload> | null
        }
        findUniqueOrThrow: {
          args: Prisma.UsuarioRoleFindUniqueOrThrowArgs<ExtArgs>
          result: runtime.Types.Utils.PayloadToResult<Prisma.$UsuarioRolePayload>
        }
        findFirst: {
          args: Prisma.UsuarioRoleFindFirstArgs<ExtArgs>
          result: runtime.Types.Utils.PayloadToResult<Prisma.$UsuarioRolePayload> | null
        }
        findFirstOrThrow: {
          args: Prisma.UsuarioRoleFindFirstOrThrowArgs<ExtArgs>
          result: runtime.Types.Utils.PayloadToResult<Prisma.$UsuarioRolePayload>
        }
        findMany: {
          args: Prisma.UsuarioRoleFindManyArgs<ExtArgs>
          result: runtime.Types.Utils.PayloadToResult<Prisma.$UsuarioRolePayload>[]
        }
        create: {
          args: Prisma.UsuarioRoleCreateArgs<ExtArgs>
          result: runtime.Types.Utils.PayloadToResult<Prisma.$UsuarioRolePayload>
        }
        createMany: {
          args: Prisma.UsuarioRoleCreateManyArgs<ExtArgs>
          result: BatchPayload
        }
        delete: {
          args: Prisma.UsuarioRoleDeleteArgs<ExtArgs>
          result: runtime.Types.Utils.PayloadToResult<Prisma.$UsuarioRolePayload>
        }
        update: {
          args: Prisma.UsuarioRoleUpdateArgs<ExtArgs>
          result: runtime.Types.Utils.PayloadToResult<Prisma.$UsuarioRolePayload>
        }
        deleteMany: {
          args: Prisma.UsuarioRoleDeleteManyArgs<ExtArgs>
          result: BatchPayload
        }
        updateMany: {
          args: Prisma.UsuarioRoleUpdateManyArgs<ExtArgs>
          result: BatchPayload
        }
        upsert: {
          args: Prisma.UsuarioRoleUpsertArgs<ExtArgs>
          result: runtime.Types.Utils.PayloadToResult<Prisma.$UsuarioRolePayload>
        }
        aggregate: {
          args: Prisma.UsuarioRoleAggregateArgs<ExtArgs>
          result: runtime.Types.Utils.Optional<Prisma.AggregateUsuarioRole>
        }
        groupBy: {
          args: Prisma.UsuarioRoleGroupByArgs<ExtArgs>
          result: runtime.Types.Utils.Optional<Prisma.UsuarioRoleGroupByOutputType>[]
        }
        count: {
          args: Prisma.UsuarioRoleCountArgs<ExtArgs>
          result: runtime.Types.Utils.Optional<Prisma.UsuarioRoleCountAggregateOutputType> | number
        }
      }
    }
    Cliente: {
      payload: Prisma.$ClientePayload<ExtArgs>
      fields: Prisma.ClienteFieldRefs
      operations: {
        findUnique: {
          args: Prisma.ClienteFindUniqueArgs<ExtArgs>
          result: runtime.Types.Utils.PayloadToResult<Prisma.$ClientePayload> | null
        }
        findUniqueOrThrow: {
          args: Prisma.ClienteFindUniqueOrThrowArgs<ExtArgs>
          result: runtime.Types.Utils.PayloadToResult<Prisma.$ClientePayload>
        }
        findFirst: {
          args: Prisma.ClienteFindFirstArgs<ExtArgs>
          result: runtime.Types.Utils.PayloadToResult<Prisma.$ClientePayload> | null
        }
        findFirstOrThrow: {
          args: Prisma.ClienteFindFirstOrThrowArgs<ExtArgs>
          result: runtime.Types.Utils.PayloadToResult<Prisma.$ClientePayload>
        }
        findMany: {
          args: Prisma.ClienteFindManyArgs<ExtArgs>
          result: runtime.Types.Utils.PayloadToResult<Prisma.$ClientePayload>[]
        }
        create: {
          args: Prisma.ClienteCreateArgs<ExtArgs>
          result: runtime.Types.Utils.PayloadToResult<Prisma.$ClientePayload>
        }
        createMany: {
          args: Prisma.ClienteCreateManyArgs<ExtArgs>
          result: BatchPayload
        }
        delete: {
          args: Prisma.ClienteDeleteArgs<ExtArgs>
          result: runtime.Types.Utils.PayloadToResult<Prisma.$ClientePayload>
        }
        update: {
          args: Prisma.ClienteUpdateArgs<ExtArgs>
          result: runtime.Types.Utils.PayloadToResult<Prisma.$ClientePayload>
        }
        deleteMany: {
          args: Prisma.ClienteDeleteManyArgs<ExtArgs>
          result: BatchPayload
        }
        updateMany: {
          args: Prisma.ClienteUpdateManyArgs<ExtArgs>
          result: BatchPayload
        }
        upsert: {
          args: Prisma.ClienteUpsertArgs<ExtArgs>
          result: runtime.Types.Utils.PayloadToResult<Prisma.$ClientePayload>
        }
        aggregate: {
          args: Prisma.ClienteAggregateArgs<ExtArgs>
          result: runtime.Types.Utils.Optional<Prisma.AggregateCliente>
        }
        groupBy: {
          args: Prisma.ClienteGroupByArgs<ExtArgs>
          result: runtime.Types.Utils.Optional<Prisma.ClienteGroupByOutputType>[]
        }
        count: {
          args: Prisma.ClienteCountArgs<ExtArgs>
          result: runtime.Types.Utils.Optional<Prisma.ClienteCountAggregateOutputType> | number
        }
      }
    }
    Especialidade: {
      payload: Prisma.$EspecialidadePayload<ExtArgs>
      fields: Prisma.EspecialidadeFieldRefs
      operations: {
        findUnique: {
          args: Prisma.EspecialidadeFindUniqueArgs<ExtArgs>
          result: runtime.Types.Utils.PayloadToResult<Prisma.$EspecialidadePayload> | null
        }
        findUniqueOrThrow: {
          args: Prisma.EspecialidadeFindUniqueOrThrowArgs<ExtArgs>
          result: runtime.Types.Utils.PayloadToResult<Prisma.$EspecialidadePayload>
        }
        findFirst: {
          args: Prisma.EspecialidadeFindFirstArgs<ExtArgs>
          result: runtime.Types.Utils.PayloadToResult<Prisma.$EspecialidadePayload> | null
        }
        findFirstOrThrow: {
          args: Prisma.EspecialidadeFindFirstOrThrowArgs<ExtArgs>
          result: runtime.Types.Utils.PayloadToResult<Prisma.$EspecialidadePayload>
        }
        findMany: {
          args: Prisma.EspecialidadeFindManyArgs<ExtArgs>
          result: runtime.Types.Utils.PayloadToResult<Prisma.$EspecialidadePayload>[]
        }
        create: {
          args: Prisma.EspecialidadeCreateArgs<ExtArgs>
          result: runtime.Types.Utils.PayloadToResult<Prisma.$EspecialidadePayload>
        }
        createMany: {
          args: Prisma.EspecialidadeCreateManyArgs<ExtArgs>
          result: BatchPayload
        }
        delete: {
          args: Prisma.EspecialidadeDeleteArgs<ExtArgs>
          result: runtime.Types.Utils.PayloadToResult<Prisma.$EspecialidadePayload>
        }
        update: {
          args: Prisma.EspecialidadeUpdateArgs<ExtArgs>
          result: runtime.Types.Utils.PayloadToResult<Prisma.$EspecialidadePayload>
        }
        deleteMany: {
          args: Prisma.EspecialidadeDeleteManyArgs<ExtArgs>
          result: BatchPayload
        }
        updateMany: {
          args: Prisma.EspecialidadeUpdateManyArgs<ExtArgs>
          result: BatchPayload
        }
        upsert: {
          args: Prisma.EspecialidadeUpsertArgs<ExtArgs>
          result: runtime.Types.Utils.PayloadToResult<Prisma.$EspecialidadePayload>
        }
        aggregate: {
          args: Prisma.EspecialidadeAggregateArgs<ExtArgs>
          result: runtime.Types.Utils.Optional<Prisma.AggregateEspecialidade>
        }
        groupBy: {
          args: Prisma.EspecialidadeGroupByArgs<ExtArgs>
          result: runtime.Types.Utils.Optional<Prisma.EspecialidadeGroupByOutputType>[]
        }
        count: {
          args: Prisma.EspecialidadeCountArgs<ExtArgs>
          result: runtime.Types.Utils.Optional<Prisma.EspecialidadeCountAggregateOutputType> | number
        }
      }
    }
    Profissional: {
      payload: Prisma.$ProfissionalPayload<ExtArgs>
      fields: Prisma.ProfissionalFieldRefs
      operations: {
        findUnique: {
          args: Prisma.ProfissionalFindUniqueArgs<ExtArgs>
          result: runtime.Types.Utils.PayloadToResult<Prisma.$ProfissionalPayload> | null
        }
        findUniqueOrThrow: {
          args: Prisma.ProfissionalFindUniqueOrThrowArgs<ExtArgs>
          result: runtime.Types.Utils.PayloadToResult<Prisma.$ProfissionalPayload>
        }
        findFirst: {
          args: Prisma.ProfissionalFindFirstArgs<ExtArgs>
          result: runtime.Types.Utils.PayloadToResult<Prisma.$ProfissionalPayload> | null
        }
        findFirstOrThrow: {
          args: Prisma.ProfissionalFindFirstOrThrowArgs<ExtArgs>
          result: runtime.Types.Utils.PayloadToResult<Prisma.$ProfissionalPayload>
        }
        findMany: {
          args: Prisma.ProfissionalFindManyArgs<ExtArgs>
          result: runtime.Types.Utils.PayloadToResult<Prisma.$ProfissionalPayload>[]
        }
        create: {
          args: Prisma.ProfissionalCreateArgs<ExtArgs>
          result: runtime.Types.Utils.PayloadToResult<Prisma.$ProfissionalPayload>
        }
        createMany: {
          args: Prisma.ProfissionalCreateManyArgs<ExtArgs>
          result: BatchPayload
        }
        delete: {
          args: Prisma.ProfissionalDeleteArgs<ExtArgs>
          result: runtime.Types.Utils.PayloadToResult<Prisma.$ProfissionalPayload>
        }
        update: {
          args: Prisma.ProfissionalUpdateArgs<ExtArgs>
          result: runtime.Types.Utils.PayloadToResult<Prisma.$ProfissionalPayload>
        }
        deleteMany: {
          args: Prisma.ProfissionalDeleteManyArgs<ExtArgs>
          result: BatchPayload
        }
        updateMany: {
          args: Prisma.ProfissionalUpdateManyArgs<ExtArgs>
          result: BatchPayload
        }
        upsert: {
          args: Prisma.ProfissionalUpsertArgs<ExtArgs>
          result: runtime.Types.Utils.PayloadToResult<Prisma.$ProfissionalPayload>
        }
        aggregate: {
          args: Prisma.ProfissionalAggregateArgs<ExtArgs>
          result: runtime.Types.Utils.Optional<Prisma.AggregateProfissional>
        }
        groupBy: {
          args: Prisma.ProfissionalGroupByArgs<ExtArgs>
          result: runtime.Types.Utils.Optional<Prisma.ProfissionalGroupByOutputType>[]
        }
        count: {
          args: Prisma.ProfissionalCountArgs<ExtArgs>
          result: runtime.Types.Utils.Optional<Prisma.ProfissionalCountAggregateOutputType> | number
        }
      }
    }
    ProfissionalEspecialidade: {
      payload: Prisma.$ProfissionalEspecialidadePayload<ExtArgs>
      fields: Prisma.ProfissionalEspecialidadeFieldRefs
      operations: {
        findUnique: {
          args: Prisma.ProfissionalEspecialidadeFindUniqueArgs<ExtArgs>
          result: runtime.Types.Utils.PayloadToResult<Prisma.$ProfissionalEspecialidadePayload> | null
        }
        findUniqueOrThrow: {
          args: Prisma.ProfissionalEspecialidadeFindUniqueOrThrowArgs<ExtArgs>
          result: runtime.Types.Utils.PayloadToResult<Prisma.$ProfissionalEspecialidadePayload>
        }
        findFirst: {
          args: Prisma.ProfissionalEspecialidadeFindFirstArgs<ExtArgs>
          result: runtime.Types.Utils.PayloadToResult<Prisma.$ProfissionalEspecialidadePayload> | null
        }
        findFirstOrThrow: {
          args: Prisma.ProfissionalEspecialidadeFindFirstOrThrowArgs<ExtArgs>
          result: runtime.Types.Utils.PayloadToResult<Prisma.$ProfissionalEspecialidadePayload>
        }
        findMany: {
          args: Prisma.ProfissionalEspecialidadeFindManyArgs<ExtArgs>
          result: runtime.Types.Utils.PayloadToResult<Prisma.$ProfissionalEspecialidadePayload>[]
        }
        create: {
          args: Prisma.ProfissionalEspecialidadeCreateArgs<ExtArgs>
          result: runtime.Types.Utils.PayloadToResult<Prisma.$ProfissionalEspecialidadePayload>
        }
        createMany: {
          args: Prisma.ProfissionalEspecialidadeCreateManyArgs<ExtArgs>
          result: BatchPayload
        }
        delete: {
          args: Prisma.ProfissionalEspecialidadeDeleteArgs<ExtArgs>
          result: runtime.Types.Utils.PayloadToResult<Prisma.$ProfissionalEspecialidadePayload>
        }
        update: {
          args: Prisma.ProfissionalEspecialidadeUpdateArgs<ExtArgs>
          result: runtime.Types.Utils.PayloadToResult<Prisma.$ProfissionalEspecialidadePayload>
        }
        deleteMany: {
          args: Prisma.ProfissionalEspecialidadeDeleteManyArgs<ExtArgs>
          result: BatchPayload
        }
        updateMany: {
          args: Prisma.ProfissionalEspecialidadeUpdateManyArgs<ExtArgs>
          result: BatchPayload
        }
        upsert: {
          args: Prisma.ProfissionalEspecialidadeUpsertArgs<ExtArgs>
          result: runtime.Types.Utils.PayloadToResult<Prisma.$ProfissionalEspecialidadePayload>
        }
        aggregate: {
          args: Prisma.ProfissionalEspecialidadeAggregateArgs<ExtArgs>
          result: runtime.Types.Utils.Optional<Prisma.AggregateProfissionalEspecialidade>
        }
        groupBy: {
          args: Prisma.ProfissionalEspecialidadeGroupByArgs<ExtArgs>
          result: runtime.Types.Utils.Optional<Prisma.ProfissionalEspecialidadeGroupByOutputType>[]
        }
        count: {
          args: Prisma.ProfissionalEspecialidadeCountArgs<ExtArgs>
          result: runtime.Types.Utils.Optional<Prisma.ProfissionalEspecialidadeCountAggregateOutputType> | number
        }
      }
    }
    UsuarioCliente: {
      payload: Prisma.$UsuarioClientePayload<ExtArgs>
      fields: Prisma.UsuarioClienteFieldRefs
      operations: {
        findUnique: {
          args: Prisma.UsuarioClienteFindUniqueArgs<ExtArgs>
          result: runtime.Types.Utils.PayloadToResult<Prisma.$UsuarioClientePayload> | null
        }
        findUniqueOrThrow: {
          args: Prisma.UsuarioClienteFindUniqueOrThrowArgs<ExtArgs>
          result: runtime.Types.Utils.PayloadToResult<Prisma.$UsuarioClientePayload>
        }
        findFirst: {
          args: Prisma.UsuarioClienteFindFirstArgs<ExtArgs>
          result: runtime.Types.Utils.PayloadToResult<Prisma.$UsuarioClientePayload> | null
        }
        findFirstOrThrow: {
          args: Prisma.UsuarioClienteFindFirstOrThrowArgs<ExtArgs>
          result: runtime.Types.Utils.PayloadToResult<Prisma.$UsuarioClientePayload>
        }
        findMany: {
          args: Prisma.UsuarioClienteFindManyArgs<ExtArgs>
          result: runtime.Types.Utils.PayloadToResult<Prisma.$UsuarioClientePayload>[]
        }
        create: {
          args: Prisma.UsuarioClienteCreateArgs<ExtArgs>
          result: runtime.Types.Utils.PayloadToResult<Prisma.$UsuarioClientePayload>
        }
        createMany: {
          args: Prisma.UsuarioClienteCreateManyArgs<ExtArgs>
          result: BatchPayload
        }
        delete: {
          args: Prisma.UsuarioClienteDeleteArgs<ExtArgs>
          result: runtime.Types.Utils.PayloadToResult<Prisma.$UsuarioClientePayload>
        }
        update: {
          args: Prisma.UsuarioClienteUpdateArgs<ExtArgs>
          result: runtime.Types.Utils.PayloadToResult<Prisma.$UsuarioClientePayload>
        }
        deleteMany: {
          args: Prisma.UsuarioClienteDeleteManyArgs<ExtArgs>
          result: BatchPayload
        }
        updateMany: {
          args: Prisma.UsuarioClienteUpdateManyArgs<ExtArgs>
          result: BatchPayload
        }
        upsert: {
          args: Prisma.UsuarioClienteUpsertArgs<ExtArgs>
          result: runtime.Types.Utils.PayloadToResult<Prisma.$UsuarioClientePayload>
        }
        aggregate: {
          args: Prisma.UsuarioClienteAggregateArgs<ExtArgs>
          result: runtime.Types.Utils.Optional<Prisma.AggregateUsuarioCliente>
        }
        groupBy: {
          args: Prisma.UsuarioClienteGroupByArgs<ExtArgs>
          result: runtime.Types.Utils.Optional<Prisma.UsuarioClienteGroupByOutputType>[]
        }
        count: {
          args: Prisma.UsuarioClienteCountArgs<ExtArgs>
          result: runtime.Types.Utils.Optional<Prisma.UsuarioClienteCountAggregateOutputType> | number
        }
      }
    }
    LocalAtendimento: {
      payload: Prisma.$LocalAtendimentoPayload<ExtArgs>
      fields: Prisma.LocalAtendimentoFieldRefs
      operations: {
        findUnique: {
          args: Prisma.LocalAtendimentoFindUniqueArgs<ExtArgs>
          result: runtime.Types.Utils.PayloadToResult<Prisma.$LocalAtendimentoPayload> | null
        }
        findUniqueOrThrow: {
          args: Prisma.LocalAtendimentoFindUniqueOrThrowArgs<ExtArgs>
          result: runtime.Types.Utils.PayloadToResult<Prisma.$LocalAtendimentoPayload>
        }
        findFirst: {
          args: Prisma.LocalAtendimentoFindFirstArgs<ExtArgs>
          result: runtime.Types.Utils.PayloadToResult<Prisma.$LocalAtendimentoPayload> | null
        }
        findFirstOrThrow: {
          args: Prisma.LocalAtendimentoFindFirstOrThrowArgs<ExtArgs>
          result: runtime.Types.Utils.PayloadToResult<Prisma.$LocalAtendimentoPayload>
        }
        findMany: {
          args: Prisma.LocalAtendimentoFindManyArgs<ExtArgs>
          result: runtime.Types.Utils.PayloadToResult<Prisma.$LocalAtendimentoPayload>[]
        }
        create: {
          args: Prisma.LocalAtendimentoCreateArgs<ExtArgs>
          result: runtime.Types.Utils.PayloadToResult<Prisma.$LocalAtendimentoPayload>
        }
        createMany: {
          args: Prisma.LocalAtendimentoCreateManyArgs<ExtArgs>
          result: BatchPayload
        }
        delete: {
          args: Prisma.LocalAtendimentoDeleteArgs<ExtArgs>
          result: runtime.Types.Utils.PayloadToResult<Prisma.$LocalAtendimentoPayload>
        }
        update: {
          args: Prisma.LocalAtendimentoUpdateArgs<ExtArgs>
          result: runtime.Types.Utils.PayloadToResult<Prisma.$LocalAtendimentoPayload>
        }
        deleteMany: {
          args: Prisma.LocalAtendimentoDeleteManyArgs<ExtArgs>
          result: BatchPayload
        }
        updateMany: {
          args: Prisma.LocalAtendimentoUpdateManyArgs<ExtArgs>
          result: BatchPayload
        }
        upsert: {
          args: Prisma.LocalAtendimentoUpsertArgs<ExtArgs>
          result: runtime.Types.Utils.PayloadToResult<Prisma.$LocalAtendimentoPayload>
        }
        aggregate: {
          args: Prisma.LocalAtendimentoAggregateArgs<ExtArgs>
          result: runtime.Types.Utils.Optional<Prisma.AggregateLocalAtendimento>
        }
        groupBy: {
          args: Prisma.LocalAtendimentoGroupByArgs<ExtArgs>
          result: runtime.Types.Utils.Optional<Prisma.LocalAtendimentoGroupByOutputType>[]
        }
        count: {
          args: Prisma.LocalAtendimentoCountArgs<ExtArgs>
          result: runtime.Types.Utils.Optional<Prisma.LocalAtendimentoCountAggregateOutputType> | number
        }
      }
    }
    LocalAtendimentoEspecialidades: {
      payload: Prisma.$LocalAtendimentoEspecialidadesPayload<ExtArgs>
      fields: Prisma.LocalAtendimentoEspecialidadesFieldRefs
      operations: {
        findUnique: {
          args: Prisma.LocalAtendimentoEspecialidadesFindUniqueArgs<ExtArgs>
          result: runtime.Types.Utils.PayloadToResult<Prisma.$LocalAtendimentoEspecialidadesPayload> | null
        }
        findUniqueOrThrow: {
          args: Prisma.LocalAtendimentoEspecialidadesFindUniqueOrThrowArgs<ExtArgs>
          result: runtime.Types.Utils.PayloadToResult<Prisma.$LocalAtendimentoEspecialidadesPayload>
        }
        findFirst: {
          args: Prisma.LocalAtendimentoEspecialidadesFindFirstArgs<ExtArgs>
          result: runtime.Types.Utils.PayloadToResult<Prisma.$LocalAtendimentoEspecialidadesPayload> | null
        }
        findFirstOrThrow: {
          args: Prisma.LocalAtendimentoEspecialidadesFindFirstOrThrowArgs<ExtArgs>
          result: runtime.Types.Utils.PayloadToResult<Prisma.$LocalAtendimentoEspecialidadesPayload>
        }
        findMany: {
          args: Prisma.LocalAtendimentoEspecialidadesFindManyArgs<ExtArgs>
          result: runtime.Types.Utils.PayloadToResult<Prisma.$LocalAtendimentoEspecialidadesPayload>[]
        }
        create: {
          args: Prisma.LocalAtendimentoEspecialidadesCreateArgs<ExtArgs>
          result: runtime.Types.Utils.PayloadToResult<Prisma.$LocalAtendimentoEspecialidadesPayload>
        }
        createMany: {
          args: Prisma.LocalAtendimentoEspecialidadesCreateManyArgs<ExtArgs>
          result: BatchPayload
        }
        delete: {
          args: Prisma.LocalAtendimentoEspecialidadesDeleteArgs<ExtArgs>
          result: runtime.Types.Utils.PayloadToResult<Prisma.$LocalAtendimentoEspecialidadesPayload>
        }
        update: {
          args: Prisma.LocalAtendimentoEspecialidadesUpdateArgs<ExtArgs>
          result: runtime.Types.Utils.PayloadToResult<Prisma.$LocalAtendimentoEspecialidadesPayload>
        }
        deleteMany: {
          args: Prisma.LocalAtendimentoEspecialidadesDeleteManyArgs<ExtArgs>
          result: BatchPayload
        }
        updateMany: {
          args: Prisma.LocalAtendimentoEspecialidadesUpdateManyArgs<ExtArgs>
          result: BatchPayload
        }
        upsert: {
          args: Prisma.LocalAtendimentoEspecialidadesUpsertArgs<ExtArgs>
          result: runtime.Types.Utils.PayloadToResult<Prisma.$LocalAtendimentoEspecialidadesPayload>
        }
        aggregate: {
          args: Prisma.LocalAtendimentoEspecialidadesAggregateArgs<ExtArgs>
          result: runtime.Types.Utils.Optional<Prisma.AggregateLocalAtendimentoEspecialidades>
        }
        groupBy: {
          args: Prisma.LocalAtendimentoEspecialidadesGroupByArgs<ExtArgs>
          result: runtime.Types.Utils.Optional<Prisma.LocalAtendimentoEspecialidadesGroupByOutputType>[]
        }
        count: {
          args: Prisma.LocalAtendimentoEspecialidadesCountArgs<ExtArgs>
          result: runtime.Types.Utils.Optional<Prisma.LocalAtendimentoEspecialidadesCountAggregateOutputType> | number
        }
      }
    }
    Plantao: {
      payload: Prisma.$PlantaoPayload<ExtArgs>
      fields: Prisma.PlantaoFieldRefs
      operations: {
        findUnique: {
          args: Prisma.PlantaoFindUniqueArgs<ExtArgs>
          result: runtime.Types.Utils.PayloadToResult<Prisma.$PlantaoPayload> | null
        }
        findUniqueOrThrow: {
          args: Prisma.PlantaoFindUniqueOrThrowArgs<ExtArgs>
          result: runtime.Types.Utils.PayloadToResult<Prisma.$PlantaoPayload>
        }
        findFirst: {
          args: Prisma.PlantaoFindFirstArgs<ExtArgs>
          result: runtime.Types.Utils.PayloadToResult<Prisma.$PlantaoPayload> | null
        }
        findFirstOrThrow: {
          args: Prisma.PlantaoFindFirstOrThrowArgs<ExtArgs>
          result: runtime.Types.Utils.PayloadToResult<Prisma.$PlantaoPayload>
        }
        findMany: {
          args: Prisma.PlantaoFindManyArgs<ExtArgs>
          result: runtime.Types.Utils.PayloadToResult<Prisma.$PlantaoPayload>[]
        }
        create: {
          args: Prisma.PlantaoCreateArgs<ExtArgs>
          result: runtime.Types.Utils.PayloadToResult<Prisma.$PlantaoPayload>
        }
        createMany: {
          args: Prisma.PlantaoCreateManyArgs<ExtArgs>
          result: BatchPayload
        }
        delete: {
          args: Prisma.PlantaoDeleteArgs<ExtArgs>
          result: runtime.Types.Utils.PayloadToResult<Prisma.$PlantaoPayload>
        }
        update: {
          args: Prisma.PlantaoUpdateArgs<ExtArgs>
          result: runtime.Types.Utils.PayloadToResult<Prisma.$PlantaoPayload>
        }
        deleteMany: {
          args: Prisma.PlantaoDeleteManyArgs<ExtArgs>
          result: BatchPayload
        }
        updateMany: {
          args: Prisma.PlantaoUpdateManyArgs<ExtArgs>
          result: BatchPayload
        }
        upsert: {
          args: Prisma.PlantaoUpsertArgs<ExtArgs>
          result: runtime.Types.Utils.PayloadToResult<Prisma.$PlantaoPayload>
        }
        aggregate: {
          args: Prisma.PlantaoAggregateArgs<ExtArgs>
          result: runtime.Types.Utils.Optional<Prisma.AggregatePlantao>
        }
        groupBy: {
          args: Prisma.PlantaoGroupByArgs<ExtArgs>
          result: runtime.Types.Utils.Optional<Prisma.PlantaoGroupByOutputType>[]
        }
        count: {
          args: Prisma.PlantaoCountArgs<ExtArgs>
          result: runtime.Types.Utils.Optional<Prisma.PlantaoCountAggregateOutputType> | number
        }
      }
    }
    DiaPlantao: {
      payload: Prisma.$DiaPlantaoPayload<ExtArgs>
      fields: Prisma.DiaPlantaoFieldRefs
      operations: {
        findUnique: {
          args: Prisma.DiaPlantaoFindUniqueArgs<ExtArgs>
          result: runtime.Types.Utils.PayloadToResult<Prisma.$DiaPlantaoPayload> | null
        }
        findUniqueOrThrow: {
          args: Prisma.DiaPlantaoFindUniqueOrThrowArgs<ExtArgs>
          result: runtime.Types.Utils.PayloadToResult<Prisma.$DiaPlantaoPayload>
        }
        findFirst: {
          args: Prisma.DiaPlantaoFindFirstArgs<ExtArgs>
          result: runtime.Types.Utils.PayloadToResult<Prisma.$DiaPlantaoPayload> | null
        }
        findFirstOrThrow: {
          args: Prisma.DiaPlantaoFindFirstOrThrowArgs<ExtArgs>
          result: runtime.Types.Utils.PayloadToResult<Prisma.$DiaPlantaoPayload>
        }
        findMany: {
          args: Prisma.DiaPlantaoFindManyArgs<ExtArgs>
          result: runtime.Types.Utils.PayloadToResult<Prisma.$DiaPlantaoPayload>[]
        }
        create: {
          args: Prisma.DiaPlantaoCreateArgs<ExtArgs>
          result: runtime.Types.Utils.PayloadToResult<Prisma.$DiaPlantaoPayload>
        }
        createMany: {
          args: Prisma.DiaPlantaoCreateManyArgs<ExtArgs>
          result: BatchPayload
        }
        delete: {
          args: Prisma.DiaPlantaoDeleteArgs<ExtArgs>
          result: runtime.Types.Utils.PayloadToResult<Prisma.$DiaPlantaoPayload>
        }
        update: {
          args: Prisma.DiaPlantaoUpdateArgs<ExtArgs>
          result: runtime.Types.Utils.PayloadToResult<Prisma.$DiaPlantaoPayload>
        }
        deleteMany: {
          args: Prisma.DiaPlantaoDeleteManyArgs<ExtArgs>
          result: BatchPayload
        }
        updateMany: {
          args: Prisma.DiaPlantaoUpdateManyArgs<ExtArgs>
          result: BatchPayload
        }
        upsert: {
          args: Prisma.DiaPlantaoUpsertArgs<ExtArgs>
          result: runtime.Types.Utils.PayloadToResult<Prisma.$DiaPlantaoPayload>
        }
        aggregate: {
          args: Prisma.DiaPlantaoAggregateArgs<ExtArgs>
          result: runtime.Types.Utils.Optional<Prisma.AggregateDiaPlantao>
        }
        groupBy: {
          args: Prisma.DiaPlantaoGroupByArgs<ExtArgs>
          result: runtime.Types.Utils.Optional<Prisma.DiaPlantaoGroupByOutputType>[]
        }
        count: {
          args: Prisma.DiaPlantaoCountArgs<ExtArgs>
          result: runtime.Types.Utils.Optional<Prisma.DiaPlantaoCountAggregateOutputType> | number
        }
      }
    }
    PresencaDiaPlantao: {
      payload: Prisma.$PresencaDiaPlantaoPayload<ExtArgs>
      fields: Prisma.PresencaDiaPlantaoFieldRefs
      operations: {
        findUnique: {
          args: Prisma.PresencaDiaPlantaoFindUniqueArgs<ExtArgs>
          result: runtime.Types.Utils.PayloadToResult<Prisma.$PresencaDiaPlantaoPayload> | null
        }
        findUniqueOrThrow: {
          args: Prisma.PresencaDiaPlantaoFindUniqueOrThrowArgs<ExtArgs>
          result: runtime.Types.Utils.PayloadToResult<Prisma.$PresencaDiaPlantaoPayload>
        }
        findFirst: {
          args: Prisma.PresencaDiaPlantaoFindFirstArgs<ExtArgs>
          result: runtime.Types.Utils.PayloadToResult<Prisma.$PresencaDiaPlantaoPayload> | null
        }
        findFirstOrThrow: {
          args: Prisma.PresencaDiaPlantaoFindFirstOrThrowArgs<ExtArgs>
          result: runtime.Types.Utils.PayloadToResult<Prisma.$PresencaDiaPlantaoPayload>
        }
        findMany: {
          args: Prisma.PresencaDiaPlantaoFindManyArgs<ExtArgs>
          result: runtime.Types.Utils.PayloadToResult<Prisma.$PresencaDiaPlantaoPayload>[]
        }
        create: {
          args: Prisma.PresencaDiaPlantaoCreateArgs<ExtArgs>
          result: runtime.Types.Utils.PayloadToResult<Prisma.$PresencaDiaPlantaoPayload>
        }
        createMany: {
          args: Prisma.PresencaDiaPlantaoCreateManyArgs<ExtArgs>
          result: BatchPayload
        }
        delete: {
          args: Prisma.PresencaDiaPlantaoDeleteArgs<ExtArgs>
          result: runtime.Types.Utils.PayloadToResult<Prisma.$PresencaDiaPlantaoPayload>
        }
        update: {
          args: Prisma.PresencaDiaPlantaoUpdateArgs<ExtArgs>
          result: runtime.Types.Utils.PayloadToResult<Prisma.$PresencaDiaPlantaoPayload>
        }
        deleteMany: {
          args: Prisma.PresencaDiaPlantaoDeleteManyArgs<ExtArgs>
          result: BatchPayload
        }
        updateMany: {
          args: Prisma.PresencaDiaPlantaoUpdateManyArgs<ExtArgs>
          result: BatchPayload
        }
        upsert: {
          args: Prisma.PresencaDiaPlantaoUpsertArgs<ExtArgs>
          result: runtime.Types.Utils.PayloadToResult<Prisma.$PresencaDiaPlantaoPayload>
        }
        aggregate: {
          args: Prisma.PresencaDiaPlantaoAggregateArgs<ExtArgs>
          result: runtime.Types.Utils.Optional<Prisma.AggregatePresencaDiaPlantao>
        }
        groupBy: {
          args: Prisma.PresencaDiaPlantaoGroupByArgs<ExtArgs>
          result: runtime.Types.Utils.Optional<Prisma.PresencaDiaPlantaoGroupByOutputType>[]
        }
        count: {
          args: Prisma.PresencaDiaPlantaoCountArgs<ExtArgs>
          result: runtime.Types.Utils.Optional<Prisma.PresencaDiaPlantaoCountAggregateOutputType> | number
        }
      }
    }
    Fechamento: {
      payload: Prisma.$FechamentoPayload<ExtArgs>
      fields: Prisma.FechamentoFieldRefs
      operations: {
        findUnique: {
          args: Prisma.FechamentoFindUniqueArgs<ExtArgs>
          result: runtime.Types.Utils.PayloadToResult<Prisma.$FechamentoPayload> | null
        }
        findUniqueOrThrow: {
          args: Prisma.FechamentoFindUniqueOrThrowArgs<ExtArgs>
          result: runtime.Types.Utils.PayloadToResult<Prisma.$FechamentoPayload>
        }
        findFirst: {
          args: Prisma.FechamentoFindFirstArgs<ExtArgs>
          result: runtime.Types.Utils.PayloadToResult<Prisma.$FechamentoPayload> | null
        }
        findFirstOrThrow: {
          args: Prisma.FechamentoFindFirstOrThrowArgs<ExtArgs>
          result: runtime.Types.Utils.PayloadToResult<Prisma.$FechamentoPayload>
        }
        findMany: {
          args: Prisma.FechamentoFindManyArgs<ExtArgs>
          result: runtime.Types.Utils.PayloadToResult<Prisma.$FechamentoPayload>[]
        }
        create: {
          args: Prisma.FechamentoCreateArgs<ExtArgs>
          result: runtime.Types.Utils.PayloadToResult<Prisma.$FechamentoPayload>
        }
        createMany: {
          args: Prisma.FechamentoCreateManyArgs<ExtArgs>
          result: BatchPayload
        }
        delete: {
          args: Prisma.FechamentoDeleteArgs<ExtArgs>
          result: runtime.Types.Utils.PayloadToResult<Prisma.$FechamentoPayload>
        }
        update: {
          args: Prisma.FechamentoUpdateArgs<ExtArgs>
          result: runtime.Types.Utils.PayloadToResult<Prisma.$FechamentoPayload>
        }
        deleteMany: {
          args: Prisma.FechamentoDeleteManyArgs<ExtArgs>
          result: BatchPayload
        }
        updateMany: {
          args: Prisma.FechamentoUpdateManyArgs<ExtArgs>
          result: BatchPayload
        }
        upsert: {
          args: Prisma.FechamentoUpsertArgs<ExtArgs>
          result: runtime.Types.Utils.PayloadToResult<Prisma.$FechamentoPayload>
        }
        aggregate: {
          args: Prisma.FechamentoAggregateArgs<ExtArgs>
          result: runtime.Types.Utils.Optional<Prisma.AggregateFechamento>
        }
        groupBy: {
          args: Prisma.FechamentoGroupByArgs<ExtArgs>
          result: runtime.Types.Utils.Optional<Prisma.FechamentoGroupByOutputType>[]
        }
        count: {
          args: Prisma.FechamentoCountArgs<ExtArgs>
          result: runtime.Types.Utils.Optional<Prisma.FechamentoCountAggregateOutputType> | number
        }
      }
    }
    Antecipacao: {
      payload: Prisma.$AntecipacaoPayload<ExtArgs>
      fields: Prisma.AntecipacaoFieldRefs
      operations: {
        findUnique: {
          args: Prisma.AntecipacaoFindUniqueArgs<ExtArgs>
          result: runtime.Types.Utils.PayloadToResult<Prisma.$AntecipacaoPayload> | null
        }
        findUniqueOrThrow: {
          args: Prisma.AntecipacaoFindUniqueOrThrowArgs<ExtArgs>
          result: runtime.Types.Utils.PayloadToResult<Prisma.$AntecipacaoPayload>
        }
        findFirst: {
          args: Prisma.AntecipacaoFindFirstArgs<ExtArgs>
          result: runtime.Types.Utils.PayloadToResult<Prisma.$AntecipacaoPayload> | null
        }
        findFirstOrThrow: {
          args: Prisma.AntecipacaoFindFirstOrThrowArgs<ExtArgs>
          result: runtime.Types.Utils.PayloadToResult<Prisma.$AntecipacaoPayload>
        }
        findMany: {
          args: Prisma.AntecipacaoFindManyArgs<ExtArgs>
          result: runtime.Types.Utils.PayloadToResult<Prisma.$AntecipacaoPayload>[]
        }
        create: {
          args: Prisma.AntecipacaoCreateArgs<ExtArgs>
          result: runtime.Types.Utils.PayloadToResult<Prisma.$AntecipacaoPayload>
        }
        createMany: {
          args: Prisma.AntecipacaoCreateManyArgs<ExtArgs>
          result: BatchPayload
        }
        delete: {
          args: Prisma.AntecipacaoDeleteArgs<ExtArgs>
          result: runtime.Types.Utils.PayloadToResult<Prisma.$AntecipacaoPayload>
        }
        update: {
          args: Prisma.AntecipacaoUpdateArgs<ExtArgs>
          result: runtime.Types.Utils.PayloadToResult<Prisma.$AntecipacaoPayload>
        }
        deleteMany: {
          args: Prisma.AntecipacaoDeleteManyArgs<ExtArgs>
          result: BatchPayload
        }
        updateMany: {
          args: Prisma.AntecipacaoUpdateManyArgs<ExtArgs>
          result: BatchPayload
        }
        upsert: {
          args: Prisma.AntecipacaoUpsertArgs<ExtArgs>
          result: runtime.Types.Utils.PayloadToResult<Prisma.$AntecipacaoPayload>
        }
        aggregate: {
          args: Prisma.AntecipacaoAggregateArgs<ExtArgs>
          result: runtime.Types.Utils.Optional<Prisma.AggregateAntecipacao>
        }
        groupBy: {
          args: Prisma.AntecipacaoGroupByArgs<ExtArgs>
          result: runtime.Types.Utils.Optional<Prisma.AntecipacaoGroupByOutputType>[]
        }
        count: {
          args: Prisma.AntecipacaoCountArgs<ExtArgs>
          result: runtime.Types.Utils.Optional<Prisma.AntecipacaoCountAggregateOutputType> | number
        }
      }
    }
    AntecipacaoHistorico: {
      payload: Prisma.$AntecipacaoHistoricoPayload<ExtArgs>
      fields: Prisma.AntecipacaoHistoricoFieldRefs
      operations: {
        findUnique: {
          args: Prisma.AntecipacaoHistoricoFindUniqueArgs<ExtArgs>
          result: runtime.Types.Utils.PayloadToResult<Prisma.$AntecipacaoHistoricoPayload> | null
        }
        findUniqueOrThrow: {
          args: Prisma.AntecipacaoHistoricoFindUniqueOrThrowArgs<ExtArgs>
          result: runtime.Types.Utils.PayloadToResult<Prisma.$AntecipacaoHistoricoPayload>
        }
        findFirst: {
          args: Prisma.AntecipacaoHistoricoFindFirstArgs<ExtArgs>
          result: runtime.Types.Utils.PayloadToResult<Prisma.$AntecipacaoHistoricoPayload> | null
        }
        findFirstOrThrow: {
          args: Prisma.AntecipacaoHistoricoFindFirstOrThrowArgs<ExtArgs>
          result: runtime.Types.Utils.PayloadToResult<Prisma.$AntecipacaoHistoricoPayload>
        }
        findMany: {
          args: Prisma.AntecipacaoHistoricoFindManyArgs<ExtArgs>
          result: runtime.Types.Utils.PayloadToResult<Prisma.$AntecipacaoHistoricoPayload>[]
        }
        create: {
          args: Prisma.AntecipacaoHistoricoCreateArgs<ExtArgs>
          result: runtime.Types.Utils.PayloadToResult<Prisma.$AntecipacaoHistoricoPayload>
        }
        createMany: {
          args: Prisma.AntecipacaoHistoricoCreateManyArgs<ExtArgs>
          result: BatchPayload
        }
        delete: {
          args: Prisma.AntecipacaoHistoricoDeleteArgs<ExtArgs>
          result: runtime.Types.Utils.PayloadToResult<Prisma.$AntecipacaoHistoricoPayload>
        }
        update: {
          args: Prisma.AntecipacaoHistoricoUpdateArgs<ExtArgs>
          result: runtime.Types.Utils.PayloadToResult<Prisma.$AntecipacaoHistoricoPayload>
        }
        deleteMany: {
          args: Prisma.AntecipacaoHistoricoDeleteManyArgs<ExtArgs>
          result: BatchPayload
        }
        updateMany: {
          args: Prisma.AntecipacaoHistoricoUpdateManyArgs<ExtArgs>
          result: BatchPayload
        }
        upsert: {
          args: Prisma.AntecipacaoHistoricoUpsertArgs<ExtArgs>
          result: runtime.Types.Utils.PayloadToResult<Prisma.$AntecipacaoHistoricoPayload>
        }
        aggregate: {
          args: Prisma.AntecipacaoHistoricoAggregateArgs<ExtArgs>
          result: runtime.Types.Utils.Optional<Prisma.AggregateAntecipacaoHistorico>
        }
        groupBy: {
          args: Prisma.AntecipacaoHistoricoGroupByArgs<ExtArgs>
          result: runtime.Types.Utils.Optional<Prisma.AntecipacaoHistoricoGroupByOutputType>[]
        }
        count: {
          args: Prisma.AntecipacaoHistoricoCountArgs<ExtArgs>
          result: runtime.Types.Utils.Optional<Prisma.AntecipacaoHistoricoCountAggregateOutputType> | number
        }
      }
    }
    AuditLog: {
      payload: Prisma.$AuditLogPayload<ExtArgs>
      fields: Prisma.AuditLogFieldRefs
      operations: {
        findUnique: {
          args: Prisma.AuditLogFindUniqueArgs<ExtArgs>
          result: runtime.Types.Utils.PayloadToResult<Prisma.$AuditLogPayload> | null
        }
        findUniqueOrThrow: {
          args: Prisma.AuditLogFindUniqueOrThrowArgs<ExtArgs>
          result: runtime.Types.Utils.PayloadToResult<Prisma.$AuditLogPayload>
        }
        findFirst: {
          args: Prisma.AuditLogFindFirstArgs<ExtArgs>
          result: runtime.Types.Utils.PayloadToResult<Prisma.$AuditLogPayload> | null
        }
        findFirstOrThrow: {
          args: Prisma.AuditLogFindFirstOrThrowArgs<ExtArgs>
          result: runtime.Types.Utils.PayloadToResult<Prisma.$AuditLogPayload>
        }
        findMany: {
          args: Prisma.AuditLogFindManyArgs<ExtArgs>
          result: runtime.Types.Utils.PayloadToResult<Prisma.$AuditLogPayload>[]
        }
        create: {
          args: Prisma.AuditLogCreateArgs<ExtArgs>
          result: runtime.Types.Utils.PayloadToResult<Prisma.$AuditLogPayload>
        }
        createMany: {
          args: Prisma.AuditLogCreateManyArgs<ExtArgs>
          result: BatchPayload
        }
        delete: {
          args: Prisma.AuditLogDeleteArgs<ExtArgs>
          result: runtime.Types.Utils.PayloadToResult<Prisma.$AuditLogPayload>
        }
        update: {
          args: Prisma.AuditLogUpdateArgs<ExtArgs>
          result: runtime.Types.Utils.PayloadToResult<Prisma.$AuditLogPayload>
        }
        deleteMany: {
          args: Prisma.AuditLogDeleteManyArgs<ExtArgs>
          result: BatchPayload
        }
        updateMany: {
          args: Prisma.AuditLogUpdateManyArgs<ExtArgs>
          result: BatchPayload
        }
        upsert: {
          args: Prisma.AuditLogUpsertArgs<ExtArgs>
          result: runtime.Types.Utils.PayloadToResult<Prisma.$AuditLogPayload>
        }
        aggregate: {
          args: Prisma.AuditLogAggregateArgs<ExtArgs>
          result: runtime.Types.Utils.Optional<Prisma.AggregateAuditLog>
        }
        groupBy: {
          args: Prisma.AuditLogGroupByArgs<ExtArgs>
          result: runtime.Types.Utils.Optional<Prisma.AuditLogGroupByOutputType>[]
        }
        count: {
          args: Prisma.AuditLogCountArgs<ExtArgs>
          result: runtime.Types.Utils.Optional<Prisma.AuditLogCountAggregateOutputType> | number
        }
      }
    }
    GerenciamentoTermosLgpd: {
      payload: Prisma.$GerenciamentoTermosLgpdPayload<ExtArgs>
      fields: Prisma.GerenciamentoTermosLgpdFieldRefs
      operations: {
        findUnique: {
          args: Prisma.GerenciamentoTermosLgpdFindUniqueArgs<ExtArgs>
          result: runtime.Types.Utils.PayloadToResult<Prisma.$GerenciamentoTermosLgpdPayload> | null
        }
        findUniqueOrThrow: {
          args: Prisma.GerenciamentoTermosLgpdFindUniqueOrThrowArgs<ExtArgs>
          result: runtime.Types.Utils.PayloadToResult<Prisma.$GerenciamentoTermosLgpdPayload>
        }
        findFirst: {
          args: Prisma.GerenciamentoTermosLgpdFindFirstArgs<ExtArgs>
          result: runtime.Types.Utils.PayloadToResult<Prisma.$GerenciamentoTermosLgpdPayload> | null
        }
        findFirstOrThrow: {
          args: Prisma.GerenciamentoTermosLgpdFindFirstOrThrowArgs<ExtArgs>
          result: runtime.Types.Utils.PayloadToResult<Prisma.$GerenciamentoTermosLgpdPayload>
        }
        findMany: {
          args: Prisma.GerenciamentoTermosLgpdFindManyArgs<ExtArgs>
          result: runtime.Types.Utils.PayloadToResult<Prisma.$GerenciamentoTermosLgpdPayload>[]
        }
        create: {
          args: Prisma.GerenciamentoTermosLgpdCreateArgs<ExtArgs>
          result: runtime.Types.Utils.PayloadToResult<Prisma.$GerenciamentoTermosLgpdPayload>
        }
        createMany: {
          args: Prisma.GerenciamentoTermosLgpdCreateManyArgs<ExtArgs>
          result: BatchPayload
        }
        delete: {
          args: Prisma.GerenciamentoTermosLgpdDeleteArgs<ExtArgs>
          result: runtime.Types.Utils.PayloadToResult<Prisma.$GerenciamentoTermosLgpdPayload>
        }
        update: {
          args: Prisma.GerenciamentoTermosLgpdUpdateArgs<ExtArgs>
          result: runtime.Types.Utils.PayloadToResult<Prisma.$GerenciamentoTermosLgpdPayload>
        }
        deleteMany: {
          args: Prisma.GerenciamentoTermosLgpdDeleteManyArgs<ExtArgs>
          result: BatchPayload
        }
        updateMany: {
          args: Prisma.GerenciamentoTermosLgpdUpdateManyArgs<ExtArgs>
          result: BatchPayload
        }
        upsert: {
          args: Prisma.GerenciamentoTermosLgpdUpsertArgs<ExtArgs>
          result: runtime.Types.Utils.PayloadToResult<Prisma.$GerenciamentoTermosLgpdPayload>
        }
        aggregate: {
          args: Prisma.GerenciamentoTermosLgpdAggregateArgs<ExtArgs>
          result: runtime.Types.Utils.Optional<Prisma.AggregateGerenciamentoTermosLgpd>
        }
        groupBy: {
          args: Prisma.GerenciamentoTermosLgpdGroupByArgs<ExtArgs>
          result: runtime.Types.Utils.Optional<Prisma.GerenciamentoTermosLgpdGroupByOutputType>[]
        }
        count: {
          args: Prisma.GerenciamentoTermosLgpdCountArgs<ExtArgs>
          result: runtime.Types.Utils.Optional<Prisma.GerenciamentoTermosLgpdCountAggregateOutputType> | number
        }
      }
    }
    AceiteTermosLgpd: {
      payload: Prisma.$AceiteTermosLgpdPayload<ExtArgs>
      fields: Prisma.AceiteTermosLgpdFieldRefs
      operations: {
        findUnique: {
          args: Prisma.AceiteTermosLgpdFindUniqueArgs<ExtArgs>
          result: runtime.Types.Utils.PayloadToResult<Prisma.$AceiteTermosLgpdPayload> | null
        }
        findUniqueOrThrow: {
          args: Prisma.AceiteTermosLgpdFindUniqueOrThrowArgs<ExtArgs>
          result: runtime.Types.Utils.PayloadToResult<Prisma.$AceiteTermosLgpdPayload>
        }
        findFirst: {
          args: Prisma.AceiteTermosLgpdFindFirstArgs<ExtArgs>
          result: runtime.Types.Utils.PayloadToResult<Prisma.$AceiteTermosLgpdPayload> | null
        }
        findFirstOrThrow: {
          args: Prisma.AceiteTermosLgpdFindFirstOrThrowArgs<ExtArgs>
          result: runtime.Types.Utils.PayloadToResult<Prisma.$AceiteTermosLgpdPayload>
        }
        findMany: {
          args: Prisma.AceiteTermosLgpdFindManyArgs<ExtArgs>
          result: runtime.Types.Utils.PayloadToResult<Prisma.$AceiteTermosLgpdPayload>[]
        }
        create: {
          args: Prisma.AceiteTermosLgpdCreateArgs<ExtArgs>
          result: runtime.Types.Utils.PayloadToResult<Prisma.$AceiteTermosLgpdPayload>
        }
        createMany: {
          args: Prisma.AceiteTermosLgpdCreateManyArgs<ExtArgs>
          result: BatchPayload
        }
        delete: {
          args: Prisma.AceiteTermosLgpdDeleteArgs<ExtArgs>
          result: runtime.Types.Utils.PayloadToResult<Prisma.$AceiteTermosLgpdPayload>
        }
        update: {
          args: Prisma.AceiteTermosLgpdUpdateArgs<ExtArgs>
          result: runtime.Types.Utils.PayloadToResult<Prisma.$AceiteTermosLgpdPayload>
        }
        deleteMany: {
          args: Prisma.AceiteTermosLgpdDeleteManyArgs<ExtArgs>
          result: BatchPayload
        }
        updateMany: {
          args: Prisma.AceiteTermosLgpdUpdateManyArgs<ExtArgs>
          result: BatchPayload
        }
        upsert: {
          args: Prisma.AceiteTermosLgpdUpsertArgs<ExtArgs>
          result: runtime.Types.Utils.PayloadToResult<Prisma.$AceiteTermosLgpdPayload>
        }
        aggregate: {
          args: Prisma.AceiteTermosLgpdAggregateArgs<ExtArgs>
          result: runtime.Types.Utils.Optional<Prisma.AggregateAceiteTermosLgpd>
        }
        groupBy: {
          args: Prisma.AceiteTermosLgpdGroupByArgs<ExtArgs>
          result: runtime.Types.Utils.Optional<Prisma.AceiteTermosLgpdGroupByOutputType>[]
        }
        count: {
          args: Prisma.AceiteTermosLgpdCountArgs<ExtArgs>
          result: runtime.Types.Utils.Optional<Prisma.AceiteTermosLgpdCountAggregateOutputType> | number
        }
      }
    }
  }
} & {
  other: {
    payload: any
    operations: {
      $executeRaw: {
        args: [query: TemplateStringsArray | Sql, ...values: any[]],
        result: any
      }
      $executeRawUnsafe: {
        args: [query: string, ...values: any[]],
        result: any
      }
      $queryRaw: {
        args: [query: TemplateStringsArray | Sql, ...values: any[]],
        result: any
      }
      $queryRawUnsafe: {
        args: [query: string, ...values: any[]],
        result: any
      }
    }
  }
}

/**
 * Enums
 */

export const TransactionIsolationLevel = runtime.makeStrictEnum({
  ReadUncommitted: 'ReadUncommitted',
  ReadCommitted: 'ReadCommitted',
  RepeatableRead: 'RepeatableRead',
  Serializable: 'Serializable'
} as const)

export type TransactionIsolationLevel = (typeof TransactionIsolationLevel)[keyof typeof TransactionIsolationLevel]


export const UsuarioScalarFieldEnum = {
  id: 'id',
  uuid: 'uuid',
  email: 'email',
  senha: 'senha',
  nome: 'nome',
  cpf: 'cpf',
  telefone: 'telefone',
  dataNascimento: 'dataNascimento',
  genero: 'genero',
  estadoCivil: 'estadoCivil',
  nacionalidade: 'nacionalidade',
  cep: 'cep',
  logradouro: 'logradouro',
  numero: 'numero',
  complemento: 'complemento',
  bairro: 'bairro',
  cidade: 'cidade',
  uf: 'uf',
  ativo: 'ativo',
  emailVerificado: 'emailVerificado',
  emailVerificadoEm: 'emailVerificadoEm',
  deveResetarSenha: 'deveResetarSenha',
  tokenRecuperacao: 'tokenRecuperacao',
  tokenRecuperacaoExpira: 'tokenRecuperacaoExpira',
  ultimoAcesso: 'ultimoAcesso',
  metaData: 'metaData',
  createdAt: 'createdAt',
  updatedAt: 'updatedAt',
  deletedAt: 'deletedAt'
} as const

export type UsuarioScalarFieldEnum = (typeof UsuarioScalarFieldEnum)[keyof typeof UsuarioScalarFieldEnum]


export const RoleScalarFieldEnum = {
  id: 'id',
  nome: 'nome',
  descricao: 'descricao',
  ativo: 'ativo',
  createdAt: 'createdAt',
  updatedAt: 'updatedAt'
} as const

export type RoleScalarFieldEnum = (typeof RoleScalarFieldEnum)[keyof typeof RoleScalarFieldEnum]


export const UsuarioRoleScalarFieldEnum = {
  id: 'id',
  usuarioId: 'usuarioId',
  roleId: 'roleId',
  createdAt: 'createdAt'
} as const

export type UsuarioRoleScalarFieldEnum = (typeof UsuarioRoleScalarFieldEnum)[keyof typeof UsuarioRoleScalarFieldEnum]


export const ClienteScalarFieldEnum = {
  id: 'id',
  uuid: 'uuid',
  nome: 'nome',
  cnpj: 'cnpj',
  email: 'email',
  telefone: 'telefone',
  taxaPadrao: 'taxaPadrao',
  cep: 'cep',
  logradouro: 'logradouro',
  numero: 'numero',
  complemento: 'complemento',
  bairro: 'bairro',
  cidade: 'cidade',
  uf: 'uf',
  fusoHorario: 'fusoHorario',
  ativo: 'ativo',
  createdAt: 'createdAt',
  updatedAt: 'updatedAt',
  deletedAt: 'deletedAt'
} as const

export type ClienteScalarFieldEnum = (typeof ClienteScalarFieldEnum)[keyof typeof ClienteScalarFieldEnum]


export const EspecialidadeScalarFieldEnum = {
  id: 'id',
  nome: 'nome',
  descricao: 'descricao',
  createdAt: 'createdAt',
  updatedAt: 'updatedAt'
} as const

export type EspecialidadeScalarFieldEnum = (typeof EspecialidadeScalarFieldEnum)[keyof typeof EspecialidadeScalarFieldEnum]


export const ProfissionalScalarFieldEnum = {
  id: 'id',
  uuid: 'uuid',
  usuarioId: 'usuarioId',
  rg: 'rg',
  orgaoEmissor: 'orgaoEmissor',
  cnes: 'cnes',
  conselhoClasse: 'conselhoClasse',
  numeroRegistro: 'numeroRegistro',
  ufConselho: 'ufConselho',
  banco: 'banco',
  agencia: 'agencia',
  digitoAgencia: 'digitoAgencia',
  conta: 'conta',
  digitoConta: 'digitoConta',
  tipoConta: 'tipoConta',
  chavePix: 'chavePix',
  tipoPix: 'tipoPix',
  tipoAssinatura: 'tipoAssinatura',
  metaData: 'metaData',
  createdAt: 'createdAt',
  updatedAt: 'updatedAt',
  deletedAt: 'deletedAt'
} as const

export type ProfissionalScalarFieldEnum = (typeof ProfissionalScalarFieldEnum)[keyof typeof ProfissionalScalarFieldEnum]


export const ProfissionalEspecialidadeScalarFieldEnum = {
  id: 'id',
  profissionalId: 'profissionalId',
  especialidadeId: 'especialidadeId',
  createdAt: 'createdAt'
} as const

export type ProfissionalEspecialidadeScalarFieldEnum = (typeof ProfissionalEspecialidadeScalarFieldEnum)[keyof typeof ProfissionalEspecialidadeScalarFieldEnum]


export const UsuarioClienteScalarFieldEnum = {
  id: 'id',
  usuarioId: 'usuarioId',
  clienteId: 'clienteId',
  ativo: 'ativo',
  createdAt: 'createdAt',
  updatedAt: 'updatedAt'
} as const

export type UsuarioClienteScalarFieldEnum = (typeof UsuarioClienteScalarFieldEnum)[keyof typeof UsuarioClienteScalarFieldEnum]


export const LocalAtendimentoScalarFieldEnum = {
  id: 'id',
  uuid: 'uuid',
  clienteId: 'clienteId',
  nome: 'nome',
  endereco: 'endereco',
  cidade: 'cidade',
  estado: 'estado',
  cep: 'cep',
  telefone: 'telefone',
  responsavel: 'responsavel',
  observacoes: 'observacoes',
  latitude: 'latitude',
  longitude: 'longitude',
  ativo: 'ativo',
  fusoHorario: 'fusoHorario',
  createdAt: 'createdAt',
  updatedAt: 'updatedAt',
  deletedAt: 'deletedAt'
} as const

export type LocalAtendimentoScalarFieldEnum = (typeof LocalAtendimentoScalarFieldEnum)[keyof typeof LocalAtendimentoScalarFieldEnum]


export const LocalAtendimentoEspecialidadesScalarFieldEnum = {
  id: 'id',
  localAtendimentoId: 'localAtendimentoId',
  especialidadeId: 'especialidadeId',
  createdAt: 'createdAt'
} as const

export type LocalAtendimentoEspecialidadesScalarFieldEnum = (typeof LocalAtendimentoEspecialidadesScalarFieldEnum)[keyof typeof LocalAtendimentoEspecialidadesScalarFieldEnum]


export const PlantaoScalarFieldEnum = {
  id: 'id',
  uuid: 'uuid',
  clienteId: 'clienteId',
  localAtendimentoId: 'localAtendimentoId',
  profissionalId: 'profissionalId',
  dataInicial: 'dataInicial',
  dataFinal: 'dataFinal',
  prazoPagamentoDias: 'prazoPagamentoDias',
  modalidadeTrabalho: 'modalidadeTrabalho',
  tipoFechamento: 'tipoFechamento',
  tipoValor: 'tipoValor',
  valorBase: 'valorBase',
  horaInicio: 'horaInicio',
  horaFim: 'horaFim',
  intervalo: 'intervalo',
  tipoTurno: 'tipoTurno',
  observacoes: 'observacoes',
  fusoHorario: 'fusoHorario',
  createdAt: 'createdAt',
  updatedAt: 'updatedAt',
  concluidoEm: 'concluidoEm',
  deletedAt: 'deletedAt'
} as const

export type PlantaoScalarFieldEnum = (typeof PlantaoScalarFieldEnum)[keyof typeof PlantaoScalarFieldEnum]


export const DiaPlantaoScalarFieldEnum = {
  id: 'id',
  plantaoId: 'plantaoId',
  data: 'data',
  horaEntrada: 'horaEntrada',
  horaSaida: 'horaSaida',
  intervalo: 'intervalo',
  observacoes: 'observacoes',
  createdAt: 'createdAt',
  updatedAt: 'updatedAt'
} as const

export type DiaPlantaoScalarFieldEnum = (typeof DiaPlantaoScalarFieldEnum)[keyof typeof DiaPlantaoScalarFieldEnum]


export const PresencaDiaPlantaoScalarFieldEnum = {
  id: 'id',
  diaPlantaoId: 'diaPlantaoId',
  fechamentoId: 'fechamentoId',
  horaEntrada: 'horaEntrada',
  horaSaida: 'horaSaida',
  intervalo: 'intervalo',
  horasTrabalhadas: 'horasTrabalhadas',
  valorEstimado: 'valorEstimado',
  status: 'status',
  historicoAprovacoes: 'historicoAprovacoes',
  tempoGlosado: 'tempoGlosado',
  justificativaGlosa: 'justificativaGlosa',
  observacao: 'observacao',
  metadata: 'metadata',
  createdAt: 'createdAt',
  updatedAt: 'updatedAt'
} as const

export type PresencaDiaPlantaoScalarFieldEnum = (typeof PresencaDiaPlantaoScalarFieldEnum)[keyof typeof PresencaDiaPlantaoScalarFieldEnum]


export const FechamentoScalarFieldEnum = {
  id: 'id',
  uuid: 'uuid',
  plantaoId: 'plantaoId',
  profissionalId: 'profissionalId',
  antecipacaoId: 'antecipacaoId',
  status: 'status',
  totalHoras: 'totalHoras',
  totalValor: 'totalValor',
  diasTrabalhados: 'diasTrabalhados',
  diasPrevistos: 'diasPrevistos',
  aprovadoPor: 'aprovadoPor',
  aprovadoEm: 'aprovadoEm',
  rejeitadoPor: 'rejeitadoPor',
  rejeitadoEm: 'rejeitadoEm',
  motivoRejeicao: 'motivoRejeicao',
  observacoes: 'observacoes',
  createdAt: 'createdAt',
  updatedAt: 'updatedAt',
  deletedAt: 'deletedAt'
} as const

export type FechamentoScalarFieldEnum = (typeof FechamentoScalarFieldEnum)[keyof typeof FechamentoScalarFieldEnum]


export const AntecipacaoScalarFieldEnum = {
  id: 'id',
  uuid: 'uuid',
  plantaoId: 'plantaoId',
  profissionalId: 'profissionalId',
  valorSolicitado: 'valorSolicitado',
  valorAprovado: 'valorAprovado',
  percentual: 'percentual',
  taxaAntecipacao: 'taxaAntecipacao',
  dataPagamentoPrevista: 'dataPagamentoPrevista',
  status: 'status',
  numeroCCB: 'numeroCCB',
  dataCCB: 'dataCCB',
  arquivoCCB: 'arquivoCCB',
  termoAssinado: 'termoAssinado',
  dataAssinaturaTermo: 'dataAssinaturaTermo',
  hashAssinatura: 'hashAssinatura',
  ipAssinatura: 'ipAssinatura',
  observacoes: 'observacoes',
  metaData: 'metaData',
  createdAt: 'createdAt',
  updatedAt: 'updatedAt',
  deletedAt: 'deletedAt'
} as const

export type AntecipacaoScalarFieldEnum = (typeof AntecipacaoScalarFieldEnum)[keyof typeof AntecipacaoScalarFieldEnum]


export const AntecipacaoHistoricoScalarFieldEnum = {
  id: 'id',
  antecipacaoId: 'antecipacaoId',
  status: 'status',
  metaData: 'metaData',
  createdAt: 'createdAt'
} as const

export type AntecipacaoHistoricoScalarFieldEnum = (typeof AntecipacaoHistoricoScalarFieldEnum)[keyof typeof AntecipacaoHistoricoScalarFieldEnum]


export const AuditLogScalarFieldEnum = {
  id: 'id',
  operation: 'operation',
  tableName: 'tableName',
  recordId: 'recordId',
  oldData: 'oldData',
  newData: 'newData',
  changes: 'changes',
  userId: 'userId',
  userName: 'userName',
  userEmail: 'userEmail',
  userRole: 'userRole',
  ipAddress: 'ipAddress',
  userAgent: 'userAgent',
  endpoint: 'endpoint',
  method: 'method',
  description: 'description',
  metadata: 'metadata',
  createdAt: 'createdAt'
} as const

export type AuditLogScalarFieldEnum = (typeof AuditLogScalarFieldEnum)[keyof typeof AuditLogScalarFieldEnum]


export const GerenciamentoTermosLgpdScalarFieldEnum = {
  id: 'id',
  versao: 'versao',
  titulo: 'titulo',
  conteudo: 'conteudo',
  hashConteudo: 'hashConteudo',
  ativo: 'ativo',
  criadoPor: 'criadoPor',
  criadoEm: 'criadoEm',
  atualizadoEm: 'atualizadoEm',
  validoDe: 'validoDe',
  validoAte: 'validoAte',
  tipoDocumento: 'tipoDocumento',
  deletedAt: 'deletedAt'
} as const

export type GerenciamentoTermosLgpdScalarFieldEnum = (typeof GerenciamentoTermosLgpdScalarFieldEnum)[keyof typeof GerenciamentoTermosLgpdScalarFieldEnum]


export const AceiteTermosLgpdScalarFieldEnum = {
  id: 'id',
  usuarioId: 'usuarioId',
  termoVersaoId: 'termoVersaoId',
  consentimentoLgpd: 'consentimentoLgpd',
  aceitoEm: 'aceitoEm',
  enderecoIp: 'enderecoIp',
  latitude: 'latitude',
  longitude: 'longitude',
  userAgent: 'userAgent',
  dadosAdicionais: 'dadosAdicionais',
  termoVersao: 'termoVersao'
} as const

export type AceiteTermosLgpdScalarFieldEnum = (typeof AceiteTermosLgpdScalarFieldEnum)[keyof typeof AceiteTermosLgpdScalarFieldEnum]


export const SortOrder = {
  asc: 'asc',
  desc: 'desc'
} as const

export type SortOrder = (typeof SortOrder)[keyof typeof SortOrder]


export const NullableJsonNullValueInput = {
  DbNull: DbNull,
  JsonNull: JsonNull
} as const

export type NullableJsonNullValueInput = (typeof NullableJsonNullValueInput)[keyof typeof NullableJsonNullValueInput]


export const JsonNullValueFilter = {
  DbNull: DbNull,
  JsonNull: JsonNull,
  AnyNull: AnyNull
} as const

export type JsonNullValueFilter = (typeof JsonNullValueFilter)[keyof typeof JsonNullValueFilter]


export const QueryMode = {
  default: 'default',
  insensitive: 'insensitive'
} as const

export type QueryMode = (typeof QueryMode)[keyof typeof QueryMode]


export const NullsOrder = {
  first: 'first',
  last: 'last'
} as const

export type NullsOrder = (typeof NullsOrder)[keyof typeof NullsOrder]


export const UsuarioOrderByRelevanceFieldEnum = {
  uuid: 'uuid',
  email: 'email',
  senha: 'senha',
  nome: 'nome',
  cpf: 'cpf',
  telefone: 'telefone',
  genero: 'genero',
  estadoCivil: 'estadoCivil',
  nacionalidade: 'nacionalidade',
  cep: 'cep',
  logradouro: 'logradouro',
  numero: 'numero',
  complemento: 'complemento',
  bairro: 'bairro',
  cidade: 'cidade',
  uf: 'uf',
  tokenRecuperacao: 'tokenRecuperacao'
} as const

export type UsuarioOrderByRelevanceFieldEnum = (typeof UsuarioOrderByRelevanceFieldEnum)[keyof typeof UsuarioOrderByRelevanceFieldEnum]


export const RoleOrderByRelevanceFieldEnum = {
  nome: 'nome',
  descricao: 'descricao'
} as const

export type RoleOrderByRelevanceFieldEnum = (typeof RoleOrderByRelevanceFieldEnum)[keyof typeof RoleOrderByRelevanceFieldEnum]


export const ClienteOrderByRelevanceFieldEnum = {
  uuid: 'uuid',
  nome: 'nome',
  cnpj: 'cnpj',
  email: 'email',
  telefone: 'telefone',
  cep: 'cep',
  logradouro: 'logradouro',
  numero: 'numero',
  complemento: 'complemento',
  bairro: 'bairro',
  cidade: 'cidade',
  uf: 'uf',
  fusoHorario: 'fusoHorario'
} as const

export type ClienteOrderByRelevanceFieldEnum = (typeof ClienteOrderByRelevanceFieldEnum)[keyof typeof ClienteOrderByRelevanceFieldEnum]


export const EspecialidadeOrderByRelevanceFieldEnum = {
  nome: 'nome',
  descricao: 'descricao'
} as const

export type EspecialidadeOrderByRelevanceFieldEnum = (typeof EspecialidadeOrderByRelevanceFieldEnum)[keyof typeof EspecialidadeOrderByRelevanceFieldEnum]


export const ProfissionalOrderByRelevanceFieldEnum = {
  uuid: 'uuid',
  rg: 'rg',
  orgaoEmissor: 'orgaoEmissor',
  cnes: 'cnes',
  conselhoClasse: 'conselhoClasse',
  numeroRegistro: 'numeroRegistro',
  ufConselho: 'ufConselho',
  banco: 'banco',
  agencia: 'agencia',
  digitoAgencia: 'digitoAgencia',
  conta: 'conta',
  digitoConta: 'digitoConta',
  tipoConta: 'tipoConta',
  chavePix: 'chavePix',
  tipoPix: 'tipoPix',
  tipoAssinatura: 'tipoAssinatura'
} as const

export type ProfissionalOrderByRelevanceFieldEnum = (typeof ProfissionalOrderByRelevanceFieldEnum)[keyof typeof ProfissionalOrderByRelevanceFieldEnum]


export const LocalAtendimentoOrderByRelevanceFieldEnum = {
  uuid: 'uuid',
  nome: 'nome',
  endereco: 'endereco',
  cidade: 'cidade',
  estado: 'estado',
  cep: 'cep',
  telefone: 'telefone',
  responsavel: 'responsavel',
  observacoes: 'observacoes',
  fusoHorario: 'fusoHorario'
} as const

export type LocalAtendimentoOrderByRelevanceFieldEnum = (typeof LocalAtendimentoOrderByRelevanceFieldEnum)[keyof typeof LocalAtendimentoOrderByRelevanceFieldEnum]


export const PlantaoOrderByRelevanceFieldEnum = {
  uuid: 'uuid',
  modalidadeTrabalho: 'modalidadeTrabalho',
  tipoFechamento: 'tipoFechamento',
  tipoValor: 'tipoValor',
  horaInicio: 'horaInicio',
  horaFim: 'horaFim',
  intervalo: 'intervalo',
  tipoTurno: 'tipoTurno',
  observacoes: 'observacoes',
  fusoHorario: 'fusoHorario'
} as const

export type PlantaoOrderByRelevanceFieldEnum = (typeof PlantaoOrderByRelevanceFieldEnum)[keyof typeof PlantaoOrderByRelevanceFieldEnum]


export const DiaPlantaoOrderByRelevanceFieldEnum = {
  horaEntrada: 'horaEntrada',
  horaSaida: 'horaSaida',
  intervalo: 'intervalo',
  observacoes: 'observacoes'
} as const

export type DiaPlantaoOrderByRelevanceFieldEnum = (typeof DiaPlantaoOrderByRelevanceFieldEnum)[keyof typeof DiaPlantaoOrderByRelevanceFieldEnum]


export const PresencaDiaPlantaoOrderByRelevanceFieldEnum = {
  intervalo: 'intervalo',
  status: 'status',
  justificativaGlosa: 'justificativaGlosa',
  observacao: 'observacao'
} as const

export type PresencaDiaPlantaoOrderByRelevanceFieldEnum = (typeof PresencaDiaPlantaoOrderByRelevanceFieldEnum)[keyof typeof PresencaDiaPlantaoOrderByRelevanceFieldEnum]


export const FechamentoOrderByRelevanceFieldEnum = {
  uuid: 'uuid',
  status: 'status',
  aprovadoPor: 'aprovadoPor',
  rejeitadoPor: 'rejeitadoPor',
  motivoRejeicao: 'motivoRejeicao',
  observacoes: 'observacoes'
} as const

export type FechamentoOrderByRelevanceFieldEnum = (typeof FechamentoOrderByRelevanceFieldEnum)[keyof typeof FechamentoOrderByRelevanceFieldEnum]


export const AntecipacaoOrderByRelevanceFieldEnum = {
  uuid: 'uuid',
  status: 'status',
  numeroCCB: 'numeroCCB',
  arquivoCCB: 'arquivoCCB',
  hashAssinatura: 'hashAssinatura',
  ipAssinatura: 'ipAssinatura',
  observacoes: 'observacoes'
} as const

export type AntecipacaoOrderByRelevanceFieldEnum = (typeof AntecipacaoOrderByRelevanceFieldEnum)[keyof typeof AntecipacaoOrderByRelevanceFieldEnum]


export const AntecipacaoHistoricoOrderByRelevanceFieldEnum = {
  status: 'status'
} as const

export type AntecipacaoHistoricoOrderByRelevanceFieldEnum = (typeof AntecipacaoHistoricoOrderByRelevanceFieldEnum)[keyof typeof AntecipacaoHistoricoOrderByRelevanceFieldEnum]


export const AuditLogOrderByRelevanceFieldEnum = {
  operation: 'operation',
  tableName: 'tableName',
  recordId: 'recordId',
  userName: 'userName',
  userEmail: 'userEmail',
  userRole: 'userRole',
  ipAddress: 'ipAddress',
  userAgent: 'userAgent',
  endpoint: 'endpoint',
  method: 'method',
  description: 'description'
} as const

export type AuditLogOrderByRelevanceFieldEnum = (typeof AuditLogOrderByRelevanceFieldEnum)[keyof typeof AuditLogOrderByRelevanceFieldEnum]


export const GerenciamentoTermosLgpdOrderByRelevanceFieldEnum = {
  versao: 'versao',
  titulo: 'titulo',
  conteudo: 'conteudo',
  hashConteudo: 'hashConteudo',
  tipoDocumento: 'tipoDocumento'
} as const

export type GerenciamentoTermosLgpdOrderByRelevanceFieldEnum = (typeof GerenciamentoTermosLgpdOrderByRelevanceFieldEnum)[keyof typeof GerenciamentoTermosLgpdOrderByRelevanceFieldEnum]


export const AceiteTermosLgpdOrderByRelevanceFieldEnum = {
  enderecoIp: 'enderecoIp',
  userAgent: 'userAgent',
  termoVersao: 'termoVersao'
} as const

export type AceiteTermosLgpdOrderByRelevanceFieldEnum = (typeof AceiteTermosLgpdOrderByRelevanceFieldEnum)[keyof typeof AceiteTermosLgpdOrderByRelevanceFieldEnum]



/**
 * Field references
 */


/**
 * Reference to a field of type 'Int'
 */
export type IntFieldRefInput<$PrismaModel> = FieldRefInputType<$PrismaModel, 'Int'>
    


/**
 * Reference to a field of type 'String'
 */
export type StringFieldRefInput<$PrismaModel> = FieldRefInputType<$PrismaModel, 'String'>
    


/**
 * Reference to a field of type 'DateTime'
 */
export type DateTimeFieldRefInput<$PrismaModel> = FieldRefInputType<$PrismaModel, 'DateTime'>
    


/**
 * Reference to a field of type 'Boolean'
 */
export type BooleanFieldRefInput<$PrismaModel> = FieldRefInputType<$PrismaModel, 'Boolean'>
    


/**
 * Reference to a field of type 'Json'
 */
export type JsonFieldRefInput<$PrismaModel> = FieldRefInputType<$PrismaModel, 'Json'>
    


/**
 * Reference to a field of type 'QueryMode'
 */
export type EnumQueryModeFieldRefInput<$PrismaModel> = FieldRefInputType<$PrismaModel, 'QueryMode'>
    


/**
 * Reference to a field of type 'Float'
 */
export type FloatFieldRefInput<$PrismaModel> = FieldRefInputType<$PrismaModel, 'Float'>
    


/**
 * Reference to a field of type 'Decimal'
 */
export type DecimalFieldRefInput<$PrismaModel> = FieldRefInputType<$PrismaModel, 'Decimal'>
    

/**
 * Batch Payload for updateMany & deleteMany & createMany
 */
export type BatchPayload = {
  count: number
}


export type Datasource = {
  url?: string
}
export type Datasources = {
  db?: Datasource
}

export const defineExtension = runtime.Extensions.defineExtension as unknown as runtime.Types.Extensions.ExtendsHook<"define", TypeMapCb, runtime.Types.Extensions.DefaultArgs>
export type DefaultPrismaClient = PrismaClient
export type ErrorFormat = 'pretty' | 'colorless' | 'minimal'
export interface PrismaClientOptions {
  /**
   * Overwrites the datasource url from your schema.prisma file
   */
  datasources?: Datasources
  /**
   * Overwrites the datasource url from your schema.prisma file
   */
  datasourceUrl?: string
  /**
   * @default "colorless"
   */
  errorFormat?: ErrorFormat
  /**
   * @example
   * ```
   * // Shorthand for `emit: 'stdout'`
   * log: ['query', 'info', 'warn', 'error']
   * 
   * // Emit as events only
   * log: [
   *   { emit: 'event', level: 'query' },
   *   { emit: 'event', level: 'info' },
   *   { emit: 'event', level: 'warn' }
   *   { emit: 'event', level: 'error' }
   * ]
   * 
   * / Emit as events and log to stdout
   * og: [
   *  { emit: 'stdout', level: 'query' },
   *  { emit: 'stdout', level: 'info' },
   *  { emit: 'stdout', level: 'warn' }
   *  { emit: 'stdout', level: 'error' }
   * 
   * ```
   * Read more in our [docs](https://www.prisma.io/docs/reference/tools-and-interfaces/prisma-client/logging#the-log-option).
   */
  log?: (LogLevel | LogDefinition)[]
  /**
   * The default values for transactionOptions
   * maxWait ?= 2000
   * timeout ?= 5000
   */
  transactionOptions?: {
    maxWait?: number
    timeout?: number
    isolationLevel?: TransactionIsolationLevel
  }
  /**
   * Global configuration for omitting model fields by default.
   * 
   * @example
   * ```
   * const prisma = new PrismaClient({
   *   omit: {
   *     user: {
   *       password: true
   *     }
   *   }
   * })
   * ```
   */
  omit?: GlobalOmitConfig
}
export type GlobalOmitConfig = {
  usuario?: Prisma.UsuarioOmit
  role?: Prisma.RoleOmit
  usuarioRole?: Prisma.UsuarioRoleOmit
  cliente?: Prisma.ClienteOmit
  especialidade?: Prisma.EspecialidadeOmit
  profissional?: Prisma.ProfissionalOmit
  profissionalEspecialidade?: Prisma.ProfissionalEspecialidadeOmit
  usuarioCliente?: Prisma.UsuarioClienteOmit
  localAtendimento?: Prisma.LocalAtendimentoOmit
  localAtendimentoEspecialidades?: Prisma.LocalAtendimentoEspecialidadesOmit
  plantao?: Prisma.PlantaoOmit
  diaPlantao?: Prisma.DiaPlantaoOmit
  presencaDiaPlantao?: Prisma.PresencaDiaPlantaoOmit
  fechamento?: Prisma.FechamentoOmit
  antecipacao?: Prisma.AntecipacaoOmit
  antecipacaoHistorico?: Prisma.AntecipacaoHistoricoOmit
  auditLog?: Prisma.AuditLogOmit
  gerenciamentoTermosLgpd?: Prisma.GerenciamentoTermosLgpdOmit
  aceiteTermosLgpd?: Prisma.AceiteTermosLgpdOmit
}

/* Types for Logging */
export type LogLevel = 'info' | 'query' | 'warn' | 'error'
export type LogDefinition = {
  level: LogLevel
  emit: 'stdout' | 'event'
}

export type CheckIsLogLevel<T> = T extends LogLevel ? T : never;

export type GetLogType<T> = CheckIsLogLevel<
  T extends LogDefinition ? T['level'] : T
>;

export type GetEvents<T extends any[]> = T extends Array<LogLevel | LogDefinition>
  ? GetLogType<T[number]>
  : never;

export type QueryEvent = {
  timestamp: Date
  query: string
  params: string
  duration: number
  target: string
}

export type LogEvent = {
  timestamp: Date
  message: string
  target: string
}
/* End Types for Logging */


export type PrismaAction =
  | 'findUnique'
  | 'findUniqueOrThrow'
  | 'findMany'
  | 'findFirst'
  | 'findFirstOrThrow'
  | 'create'
  | 'createMany'
  | 'createManyAndReturn'
  | 'update'
  | 'updateMany'
  | 'updateManyAndReturn'
  | 'upsert'
  | 'delete'
  | 'deleteMany'
  | 'executeRaw'
  | 'queryRaw'
  | 'aggregate'
  | 'count'
  | 'runCommandRaw'
  | 'findRaw'
  | 'groupBy'

/**
 * `PrismaClient` proxy available in interactive transactions.
 */
export type TransactionClient = Omit<DefaultPrismaClient, runtime.ITXClientDenyList>

