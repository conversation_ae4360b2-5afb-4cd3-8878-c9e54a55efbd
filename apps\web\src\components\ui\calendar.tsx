import { useState } from "react";
import { cn, getCurrentDate } from "@/lib/utils";
import { Button } from "./button";
import {
  format,
  startOfMonth,
  endOfMonth,
  eachDayOfInterval,
  isSameMonth,
  isSameDay,
  isToday,
  addMonths,
  subMonths,
  getDay,
} from "date-fns";
import { ptBR } from "date-fns/locale";
import { ChevronLeft, ChevronRight } from "lucide-react";

export interface CalendarProps {
  mode?: "single" | "range";
  selected?: Date | { from: Date; to?: Date };
  onSelect?: (date: Date | { from: Date; to?: Date } | undefined) => void;
  disabled?: (date: Date) => boolean;
  className?: string;
  month?: Date;
  onMonthChange?: (month: Date) => void;
}

export function Calendar({
  mode = "single",
  selected,
  onSelect,
  disabled,
  className,
  month: controlledMonth,
  onMonthChange,
}: CalendarProps) {
  const [internalMonth, setInternalMonth] = useState(getCurrentDate());
  const currentMonth = controlledMonth || internalMonth;

  const handleMonthChange = (newMonth: Date) => {
    if (onMonthChange) {
      onMonthChange(newMonth);
    } else {
      setInternalMonth(newMonth);
    }
  };

  const monthStart = startOfMonth(currentMonth);
  const monthEnd = endOfMonth(currentMonth);
  const days = eachDayOfInterval({ start: monthStart, end: monthEnd });

  // Get the first day of the week for the month
  const startDay = getDay(monthStart); // 0 = Sunday, 1 = Monday, etc.

  // Add empty cells for days before the first day of the month
  const emptyCells = Array.from({ length: startDay }, (_, i) => i);

  const previousMonth = () => {
    handleMonthChange(subMonths(currentMonth, 1));
  };

  const nextMonth = () => {
    handleMonthChange(addMonths(currentMonth, 1));
  };

  const handleDayClick = (day: Date) => {
    if (disabled?.(day)) return;

    if (mode === "single") {
      onSelect?.(day);
    } else if (mode === "range") {
      if (!selected || (typeof selected === "object" && "from" in selected)) {
        const rangeSelected = selected as { from: Date; to?: Date } | undefined;
        if (!rangeSelected?.from || (rangeSelected.from && rangeSelected.to)) {
          // Start new range
          onSelect?.({ from: day });
        } else if (rangeSelected.from && !rangeSelected.to) {
          // Complete the range
          if (day >= rangeSelected.from) {
            onSelect?.({ from: rangeSelected.from, to: day });
          } else {
            onSelect?.({ from: day, to: rangeSelected.from });
          }
        }
      }
    }
  };

  const isDaySelected = (day: Date) => {
    if (mode === "single") {
      return selected && isSameDay(day, selected as Date);
    } else if (mode === "range" && selected && typeof selected === "object" && "from" in selected) {
      const rangeSelected = selected as { from: Date; to?: Date };
      if (rangeSelected.from && rangeSelected.to) {
        return day >= rangeSelected.from && day <= rangeSelected.to;
      } else if (rangeSelected.from) {
        return isSameDay(day, rangeSelected.from);
      }
    }
    return false;
  };

  const isRangeStart = (day: Date) => {
    if (mode === "range" && selected && typeof selected === "object" && "from" in selected) {
      const rangeSelected = selected as { from: Date; to?: Date };
      return rangeSelected.from && isSameDay(day, rangeSelected.from);
    }
    return false;
  };

  const isRangeEnd = (day: Date) => {
    if (mode === "range" && selected && typeof selected === "object" && "from" in selected) {
      const rangeSelected = selected as { from: Date; to?: Date };
      return rangeSelected.to && isSameDay(day, rangeSelected.to);
    }
    return false;
  };

  return (
    <div className={cn("p-3", className)}>
      {/* Header with month navigation */}
      <div className="flex items-center justify-between mb-4">
        <Button variant="outline" size="icon" onClick={previousMonth} className="h-7 w-7">
          <ChevronLeft className="h-4 w-4" />
        </Button>
        <div className="font-semibold">{format(currentMonth, "MMMM yyyy", { locale: ptBR })}</div>
        <Button variant="outline" size="icon" onClick={nextMonth} className="h-7 w-7">
          <ChevronRight className="h-4 w-4" />
        </Button>
      </div>

      {/* Days of week header */}
      <div className="grid grid-cols-7 mb-2">
        {["Dom", "Seg", "Ter", "Qua", "Qui", "Sex", "Sáb"].map((day) => (
          <div key={day} className="text-xs font-medium text-muted-foreground p-2 text-center">
            {day}
          </div>
        ))}
      </div>

      {/* Calendar grid */}
      <div className="grid grid-cols-7 gap-1">
        {/* Empty cells for days before the first day of the month */}
        {emptyCells.map((_, index) => (
          <div key={`empty-${index}`} className="p-2" />
        ))}

        {/* Days of the month */}
        {days.map((day) => {
          const isSelected = isDaySelected(day);
          const isDisabled = disabled?.(day);
          const isCurrentDay = isToday(day);
          const isStart = isRangeStart(day);
          const isEnd = isRangeEnd(day);

          return (
            <Button
              key={day.toISOString()}
              variant="ghost"
              size="sm"
              className={cn(
                "h-9 w-9 p-0 font-normal",
                isSelected && "bg-primary text-primary-foreground hover:bg-primary hover:text-primary-foreground",
                isCurrentDay && !isSelected && "bg-accent text-accent-foreground",
                isDisabled && "text-muted-foreground opacity-50 cursor-not-allowed hover:bg-transparent",
                mode === "range" && isSelected && !isStart && !isEnd && "rounded-none",
                mode === "range" && isStart && "rounded-r-none",
                mode === "range" && isEnd && "rounded-l-none"
              )}
              disabled={isDisabled}
              onClick={() => handleDayClick(day)}
            >
              {format(day, "d")}
            </Button>
          );
        })}
      </div>
    </div>
  );
}
