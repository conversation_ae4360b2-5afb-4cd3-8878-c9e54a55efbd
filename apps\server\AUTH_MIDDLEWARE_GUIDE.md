# Guia de Autenticação e Autorização

## Visão Geral

Implementamos um sistema completo de autenticação e autorização baseado em JWT para proteger as rotas da API.

## Estrutura

### 1. Middleware de Autenticação

- **Arquivo**: `src/middlewares/auth.middleware.ts`
- **Funções principais**:
  - `authenticate`: Valida o token JWT e adiciona dados do usuário ao request
  - `authorize(...roles)`: Verifica se o usuário tem uma das roles permitidas
  - `authenticateOptional`: Autenticação opcional (não bloqueia se sem token)

### 2. Configuração Global

- **Arquivo**: `src/index.ts`
- <PERSON><PERSON> as rotas protegidas agora passam pelo middleware `authenticate`
- Exceção: rotas de `/auth/*` são públicas

### 3. Interface do Request

- **Arquivo**: `src/types/fastify.d.ts`
- Adiciona propriedade `user` ao FastifyRequest com informações do usuário autenticado

## Uso nas Rotas

### Autenticação Básica (aplicada globalmente)

Todas as rotas exceto `/auth/*` exigem token JWT válido.

### Autorização por Role

```typescript
// Exemplo: apenas admin e master podem criar usuários
app.post("/usuarios", { preHandler: [authorize("admin", "master")] }, async (request, reply) => {
  // ...
});
```

### Acessar Dados do Usuário

```typescript
app.get("/me", async (request, reply) => {
  const userId = request.user?.id; // ID do usuário autenticado
  const userRoles = request.user?.roles; // Roles do usuário
  // ...
});
```

## Roles Disponíveis

- `admin`: Administrador completo do sistema
- `master`: Acesso total (similar ao admin)
- `gestor`: Gestão de recursos e visualização
- `profissional`: Profissional de saúde
- `financeiro`: Acesso às operações financeiras

## Endpoints Protegidos

### Usuários (`/api/usuarios/*`)

- **GET /api/me**: Qualquer usuário autenticado (retorna próprio perfil)
- **GET /api/usuarios**: admin, master, gestor
- **GET /api/usuarios/:uuid**: admin, master, gestor
- **POST /api/usuarios**: admin, master
- **PUT /api/usuarios/:uuid**: admin, master
- **DELETE /api/usuarios/:uuid**: admin, master

### Demais Rotas

Todas as rotas abaixo exigem autenticação mas não têm restrição de role específica:

- `/api/clientes/*`
- `/api/profissionais/*`
- `/api/plantoes/*`
- `/api/locais/*`
- `/api/fechamentos/*`
- `/api/antecipacoes/*`
- `/api/especialidades/*`
- `/api/presencas/*`
- `/api/dashboard/*`
- `/api/audit/*`

## Fluxo de Autenticação

1. **Login**: POST `/api/auth/login`
   - Envia: `{ usuario: "email ou cpf", password: "senha" }`
   - Retorna: `{ token: "JWT", usuario: {...} }`

2. **Uso do Token**:
   - Adicionar header: `Authorization: Bearer <token>`

3. **Verificar Token**: POST `/api/auth/verify`
   - Valida e retorna dados atualizados do usuário

4. **Logout**: POST `/api/auth/logout`
   - Remove token no cliente (JWT é stateless)

## Segurança

- Senhas são hasheadas com bcrypt (10 rounds)
- Tokens JWT expiram em 2 dias
- Validação de usuário ativo a cada requisição
- Audit log de todas as operações críticas

## Próximos Passos Recomendados

1. Implementar refresh tokens
2. Adicionar rate limiting
3. Configurar CORS mais restritivo em produção
4. Implementar 2FA para roles administrativas
5. Adicionar controle granular de permissões por recurso
