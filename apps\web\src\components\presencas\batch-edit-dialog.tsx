import {
  Dialog,
  DialogContent,
  DialogDescription,
  DialogFooter,
  DialogHeader,
  DialogTitle,
} from "@/components/ui/dialog";
import { Button } from "@/components/ui/button";
import { Input } from "@/components/ui/input";
import { Label } from "@/components/ui/label";
import { Textarea } from "@/components/ui/textarea";
import { Alert, AlertDescription } from "@/components/ui/alert";
import { Info } from "lucide-react";

interface BatchEditDialogProps {
  isOpen: boolean;
  onOpenChange: (open: boolean) => void;
  selectedRegistros: Set<number>;
  batchEditData: {
    entrada?: string;
    saida?: string;
    intervalo?: string;
    observacao?: string;
  };
  onBatchEditDataChange: (data: { entrada?: string; saida?: string; intervalo?: string; observacao?: string }) => void;
  onSave: () => void;
  onCancel: () => void;
  isLoading: boolean;
}

export function BatchEditDialog({
  isOpen,
  onOpenChange,
  selectedRegistros,
  batchEditData,
  onBatchEditDataChange,
  onSave,
  onCancel,
  isLoading,
}: BatchEditDialogProps) {
  return (
    <Dialog open={isOpen} onOpenChange={onOpenChange}>
      <DialogContent>
        <DialogHeader>
          <DialogTitle>Editar Registros em Lote</DialogTitle>
          <DialogDescription>
            Editando {selectedRegistros.size} registros selecionados. Os campos preenchidos serão aplicados a todos os
            registros selecionados.
          </DialogDescription>
        </DialogHeader>
        <div className="space-y-4">
          <div className="grid grid-cols-2 gap-4">
            <div className="space-y-2">
              <Label htmlFor="batchEntrada">Hora Entrada</Label>
              <Input
                id="batchEntrada"
                type="time"
                value={batchEditData.entrada || ""}
                onChange={(e) => onBatchEditDataChange({ ...batchEditData, entrada: e.target.value })}
                placeholder="Deixe vazio para não alterar"
              />
            </div>

            <div className="space-y-2">
              <Label htmlFor="batchSaida">Hora Saída</Label>
              <Input
                id="batchSaida"
                type="time"
                value={batchEditData.saida || ""}
                onChange={(e) => onBatchEditDataChange({ ...batchEditData, saida: e.target.value })}
                placeholder="Deixe vazio para não alterar"
              />
            </div>
          </div>

          <div className="space-y-2">
            <Label htmlFor="batchIntervalo">Intervalo</Label>
            <Input
              id="batchIntervalo"
              type="time"
              value={batchEditData.intervalo || ""}
              onChange={(e) => onBatchEditDataChange({ ...batchEditData, intervalo: e.target.value })}
              placeholder="Deixe vazio para não alterar"
            />
          </div>

          <div className="space-y-2">
            <Label htmlFor="batchObservacao">Observação</Label>
            <Textarea
              id="batchObservacao"
              value={batchEditData.observacao || ""}
              onChange={(e) => onBatchEditDataChange({ ...batchEditData, observacao: e.target.value })}
              placeholder="Deixe vazio para não alterar"
              rows={3}
            />
          </div>

          <Alert>
            <Info className="h-4 w-4" />
            <AlertDescription>
              Apenas os campos preenchidos serão atualizados. Campos vazios não alterarão os valores existentes.
            </AlertDescription>
          </Alert>
        </div>

        <DialogFooter className="mt-6">
          <Button type="button" variant="outline" onClick={onCancel}>
            Cancelar
          </Button>
          <Button onClick={onSave} disabled={isLoading}>
            {isLoading ? "Salvando..." : `Atualizar ${selectedRegistros.size} Registros`}
          </Button>
        </DialogFooter>
      </DialogContent>
    </Dialog>
  );
}
