# Documentação Completa - Sistema de Antecipação de Recebíveis

## 📋 Visão Geral

O Sistema de Antecipação de Recebíveis permite que profissionais de saúde solicitem o adiantamento de valores referentes aos seus plantões já realizados, mediante cessão de direitos creditórios. O sistema implementa um fluxo completo desde a solicitação até a assinatura digital do termo de cessão.

## 🏗️ Arquitetura do Sistema

### Entidades Principais

#### 1. Antecipacao (Model)

```sql
model Antecipacao {
  id                  Int       @id @default(autoincrement())
  uuid                String    @unique @default(uuid())
  plantaoId           Int
  profissionalId      Int
  valorSolicitado     Float
  valorAprovado       Float?
  percentual          Float     // Percentual de antecipação (máximo 100%)
  taxaAntecipacao     Float?    // Taxa aplicada baseada na fórmula do cliente
  dataPagamentoPrevista DateTime?
  status              String    @default("PENDENTE") // PENDENTE|APROVADA|REJEITADA|PAGA|CANCELADA

  // Dados da CCB
  numeroCCB           String?   @db.VarChar(250)
  dataCCB             DateTime?
  arquivoCCB          String?   @db.VarChar(250)

  // Campos para assinatura digital do termo
  termoAssinado        Boolean   @default(false)
  dataAssinaturaTermo  DateTime?
  ipAssinatura         String?   @db.VarChar(50)
  tokenExpiracaoEm     DateTime?

  observacoes         String?   @db.Text
  metaData            Json?
  createdAt           DateTime  @default(now())
  updatedAt           DateTime  @default(now()) @updatedAt
  deletedAt           DateTime?

  // Relacionamentos
  plantao             Plantao               @relation(fields: [plantaoId], references: [id])
  profissional        Profissional          @relation(fields: [profissionalId], references: [id])
  fechamentos         Fechamento[]          // 1:N - uma antecipação pode ter vários fechamentos
  historico           AntecipacaoHistorico[]
}
```

### Status da Antecipação

| Status      | Descrição                                            |
| ----------- | ---------------------------------------------------- |
| `PENDENTE`  | Antecipação criada, aguardando assinatura do termo   |
| `APROVADA`  | Termo assinado e antecipação aprovada para pagamento |
| `REJEITADA` | Antecipação rejeitada por algum motivo               |
| `PAGA`      | Valor da antecipação foi creditado ao profissional   |
| `CANCELADA` | Antecipação cancelada antes do pagamento             |

## 🔄 Fluxo Completo de Antecipação

### 1. Criação da Antecipação

**Endpoint**: `POST /antecipacoes`

**Processo**:

1. **Validação de Fechamentos**: Verifica se todos os fechamentos estão aprovados
2. **Cálculo de Valores**:
   - Valor total baseado nos fechamentos selecionados
   - Aplicação do percentual de antecipação
   - Cálculo da taxa de antecipação baseada na fórmula do cliente
3. **Status Inicial**: Antecipação criada com status "PENDENTE"
4. **Aguarda Aprovação**: Nenhuma notificação é enviada nesta etapa

### 2. Aprovação da Antecipação

**Endpoint**: `PUT /antecipacoes/:uuid`

**Processo quando status muda para "APROVADA"**:

1. **Envio de Email Simples**: Notificação para o profissional fazer login na plataforma
2. **Sem Tokens**: Não há mais geração de tokens ou links diretos
3. **Orientação**: Email orienta o profissional a fazer login

**Fórmula de Cálculo da Taxa**:

```typescript
// Se dias <= 30: taxa padrão
// Se dias > 30: (1 + taxaPadrao)^(dias / 30) - 1
const calcularTaxa = (taxaPadrao: number, dias: number): number => {
  if (dias <= 30) {
    return taxaPadrao;
  }
  return Math.pow(1 + taxaPadrao, dias / 30) - 1;
};
```

### 3. Notificação por Email

**Funcionalidade**: `sendAntecipacaoEmail()`
**Trigger**: Apenas quando antecipação é aprovada (status muda para "APROVADA")

**Conteúdo do Email**:

- Informações do plantão (cliente, local, período)
- Valores da antecipação (solicitado, líquido, taxa)
- **Link para Login**: Direciona para a página de login da plataforma
- **Sem Expiração**: Não há mais prazo de validade
- **Instruções**: Orienta a fazer login para assinar

### 4. Login na Plataforma

**Fluxo Automático no Dashboard**:

1. **Profissional faz login** na plataforma
2. **Modal automático** aparece se houver termos pendentes
3. **Indicador visual** no dashboard
4. **Carregamento automático** após 1.5s da entrada no dashboard

### 5. Visualização do Termo

**Endpoint**: `GET /antecipacoes/:uuid/termo`

**Validações**:

- Usuário autenticado obrigatório
- Antecipação com status "APROVADA"
- Termo não assinado ainda
- Verificação de permissões (profissional proprietário ou admin)

**Dados Retornados**:

- Informações completas da antecipação
- Dados do termo de cessão formatados
- Status de autenticação do usuário

### 6. Assinatura Digital

**Endpoint**: `POST /antecipacoes/:uuid/assinar`

**Processo de Assinatura**:

1. **Autenticação Obrigatória**: Usuário deve estar logado
2. **Validação do Aceite**: Usuário deve marcar aceite dos termos
3. **Verificação de Identidade**: Confirma se é o profissional correto
4. **Captura de Dados**:
   - Data e hora da assinatura
   - IP do usuário
   - Hash de segurança (SHA-256)
5. **Atualização do Status**: Marca termo como assinado
6. **Email de Confirmação**: Envia confirmação da assinatura
7. **Redirecionamento**: Volta para o dashboard

### 7. Consultas e Relatórios

#### Antecipações Pendentes de Assinatura

**Endpoint**: `GET /antecipacoes/pendentes-assinatura`

- Lista termos não assinados do profissional logado
- Filtra por status "APROVADA" e `termoAssinado: false`

## 🔐 Sistema de Assinatura Digital

### Segurança Baseada em Autenticação

- **Sem Tokens**: Sistema baseado em autenticação de usuário
- **Login Obrigatório**: Usuário deve estar logado para assinar
- **Identificação**: UUID da antecipação como identificador
- **Sessão**: Validação baseada na sessão do usuário autenticado

### Hash de Assinatura

```typescript
// Geração do hash de segurança
const hashAssinatura = createHash("sha256")
  .update(`${antecipacao.uuid}-${profissionalId}-${dataAssinatura.toISOString()}`)
  .digest("hex");
```

### Auditoria de Assinatura

- Data e hora exata da assinatura
- IP do usuário no momento da assinatura
- Hash de segurança único
- Histórico completo de mudanças de status
- Vinculação direta com o usuário autenticado

## 📧 Sistema de Notificações

### Email de Aprovação

- **Template**: HTML responsivo com dados da antecipação
- **Conteúdo**: Informações completas + link para login
- **Orientação**: Direciona para fazer login na plataforma

### Email de Confirmação

- **Trigger**: Após assinatura bem-sucedida
- **Conteúdo**: Confirmação da assinatura + próximos passos
- **Dados**: Número da antecipação, data de assinatura, cliente

## 🌍 Gestão de Timezone

### Implementação Consistente

```typescript
// Uso obrigatório das funções @shared/date
import { getCurrentDateInTimezone, formatInTimezone, addDaysToDate } from "@shared/date";

// Hierarquia de timezone
const timezone = plantao.cliente?.fusoHorario || plantao.fusoHorario || request.fusoHorario || "America/Sao_Paulo";
```

### Cálculos de Data

- **Expiração do Token**: Considera timezone do cliente/plantão
- **Formatação**: Datas exibidas no timezone correto
- **Persistência**: Todas as datas armazenadas em UTC

## 🎯 Regras de Negócio

### Validações Obrigatórias

1. **Fechamentos**:
   - Todos os fechamentos devem estar com status `APROVADO`
   - Não podem estar vinculados a outra antecipação

2. **Percentual de Antecipação**:
   - Máximo de 100% do valor dos fechamentos
   - Valor solicitado não pode exceder o total disponível

3. **Taxa de Antecipação**:
   - Calculada automaticamente baseada na configuração do cliente
   - Editável durante a criação da antecipação
   - Fórmula de juros compostos para períodos > 30 dias

4. **Token de Assinatura**:
   - Validade máxima de 48 horas
   - Único por antecipação
   - Invalidado após uso ou expiração

### Restrições de Acesso

1. **Visualização do Termo**:
   - Profissional: Apenas seus próprios termos
   - Admin/Master: Todos os termos
   - Público: Visualização limitada sem ação

2. **Assinatura**:
   - Apenas o profissional proprietário pode assinar
   - Requer autenticação válida
   - Validação de identidade obrigatória

## 📊 Métricas e Monitoramento

### Campos de Controle

- `createdAt`: Data de criação da antecipação
- `dataAssinaturaTermo`: Data da assinatura digital
- `tokenExpiracaoEm`: Controle de validade do token
- `ipAssinatura`: Rastreamento de segurança

### Histórico de Status

```sql
model AntecipacaoHistorico {
  id            Int      @id @default(autoincrement())
  antecipacaoId Int
  status        String   @db.VarChar(20)
  metaData      Json?
  createdAt     DateTime @default(now())

  antecipacao   Antecipacao @relation(fields: [antecipacaoId], references: [id])
}
```

## 🔧 Integrações

### Sistema de Email

- **Serviço**: Configurado via `getEmailService()`
- **Templates**: HTML + Text para compatibilidade
- **Fallback**: Logs de erro sem interromper o fluxo

### Frontend (React Router v7)

- **Componentes**:
  - `termos-pendentes-modal.tsx`: Modal para listar termos
  - `termo.$token.tsx`: Página de visualização/assinatura
- **Rotas**: Integração com sistema de roteamento existente

## 🚨 Tratamento de Erros

### Cenários Cobertos

1. **Token Inválido/Expirado**:

   ```json
   {
     "error": "Token inválido ou expirado",
     "code": 404
   }
   ```

2. **Termo Já Assinado**:

   ```json
   {
     "error": "Este termo já foi assinado",
     "dataAssinatura": "2024-09-08T10:30:00Z",
     "code": 400
   }
   ```

3. **Sem Permissão**:

   ```json
   {
     "error": "Você não tem permissão para visualizar este termo",
     "isProfissional": false,
     "needsAuthentication": true,
     "code": 403
   }
   ```

4. **Antecipação Não Aprovada**:

   ```json
   {
     "error": "Antecipação deve estar aprovada para gerar token de assinatura",
     "code": 400
   }
   ```

5. **Falha no Email**:
   - Não interrompe a atualização da antecipação
   - Log do erro para monitoramento
   - Token permanece válido para reenvio manual

## 📋 Checklist de Implementação

### ✅ Concluído

- [x] Modelo de dados para assinatura digital
- [x] Sistema de geração e validação de tokens
- [x] Endpoints para visualização e assinatura de termos
- [x] Sistema de notificações por email
- [x] Validações de segurança e permissões
- [x] Integração com sistema de timezone
- [x] Componentes frontend para assinatura
- [x] Auditoria completa de assinaturas

### 🔄 Próximos Passos (Conforme atualizado.md)

- [ ] **Limitação de Percentual**: Implementar limite máximo de 100%
- [ ] **Integração com API Quará**: Envio direto após assinatura
- [ ] **Dashboard Profissional**: Cards consolidados de antecipações
- [ ] **Calendário de Escalas**: Visualização de plantões
- [ ] **Validação Anti-Fraude**: Evitar plantões sobrepostos
- [ ] **Fórmula Dinâmica**: Configuração editável por cliente
- [ ] **Portal de Antecipação**: Projeto separado para Rede Total/Aura

## 🔍 Considerações Técnicas

### Performance

- Indexes otimizados para consultas frequentes
- Paginação em listagens de antecipações
- Cache de configurações de cliente

### Segurança

- Tokens únicos com expiração
- Hash SHA-256 para integridade
- Auditoria completa de ações
- Validação de permissões em múltiplos níveis

### Manutenibilidade

- Separação clara de responsabilidades
- Reutilização de componentes de email
- Padrão de erro consistente
- Documentação inline no código

---

**Versão**: 1.0.0  
**Última Atualização**: 2024-09-08  
**Status**: Implementado e Funcional
