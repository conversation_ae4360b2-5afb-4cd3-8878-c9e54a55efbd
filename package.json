{"name": "gs2-monorepo", "private": true, "type": "module", "license": "UNLICENSED", "workspaces": ["apps/*", "packages/*"], "scripts": {"dev:server": "yarn workspace server dev", "dev:web": "yarn workspace web dev", "build": "yarn workspaces run build", "build:lambda": "yarn workspace server run build:lambda", "check-types": "yarn workspaces run check-types", "db:push": "yarn workspace server run db:push", "db:studio": "yarn workspace server run db:studio", "db:generate": "yarn workspace server run db:generate", "db:migrate": "yarn workspace server run db:migrate", "db:seed": "yarn workspace server run db:seed", "db:reset": "yarn workspace server run db:reset", "format": "prettier --write \"**/*.{js,jsx,ts,tsx,json,css,md}\" --ignore-path .prettierignore", "format:check": "prettier --check \"**/*.{js,jsx,ts,tsx,json,css,md}\" --ignore-path .prettierignore", "lint": "yarn workspaces run lint", "lint:fix": "yarn workspaces run lint:fix", "verify": "yarn format:check && yarn check-types && yarn lint", "verify:fix": "yarn format && yarn lint:fix", "pre-commit": "yarn verify", "claude": "node scripts/toggle-claude.js default && claude", "glm": "node scripts/toggle-claude.js custom && claude"}, "devDependencies": {"eslint": "^9.33.0", "prettier": "^3.6.2"}}