import { useState } from "react";
import { useQuery, useMutation, useQueryClient } from "@tanstack/react-query";
import { Plus, Trash2, Search, UserPlus, Users, Eye, Shield, MoreHorizontal, Key } from "lucide-react";
import { Link, useNavigate } from "@tanstack/react-router";
import { Button } from "@/components/ui/button";
import { Input } from "@/components/ui/input";
import { Table, TableBody, TableCell, TableHead, TableHeader, TableRow } from "@/components/ui/table";
import { Badge } from "@/components/ui/badge";
import { Card, CardContent, CardHeader, CardTitle } from "@/components/ui/card";
import {
  DropdownMenu,
  DropdownMenuContent,
  DropdownMenuItem,
  DropdownMenuTrigger,
} from "@/components/ui/dropdown-menu";
import { Skeleton } from "@/components/ui/skeleton";
import { DeleteConfirmDialog } from "@/components/ui/delete-confirm-dialog";
import { toast } from "sonner";
import { api } from "@/lib/api";
import { formatCPF, formatPhone } from "@/lib/utils";

interface Usuario {
  id: number;
  uuid: string;
  nome: string;
  email: string;
  telefone: string | null;
  cpf: string | null;
  ativo: boolean;
  deveResetarSenha: boolean;
  roles: string[];
  createdAt: string;
  updatedAt: string;
  ultimoAcesso: string | null;
}

export function UsuariosList() {
  const navigate = useNavigate();
  const [searchTerm, setSearchTerm] = useState("");
  const [deleteUserId, setDeleteUserId] = useState<string | null>(null);
  const queryClient = useQueryClient();

  // Buscar usuários
  const {
    data: usuarios,
    isLoading,
    error,
  } = useQuery({
    queryKey: ["usuarios"],
    queryFn: async () => {
      return await api.get<Usuario[]>("/usuarios");
    },
    refetchOnWindowFocus: true,
    refetchOnMount: true,
  });

  // Mutation para deletar usuário
  const deleteMutation = useMutation({
    mutationFn: async (id: string) => {
      await api.delete(`/usuarios/${id}`);
    },
    onSuccess: () => {
      queryClient.invalidateQueries({ queryKey: ["usuarios"] });
      toast.success("Usuário desativado com sucesso.");
      setDeleteUserId(null);
    },
    onError: () => {
      toast.error("Ocorreu um erro ao tentar desativar o usuário.");
    },
  });

  // Mutation para forçar reset de senha
  const requireResetMutation = useMutation({
    mutationFn: async (uuid: string) => {
      await api.post(`/usuarios/${uuid}/require-password-reset`);
    },
    onSuccess: () => {
      queryClient.invalidateQueries({ queryKey: ["usuarios"] });
      toast.success("Usuário deverá resetar a senha no próximo acesso.");
    },
    onError: () => {
      toast.error("Erro ao marcar reset de senha.");
    },
  });

  // Filtrar usuários
  const filteredUsuarios = usuarios?.filter((usuario) => {
    const searchLower = searchTerm.toLowerCase();
    return (
      usuario.nome.toLowerCase().includes(searchLower) ||
      usuario.email.toLowerCase().includes(searchLower) ||
      usuario.cpf?.toLowerCase().includes(searchLower) ||
      usuario.telefone?.toLowerCase().includes(searchLower)
    );
  });

  const getRoleBadgeVariant = (role: string) => {
    switch (role) {
      case "admin":
        return "secondary";
      case "master":
        return "default";
      case "gestor":
        return "secondary";
      case "profissional":
        return "secondary";
      default:
        return "outline";
    }
  };

  const formatCPF = (cpf: string) => {
    return cpf.replace(/(\d{3})(\d{3})(\d{3})(\d{2})/, "$1.$2.$3-$4");
  };

  const formatPhone = (phone: string) => {
    const numbers = phone.replace(/\D/g, "");
    if (numbers.length === 11) {
      return numbers.replace(/(\d{2})(\d{5})(\d{4})/, "($1) $2-$3");
    }
    return phone;
  };

  if (error) {
    console.error("Erro ao buscar usuários:", error);
    return (
      <div className="container mx-auto py-6">
        <div className="bg-red-50 border border-red-200 rounded-md p-4">
          <p className="text-red-600">Erro ao carregar usuários: {error.message}</p>
        </div>
      </div>
    );
  }

  return (
    <div className="space-y-6">
      <div className="flex justify-between items-center">
        <div>
          <h1 className="text-3xl font-bold">Usuários</h1>
          <p className="text-muted-foreground">Gerencie os usuários do sistema</p>
        </div>
        <Link to="/cadastros/usuarios/novo">
          <Button>
            <Plus className="mr-2 h-4 w-4" />
            Novo Usuário
          </Button>
        </Link>
      </div>

      <Card>
        <CardHeader>
          <CardTitle>Lista de Usuários</CardTitle>
          <div className="flex gap-4 mt-4">
            <div className="relative flex-1 max-w-sm">
              <Search className="absolute left-3 top-1/2 transform -translate-y-1/2 text-muted-foreground h-4 w-4" />
              <Input
                placeholder="Buscar por nome, email, CPF ou telefone..."
                value={searchTerm}
                onChange={(e) => setSearchTerm(e.target.value)}
                className="pl-9"
              />
            </div>
          </div>
        </CardHeader>
        <CardContent>
          <div className="rounded-md border">
            <Table>
              <TableHeader>
                <TableRow>
                  <TableHead>Nome</TableHead>
                  <TableHead>CPF</TableHead>
                  <TableHead>Email</TableHead>
                  <TableHead>Telefone</TableHead>
                  <TableHead>Perfis</TableHead>
                  <TableHead>Status</TableHead>
                  <TableHead>Reset Senha</TableHead>
                  <TableHead>Último Acesso</TableHead>
                  <TableHead className="w-[50px]"></TableHead>
                </TableRow>
              </TableHeader>
              <TableBody>
                {isLoading ? (
                  Array.from({ length: 5 }).map((_, i) => (
                    <TableRow key={i}>
                      <TableCell>
                        <Skeleton className="h-4 w-32" />
                      </TableCell>
                      <TableCell>
                        <Skeleton className="h-4 w-28" />
                      </TableCell>
                      <TableCell>
                        <Skeleton className="h-4 w-36" />
                      </TableCell>
                      <TableCell>
                        <Skeleton className="h-4 w-24" />
                      </TableCell>
                      <TableCell>
                        <Skeleton className="h-4 w-24" />
                      </TableCell>
                      <TableCell>
                        <Skeleton className="h-4 w-16" />
                      </TableCell>
                      <TableCell>
                        <Skeleton className="h-4 w-20" />
                      </TableCell>
                      <TableCell>
                        <Skeleton className="h-4 w-4" />
                      </TableCell>
                    </TableRow>
                  ))
                ) : !filteredUsuarios || filteredUsuarios.length === 0 ? (
                  <TableRow>
                    <TableCell colSpan={9} className="text-center py-8">
                      <Users className="mx-auto h-12 w-12 text-muted-foreground/20 mb-2" />
                      <p className="text-muted-foreground">Nenhum usuário encontrado</p>
                    </TableCell>
                  </TableRow>
                ) : (
                  filteredUsuarios?.map((usuario) => (
                    <TableRow key={usuario.uuid}>
                      <TableCell className="font-medium">{usuario.nome}</TableCell>
                      <TableCell className="font-mono text-sm">{usuario.cpf ? formatCPF(usuario.cpf) : "-"}</TableCell>
                      <TableCell>{usuario.email}</TableCell>
                      <TableCell>{usuario.telefone ? formatPhone(usuario.telefone) : "-"}</TableCell>
                      <TableCell>
                        <div className="flex gap-1 flex-wrap">
                          {usuario.roles && usuario.roles.length > 0 ? (
                            usuario.roles.map((role) => (
                              <Badge key={role} variant={getRoleBadgeVariant(role)}>
                                {role}
                              </Badge>
                            ))
                          ) : (
                            <span className="text-muted-foreground text-sm">Sem perfil</span>
                          )}
                        </div>
                      </TableCell>
                      <TableCell>
                        <Badge variant={usuario.ativo ? "default" : "secondary"}>
                          {usuario.ativo ? "Ativo" : "Inativo"}
                        </Badge>
                      </TableCell>
                      <TableCell>
                        <Badge variant={usuario.deveResetarSenha ? "secondary" : "outline"}>
                          {usuario.deveResetarSenha ? "Pendente" : "OK"}
                        </Badge>
                      </TableCell>
                      <TableCell>
                        {usuario.ultimoAcesso ? new Date(usuario.ultimoAcesso).toLocaleDateString("pt-BR") : "Nunca"}
                      </TableCell>
                      <TableCell>
                        <DropdownMenu>
                          <DropdownMenuTrigger asChild>
                            <Button variant="ghost" size="icon">
                              <MoreHorizontal className="h-4 w-4" />
                            </Button>
                          </DropdownMenuTrigger>
                          <DropdownMenuContent align="end">
                            <DropdownMenuItem onClick={() => navigate({ to: `/cadastros/usuarios/${usuario.uuid}` })}>
                              <Eye className="mr-2 h-4 w-4" />
                              Visualizar
                            </DropdownMenuItem>
                            <DropdownMenuItem
                              onClick={() => requireResetMutation.mutate(usuario.uuid)}
                              disabled={usuario.deveResetarSenha}
                            >
                              <Key className="mr-2 h-4 w-4" />
                              {usuario.deveResetarSenha ? "Reset Pendente" : "Forçar Reset de Senha"}
                            </DropdownMenuItem>
                            <DropdownMenuItem
                              className="text-destructive"
                              onClick={() => setDeleteUserId(usuario.uuid)}
                            >
                              <Trash2 className="mr-2 h-4 w-4" />
                              Excluir
                            </DropdownMenuItem>
                          </DropdownMenuContent>
                        </DropdownMenu>
                      </TableCell>
                    </TableRow>
                  ))
                )}
              </TableBody>
            </Table>
          </div>
        </CardContent>
      </Card>

      <DeleteConfirmDialog
        open={!!deleteUserId}
        onOpenChange={(open) => !open && setDeleteUserId(null)}
        onConfirm={() => deleteUserId && deleteMutation.mutate(deleteUserId)}
        title="Desativar usuário?"
        description="Esta ação irá desativar o usuário. O usuário não poderá mais acessar o sistema, mas seus dados serão mantidos."
        confirmText="Desativar"
        cancelText="Cancelar"
      />
    </div>
  );
}
