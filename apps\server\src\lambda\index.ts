import "dotenv/config";

import awsLambdaFastify from "@fastify/aws-lambda";

import { fastify } from "fastify";
import fastifyCors from "@fastify/cors";
import { authRouter } from "../routers/auth.router";
import { clienteRouter } from "../routers/cliente.router";
import { profissionalRouter } from "../routers/profissional.router";
import { plantaoRouter } from "../routers/plantao";
import { localAtendimentoRouter } from "../routers/local-atendimento.router";
import { fechamentoRouter } from "../routers/fechamento.router";
import { antecipacaoRouter } from "../routers/antecipacao.router";
import { especialidadeRouter } from "../routers/especialidade.router";
import { presencaDiaPlantaoRouter } from "../routers/registro-ponto.router";
import { dashboardRouter } from "../routers/dashboard.router";
import { auditRouter } from "../routers/audit.router";
import { usuariosRouter } from "../routers/usuarios.router";
import { authenticate } from "../middlewares/auth.middleware";
import { appEnv } from "../lib/env";

const baseCorsConfig = {
  origin: appEnv.FRONT_END_URL || true, // Em Lambda, aceitar qualquer origem ou configurar via env
  methods: ["GET", "POST", "PUT", "DELETE", "OPTIONS"],
  allowedHeaders: ["Content-Type", "Authorization", "X-Requested-With"],
  credentials: true,
  maxAge: 86400,
};

async function buildApp() {
  const app = fastify();

  await app.register(fastifyCors, baseCorsConfig);

  await app.register(async function apiRoutes(fastify) {
    // Rotas públicas (autenticação)
    await fastify.register(authRouter, { prefix: "/api" });

    // Rotas protegidas
    await fastify.register(
      async function protectedRoutes(fastify) {
        // Adiciona hook de autenticação para todas as rotas protegidas
        fastify.addHook("preHandler", authenticate);

        // Registra as rotas protegidas
        await fastify.register(clienteRouter);
        await fastify.register(profissionalRouter);
        await fastify.register(plantaoRouter);
        await fastify.register(localAtendimentoRouter);
        await fastify.register(fechamentoRouter);
        await fastify.register(antecipacaoRouter);
        await fastify.register(especialidadeRouter);
        await fastify.register(presencaDiaPlantaoRouter);
        await fastify.register(dashboardRouter);
        await fastify.register(auditRouter);
        await fastify.register(usuariosRouter);
      },
      { prefix: "/api" }
    );
  });

  return app;
}

const appPromise = buildApp();

export const handler = async (event: any, context: any) => {
  const app = await appPromise;
  const proxy = awsLambdaFastify(app);
  return proxy(event, context);
};
