import { useEffect } from "react";
import { <PERSON><PERSON> } from "@/components/ui/button";
import { Input } from "@/components/ui/input";
import { Label } from "@/components/ui/label";
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from "@/components/ui/card";
import { Switch } from "@/components/ui/switch";
import { Calendar, Loader2, Save } from "lucide-react";
import { DatePicker } from "@/components/ui/date-picker";
import { Select, SelectContent, SelectItem, SelectTrigger, SelectValue } from "@/components/ui/select";
import { Textarea } from "@/components/ui/textarea";
import { CalendarioPlantao, type DiaPlantao } from "@/components/calendario-plantao";
import { usePlantaoStore } from "@/stores/plantao.store";
import { formatDateStringUTC, getDaysInMonthForDate, parseISO, createLocalDate } from "@/lib/utils";
import type { Cliente, Profissional, LocalAtendimento } from "@/lib/api";

interface PlantaoFormProps {
  mode: "create" | "edit";
  clientes?: { data: Cliente[] };
  profissionais?: Profissional[] | { data: Profissional[] };
  locais?: { data: LocalAtendimento[] };
  onSubmit: () => void;
  isSubmitting?: boolean;
  onCancel?: () => void;
}

export function PlantaoForm({
  mode,
  clientes,
  profissionais,
  locais,
  onSubmit,
  isSubmitting = false,
  onCancel,
}: PlantaoFormProps) {
  const {
    form,
    setDataInicial,
    setDataFinal,
    setClienteId,
    setLocalAtendimentoId,
    setProfissionalId,
    setMes,
    setAno,
    setModalidadeTrabalho,
    setTipoFechamento,
    setTipoValor,
    setValorBase,
    setPrazoPagamentoDias,
    setHoraInicio,
    setHoraFim,
    setObservacoes,
    setDiasPlantao,
    setTipoTurno,
    getDaysInMonth,
    calculateTotalDays,
    calculateTotalHours,
    setDiasSemanaTodoPeriodo,
    saveDiasForMonth,
  } = usePlantaoStore();

  // Normalize profissionais data structure
  const profissionaisList = Array.isArray(profissionais) ? profissionais : profissionais?.data || [];

  // Initialize dias when month/year changes (load from saved data)
  useEffect(() => {
    const key = `${form.ano}-${form.mes}`;
    const diasSalvos = form.diasPorMes?.[key];

    if (diasSalvos && diasSalvos.length > 0) {
      setDiasPlantao(diasSalvos);
    } else if (!form.dataInicial || !form.dataFinal) {
      // Se não tem período definido, criar dias vazios
      const daysInMonth = getDaysInMonth();
      const newDias: DiaPlantao[] = [];

      for (let dia = 1; dia <= daysInMonth; dia++) {
        const dataStr = `${form.ano}-${form.mes.toString().padStart(2, "0")}-${dia.toString().padStart(2, "0")}`;
        newDias.push({
          data: dataStr,
          selecionado: false,
          horario: undefined,
        });
      }
      setDiasPlantao(newDias);
    }
  }, [form.mes, form.ano, form.diasPorMes, getDaysInMonth, setDiasPlantao, form.dataInicial, form.dataFinal]);

  // Ajustar mês e ano quando as datas mudam
  useEffect(() => {
    if (form.dataInicial) {
      const dataInicialDate = parseISO(form.dataInicial);
      setMes(dataInicialDate.getMonth() + 1);
      setAno(dataInicialDate.getFullYear());
    }
  }, [form.dataInicial, setMes, setAno]);

  // Gerar diasPlantao quando dataFinal é selecionado após dataInicial
  useEffect(() => {
    if (!form.dataInicial || !form.dataFinal) return;

    const dataInicial = parseISO(form.dataInicial);
    const dataFinal = parseISO(form.dataFinal);

    // Horários padrão baseados no tipo de turno
    const horariosPreDefinidos: Record<string, { inicio: string; fim: string; intervalo: string }> = {
      diurno: { inicio: "07:00", fim: "19:00", intervalo: "01:00" },
      comercial: { inicio: "09:00", fim: "18:00", intervalo: "01:00" },
      noturno: { inicio: "19:00", fim: "07:00", intervalo: "01:00" },
      customizado: { inicio: "", fim: "", intervalo: "" },
    };

    // Gerar todos os meses do período
    const mesesDoPeriodo: { ano: number; mes: number }[] = [];
    let currentDate = createLocalDate(dataInicial.getFullYear(), dataInicial.getMonth() + 1, 1);
    const endDate = createLocalDate(dataFinal.getFullYear(), dataFinal.getMonth() + 1, 1);

    while (currentDate <= endDate) {
      mesesDoPeriodo.push({
        ano: currentDate.getFullYear(),
        mes: currentDate.getMonth() + 1,
      });
      currentDate.setMonth(currentDate.getMonth() + 1);
    }

    // Gerar dias para cada mês
    const novosDiasPorMes: Record<string, DiaPlantao[]> = {};

    mesesDoPeriodo.forEach(({ ano, mes }) => {
      const key = `${ano}-${mes}`;
      const daysInMonth = getDaysInMonthForDate(ano, mes);
      const diasDoMes: DiaPlantao[] = [];

      for (let dia = 1; dia <= daysInMonth; dia++) {
        const currentDateDay = createLocalDate(ano, mes, dia);
        const dataStr = `${ano}-${mes.toString().padStart(2, "0")}-${dia.toString().padStart(2, "0")}`;

        // Verifica se o dia está dentro do período
        const dentroPeríodo = currentDateDay >= dataInicial && currentDateDay <= dataFinal;

        diasDoMes.push({
          data: dataStr,
          selecionado: dentroPeríodo,
          horario: dentroPeríodo && form.tipoTurno !== "customizado" ? horariosPreDefinidos[form.tipoTurno] : undefined,
        });
      }

      novosDiasPorMes[key] = diasDoMes;
    });

    // Atualizar o store com os novos dias
    const mesAtualKey = `${form.ano}-${form.mes}`;
    if (novosDiasPorMes[mesAtualKey]) {
      setDiasPlantao(novosDiasPorMes[mesAtualKey]);
    }

    // Salvar todos os meses no diasPorMes
    Object.entries(novosDiasPorMes).forEach(([key, dias]) => {
      const [ano, mes] = key.split("-").map(Number);
      saveDiasForMonth(mes, ano, dias);
    });

    // Definir horários no formulário baseado no primeiro dia selecionado
    const primeiroMes = Object.values(novosDiasPorMes)[0];
    const primeiroDiaSelecionado = primeiroMes?.find((d) => d.selecionado && d.horario);
    if (primeiroDiaSelecionado?.horario) {
      setHoraInicio(primeiroDiaSelecionado.horario.inicio);
      setHoraFim(primeiroDiaSelecionado.horario.fim);
    } else if (form.tipoTurno !== "customizado") {
      const horarioPadrao = horariosPreDefinidos[form.tipoTurno];
      setHoraInicio(horarioPadrao.inicio);
      setHoraFim(horarioPadrao.fim);
    }
  }, [
    form.dataInicial,
    form.dataFinal,
    form.tipoTurno,
    setDiasPlantao,
    saveDiasForMonth,
    setHoraInicio,
    setHoraFim,
    form.ano,
    form.mes,
  ]);

  const handleClienteChange = (clienteUuid: string) => {
    setClienteId(clienteUuid);
    setLocalAtendimentoId("");
    setProfissionalId(""); // Limpar profissional quando mudar cliente
  };

  const handleLocalChange = (localUuid: string) => {
    setLocalAtendimentoId(localUuid);
    setProfissionalId(""); // Limpar profissional quando mudar local
  };

  const handleDiasChange = (dias: DiaPlantao[]) => {
    setDiasPlantao(dias);
    // Também salvar no diasPorMes para o mês atual
    saveDiasForMonth(form.mes, form.ano, dias);

    // Update horaInicio and horaFim based on selected turno
    const firstSelectedDia = dias.find((d) => d.selecionado && d.horario);
    if (firstSelectedDia && firstSelectedDia.horario) {
      setHoraInicio(firstSelectedDia.horario.inicio);
      setHoraFim(firstSelectedDia.horario.fim);
    } else if (dias.some((d) => d.selecionado)) {
      // Se tem dias selecionados mas sem horário, usar horários padrão do turno
      const horariosPreDefinidos: Record<string, { inicio: string; fim: string; intervalo: string }> = {
        diurno: { inicio: "07:00", fim: "19:00", intervalo: "01:00" },
        comercial: { inicio: "09:00", fim: "18:00", intervalo: "01:00" },
        noturno: { inicio: "19:00", fim: "07:00", intervalo: "01:00" },
        customizado: { inicio: "", fim: "", intervalo: "" },
      };

      if (form.tipoTurno && form.tipoTurno !== "customizado") {
        const horarioPadrao = horariosPreDefinidos[form.tipoTurno];
        setHoraInicio(horarioPadrao.inicio);
        setHoraFim(horarioPadrao.fim);
      }
    } else {
      // Se não há dias selecionados, limpar horários
      setHoraInicio("");
      setHoraFim("");
    }
  };

  const navigateMonth = (direction: "prev" | "next") => {
    if (!form.dataInicial) return;

    // Salvar os dias do mês atual antes de navegar
    saveDiasForMonth(form.mes, form.ano, form.diasPlantao);

    const dataInicialDate = parseISO(form.dataInicial);
    const dataFinalDate = form.dataFinal ? parseISO(form.dataFinal) : createLocalDate(2099, 12, 31);

    // Gerar lista de todos os meses válidos no período
    const mesesValidos: { mes: number; ano: number }[] = [];
    let currentDate = createLocalDate(dataInicialDate.getFullYear(), dataInicialDate.getMonth() + 1, 1);
    const endDate = createLocalDate(dataFinalDate.getFullYear(), dataFinalDate.getMonth() + 1, 1);

    while (currentDate <= endDate) {
      mesesValidos.push({
        mes: currentDate.getMonth() + 1,
        ano: currentDate.getFullYear(),
      });
      currentDate.setMonth(currentDate.getMonth() + 1);
    }

    // Encontrar índice do mês atual
    const currentIndex = mesesValidos.findIndex((m) => m.mes === form.mes && m.ano === form.ano);

    if (currentIndex === -1) return; // Mês atual não está na lista válida

    let newIndex;
    if (direction === "next") {
      newIndex = currentIndex + 1;
      if (newIndex >= mesesValidos.length) return; // Já está no último mês
    } else {
      newIndex = currentIndex - 1;
      if (newIndex < 0) return; // Já está no primeiro mês
    }

    const novoMes = mesesValidos[newIndex];
    setMes(novoMes.mes);
    setAno(novoMes.ano);
  };

  const totalDays = calculateTotalDays();
  const totalHours = calculateTotalHours();

  return (
    <form
      onSubmit={(e) => {
        e.preventDefault();
        onSubmit();
      }}
    >
      <div className="space-y-6">
        {/* Período do Plantão em destaque */}
        <Card>
          <CardHeader>
            <CardTitle className="flex items-center gap-2">
              <Calendar className="h-5 w-5" />
              Período do Plantão
            </CardTitle>
            <CardDescription>Defina o período de vigência do plantão</CardDescription>
          </CardHeader>
          <CardContent className="space-y-4">
            <div className="grid grid-cols-2 gap-4">
              <div className="space-y-2">
                <Label htmlFor="dataInicial">Data Inicial *</Label>
                <DatePicker
                  value={form.dataInicial ? parseISO(form.dataInicial) : undefined}
                  onChange={(date) => {
                    setDataInicial(date ? date.toISOString().split("T")[0] : "");
                  }}
                  placeholder="Selecione a data inicial"
                  disabled={(date) => {
                    if (form.dataFinal) {
                      const dataFinal = parseISO(form.dataFinal);
                      return date > dataFinal;
                    }
                    return false;
                  }}
                />
                {!form.dataInicial && <p className="text-sm text-destructive">Data inicial é obrigatória</p>}
              </div>

              <div className="space-y-2">
                <Label htmlFor="dataFinal">Data Final *</Label>
                <DatePicker
                  value={form.dataFinal ? parseISO(form.dataFinal) : undefined}
                  onChange={(date) => {
                    setDataFinal(date!);
                  }}
                  placeholder="Selecione a data final"
                  disabled={(date) => {
                    if (form.dataInicial) {
                      const dataInicial = parseISO(form.dataInicial);
                      return date < dataInicial;
                    }
                    return false;
                  }}
                />
                {!form.dataFinal && <p className="text-sm text-destructive">Data final é obrigatória</p>}
              </div>
            </div>

            {/* Resumo do período */}
            {form.dataInicial && (
              <div className="mt-4 p-3 bg-muted rounded-lg">
                <div className="grid grid-cols-1 md:grid-cols-4 gap-2 text-sm">
                  <div>
                    <span className="font-medium">Início:</span> {formatDateStringUTC(form.dataInicial)}
                  </div>
                  <div>
                    <span className="font-medium">Fim:</span>{" "}
                    {form.dataFinal ? formatDateStringUTC(form.dataFinal) : "-"}
                  </div>
                  {totalDays && (
                    <div>
                      <span className="font-medium">Total de dias:</span>{" "}
                      <span className="text-primary font-bold">{totalDays} dias</span>
                    </div>
                  )}
                  {totalHours && (
                    <div>
                      <span className="font-medium">Total de horas:</span>{" "}
                      <span className="text-primary font-bold">{totalHours.toFixed(1)}h</span>
                      {(!form.horaInicio || !form.horaFim) && (
                        <span className="ml-1 text-xs text-muted-foreground">(estimado)</span>
                      )}
                    </div>
                  )}
                </div>
              </div>
            )}
          </CardContent>
        </Card>

        {/* Calendário de Horários */}
        <Card className={!form.dataFinal ? "opacity-50 pointer-events-none" : ""}>
          <CardHeader>
            <CardTitle className="flex items-center justify-between">
              <span>Calendário de Horários</span>
            </CardTitle>
            <CardDescription>Selecione os dias e horários de trabalho</CardDescription>
          </CardHeader>
          <CardContent>
            <CalendarioPlantao
              navigateMonth={navigateMonth}
              mes={form.mes}
              ano={form.ano}
              onMesChange={setMes}
              onAnoChange={setAno}
              diasPlantao={form.diasPlantao}
              onDiasChange={handleDiasChange}
              tipoTurno={form.tipoTurno}
              onTipoTurnoChange={setTipoTurno}
              dataInicial={form.dataInicial}
              dataFinal={form.dataFinal}
              onTodoPeriodoChange={setDiasSemanaTodoPeriodo}
            />
          </CardContent>
        </Card>

        <div className="grid gap-6 lg:grid-cols-2">
          <div className="space-y-6">
            <Card className={!form.dataFinal ? "opacity-50 pointer-events-none" : ""}>
              <CardHeader>
                <CardTitle>Informações Básicas</CardTitle>
                <CardDescription>Dados do profissional e local de atendimento</CardDescription>
              </CardHeader>
              <CardContent className="space-y-4">
                <div className="space-y-2">
                  <Label htmlFor="clienteId">Cliente *</Label>
                  <Select value={form.clienteId || undefined} onValueChange={(value) => handleClienteChange(value)}>
                    <SelectTrigger>
                      <SelectValue placeholder="Selecione o cliente" />
                    </SelectTrigger>
                    <SelectContent>
                      {clientes?.data.map((cliente) => (
                        <SelectItem key={cliente.uuid} value={cliente.uuid}>
                          {cliente.nome}
                        </SelectItem>
                      ))}
                    </SelectContent>
                  </Select>
                  {!form.clienteId && <p className="text-sm text-destructive">Cliente é obrigatório</p>}
                </div>

                <div className="space-y-2">
                  <Label htmlFor="localAtendimentoId">Local de Atendimento *</Label>
                  <Select
                    value={form.localAtendimentoId || undefined}
                    onValueChange={handleLocalChange}
                    disabled={!form.clienteId}
                  >
                    <SelectTrigger>
                      <SelectValue placeholder="Selecione o local" />
                    </SelectTrigger>
                    <SelectContent>
                      {locais?.data?.map((local) => (
                        <SelectItem key={local.uuid} value={local.uuid}>
                          {local.nome}
                          {local.especialidades && local.especialidades.length > 0 && (
                            <span className="text-xs text-muted-foreground ml-2">
                              ({local.especialidades.map((e) => e.especialidade.nome).join(", ")})
                            </span>
                          )}
                        </SelectItem>
                      ))}
                    </SelectContent>
                  </Select>
                  {!form.localAtendimentoId && <p className="text-sm text-destructive">Local é obrigatório</p>}
                </div>

                <div className="space-y-2">
                  <Label htmlFor="profissionalId">Profissional *</Label>
                  <Select
                    value={form.profissionalId || undefined}
                    onValueChange={(value) => setProfissionalId(value)}
                    disabled={!form.clienteId}
                  >
                    <SelectTrigger>
                      <SelectValue
                        placeholder={
                          !form.clienteId
                            ? "Selecione um cliente primeiro"
                            : profissionaisList?.length === 0
                              ? "Nenhum profissional disponível"
                              : "Selecione o profissional"
                        }
                      />
                    </SelectTrigger>
                    <SelectContent>
                      {profissionaisList?.map((prof) => (
                        <SelectItem key={prof.uuid} value={prof.uuid}>
                          {prof.usuario?.nome}
                          {prof.especialidades && prof.especialidades.length > 0 && (
                            <span className="text-xs text-muted-foreground ml-2">
                              ({prof.especialidades.map((e) => e.especialidade.nome).join(", ")})
                            </span>
                          )}
                        </SelectItem>
                      ))}
                    </SelectContent>
                  </Select>
                  {!form.profissionalId && <p className="text-sm text-destructive">Profissional é obrigatório</p>}
                  {form.clienteId && profissionaisList?.length === 0 && (
                    <p className="text-sm text-amber-600">
                      {form.localAtendimentoId
                        ? "Nenhum profissional com a especialidade deste local está vinculado ao cliente"
                        : "Nenhum profissional está vinculado a este cliente"}
                    </p>
                  )}
                </div>

                <div className="space-y-2">
                  <Label htmlFor="tipoFechamento">Tipo de Fechamento *</Label>
                  <Select value={form.tipoFechamento} onValueChange={setTipoFechamento}>
                    <SelectTrigger>
                      <SelectValue />
                    </SelectTrigger>
                    <SelectContent>
                      <SelectItem value="DIARIO">Diário</SelectItem>
                      <SelectItem value="SEMANAL">Semanal</SelectItem>
                      <SelectItem value="QUINZENAL">Quinzenal</SelectItem>
                      <SelectItem value="MENSAL">Mensal</SelectItem>
                    </SelectContent>
                  </Select>
                </div>

                <div className="space-y-2">
                  <Label htmlFor="modalidadeTrabalho">Modalidade de Trabalho *</Label>
                  <Select value={form.modalidadeTrabalho} onValueChange={setModalidadeTrabalho}>
                    <SelectTrigger>
                      <SelectValue />
                    </SelectTrigger>
                    <SelectContent>
                      <SelectItem value="PLANTONISTA">Plantonista</SelectItem>
                      <SelectItem value="MENSALISTA">Mensalista</SelectItem>
                      <SelectItem value="COORDENADOR">Coordenador</SelectItem>
                      <SelectItem value="SUPERVISOR">Supervisor</SelectItem>
                      <SelectItem value="DIRETOR">Diretor</SelectItem>
                    </SelectContent>
                  </Select>
                </div>
              </CardContent>
            </Card>
          </div>

          <Card className={!form.dataFinal ? "opacity-50 pointer-events-none" : ""}>
            <CardHeader>
              <CardTitle>Valores e Pagamento</CardTitle>
              <CardDescription>Configure os valores e forma de pagamento</CardDescription>
            </CardHeader>
            <CardContent className="space-y-4">
              {/* <div className="space-y-2">
                <Label htmlFor="tipoValor">Tipo de Valor *</Label>
                <Select value={form.tipoValor || "HORA"} onValueChange={setTipoValor}>
                  <SelectTrigger>
                    <SelectValue />
                  </SelectTrigger>
                  <SelectContent>
                    <SelectItem value="HORA">Por Hora</SelectItem>
                    <SelectItem value="DIARIA">Por Dia</SelectItem>
                    <SelectItem value="PLANTAO">Por Plantão</SelectItem>
                    <SelectItem value="MENSAL">Mensal</SelectItem>
                  </SelectContent>
                </Select>
              </div> */}

              <div className="space-y-2">
                <Label htmlFor="valorBase">
                  Valor Base{" "}
                  {form.tipoValor === "HORA"
                    ? "(por hora)"
                    : form.tipoValor === "DIARIA"
                      ? "(por dia)"
                      : form.tipoValor === "PLANTAO"
                        ? "(por plantão)"
                        : "(mensal)"}{" "}
                  *
                </Label>
                <Input
                  id="valorBase"
                  type="number"
                  step="0.01"
                  value={form.valorBase || ""}
                  onChange={(e) => setValorBase(e.target.value ? parseFloat(e.target.value) : undefined)}
                  placeholder="0.00"
                  required
                />
              </div>

              <div className="space-y-2">
                <Label htmlFor="prazoPagamentoDias">Prazo de Pagamento (dias)</Label>
                <Input
                  id="prazoPagamentoDias"
                  type="number"
                  min="1"
                  max="365"
                  value={form.prazoPagamentoDias || ""}
                  onChange={(e) => setPrazoPagamentoDias(e.target.value ? parseInt(e.target.value) : undefined)}
                  placeholder="Ex: 30, 45, 60"
                />
              </div>

              <div className="space-y-2">
                <Label htmlFor="observacoes">Observações</Label>
                <Textarea
                  id="observacoes"
                  value={form.observacoes}
                  onChange={(e) => setObservacoes(e.target.value)}
                  placeholder="Observações adicionais..."
                  rows={3}
                />
              </div>
            </CardContent>
          </Card>
        </div>
      </div>

      <div className="flex justify-end gap-4 mt-6">
        {onCancel && (
          <Button type="button" variant="outline" onClick={onCancel}>
            Cancelar
          </Button>
        )}
        <Button type="submit" disabled={isSubmitting}>
          {isSubmitting ? (
            <>
              <Loader2 className="mr-2 h-4 w-4 animate-spin" />
              Salvando...
            </>
          ) : (
            <>
              <Save className="mr-2 h-4 w-4" />
              {mode === "create" ? "Salvar Plantão" : "Salvar Alterações"}
            </>
          )}
        </Button>
      </div>
    </form>
  );
}
