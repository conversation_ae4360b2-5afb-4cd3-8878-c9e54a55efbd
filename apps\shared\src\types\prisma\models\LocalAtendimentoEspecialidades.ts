
/* !!! This is code generated by Prisma. Do not edit directly. !!! */
/* eslint-disable */
// @ts-nocheck 
/*
 * This file exports the `LocalAtendimentoEspecialidades` model and its related types.
 *
 * 🟢 You can import this file directly.
 */
import * as runtime from "@prisma/client/runtime/library"
import type * as $Enums from "../enums.ts"
import type * as Prisma from "../internal/prismaNamespace.ts"

/**
 * Model LocalAtendimentoEspecialidades
 * 
 */
export type LocalAtendimentoEspecialidadesModel = runtime.Types.Result.DefaultSelection<Prisma.$LocalAtendimentoEspecialidadesPayload>

export type AggregateLocalAtendimentoEspecialidades = {
  _count: LocalAtendimentoEspecialidadesCountAggregateOutputType | null
  _avg: LocalAtendimentoEspecialidadesAvgAggregateOutputType | null
  _sum: LocalAtendimentoEspecialidadesSumAggregateOutputType | null
  _min: LocalAtendimentoEspecialidadesMinAggregateOutputType | null
  _max: LocalAtendimentoEspecialidadesMaxAggregateOutputType | null
}

export type LocalAtendimentoEspecialidadesAvgAggregateOutputType = {
  id: number | null
  localAtendimentoId: number | null
  especialidadeId: number | null
}

export type LocalAtendimentoEspecialidadesSumAggregateOutputType = {
  id: number | null
  localAtendimentoId: number | null
  especialidadeId: number | null
}

export type LocalAtendimentoEspecialidadesMinAggregateOutputType = {
  id: number | null
  localAtendimentoId: number | null
  especialidadeId: number | null
  createdAt: Date | null
}

export type LocalAtendimentoEspecialidadesMaxAggregateOutputType = {
  id: number | null
  localAtendimentoId: number | null
  especialidadeId: number | null
  createdAt: Date | null
}

export type LocalAtendimentoEspecialidadesCountAggregateOutputType = {
  id: number
  localAtendimentoId: number
  especialidadeId: number
  createdAt: number
  _all: number
}


export type LocalAtendimentoEspecialidadesAvgAggregateInputType = {
  id?: true
  localAtendimentoId?: true
  especialidadeId?: true
}

export type LocalAtendimentoEspecialidadesSumAggregateInputType = {
  id?: true
  localAtendimentoId?: true
  especialidadeId?: true
}

export type LocalAtendimentoEspecialidadesMinAggregateInputType = {
  id?: true
  localAtendimentoId?: true
  especialidadeId?: true
  createdAt?: true
}

export type LocalAtendimentoEspecialidadesMaxAggregateInputType = {
  id?: true
  localAtendimentoId?: true
  especialidadeId?: true
  createdAt?: true
}

export type LocalAtendimentoEspecialidadesCountAggregateInputType = {
  id?: true
  localAtendimentoId?: true
  especialidadeId?: true
  createdAt?: true
  _all?: true
}

export type LocalAtendimentoEspecialidadesAggregateArgs<ExtArgs extends runtime.Types.Extensions.InternalArgs = runtime.Types.Extensions.DefaultArgs> = {
  /**
   * Filter which LocalAtendimentoEspecialidades to aggregate.
   */
  where?: Prisma.LocalAtendimentoEspecialidadesWhereInput
  /**
   * {@link https://www.prisma.io/docs/concepts/components/prisma-client/sorting Sorting Docs}
   * 
   * Determine the order of LocalAtendimentoEspecialidades to fetch.
   */
  orderBy?: Prisma.LocalAtendimentoEspecialidadesOrderByWithRelationInput | Prisma.LocalAtendimentoEspecialidadesOrderByWithRelationInput[]
  /**
   * {@link https://www.prisma.io/docs/concepts/components/prisma-client/pagination#cursor-based-pagination Cursor Docs}
   * 
   * Sets the start position
   */
  cursor?: Prisma.LocalAtendimentoEspecialidadesWhereUniqueInput
  /**
   * {@link https://www.prisma.io/docs/concepts/components/prisma-client/pagination Pagination Docs}
   * 
   * Take `±n` LocalAtendimentoEspecialidades from the position of the cursor.
   */
  take?: number
  /**
   * {@link https://www.prisma.io/docs/concepts/components/prisma-client/pagination Pagination Docs}
   * 
   * Skip the first `n` LocalAtendimentoEspecialidades.
   */
  skip?: number
  /**
   * {@link https://www.prisma.io/docs/concepts/components/prisma-client/aggregations Aggregation Docs}
   * 
   * Count returned LocalAtendimentoEspecialidades
  **/
  _count?: true | LocalAtendimentoEspecialidadesCountAggregateInputType
  /**
   * {@link https://www.prisma.io/docs/concepts/components/prisma-client/aggregations Aggregation Docs}
   * 
   * Select which fields to average
  **/
  _avg?: LocalAtendimentoEspecialidadesAvgAggregateInputType
  /**
   * {@link https://www.prisma.io/docs/concepts/components/prisma-client/aggregations Aggregation Docs}
   * 
   * Select which fields to sum
  **/
  _sum?: LocalAtendimentoEspecialidadesSumAggregateInputType
  /**
   * {@link https://www.prisma.io/docs/concepts/components/prisma-client/aggregations Aggregation Docs}
   * 
   * Select which fields to find the minimum value
  **/
  _min?: LocalAtendimentoEspecialidadesMinAggregateInputType
  /**
   * {@link https://www.prisma.io/docs/concepts/components/prisma-client/aggregations Aggregation Docs}
   * 
   * Select which fields to find the maximum value
  **/
  _max?: LocalAtendimentoEspecialidadesMaxAggregateInputType
}

export type GetLocalAtendimentoEspecialidadesAggregateType<T extends LocalAtendimentoEspecialidadesAggregateArgs> = {
      [P in keyof T & keyof AggregateLocalAtendimentoEspecialidades]: P extends '_count' | 'count'
    ? T[P] extends true
      ? number
      : Prisma.GetScalarType<T[P], AggregateLocalAtendimentoEspecialidades[P]>
    : Prisma.GetScalarType<T[P], AggregateLocalAtendimentoEspecialidades[P]>
}




export type LocalAtendimentoEspecialidadesGroupByArgs<ExtArgs extends runtime.Types.Extensions.InternalArgs = runtime.Types.Extensions.DefaultArgs> = {
  where?: Prisma.LocalAtendimentoEspecialidadesWhereInput
  orderBy?: Prisma.LocalAtendimentoEspecialidadesOrderByWithAggregationInput | Prisma.LocalAtendimentoEspecialidadesOrderByWithAggregationInput[]
  by: Prisma.LocalAtendimentoEspecialidadesScalarFieldEnum[] | Prisma.LocalAtendimentoEspecialidadesScalarFieldEnum
  having?: Prisma.LocalAtendimentoEspecialidadesScalarWhereWithAggregatesInput
  take?: number
  skip?: number
  _count?: LocalAtendimentoEspecialidadesCountAggregateInputType | true
  _avg?: LocalAtendimentoEspecialidadesAvgAggregateInputType
  _sum?: LocalAtendimentoEspecialidadesSumAggregateInputType
  _min?: LocalAtendimentoEspecialidadesMinAggregateInputType
  _max?: LocalAtendimentoEspecialidadesMaxAggregateInputType
}

export type LocalAtendimentoEspecialidadesGroupByOutputType = {
  id: number
  localAtendimentoId: number
  especialidadeId: number
  createdAt: Date
  _count: LocalAtendimentoEspecialidadesCountAggregateOutputType | null
  _avg: LocalAtendimentoEspecialidadesAvgAggregateOutputType | null
  _sum: LocalAtendimentoEspecialidadesSumAggregateOutputType | null
  _min: LocalAtendimentoEspecialidadesMinAggregateOutputType | null
  _max: LocalAtendimentoEspecialidadesMaxAggregateOutputType | null
}

type GetLocalAtendimentoEspecialidadesGroupByPayload<T extends LocalAtendimentoEspecialidadesGroupByArgs> = Prisma.PrismaPromise<
  Array<
    Prisma.PickEnumerable<LocalAtendimentoEspecialidadesGroupByOutputType, T['by']> &
      {
        [P in ((keyof T) & (keyof LocalAtendimentoEspecialidadesGroupByOutputType))]: P extends '_count'
          ? T[P] extends boolean
            ? number
            : Prisma.GetScalarType<T[P], LocalAtendimentoEspecialidadesGroupByOutputType[P]>
          : Prisma.GetScalarType<T[P], LocalAtendimentoEspecialidadesGroupByOutputType[P]>
      }
    >
  >



export type LocalAtendimentoEspecialidadesWhereInput = {
  AND?: Prisma.LocalAtendimentoEspecialidadesWhereInput | Prisma.LocalAtendimentoEspecialidadesWhereInput[]
  OR?: Prisma.LocalAtendimentoEspecialidadesWhereInput[]
  NOT?: Prisma.LocalAtendimentoEspecialidadesWhereInput | Prisma.LocalAtendimentoEspecialidadesWhereInput[]
  id?: Prisma.IntFilter<"LocalAtendimentoEspecialidades"> | number
  localAtendimentoId?: Prisma.IntFilter<"LocalAtendimentoEspecialidades"> | number
  especialidadeId?: Prisma.IntFilter<"LocalAtendimentoEspecialidades"> | number
  createdAt?: Prisma.DateTimeFilter<"LocalAtendimentoEspecialidades"> | Date | string
  localAtendimento?: Prisma.XOR<Prisma.LocalAtendimentoScalarRelationFilter, Prisma.LocalAtendimentoWhereInput>
  especialidade?: Prisma.XOR<Prisma.EspecialidadeScalarRelationFilter, Prisma.EspecialidadeWhereInput>
}

export type LocalAtendimentoEspecialidadesOrderByWithRelationInput = {
  id?: Prisma.SortOrder
  localAtendimentoId?: Prisma.SortOrder
  especialidadeId?: Prisma.SortOrder
  createdAt?: Prisma.SortOrder
  localAtendimento?: Prisma.LocalAtendimentoOrderByWithRelationInput
  especialidade?: Prisma.EspecialidadeOrderByWithRelationInput
}

export type LocalAtendimentoEspecialidadesWhereUniqueInput = Prisma.AtLeast<{
  id?: number
  localAtendimentoId_especialidadeId?: Prisma.LocalAtendimentoEspecialidadesLocalAtendimentoIdEspecialidadeIdCompoundUniqueInput
  AND?: Prisma.LocalAtendimentoEspecialidadesWhereInput | Prisma.LocalAtendimentoEspecialidadesWhereInput[]
  OR?: Prisma.LocalAtendimentoEspecialidadesWhereInput[]
  NOT?: Prisma.LocalAtendimentoEspecialidadesWhereInput | Prisma.LocalAtendimentoEspecialidadesWhereInput[]
  localAtendimentoId?: Prisma.IntFilter<"LocalAtendimentoEspecialidades"> | number
  especialidadeId?: Prisma.IntFilter<"LocalAtendimentoEspecialidades"> | number
  createdAt?: Prisma.DateTimeFilter<"LocalAtendimentoEspecialidades"> | Date | string
  localAtendimento?: Prisma.XOR<Prisma.LocalAtendimentoScalarRelationFilter, Prisma.LocalAtendimentoWhereInput>
  especialidade?: Prisma.XOR<Prisma.EspecialidadeScalarRelationFilter, Prisma.EspecialidadeWhereInput>
}, "id" | "localAtendimentoId_especialidadeId">

export type LocalAtendimentoEspecialidadesOrderByWithAggregationInput = {
  id?: Prisma.SortOrder
  localAtendimentoId?: Prisma.SortOrder
  especialidadeId?: Prisma.SortOrder
  createdAt?: Prisma.SortOrder
  _count?: Prisma.LocalAtendimentoEspecialidadesCountOrderByAggregateInput
  _avg?: Prisma.LocalAtendimentoEspecialidadesAvgOrderByAggregateInput
  _max?: Prisma.LocalAtendimentoEspecialidadesMaxOrderByAggregateInput
  _min?: Prisma.LocalAtendimentoEspecialidadesMinOrderByAggregateInput
  _sum?: Prisma.LocalAtendimentoEspecialidadesSumOrderByAggregateInput
}

export type LocalAtendimentoEspecialidadesScalarWhereWithAggregatesInput = {
  AND?: Prisma.LocalAtendimentoEspecialidadesScalarWhereWithAggregatesInput | Prisma.LocalAtendimentoEspecialidadesScalarWhereWithAggregatesInput[]
  OR?: Prisma.LocalAtendimentoEspecialidadesScalarWhereWithAggregatesInput[]
  NOT?: Prisma.LocalAtendimentoEspecialidadesScalarWhereWithAggregatesInput | Prisma.LocalAtendimentoEspecialidadesScalarWhereWithAggregatesInput[]
  id?: Prisma.IntWithAggregatesFilter<"LocalAtendimentoEspecialidades"> | number
  localAtendimentoId?: Prisma.IntWithAggregatesFilter<"LocalAtendimentoEspecialidades"> | number
  especialidadeId?: Prisma.IntWithAggregatesFilter<"LocalAtendimentoEspecialidades"> | number
  createdAt?: Prisma.DateTimeWithAggregatesFilter<"LocalAtendimentoEspecialidades"> | Date | string
}

export type LocalAtendimentoEspecialidadesCreateInput = {
  createdAt?: Date | string
  localAtendimento: Prisma.LocalAtendimentoCreateNestedOneWithoutEspecialidadesInput
  especialidade: Prisma.EspecialidadeCreateNestedOneWithoutLocaisInput
}

export type LocalAtendimentoEspecialidadesUncheckedCreateInput = {
  id?: number
  localAtendimentoId: number
  especialidadeId: number
  createdAt?: Date | string
}

export type LocalAtendimentoEspecialidadesUpdateInput = {
  createdAt?: Prisma.DateTimeFieldUpdateOperationsInput | Date | string
  localAtendimento?: Prisma.LocalAtendimentoUpdateOneRequiredWithoutEspecialidadesNestedInput
  especialidade?: Prisma.EspecialidadeUpdateOneRequiredWithoutLocaisNestedInput
}

export type LocalAtendimentoEspecialidadesUncheckedUpdateInput = {
  id?: Prisma.IntFieldUpdateOperationsInput | number
  localAtendimentoId?: Prisma.IntFieldUpdateOperationsInput | number
  especialidadeId?: Prisma.IntFieldUpdateOperationsInput | number
  createdAt?: Prisma.DateTimeFieldUpdateOperationsInput | Date | string
}

export type LocalAtendimentoEspecialidadesCreateManyInput = {
  id?: number
  localAtendimentoId: number
  especialidadeId: number
  createdAt?: Date | string
}

export type LocalAtendimentoEspecialidadesUpdateManyMutationInput = {
  createdAt?: Prisma.DateTimeFieldUpdateOperationsInput | Date | string
}

export type LocalAtendimentoEspecialidadesUncheckedUpdateManyInput = {
  id?: Prisma.IntFieldUpdateOperationsInput | number
  localAtendimentoId?: Prisma.IntFieldUpdateOperationsInput | number
  especialidadeId?: Prisma.IntFieldUpdateOperationsInput | number
  createdAt?: Prisma.DateTimeFieldUpdateOperationsInput | Date | string
}

export type LocalAtendimentoEspecialidadesListRelationFilter = {
  every?: Prisma.LocalAtendimentoEspecialidadesWhereInput
  some?: Prisma.LocalAtendimentoEspecialidadesWhereInput
  none?: Prisma.LocalAtendimentoEspecialidadesWhereInput
}

export type LocalAtendimentoEspecialidadesOrderByRelationAggregateInput = {
  _count?: Prisma.SortOrder
}

export type LocalAtendimentoEspecialidadesLocalAtendimentoIdEspecialidadeIdCompoundUniqueInput = {
  localAtendimentoId: number
  especialidadeId: number
}

export type LocalAtendimentoEspecialidadesCountOrderByAggregateInput = {
  id?: Prisma.SortOrder
  localAtendimentoId?: Prisma.SortOrder
  especialidadeId?: Prisma.SortOrder
  createdAt?: Prisma.SortOrder
}

export type LocalAtendimentoEspecialidadesAvgOrderByAggregateInput = {
  id?: Prisma.SortOrder
  localAtendimentoId?: Prisma.SortOrder
  especialidadeId?: Prisma.SortOrder
}

export type LocalAtendimentoEspecialidadesMaxOrderByAggregateInput = {
  id?: Prisma.SortOrder
  localAtendimentoId?: Prisma.SortOrder
  especialidadeId?: Prisma.SortOrder
  createdAt?: Prisma.SortOrder
}

export type LocalAtendimentoEspecialidadesMinOrderByAggregateInput = {
  id?: Prisma.SortOrder
  localAtendimentoId?: Prisma.SortOrder
  especialidadeId?: Prisma.SortOrder
  createdAt?: Prisma.SortOrder
}

export type LocalAtendimentoEspecialidadesSumOrderByAggregateInput = {
  id?: Prisma.SortOrder
  localAtendimentoId?: Prisma.SortOrder
  especialidadeId?: Prisma.SortOrder
}

export type LocalAtendimentoEspecialidadesCreateNestedManyWithoutEspecialidadeInput = {
  create?: Prisma.XOR<Prisma.LocalAtendimentoEspecialidadesCreateWithoutEspecialidadeInput, Prisma.LocalAtendimentoEspecialidadesUncheckedCreateWithoutEspecialidadeInput> | Prisma.LocalAtendimentoEspecialidadesCreateWithoutEspecialidadeInput[] | Prisma.LocalAtendimentoEspecialidadesUncheckedCreateWithoutEspecialidadeInput[]
  connectOrCreate?: Prisma.LocalAtendimentoEspecialidadesCreateOrConnectWithoutEspecialidadeInput | Prisma.LocalAtendimentoEspecialidadesCreateOrConnectWithoutEspecialidadeInput[]
  createMany?: Prisma.LocalAtendimentoEspecialidadesCreateManyEspecialidadeInputEnvelope
  connect?: Prisma.LocalAtendimentoEspecialidadesWhereUniqueInput | Prisma.LocalAtendimentoEspecialidadesWhereUniqueInput[]
}

export type LocalAtendimentoEspecialidadesUncheckedCreateNestedManyWithoutEspecialidadeInput = {
  create?: Prisma.XOR<Prisma.LocalAtendimentoEspecialidadesCreateWithoutEspecialidadeInput, Prisma.LocalAtendimentoEspecialidadesUncheckedCreateWithoutEspecialidadeInput> | Prisma.LocalAtendimentoEspecialidadesCreateWithoutEspecialidadeInput[] | Prisma.LocalAtendimentoEspecialidadesUncheckedCreateWithoutEspecialidadeInput[]
  connectOrCreate?: Prisma.LocalAtendimentoEspecialidadesCreateOrConnectWithoutEspecialidadeInput | Prisma.LocalAtendimentoEspecialidadesCreateOrConnectWithoutEspecialidadeInput[]
  createMany?: Prisma.LocalAtendimentoEspecialidadesCreateManyEspecialidadeInputEnvelope
  connect?: Prisma.LocalAtendimentoEspecialidadesWhereUniqueInput | Prisma.LocalAtendimentoEspecialidadesWhereUniqueInput[]
}

export type LocalAtendimentoEspecialidadesUpdateManyWithoutEspecialidadeNestedInput = {
  create?: Prisma.XOR<Prisma.LocalAtendimentoEspecialidadesCreateWithoutEspecialidadeInput, Prisma.LocalAtendimentoEspecialidadesUncheckedCreateWithoutEspecialidadeInput> | Prisma.LocalAtendimentoEspecialidadesCreateWithoutEspecialidadeInput[] | Prisma.LocalAtendimentoEspecialidadesUncheckedCreateWithoutEspecialidadeInput[]
  connectOrCreate?: Prisma.LocalAtendimentoEspecialidadesCreateOrConnectWithoutEspecialidadeInput | Prisma.LocalAtendimentoEspecialidadesCreateOrConnectWithoutEspecialidadeInput[]
  upsert?: Prisma.LocalAtendimentoEspecialidadesUpsertWithWhereUniqueWithoutEspecialidadeInput | Prisma.LocalAtendimentoEspecialidadesUpsertWithWhereUniqueWithoutEspecialidadeInput[]
  createMany?: Prisma.LocalAtendimentoEspecialidadesCreateManyEspecialidadeInputEnvelope
  set?: Prisma.LocalAtendimentoEspecialidadesWhereUniqueInput | Prisma.LocalAtendimentoEspecialidadesWhereUniqueInput[]
  disconnect?: Prisma.LocalAtendimentoEspecialidadesWhereUniqueInput | Prisma.LocalAtendimentoEspecialidadesWhereUniqueInput[]
  delete?: Prisma.LocalAtendimentoEspecialidadesWhereUniqueInput | Prisma.LocalAtendimentoEspecialidadesWhereUniqueInput[]
  connect?: Prisma.LocalAtendimentoEspecialidadesWhereUniqueInput | Prisma.LocalAtendimentoEspecialidadesWhereUniqueInput[]
  update?: Prisma.LocalAtendimentoEspecialidadesUpdateWithWhereUniqueWithoutEspecialidadeInput | Prisma.LocalAtendimentoEspecialidadesUpdateWithWhereUniqueWithoutEspecialidadeInput[]
  updateMany?: Prisma.LocalAtendimentoEspecialidadesUpdateManyWithWhereWithoutEspecialidadeInput | Prisma.LocalAtendimentoEspecialidadesUpdateManyWithWhereWithoutEspecialidadeInput[]
  deleteMany?: Prisma.LocalAtendimentoEspecialidadesScalarWhereInput | Prisma.LocalAtendimentoEspecialidadesScalarWhereInput[]
}

export type LocalAtendimentoEspecialidadesUncheckedUpdateManyWithoutEspecialidadeNestedInput = {
  create?: Prisma.XOR<Prisma.LocalAtendimentoEspecialidadesCreateWithoutEspecialidadeInput, Prisma.LocalAtendimentoEspecialidadesUncheckedCreateWithoutEspecialidadeInput> | Prisma.LocalAtendimentoEspecialidadesCreateWithoutEspecialidadeInput[] | Prisma.LocalAtendimentoEspecialidadesUncheckedCreateWithoutEspecialidadeInput[]
  connectOrCreate?: Prisma.LocalAtendimentoEspecialidadesCreateOrConnectWithoutEspecialidadeInput | Prisma.LocalAtendimentoEspecialidadesCreateOrConnectWithoutEspecialidadeInput[]
  upsert?: Prisma.LocalAtendimentoEspecialidadesUpsertWithWhereUniqueWithoutEspecialidadeInput | Prisma.LocalAtendimentoEspecialidadesUpsertWithWhereUniqueWithoutEspecialidadeInput[]
  createMany?: Prisma.LocalAtendimentoEspecialidadesCreateManyEspecialidadeInputEnvelope
  set?: Prisma.LocalAtendimentoEspecialidadesWhereUniqueInput | Prisma.LocalAtendimentoEspecialidadesWhereUniqueInput[]
  disconnect?: Prisma.LocalAtendimentoEspecialidadesWhereUniqueInput | Prisma.LocalAtendimentoEspecialidadesWhereUniqueInput[]
  delete?: Prisma.LocalAtendimentoEspecialidadesWhereUniqueInput | Prisma.LocalAtendimentoEspecialidadesWhereUniqueInput[]
  connect?: Prisma.LocalAtendimentoEspecialidadesWhereUniqueInput | Prisma.LocalAtendimentoEspecialidadesWhereUniqueInput[]
  update?: Prisma.LocalAtendimentoEspecialidadesUpdateWithWhereUniqueWithoutEspecialidadeInput | Prisma.LocalAtendimentoEspecialidadesUpdateWithWhereUniqueWithoutEspecialidadeInput[]
  updateMany?: Prisma.LocalAtendimentoEspecialidadesUpdateManyWithWhereWithoutEspecialidadeInput | Prisma.LocalAtendimentoEspecialidadesUpdateManyWithWhereWithoutEspecialidadeInput[]
  deleteMany?: Prisma.LocalAtendimentoEspecialidadesScalarWhereInput | Prisma.LocalAtendimentoEspecialidadesScalarWhereInput[]
}

export type LocalAtendimentoEspecialidadesCreateNestedManyWithoutLocalAtendimentoInput = {
  create?: Prisma.XOR<Prisma.LocalAtendimentoEspecialidadesCreateWithoutLocalAtendimentoInput, Prisma.LocalAtendimentoEspecialidadesUncheckedCreateWithoutLocalAtendimentoInput> | Prisma.LocalAtendimentoEspecialidadesCreateWithoutLocalAtendimentoInput[] | Prisma.LocalAtendimentoEspecialidadesUncheckedCreateWithoutLocalAtendimentoInput[]
  connectOrCreate?: Prisma.LocalAtendimentoEspecialidadesCreateOrConnectWithoutLocalAtendimentoInput | Prisma.LocalAtendimentoEspecialidadesCreateOrConnectWithoutLocalAtendimentoInput[]
  createMany?: Prisma.LocalAtendimentoEspecialidadesCreateManyLocalAtendimentoInputEnvelope
  connect?: Prisma.LocalAtendimentoEspecialidadesWhereUniqueInput | Prisma.LocalAtendimentoEspecialidadesWhereUniqueInput[]
}

export type LocalAtendimentoEspecialidadesUncheckedCreateNestedManyWithoutLocalAtendimentoInput = {
  create?: Prisma.XOR<Prisma.LocalAtendimentoEspecialidadesCreateWithoutLocalAtendimentoInput, Prisma.LocalAtendimentoEspecialidadesUncheckedCreateWithoutLocalAtendimentoInput> | Prisma.LocalAtendimentoEspecialidadesCreateWithoutLocalAtendimentoInput[] | Prisma.LocalAtendimentoEspecialidadesUncheckedCreateWithoutLocalAtendimentoInput[]
  connectOrCreate?: Prisma.LocalAtendimentoEspecialidadesCreateOrConnectWithoutLocalAtendimentoInput | Prisma.LocalAtendimentoEspecialidadesCreateOrConnectWithoutLocalAtendimentoInput[]
  createMany?: Prisma.LocalAtendimentoEspecialidadesCreateManyLocalAtendimentoInputEnvelope
  connect?: Prisma.LocalAtendimentoEspecialidadesWhereUniqueInput | Prisma.LocalAtendimentoEspecialidadesWhereUniqueInput[]
}

export type LocalAtendimentoEspecialidadesUpdateManyWithoutLocalAtendimentoNestedInput = {
  create?: Prisma.XOR<Prisma.LocalAtendimentoEspecialidadesCreateWithoutLocalAtendimentoInput, Prisma.LocalAtendimentoEspecialidadesUncheckedCreateWithoutLocalAtendimentoInput> | Prisma.LocalAtendimentoEspecialidadesCreateWithoutLocalAtendimentoInput[] | Prisma.LocalAtendimentoEspecialidadesUncheckedCreateWithoutLocalAtendimentoInput[]
  connectOrCreate?: Prisma.LocalAtendimentoEspecialidadesCreateOrConnectWithoutLocalAtendimentoInput | Prisma.LocalAtendimentoEspecialidadesCreateOrConnectWithoutLocalAtendimentoInput[]
  upsert?: Prisma.LocalAtendimentoEspecialidadesUpsertWithWhereUniqueWithoutLocalAtendimentoInput | Prisma.LocalAtendimentoEspecialidadesUpsertWithWhereUniqueWithoutLocalAtendimentoInput[]
  createMany?: Prisma.LocalAtendimentoEspecialidadesCreateManyLocalAtendimentoInputEnvelope
  set?: Prisma.LocalAtendimentoEspecialidadesWhereUniqueInput | Prisma.LocalAtendimentoEspecialidadesWhereUniqueInput[]
  disconnect?: Prisma.LocalAtendimentoEspecialidadesWhereUniqueInput | Prisma.LocalAtendimentoEspecialidadesWhereUniqueInput[]
  delete?: Prisma.LocalAtendimentoEspecialidadesWhereUniqueInput | Prisma.LocalAtendimentoEspecialidadesWhereUniqueInput[]
  connect?: Prisma.LocalAtendimentoEspecialidadesWhereUniqueInput | Prisma.LocalAtendimentoEspecialidadesWhereUniqueInput[]
  update?: Prisma.LocalAtendimentoEspecialidadesUpdateWithWhereUniqueWithoutLocalAtendimentoInput | Prisma.LocalAtendimentoEspecialidadesUpdateWithWhereUniqueWithoutLocalAtendimentoInput[]
  updateMany?: Prisma.LocalAtendimentoEspecialidadesUpdateManyWithWhereWithoutLocalAtendimentoInput | Prisma.LocalAtendimentoEspecialidadesUpdateManyWithWhereWithoutLocalAtendimentoInput[]
  deleteMany?: Prisma.LocalAtendimentoEspecialidadesScalarWhereInput | Prisma.LocalAtendimentoEspecialidadesScalarWhereInput[]
}

export type LocalAtendimentoEspecialidadesUncheckedUpdateManyWithoutLocalAtendimentoNestedInput = {
  create?: Prisma.XOR<Prisma.LocalAtendimentoEspecialidadesCreateWithoutLocalAtendimentoInput, Prisma.LocalAtendimentoEspecialidadesUncheckedCreateWithoutLocalAtendimentoInput> | Prisma.LocalAtendimentoEspecialidadesCreateWithoutLocalAtendimentoInput[] | Prisma.LocalAtendimentoEspecialidadesUncheckedCreateWithoutLocalAtendimentoInput[]
  connectOrCreate?: Prisma.LocalAtendimentoEspecialidadesCreateOrConnectWithoutLocalAtendimentoInput | Prisma.LocalAtendimentoEspecialidadesCreateOrConnectWithoutLocalAtendimentoInput[]
  upsert?: Prisma.LocalAtendimentoEspecialidadesUpsertWithWhereUniqueWithoutLocalAtendimentoInput | Prisma.LocalAtendimentoEspecialidadesUpsertWithWhereUniqueWithoutLocalAtendimentoInput[]
  createMany?: Prisma.LocalAtendimentoEspecialidadesCreateManyLocalAtendimentoInputEnvelope
  set?: Prisma.LocalAtendimentoEspecialidadesWhereUniqueInput | Prisma.LocalAtendimentoEspecialidadesWhereUniqueInput[]
  disconnect?: Prisma.LocalAtendimentoEspecialidadesWhereUniqueInput | Prisma.LocalAtendimentoEspecialidadesWhereUniqueInput[]
  delete?: Prisma.LocalAtendimentoEspecialidadesWhereUniqueInput | Prisma.LocalAtendimentoEspecialidadesWhereUniqueInput[]
  connect?: Prisma.LocalAtendimentoEspecialidadesWhereUniqueInput | Prisma.LocalAtendimentoEspecialidadesWhereUniqueInput[]
  update?: Prisma.LocalAtendimentoEspecialidadesUpdateWithWhereUniqueWithoutLocalAtendimentoInput | Prisma.LocalAtendimentoEspecialidadesUpdateWithWhereUniqueWithoutLocalAtendimentoInput[]
  updateMany?: Prisma.LocalAtendimentoEspecialidadesUpdateManyWithWhereWithoutLocalAtendimentoInput | Prisma.LocalAtendimentoEspecialidadesUpdateManyWithWhereWithoutLocalAtendimentoInput[]
  deleteMany?: Prisma.LocalAtendimentoEspecialidadesScalarWhereInput | Prisma.LocalAtendimentoEspecialidadesScalarWhereInput[]
}

export type LocalAtendimentoEspecialidadesCreateWithoutEspecialidadeInput = {
  createdAt?: Date | string
  localAtendimento: Prisma.LocalAtendimentoCreateNestedOneWithoutEspecialidadesInput
}

export type LocalAtendimentoEspecialidadesUncheckedCreateWithoutEspecialidadeInput = {
  id?: number
  localAtendimentoId: number
  createdAt?: Date | string
}

export type LocalAtendimentoEspecialidadesCreateOrConnectWithoutEspecialidadeInput = {
  where: Prisma.LocalAtendimentoEspecialidadesWhereUniqueInput
  create: Prisma.XOR<Prisma.LocalAtendimentoEspecialidadesCreateWithoutEspecialidadeInput, Prisma.LocalAtendimentoEspecialidadesUncheckedCreateWithoutEspecialidadeInput>
}

export type LocalAtendimentoEspecialidadesCreateManyEspecialidadeInputEnvelope = {
  data: Prisma.LocalAtendimentoEspecialidadesCreateManyEspecialidadeInput | Prisma.LocalAtendimentoEspecialidadesCreateManyEspecialidadeInput[]
  skipDuplicates?: boolean
}

export type LocalAtendimentoEspecialidadesUpsertWithWhereUniqueWithoutEspecialidadeInput = {
  where: Prisma.LocalAtendimentoEspecialidadesWhereUniqueInput
  update: Prisma.XOR<Prisma.LocalAtendimentoEspecialidadesUpdateWithoutEspecialidadeInput, Prisma.LocalAtendimentoEspecialidadesUncheckedUpdateWithoutEspecialidadeInput>
  create: Prisma.XOR<Prisma.LocalAtendimentoEspecialidadesCreateWithoutEspecialidadeInput, Prisma.LocalAtendimentoEspecialidadesUncheckedCreateWithoutEspecialidadeInput>
}

export type LocalAtendimentoEspecialidadesUpdateWithWhereUniqueWithoutEspecialidadeInput = {
  where: Prisma.LocalAtendimentoEspecialidadesWhereUniqueInput
  data: Prisma.XOR<Prisma.LocalAtendimentoEspecialidadesUpdateWithoutEspecialidadeInput, Prisma.LocalAtendimentoEspecialidadesUncheckedUpdateWithoutEspecialidadeInput>
}

export type LocalAtendimentoEspecialidadesUpdateManyWithWhereWithoutEspecialidadeInput = {
  where: Prisma.LocalAtendimentoEspecialidadesScalarWhereInput
  data: Prisma.XOR<Prisma.LocalAtendimentoEspecialidadesUpdateManyMutationInput, Prisma.LocalAtendimentoEspecialidadesUncheckedUpdateManyWithoutEspecialidadeInput>
}

export type LocalAtendimentoEspecialidadesScalarWhereInput = {
  AND?: Prisma.LocalAtendimentoEspecialidadesScalarWhereInput | Prisma.LocalAtendimentoEspecialidadesScalarWhereInput[]
  OR?: Prisma.LocalAtendimentoEspecialidadesScalarWhereInput[]
  NOT?: Prisma.LocalAtendimentoEspecialidadesScalarWhereInput | Prisma.LocalAtendimentoEspecialidadesScalarWhereInput[]
  id?: Prisma.IntFilter<"LocalAtendimentoEspecialidades"> | number
  localAtendimentoId?: Prisma.IntFilter<"LocalAtendimentoEspecialidades"> | number
  especialidadeId?: Prisma.IntFilter<"LocalAtendimentoEspecialidades"> | number
  createdAt?: Prisma.DateTimeFilter<"LocalAtendimentoEspecialidades"> | Date | string
}

export type LocalAtendimentoEspecialidadesCreateWithoutLocalAtendimentoInput = {
  createdAt?: Date | string
  especialidade: Prisma.EspecialidadeCreateNestedOneWithoutLocaisInput
}

export type LocalAtendimentoEspecialidadesUncheckedCreateWithoutLocalAtendimentoInput = {
  id?: number
  especialidadeId: number
  createdAt?: Date | string
}

export type LocalAtendimentoEspecialidadesCreateOrConnectWithoutLocalAtendimentoInput = {
  where: Prisma.LocalAtendimentoEspecialidadesWhereUniqueInput
  create: Prisma.XOR<Prisma.LocalAtendimentoEspecialidadesCreateWithoutLocalAtendimentoInput, Prisma.LocalAtendimentoEspecialidadesUncheckedCreateWithoutLocalAtendimentoInput>
}

export type LocalAtendimentoEspecialidadesCreateManyLocalAtendimentoInputEnvelope = {
  data: Prisma.LocalAtendimentoEspecialidadesCreateManyLocalAtendimentoInput | Prisma.LocalAtendimentoEspecialidadesCreateManyLocalAtendimentoInput[]
  skipDuplicates?: boolean
}

export type LocalAtendimentoEspecialidadesUpsertWithWhereUniqueWithoutLocalAtendimentoInput = {
  where: Prisma.LocalAtendimentoEspecialidadesWhereUniqueInput
  update: Prisma.XOR<Prisma.LocalAtendimentoEspecialidadesUpdateWithoutLocalAtendimentoInput, Prisma.LocalAtendimentoEspecialidadesUncheckedUpdateWithoutLocalAtendimentoInput>
  create: Prisma.XOR<Prisma.LocalAtendimentoEspecialidadesCreateWithoutLocalAtendimentoInput, Prisma.LocalAtendimentoEspecialidadesUncheckedCreateWithoutLocalAtendimentoInput>
}

export type LocalAtendimentoEspecialidadesUpdateWithWhereUniqueWithoutLocalAtendimentoInput = {
  where: Prisma.LocalAtendimentoEspecialidadesWhereUniqueInput
  data: Prisma.XOR<Prisma.LocalAtendimentoEspecialidadesUpdateWithoutLocalAtendimentoInput, Prisma.LocalAtendimentoEspecialidadesUncheckedUpdateWithoutLocalAtendimentoInput>
}

export type LocalAtendimentoEspecialidadesUpdateManyWithWhereWithoutLocalAtendimentoInput = {
  where: Prisma.LocalAtendimentoEspecialidadesScalarWhereInput
  data: Prisma.XOR<Prisma.LocalAtendimentoEspecialidadesUpdateManyMutationInput, Prisma.LocalAtendimentoEspecialidadesUncheckedUpdateManyWithoutLocalAtendimentoInput>
}

export type LocalAtendimentoEspecialidadesCreateManyEspecialidadeInput = {
  id?: number
  localAtendimentoId: number
  createdAt?: Date | string
}

export type LocalAtendimentoEspecialidadesUpdateWithoutEspecialidadeInput = {
  createdAt?: Prisma.DateTimeFieldUpdateOperationsInput | Date | string
  localAtendimento?: Prisma.LocalAtendimentoUpdateOneRequiredWithoutEspecialidadesNestedInput
}

export type LocalAtendimentoEspecialidadesUncheckedUpdateWithoutEspecialidadeInput = {
  id?: Prisma.IntFieldUpdateOperationsInput | number
  localAtendimentoId?: Prisma.IntFieldUpdateOperationsInput | number
  createdAt?: Prisma.DateTimeFieldUpdateOperationsInput | Date | string
}

export type LocalAtendimentoEspecialidadesUncheckedUpdateManyWithoutEspecialidadeInput = {
  id?: Prisma.IntFieldUpdateOperationsInput | number
  localAtendimentoId?: Prisma.IntFieldUpdateOperationsInput | number
  createdAt?: Prisma.DateTimeFieldUpdateOperationsInput | Date | string
}

export type LocalAtendimentoEspecialidadesCreateManyLocalAtendimentoInput = {
  id?: number
  especialidadeId: number
  createdAt?: Date | string
}

export type LocalAtendimentoEspecialidadesUpdateWithoutLocalAtendimentoInput = {
  createdAt?: Prisma.DateTimeFieldUpdateOperationsInput | Date | string
  especialidade?: Prisma.EspecialidadeUpdateOneRequiredWithoutLocaisNestedInput
}

export type LocalAtendimentoEspecialidadesUncheckedUpdateWithoutLocalAtendimentoInput = {
  id?: Prisma.IntFieldUpdateOperationsInput | number
  especialidadeId?: Prisma.IntFieldUpdateOperationsInput | number
  createdAt?: Prisma.DateTimeFieldUpdateOperationsInput | Date | string
}

export type LocalAtendimentoEspecialidadesUncheckedUpdateManyWithoutLocalAtendimentoInput = {
  id?: Prisma.IntFieldUpdateOperationsInput | number
  especialidadeId?: Prisma.IntFieldUpdateOperationsInput | number
  createdAt?: Prisma.DateTimeFieldUpdateOperationsInput | Date | string
}



export type LocalAtendimentoEspecialidadesSelect<ExtArgs extends runtime.Types.Extensions.InternalArgs = runtime.Types.Extensions.DefaultArgs> = runtime.Types.Extensions.GetSelect<{
  id?: boolean
  localAtendimentoId?: boolean
  especialidadeId?: boolean
  createdAt?: boolean
  localAtendimento?: boolean | Prisma.LocalAtendimentoDefaultArgs<ExtArgs>
  especialidade?: boolean | Prisma.EspecialidadeDefaultArgs<ExtArgs>
}, ExtArgs["result"]["localAtendimentoEspecialidades"]>



export type LocalAtendimentoEspecialidadesSelectScalar = {
  id?: boolean
  localAtendimentoId?: boolean
  especialidadeId?: boolean
  createdAt?: boolean
}

export type LocalAtendimentoEspecialidadesOmit<ExtArgs extends runtime.Types.Extensions.InternalArgs = runtime.Types.Extensions.DefaultArgs> = runtime.Types.Extensions.GetOmit<"id" | "localAtendimentoId" | "especialidadeId" | "createdAt", ExtArgs["result"]["localAtendimentoEspecialidades"]>
export type LocalAtendimentoEspecialidadesInclude<ExtArgs extends runtime.Types.Extensions.InternalArgs = runtime.Types.Extensions.DefaultArgs> = {
  localAtendimento?: boolean | Prisma.LocalAtendimentoDefaultArgs<ExtArgs>
  especialidade?: boolean | Prisma.EspecialidadeDefaultArgs<ExtArgs>
}

export type $LocalAtendimentoEspecialidadesPayload<ExtArgs extends runtime.Types.Extensions.InternalArgs = runtime.Types.Extensions.DefaultArgs> = {
  name: "LocalAtendimentoEspecialidades"
  objects: {
    localAtendimento: Prisma.$LocalAtendimentoPayload<ExtArgs>
    especialidade: Prisma.$EspecialidadePayload<ExtArgs>
  }
  scalars: runtime.Types.Extensions.GetPayloadResult<{
    id: number
    localAtendimentoId: number
    especialidadeId: number
    createdAt: Date
  }, ExtArgs["result"]["localAtendimentoEspecialidades"]>
  composites: {}
}

export type LocalAtendimentoEspecialidadesGetPayload<S extends boolean | null | undefined | LocalAtendimentoEspecialidadesDefaultArgs> = runtime.Types.Result.GetResult<Prisma.$LocalAtendimentoEspecialidadesPayload, S>

export type LocalAtendimentoEspecialidadesCountArgs<ExtArgs extends runtime.Types.Extensions.InternalArgs = runtime.Types.Extensions.DefaultArgs> =
  Omit<LocalAtendimentoEspecialidadesFindManyArgs, 'select' | 'include' | 'distinct' | 'omit'> & {
    select?: LocalAtendimentoEspecialidadesCountAggregateInputType | true
  }

export interface LocalAtendimentoEspecialidadesDelegate<ExtArgs extends runtime.Types.Extensions.InternalArgs = runtime.Types.Extensions.DefaultArgs, GlobalOmitOptions = {}> {
  [K: symbol]: { types: Prisma.TypeMap<ExtArgs>['model']['LocalAtendimentoEspecialidades'], meta: { name: 'LocalAtendimentoEspecialidades' } }
  /**
   * Find zero or one LocalAtendimentoEspecialidades that matches the filter.
   * @param {LocalAtendimentoEspecialidadesFindUniqueArgs} args - Arguments to find a LocalAtendimentoEspecialidades
   * @example
   * // Get one LocalAtendimentoEspecialidades
   * const localAtendimentoEspecialidades = await prisma.localAtendimentoEspecialidades.findUnique({
   *   where: {
   *     // ... provide filter here
   *   }
   * })
   */
  findUnique<T extends LocalAtendimentoEspecialidadesFindUniqueArgs>(args: Prisma.SelectSubset<T, LocalAtendimentoEspecialidadesFindUniqueArgs<ExtArgs>>): Prisma.Prisma__LocalAtendimentoEspecialidadesClient<runtime.Types.Result.GetResult<Prisma.$LocalAtendimentoEspecialidadesPayload<ExtArgs>, T, "findUnique", GlobalOmitOptions> | null, null, ExtArgs, GlobalOmitOptions>

  /**
   * Find one LocalAtendimentoEspecialidades that matches the filter or throw an error with `error.code='P2025'`
   * if no matches were found.
   * @param {LocalAtendimentoEspecialidadesFindUniqueOrThrowArgs} args - Arguments to find a LocalAtendimentoEspecialidades
   * @example
   * // Get one LocalAtendimentoEspecialidades
   * const localAtendimentoEspecialidades = await prisma.localAtendimentoEspecialidades.findUniqueOrThrow({
   *   where: {
   *     // ... provide filter here
   *   }
   * })
   */
  findUniqueOrThrow<T extends LocalAtendimentoEspecialidadesFindUniqueOrThrowArgs>(args: Prisma.SelectSubset<T, LocalAtendimentoEspecialidadesFindUniqueOrThrowArgs<ExtArgs>>): Prisma.Prisma__LocalAtendimentoEspecialidadesClient<runtime.Types.Result.GetResult<Prisma.$LocalAtendimentoEspecialidadesPayload<ExtArgs>, T, "findUniqueOrThrow", GlobalOmitOptions>, never, ExtArgs, GlobalOmitOptions>

  /**
   * Find the first LocalAtendimentoEspecialidades that matches the filter.
   * Note, that providing `undefined` is treated as the value not being there.
   * Read more here: https://pris.ly/d/null-undefined
   * @param {LocalAtendimentoEspecialidadesFindFirstArgs} args - Arguments to find a LocalAtendimentoEspecialidades
   * @example
   * // Get one LocalAtendimentoEspecialidades
   * const localAtendimentoEspecialidades = await prisma.localAtendimentoEspecialidades.findFirst({
   *   where: {
   *     // ... provide filter here
   *   }
   * })
   */
  findFirst<T extends LocalAtendimentoEspecialidadesFindFirstArgs>(args?: Prisma.SelectSubset<T, LocalAtendimentoEspecialidadesFindFirstArgs<ExtArgs>>): Prisma.Prisma__LocalAtendimentoEspecialidadesClient<runtime.Types.Result.GetResult<Prisma.$LocalAtendimentoEspecialidadesPayload<ExtArgs>, T, "findFirst", GlobalOmitOptions> | null, null, ExtArgs, GlobalOmitOptions>

  /**
   * Find the first LocalAtendimentoEspecialidades that matches the filter or
   * throw `PrismaKnownClientError` with `P2025` code if no matches were found.
   * Note, that providing `undefined` is treated as the value not being there.
   * Read more here: https://pris.ly/d/null-undefined
   * @param {LocalAtendimentoEspecialidadesFindFirstOrThrowArgs} args - Arguments to find a LocalAtendimentoEspecialidades
   * @example
   * // Get one LocalAtendimentoEspecialidades
   * const localAtendimentoEspecialidades = await prisma.localAtendimentoEspecialidades.findFirstOrThrow({
   *   where: {
   *     // ... provide filter here
   *   }
   * })
   */
  findFirstOrThrow<T extends LocalAtendimentoEspecialidadesFindFirstOrThrowArgs>(args?: Prisma.SelectSubset<T, LocalAtendimentoEspecialidadesFindFirstOrThrowArgs<ExtArgs>>): Prisma.Prisma__LocalAtendimentoEspecialidadesClient<runtime.Types.Result.GetResult<Prisma.$LocalAtendimentoEspecialidadesPayload<ExtArgs>, T, "findFirstOrThrow", GlobalOmitOptions>, never, ExtArgs, GlobalOmitOptions>

  /**
   * Find zero or more LocalAtendimentoEspecialidades that matches the filter.
   * Note, that providing `undefined` is treated as the value not being there.
   * Read more here: https://pris.ly/d/null-undefined
   * @param {LocalAtendimentoEspecialidadesFindManyArgs} args - Arguments to filter and select certain fields only.
   * @example
   * // Get all LocalAtendimentoEspecialidades
   * const localAtendimentoEspecialidades = await prisma.localAtendimentoEspecialidades.findMany()
   * 
   * // Get first 10 LocalAtendimentoEspecialidades
   * const localAtendimentoEspecialidades = await prisma.localAtendimentoEspecialidades.findMany({ take: 10 })
   * 
   * // Only select the `id`
   * const localAtendimentoEspecialidadesWithIdOnly = await prisma.localAtendimentoEspecialidades.findMany({ select: { id: true } })
   * 
   */
  findMany<T extends LocalAtendimentoEspecialidadesFindManyArgs>(args?: Prisma.SelectSubset<T, LocalAtendimentoEspecialidadesFindManyArgs<ExtArgs>>): Prisma.PrismaPromise<runtime.Types.Result.GetResult<Prisma.$LocalAtendimentoEspecialidadesPayload<ExtArgs>, T, "findMany", GlobalOmitOptions>>

  /**
   * Create a LocalAtendimentoEspecialidades.
   * @param {LocalAtendimentoEspecialidadesCreateArgs} args - Arguments to create a LocalAtendimentoEspecialidades.
   * @example
   * // Create one LocalAtendimentoEspecialidades
   * const LocalAtendimentoEspecialidades = await prisma.localAtendimentoEspecialidades.create({
   *   data: {
   *     // ... data to create a LocalAtendimentoEspecialidades
   *   }
   * })
   * 
   */
  create<T extends LocalAtendimentoEspecialidadesCreateArgs>(args: Prisma.SelectSubset<T, LocalAtendimentoEspecialidadesCreateArgs<ExtArgs>>): Prisma.Prisma__LocalAtendimentoEspecialidadesClient<runtime.Types.Result.GetResult<Prisma.$LocalAtendimentoEspecialidadesPayload<ExtArgs>, T, "create", GlobalOmitOptions>, never, ExtArgs, GlobalOmitOptions>

  /**
   * Create many LocalAtendimentoEspecialidades.
   * @param {LocalAtendimentoEspecialidadesCreateManyArgs} args - Arguments to create many LocalAtendimentoEspecialidades.
   * @example
   * // Create many LocalAtendimentoEspecialidades
   * const localAtendimentoEspecialidades = await prisma.localAtendimentoEspecialidades.createMany({
   *   data: [
   *     // ... provide data here
   *   ]
   * })
   *     
   */
  createMany<T extends LocalAtendimentoEspecialidadesCreateManyArgs>(args?: Prisma.SelectSubset<T, LocalAtendimentoEspecialidadesCreateManyArgs<ExtArgs>>): Prisma.PrismaPromise<Prisma.BatchPayload>

  /**
   * Delete a LocalAtendimentoEspecialidades.
   * @param {LocalAtendimentoEspecialidadesDeleteArgs} args - Arguments to delete one LocalAtendimentoEspecialidades.
   * @example
   * // Delete one LocalAtendimentoEspecialidades
   * const LocalAtendimentoEspecialidades = await prisma.localAtendimentoEspecialidades.delete({
   *   where: {
   *     // ... filter to delete one LocalAtendimentoEspecialidades
   *   }
   * })
   * 
   */
  delete<T extends LocalAtendimentoEspecialidadesDeleteArgs>(args: Prisma.SelectSubset<T, LocalAtendimentoEspecialidadesDeleteArgs<ExtArgs>>): Prisma.Prisma__LocalAtendimentoEspecialidadesClient<runtime.Types.Result.GetResult<Prisma.$LocalAtendimentoEspecialidadesPayload<ExtArgs>, T, "delete", GlobalOmitOptions>, never, ExtArgs, GlobalOmitOptions>

  /**
   * Update one LocalAtendimentoEspecialidades.
   * @param {LocalAtendimentoEspecialidadesUpdateArgs} args - Arguments to update one LocalAtendimentoEspecialidades.
   * @example
   * // Update one LocalAtendimentoEspecialidades
   * const localAtendimentoEspecialidades = await prisma.localAtendimentoEspecialidades.update({
   *   where: {
   *     // ... provide filter here
   *   },
   *   data: {
   *     // ... provide data here
   *   }
   * })
   * 
   */
  update<T extends LocalAtendimentoEspecialidadesUpdateArgs>(args: Prisma.SelectSubset<T, LocalAtendimentoEspecialidadesUpdateArgs<ExtArgs>>): Prisma.Prisma__LocalAtendimentoEspecialidadesClient<runtime.Types.Result.GetResult<Prisma.$LocalAtendimentoEspecialidadesPayload<ExtArgs>, T, "update", GlobalOmitOptions>, never, ExtArgs, GlobalOmitOptions>

  /**
   * Delete zero or more LocalAtendimentoEspecialidades.
   * @param {LocalAtendimentoEspecialidadesDeleteManyArgs} args - Arguments to filter LocalAtendimentoEspecialidades to delete.
   * @example
   * // Delete a few LocalAtendimentoEspecialidades
   * const { count } = await prisma.localAtendimentoEspecialidades.deleteMany({
   *   where: {
   *     // ... provide filter here
   *   }
   * })
   * 
   */
  deleteMany<T extends LocalAtendimentoEspecialidadesDeleteManyArgs>(args?: Prisma.SelectSubset<T, LocalAtendimentoEspecialidadesDeleteManyArgs<ExtArgs>>): Prisma.PrismaPromise<Prisma.BatchPayload>

  /**
   * Update zero or more LocalAtendimentoEspecialidades.
   * Note, that providing `undefined` is treated as the value not being there.
   * Read more here: https://pris.ly/d/null-undefined
   * @param {LocalAtendimentoEspecialidadesUpdateManyArgs} args - Arguments to update one or more rows.
   * @example
   * // Update many LocalAtendimentoEspecialidades
   * const localAtendimentoEspecialidades = await prisma.localAtendimentoEspecialidades.updateMany({
   *   where: {
   *     // ... provide filter here
   *   },
   *   data: {
   *     // ... provide data here
   *   }
   * })
   * 
   */
  updateMany<T extends LocalAtendimentoEspecialidadesUpdateManyArgs>(args: Prisma.SelectSubset<T, LocalAtendimentoEspecialidadesUpdateManyArgs<ExtArgs>>): Prisma.PrismaPromise<Prisma.BatchPayload>

  /**
   * Create or update one LocalAtendimentoEspecialidades.
   * @param {LocalAtendimentoEspecialidadesUpsertArgs} args - Arguments to update or create a LocalAtendimentoEspecialidades.
   * @example
   * // Update or create a LocalAtendimentoEspecialidades
   * const localAtendimentoEspecialidades = await prisma.localAtendimentoEspecialidades.upsert({
   *   create: {
   *     // ... data to create a LocalAtendimentoEspecialidades
   *   },
   *   update: {
   *     // ... in case it already exists, update
   *   },
   *   where: {
   *     // ... the filter for the LocalAtendimentoEspecialidades we want to update
   *   }
   * })
   */
  upsert<T extends LocalAtendimentoEspecialidadesUpsertArgs>(args: Prisma.SelectSubset<T, LocalAtendimentoEspecialidadesUpsertArgs<ExtArgs>>): Prisma.Prisma__LocalAtendimentoEspecialidadesClient<runtime.Types.Result.GetResult<Prisma.$LocalAtendimentoEspecialidadesPayload<ExtArgs>, T, "upsert", GlobalOmitOptions>, never, ExtArgs, GlobalOmitOptions>


  /**
   * Count the number of LocalAtendimentoEspecialidades.
   * Note, that providing `undefined` is treated as the value not being there.
   * Read more here: https://pris.ly/d/null-undefined
   * @param {LocalAtendimentoEspecialidadesCountArgs} args - Arguments to filter LocalAtendimentoEspecialidades to count.
   * @example
   * // Count the number of LocalAtendimentoEspecialidades
   * const count = await prisma.localAtendimentoEspecialidades.count({
   *   where: {
   *     // ... the filter for the LocalAtendimentoEspecialidades we want to count
   *   }
   * })
  **/
  count<T extends LocalAtendimentoEspecialidadesCountArgs>(
    args?: Prisma.Subset<T, LocalAtendimentoEspecialidadesCountArgs>,
  ): Prisma.PrismaPromise<
    T extends runtime.Types.Utils.Record<'select', any>
      ? T['select'] extends true
        ? number
        : Prisma.GetScalarType<T['select'], LocalAtendimentoEspecialidadesCountAggregateOutputType>
      : number
  >

  /**
   * Allows you to perform aggregations operations on a LocalAtendimentoEspecialidades.
   * Note, that providing `undefined` is treated as the value not being there.
   * Read more here: https://pris.ly/d/null-undefined
   * @param {LocalAtendimentoEspecialidadesAggregateArgs} args - Select which aggregations you would like to apply and on what fields.
   * @example
   * // Ordered by age ascending
   * // Where email contains prisma.io
   * // Limited to the 10 users
   * const aggregations = await prisma.user.aggregate({
   *   _avg: {
   *     age: true,
   *   },
   *   where: {
   *     email: {
   *       contains: "prisma.io",
   *     },
   *   },
   *   orderBy: {
   *     age: "asc",
   *   },
   *   take: 10,
   * })
  **/
  aggregate<T extends LocalAtendimentoEspecialidadesAggregateArgs>(args: Prisma.Subset<T, LocalAtendimentoEspecialidadesAggregateArgs>): Prisma.PrismaPromise<GetLocalAtendimentoEspecialidadesAggregateType<T>>

  /**
   * Group by LocalAtendimentoEspecialidades.
   * Note, that providing `undefined` is treated as the value not being there.
   * Read more here: https://pris.ly/d/null-undefined
   * @param {LocalAtendimentoEspecialidadesGroupByArgs} args - Group by arguments.
   * @example
   * // Group by city, order by createdAt, get count
   * const result = await prisma.user.groupBy({
   *   by: ['city', 'createdAt'],
   *   orderBy: {
   *     createdAt: true
   *   },
   *   _count: {
   *     _all: true
   *   },
   * })
   * 
  **/
  groupBy<
    T extends LocalAtendimentoEspecialidadesGroupByArgs,
    HasSelectOrTake extends Prisma.Or<
      Prisma.Extends<'skip', Prisma.Keys<T>>,
      Prisma.Extends<'take', Prisma.Keys<T>>
    >,
    OrderByArg extends Prisma.True extends HasSelectOrTake
      ? { orderBy: LocalAtendimentoEspecialidadesGroupByArgs['orderBy'] }
      : { orderBy?: LocalAtendimentoEspecialidadesGroupByArgs['orderBy'] },
    OrderFields extends Prisma.ExcludeUnderscoreKeys<Prisma.Keys<Prisma.MaybeTupleToUnion<T['orderBy']>>>,
    ByFields extends Prisma.MaybeTupleToUnion<T['by']>,
    ByValid extends Prisma.Has<ByFields, OrderFields>,
    HavingFields extends Prisma.GetHavingFields<T['having']>,
    HavingValid extends Prisma.Has<ByFields, HavingFields>,
    ByEmpty extends T['by'] extends never[] ? Prisma.True : Prisma.False,
    InputErrors extends ByEmpty extends Prisma.True
    ? `Error: "by" must not be empty.`
    : HavingValid extends Prisma.False
    ? {
        [P in HavingFields]: P extends ByFields
          ? never
          : P extends string
          ? `Error: Field "${P}" used in "having" needs to be provided in "by".`
          : [
              Error,
              'Field ',
              P,
              ` in "having" needs to be provided in "by"`,
            ]
      }[HavingFields]
    : 'take' extends Prisma.Keys<T>
    ? 'orderBy' extends Prisma.Keys<T>
      ? ByValid extends Prisma.True
        ? {}
        : {
            [P in OrderFields]: P extends ByFields
              ? never
              : `Error: Field "${P}" in "orderBy" needs to be provided in "by"`
          }[OrderFields]
      : 'Error: If you provide "take", you also need to provide "orderBy"'
    : 'skip' extends Prisma.Keys<T>
    ? 'orderBy' extends Prisma.Keys<T>
      ? ByValid extends Prisma.True
        ? {}
        : {
            [P in OrderFields]: P extends ByFields
              ? never
              : `Error: Field "${P}" in "orderBy" needs to be provided in "by"`
          }[OrderFields]
      : 'Error: If you provide "skip", you also need to provide "orderBy"'
    : ByValid extends Prisma.True
    ? {}
    : {
        [P in OrderFields]: P extends ByFields
          ? never
          : `Error: Field "${P}" in "orderBy" needs to be provided in "by"`
      }[OrderFields]
  >(args: Prisma.SubsetIntersection<T, LocalAtendimentoEspecialidadesGroupByArgs, OrderByArg> & InputErrors): {} extends InputErrors ? GetLocalAtendimentoEspecialidadesGroupByPayload<T> : Prisma.PrismaPromise<InputErrors>
/**
 * Fields of the LocalAtendimentoEspecialidades model
 */
readonly fields: LocalAtendimentoEspecialidadesFieldRefs;
}

/**
 * The delegate class that acts as a "Promise-like" for LocalAtendimentoEspecialidades.
 * Why is this prefixed with `Prisma__`?
 * Because we want to prevent naming conflicts as mentioned in
 * https://github.com/prisma/prisma-client-js/issues/707
 */
export interface Prisma__LocalAtendimentoEspecialidadesClient<T, Null = never, ExtArgs extends runtime.Types.Extensions.InternalArgs = runtime.Types.Extensions.DefaultArgs, GlobalOmitOptions = {}> extends Prisma.PrismaPromise<T> {
  readonly [Symbol.toStringTag]: "PrismaPromise"
  localAtendimento<T extends Prisma.LocalAtendimentoDefaultArgs<ExtArgs> = {}>(args?: Prisma.Subset<T, Prisma.LocalAtendimentoDefaultArgs<ExtArgs>>): Prisma.Prisma__LocalAtendimentoClient<runtime.Types.Result.GetResult<Prisma.$LocalAtendimentoPayload<ExtArgs>, T, "findUniqueOrThrow", GlobalOmitOptions> | Null, Null, ExtArgs, GlobalOmitOptions>
  especialidade<T extends Prisma.EspecialidadeDefaultArgs<ExtArgs> = {}>(args?: Prisma.Subset<T, Prisma.EspecialidadeDefaultArgs<ExtArgs>>): Prisma.Prisma__EspecialidadeClient<runtime.Types.Result.GetResult<Prisma.$EspecialidadePayload<ExtArgs>, T, "findUniqueOrThrow", GlobalOmitOptions> | Null, Null, ExtArgs, GlobalOmitOptions>
  /**
   * Attaches callbacks for the resolution and/or rejection of the Promise.
   * @param onfulfilled The callback to execute when the Promise is resolved.
   * @param onrejected The callback to execute when the Promise is rejected.
   * @returns A Promise for the completion of which ever callback is executed.
   */
  then<TResult1 = T, TResult2 = never>(onfulfilled?: ((value: T) => TResult1 | PromiseLike<TResult1>) | undefined | null, onrejected?: ((reason: any) => TResult2 | PromiseLike<TResult2>) | undefined | null): runtime.Types.Utils.JsPromise<TResult1 | TResult2>
  /**
   * Attaches a callback for only the rejection of the Promise.
   * @param onrejected The callback to execute when the Promise is rejected.
   * @returns A Promise for the completion of the callback.
   */
  catch<TResult = never>(onrejected?: ((reason: any) => TResult | PromiseLike<TResult>) | undefined | null): runtime.Types.Utils.JsPromise<T | TResult>
  /**
   * Attaches a callback that is invoked when the Promise is settled (fulfilled or rejected). The
   * resolved value cannot be modified from the callback.
   * @param onfinally The callback to execute when the Promise is settled (fulfilled or rejected).
   * @returns A Promise for the completion of the callback.
   */
  finally(onfinally?: (() => void) | undefined | null): runtime.Types.Utils.JsPromise<T>
}




/**
 * Fields of the LocalAtendimentoEspecialidades model
 */
export interface LocalAtendimentoEspecialidadesFieldRefs {
  readonly id: Prisma.FieldRef<"LocalAtendimentoEspecialidades", 'Int'>
  readonly localAtendimentoId: Prisma.FieldRef<"LocalAtendimentoEspecialidades", 'Int'>
  readonly especialidadeId: Prisma.FieldRef<"LocalAtendimentoEspecialidades", 'Int'>
  readonly createdAt: Prisma.FieldRef<"LocalAtendimentoEspecialidades", 'DateTime'>
}
    

// Custom InputTypes
/**
 * LocalAtendimentoEspecialidades findUnique
 */
export type LocalAtendimentoEspecialidadesFindUniqueArgs<ExtArgs extends runtime.Types.Extensions.InternalArgs = runtime.Types.Extensions.DefaultArgs> = {
  /**
   * Select specific fields to fetch from the LocalAtendimentoEspecialidades
   */
  select?: Prisma.LocalAtendimentoEspecialidadesSelect<ExtArgs> | null
  /**
   * Omit specific fields from the LocalAtendimentoEspecialidades
   */
  omit?: Prisma.LocalAtendimentoEspecialidadesOmit<ExtArgs> | null
  /**
   * Choose, which related nodes to fetch as well
   */
  include?: Prisma.LocalAtendimentoEspecialidadesInclude<ExtArgs> | null
  /**
   * Filter, which LocalAtendimentoEspecialidades to fetch.
   */
  where: Prisma.LocalAtendimentoEspecialidadesWhereUniqueInput
}

/**
 * LocalAtendimentoEspecialidades findUniqueOrThrow
 */
export type LocalAtendimentoEspecialidadesFindUniqueOrThrowArgs<ExtArgs extends runtime.Types.Extensions.InternalArgs = runtime.Types.Extensions.DefaultArgs> = {
  /**
   * Select specific fields to fetch from the LocalAtendimentoEspecialidades
   */
  select?: Prisma.LocalAtendimentoEspecialidadesSelect<ExtArgs> | null
  /**
   * Omit specific fields from the LocalAtendimentoEspecialidades
   */
  omit?: Prisma.LocalAtendimentoEspecialidadesOmit<ExtArgs> | null
  /**
   * Choose, which related nodes to fetch as well
   */
  include?: Prisma.LocalAtendimentoEspecialidadesInclude<ExtArgs> | null
  /**
   * Filter, which LocalAtendimentoEspecialidades to fetch.
   */
  where: Prisma.LocalAtendimentoEspecialidadesWhereUniqueInput
}

/**
 * LocalAtendimentoEspecialidades findFirst
 */
export type LocalAtendimentoEspecialidadesFindFirstArgs<ExtArgs extends runtime.Types.Extensions.InternalArgs = runtime.Types.Extensions.DefaultArgs> = {
  /**
   * Select specific fields to fetch from the LocalAtendimentoEspecialidades
   */
  select?: Prisma.LocalAtendimentoEspecialidadesSelect<ExtArgs> | null
  /**
   * Omit specific fields from the LocalAtendimentoEspecialidades
   */
  omit?: Prisma.LocalAtendimentoEspecialidadesOmit<ExtArgs> | null
  /**
   * Choose, which related nodes to fetch as well
   */
  include?: Prisma.LocalAtendimentoEspecialidadesInclude<ExtArgs> | null
  /**
   * Filter, which LocalAtendimentoEspecialidades to fetch.
   */
  where?: Prisma.LocalAtendimentoEspecialidadesWhereInput
  /**
   * {@link https://www.prisma.io/docs/concepts/components/prisma-client/sorting Sorting Docs}
   * 
   * Determine the order of LocalAtendimentoEspecialidades to fetch.
   */
  orderBy?: Prisma.LocalAtendimentoEspecialidadesOrderByWithRelationInput | Prisma.LocalAtendimentoEspecialidadesOrderByWithRelationInput[]
  /**
   * {@link https://www.prisma.io/docs/concepts/components/prisma-client/pagination#cursor-based-pagination Cursor Docs}
   * 
   * Sets the position for searching for LocalAtendimentoEspecialidades.
   */
  cursor?: Prisma.LocalAtendimentoEspecialidadesWhereUniqueInput
  /**
   * {@link https://www.prisma.io/docs/concepts/components/prisma-client/pagination Pagination Docs}
   * 
   * Take `±n` LocalAtendimentoEspecialidades from the position of the cursor.
   */
  take?: number
  /**
   * {@link https://www.prisma.io/docs/concepts/components/prisma-client/pagination Pagination Docs}
   * 
   * Skip the first `n` LocalAtendimentoEspecialidades.
   */
  skip?: number
  /**
   * {@link https://www.prisma.io/docs/concepts/components/prisma-client/distinct Distinct Docs}
   * 
   * Filter by unique combinations of LocalAtendimentoEspecialidades.
   */
  distinct?: Prisma.LocalAtendimentoEspecialidadesScalarFieldEnum | Prisma.LocalAtendimentoEspecialidadesScalarFieldEnum[]
}

/**
 * LocalAtendimentoEspecialidades findFirstOrThrow
 */
export type LocalAtendimentoEspecialidadesFindFirstOrThrowArgs<ExtArgs extends runtime.Types.Extensions.InternalArgs = runtime.Types.Extensions.DefaultArgs> = {
  /**
   * Select specific fields to fetch from the LocalAtendimentoEspecialidades
   */
  select?: Prisma.LocalAtendimentoEspecialidadesSelect<ExtArgs> | null
  /**
   * Omit specific fields from the LocalAtendimentoEspecialidades
   */
  omit?: Prisma.LocalAtendimentoEspecialidadesOmit<ExtArgs> | null
  /**
   * Choose, which related nodes to fetch as well
   */
  include?: Prisma.LocalAtendimentoEspecialidadesInclude<ExtArgs> | null
  /**
   * Filter, which LocalAtendimentoEspecialidades to fetch.
   */
  where?: Prisma.LocalAtendimentoEspecialidadesWhereInput
  /**
   * {@link https://www.prisma.io/docs/concepts/components/prisma-client/sorting Sorting Docs}
   * 
   * Determine the order of LocalAtendimentoEspecialidades to fetch.
   */
  orderBy?: Prisma.LocalAtendimentoEspecialidadesOrderByWithRelationInput | Prisma.LocalAtendimentoEspecialidadesOrderByWithRelationInput[]
  /**
   * {@link https://www.prisma.io/docs/concepts/components/prisma-client/pagination#cursor-based-pagination Cursor Docs}
   * 
   * Sets the position for searching for LocalAtendimentoEspecialidades.
   */
  cursor?: Prisma.LocalAtendimentoEspecialidadesWhereUniqueInput
  /**
   * {@link https://www.prisma.io/docs/concepts/components/prisma-client/pagination Pagination Docs}
   * 
   * Take `±n` LocalAtendimentoEspecialidades from the position of the cursor.
   */
  take?: number
  /**
   * {@link https://www.prisma.io/docs/concepts/components/prisma-client/pagination Pagination Docs}
   * 
   * Skip the first `n` LocalAtendimentoEspecialidades.
   */
  skip?: number
  /**
   * {@link https://www.prisma.io/docs/concepts/components/prisma-client/distinct Distinct Docs}
   * 
   * Filter by unique combinations of LocalAtendimentoEspecialidades.
   */
  distinct?: Prisma.LocalAtendimentoEspecialidadesScalarFieldEnum | Prisma.LocalAtendimentoEspecialidadesScalarFieldEnum[]
}

/**
 * LocalAtendimentoEspecialidades findMany
 */
export type LocalAtendimentoEspecialidadesFindManyArgs<ExtArgs extends runtime.Types.Extensions.InternalArgs = runtime.Types.Extensions.DefaultArgs> = {
  /**
   * Select specific fields to fetch from the LocalAtendimentoEspecialidades
   */
  select?: Prisma.LocalAtendimentoEspecialidadesSelect<ExtArgs> | null
  /**
   * Omit specific fields from the LocalAtendimentoEspecialidades
   */
  omit?: Prisma.LocalAtendimentoEspecialidadesOmit<ExtArgs> | null
  /**
   * Choose, which related nodes to fetch as well
   */
  include?: Prisma.LocalAtendimentoEspecialidadesInclude<ExtArgs> | null
  /**
   * Filter, which LocalAtendimentoEspecialidades to fetch.
   */
  where?: Prisma.LocalAtendimentoEspecialidadesWhereInput
  /**
   * {@link https://www.prisma.io/docs/concepts/components/prisma-client/sorting Sorting Docs}
   * 
   * Determine the order of LocalAtendimentoEspecialidades to fetch.
   */
  orderBy?: Prisma.LocalAtendimentoEspecialidadesOrderByWithRelationInput | Prisma.LocalAtendimentoEspecialidadesOrderByWithRelationInput[]
  /**
   * {@link https://www.prisma.io/docs/concepts/components/prisma-client/pagination#cursor-based-pagination Cursor Docs}
   * 
   * Sets the position for listing LocalAtendimentoEspecialidades.
   */
  cursor?: Prisma.LocalAtendimentoEspecialidadesWhereUniqueInput
  /**
   * {@link https://www.prisma.io/docs/concepts/components/prisma-client/pagination Pagination Docs}
   * 
   * Take `±n` LocalAtendimentoEspecialidades from the position of the cursor.
   */
  take?: number
  /**
   * {@link https://www.prisma.io/docs/concepts/components/prisma-client/pagination Pagination Docs}
   * 
   * Skip the first `n` LocalAtendimentoEspecialidades.
   */
  skip?: number
  distinct?: Prisma.LocalAtendimentoEspecialidadesScalarFieldEnum | Prisma.LocalAtendimentoEspecialidadesScalarFieldEnum[]
}

/**
 * LocalAtendimentoEspecialidades create
 */
export type LocalAtendimentoEspecialidadesCreateArgs<ExtArgs extends runtime.Types.Extensions.InternalArgs = runtime.Types.Extensions.DefaultArgs> = {
  /**
   * Select specific fields to fetch from the LocalAtendimentoEspecialidades
   */
  select?: Prisma.LocalAtendimentoEspecialidadesSelect<ExtArgs> | null
  /**
   * Omit specific fields from the LocalAtendimentoEspecialidades
   */
  omit?: Prisma.LocalAtendimentoEspecialidadesOmit<ExtArgs> | null
  /**
   * Choose, which related nodes to fetch as well
   */
  include?: Prisma.LocalAtendimentoEspecialidadesInclude<ExtArgs> | null
  /**
   * The data needed to create a LocalAtendimentoEspecialidades.
   */
  data: Prisma.XOR<Prisma.LocalAtendimentoEspecialidadesCreateInput, Prisma.LocalAtendimentoEspecialidadesUncheckedCreateInput>
}

/**
 * LocalAtendimentoEspecialidades createMany
 */
export type LocalAtendimentoEspecialidadesCreateManyArgs<ExtArgs extends runtime.Types.Extensions.InternalArgs = runtime.Types.Extensions.DefaultArgs> = {
  /**
   * The data used to create many LocalAtendimentoEspecialidades.
   */
  data: Prisma.LocalAtendimentoEspecialidadesCreateManyInput | Prisma.LocalAtendimentoEspecialidadesCreateManyInput[]
  skipDuplicates?: boolean
}

/**
 * LocalAtendimentoEspecialidades update
 */
export type LocalAtendimentoEspecialidadesUpdateArgs<ExtArgs extends runtime.Types.Extensions.InternalArgs = runtime.Types.Extensions.DefaultArgs> = {
  /**
   * Select specific fields to fetch from the LocalAtendimentoEspecialidades
   */
  select?: Prisma.LocalAtendimentoEspecialidadesSelect<ExtArgs> | null
  /**
   * Omit specific fields from the LocalAtendimentoEspecialidades
   */
  omit?: Prisma.LocalAtendimentoEspecialidadesOmit<ExtArgs> | null
  /**
   * Choose, which related nodes to fetch as well
   */
  include?: Prisma.LocalAtendimentoEspecialidadesInclude<ExtArgs> | null
  /**
   * The data needed to update a LocalAtendimentoEspecialidades.
   */
  data: Prisma.XOR<Prisma.LocalAtendimentoEspecialidadesUpdateInput, Prisma.LocalAtendimentoEspecialidadesUncheckedUpdateInput>
  /**
   * Choose, which LocalAtendimentoEspecialidades to update.
   */
  where: Prisma.LocalAtendimentoEspecialidadesWhereUniqueInput
}

/**
 * LocalAtendimentoEspecialidades updateMany
 */
export type LocalAtendimentoEspecialidadesUpdateManyArgs<ExtArgs extends runtime.Types.Extensions.InternalArgs = runtime.Types.Extensions.DefaultArgs> = {
  /**
   * The data used to update LocalAtendimentoEspecialidades.
   */
  data: Prisma.XOR<Prisma.LocalAtendimentoEspecialidadesUpdateManyMutationInput, Prisma.LocalAtendimentoEspecialidadesUncheckedUpdateManyInput>
  /**
   * Filter which LocalAtendimentoEspecialidades to update
   */
  where?: Prisma.LocalAtendimentoEspecialidadesWhereInput
  /**
   * Limit how many LocalAtendimentoEspecialidades to update.
   */
  limit?: number
}

/**
 * LocalAtendimentoEspecialidades upsert
 */
export type LocalAtendimentoEspecialidadesUpsertArgs<ExtArgs extends runtime.Types.Extensions.InternalArgs = runtime.Types.Extensions.DefaultArgs> = {
  /**
   * Select specific fields to fetch from the LocalAtendimentoEspecialidades
   */
  select?: Prisma.LocalAtendimentoEspecialidadesSelect<ExtArgs> | null
  /**
   * Omit specific fields from the LocalAtendimentoEspecialidades
   */
  omit?: Prisma.LocalAtendimentoEspecialidadesOmit<ExtArgs> | null
  /**
   * Choose, which related nodes to fetch as well
   */
  include?: Prisma.LocalAtendimentoEspecialidadesInclude<ExtArgs> | null
  /**
   * The filter to search for the LocalAtendimentoEspecialidades to update in case it exists.
   */
  where: Prisma.LocalAtendimentoEspecialidadesWhereUniqueInput
  /**
   * In case the LocalAtendimentoEspecialidades found by the `where` argument doesn't exist, create a new LocalAtendimentoEspecialidades with this data.
   */
  create: Prisma.XOR<Prisma.LocalAtendimentoEspecialidadesCreateInput, Prisma.LocalAtendimentoEspecialidadesUncheckedCreateInput>
  /**
   * In case the LocalAtendimentoEspecialidades was found with the provided `where` argument, update it with this data.
   */
  update: Prisma.XOR<Prisma.LocalAtendimentoEspecialidadesUpdateInput, Prisma.LocalAtendimentoEspecialidadesUncheckedUpdateInput>
}

/**
 * LocalAtendimentoEspecialidades delete
 */
export type LocalAtendimentoEspecialidadesDeleteArgs<ExtArgs extends runtime.Types.Extensions.InternalArgs = runtime.Types.Extensions.DefaultArgs> = {
  /**
   * Select specific fields to fetch from the LocalAtendimentoEspecialidades
   */
  select?: Prisma.LocalAtendimentoEspecialidadesSelect<ExtArgs> | null
  /**
   * Omit specific fields from the LocalAtendimentoEspecialidades
   */
  omit?: Prisma.LocalAtendimentoEspecialidadesOmit<ExtArgs> | null
  /**
   * Choose, which related nodes to fetch as well
   */
  include?: Prisma.LocalAtendimentoEspecialidadesInclude<ExtArgs> | null
  /**
   * Filter which LocalAtendimentoEspecialidades to delete.
   */
  where: Prisma.LocalAtendimentoEspecialidadesWhereUniqueInput
}

/**
 * LocalAtendimentoEspecialidades deleteMany
 */
export type LocalAtendimentoEspecialidadesDeleteManyArgs<ExtArgs extends runtime.Types.Extensions.InternalArgs = runtime.Types.Extensions.DefaultArgs> = {
  /**
   * Filter which LocalAtendimentoEspecialidades to delete
   */
  where?: Prisma.LocalAtendimentoEspecialidadesWhereInput
  /**
   * Limit how many LocalAtendimentoEspecialidades to delete.
   */
  limit?: number
}

/**
 * LocalAtendimentoEspecialidades without action
 */
export type LocalAtendimentoEspecialidadesDefaultArgs<ExtArgs extends runtime.Types.Extensions.InternalArgs = runtime.Types.Extensions.DefaultArgs> = {
  /**
   * Select specific fields to fetch from the LocalAtendimentoEspecialidades
   */
  select?: Prisma.LocalAtendimentoEspecialidadesSelect<ExtArgs> | null
  /**
   * Omit specific fields from the LocalAtendimentoEspecialidades
   */
  omit?: Prisma.LocalAtendimentoEspecialidadesOmit<ExtArgs> | null
  /**
   * Choose, which related nodes to fetch as well
   */
  include?: Prisma.LocalAtendimentoEspecialidadesInclude<ExtArgs> | null
}
