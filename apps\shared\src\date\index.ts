import {
  format,
  parseISO as parseISODateFns,
  startOfDay,
  endOfDay,
  getYear as getYearDateFns,
  getMonth as getMonthDateFns,
  getDate as getDateDateFns,
  addDays,
  setDefaultOptions,
  toDate,
  set,
  getDaysInMonth,
  startOfMonth,
  endOfMonth,
  addMonths,
  subMonths,
  formatISO,
  setHours,
  setMinutes,
  parse,
  isDate,
} from "date-fns";
import { ptBR } from "date-fns/locale";
import { toZonedTime, fromZonedTime, formatInTimeZone } from "date-fns-tz";

// Configura pt-BR como locale padrão para todas as funções date-fns
setDefaultOptions({ locale: ptBR });

// Default timezone for the application (Brazil/São Paulo)
const DEFAULT_TIMEZONE = "America/Sao_Paulo";

// ========================
// Core Date Creation Functions - NEVER USE new Date() DIRECTLY
// ========================

/**
 * Obtém a data/hora atual
 * SEMPRE use esta função ao invés de new Date()
 */
export function getCurrentDate(): Date {
  return toDate(Date.now());
}

/**
 * Obtém o ano atual
 */
export function getCurrentYear(): number {
  return getYearDateFns(Date.now());
}

/**
 * Obtém o mês atual (1-12)
 */
export function getCurrentMonth(): number {
  return getMonthDateFns(Date.now()) + 1;
}

/**
 * Obtém o dia atual
 */
export function getCurrentDay(): number {
  return getDateDateFns(Date.now());
}

/**
 * Cria uma data local (sem considerar timezone)
 * Use esta função ao invés de new Date(year, month, day)
 */
export function createLocalDate(year: number, month: number, day: number): Date {
  return set(toDate(0), {
    year,
    month: month - 1,
    date: day,
    hours: 0,
    minutes: 0,
    seconds: 0,
    milliseconds: 0,
  });
}

/**
 * Cria uma data com horário específico
 * Use esta função ao invés de new Date(year, month, day, hour, minute)
 */
export function createDateWithTime(year: number, month: number, day: number, hour: number, minute: number): Date {
  const date = set(toDate(0), { year, month: month - 1, date: day });
  return setMinutes(setHours(date, hour), minute);
}

/**
 * Cria uma data local usando date-fns para obter valores atuais
 */
export function createCurrentLocalDate(): Date {
  const now = toDate(Date.now());
  return createLocalDate(getYearDateFns(now), getMonthDateFns(now) + 1, getDateDateFns(now));
}

/**
 * Cria uma data para o final de um período (usado para datas finais padrão)
 */
export function createEndOfPeriodDate(): Date {
  return set(toDate(0), { year: 2099, month: 11, date: 31 });
}

// ========================
// Timezone Conversion Functions
// ========================

/**
 * Converte uma data UTC para a timezone especificada
 * @param date - Data em UTC (do banco de dados) - pode ser Date ou string
 * @param timezone - Timezone IANA (ex: "America/Sao_Paulo")
 */
export function utcToTimezone(date: Date | string, timezone: string = DEFAULT_TIMEZONE): Date {
  const dateObj = normalizeDate(date);
  if (!dateObj) throw new Error("Invalid date provided to utcToTimezone");
  return toZonedTime(dateObj, timezone);
}

/**
 * Converte uma data de uma timezone específica para UTC
 * @param date - Data na timezone local
 * @param timezone - Timezone IANA (ex: "America/Sao_Paulo")
 */
export function timezoneToUTC(date: Date | string, timezone: string = DEFAULT_TIMEZONE): Date {
  const dateObj = normalizeDate(date);
  if (!dateObj) throw new Error("Invalid date provided to timezoneToUTC");
  return fromZonedTime(dateObj, timezone);
}

/**
 * Formata uma data UTC diretamente na timezone especificada
 * @param date - Data em UTC
 * @param formatStr - String de formato (ex: "dd/MM/yyyy HH:mm")
 * @param timezone - Timezone IANA (ex: "America/Sao_Paulo")
 */
export function formatInTimezone(
  date: Date | string,
  formatStr: string = "dd/MM/yyyy",
  timezone: string = DEFAULT_TIMEZONE
): string {
  const dateObj = normalizeDate(date);
  if (!dateObj) throw new Error("Invalid date provided to formatInTimezone");
  return formatInTimeZone(dateObj, timezone, formatStr, { locale: ptBR });
}

// ========================
// Helper Functions
// ========================

/**
 * Função auxiliar para normalizar input de data
 * Aceita Date, string ou null/undefined
 * @internal
 */
function normalizeDate(dateInput: Date | string | null | undefined): Date | null {
  if (!dateInput) return null;
  if (dateInput instanceof Date) return dateInput;
  return parseISODateFns(dateInput);
}

// ========================
// Parsing Functions
// ========================

/**
 * Parse de ISO string ou retorna Date se já for Date
 * Para strings: converte corretamente considerando timezone UTC do banco
 * Use esta função ao invés de new Date(dateString)
 */
export function parseISO(dateInput: string | Date): Date {
  // Se já for Date, apenas retorna
  if (dateInput instanceof Date) {
    return dateInput;
  }

  // Para strings, usar parseISO do date-fns que trata timezone corretamente
  return parseISODateFns(dateInput);
}

/**
 * parseISO original do date-fns (COM conversão de timezone)
 * Use apenas quando REALMENTE precisar converter timezone
 */
export const parseISOWithTimezone = parseISODateFns;

/**
 * Converte uma data ISO string para Date
 * Trata corretamente strings UTC (terminadas em Z)
 */
export function parseUTCDate(dateString: Date | string): Date {
  if (dateString instanceof Date) {
    return dateString;
  }

  const parsed = parseISODateFns(dateString);
  // Se for UTC (termina com Z), converte para início do dia local
  if (dateString.endsWith("Z")) {
    return startOfDay(parsed);
  }
  return parsed;
}

/**
 * Converte string de date no formato YYYY-MM-DD para YYYY-MM-DDTHH:MM:SS (ISO-8601)
 */
export function formatDateToDateTime(dateStr: string): string {
  const parsedDate = parse(dateStr, "yyyy-MM-dd", getCurrentDate());
  if (!isDate(parsedDate) || isNaN(parsedDate.getTime())) {
    throw new Error("Data inválida. Formato esperado: YYYY-MM-DD");
  }

  // Retornar no formato ISO-8601 (YYYY-MM-DDTHH:MM:SS)
  return formatISO(parsedDate, { representation: "complete" });
}

// ========================
// Formatting Functions
// ========================

/**
 * Formata uma data para exibição no formato brasileiro
 * AVISO: Se passar string, use formatDateString para evitar conversão de timezone
 */
export function formatDate(date: Date | string, formatStr: string = "dd/MM/yyyy"): string {
  if (typeof date === "string") {
    // Se for string, usar extração direta para evitar timezone
    if (formatStr === "dd/MM/yyyy") {
      return formatDateString(date);
    }
    // Para outros formatos, ainda usa parseISO (pode converter timezone)
    const dateObj = parseISODateFns(date);
    return format(dateObj, formatStr, { locale: ptBR });
  }
  return format(date, formatStr, { locale: ptBR });
}

/**
 * Formata uma data ISO string ou Date para formato brasileiro
 * Para strings: extrai diretamente sem conversão de timezone
 * Para Date: formata considerando o valor local
 */
export function formatDateString(dateInput: string | Date): string {
  if (typeof dateInput === "string") {
    // Extrai ano, mês e dia diretamente da string ISO
    const match = dateInput.match(/^(\d{4})-(\d{2})-(\d{2})/);
    if (!match) return dateInput;

    const [_, year, month, day] = match;
    return `${day}/${month}/${year}`;
  }

  // Para Date, formata usando a data local
  return format(dateInput, "dd/MM/yyyy", { locale: ptBR });
}

/**
 * Alias para formatDateString - mantido para compatibilidade
 */
export function formatDateStringUTC(dateInput: string | Date): string {
  return formatDateString(dateInput);
}

/**
 * Formata uma data para uso em inputs de tipo date (YYYY-MM-DD)
 * Aceita Date ou string
 */
export function formatDateForInput(date: Date | string): string {
  const dateObj = normalizeDate(date);
  if (!dateObj) throw new Error("Invalid date provided to formatDateForInput");
  const year = dateObj.getFullYear();
  const month = String(dateObj.getMonth() + 1).padStart(2, "0");
  const day = String(dateObj.getDate()).padStart(2, "0");
  return `${year}-${month}-${day}`;
}

/**
 * Obtém a data atual como ISO string
 */
export function getCurrentISOString(): string {
  return formatISO(getCurrentDate());
}

/**
 * Converte uma Date para ISO string
 */
export function toISOString(date: Date): string {
  return formatISO(date);
}

// ========================
// Timezone-specific Functions
// ========================

/**
 * Cria uma data local considerando o timezone do cliente
 * A data é criada no timezone local e depois convertida para UTC para armazenamento
 */
export function createLocalDateInTimezone(year: number, month: number, day: number, timezone: string): Date {
  const localDate = createLocalDate(year, month, day);
  return timezoneToUTC(localDate, timezone);
}

/**
 * Cria uma data com horário considerando o timezone do cliente
 */
export function createDateWithTimeInTimezone(
  year: number,
  month: number,
  day: number,
  hour: number,
  minute: number,
  timezone: string
): Date {
  const localDate = createDateWithTime(year, month, day, hour, minute);
  return timezoneToUTC(localDate, timezone);
}

/**
 * Obtém a data/hora atual no timezone do cliente e converte para UTC
 */
export function getCurrentDateInTimezone(timezone: string): Date {
  const now = getCurrentDate();
  const localTime = utcToTimezone(now, timezone);
  return timezoneToUTC(localTime, timezone);
}

/**
 * Parse de string ISO considerando o timezone
 * Útil quando recebemos datas do frontend que já estão em UTC
 */
export function parseISOInTimezone(dateString: string, timezone: string): Date {
  const date = parseISO(dateString);
  // Se a data já está em UTC (termina com 'Z' ou tem offset), retorna como está
  if (dateString.includes("Z") || dateString.match(/[+-]\d{2}:\d{2}$/)) {
    return date;
  }
  // Caso contrário, assume que está no timezone local e converte para UTC
  return timezoneToUTC(date, timezone);
}

/**
 * Converte uma data do banco (UTC) para ISO string no timezone do cliente
 */
export function toISOStringInTimezone(date: Date | string, timezone: string): string {
  const localDate = utcToTimezone(date, timezone);
  return localDate.toISOString();
}

/**
 * Obtém o início do mês no timezone do cliente
 */
export function getStartOfMonthInTimezone(year: number, month: number, timezone: string): Date {
  return createLocalDateInTimezone(year, month, 1, timezone);
}

/**
 * Obtém o fim do mês no timezone do cliente
 */
export function getEndOfMonthInTimezone(year: number, month: number, timezone: string): Date {
  // Último dia do mês usando date-fns
  const firstDayOfMonth = createLocalDate(year, month, 1);
  const lastDay = getDaysInMonth(firstDayOfMonth);
  const endOfMonth = createDateWithTime(year, month, lastDay, 23, 59);
  return timezoneToUTC(endOfMonth, timezone);
}

// ========================
// Date Extraction Functions
// ========================

/**
 * Extrai apenas a data (sem hora) de uma string ISO ou Date
 * Mantém formato YYYY-MM-DD sem considerar timezone
 */
export function getDateOnly(dateInput: string | Date): string {
  if (typeof dateInput === "string") {
    // Para string, extrai apenas a parte da data (YYYY-MM-DD)
    const match = dateInput.match(/^\d{4}-\d{2}-\d{2}/);
    return match ? match[0] : dateInput;
  }
  // Para Date, formata como YYYY-MM-DD
  return formatDateForInput(dateInput);
}

/**
 * Extrai o mês (1-12) de uma data
 */
export function getMesFromDate(date: Date | string | null | undefined): number | null {
  const dateObj = normalizeDate(date);
  if (!dateObj) return null;
  return getMonthDateFns(dateObj) + 1; // getMonth retorna 0-11, precisamos de 1-12
}

/**
 * Extrai o ano de uma data
 */
export function getAnoFromDate(date: Date | string | null | undefined): number | null {
  const dateObj = normalizeDate(date);
  if (!dateObj) return null;
  return getYearDateFns(dateObj);
}

/**
 * Extrai o mês (1-12) de uma data inicial
 */
export function getMesFromDataInicial(dataInicial: Date | string | null | undefined): number | null {
  return getMesFromDate(dataInicial);
}

/**
 * Extrai o ano de uma data inicial
 */
export function getAnoFromDataInicial(dataInicial: Date | string | null | undefined): number | null {
  return getAnoFromDate(dataInicial);
}

/**
 * Extrai mês e ano de um plantão baseado em dataInicial
 * Mantém compatibilidade com código existente que usa mes/ano
 */
export function getMesAnoFromPlantao(plantao: any): { mes: number; ano: number } {
  // Se ainda tiver mes/ano, usa eles (para compatibilidade durante migração)
  if (plantao.mes && plantao.ano) {
    return { mes: plantao.mes, ano: plantao.ano };
  }

  // Senão, extrai de dataInicial
  const mes = getMesFromDataInicial(plantao.dataInicial) || getCurrentMonth();
  const ano = getAnoFromDataInicial(plantao.dataInicial) || getCurrentYear();

  return { mes, ano };
}

// ========================
// Date Manipulation Functions
// ========================

/**
 * Adiciona dias a uma data
 */
export function addDaysToDate(date: Date, days: number): Date {
  return addDays(date, days);
}

/**
 * Obtém o início do mês
 */
export function getStartOfMonth(date: Date): Date {
  return startOfMonth(date);
}

/**
 * Obtém o fim do mês
 */
export function getEndOfMonth(date: Date): Date {
  return endOfMonth(date);
}

/**
 * Adiciona meses a uma data
 */
export function addMonthsToDate(date: Date, months: number): Date {
  return addMonths(date, months);
}

/**
 * Subtrai meses de uma data
 */
export function subtractMonthsFromDate(date: Date, months: number): Date {
  return subMonths(date, months);
}

/**
 * Obtém o número de dias em um mês específico
 */
export function getDaysInMonthForDate(year: number, month: number): number {
  // Cria uma data segura para o primeiro dia do mês
  const date = createLocalDate(year, month, 1);
  return getDaysInMonth(date);
}

/**
 * Cria uma data para 24 horas atrás
 */
export function get24HoursAgo(): Date {
  const now = getCurrentDate();
  return addDays(now, -1);
}

// ========================
// Re-exports from date-fns (for convenience)
// ========================

// Re-export direto do date-fns para funções que já aceitam múltiplos tipos
export { format, startOfDay, endOfDay, toDate } from "date-fns";

// Wrapper functions para aceitar Date | string
export function getYear(date: Date | string): number {
  const dateObj = normalizeDate(date);
  if (!dateObj) throw new Error("Invalid date provided to getYear");
  return dateObj.getFullYear();
}

export function getMonth(date: Date | string): number {
  const dateObj = normalizeDate(date);
  if (!dateObj) throw new Error("Invalid date provided to getMonth");
  return dateObj.getMonth();
}

export function getDate(date: Date | string): number {
  const dateObj = normalizeDate(date);
  if (!dateObj) throw new Error("Invalid date provided to getDate");
  return dateObj.getDate();
}
