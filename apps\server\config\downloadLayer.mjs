import path from 'path';
import { dirname } from 'path';
import { fileURLToPath } from 'url';

const __filename = fileURLToPath(import.meta.url);
const __dirname = dirname(__filename);

import { getParams, execTerminal, readInputTerminal, unzipLayer } from './functions.mjs';

const downloadLayer = async () => {
    try {
        const { profile, region } = await getParams(['profile']);

        /**
         * @type {{Layers:
         *  Array<{
         *     LayerName: string,
         *     LayerArn: string
         *     LatestMatchingVersion: {
         *       LayerVersionArn: string
         *       Version: number,
         *       CreatedDate: Date,
         *       CompatibleRuntimes: string[],
         *       CompatibleArchitectures: string[]
         *     }}>
         * }}
         */
        const { Layers } = JSON.parse(
            await execTerminal(`aws lambda list-layers --profile ${profile} --region ${region}`)
        );

        const indexLayer = await readInputTerminal(
            `Selecione o Layer:\n\n${Layers.map((l, index) => `${index}: ${l.LayerName}`).join(
                '\n'
            )}\n`,
            {
                inputType: 'number',
            }
        );

        console.log(Layers[indexLayer]);

        const selecionado = Layers[indexLayer];

        const listVersions = JSON.parse(
            await execTerminal(
                `aws lambda list-layer-versions --layer-name ${selecionado.LayerName} --query "LayerVersions[*].LayerVersionArn" --profile ${profile} --region ${region}`
            )
        );

        const indexVersionLayer = await readInputTerminal(
            `Selecione a versão:\n\n${listVersions
                .map((l, index) => `${index}: ${l}`)
                .join('\n')}\n`,
            {
                inputType: 'number',
            }
        );

        const result = JSON.parse(
            await execTerminal(
                `aws lambda get-layer-version-by-arn --arn "${listVersions[indexVersionLayer]}" --profile ${profile} --region ${region}`
            )
        );

        console.log(result);

        await unzipLayer(
            result.Content.Location,
            path.resolve(
                __dirname,
                '..',
                'src',
                'layers',
                'from-aws-to-verify',
                selecionado.LayerName,
                `version-${listVersions[indexVersionLayer].split(':').reverse()[0]}`,
                selecionado.LayerName
            )
        );

        console.log('\nFinalizado!\n');
    } catch (error) {
        console.log('\n');
        console.log('\x1b[31m%s\x1b[0m', 'Catch no listLayers():');
        console.log(error.message);
        console.log('\n');
    } finally {
        process.exit();
    }
};

downloadLayer();
