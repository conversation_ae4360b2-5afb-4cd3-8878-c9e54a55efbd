import { Card, CardContent, CardHeader, CardTitle } from "@/components/ui/card";
import { Info } from "lucide-react";
import { formatCurrency } from "@/lib/utils";

interface AntecipacaoSummaryCardProps {
  totalSelectedValue: number;
  valorSolicitado: number;
  taxaAntecipacao: number;
  valorDesconto: number;
  valorLiquido: number;
}

export function AntecipacaoSummaryCard({
  totalSelectedValue,
  valorSolicitado,
  taxaAntecipacao,
  valorDesconto,
  valorLiquido,
}: AntecipacaoSummaryCardProps) {
  return (
    <Card>
      <CardHeader>
        <CardTitle className="flex items-center gap-2">
          <Info className="h-5 w-5" />
          Resumo da Antecipação
        </CardTitle>
      </CardHeader>
      <CardContent>
        <div className="space-y-3">
          <div className="flex justify-between">
            <span>Valor Total dos Fechamentos:</span>
            <span>{formatCurrency(totalSelectedValue)}</span>
          </div>
          <div className="flex justify-between">
            <span>Valor Solicitado:</span>
            <span>{formatCurrency(valorSolicitado)}</span>
          </div>
          <div className="flex justify-between">
            <span>Taxa ({taxaAntecipacao}%):</span>
            <span>- {formatCurrency(valorDesconto)}</span>
          </div>
          <div className="border-t pt-2">
            <div className="flex justify-between font-semibold text-lg">
              <span>Valor Líquido:</span>
              <span className="text-green-600">{formatCurrency(valorLiquido)}</span>
            </div>
          </div>
        </div>
      </CardContent>
    </Card>
  );
}
