import { useNavigate } from "@tanstack/react-router";
import { useQuery } from "@tanstack/react-query";
import { api } from "@/lib/api";
import { Dialog, DialogContent, DialogDescription, DialogHeader, DialogTitle } from "@/components/ui/dialog";
import { Button } from "@/components/ui/button";
import { Badge } from "@/components/ui/badge";
import { ScrollArea } from "@/components/ui/scroll-area";
import {
  FileSignature,
  Calendar,
  DollarSign,
  Building2,
  MapPin,
  AlertCircle,
  ArrowRight,
  FileText,
} from "lucide-react";
import { formatCurrency, formatDate } from "@/lib/utils";
import { useState } from "react";
import { toast } from "sonner";

interface AntecipacaoPendente {
  uuid: string;
  valorSolicitado: number;
  percentual: number;
  taxaAntecipacao: number;
  valorLiquido: number;
  cliente: string;
  localAtendimento: string;
  periodo: string;
  dataEmissao: string;
  dataAprovacao: string;
  quantidadeFechamentos: number;
}

interface TermosPendentesModalProps {
  open: boolean;
  onClose: () => void;
}

export function TermosPendentesModal({ open, onClose }: TermosPendentesModalProps) {
  const navigate = useNavigate();
  const [selectedIndex, setSelectedIndex] = useState(0);

  // Buscar antecipações pendentes
  const { data, isLoading } = useQuery({
    queryKey: ["antecipacoes-pendentes"],
    queryFn: () =>
      api.get<{ antecipacoes: AntecipacaoPendente[]; total: number }>("/antecipacoes/pendentes-assinatura"),
    enabled: open,
  });

  const antecipacoes = data?.antecipacoes || [];
  const total = data?.total || 0;

  // Navegar para o termo selecionado
  const handleAssinar = (uuid: string) => {
    if (!uuid) {
      toast.error("Antecipação não encontrada. Entre em contato com o suporte.");
      return;
    }
    // Usar navigate com params para React Router v7
    navigate({ to: "/antecipacoes/$uuid/termo", params: { uuid } });
    onClose();
  };

  if (!open) return null;

  return (
    <Dialog open={open} onOpenChange={onClose}>
      <DialogContent className="max-w-4xl max-h-[90vh]">
        <DialogHeader>
          <DialogTitle className="flex items-center gap-2">
            <FileSignature className="h-5 w-5" />
            Termos de Antecipação Pendentes
          </DialogTitle>
          <DialogDescription>
            {total === 0
              ? "Não há termos pendentes de assinatura."
              : total === 1
                ? "Você tem 1 termo de antecipação pendente de assinatura."
                : `Você tem ${total} termos de antecipação pendentes de assinatura.`}
          </DialogDescription>
        </DialogHeader>

        {isLoading ? (
          <div className="flex items-center justify-center py-8">
            <div className="animate-spin rounded-full h-8 w-8 border-b-2 border-primary"></div>
          </div>
        ) : antecipacoes.length > 0 ? (
          <ScrollArea className="max-h-[60vh]">
            <div className="space-y-4">
              {antecipacoes.map((ant, index) => (
                <div
                  key={ant.uuid}
                  className="border rounded-lg p-4 hover:bg-accent/50 transition-colors cursor-pointer"
                  onClick={() => setSelectedIndex(index)}
                >
                  <div className="flex items-start justify-between mb-3">
                    <div className="flex items-center gap-2">
                      <FileText className="h-4 w-4 text-muted-foreground" />
                      <span className="font-medium">Termo {ant.uuid.slice(0, 8)}</span>
                      <Badge variant="secondary">{ant.periodo}</Badge>
                    </div>
                    {selectedIndex === index && <Badge className="bg-primary">Selecionado</Badge>}
                  </div>

                  <div className="grid md:grid-cols-2 gap-4 mb-3">
                    <div className="space-y-2">
                      <div className="flex items-center gap-2 text-sm">
                        <Building2 className="h-4 w-4 text-muted-foreground" />
                        <span className="text-muted-foreground">Cliente:</span>
                        <span className="font-medium">{ant.cliente}</span>
                      </div>
                      <div className="flex items-center gap-2 text-sm">
                        <MapPin className="h-4 w-4 text-muted-foreground" />
                        <span className="text-muted-foreground">Local:</span>
                        <span className="font-medium">{ant.localAtendimento}</span>
                      </div>
                    </div>

                    <div className="space-y-2">
                      <div className="flex items-center gap-2 text-sm">
                        <Calendar className="h-4 w-4 text-muted-foreground" />
                        <span className="text-muted-foreground">Emissão:</span>
                        <span className="font-medium">{formatDate(ant.dataEmissao)}</span>
                      </div>
                      <div className="flex items-center gap-2 text-sm">
                        <AlertCircle className="h-4 w-4 text-muted-foreground" />
                        <span className="text-muted-foreground">Aprovado em:</span>
                        <span className="font-medium text-green-600">{formatDate(ant.dataAprovacao)}</span>
                      </div>
                    </div>
                  </div>

                  <div className="flex items-center justify-between p-3 bg-accent/30 rounded">
                    <div className="space-y-1">
                      <div className="flex items-center gap-2">
                        <DollarSign className="h-4 w-4 text-green-600" />
                        <span className="text-sm text-muted-foreground">Valor Solicitado:</span>
                        <span className="font-semibold text-green-600">{formatCurrency(ant.valorSolicitado)}</span>
                      </div>
                      <div className="flex items-center gap-4 text-sm">
                        <span>Taxa: {ant.taxaAntecipacao}%</span>
                        <span>Líquido: {formatCurrency(ant.valorLiquido)}</span>
                        {ant.quantidadeFechamentos > 0 && (
                          <Badge variant="outline">
                            {ant.quantidadeFechamentos} fechamento{ant.quantidadeFechamentos > 1 ? "s" : ""}
                          </Badge>
                        )}
                      </div>
                    </div>

                    <Button
                      size="sm"
                      onClick={(e) => {
                        e.stopPropagation();
                        handleAssinar(ant.uuid);
                      }}
                    >
                      Assinar
                      <ArrowRight className="ml-2 h-4 w-4" />
                    </Button>
                  </div>
                </div>
              ))}
            </div>
          </ScrollArea>
        ) : (
          <div className="text-center py-8">
            <FileSignature className="h-12 w-12 text-muted-foreground mx-auto mb-3" />
            <p className="text-muted-foreground">Não há termos pendentes de assinatura.</p>
          </div>
        )}

        <div className="flex justify-end gap-2">
          <Button variant="outline" onClick={onClose}>
            {antecipacoes.length > 0 ? "Assinar Depois" : "Fechar"}
          </Button>
          {antecipacoes.length === 1 && (
            <Button onClick={() => handleAssinar(antecipacoes[0].uuid)}>
              Assinar Agora
              <ArrowRight className="ml-2 h-4 w-4" />
            </Button>
          )}
          {antecipacoes.length > 1 && selectedIndex >= 0 && (
            <Button onClick={() => handleAssinar(antecipacoes[selectedIndex].uuid)}>
              Assinar Selecionado
              <ArrowRight className="ml-2 h-4 w-4" />
            </Button>
          )}
        </div>
      </DialogContent>
    </Dialog>
  );
}
