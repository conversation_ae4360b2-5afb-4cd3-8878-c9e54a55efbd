
/* !!! This is code generated by Prisma. Do not edit directly. !!! */
/* eslint-disable */
// @ts-nocheck 
/*
 * This file exports the `Fechamento` model and its related types.
 *
 * 🟢 You can import this file directly.
 */
import * as runtime from "@prisma/client/runtime/library"
import type * as $Enums from "../enums.ts"
import type * as Prisma from "../internal/prismaNamespace.ts"

/**
 * Model Fechamento
 * 
 */
export type FechamentoModel = runtime.Types.Result.DefaultSelection<Prisma.$FechamentoPayload>

export type AggregateFechamento = {
  _count: FechamentoCountAggregateOutputType | null
  _avg: FechamentoAvgAggregateOutputType | null
  _sum: FechamentoSumAggregateOutputType | null
  _min: FechamentoMinAggregateOutputType | null
  _max: FechamentoMaxAggregateOutputType | null
}

export type FechamentoAvgAggregateOutputType = {
  id: number | null
  plantaoId: number | null
  profissionalId: number | null
  antecipacaoId: number | null
  totalHoras: number | null
  totalValor: number | null
  diasTrabalhados: number | null
  diasPrevistos: number | null
}

export type FechamentoSumAggregateOutputType = {
  id: number | null
  plantaoId: number | null
  profissionalId: number | null
  antecipacaoId: number | null
  totalHoras: number | null
  totalValor: number | null
  diasTrabalhados: number | null
  diasPrevistos: number | null
}

export type FechamentoMinAggregateOutputType = {
  id: number | null
  uuid: string | null
  plantaoId: number | null
  profissionalId: number | null
  antecipacaoId: number | null
  status: string | null
  totalHoras: number | null
  totalValor: number | null
  diasTrabalhados: number | null
  diasPrevistos: number | null
  aprovadoPor: string | null
  aprovadoEm: Date | null
  rejeitadoPor: string | null
  rejeitadoEm: Date | null
  motivoRejeicao: string | null
  observacoes: string | null
  createdAt: Date | null
  updatedAt: Date | null
  deletedAt: Date | null
}

export type FechamentoMaxAggregateOutputType = {
  id: number | null
  uuid: string | null
  plantaoId: number | null
  profissionalId: number | null
  antecipacaoId: number | null
  status: string | null
  totalHoras: number | null
  totalValor: number | null
  diasTrabalhados: number | null
  diasPrevistos: number | null
  aprovadoPor: string | null
  aprovadoEm: Date | null
  rejeitadoPor: string | null
  rejeitadoEm: Date | null
  motivoRejeicao: string | null
  observacoes: string | null
  createdAt: Date | null
  updatedAt: Date | null
  deletedAt: Date | null
}

export type FechamentoCountAggregateOutputType = {
  id: number
  uuid: number
  plantaoId: number
  profissionalId: number
  antecipacaoId: number
  status: number
  totalHoras: number
  totalValor: number
  diasTrabalhados: number
  diasPrevistos: number
  aprovadoPor: number
  aprovadoEm: number
  rejeitadoPor: number
  rejeitadoEm: number
  motivoRejeicao: number
  observacoes: number
  createdAt: number
  updatedAt: number
  deletedAt: number
  _all: number
}


export type FechamentoAvgAggregateInputType = {
  id?: true
  plantaoId?: true
  profissionalId?: true
  antecipacaoId?: true
  totalHoras?: true
  totalValor?: true
  diasTrabalhados?: true
  diasPrevistos?: true
}

export type FechamentoSumAggregateInputType = {
  id?: true
  plantaoId?: true
  profissionalId?: true
  antecipacaoId?: true
  totalHoras?: true
  totalValor?: true
  diasTrabalhados?: true
  diasPrevistos?: true
}

export type FechamentoMinAggregateInputType = {
  id?: true
  uuid?: true
  plantaoId?: true
  profissionalId?: true
  antecipacaoId?: true
  status?: true
  totalHoras?: true
  totalValor?: true
  diasTrabalhados?: true
  diasPrevistos?: true
  aprovadoPor?: true
  aprovadoEm?: true
  rejeitadoPor?: true
  rejeitadoEm?: true
  motivoRejeicao?: true
  observacoes?: true
  createdAt?: true
  updatedAt?: true
  deletedAt?: true
}

export type FechamentoMaxAggregateInputType = {
  id?: true
  uuid?: true
  plantaoId?: true
  profissionalId?: true
  antecipacaoId?: true
  status?: true
  totalHoras?: true
  totalValor?: true
  diasTrabalhados?: true
  diasPrevistos?: true
  aprovadoPor?: true
  aprovadoEm?: true
  rejeitadoPor?: true
  rejeitadoEm?: true
  motivoRejeicao?: true
  observacoes?: true
  createdAt?: true
  updatedAt?: true
  deletedAt?: true
}

export type FechamentoCountAggregateInputType = {
  id?: true
  uuid?: true
  plantaoId?: true
  profissionalId?: true
  antecipacaoId?: true
  status?: true
  totalHoras?: true
  totalValor?: true
  diasTrabalhados?: true
  diasPrevistos?: true
  aprovadoPor?: true
  aprovadoEm?: true
  rejeitadoPor?: true
  rejeitadoEm?: true
  motivoRejeicao?: true
  observacoes?: true
  createdAt?: true
  updatedAt?: true
  deletedAt?: true
  _all?: true
}

export type FechamentoAggregateArgs<ExtArgs extends runtime.Types.Extensions.InternalArgs = runtime.Types.Extensions.DefaultArgs> = {
  /**
   * Filter which Fechamento to aggregate.
   */
  where?: Prisma.FechamentoWhereInput
  /**
   * {@link https://www.prisma.io/docs/concepts/components/prisma-client/sorting Sorting Docs}
   * 
   * Determine the order of Fechamentos to fetch.
   */
  orderBy?: Prisma.FechamentoOrderByWithRelationInput | Prisma.FechamentoOrderByWithRelationInput[]
  /**
   * {@link https://www.prisma.io/docs/concepts/components/prisma-client/pagination#cursor-based-pagination Cursor Docs}
   * 
   * Sets the start position
   */
  cursor?: Prisma.FechamentoWhereUniqueInput
  /**
   * {@link https://www.prisma.io/docs/concepts/components/prisma-client/pagination Pagination Docs}
   * 
   * Take `±n` Fechamentos from the position of the cursor.
   */
  take?: number
  /**
   * {@link https://www.prisma.io/docs/concepts/components/prisma-client/pagination Pagination Docs}
   * 
   * Skip the first `n` Fechamentos.
   */
  skip?: number
  /**
   * {@link https://www.prisma.io/docs/concepts/components/prisma-client/aggregations Aggregation Docs}
   * 
   * Count returned Fechamentos
  **/
  _count?: true | FechamentoCountAggregateInputType
  /**
   * {@link https://www.prisma.io/docs/concepts/components/prisma-client/aggregations Aggregation Docs}
   * 
   * Select which fields to average
  **/
  _avg?: FechamentoAvgAggregateInputType
  /**
   * {@link https://www.prisma.io/docs/concepts/components/prisma-client/aggregations Aggregation Docs}
   * 
   * Select which fields to sum
  **/
  _sum?: FechamentoSumAggregateInputType
  /**
   * {@link https://www.prisma.io/docs/concepts/components/prisma-client/aggregations Aggregation Docs}
   * 
   * Select which fields to find the minimum value
  **/
  _min?: FechamentoMinAggregateInputType
  /**
   * {@link https://www.prisma.io/docs/concepts/components/prisma-client/aggregations Aggregation Docs}
   * 
   * Select which fields to find the maximum value
  **/
  _max?: FechamentoMaxAggregateInputType
}

export type GetFechamentoAggregateType<T extends FechamentoAggregateArgs> = {
      [P in keyof T & keyof AggregateFechamento]: P extends '_count' | 'count'
    ? T[P] extends true
      ? number
      : Prisma.GetScalarType<T[P], AggregateFechamento[P]>
    : Prisma.GetScalarType<T[P], AggregateFechamento[P]>
}




export type FechamentoGroupByArgs<ExtArgs extends runtime.Types.Extensions.InternalArgs = runtime.Types.Extensions.DefaultArgs> = {
  where?: Prisma.FechamentoWhereInput
  orderBy?: Prisma.FechamentoOrderByWithAggregationInput | Prisma.FechamentoOrderByWithAggregationInput[]
  by: Prisma.FechamentoScalarFieldEnum[] | Prisma.FechamentoScalarFieldEnum
  having?: Prisma.FechamentoScalarWhereWithAggregatesInput
  take?: number
  skip?: number
  _count?: FechamentoCountAggregateInputType | true
  _avg?: FechamentoAvgAggregateInputType
  _sum?: FechamentoSumAggregateInputType
  _min?: FechamentoMinAggregateInputType
  _max?: FechamentoMaxAggregateInputType
}

export type FechamentoGroupByOutputType = {
  id: number
  uuid: string
  plantaoId: number
  profissionalId: number
  antecipacaoId: number | null
  status: string
  totalHoras: number | null
  totalValor: number
  diasTrabalhados: number
  diasPrevistos: number
  aprovadoPor: string | null
  aprovadoEm: Date | null
  rejeitadoPor: string | null
  rejeitadoEm: Date | null
  motivoRejeicao: string | null
  observacoes: string | null
  createdAt: Date
  updatedAt: Date
  deletedAt: Date | null
  _count: FechamentoCountAggregateOutputType | null
  _avg: FechamentoAvgAggregateOutputType | null
  _sum: FechamentoSumAggregateOutputType | null
  _min: FechamentoMinAggregateOutputType | null
  _max: FechamentoMaxAggregateOutputType | null
}

type GetFechamentoGroupByPayload<T extends FechamentoGroupByArgs> = Prisma.PrismaPromise<
  Array<
    Prisma.PickEnumerable<FechamentoGroupByOutputType, T['by']> &
      {
        [P in ((keyof T) & (keyof FechamentoGroupByOutputType))]: P extends '_count'
          ? T[P] extends boolean
            ? number
            : Prisma.GetScalarType<T[P], FechamentoGroupByOutputType[P]>
          : Prisma.GetScalarType<T[P], FechamentoGroupByOutputType[P]>
      }
    >
  >



export type FechamentoWhereInput = {
  AND?: Prisma.FechamentoWhereInput | Prisma.FechamentoWhereInput[]
  OR?: Prisma.FechamentoWhereInput[]
  NOT?: Prisma.FechamentoWhereInput | Prisma.FechamentoWhereInput[]
  id?: Prisma.IntFilter<"Fechamento"> | number
  uuid?: Prisma.StringFilter<"Fechamento"> | string
  plantaoId?: Prisma.IntFilter<"Fechamento"> | number
  profissionalId?: Prisma.IntFilter<"Fechamento"> | number
  antecipacaoId?: Prisma.IntNullableFilter<"Fechamento"> | number | null
  status?: Prisma.StringFilter<"Fechamento"> | string
  totalHoras?: Prisma.FloatNullableFilter<"Fechamento"> | number | null
  totalValor?: Prisma.FloatFilter<"Fechamento"> | number
  diasTrabalhados?: Prisma.IntFilter<"Fechamento"> | number
  diasPrevistos?: Prisma.IntFilter<"Fechamento"> | number
  aprovadoPor?: Prisma.StringNullableFilter<"Fechamento"> | string | null
  aprovadoEm?: Prisma.DateTimeNullableFilter<"Fechamento"> | Date | string | null
  rejeitadoPor?: Prisma.StringNullableFilter<"Fechamento"> | string | null
  rejeitadoEm?: Prisma.DateTimeNullableFilter<"Fechamento"> | Date | string | null
  motivoRejeicao?: Prisma.StringNullableFilter<"Fechamento"> | string | null
  observacoes?: Prisma.StringNullableFilter<"Fechamento"> | string | null
  createdAt?: Prisma.DateTimeFilter<"Fechamento"> | Date | string
  updatedAt?: Prisma.DateTimeFilter<"Fechamento"> | Date | string
  deletedAt?: Prisma.DateTimeNullableFilter<"Fechamento"> | Date | string | null
  plantao?: Prisma.XOR<Prisma.PlantaoScalarRelationFilter, Prisma.PlantaoWhereInput>
  profissional?: Prisma.XOR<Prisma.ProfissionalScalarRelationFilter, Prisma.ProfissionalWhereInput>
  antecipacao?: Prisma.XOR<Prisma.AntecipacaoNullableScalarRelationFilter, Prisma.AntecipacaoWhereInput> | null
  presencaDiaPlantao?: Prisma.PresencaDiaPlantaoListRelationFilter
}

export type FechamentoOrderByWithRelationInput = {
  id?: Prisma.SortOrder
  uuid?: Prisma.SortOrder
  plantaoId?: Prisma.SortOrder
  profissionalId?: Prisma.SortOrder
  antecipacaoId?: Prisma.SortOrderInput | Prisma.SortOrder
  status?: Prisma.SortOrder
  totalHoras?: Prisma.SortOrderInput | Prisma.SortOrder
  totalValor?: Prisma.SortOrder
  diasTrabalhados?: Prisma.SortOrder
  diasPrevistos?: Prisma.SortOrder
  aprovadoPor?: Prisma.SortOrderInput | Prisma.SortOrder
  aprovadoEm?: Prisma.SortOrderInput | Prisma.SortOrder
  rejeitadoPor?: Prisma.SortOrderInput | Prisma.SortOrder
  rejeitadoEm?: Prisma.SortOrderInput | Prisma.SortOrder
  motivoRejeicao?: Prisma.SortOrderInput | Prisma.SortOrder
  observacoes?: Prisma.SortOrderInput | Prisma.SortOrder
  createdAt?: Prisma.SortOrder
  updatedAt?: Prisma.SortOrder
  deletedAt?: Prisma.SortOrderInput | Prisma.SortOrder
  plantao?: Prisma.PlantaoOrderByWithRelationInput
  profissional?: Prisma.ProfissionalOrderByWithRelationInput
  antecipacao?: Prisma.AntecipacaoOrderByWithRelationInput
  presencaDiaPlantao?: Prisma.PresencaDiaPlantaoOrderByRelationAggregateInput
  _relevance?: Prisma.FechamentoOrderByRelevanceInput
}

export type FechamentoWhereUniqueInput = Prisma.AtLeast<{
  id?: number
  uuid?: string
  AND?: Prisma.FechamentoWhereInput | Prisma.FechamentoWhereInput[]
  OR?: Prisma.FechamentoWhereInput[]
  NOT?: Prisma.FechamentoWhereInput | Prisma.FechamentoWhereInput[]
  plantaoId?: Prisma.IntFilter<"Fechamento"> | number
  profissionalId?: Prisma.IntFilter<"Fechamento"> | number
  antecipacaoId?: Prisma.IntNullableFilter<"Fechamento"> | number | null
  status?: Prisma.StringFilter<"Fechamento"> | string
  totalHoras?: Prisma.FloatNullableFilter<"Fechamento"> | number | null
  totalValor?: Prisma.FloatFilter<"Fechamento"> | number
  diasTrabalhados?: Prisma.IntFilter<"Fechamento"> | number
  diasPrevistos?: Prisma.IntFilter<"Fechamento"> | number
  aprovadoPor?: Prisma.StringNullableFilter<"Fechamento"> | string | null
  aprovadoEm?: Prisma.DateTimeNullableFilter<"Fechamento"> | Date | string | null
  rejeitadoPor?: Prisma.StringNullableFilter<"Fechamento"> | string | null
  rejeitadoEm?: Prisma.DateTimeNullableFilter<"Fechamento"> | Date | string | null
  motivoRejeicao?: Prisma.StringNullableFilter<"Fechamento"> | string | null
  observacoes?: Prisma.StringNullableFilter<"Fechamento"> | string | null
  createdAt?: Prisma.DateTimeFilter<"Fechamento"> | Date | string
  updatedAt?: Prisma.DateTimeFilter<"Fechamento"> | Date | string
  deletedAt?: Prisma.DateTimeNullableFilter<"Fechamento"> | Date | string | null
  plantao?: Prisma.XOR<Prisma.PlantaoScalarRelationFilter, Prisma.PlantaoWhereInput>
  profissional?: Prisma.XOR<Prisma.ProfissionalScalarRelationFilter, Prisma.ProfissionalWhereInput>
  antecipacao?: Prisma.XOR<Prisma.AntecipacaoNullableScalarRelationFilter, Prisma.AntecipacaoWhereInput> | null
  presencaDiaPlantao?: Prisma.PresencaDiaPlantaoListRelationFilter
}, "id" | "uuid">

export type FechamentoOrderByWithAggregationInput = {
  id?: Prisma.SortOrder
  uuid?: Prisma.SortOrder
  plantaoId?: Prisma.SortOrder
  profissionalId?: Prisma.SortOrder
  antecipacaoId?: Prisma.SortOrderInput | Prisma.SortOrder
  status?: Prisma.SortOrder
  totalHoras?: Prisma.SortOrderInput | Prisma.SortOrder
  totalValor?: Prisma.SortOrder
  diasTrabalhados?: Prisma.SortOrder
  diasPrevistos?: Prisma.SortOrder
  aprovadoPor?: Prisma.SortOrderInput | Prisma.SortOrder
  aprovadoEm?: Prisma.SortOrderInput | Prisma.SortOrder
  rejeitadoPor?: Prisma.SortOrderInput | Prisma.SortOrder
  rejeitadoEm?: Prisma.SortOrderInput | Prisma.SortOrder
  motivoRejeicao?: Prisma.SortOrderInput | Prisma.SortOrder
  observacoes?: Prisma.SortOrderInput | Prisma.SortOrder
  createdAt?: Prisma.SortOrder
  updatedAt?: Prisma.SortOrder
  deletedAt?: Prisma.SortOrderInput | Prisma.SortOrder
  _count?: Prisma.FechamentoCountOrderByAggregateInput
  _avg?: Prisma.FechamentoAvgOrderByAggregateInput
  _max?: Prisma.FechamentoMaxOrderByAggregateInput
  _min?: Prisma.FechamentoMinOrderByAggregateInput
  _sum?: Prisma.FechamentoSumOrderByAggregateInput
}

export type FechamentoScalarWhereWithAggregatesInput = {
  AND?: Prisma.FechamentoScalarWhereWithAggregatesInput | Prisma.FechamentoScalarWhereWithAggregatesInput[]
  OR?: Prisma.FechamentoScalarWhereWithAggregatesInput[]
  NOT?: Prisma.FechamentoScalarWhereWithAggregatesInput | Prisma.FechamentoScalarWhereWithAggregatesInput[]
  id?: Prisma.IntWithAggregatesFilter<"Fechamento"> | number
  uuid?: Prisma.StringWithAggregatesFilter<"Fechamento"> | string
  plantaoId?: Prisma.IntWithAggregatesFilter<"Fechamento"> | number
  profissionalId?: Prisma.IntWithAggregatesFilter<"Fechamento"> | number
  antecipacaoId?: Prisma.IntNullableWithAggregatesFilter<"Fechamento"> | number | null
  status?: Prisma.StringWithAggregatesFilter<"Fechamento"> | string
  totalHoras?: Prisma.FloatNullableWithAggregatesFilter<"Fechamento"> | number | null
  totalValor?: Prisma.FloatWithAggregatesFilter<"Fechamento"> | number
  diasTrabalhados?: Prisma.IntWithAggregatesFilter<"Fechamento"> | number
  diasPrevistos?: Prisma.IntWithAggregatesFilter<"Fechamento"> | number
  aprovadoPor?: Prisma.StringNullableWithAggregatesFilter<"Fechamento"> | string | null
  aprovadoEm?: Prisma.DateTimeNullableWithAggregatesFilter<"Fechamento"> | Date | string | null
  rejeitadoPor?: Prisma.StringNullableWithAggregatesFilter<"Fechamento"> | string | null
  rejeitadoEm?: Prisma.DateTimeNullableWithAggregatesFilter<"Fechamento"> | Date | string | null
  motivoRejeicao?: Prisma.StringNullableWithAggregatesFilter<"Fechamento"> | string | null
  observacoes?: Prisma.StringNullableWithAggregatesFilter<"Fechamento"> | string | null
  createdAt?: Prisma.DateTimeWithAggregatesFilter<"Fechamento"> | Date | string
  updatedAt?: Prisma.DateTimeWithAggregatesFilter<"Fechamento"> | Date | string
  deletedAt?: Prisma.DateTimeNullableWithAggregatesFilter<"Fechamento"> | Date | string | null
}

export type FechamentoCreateInput = {
  uuid?: string
  status?: string
  totalHoras?: number | null
  totalValor: number
  diasTrabalhados: number
  diasPrevistos: number
  aprovadoPor?: string | null
  aprovadoEm?: Date | string | null
  rejeitadoPor?: string | null
  rejeitadoEm?: Date | string | null
  motivoRejeicao?: string | null
  observacoes?: string | null
  createdAt?: Date | string
  updatedAt?: Date | string
  deletedAt?: Date | string | null
  plantao: Prisma.PlantaoCreateNestedOneWithoutFechamentosInput
  profissional: Prisma.ProfissionalCreateNestedOneWithoutFechamentosInput
  antecipacao?: Prisma.AntecipacaoCreateNestedOneWithoutFechamentosInput
  presencaDiaPlantao?: Prisma.PresencaDiaPlantaoCreateNestedManyWithoutFechamentoInput
}

export type FechamentoUncheckedCreateInput = {
  id?: number
  uuid?: string
  plantaoId: number
  profissionalId: number
  antecipacaoId?: number | null
  status?: string
  totalHoras?: number | null
  totalValor: number
  diasTrabalhados: number
  diasPrevistos: number
  aprovadoPor?: string | null
  aprovadoEm?: Date | string | null
  rejeitadoPor?: string | null
  rejeitadoEm?: Date | string | null
  motivoRejeicao?: string | null
  observacoes?: string | null
  createdAt?: Date | string
  updatedAt?: Date | string
  deletedAt?: Date | string | null
  presencaDiaPlantao?: Prisma.PresencaDiaPlantaoUncheckedCreateNestedManyWithoutFechamentoInput
}

export type FechamentoUpdateInput = {
  uuid?: Prisma.StringFieldUpdateOperationsInput | string
  status?: Prisma.StringFieldUpdateOperationsInput | string
  totalHoras?: Prisma.NullableFloatFieldUpdateOperationsInput | number | null
  totalValor?: Prisma.FloatFieldUpdateOperationsInput | number
  diasTrabalhados?: Prisma.IntFieldUpdateOperationsInput | number
  diasPrevistos?: Prisma.IntFieldUpdateOperationsInput | number
  aprovadoPor?: Prisma.NullableStringFieldUpdateOperationsInput | string | null
  aprovadoEm?: Prisma.NullableDateTimeFieldUpdateOperationsInput | Date | string | null
  rejeitadoPor?: Prisma.NullableStringFieldUpdateOperationsInput | string | null
  rejeitadoEm?: Prisma.NullableDateTimeFieldUpdateOperationsInput | Date | string | null
  motivoRejeicao?: Prisma.NullableStringFieldUpdateOperationsInput | string | null
  observacoes?: Prisma.NullableStringFieldUpdateOperationsInput | string | null
  createdAt?: Prisma.DateTimeFieldUpdateOperationsInput | Date | string
  updatedAt?: Prisma.DateTimeFieldUpdateOperationsInput | Date | string
  deletedAt?: Prisma.NullableDateTimeFieldUpdateOperationsInput | Date | string | null
  plantao?: Prisma.PlantaoUpdateOneRequiredWithoutFechamentosNestedInput
  profissional?: Prisma.ProfissionalUpdateOneRequiredWithoutFechamentosNestedInput
  antecipacao?: Prisma.AntecipacaoUpdateOneWithoutFechamentosNestedInput
  presencaDiaPlantao?: Prisma.PresencaDiaPlantaoUpdateManyWithoutFechamentoNestedInput
}

export type FechamentoUncheckedUpdateInput = {
  id?: Prisma.IntFieldUpdateOperationsInput | number
  uuid?: Prisma.StringFieldUpdateOperationsInput | string
  plantaoId?: Prisma.IntFieldUpdateOperationsInput | number
  profissionalId?: Prisma.IntFieldUpdateOperationsInput | number
  antecipacaoId?: Prisma.NullableIntFieldUpdateOperationsInput | number | null
  status?: Prisma.StringFieldUpdateOperationsInput | string
  totalHoras?: Prisma.NullableFloatFieldUpdateOperationsInput | number | null
  totalValor?: Prisma.FloatFieldUpdateOperationsInput | number
  diasTrabalhados?: Prisma.IntFieldUpdateOperationsInput | number
  diasPrevistos?: Prisma.IntFieldUpdateOperationsInput | number
  aprovadoPor?: Prisma.NullableStringFieldUpdateOperationsInput | string | null
  aprovadoEm?: Prisma.NullableDateTimeFieldUpdateOperationsInput | Date | string | null
  rejeitadoPor?: Prisma.NullableStringFieldUpdateOperationsInput | string | null
  rejeitadoEm?: Prisma.NullableDateTimeFieldUpdateOperationsInput | Date | string | null
  motivoRejeicao?: Prisma.NullableStringFieldUpdateOperationsInput | string | null
  observacoes?: Prisma.NullableStringFieldUpdateOperationsInput | string | null
  createdAt?: Prisma.DateTimeFieldUpdateOperationsInput | Date | string
  updatedAt?: Prisma.DateTimeFieldUpdateOperationsInput | Date | string
  deletedAt?: Prisma.NullableDateTimeFieldUpdateOperationsInput | Date | string | null
  presencaDiaPlantao?: Prisma.PresencaDiaPlantaoUncheckedUpdateManyWithoutFechamentoNestedInput
}

export type FechamentoCreateManyInput = {
  id?: number
  uuid?: string
  plantaoId: number
  profissionalId: number
  antecipacaoId?: number | null
  status?: string
  totalHoras?: number | null
  totalValor: number
  diasTrabalhados: number
  diasPrevistos: number
  aprovadoPor?: string | null
  aprovadoEm?: Date | string | null
  rejeitadoPor?: string | null
  rejeitadoEm?: Date | string | null
  motivoRejeicao?: string | null
  observacoes?: string | null
  createdAt?: Date | string
  updatedAt?: Date | string
  deletedAt?: Date | string | null
}

export type FechamentoUpdateManyMutationInput = {
  uuid?: Prisma.StringFieldUpdateOperationsInput | string
  status?: Prisma.StringFieldUpdateOperationsInput | string
  totalHoras?: Prisma.NullableFloatFieldUpdateOperationsInput | number | null
  totalValor?: Prisma.FloatFieldUpdateOperationsInput | number
  diasTrabalhados?: Prisma.IntFieldUpdateOperationsInput | number
  diasPrevistos?: Prisma.IntFieldUpdateOperationsInput | number
  aprovadoPor?: Prisma.NullableStringFieldUpdateOperationsInput | string | null
  aprovadoEm?: Prisma.NullableDateTimeFieldUpdateOperationsInput | Date | string | null
  rejeitadoPor?: Prisma.NullableStringFieldUpdateOperationsInput | string | null
  rejeitadoEm?: Prisma.NullableDateTimeFieldUpdateOperationsInput | Date | string | null
  motivoRejeicao?: Prisma.NullableStringFieldUpdateOperationsInput | string | null
  observacoes?: Prisma.NullableStringFieldUpdateOperationsInput | string | null
  createdAt?: Prisma.DateTimeFieldUpdateOperationsInput | Date | string
  updatedAt?: Prisma.DateTimeFieldUpdateOperationsInput | Date | string
  deletedAt?: Prisma.NullableDateTimeFieldUpdateOperationsInput | Date | string | null
}

export type FechamentoUncheckedUpdateManyInput = {
  id?: Prisma.IntFieldUpdateOperationsInput | number
  uuid?: Prisma.StringFieldUpdateOperationsInput | string
  plantaoId?: Prisma.IntFieldUpdateOperationsInput | number
  profissionalId?: Prisma.IntFieldUpdateOperationsInput | number
  antecipacaoId?: Prisma.NullableIntFieldUpdateOperationsInput | number | null
  status?: Prisma.StringFieldUpdateOperationsInput | string
  totalHoras?: Prisma.NullableFloatFieldUpdateOperationsInput | number | null
  totalValor?: Prisma.FloatFieldUpdateOperationsInput | number
  diasTrabalhados?: Prisma.IntFieldUpdateOperationsInput | number
  diasPrevistos?: Prisma.IntFieldUpdateOperationsInput | number
  aprovadoPor?: Prisma.NullableStringFieldUpdateOperationsInput | string | null
  aprovadoEm?: Prisma.NullableDateTimeFieldUpdateOperationsInput | Date | string | null
  rejeitadoPor?: Prisma.NullableStringFieldUpdateOperationsInput | string | null
  rejeitadoEm?: Prisma.NullableDateTimeFieldUpdateOperationsInput | Date | string | null
  motivoRejeicao?: Prisma.NullableStringFieldUpdateOperationsInput | string | null
  observacoes?: Prisma.NullableStringFieldUpdateOperationsInput | string | null
  createdAt?: Prisma.DateTimeFieldUpdateOperationsInput | Date | string
  updatedAt?: Prisma.DateTimeFieldUpdateOperationsInput | Date | string
  deletedAt?: Prisma.NullableDateTimeFieldUpdateOperationsInput | Date | string | null
}

export type FechamentoListRelationFilter = {
  every?: Prisma.FechamentoWhereInput
  some?: Prisma.FechamentoWhereInput
  none?: Prisma.FechamentoWhereInput
}

export type FechamentoOrderByRelationAggregateInput = {
  _count?: Prisma.SortOrder
}

export type FechamentoNullableScalarRelationFilter = {
  is?: Prisma.FechamentoWhereInput | null
  isNot?: Prisma.FechamentoWhereInput | null
}

export type FechamentoOrderByRelevanceInput = {
  fields: Prisma.FechamentoOrderByRelevanceFieldEnum | Prisma.FechamentoOrderByRelevanceFieldEnum[]
  sort: Prisma.SortOrder
  search: string
}

export type FechamentoCountOrderByAggregateInput = {
  id?: Prisma.SortOrder
  uuid?: Prisma.SortOrder
  plantaoId?: Prisma.SortOrder
  profissionalId?: Prisma.SortOrder
  antecipacaoId?: Prisma.SortOrder
  status?: Prisma.SortOrder
  totalHoras?: Prisma.SortOrder
  totalValor?: Prisma.SortOrder
  diasTrabalhados?: Prisma.SortOrder
  diasPrevistos?: Prisma.SortOrder
  aprovadoPor?: Prisma.SortOrder
  aprovadoEm?: Prisma.SortOrder
  rejeitadoPor?: Prisma.SortOrder
  rejeitadoEm?: Prisma.SortOrder
  motivoRejeicao?: Prisma.SortOrder
  observacoes?: Prisma.SortOrder
  createdAt?: Prisma.SortOrder
  updatedAt?: Prisma.SortOrder
  deletedAt?: Prisma.SortOrder
}

export type FechamentoAvgOrderByAggregateInput = {
  id?: Prisma.SortOrder
  plantaoId?: Prisma.SortOrder
  profissionalId?: Prisma.SortOrder
  antecipacaoId?: Prisma.SortOrder
  totalHoras?: Prisma.SortOrder
  totalValor?: Prisma.SortOrder
  diasTrabalhados?: Prisma.SortOrder
  diasPrevistos?: Prisma.SortOrder
}

export type FechamentoMaxOrderByAggregateInput = {
  id?: Prisma.SortOrder
  uuid?: Prisma.SortOrder
  plantaoId?: Prisma.SortOrder
  profissionalId?: Prisma.SortOrder
  antecipacaoId?: Prisma.SortOrder
  status?: Prisma.SortOrder
  totalHoras?: Prisma.SortOrder
  totalValor?: Prisma.SortOrder
  diasTrabalhados?: Prisma.SortOrder
  diasPrevistos?: Prisma.SortOrder
  aprovadoPor?: Prisma.SortOrder
  aprovadoEm?: Prisma.SortOrder
  rejeitadoPor?: Prisma.SortOrder
  rejeitadoEm?: Prisma.SortOrder
  motivoRejeicao?: Prisma.SortOrder
  observacoes?: Prisma.SortOrder
  createdAt?: Prisma.SortOrder
  updatedAt?: Prisma.SortOrder
  deletedAt?: Prisma.SortOrder
}

export type FechamentoMinOrderByAggregateInput = {
  id?: Prisma.SortOrder
  uuid?: Prisma.SortOrder
  plantaoId?: Prisma.SortOrder
  profissionalId?: Prisma.SortOrder
  antecipacaoId?: Prisma.SortOrder
  status?: Prisma.SortOrder
  totalHoras?: Prisma.SortOrder
  totalValor?: Prisma.SortOrder
  diasTrabalhados?: Prisma.SortOrder
  diasPrevistos?: Prisma.SortOrder
  aprovadoPor?: Prisma.SortOrder
  aprovadoEm?: Prisma.SortOrder
  rejeitadoPor?: Prisma.SortOrder
  rejeitadoEm?: Prisma.SortOrder
  motivoRejeicao?: Prisma.SortOrder
  observacoes?: Prisma.SortOrder
  createdAt?: Prisma.SortOrder
  updatedAt?: Prisma.SortOrder
  deletedAt?: Prisma.SortOrder
}

export type FechamentoSumOrderByAggregateInput = {
  id?: Prisma.SortOrder
  plantaoId?: Prisma.SortOrder
  profissionalId?: Prisma.SortOrder
  antecipacaoId?: Prisma.SortOrder
  totalHoras?: Prisma.SortOrder
  totalValor?: Prisma.SortOrder
  diasTrabalhados?: Prisma.SortOrder
  diasPrevistos?: Prisma.SortOrder
}

export type FechamentoCreateNestedManyWithoutProfissionalInput = {
  create?: Prisma.XOR<Prisma.FechamentoCreateWithoutProfissionalInput, Prisma.FechamentoUncheckedCreateWithoutProfissionalInput> | Prisma.FechamentoCreateWithoutProfissionalInput[] | Prisma.FechamentoUncheckedCreateWithoutProfissionalInput[]
  connectOrCreate?: Prisma.FechamentoCreateOrConnectWithoutProfissionalInput | Prisma.FechamentoCreateOrConnectWithoutProfissionalInput[]
  createMany?: Prisma.FechamentoCreateManyProfissionalInputEnvelope
  connect?: Prisma.FechamentoWhereUniqueInput | Prisma.FechamentoWhereUniqueInput[]
}

export type FechamentoUncheckedCreateNestedManyWithoutProfissionalInput = {
  create?: Prisma.XOR<Prisma.FechamentoCreateWithoutProfissionalInput, Prisma.FechamentoUncheckedCreateWithoutProfissionalInput> | Prisma.FechamentoCreateWithoutProfissionalInput[] | Prisma.FechamentoUncheckedCreateWithoutProfissionalInput[]
  connectOrCreate?: Prisma.FechamentoCreateOrConnectWithoutProfissionalInput | Prisma.FechamentoCreateOrConnectWithoutProfissionalInput[]
  createMany?: Prisma.FechamentoCreateManyProfissionalInputEnvelope
  connect?: Prisma.FechamentoWhereUniqueInput | Prisma.FechamentoWhereUniqueInput[]
}

export type FechamentoUpdateManyWithoutProfissionalNestedInput = {
  create?: Prisma.XOR<Prisma.FechamentoCreateWithoutProfissionalInput, Prisma.FechamentoUncheckedCreateWithoutProfissionalInput> | Prisma.FechamentoCreateWithoutProfissionalInput[] | Prisma.FechamentoUncheckedCreateWithoutProfissionalInput[]
  connectOrCreate?: Prisma.FechamentoCreateOrConnectWithoutProfissionalInput | Prisma.FechamentoCreateOrConnectWithoutProfissionalInput[]
  upsert?: Prisma.FechamentoUpsertWithWhereUniqueWithoutProfissionalInput | Prisma.FechamentoUpsertWithWhereUniqueWithoutProfissionalInput[]
  createMany?: Prisma.FechamentoCreateManyProfissionalInputEnvelope
  set?: Prisma.FechamentoWhereUniqueInput | Prisma.FechamentoWhereUniqueInput[]
  disconnect?: Prisma.FechamentoWhereUniqueInput | Prisma.FechamentoWhereUniqueInput[]
  delete?: Prisma.FechamentoWhereUniqueInput | Prisma.FechamentoWhereUniqueInput[]
  connect?: Prisma.FechamentoWhereUniqueInput | Prisma.FechamentoWhereUniqueInput[]
  update?: Prisma.FechamentoUpdateWithWhereUniqueWithoutProfissionalInput | Prisma.FechamentoUpdateWithWhereUniqueWithoutProfissionalInput[]
  updateMany?: Prisma.FechamentoUpdateManyWithWhereWithoutProfissionalInput | Prisma.FechamentoUpdateManyWithWhereWithoutProfissionalInput[]
  deleteMany?: Prisma.FechamentoScalarWhereInput | Prisma.FechamentoScalarWhereInput[]
}

export type FechamentoUncheckedUpdateManyWithoutProfissionalNestedInput = {
  create?: Prisma.XOR<Prisma.FechamentoCreateWithoutProfissionalInput, Prisma.FechamentoUncheckedCreateWithoutProfissionalInput> | Prisma.FechamentoCreateWithoutProfissionalInput[] | Prisma.FechamentoUncheckedCreateWithoutProfissionalInput[]
  connectOrCreate?: Prisma.FechamentoCreateOrConnectWithoutProfissionalInput | Prisma.FechamentoCreateOrConnectWithoutProfissionalInput[]
  upsert?: Prisma.FechamentoUpsertWithWhereUniqueWithoutProfissionalInput | Prisma.FechamentoUpsertWithWhereUniqueWithoutProfissionalInput[]
  createMany?: Prisma.FechamentoCreateManyProfissionalInputEnvelope
  set?: Prisma.FechamentoWhereUniqueInput | Prisma.FechamentoWhereUniqueInput[]
  disconnect?: Prisma.FechamentoWhereUniqueInput | Prisma.FechamentoWhereUniqueInput[]
  delete?: Prisma.FechamentoWhereUniqueInput | Prisma.FechamentoWhereUniqueInput[]
  connect?: Prisma.FechamentoWhereUniqueInput | Prisma.FechamentoWhereUniqueInput[]
  update?: Prisma.FechamentoUpdateWithWhereUniqueWithoutProfissionalInput | Prisma.FechamentoUpdateWithWhereUniqueWithoutProfissionalInput[]
  updateMany?: Prisma.FechamentoUpdateManyWithWhereWithoutProfissionalInput | Prisma.FechamentoUpdateManyWithWhereWithoutProfissionalInput[]
  deleteMany?: Prisma.FechamentoScalarWhereInput | Prisma.FechamentoScalarWhereInput[]
}

export type FechamentoCreateNestedManyWithoutPlantaoInput = {
  create?: Prisma.XOR<Prisma.FechamentoCreateWithoutPlantaoInput, Prisma.FechamentoUncheckedCreateWithoutPlantaoInput> | Prisma.FechamentoCreateWithoutPlantaoInput[] | Prisma.FechamentoUncheckedCreateWithoutPlantaoInput[]
  connectOrCreate?: Prisma.FechamentoCreateOrConnectWithoutPlantaoInput | Prisma.FechamentoCreateOrConnectWithoutPlantaoInput[]
  createMany?: Prisma.FechamentoCreateManyPlantaoInputEnvelope
  connect?: Prisma.FechamentoWhereUniqueInput | Prisma.FechamentoWhereUniqueInput[]
}

export type FechamentoUncheckedCreateNestedManyWithoutPlantaoInput = {
  create?: Prisma.XOR<Prisma.FechamentoCreateWithoutPlantaoInput, Prisma.FechamentoUncheckedCreateWithoutPlantaoInput> | Prisma.FechamentoCreateWithoutPlantaoInput[] | Prisma.FechamentoUncheckedCreateWithoutPlantaoInput[]
  connectOrCreate?: Prisma.FechamentoCreateOrConnectWithoutPlantaoInput | Prisma.FechamentoCreateOrConnectWithoutPlantaoInput[]
  createMany?: Prisma.FechamentoCreateManyPlantaoInputEnvelope
  connect?: Prisma.FechamentoWhereUniqueInput | Prisma.FechamentoWhereUniqueInput[]
}

export type FechamentoUpdateManyWithoutPlantaoNestedInput = {
  create?: Prisma.XOR<Prisma.FechamentoCreateWithoutPlantaoInput, Prisma.FechamentoUncheckedCreateWithoutPlantaoInput> | Prisma.FechamentoCreateWithoutPlantaoInput[] | Prisma.FechamentoUncheckedCreateWithoutPlantaoInput[]
  connectOrCreate?: Prisma.FechamentoCreateOrConnectWithoutPlantaoInput | Prisma.FechamentoCreateOrConnectWithoutPlantaoInput[]
  upsert?: Prisma.FechamentoUpsertWithWhereUniqueWithoutPlantaoInput | Prisma.FechamentoUpsertWithWhereUniqueWithoutPlantaoInput[]
  createMany?: Prisma.FechamentoCreateManyPlantaoInputEnvelope
  set?: Prisma.FechamentoWhereUniqueInput | Prisma.FechamentoWhereUniqueInput[]
  disconnect?: Prisma.FechamentoWhereUniqueInput | Prisma.FechamentoWhereUniqueInput[]
  delete?: Prisma.FechamentoWhereUniqueInput | Prisma.FechamentoWhereUniqueInput[]
  connect?: Prisma.FechamentoWhereUniqueInput | Prisma.FechamentoWhereUniqueInput[]
  update?: Prisma.FechamentoUpdateWithWhereUniqueWithoutPlantaoInput | Prisma.FechamentoUpdateWithWhereUniqueWithoutPlantaoInput[]
  updateMany?: Prisma.FechamentoUpdateManyWithWhereWithoutPlantaoInput | Prisma.FechamentoUpdateManyWithWhereWithoutPlantaoInput[]
  deleteMany?: Prisma.FechamentoScalarWhereInput | Prisma.FechamentoScalarWhereInput[]
}

export type FechamentoUncheckedUpdateManyWithoutPlantaoNestedInput = {
  create?: Prisma.XOR<Prisma.FechamentoCreateWithoutPlantaoInput, Prisma.FechamentoUncheckedCreateWithoutPlantaoInput> | Prisma.FechamentoCreateWithoutPlantaoInput[] | Prisma.FechamentoUncheckedCreateWithoutPlantaoInput[]
  connectOrCreate?: Prisma.FechamentoCreateOrConnectWithoutPlantaoInput | Prisma.FechamentoCreateOrConnectWithoutPlantaoInput[]
  upsert?: Prisma.FechamentoUpsertWithWhereUniqueWithoutPlantaoInput | Prisma.FechamentoUpsertWithWhereUniqueWithoutPlantaoInput[]
  createMany?: Prisma.FechamentoCreateManyPlantaoInputEnvelope
  set?: Prisma.FechamentoWhereUniqueInput | Prisma.FechamentoWhereUniqueInput[]
  disconnect?: Prisma.FechamentoWhereUniqueInput | Prisma.FechamentoWhereUniqueInput[]
  delete?: Prisma.FechamentoWhereUniqueInput | Prisma.FechamentoWhereUniqueInput[]
  connect?: Prisma.FechamentoWhereUniqueInput | Prisma.FechamentoWhereUniqueInput[]
  update?: Prisma.FechamentoUpdateWithWhereUniqueWithoutPlantaoInput | Prisma.FechamentoUpdateWithWhereUniqueWithoutPlantaoInput[]
  updateMany?: Prisma.FechamentoUpdateManyWithWhereWithoutPlantaoInput | Prisma.FechamentoUpdateManyWithWhereWithoutPlantaoInput[]
  deleteMany?: Prisma.FechamentoScalarWhereInput | Prisma.FechamentoScalarWhereInput[]
}

export type FechamentoCreateNestedOneWithoutPresencaDiaPlantaoInput = {
  create?: Prisma.XOR<Prisma.FechamentoCreateWithoutPresencaDiaPlantaoInput, Prisma.FechamentoUncheckedCreateWithoutPresencaDiaPlantaoInput>
  connectOrCreate?: Prisma.FechamentoCreateOrConnectWithoutPresencaDiaPlantaoInput
  connect?: Prisma.FechamentoWhereUniqueInput
}

export type FechamentoUpdateOneWithoutPresencaDiaPlantaoNestedInput = {
  create?: Prisma.XOR<Prisma.FechamentoCreateWithoutPresencaDiaPlantaoInput, Prisma.FechamentoUncheckedCreateWithoutPresencaDiaPlantaoInput>
  connectOrCreate?: Prisma.FechamentoCreateOrConnectWithoutPresencaDiaPlantaoInput
  upsert?: Prisma.FechamentoUpsertWithoutPresencaDiaPlantaoInput
  disconnect?: Prisma.FechamentoWhereInput | boolean
  delete?: Prisma.FechamentoWhereInput | boolean
  connect?: Prisma.FechamentoWhereUniqueInput
  update?: Prisma.XOR<Prisma.XOR<Prisma.FechamentoUpdateToOneWithWhereWithoutPresencaDiaPlantaoInput, Prisma.FechamentoUpdateWithoutPresencaDiaPlantaoInput>, Prisma.FechamentoUncheckedUpdateWithoutPresencaDiaPlantaoInput>
}

export type FechamentoCreateNestedManyWithoutAntecipacaoInput = {
  create?: Prisma.XOR<Prisma.FechamentoCreateWithoutAntecipacaoInput, Prisma.FechamentoUncheckedCreateWithoutAntecipacaoInput> | Prisma.FechamentoCreateWithoutAntecipacaoInput[] | Prisma.FechamentoUncheckedCreateWithoutAntecipacaoInput[]
  connectOrCreate?: Prisma.FechamentoCreateOrConnectWithoutAntecipacaoInput | Prisma.FechamentoCreateOrConnectWithoutAntecipacaoInput[]
  createMany?: Prisma.FechamentoCreateManyAntecipacaoInputEnvelope
  connect?: Prisma.FechamentoWhereUniqueInput | Prisma.FechamentoWhereUniqueInput[]
}

export type FechamentoUncheckedCreateNestedManyWithoutAntecipacaoInput = {
  create?: Prisma.XOR<Prisma.FechamentoCreateWithoutAntecipacaoInput, Prisma.FechamentoUncheckedCreateWithoutAntecipacaoInput> | Prisma.FechamentoCreateWithoutAntecipacaoInput[] | Prisma.FechamentoUncheckedCreateWithoutAntecipacaoInput[]
  connectOrCreate?: Prisma.FechamentoCreateOrConnectWithoutAntecipacaoInput | Prisma.FechamentoCreateOrConnectWithoutAntecipacaoInput[]
  createMany?: Prisma.FechamentoCreateManyAntecipacaoInputEnvelope
  connect?: Prisma.FechamentoWhereUniqueInput | Prisma.FechamentoWhereUniqueInput[]
}

export type FechamentoUpdateManyWithoutAntecipacaoNestedInput = {
  create?: Prisma.XOR<Prisma.FechamentoCreateWithoutAntecipacaoInput, Prisma.FechamentoUncheckedCreateWithoutAntecipacaoInput> | Prisma.FechamentoCreateWithoutAntecipacaoInput[] | Prisma.FechamentoUncheckedCreateWithoutAntecipacaoInput[]
  connectOrCreate?: Prisma.FechamentoCreateOrConnectWithoutAntecipacaoInput | Prisma.FechamentoCreateOrConnectWithoutAntecipacaoInput[]
  upsert?: Prisma.FechamentoUpsertWithWhereUniqueWithoutAntecipacaoInput | Prisma.FechamentoUpsertWithWhereUniqueWithoutAntecipacaoInput[]
  createMany?: Prisma.FechamentoCreateManyAntecipacaoInputEnvelope
  set?: Prisma.FechamentoWhereUniqueInput | Prisma.FechamentoWhereUniqueInput[]
  disconnect?: Prisma.FechamentoWhereUniqueInput | Prisma.FechamentoWhereUniqueInput[]
  delete?: Prisma.FechamentoWhereUniqueInput | Prisma.FechamentoWhereUniqueInput[]
  connect?: Prisma.FechamentoWhereUniqueInput | Prisma.FechamentoWhereUniqueInput[]
  update?: Prisma.FechamentoUpdateWithWhereUniqueWithoutAntecipacaoInput | Prisma.FechamentoUpdateWithWhereUniqueWithoutAntecipacaoInput[]
  updateMany?: Prisma.FechamentoUpdateManyWithWhereWithoutAntecipacaoInput | Prisma.FechamentoUpdateManyWithWhereWithoutAntecipacaoInput[]
  deleteMany?: Prisma.FechamentoScalarWhereInput | Prisma.FechamentoScalarWhereInput[]
}

export type FechamentoUncheckedUpdateManyWithoutAntecipacaoNestedInput = {
  create?: Prisma.XOR<Prisma.FechamentoCreateWithoutAntecipacaoInput, Prisma.FechamentoUncheckedCreateWithoutAntecipacaoInput> | Prisma.FechamentoCreateWithoutAntecipacaoInput[] | Prisma.FechamentoUncheckedCreateWithoutAntecipacaoInput[]
  connectOrCreate?: Prisma.FechamentoCreateOrConnectWithoutAntecipacaoInput | Prisma.FechamentoCreateOrConnectWithoutAntecipacaoInput[]
  upsert?: Prisma.FechamentoUpsertWithWhereUniqueWithoutAntecipacaoInput | Prisma.FechamentoUpsertWithWhereUniqueWithoutAntecipacaoInput[]
  createMany?: Prisma.FechamentoCreateManyAntecipacaoInputEnvelope
  set?: Prisma.FechamentoWhereUniqueInput | Prisma.FechamentoWhereUniqueInput[]
  disconnect?: Prisma.FechamentoWhereUniqueInput | Prisma.FechamentoWhereUniqueInput[]
  delete?: Prisma.FechamentoWhereUniqueInput | Prisma.FechamentoWhereUniqueInput[]
  connect?: Prisma.FechamentoWhereUniqueInput | Prisma.FechamentoWhereUniqueInput[]
  update?: Prisma.FechamentoUpdateWithWhereUniqueWithoutAntecipacaoInput | Prisma.FechamentoUpdateWithWhereUniqueWithoutAntecipacaoInput[]
  updateMany?: Prisma.FechamentoUpdateManyWithWhereWithoutAntecipacaoInput | Prisma.FechamentoUpdateManyWithWhereWithoutAntecipacaoInput[]
  deleteMany?: Prisma.FechamentoScalarWhereInput | Prisma.FechamentoScalarWhereInput[]
}

export type FechamentoCreateWithoutProfissionalInput = {
  uuid?: string
  status?: string
  totalHoras?: number | null
  totalValor: number
  diasTrabalhados: number
  diasPrevistos: number
  aprovadoPor?: string | null
  aprovadoEm?: Date | string | null
  rejeitadoPor?: string | null
  rejeitadoEm?: Date | string | null
  motivoRejeicao?: string | null
  observacoes?: string | null
  createdAt?: Date | string
  updatedAt?: Date | string
  deletedAt?: Date | string | null
  plantao: Prisma.PlantaoCreateNestedOneWithoutFechamentosInput
  antecipacao?: Prisma.AntecipacaoCreateNestedOneWithoutFechamentosInput
  presencaDiaPlantao?: Prisma.PresencaDiaPlantaoCreateNestedManyWithoutFechamentoInput
}

export type FechamentoUncheckedCreateWithoutProfissionalInput = {
  id?: number
  uuid?: string
  plantaoId: number
  antecipacaoId?: number | null
  status?: string
  totalHoras?: number | null
  totalValor: number
  diasTrabalhados: number
  diasPrevistos: number
  aprovadoPor?: string | null
  aprovadoEm?: Date | string | null
  rejeitadoPor?: string | null
  rejeitadoEm?: Date | string | null
  motivoRejeicao?: string | null
  observacoes?: string | null
  createdAt?: Date | string
  updatedAt?: Date | string
  deletedAt?: Date | string | null
  presencaDiaPlantao?: Prisma.PresencaDiaPlantaoUncheckedCreateNestedManyWithoutFechamentoInput
}

export type FechamentoCreateOrConnectWithoutProfissionalInput = {
  where: Prisma.FechamentoWhereUniqueInput
  create: Prisma.XOR<Prisma.FechamentoCreateWithoutProfissionalInput, Prisma.FechamentoUncheckedCreateWithoutProfissionalInput>
}

export type FechamentoCreateManyProfissionalInputEnvelope = {
  data: Prisma.FechamentoCreateManyProfissionalInput | Prisma.FechamentoCreateManyProfissionalInput[]
  skipDuplicates?: boolean
}

export type FechamentoUpsertWithWhereUniqueWithoutProfissionalInput = {
  where: Prisma.FechamentoWhereUniqueInput
  update: Prisma.XOR<Prisma.FechamentoUpdateWithoutProfissionalInput, Prisma.FechamentoUncheckedUpdateWithoutProfissionalInput>
  create: Prisma.XOR<Prisma.FechamentoCreateWithoutProfissionalInput, Prisma.FechamentoUncheckedCreateWithoutProfissionalInput>
}

export type FechamentoUpdateWithWhereUniqueWithoutProfissionalInput = {
  where: Prisma.FechamentoWhereUniqueInput
  data: Prisma.XOR<Prisma.FechamentoUpdateWithoutProfissionalInput, Prisma.FechamentoUncheckedUpdateWithoutProfissionalInput>
}

export type FechamentoUpdateManyWithWhereWithoutProfissionalInput = {
  where: Prisma.FechamentoScalarWhereInput
  data: Prisma.XOR<Prisma.FechamentoUpdateManyMutationInput, Prisma.FechamentoUncheckedUpdateManyWithoutProfissionalInput>
}

export type FechamentoScalarWhereInput = {
  AND?: Prisma.FechamentoScalarWhereInput | Prisma.FechamentoScalarWhereInput[]
  OR?: Prisma.FechamentoScalarWhereInput[]
  NOT?: Prisma.FechamentoScalarWhereInput | Prisma.FechamentoScalarWhereInput[]
  id?: Prisma.IntFilter<"Fechamento"> | number
  uuid?: Prisma.StringFilter<"Fechamento"> | string
  plantaoId?: Prisma.IntFilter<"Fechamento"> | number
  profissionalId?: Prisma.IntFilter<"Fechamento"> | number
  antecipacaoId?: Prisma.IntNullableFilter<"Fechamento"> | number | null
  status?: Prisma.StringFilter<"Fechamento"> | string
  totalHoras?: Prisma.FloatNullableFilter<"Fechamento"> | number | null
  totalValor?: Prisma.FloatFilter<"Fechamento"> | number
  diasTrabalhados?: Prisma.IntFilter<"Fechamento"> | number
  diasPrevistos?: Prisma.IntFilter<"Fechamento"> | number
  aprovadoPor?: Prisma.StringNullableFilter<"Fechamento"> | string | null
  aprovadoEm?: Prisma.DateTimeNullableFilter<"Fechamento"> | Date | string | null
  rejeitadoPor?: Prisma.StringNullableFilter<"Fechamento"> | string | null
  rejeitadoEm?: Prisma.DateTimeNullableFilter<"Fechamento"> | Date | string | null
  motivoRejeicao?: Prisma.StringNullableFilter<"Fechamento"> | string | null
  observacoes?: Prisma.StringNullableFilter<"Fechamento"> | string | null
  createdAt?: Prisma.DateTimeFilter<"Fechamento"> | Date | string
  updatedAt?: Prisma.DateTimeFilter<"Fechamento"> | Date | string
  deletedAt?: Prisma.DateTimeNullableFilter<"Fechamento"> | Date | string | null
}

export type FechamentoCreateWithoutPlantaoInput = {
  uuid?: string
  status?: string
  totalHoras?: number | null
  totalValor: number
  diasTrabalhados: number
  diasPrevistos: number
  aprovadoPor?: string | null
  aprovadoEm?: Date | string | null
  rejeitadoPor?: string | null
  rejeitadoEm?: Date | string | null
  motivoRejeicao?: string | null
  observacoes?: string | null
  createdAt?: Date | string
  updatedAt?: Date | string
  deletedAt?: Date | string | null
  profissional: Prisma.ProfissionalCreateNestedOneWithoutFechamentosInput
  antecipacao?: Prisma.AntecipacaoCreateNestedOneWithoutFechamentosInput
  presencaDiaPlantao?: Prisma.PresencaDiaPlantaoCreateNestedManyWithoutFechamentoInput
}

export type FechamentoUncheckedCreateWithoutPlantaoInput = {
  id?: number
  uuid?: string
  profissionalId: number
  antecipacaoId?: number | null
  status?: string
  totalHoras?: number | null
  totalValor: number
  diasTrabalhados: number
  diasPrevistos: number
  aprovadoPor?: string | null
  aprovadoEm?: Date | string | null
  rejeitadoPor?: string | null
  rejeitadoEm?: Date | string | null
  motivoRejeicao?: string | null
  observacoes?: string | null
  createdAt?: Date | string
  updatedAt?: Date | string
  deletedAt?: Date | string | null
  presencaDiaPlantao?: Prisma.PresencaDiaPlantaoUncheckedCreateNestedManyWithoutFechamentoInput
}

export type FechamentoCreateOrConnectWithoutPlantaoInput = {
  where: Prisma.FechamentoWhereUniqueInput
  create: Prisma.XOR<Prisma.FechamentoCreateWithoutPlantaoInput, Prisma.FechamentoUncheckedCreateWithoutPlantaoInput>
}

export type FechamentoCreateManyPlantaoInputEnvelope = {
  data: Prisma.FechamentoCreateManyPlantaoInput | Prisma.FechamentoCreateManyPlantaoInput[]
  skipDuplicates?: boolean
}

export type FechamentoUpsertWithWhereUniqueWithoutPlantaoInput = {
  where: Prisma.FechamentoWhereUniqueInput
  update: Prisma.XOR<Prisma.FechamentoUpdateWithoutPlantaoInput, Prisma.FechamentoUncheckedUpdateWithoutPlantaoInput>
  create: Prisma.XOR<Prisma.FechamentoCreateWithoutPlantaoInput, Prisma.FechamentoUncheckedCreateWithoutPlantaoInput>
}

export type FechamentoUpdateWithWhereUniqueWithoutPlantaoInput = {
  where: Prisma.FechamentoWhereUniqueInput
  data: Prisma.XOR<Prisma.FechamentoUpdateWithoutPlantaoInput, Prisma.FechamentoUncheckedUpdateWithoutPlantaoInput>
}

export type FechamentoUpdateManyWithWhereWithoutPlantaoInput = {
  where: Prisma.FechamentoScalarWhereInput
  data: Prisma.XOR<Prisma.FechamentoUpdateManyMutationInput, Prisma.FechamentoUncheckedUpdateManyWithoutPlantaoInput>
}

export type FechamentoCreateWithoutPresencaDiaPlantaoInput = {
  uuid?: string
  status?: string
  totalHoras?: number | null
  totalValor: number
  diasTrabalhados: number
  diasPrevistos: number
  aprovadoPor?: string | null
  aprovadoEm?: Date | string | null
  rejeitadoPor?: string | null
  rejeitadoEm?: Date | string | null
  motivoRejeicao?: string | null
  observacoes?: string | null
  createdAt?: Date | string
  updatedAt?: Date | string
  deletedAt?: Date | string | null
  plantao: Prisma.PlantaoCreateNestedOneWithoutFechamentosInput
  profissional: Prisma.ProfissionalCreateNestedOneWithoutFechamentosInput
  antecipacao?: Prisma.AntecipacaoCreateNestedOneWithoutFechamentosInput
}

export type FechamentoUncheckedCreateWithoutPresencaDiaPlantaoInput = {
  id?: number
  uuid?: string
  plantaoId: number
  profissionalId: number
  antecipacaoId?: number | null
  status?: string
  totalHoras?: number | null
  totalValor: number
  diasTrabalhados: number
  diasPrevistos: number
  aprovadoPor?: string | null
  aprovadoEm?: Date | string | null
  rejeitadoPor?: string | null
  rejeitadoEm?: Date | string | null
  motivoRejeicao?: string | null
  observacoes?: string | null
  createdAt?: Date | string
  updatedAt?: Date | string
  deletedAt?: Date | string | null
}

export type FechamentoCreateOrConnectWithoutPresencaDiaPlantaoInput = {
  where: Prisma.FechamentoWhereUniqueInput
  create: Prisma.XOR<Prisma.FechamentoCreateWithoutPresencaDiaPlantaoInput, Prisma.FechamentoUncheckedCreateWithoutPresencaDiaPlantaoInput>
}

export type FechamentoUpsertWithoutPresencaDiaPlantaoInput = {
  update: Prisma.XOR<Prisma.FechamentoUpdateWithoutPresencaDiaPlantaoInput, Prisma.FechamentoUncheckedUpdateWithoutPresencaDiaPlantaoInput>
  create: Prisma.XOR<Prisma.FechamentoCreateWithoutPresencaDiaPlantaoInput, Prisma.FechamentoUncheckedCreateWithoutPresencaDiaPlantaoInput>
  where?: Prisma.FechamentoWhereInput
}

export type FechamentoUpdateToOneWithWhereWithoutPresencaDiaPlantaoInput = {
  where?: Prisma.FechamentoWhereInput
  data: Prisma.XOR<Prisma.FechamentoUpdateWithoutPresencaDiaPlantaoInput, Prisma.FechamentoUncheckedUpdateWithoutPresencaDiaPlantaoInput>
}

export type FechamentoUpdateWithoutPresencaDiaPlantaoInput = {
  uuid?: Prisma.StringFieldUpdateOperationsInput | string
  status?: Prisma.StringFieldUpdateOperationsInput | string
  totalHoras?: Prisma.NullableFloatFieldUpdateOperationsInput | number | null
  totalValor?: Prisma.FloatFieldUpdateOperationsInput | number
  diasTrabalhados?: Prisma.IntFieldUpdateOperationsInput | number
  diasPrevistos?: Prisma.IntFieldUpdateOperationsInput | number
  aprovadoPor?: Prisma.NullableStringFieldUpdateOperationsInput | string | null
  aprovadoEm?: Prisma.NullableDateTimeFieldUpdateOperationsInput | Date | string | null
  rejeitadoPor?: Prisma.NullableStringFieldUpdateOperationsInput | string | null
  rejeitadoEm?: Prisma.NullableDateTimeFieldUpdateOperationsInput | Date | string | null
  motivoRejeicao?: Prisma.NullableStringFieldUpdateOperationsInput | string | null
  observacoes?: Prisma.NullableStringFieldUpdateOperationsInput | string | null
  createdAt?: Prisma.DateTimeFieldUpdateOperationsInput | Date | string
  updatedAt?: Prisma.DateTimeFieldUpdateOperationsInput | Date | string
  deletedAt?: Prisma.NullableDateTimeFieldUpdateOperationsInput | Date | string | null
  plantao?: Prisma.PlantaoUpdateOneRequiredWithoutFechamentosNestedInput
  profissional?: Prisma.ProfissionalUpdateOneRequiredWithoutFechamentosNestedInput
  antecipacao?: Prisma.AntecipacaoUpdateOneWithoutFechamentosNestedInput
}

export type FechamentoUncheckedUpdateWithoutPresencaDiaPlantaoInput = {
  id?: Prisma.IntFieldUpdateOperationsInput | number
  uuid?: Prisma.StringFieldUpdateOperationsInput | string
  plantaoId?: Prisma.IntFieldUpdateOperationsInput | number
  profissionalId?: Prisma.IntFieldUpdateOperationsInput | number
  antecipacaoId?: Prisma.NullableIntFieldUpdateOperationsInput | number | null
  status?: Prisma.StringFieldUpdateOperationsInput | string
  totalHoras?: Prisma.NullableFloatFieldUpdateOperationsInput | number | null
  totalValor?: Prisma.FloatFieldUpdateOperationsInput | number
  diasTrabalhados?: Prisma.IntFieldUpdateOperationsInput | number
  diasPrevistos?: Prisma.IntFieldUpdateOperationsInput | number
  aprovadoPor?: Prisma.NullableStringFieldUpdateOperationsInput | string | null
  aprovadoEm?: Prisma.NullableDateTimeFieldUpdateOperationsInput | Date | string | null
  rejeitadoPor?: Prisma.NullableStringFieldUpdateOperationsInput | string | null
  rejeitadoEm?: Prisma.NullableDateTimeFieldUpdateOperationsInput | Date | string | null
  motivoRejeicao?: Prisma.NullableStringFieldUpdateOperationsInput | string | null
  observacoes?: Prisma.NullableStringFieldUpdateOperationsInput | string | null
  createdAt?: Prisma.DateTimeFieldUpdateOperationsInput | Date | string
  updatedAt?: Prisma.DateTimeFieldUpdateOperationsInput | Date | string
  deletedAt?: Prisma.NullableDateTimeFieldUpdateOperationsInput | Date | string | null
}

export type FechamentoCreateWithoutAntecipacaoInput = {
  uuid?: string
  status?: string
  totalHoras?: number | null
  totalValor: number
  diasTrabalhados: number
  diasPrevistos: number
  aprovadoPor?: string | null
  aprovadoEm?: Date | string | null
  rejeitadoPor?: string | null
  rejeitadoEm?: Date | string | null
  motivoRejeicao?: string | null
  observacoes?: string | null
  createdAt?: Date | string
  updatedAt?: Date | string
  deletedAt?: Date | string | null
  plantao: Prisma.PlantaoCreateNestedOneWithoutFechamentosInput
  profissional: Prisma.ProfissionalCreateNestedOneWithoutFechamentosInput
  presencaDiaPlantao?: Prisma.PresencaDiaPlantaoCreateNestedManyWithoutFechamentoInput
}

export type FechamentoUncheckedCreateWithoutAntecipacaoInput = {
  id?: number
  uuid?: string
  plantaoId: number
  profissionalId: number
  status?: string
  totalHoras?: number | null
  totalValor: number
  diasTrabalhados: number
  diasPrevistos: number
  aprovadoPor?: string | null
  aprovadoEm?: Date | string | null
  rejeitadoPor?: string | null
  rejeitadoEm?: Date | string | null
  motivoRejeicao?: string | null
  observacoes?: string | null
  createdAt?: Date | string
  updatedAt?: Date | string
  deletedAt?: Date | string | null
  presencaDiaPlantao?: Prisma.PresencaDiaPlantaoUncheckedCreateNestedManyWithoutFechamentoInput
}

export type FechamentoCreateOrConnectWithoutAntecipacaoInput = {
  where: Prisma.FechamentoWhereUniqueInput
  create: Prisma.XOR<Prisma.FechamentoCreateWithoutAntecipacaoInput, Prisma.FechamentoUncheckedCreateWithoutAntecipacaoInput>
}

export type FechamentoCreateManyAntecipacaoInputEnvelope = {
  data: Prisma.FechamentoCreateManyAntecipacaoInput | Prisma.FechamentoCreateManyAntecipacaoInput[]
  skipDuplicates?: boolean
}

export type FechamentoUpsertWithWhereUniqueWithoutAntecipacaoInput = {
  where: Prisma.FechamentoWhereUniqueInput
  update: Prisma.XOR<Prisma.FechamentoUpdateWithoutAntecipacaoInput, Prisma.FechamentoUncheckedUpdateWithoutAntecipacaoInput>
  create: Prisma.XOR<Prisma.FechamentoCreateWithoutAntecipacaoInput, Prisma.FechamentoUncheckedCreateWithoutAntecipacaoInput>
}

export type FechamentoUpdateWithWhereUniqueWithoutAntecipacaoInput = {
  where: Prisma.FechamentoWhereUniqueInput
  data: Prisma.XOR<Prisma.FechamentoUpdateWithoutAntecipacaoInput, Prisma.FechamentoUncheckedUpdateWithoutAntecipacaoInput>
}

export type FechamentoUpdateManyWithWhereWithoutAntecipacaoInput = {
  where: Prisma.FechamentoScalarWhereInput
  data: Prisma.XOR<Prisma.FechamentoUpdateManyMutationInput, Prisma.FechamentoUncheckedUpdateManyWithoutAntecipacaoInput>
}

export type FechamentoCreateManyProfissionalInput = {
  id?: number
  uuid?: string
  plantaoId: number
  antecipacaoId?: number | null
  status?: string
  totalHoras?: number | null
  totalValor: number
  diasTrabalhados: number
  diasPrevistos: number
  aprovadoPor?: string | null
  aprovadoEm?: Date | string | null
  rejeitadoPor?: string | null
  rejeitadoEm?: Date | string | null
  motivoRejeicao?: string | null
  observacoes?: string | null
  createdAt?: Date | string
  updatedAt?: Date | string
  deletedAt?: Date | string | null
}

export type FechamentoUpdateWithoutProfissionalInput = {
  uuid?: Prisma.StringFieldUpdateOperationsInput | string
  status?: Prisma.StringFieldUpdateOperationsInput | string
  totalHoras?: Prisma.NullableFloatFieldUpdateOperationsInput | number | null
  totalValor?: Prisma.FloatFieldUpdateOperationsInput | number
  diasTrabalhados?: Prisma.IntFieldUpdateOperationsInput | number
  diasPrevistos?: Prisma.IntFieldUpdateOperationsInput | number
  aprovadoPor?: Prisma.NullableStringFieldUpdateOperationsInput | string | null
  aprovadoEm?: Prisma.NullableDateTimeFieldUpdateOperationsInput | Date | string | null
  rejeitadoPor?: Prisma.NullableStringFieldUpdateOperationsInput | string | null
  rejeitadoEm?: Prisma.NullableDateTimeFieldUpdateOperationsInput | Date | string | null
  motivoRejeicao?: Prisma.NullableStringFieldUpdateOperationsInput | string | null
  observacoes?: Prisma.NullableStringFieldUpdateOperationsInput | string | null
  createdAt?: Prisma.DateTimeFieldUpdateOperationsInput | Date | string
  updatedAt?: Prisma.DateTimeFieldUpdateOperationsInput | Date | string
  deletedAt?: Prisma.NullableDateTimeFieldUpdateOperationsInput | Date | string | null
  plantao?: Prisma.PlantaoUpdateOneRequiredWithoutFechamentosNestedInput
  antecipacao?: Prisma.AntecipacaoUpdateOneWithoutFechamentosNestedInput
  presencaDiaPlantao?: Prisma.PresencaDiaPlantaoUpdateManyWithoutFechamentoNestedInput
}

export type FechamentoUncheckedUpdateWithoutProfissionalInput = {
  id?: Prisma.IntFieldUpdateOperationsInput | number
  uuid?: Prisma.StringFieldUpdateOperationsInput | string
  plantaoId?: Prisma.IntFieldUpdateOperationsInput | number
  antecipacaoId?: Prisma.NullableIntFieldUpdateOperationsInput | number | null
  status?: Prisma.StringFieldUpdateOperationsInput | string
  totalHoras?: Prisma.NullableFloatFieldUpdateOperationsInput | number | null
  totalValor?: Prisma.FloatFieldUpdateOperationsInput | number
  diasTrabalhados?: Prisma.IntFieldUpdateOperationsInput | number
  diasPrevistos?: Prisma.IntFieldUpdateOperationsInput | number
  aprovadoPor?: Prisma.NullableStringFieldUpdateOperationsInput | string | null
  aprovadoEm?: Prisma.NullableDateTimeFieldUpdateOperationsInput | Date | string | null
  rejeitadoPor?: Prisma.NullableStringFieldUpdateOperationsInput | string | null
  rejeitadoEm?: Prisma.NullableDateTimeFieldUpdateOperationsInput | Date | string | null
  motivoRejeicao?: Prisma.NullableStringFieldUpdateOperationsInput | string | null
  observacoes?: Prisma.NullableStringFieldUpdateOperationsInput | string | null
  createdAt?: Prisma.DateTimeFieldUpdateOperationsInput | Date | string
  updatedAt?: Prisma.DateTimeFieldUpdateOperationsInput | Date | string
  deletedAt?: Prisma.NullableDateTimeFieldUpdateOperationsInput | Date | string | null
  presencaDiaPlantao?: Prisma.PresencaDiaPlantaoUncheckedUpdateManyWithoutFechamentoNestedInput
}

export type FechamentoUncheckedUpdateManyWithoutProfissionalInput = {
  id?: Prisma.IntFieldUpdateOperationsInput | number
  uuid?: Prisma.StringFieldUpdateOperationsInput | string
  plantaoId?: Prisma.IntFieldUpdateOperationsInput | number
  antecipacaoId?: Prisma.NullableIntFieldUpdateOperationsInput | number | null
  status?: Prisma.StringFieldUpdateOperationsInput | string
  totalHoras?: Prisma.NullableFloatFieldUpdateOperationsInput | number | null
  totalValor?: Prisma.FloatFieldUpdateOperationsInput | number
  diasTrabalhados?: Prisma.IntFieldUpdateOperationsInput | number
  diasPrevistos?: Prisma.IntFieldUpdateOperationsInput | number
  aprovadoPor?: Prisma.NullableStringFieldUpdateOperationsInput | string | null
  aprovadoEm?: Prisma.NullableDateTimeFieldUpdateOperationsInput | Date | string | null
  rejeitadoPor?: Prisma.NullableStringFieldUpdateOperationsInput | string | null
  rejeitadoEm?: Prisma.NullableDateTimeFieldUpdateOperationsInput | Date | string | null
  motivoRejeicao?: Prisma.NullableStringFieldUpdateOperationsInput | string | null
  observacoes?: Prisma.NullableStringFieldUpdateOperationsInput | string | null
  createdAt?: Prisma.DateTimeFieldUpdateOperationsInput | Date | string
  updatedAt?: Prisma.DateTimeFieldUpdateOperationsInput | Date | string
  deletedAt?: Prisma.NullableDateTimeFieldUpdateOperationsInput | Date | string | null
}

export type FechamentoCreateManyPlantaoInput = {
  id?: number
  uuid?: string
  profissionalId: number
  antecipacaoId?: number | null
  status?: string
  totalHoras?: number | null
  totalValor: number
  diasTrabalhados: number
  diasPrevistos: number
  aprovadoPor?: string | null
  aprovadoEm?: Date | string | null
  rejeitadoPor?: string | null
  rejeitadoEm?: Date | string | null
  motivoRejeicao?: string | null
  observacoes?: string | null
  createdAt?: Date | string
  updatedAt?: Date | string
  deletedAt?: Date | string | null
}

export type FechamentoUpdateWithoutPlantaoInput = {
  uuid?: Prisma.StringFieldUpdateOperationsInput | string
  status?: Prisma.StringFieldUpdateOperationsInput | string
  totalHoras?: Prisma.NullableFloatFieldUpdateOperationsInput | number | null
  totalValor?: Prisma.FloatFieldUpdateOperationsInput | number
  diasTrabalhados?: Prisma.IntFieldUpdateOperationsInput | number
  diasPrevistos?: Prisma.IntFieldUpdateOperationsInput | number
  aprovadoPor?: Prisma.NullableStringFieldUpdateOperationsInput | string | null
  aprovadoEm?: Prisma.NullableDateTimeFieldUpdateOperationsInput | Date | string | null
  rejeitadoPor?: Prisma.NullableStringFieldUpdateOperationsInput | string | null
  rejeitadoEm?: Prisma.NullableDateTimeFieldUpdateOperationsInput | Date | string | null
  motivoRejeicao?: Prisma.NullableStringFieldUpdateOperationsInput | string | null
  observacoes?: Prisma.NullableStringFieldUpdateOperationsInput | string | null
  createdAt?: Prisma.DateTimeFieldUpdateOperationsInput | Date | string
  updatedAt?: Prisma.DateTimeFieldUpdateOperationsInput | Date | string
  deletedAt?: Prisma.NullableDateTimeFieldUpdateOperationsInput | Date | string | null
  profissional?: Prisma.ProfissionalUpdateOneRequiredWithoutFechamentosNestedInput
  antecipacao?: Prisma.AntecipacaoUpdateOneWithoutFechamentosNestedInput
  presencaDiaPlantao?: Prisma.PresencaDiaPlantaoUpdateManyWithoutFechamentoNestedInput
}

export type FechamentoUncheckedUpdateWithoutPlantaoInput = {
  id?: Prisma.IntFieldUpdateOperationsInput | number
  uuid?: Prisma.StringFieldUpdateOperationsInput | string
  profissionalId?: Prisma.IntFieldUpdateOperationsInput | number
  antecipacaoId?: Prisma.NullableIntFieldUpdateOperationsInput | number | null
  status?: Prisma.StringFieldUpdateOperationsInput | string
  totalHoras?: Prisma.NullableFloatFieldUpdateOperationsInput | number | null
  totalValor?: Prisma.FloatFieldUpdateOperationsInput | number
  diasTrabalhados?: Prisma.IntFieldUpdateOperationsInput | number
  diasPrevistos?: Prisma.IntFieldUpdateOperationsInput | number
  aprovadoPor?: Prisma.NullableStringFieldUpdateOperationsInput | string | null
  aprovadoEm?: Prisma.NullableDateTimeFieldUpdateOperationsInput | Date | string | null
  rejeitadoPor?: Prisma.NullableStringFieldUpdateOperationsInput | string | null
  rejeitadoEm?: Prisma.NullableDateTimeFieldUpdateOperationsInput | Date | string | null
  motivoRejeicao?: Prisma.NullableStringFieldUpdateOperationsInput | string | null
  observacoes?: Prisma.NullableStringFieldUpdateOperationsInput | string | null
  createdAt?: Prisma.DateTimeFieldUpdateOperationsInput | Date | string
  updatedAt?: Prisma.DateTimeFieldUpdateOperationsInput | Date | string
  deletedAt?: Prisma.NullableDateTimeFieldUpdateOperationsInput | Date | string | null
  presencaDiaPlantao?: Prisma.PresencaDiaPlantaoUncheckedUpdateManyWithoutFechamentoNestedInput
}

export type FechamentoUncheckedUpdateManyWithoutPlantaoInput = {
  id?: Prisma.IntFieldUpdateOperationsInput | number
  uuid?: Prisma.StringFieldUpdateOperationsInput | string
  profissionalId?: Prisma.IntFieldUpdateOperationsInput | number
  antecipacaoId?: Prisma.NullableIntFieldUpdateOperationsInput | number | null
  status?: Prisma.StringFieldUpdateOperationsInput | string
  totalHoras?: Prisma.NullableFloatFieldUpdateOperationsInput | number | null
  totalValor?: Prisma.FloatFieldUpdateOperationsInput | number
  diasTrabalhados?: Prisma.IntFieldUpdateOperationsInput | number
  diasPrevistos?: Prisma.IntFieldUpdateOperationsInput | number
  aprovadoPor?: Prisma.NullableStringFieldUpdateOperationsInput | string | null
  aprovadoEm?: Prisma.NullableDateTimeFieldUpdateOperationsInput | Date | string | null
  rejeitadoPor?: Prisma.NullableStringFieldUpdateOperationsInput | string | null
  rejeitadoEm?: Prisma.NullableDateTimeFieldUpdateOperationsInput | Date | string | null
  motivoRejeicao?: Prisma.NullableStringFieldUpdateOperationsInput | string | null
  observacoes?: Prisma.NullableStringFieldUpdateOperationsInput | string | null
  createdAt?: Prisma.DateTimeFieldUpdateOperationsInput | Date | string
  updatedAt?: Prisma.DateTimeFieldUpdateOperationsInput | Date | string
  deletedAt?: Prisma.NullableDateTimeFieldUpdateOperationsInput | Date | string | null
}

export type FechamentoCreateManyAntecipacaoInput = {
  id?: number
  uuid?: string
  plantaoId: number
  profissionalId: number
  status?: string
  totalHoras?: number | null
  totalValor: number
  diasTrabalhados: number
  diasPrevistos: number
  aprovadoPor?: string | null
  aprovadoEm?: Date | string | null
  rejeitadoPor?: string | null
  rejeitadoEm?: Date | string | null
  motivoRejeicao?: string | null
  observacoes?: string | null
  createdAt?: Date | string
  updatedAt?: Date | string
  deletedAt?: Date | string | null
}

export type FechamentoUpdateWithoutAntecipacaoInput = {
  uuid?: Prisma.StringFieldUpdateOperationsInput | string
  status?: Prisma.StringFieldUpdateOperationsInput | string
  totalHoras?: Prisma.NullableFloatFieldUpdateOperationsInput | number | null
  totalValor?: Prisma.FloatFieldUpdateOperationsInput | number
  diasTrabalhados?: Prisma.IntFieldUpdateOperationsInput | number
  diasPrevistos?: Prisma.IntFieldUpdateOperationsInput | number
  aprovadoPor?: Prisma.NullableStringFieldUpdateOperationsInput | string | null
  aprovadoEm?: Prisma.NullableDateTimeFieldUpdateOperationsInput | Date | string | null
  rejeitadoPor?: Prisma.NullableStringFieldUpdateOperationsInput | string | null
  rejeitadoEm?: Prisma.NullableDateTimeFieldUpdateOperationsInput | Date | string | null
  motivoRejeicao?: Prisma.NullableStringFieldUpdateOperationsInput | string | null
  observacoes?: Prisma.NullableStringFieldUpdateOperationsInput | string | null
  createdAt?: Prisma.DateTimeFieldUpdateOperationsInput | Date | string
  updatedAt?: Prisma.DateTimeFieldUpdateOperationsInput | Date | string
  deletedAt?: Prisma.NullableDateTimeFieldUpdateOperationsInput | Date | string | null
  plantao?: Prisma.PlantaoUpdateOneRequiredWithoutFechamentosNestedInput
  profissional?: Prisma.ProfissionalUpdateOneRequiredWithoutFechamentosNestedInput
  presencaDiaPlantao?: Prisma.PresencaDiaPlantaoUpdateManyWithoutFechamentoNestedInput
}

export type FechamentoUncheckedUpdateWithoutAntecipacaoInput = {
  id?: Prisma.IntFieldUpdateOperationsInput | number
  uuid?: Prisma.StringFieldUpdateOperationsInput | string
  plantaoId?: Prisma.IntFieldUpdateOperationsInput | number
  profissionalId?: Prisma.IntFieldUpdateOperationsInput | number
  status?: Prisma.StringFieldUpdateOperationsInput | string
  totalHoras?: Prisma.NullableFloatFieldUpdateOperationsInput | number | null
  totalValor?: Prisma.FloatFieldUpdateOperationsInput | number
  diasTrabalhados?: Prisma.IntFieldUpdateOperationsInput | number
  diasPrevistos?: Prisma.IntFieldUpdateOperationsInput | number
  aprovadoPor?: Prisma.NullableStringFieldUpdateOperationsInput | string | null
  aprovadoEm?: Prisma.NullableDateTimeFieldUpdateOperationsInput | Date | string | null
  rejeitadoPor?: Prisma.NullableStringFieldUpdateOperationsInput | string | null
  rejeitadoEm?: Prisma.NullableDateTimeFieldUpdateOperationsInput | Date | string | null
  motivoRejeicao?: Prisma.NullableStringFieldUpdateOperationsInput | string | null
  observacoes?: Prisma.NullableStringFieldUpdateOperationsInput | string | null
  createdAt?: Prisma.DateTimeFieldUpdateOperationsInput | Date | string
  updatedAt?: Prisma.DateTimeFieldUpdateOperationsInput | Date | string
  deletedAt?: Prisma.NullableDateTimeFieldUpdateOperationsInput | Date | string | null
  presencaDiaPlantao?: Prisma.PresencaDiaPlantaoUncheckedUpdateManyWithoutFechamentoNestedInput
}

export type FechamentoUncheckedUpdateManyWithoutAntecipacaoInput = {
  id?: Prisma.IntFieldUpdateOperationsInput | number
  uuid?: Prisma.StringFieldUpdateOperationsInput | string
  plantaoId?: Prisma.IntFieldUpdateOperationsInput | number
  profissionalId?: Prisma.IntFieldUpdateOperationsInput | number
  status?: Prisma.StringFieldUpdateOperationsInput | string
  totalHoras?: Prisma.NullableFloatFieldUpdateOperationsInput | number | null
  totalValor?: Prisma.FloatFieldUpdateOperationsInput | number
  diasTrabalhados?: Prisma.IntFieldUpdateOperationsInput | number
  diasPrevistos?: Prisma.IntFieldUpdateOperationsInput | number
  aprovadoPor?: Prisma.NullableStringFieldUpdateOperationsInput | string | null
  aprovadoEm?: Prisma.NullableDateTimeFieldUpdateOperationsInput | Date | string | null
  rejeitadoPor?: Prisma.NullableStringFieldUpdateOperationsInput | string | null
  rejeitadoEm?: Prisma.NullableDateTimeFieldUpdateOperationsInput | Date | string | null
  motivoRejeicao?: Prisma.NullableStringFieldUpdateOperationsInput | string | null
  observacoes?: Prisma.NullableStringFieldUpdateOperationsInput | string | null
  createdAt?: Prisma.DateTimeFieldUpdateOperationsInput | Date | string
  updatedAt?: Prisma.DateTimeFieldUpdateOperationsInput | Date | string
  deletedAt?: Prisma.NullableDateTimeFieldUpdateOperationsInput | Date | string | null
}


/**
 * Count Type FechamentoCountOutputType
 */

export type FechamentoCountOutputType = {
  presencaDiaPlantao: number
}

export type FechamentoCountOutputTypeSelect<ExtArgs extends runtime.Types.Extensions.InternalArgs = runtime.Types.Extensions.DefaultArgs> = {
  presencaDiaPlantao?: boolean | FechamentoCountOutputTypeCountPresencaDiaPlantaoArgs
}

/**
 * FechamentoCountOutputType without action
 */
export type FechamentoCountOutputTypeDefaultArgs<ExtArgs extends runtime.Types.Extensions.InternalArgs = runtime.Types.Extensions.DefaultArgs> = {
  /**
   * Select specific fields to fetch from the FechamentoCountOutputType
   */
  select?: Prisma.FechamentoCountOutputTypeSelect<ExtArgs> | null
}

/**
 * FechamentoCountOutputType without action
 */
export type FechamentoCountOutputTypeCountPresencaDiaPlantaoArgs<ExtArgs extends runtime.Types.Extensions.InternalArgs = runtime.Types.Extensions.DefaultArgs> = {
  where?: Prisma.PresencaDiaPlantaoWhereInput
}


export type FechamentoSelect<ExtArgs extends runtime.Types.Extensions.InternalArgs = runtime.Types.Extensions.DefaultArgs> = runtime.Types.Extensions.GetSelect<{
  id?: boolean
  uuid?: boolean
  plantaoId?: boolean
  profissionalId?: boolean
  antecipacaoId?: boolean
  status?: boolean
  totalHoras?: boolean
  totalValor?: boolean
  diasTrabalhados?: boolean
  diasPrevistos?: boolean
  aprovadoPor?: boolean
  aprovadoEm?: boolean
  rejeitadoPor?: boolean
  rejeitadoEm?: boolean
  motivoRejeicao?: boolean
  observacoes?: boolean
  createdAt?: boolean
  updatedAt?: boolean
  deletedAt?: boolean
  plantao?: boolean | Prisma.PlantaoDefaultArgs<ExtArgs>
  profissional?: boolean | Prisma.ProfissionalDefaultArgs<ExtArgs>
  antecipacao?: boolean | Prisma.Fechamento$antecipacaoArgs<ExtArgs>
  presencaDiaPlantao?: boolean | Prisma.Fechamento$presencaDiaPlantaoArgs<ExtArgs>
  _count?: boolean | Prisma.FechamentoCountOutputTypeDefaultArgs<ExtArgs>
}, ExtArgs["result"]["fechamento"]>



export type FechamentoSelectScalar = {
  id?: boolean
  uuid?: boolean
  plantaoId?: boolean
  profissionalId?: boolean
  antecipacaoId?: boolean
  status?: boolean
  totalHoras?: boolean
  totalValor?: boolean
  diasTrabalhados?: boolean
  diasPrevistos?: boolean
  aprovadoPor?: boolean
  aprovadoEm?: boolean
  rejeitadoPor?: boolean
  rejeitadoEm?: boolean
  motivoRejeicao?: boolean
  observacoes?: boolean
  createdAt?: boolean
  updatedAt?: boolean
  deletedAt?: boolean
}

export type FechamentoOmit<ExtArgs extends runtime.Types.Extensions.InternalArgs = runtime.Types.Extensions.DefaultArgs> = runtime.Types.Extensions.GetOmit<"id" | "uuid" | "plantaoId" | "profissionalId" | "antecipacaoId" | "status" | "totalHoras" | "totalValor" | "diasTrabalhados" | "diasPrevistos" | "aprovadoPor" | "aprovadoEm" | "rejeitadoPor" | "rejeitadoEm" | "motivoRejeicao" | "observacoes" | "createdAt" | "updatedAt" | "deletedAt", ExtArgs["result"]["fechamento"]>
export type FechamentoInclude<ExtArgs extends runtime.Types.Extensions.InternalArgs = runtime.Types.Extensions.DefaultArgs> = {
  plantao?: boolean | Prisma.PlantaoDefaultArgs<ExtArgs>
  profissional?: boolean | Prisma.ProfissionalDefaultArgs<ExtArgs>
  antecipacao?: boolean | Prisma.Fechamento$antecipacaoArgs<ExtArgs>
  presencaDiaPlantao?: boolean | Prisma.Fechamento$presencaDiaPlantaoArgs<ExtArgs>
  _count?: boolean | Prisma.FechamentoCountOutputTypeDefaultArgs<ExtArgs>
}

export type $FechamentoPayload<ExtArgs extends runtime.Types.Extensions.InternalArgs = runtime.Types.Extensions.DefaultArgs> = {
  name: "Fechamento"
  objects: {
    plantao: Prisma.$PlantaoPayload<ExtArgs>
    profissional: Prisma.$ProfissionalPayload<ExtArgs>
    antecipacao: Prisma.$AntecipacaoPayload<ExtArgs> | null
    presencaDiaPlantao: Prisma.$PresencaDiaPlantaoPayload<ExtArgs>[]
  }
  scalars: runtime.Types.Extensions.GetPayloadResult<{
    id: number
    uuid: string
    plantaoId: number
    profissionalId: number
    antecipacaoId: number | null
    status: string
    totalHoras: number | null
    totalValor: number
    diasTrabalhados: number
    diasPrevistos: number
    aprovadoPor: string | null
    aprovadoEm: Date | null
    rejeitadoPor: string | null
    rejeitadoEm: Date | null
    motivoRejeicao: string | null
    observacoes: string | null
    createdAt: Date
    updatedAt: Date
    deletedAt: Date | null
  }, ExtArgs["result"]["fechamento"]>
  composites: {}
}

export type FechamentoGetPayload<S extends boolean | null | undefined | FechamentoDefaultArgs> = runtime.Types.Result.GetResult<Prisma.$FechamentoPayload, S>

export type FechamentoCountArgs<ExtArgs extends runtime.Types.Extensions.InternalArgs = runtime.Types.Extensions.DefaultArgs> =
  Omit<FechamentoFindManyArgs, 'select' | 'include' | 'distinct' | 'omit'> & {
    select?: FechamentoCountAggregateInputType | true
  }

export interface FechamentoDelegate<ExtArgs extends runtime.Types.Extensions.InternalArgs = runtime.Types.Extensions.DefaultArgs, GlobalOmitOptions = {}> {
  [K: symbol]: { types: Prisma.TypeMap<ExtArgs>['model']['Fechamento'], meta: { name: 'Fechamento' } }
  /**
   * Find zero or one Fechamento that matches the filter.
   * @param {FechamentoFindUniqueArgs} args - Arguments to find a Fechamento
   * @example
   * // Get one Fechamento
   * const fechamento = await prisma.fechamento.findUnique({
   *   where: {
   *     // ... provide filter here
   *   }
   * })
   */
  findUnique<T extends FechamentoFindUniqueArgs>(args: Prisma.SelectSubset<T, FechamentoFindUniqueArgs<ExtArgs>>): Prisma.Prisma__FechamentoClient<runtime.Types.Result.GetResult<Prisma.$FechamentoPayload<ExtArgs>, T, "findUnique", GlobalOmitOptions> | null, null, ExtArgs, GlobalOmitOptions>

  /**
   * Find one Fechamento that matches the filter or throw an error with `error.code='P2025'`
   * if no matches were found.
   * @param {FechamentoFindUniqueOrThrowArgs} args - Arguments to find a Fechamento
   * @example
   * // Get one Fechamento
   * const fechamento = await prisma.fechamento.findUniqueOrThrow({
   *   where: {
   *     // ... provide filter here
   *   }
   * })
   */
  findUniqueOrThrow<T extends FechamentoFindUniqueOrThrowArgs>(args: Prisma.SelectSubset<T, FechamentoFindUniqueOrThrowArgs<ExtArgs>>): Prisma.Prisma__FechamentoClient<runtime.Types.Result.GetResult<Prisma.$FechamentoPayload<ExtArgs>, T, "findUniqueOrThrow", GlobalOmitOptions>, never, ExtArgs, GlobalOmitOptions>

  /**
   * Find the first Fechamento that matches the filter.
   * Note, that providing `undefined` is treated as the value not being there.
   * Read more here: https://pris.ly/d/null-undefined
   * @param {FechamentoFindFirstArgs} args - Arguments to find a Fechamento
   * @example
   * // Get one Fechamento
   * const fechamento = await prisma.fechamento.findFirst({
   *   where: {
   *     // ... provide filter here
   *   }
   * })
   */
  findFirst<T extends FechamentoFindFirstArgs>(args?: Prisma.SelectSubset<T, FechamentoFindFirstArgs<ExtArgs>>): Prisma.Prisma__FechamentoClient<runtime.Types.Result.GetResult<Prisma.$FechamentoPayload<ExtArgs>, T, "findFirst", GlobalOmitOptions> | null, null, ExtArgs, GlobalOmitOptions>

  /**
   * Find the first Fechamento that matches the filter or
   * throw `PrismaKnownClientError` with `P2025` code if no matches were found.
   * Note, that providing `undefined` is treated as the value not being there.
   * Read more here: https://pris.ly/d/null-undefined
   * @param {FechamentoFindFirstOrThrowArgs} args - Arguments to find a Fechamento
   * @example
   * // Get one Fechamento
   * const fechamento = await prisma.fechamento.findFirstOrThrow({
   *   where: {
   *     // ... provide filter here
   *   }
   * })
   */
  findFirstOrThrow<T extends FechamentoFindFirstOrThrowArgs>(args?: Prisma.SelectSubset<T, FechamentoFindFirstOrThrowArgs<ExtArgs>>): Prisma.Prisma__FechamentoClient<runtime.Types.Result.GetResult<Prisma.$FechamentoPayload<ExtArgs>, T, "findFirstOrThrow", GlobalOmitOptions>, never, ExtArgs, GlobalOmitOptions>

  /**
   * Find zero or more Fechamentos that matches the filter.
   * Note, that providing `undefined` is treated as the value not being there.
   * Read more here: https://pris.ly/d/null-undefined
   * @param {FechamentoFindManyArgs} args - Arguments to filter and select certain fields only.
   * @example
   * // Get all Fechamentos
   * const fechamentos = await prisma.fechamento.findMany()
   * 
   * // Get first 10 Fechamentos
   * const fechamentos = await prisma.fechamento.findMany({ take: 10 })
   * 
   * // Only select the `id`
   * const fechamentoWithIdOnly = await prisma.fechamento.findMany({ select: { id: true } })
   * 
   */
  findMany<T extends FechamentoFindManyArgs>(args?: Prisma.SelectSubset<T, FechamentoFindManyArgs<ExtArgs>>): Prisma.PrismaPromise<runtime.Types.Result.GetResult<Prisma.$FechamentoPayload<ExtArgs>, T, "findMany", GlobalOmitOptions>>

  /**
   * Create a Fechamento.
   * @param {FechamentoCreateArgs} args - Arguments to create a Fechamento.
   * @example
   * // Create one Fechamento
   * const Fechamento = await prisma.fechamento.create({
   *   data: {
   *     // ... data to create a Fechamento
   *   }
   * })
   * 
   */
  create<T extends FechamentoCreateArgs>(args: Prisma.SelectSubset<T, FechamentoCreateArgs<ExtArgs>>): Prisma.Prisma__FechamentoClient<runtime.Types.Result.GetResult<Prisma.$FechamentoPayload<ExtArgs>, T, "create", GlobalOmitOptions>, never, ExtArgs, GlobalOmitOptions>

  /**
   * Create many Fechamentos.
   * @param {FechamentoCreateManyArgs} args - Arguments to create many Fechamentos.
   * @example
   * // Create many Fechamentos
   * const fechamento = await prisma.fechamento.createMany({
   *   data: [
   *     // ... provide data here
   *   ]
   * })
   *     
   */
  createMany<T extends FechamentoCreateManyArgs>(args?: Prisma.SelectSubset<T, FechamentoCreateManyArgs<ExtArgs>>): Prisma.PrismaPromise<Prisma.BatchPayload>

  /**
   * Delete a Fechamento.
   * @param {FechamentoDeleteArgs} args - Arguments to delete one Fechamento.
   * @example
   * // Delete one Fechamento
   * const Fechamento = await prisma.fechamento.delete({
   *   where: {
   *     // ... filter to delete one Fechamento
   *   }
   * })
   * 
   */
  delete<T extends FechamentoDeleteArgs>(args: Prisma.SelectSubset<T, FechamentoDeleteArgs<ExtArgs>>): Prisma.Prisma__FechamentoClient<runtime.Types.Result.GetResult<Prisma.$FechamentoPayload<ExtArgs>, T, "delete", GlobalOmitOptions>, never, ExtArgs, GlobalOmitOptions>

  /**
   * Update one Fechamento.
   * @param {FechamentoUpdateArgs} args - Arguments to update one Fechamento.
   * @example
   * // Update one Fechamento
   * const fechamento = await prisma.fechamento.update({
   *   where: {
   *     // ... provide filter here
   *   },
   *   data: {
   *     // ... provide data here
   *   }
   * })
   * 
   */
  update<T extends FechamentoUpdateArgs>(args: Prisma.SelectSubset<T, FechamentoUpdateArgs<ExtArgs>>): Prisma.Prisma__FechamentoClient<runtime.Types.Result.GetResult<Prisma.$FechamentoPayload<ExtArgs>, T, "update", GlobalOmitOptions>, never, ExtArgs, GlobalOmitOptions>

  /**
   * Delete zero or more Fechamentos.
   * @param {FechamentoDeleteManyArgs} args - Arguments to filter Fechamentos to delete.
   * @example
   * // Delete a few Fechamentos
   * const { count } = await prisma.fechamento.deleteMany({
   *   where: {
   *     // ... provide filter here
   *   }
   * })
   * 
   */
  deleteMany<T extends FechamentoDeleteManyArgs>(args?: Prisma.SelectSubset<T, FechamentoDeleteManyArgs<ExtArgs>>): Prisma.PrismaPromise<Prisma.BatchPayload>

  /**
   * Update zero or more Fechamentos.
   * Note, that providing `undefined` is treated as the value not being there.
   * Read more here: https://pris.ly/d/null-undefined
   * @param {FechamentoUpdateManyArgs} args - Arguments to update one or more rows.
   * @example
   * // Update many Fechamentos
   * const fechamento = await prisma.fechamento.updateMany({
   *   where: {
   *     // ... provide filter here
   *   },
   *   data: {
   *     // ... provide data here
   *   }
   * })
   * 
   */
  updateMany<T extends FechamentoUpdateManyArgs>(args: Prisma.SelectSubset<T, FechamentoUpdateManyArgs<ExtArgs>>): Prisma.PrismaPromise<Prisma.BatchPayload>

  /**
   * Create or update one Fechamento.
   * @param {FechamentoUpsertArgs} args - Arguments to update or create a Fechamento.
   * @example
   * // Update or create a Fechamento
   * const fechamento = await prisma.fechamento.upsert({
   *   create: {
   *     // ... data to create a Fechamento
   *   },
   *   update: {
   *     // ... in case it already exists, update
   *   },
   *   where: {
   *     // ... the filter for the Fechamento we want to update
   *   }
   * })
   */
  upsert<T extends FechamentoUpsertArgs>(args: Prisma.SelectSubset<T, FechamentoUpsertArgs<ExtArgs>>): Prisma.Prisma__FechamentoClient<runtime.Types.Result.GetResult<Prisma.$FechamentoPayload<ExtArgs>, T, "upsert", GlobalOmitOptions>, never, ExtArgs, GlobalOmitOptions>


  /**
   * Count the number of Fechamentos.
   * Note, that providing `undefined` is treated as the value not being there.
   * Read more here: https://pris.ly/d/null-undefined
   * @param {FechamentoCountArgs} args - Arguments to filter Fechamentos to count.
   * @example
   * // Count the number of Fechamentos
   * const count = await prisma.fechamento.count({
   *   where: {
   *     // ... the filter for the Fechamentos we want to count
   *   }
   * })
  **/
  count<T extends FechamentoCountArgs>(
    args?: Prisma.Subset<T, FechamentoCountArgs>,
  ): Prisma.PrismaPromise<
    T extends runtime.Types.Utils.Record<'select', any>
      ? T['select'] extends true
        ? number
        : Prisma.GetScalarType<T['select'], FechamentoCountAggregateOutputType>
      : number
  >

  /**
   * Allows you to perform aggregations operations on a Fechamento.
   * Note, that providing `undefined` is treated as the value not being there.
   * Read more here: https://pris.ly/d/null-undefined
   * @param {FechamentoAggregateArgs} args - Select which aggregations you would like to apply and on what fields.
   * @example
   * // Ordered by age ascending
   * // Where email contains prisma.io
   * // Limited to the 10 users
   * const aggregations = await prisma.user.aggregate({
   *   _avg: {
   *     age: true,
   *   },
   *   where: {
   *     email: {
   *       contains: "prisma.io",
   *     },
   *   },
   *   orderBy: {
   *     age: "asc",
   *   },
   *   take: 10,
   * })
  **/
  aggregate<T extends FechamentoAggregateArgs>(args: Prisma.Subset<T, FechamentoAggregateArgs>): Prisma.PrismaPromise<GetFechamentoAggregateType<T>>

  /**
   * Group by Fechamento.
   * Note, that providing `undefined` is treated as the value not being there.
   * Read more here: https://pris.ly/d/null-undefined
   * @param {FechamentoGroupByArgs} args - Group by arguments.
   * @example
   * // Group by city, order by createdAt, get count
   * const result = await prisma.user.groupBy({
   *   by: ['city', 'createdAt'],
   *   orderBy: {
   *     createdAt: true
   *   },
   *   _count: {
   *     _all: true
   *   },
   * })
   * 
  **/
  groupBy<
    T extends FechamentoGroupByArgs,
    HasSelectOrTake extends Prisma.Or<
      Prisma.Extends<'skip', Prisma.Keys<T>>,
      Prisma.Extends<'take', Prisma.Keys<T>>
    >,
    OrderByArg extends Prisma.True extends HasSelectOrTake
      ? { orderBy: FechamentoGroupByArgs['orderBy'] }
      : { orderBy?: FechamentoGroupByArgs['orderBy'] },
    OrderFields extends Prisma.ExcludeUnderscoreKeys<Prisma.Keys<Prisma.MaybeTupleToUnion<T['orderBy']>>>,
    ByFields extends Prisma.MaybeTupleToUnion<T['by']>,
    ByValid extends Prisma.Has<ByFields, OrderFields>,
    HavingFields extends Prisma.GetHavingFields<T['having']>,
    HavingValid extends Prisma.Has<ByFields, HavingFields>,
    ByEmpty extends T['by'] extends never[] ? Prisma.True : Prisma.False,
    InputErrors extends ByEmpty extends Prisma.True
    ? `Error: "by" must not be empty.`
    : HavingValid extends Prisma.False
    ? {
        [P in HavingFields]: P extends ByFields
          ? never
          : P extends string
          ? `Error: Field "${P}" used in "having" needs to be provided in "by".`
          : [
              Error,
              'Field ',
              P,
              ` in "having" needs to be provided in "by"`,
            ]
      }[HavingFields]
    : 'take' extends Prisma.Keys<T>
    ? 'orderBy' extends Prisma.Keys<T>
      ? ByValid extends Prisma.True
        ? {}
        : {
            [P in OrderFields]: P extends ByFields
              ? never
              : `Error: Field "${P}" in "orderBy" needs to be provided in "by"`
          }[OrderFields]
      : 'Error: If you provide "take", you also need to provide "orderBy"'
    : 'skip' extends Prisma.Keys<T>
    ? 'orderBy' extends Prisma.Keys<T>
      ? ByValid extends Prisma.True
        ? {}
        : {
            [P in OrderFields]: P extends ByFields
              ? never
              : `Error: Field "${P}" in "orderBy" needs to be provided in "by"`
          }[OrderFields]
      : 'Error: If you provide "skip", you also need to provide "orderBy"'
    : ByValid extends Prisma.True
    ? {}
    : {
        [P in OrderFields]: P extends ByFields
          ? never
          : `Error: Field "${P}" in "orderBy" needs to be provided in "by"`
      }[OrderFields]
  >(args: Prisma.SubsetIntersection<T, FechamentoGroupByArgs, OrderByArg> & InputErrors): {} extends InputErrors ? GetFechamentoGroupByPayload<T> : Prisma.PrismaPromise<InputErrors>
/**
 * Fields of the Fechamento model
 */
readonly fields: FechamentoFieldRefs;
}

/**
 * The delegate class that acts as a "Promise-like" for Fechamento.
 * Why is this prefixed with `Prisma__`?
 * Because we want to prevent naming conflicts as mentioned in
 * https://github.com/prisma/prisma-client-js/issues/707
 */
export interface Prisma__FechamentoClient<T, Null = never, ExtArgs extends runtime.Types.Extensions.InternalArgs = runtime.Types.Extensions.DefaultArgs, GlobalOmitOptions = {}> extends Prisma.PrismaPromise<T> {
  readonly [Symbol.toStringTag]: "PrismaPromise"
  plantao<T extends Prisma.PlantaoDefaultArgs<ExtArgs> = {}>(args?: Prisma.Subset<T, Prisma.PlantaoDefaultArgs<ExtArgs>>): Prisma.Prisma__PlantaoClient<runtime.Types.Result.GetResult<Prisma.$PlantaoPayload<ExtArgs>, T, "findUniqueOrThrow", GlobalOmitOptions> | Null, Null, ExtArgs, GlobalOmitOptions>
  profissional<T extends Prisma.ProfissionalDefaultArgs<ExtArgs> = {}>(args?: Prisma.Subset<T, Prisma.ProfissionalDefaultArgs<ExtArgs>>): Prisma.Prisma__ProfissionalClient<runtime.Types.Result.GetResult<Prisma.$ProfissionalPayload<ExtArgs>, T, "findUniqueOrThrow", GlobalOmitOptions> | Null, Null, ExtArgs, GlobalOmitOptions>
  antecipacao<T extends Prisma.Fechamento$antecipacaoArgs<ExtArgs> = {}>(args?: Prisma.Subset<T, Prisma.Fechamento$antecipacaoArgs<ExtArgs>>): Prisma.Prisma__AntecipacaoClient<runtime.Types.Result.GetResult<Prisma.$AntecipacaoPayload<ExtArgs>, T, "findUniqueOrThrow", GlobalOmitOptions> | null, null, ExtArgs, GlobalOmitOptions>
  presencaDiaPlantao<T extends Prisma.Fechamento$presencaDiaPlantaoArgs<ExtArgs> = {}>(args?: Prisma.Subset<T, Prisma.Fechamento$presencaDiaPlantaoArgs<ExtArgs>>): Prisma.PrismaPromise<runtime.Types.Result.GetResult<Prisma.$PresencaDiaPlantaoPayload<ExtArgs>, T, "findMany", GlobalOmitOptions> | Null>
  /**
   * Attaches callbacks for the resolution and/or rejection of the Promise.
   * @param onfulfilled The callback to execute when the Promise is resolved.
   * @param onrejected The callback to execute when the Promise is rejected.
   * @returns A Promise for the completion of which ever callback is executed.
   */
  then<TResult1 = T, TResult2 = never>(onfulfilled?: ((value: T) => TResult1 | PromiseLike<TResult1>) | undefined | null, onrejected?: ((reason: any) => TResult2 | PromiseLike<TResult2>) | undefined | null): runtime.Types.Utils.JsPromise<TResult1 | TResult2>
  /**
   * Attaches a callback for only the rejection of the Promise.
   * @param onrejected The callback to execute when the Promise is rejected.
   * @returns A Promise for the completion of the callback.
   */
  catch<TResult = never>(onrejected?: ((reason: any) => TResult | PromiseLike<TResult>) | undefined | null): runtime.Types.Utils.JsPromise<T | TResult>
  /**
   * Attaches a callback that is invoked when the Promise is settled (fulfilled or rejected). The
   * resolved value cannot be modified from the callback.
   * @param onfinally The callback to execute when the Promise is settled (fulfilled or rejected).
   * @returns A Promise for the completion of the callback.
   */
  finally(onfinally?: (() => void) | undefined | null): runtime.Types.Utils.JsPromise<T>
}




/**
 * Fields of the Fechamento model
 */
export interface FechamentoFieldRefs {
  readonly id: Prisma.FieldRef<"Fechamento", 'Int'>
  readonly uuid: Prisma.FieldRef<"Fechamento", 'String'>
  readonly plantaoId: Prisma.FieldRef<"Fechamento", 'Int'>
  readonly profissionalId: Prisma.FieldRef<"Fechamento", 'Int'>
  readonly antecipacaoId: Prisma.FieldRef<"Fechamento", 'Int'>
  readonly status: Prisma.FieldRef<"Fechamento", 'String'>
  readonly totalHoras: Prisma.FieldRef<"Fechamento", 'Float'>
  readonly totalValor: Prisma.FieldRef<"Fechamento", 'Float'>
  readonly diasTrabalhados: Prisma.FieldRef<"Fechamento", 'Int'>
  readonly diasPrevistos: Prisma.FieldRef<"Fechamento", 'Int'>
  readonly aprovadoPor: Prisma.FieldRef<"Fechamento", 'String'>
  readonly aprovadoEm: Prisma.FieldRef<"Fechamento", 'DateTime'>
  readonly rejeitadoPor: Prisma.FieldRef<"Fechamento", 'String'>
  readonly rejeitadoEm: Prisma.FieldRef<"Fechamento", 'DateTime'>
  readonly motivoRejeicao: Prisma.FieldRef<"Fechamento", 'String'>
  readonly observacoes: Prisma.FieldRef<"Fechamento", 'String'>
  readonly createdAt: Prisma.FieldRef<"Fechamento", 'DateTime'>
  readonly updatedAt: Prisma.FieldRef<"Fechamento", 'DateTime'>
  readonly deletedAt: Prisma.FieldRef<"Fechamento", 'DateTime'>
}
    

// Custom InputTypes
/**
 * Fechamento findUnique
 */
export type FechamentoFindUniqueArgs<ExtArgs extends runtime.Types.Extensions.InternalArgs = runtime.Types.Extensions.DefaultArgs> = {
  /**
   * Select specific fields to fetch from the Fechamento
   */
  select?: Prisma.FechamentoSelect<ExtArgs> | null
  /**
   * Omit specific fields from the Fechamento
   */
  omit?: Prisma.FechamentoOmit<ExtArgs> | null
  /**
   * Choose, which related nodes to fetch as well
   */
  include?: Prisma.FechamentoInclude<ExtArgs> | null
  /**
   * Filter, which Fechamento to fetch.
   */
  where: Prisma.FechamentoWhereUniqueInput
}

/**
 * Fechamento findUniqueOrThrow
 */
export type FechamentoFindUniqueOrThrowArgs<ExtArgs extends runtime.Types.Extensions.InternalArgs = runtime.Types.Extensions.DefaultArgs> = {
  /**
   * Select specific fields to fetch from the Fechamento
   */
  select?: Prisma.FechamentoSelect<ExtArgs> | null
  /**
   * Omit specific fields from the Fechamento
   */
  omit?: Prisma.FechamentoOmit<ExtArgs> | null
  /**
   * Choose, which related nodes to fetch as well
   */
  include?: Prisma.FechamentoInclude<ExtArgs> | null
  /**
   * Filter, which Fechamento to fetch.
   */
  where: Prisma.FechamentoWhereUniqueInput
}

/**
 * Fechamento findFirst
 */
export type FechamentoFindFirstArgs<ExtArgs extends runtime.Types.Extensions.InternalArgs = runtime.Types.Extensions.DefaultArgs> = {
  /**
   * Select specific fields to fetch from the Fechamento
   */
  select?: Prisma.FechamentoSelect<ExtArgs> | null
  /**
   * Omit specific fields from the Fechamento
   */
  omit?: Prisma.FechamentoOmit<ExtArgs> | null
  /**
   * Choose, which related nodes to fetch as well
   */
  include?: Prisma.FechamentoInclude<ExtArgs> | null
  /**
   * Filter, which Fechamento to fetch.
   */
  where?: Prisma.FechamentoWhereInput
  /**
   * {@link https://www.prisma.io/docs/concepts/components/prisma-client/sorting Sorting Docs}
   * 
   * Determine the order of Fechamentos to fetch.
   */
  orderBy?: Prisma.FechamentoOrderByWithRelationInput | Prisma.FechamentoOrderByWithRelationInput[]
  /**
   * {@link https://www.prisma.io/docs/concepts/components/prisma-client/pagination#cursor-based-pagination Cursor Docs}
   * 
   * Sets the position for searching for Fechamentos.
   */
  cursor?: Prisma.FechamentoWhereUniqueInput
  /**
   * {@link https://www.prisma.io/docs/concepts/components/prisma-client/pagination Pagination Docs}
   * 
   * Take `±n` Fechamentos from the position of the cursor.
   */
  take?: number
  /**
   * {@link https://www.prisma.io/docs/concepts/components/prisma-client/pagination Pagination Docs}
   * 
   * Skip the first `n` Fechamentos.
   */
  skip?: number
  /**
   * {@link https://www.prisma.io/docs/concepts/components/prisma-client/distinct Distinct Docs}
   * 
   * Filter by unique combinations of Fechamentos.
   */
  distinct?: Prisma.FechamentoScalarFieldEnum | Prisma.FechamentoScalarFieldEnum[]
}

/**
 * Fechamento findFirstOrThrow
 */
export type FechamentoFindFirstOrThrowArgs<ExtArgs extends runtime.Types.Extensions.InternalArgs = runtime.Types.Extensions.DefaultArgs> = {
  /**
   * Select specific fields to fetch from the Fechamento
   */
  select?: Prisma.FechamentoSelect<ExtArgs> | null
  /**
   * Omit specific fields from the Fechamento
   */
  omit?: Prisma.FechamentoOmit<ExtArgs> | null
  /**
   * Choose, which related nodes to fetch as well
   */
  include?: Prisma.FechamentoInclude<ExtArgs> | null
  /**
   * Filter, which Fechamento to fetch.
   */
  where?: Prisma.FechamentoWhereInput
  /**
   * {@link https://www.prisma.io/docs/concepts/components/prisma-client/sorting Sorting Docs}
   * 
   * Determine the order of Fechamentos to fetch.
   */
  orderBy?: Prisma.FechamentoOrderByWithRelationInput | Prisma.FechamentoOrderByWithRelationInput[]
  /**
   * {@link https://www.prisma.io/docs/concepts/components/prisma-client/pagination#cursor-based-pagination Cursor Docs}
   * 
   * Sets the position for searching for Fechamentos.
   */
  cursor?: Prisma.FechamentoWhereUniqueInput
  /**
   * {@link https://www.prisma.io/docs/concepts/components/prisma-client/pagination Pagination Docs}
   * 
   * Take `±n` Fechamentos from the position of the cursor.
   */
  take?: number
  /**
   * {@link https://www.prisma.io/docs/concepts/components/prisma-client/pagination Pagination Docs}
   * 
   * Skip the first `n` Fechamentos.
   */
  skip?: number
  /**
   * {@link https://www.prisma.io/docs/concepts/components/prisma-client/distinct Distinct Docs}
   * 
   * Filter by unique combinations of Fechamentos.
   */
  distinct?: Prisma.FechamentoScalarFieldEnum | Prisma.FechamentoScalarFieldEnum[]
}

/**
 * Fechamento findMany
 */
export type FechamentoFindManyArgs<ExtArgs extends runtime.Types.Extensions.InternalArgs = runtime.Types.Extensions.DefaultArgs> = {
  /**
   * Select specific fields to fetch from the Fechamento
   */
  select?: Prisma.FechamentoSelect<ExtArgs> | null
  /**
   * Omit specific fields from the Fechamento
   */
  omit?: Prisma.FechamentoOmit<ExtArgs> | null
  /**
   * Choose, which related nodes to fetch as well
   */
  include?: Prisma.FechamentoInclude<ExtArgs> | null
  /**
   * Filter, which Fechamentos to fetch.
   */
  where?: Prisma.FechamentoWhereInput
  /**
   * {@link https://www.prisma.io/docs/concepts/components/prisma-client/sorting Sorting Docs}
   * 
   * Determine the order of Fechamentos to fetch.
   */
  orderBy?: Prisma.FechamentoOrderByWithRelationInput | Prisma.FechamentoOrderByWithRelationInput[]
  /**
   * {@link https://www.prisma.io/docs/concepts/components/prisma-client/pagination#cursor-based-pagination Cursor Docs}
   * 
   * Sets the position for listing Fechamentos.
   */
  cursor?: Prisma.FechamentoWhereUniqueInput
  /**
   * {@link https://www.prisma.io/docs/concepts/components/prisma-client/pagination Pagination Docs}
   * 
   * Take `±n` Fechamentos from the position of the cursor.
   */
  take?: number
  /**
   * {@link https://www.prisma.io/docs/concepts/components/prisma-client/pagination Pagination Docs}
   * 
   * Skip the first `n` Fechamentos.
   */
  skip?: number
  distinct?: Prisma.FechamentoScalarFieldEnum | Prisma.FechamentoScalarFieldEnum[]
}

/**
 * Fechamento create
 */
export type FechamentoCreateArgs<ExtArgs extends runtime.Types.Extensions.InternalArgs = runtime.Types.Extensions.DefaultArgs> = {
  /**
   * Select specific fields to fetch from the Fechamento
   */
  select?: Prisma.FechamentoSelect<ExtArgs> | null
  /**
   * Omit specific fields from the Fechamento
   */
  omit?: Prisma.FechamentoOmit<ExtArgs> | null
  /**
   * Choose, which related nodes to fetch as well
   */
  include?: Prisma.FechamentoInclude<ExtArgs> | null
  /**
   * The data needed to create a Fechamento.
   */
  data: Prisma.XOR<Prisma.FechamentoCreateInput, Prisma.FechamentoUncheckedCreateInput>
}

/**
 * Fechamento createMany
 */
export type FechamentoCreateManyArgs<ExtArgs extends runtime.Types.Extensions.InternalArgs = runtime.Types.Extensions.DefaultArgs> = {
  /**
   * The data used to create many Fechamentos.
   */
  data: Prisma.FechamentoCreateManyInput | Prisma.FechamentoCreateManyInput[]
  skipDuplicates?: boolean
}

/**
 * Fechamento update
 */
export type FechamentoUpdateArgs<ExtArgs extends runtime.Types.Extensions.InternalArgs = runtime.Types.Extensions.DefaultArgs> = {
  /**
   * Select specific fields to fetch from the Fechamento
   */
  select?: Prisma.FechamentoSelect<ExtArgs> | null
  /**
   * Omit specific fields from the Fechamento
   */
  omit?: Prisma.FechamentoOmit<ExtArgs> | null
  /**
   * Choose, which related nodes to fetch as well
   */
  include?: Prisma.FechamentoInclude<ExtArgs> | null
  /**
   * The data needed to update a Fechamento.
   */
  data: Prisma.XOR<Prisma.FechamentoUpdateInput, Prisma.FechamentoUncheckedUpdateInput>
  /**
   * Choose, which Fechamento to update.
   */
  where: Prisma.FechamentoWhereUniqueInput
}

/**
 * Fechamento updateMany
 */
export type FechamentoUpdateManyArgs<ExtArgs extends runtime.Types.Extensions.InternalArgs = runtime.Types.Extensions.DefaultArgs> = {
  /**
   * The data used to update Fechamentos.
   */
  data: Prisma.XOR<Prisma.FechamentoUpdateManyMutationInput, Prisma.FechamentoUncheckedUpdateManyInput>
  /**
   * Filter which Fechamentos to update
   */
  where?: Prisma.FechamentoWhereInput
  /**
   * Limit how many Fechamentos to update.
   */
  limit?: number
}

/**
 * Fechamento upsert
 */
export type FechamentoUpsertArgs<ExtArgs extends runtime.Types.Extensions.InternalArgs = runtime.Types.Extensions.DefaultArgs> = {
  /**
   * Select specific fields to fetch from the Fechamento
   */
  select?: Prisma.FechamentoSelect<ExtArgs> | null
  /**
   * Omit specific fields from the Fechamento
   */
  omit?: Prisma.FechamentoOmit<ExtArgs> | null
  /**
   * Choose, which related nodes to fetch as well
   */
  include?: Prisma.FechamentoInclude<ExtArgs> | null
  /**
   * The filter to search for the Fechamento to update in case it exists.
   */
  where: Prisma.FechamentoWhereUniqueInput
  /**
   * In case the Fechamento found by the `where` argument doesn't exist, create a new Fechamento with this data.
   */
  create: Prisma.XOR<Prisma.FechamentoCreateInput, Prisma.FechamentoUncheckedCreateInput>
  /**
   * In case the Fechamento was found with the provided `where` argument, update it with this data.
   */
  update: Prisma.XOR<Prisma.FechamentoUpdateInput, Prisma.FechamentoUncheckedUpdateInput>
}

/**
 * Fechamento delete
 */
export type FechamentoDeleteArgs<ExtArgs extends runtime.Types.Extensions.InternalArgs = runtime.Types.Extensions.DefaultArgs> = {
  /**
   * Select specific fields to fetch from the Fechamento
   */
  select?: Prisma.FechamentoSelect<ExtArgs> | null
  /**
   * Omit specific fields from the Fechamento
   */
  omit?: Prisma.FechamentoOmit<ExtArgs> | null
  /**
   * Choose, which related nodes to fetch as well
   */
  include?: Prisma.FechamentoInclude<ExtArgs> | null
  /**
   * Filter which Fechamento to delete.
   */
  where: Prisma.FechamentoWhereUniqueInput
}

/**
 * Fechamento deleteMany
 */
export type FechamentoDeleteManyArgs<ExtArgs extends runtime.Types.Extensions.InternalArgs = runtime.Types.Extensions.DefaultArgs> = {
  /**
   * Filter which Fechamentos to delete
   */
  where?: Prisma.FechamentoWhereInput
  /**
   * Limit how many Fechamentos to delete.
   */
  limit?: number
}

/**
 * Fechamento.antecipacao
 */
export type Fechamento$antecipacaoArgs<ExtArgs extends runtime.Types.Extensions.InternalArgs = runtime.Types.Extensions.DefaultArgs> = {
  /**
   * Select specific fields to fetch from the Antecipacao
   */
  select?: Prisma.AntecipacaoSelect<ExtArgs> | null
  /**
   * Omit specific fields from the Antecipacao
   */
  omit?: Prisma.AntecipacaoOmit<ExtArgs> | null
  /**
   * Choose, which related nodes to fetch as well
   */
  include?: Prisma.AntecipacaoInclude<ExtArgs> | null
  where?: Prisma.AntecipacaoWhereInput
}

/**
 * Fechamento.presencaDiaPlantao
 */
export type Fechamento$presencaDiaPlantaoArgs<ExtArgs extends runtime.Types.Extensions.InternalArgs = runtime.Types.Extensions.DefaultArgs> = {
  /**
   * Select specific fields to fetch from the PresencaDiaPlantao
   */
  select?: Prisma.PresencaDiaPlantaoSelect<ExtArgs> | null
  /**
   * Omit specific fields from the PresencaDiaPlantao
   */
  omit?: Prisma.PresencaDiaPlantaoOmit<ExtArgs> | null
  /**
   * Choose, which related nodes to fetch as well
   */
  include?: Prisma.PresencaDiaPlantaoInclude<ExtArgs> | null
  where?: Prisma.PresencaDiaPlantaoWhereInput
  orderBy?: Prisma.PresencaDiaPlantaoOrderByWithRelationInput | Prisma.PresencaDiaPlantaoOrderByWithRelationInput[]
  cursor?: Prisma.PresencaDiaPlantaoWhereUniqueInput
  take?: number
  skip?: number
  distinct?: Prisma.PresencaDiaPlantaoScalarFieldEnum | Prisma.PresencaDiaPlantaoScalarFieldEnum[]
}

/**
 * Fechamento without action
 */
export type FechamentoDefaultArgs<ExtArgs extends runtime.Types.Extensions.InternalArgs = runtime.Types.Extensions.DefaultArgs> = {
  /**
   * Select specific fields to fetch from the Fechamento
   */
  select?: Prisma.FechamentoSelect<ExtArgs> | null
  /**
   * Omit specific fields from the Fechamento
   */
  omit?: Prisma.FechamentoOmit<ExtArgs> | null
  /**
   * Choose, which related nodes to fetch as well
   */
  include?: Prisma.FechamentoInclude<ExtArgs> | null
}
