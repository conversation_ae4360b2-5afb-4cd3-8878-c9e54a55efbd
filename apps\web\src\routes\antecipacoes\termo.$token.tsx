import { createFileRoute, useNavigate } from "@tanstack/react-router";
import { useMutation, useQuery } from "@tanstack/react-query";
import { api } from "@/lib/api";
import { Button } from "@/components/ui/button";
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from "@/components/ui/card";
import { Checkbox } from "@/components/ui/checkbox";
import { Alert, AlertDescription } from "@/components/ui/alert";
import { Badge } from "@/components/ui/badge";
import { Separator } from "@/components/ui/separator";
import {
  FileText,
  Calendar,
  Building2,
  User,
  MapPin,
  DollarSign,
  Percent,
  Calculator,
  AlertCircle,
  CheckCircle,
  Loader2,
  FileSignature,
} from "lucide-react";
import { useState } from "react";
import { toast } from "sonner";
import { formatCurrency, formatDate } from "@/lib/utils";

export const Route = createFileRoute("/antecipacoes/termo/$token")({
  component: TermoAssinatura,
});

interface TermoData {
  antecipacao: {
    numeroTermo: string;
    dataEmissao: string;
    hospital: string;
    hospitalCNPJ: string;
    profissional: string;
    profissionalCPF: string;
    localAtendimento: string;
    periodo: string;
    valorTotal: number;
    percentual: number;
    taxaAntecipacao: number;
    valorLiquido: number;
    dataPagamentoPrevista: string;
    fechamentos: Array<{
      uuid: string;
      valor: number;
    }>;
  };
  termoAssinado: boolean;
  profissionalId?: string;
  isAuthenticated?: boolean;
  isProfissional?: boolean;
  isAdmin?: boolean;
}

function TermoAssinatura() {
  const navigate = useNavigate();
  const { token } = Route.useParams();
  const [aceite, setAceite] = useState(false);

  // Buscar dados do termo
  const { data, isLoading, error } = useQuery({
    queryKey: ["termo-antecipacao", token],
    queryFn: () => api.get<TermoData>(`/antecipacoes/termo/${token}`),
    retry: false,
  });

  // Mutation para assinar o termo
  const assinaturaMutation = useMutation({
    mutationFn: () => api.post(`/antecipacoes/termo/${token}/assinar`, { aceite: true }),
    onSuccess: () => {
      toast.success("Termo assinado com sucesso! Você receberá um email de confirmação.");
      navigate({ to: "/login" });
    },
    onError: (error: any) => {
      toast.error(error.response?.data?.error || "Erro ao assinar o termo");
    },
  });

  if (isLoading) {
    return (
      <div className="flex items-center justify-center min-h-screen">
        <Loader2 className="h-8 w-8 animate-spin text-primary" />
      </div>
    );
  }

  if (error) {
    const errorMessage = (error as any).response?.data?.error || "Erro ao carregar o termo";
    const isExpired = errorMessage.includes("expirado");
    const isAlreadySigned = errorMessage.includes("já foi assinado");

    return (
      <div className="container max-w-4xl mx-auto p-8">
        <Alert variant={isAlreadySigned ? "default" : "destructive"}>
          <AlertCircle className="h-4 w-4" />
          <AlertDescription className="text-lg">{errorMessage}</AlertDescription>
        </Alert>
        {isExpired && (
          <p className="mt-4 text-muted-foreground">
            Entre em contato com o setor financeiro para solicitar um novo link.
          </p>
        )}
      </div>
    );
  }

  if (!data) return null;

  const { antecipacao, isAuthenticated, isProfissional, isAdmin } = data;

  // Verificar se pode assinar o termo
  const podeAssinar = isProfissional && !data.termoAssinado;
  const podeVisualizar = isAdmin || isProfissional;

  // Se não está autenticado, mostrar mensagem para fazer login
  if (!isAuthenticated) {
    return (
      <div className="container max-w-4xl mx-auto p-8">
        <Alert>
          <AlertCircle className="h-4 w-4" />
          <AlertDescription className="text-lg">
            Para visualizar e assinar este termo, você precisa fazer login com a conta do profissional vinculado.
          </AlertDescription>
        </Alert>
        <div className="mt-4 flex justify-center">
          <Button onClick={() => navigate({ to: "/login" })}>Fazer Login</Button>
        </div>
      </div>
    );
  }

  // Se está autenticado mas não tem permissão
  if (!podeVisualizar) {
    return (
      <div className="container max-w-4xl mx-auto p-8">
        <Alert variant="destructive">
          <AlertCircle className="h-4 w-4" />
          <AlertDescription className="text-lg">
            Você não tem permissão para visualizar este termo. Apenas o profissional vinculado ou administradores podem
            acessar.
          </AlertDescription>
        </Alert>
        <div className="mt-4 flex justify-center">
          <Button onClick={() => navigate({ to: "/" })}>Voltar ao Início</Button>
        </div>
      </div>
    );
  }

  return (
    <div className="container max-w-5xl mx-auto p-8">
      <Card>
        <CardHeader className="text-center bg-gradient-to-r from-purple-600 to-purple-800 text-white rounded-t-lg">
          <div className="flex items-center justify-center mb-4">
            <FileSignature className="h-12 w-12" />
          </div>
          <CardTitle className="text-3xl">TERMO DE CESSÃO DE DIREITOS DE CRÉDITO</CardTitle>
          <CardDescription className="text-purple-100">Nº {antecipacao.numeroTermo}</CardDescription>
        </CardHeader>

        <CardContent className="p-8 space-y-8">
          {/* Informações do Termo */}
          <div className="grid md:grid-cols-2 gap-6">
            <div className="space-y-4">
              <div className="flex items-start space-x-3">
                <Calendar className="h-5 w-5 text-muted-foreground mt-0.5" />
                <div>
                  <p className="text-sm text-muted-foreground">Data de Emissão</p>
                  <p className="font-medium">{formatDate(antecipacao.dataEmissao)}</p>
                </div>
              </div>

              <div className="flex items-start space-x-3">
                <Building2 className="h-5 w-5 text-muted-foreground mt-0.5" />
                <div>
                  <p className="text-sm text-muted-foreground">Hospital (Parte 1)</p>
                  <p className="font-medium">{antecipacao.hospital}</p>
                  <p className="text-sm text-muted-foreground">CNPJ: {antecipacao.hospitalCNPJ}</p>
                </div>
              </div>

              <div className="flex items-start space-x-3">
                <MapPin className="h-5 w-5 text-muted-foreground mt-0.5" />
                <div>
                  <p className="text-sm text-muted-foreground">Local de Atendimento</p>
                  <p className="font-medium">{antecipacao.localAtendimento}</p>
                </div>
              </div>
            </div>

            <div className="space-y-4">
              <div className="flex items-start space-x-3">
                <User className="h-5 w-5 text-muted-foreground mt-0.5" />
                <div>
                  <p className="text-sm text-muted-foreground">Profissional (Cedente)</p>
                  <p className="font-medium">{antecipacao.profissional}</p>
                  <p className="text-sm text-muted-foreground">CPF: {antecipacao.profissionalCPF}</p>
                </div>
              </div>

              <div className="flex items-start space-x-3">
                <Calendar className="h-5 w-5 text-muted-foreground mt-0.5" />
                <div>
                  <p className="text-sm text-muted-foreground">Período de Referência</p>
                  <p className="font-medium">{antecipacao.periodo}</p>
                </div>
              </div>

              <div className="flex items-start space-x-3">
                <FileText className="h-5 w-5 text-muted-foreground mt-0.5" />
                <div>
                  <p className="text-sm text-muted-foreground">Fundo (Parte 2)</p>
                  <p className="font-medium">KUARÁ GS2 IGSC I FUNDO DE INVESTIMENTO</p>
                  <p className="text-sm text-muted-foreground">CNPJ: 57.671.358/0001-63</p>
                </div>
              </div>
            </div>
          </div>

          <Separator />

          {/* Valores */}
          <div className="bg-gray-50 p-6 rounded-lg space-y-4">
            <h3 className="font-semibold text-lg flex items-center">
              <DollarSign className="h-5 w-5 mr-2" />
              Valores da Antecipação
            </h3>

            <div className="grid md:grid-cols-2 gap-4">
              <div className="flex justify-between">
                <span className="text-muted-foreground">Total dos Valores a Receber:</span>
                <span className="font-semibold text-green-600">{formatCurrency(antecipacao.valorTotal)}</span>
              </div>

              <div className="flex justify-between">
                <span className="text-muted-foreground">Percentual de Antecipação:</span>
                <span className="font-semibold">{antecipacao.percentual}%</span>
              </div>

              <div className="flex justify-between">
                <span className="text-muted-foreground">Taxa de Antecipação:</span>
                <span className="font-semibold">{antecipacao.taxaAntecipacao}%</span>
              </div>

              <div className="flex justify-between">
                <span className="text-muted-foreground">Preço de Aquisição (Valor Líquido):</span>
                <span className="font-semibold text-blue-600">{formatCurrency(antecipacao.valorLiquido)}</span>
              </div>

              <div className="flex justify-between md:col-span-2">
                <span className="text-muted-foreground">Data de Pagamento Prevista:</span>
                <span className="font-semibold">{formatDate(antecipacao.dataPagamentoPrevista)}</span>
              </div>
            </div>
          </div>

          {/* Fechamentos incluídos */}
          {antecipacao.fechamentos.length > 0 && (
            <>
              <Separator />
              <div className="space-y-4">
                <h3 className="font-semibold text-lg">Fechamentos Incluídos</h3>
                <div className="space-y-2">
                  {antecipacao.fechamentos.map((fechamento) => (
                    <div key={fechamento.uuid} className="flex justify-between p-3 bg-gray-50 rounded">
                      <span className="text-sm">Fechamento {fechamento.uuid.slice(0, 8)}</span>
                      <span className="font-medium">{formatCurrency(fechamento.valor)}</span>
                    </div>
                  ))}
                </div>
              </div>
            </>
          )}

          <Separator />

          {/* Texto do Termo */}
          <div className="space-y-4 text-sm leading-relaxed">
            <p>
              Pelo presente instrumento particular e na melhor forma de direito, o Hospital e o Fundo acima
              qualificados, devidamente representados, doravante conjuntamente denominados "Partes", resolvem, de forma
              irrevogável e irretratável, celebrar o presente Termo de Cessão, que se regerá pelas cláusulas e condições
              a seguir especificadas:
            </p>

            <div className="space-y-3">
              <p>
                <strong>1.</strong> Todas as condições e termos relativos à presente cessão que não estiverem
                expressamente estabelecidos neste Termo de Cessão encontram-se descritos no Contrato firmado pelas
                Partes, em especial a Obrigação de Recompra e Nota Promissória.
              </p>

              <p>
                <strong>2.</strong> Pelo presente Termo de Cessão, o Hospital cede e transfere ao Fundo de forma
                irrevogável e irretratável, sem qualquer direito de regresso, os Valores a Receber descritos acima, pelo
                Preço de Aquisição especificado, mediante pagamento nos termos do Contrato, pelo que o Hospital dá ao
                Fundo a mais ampla, geral, irrevogável e irretratável quitação, para nada mais reclamar, seja a que
                tempo e título for.
              </p>

              <p>
                <strong>3.</strong> O presente termo de cessão é parte integrante e indissociável do INSTRUMENTO
                PARTICULAR DE COMPROMISSO DE CESSÃO DIREITOS DE CRÉDITO C/ COOBRIGAÇÃO E OUTRAS AVENÇAS firmado entre as
                Partes na data de 19 de Agosto de 2025, aplicando-se ao presente Termo todas as disposições do aludido
                Contrato.
              </p>

              <p>
                <strong>4.</strong> O presente Termo de Cessão será regido e interpretado em conformidade com as leis da
                República Federativa do Brasil.
              </p>

              <p>
                <strong>5.</strong> As Partes assinam o presente Termo com certificado digital, com o mesmo teor e para
                um único propósito e efeito.
              </p>
            </div>
          </div>

          <Separator />

          {/* Mostrar área de assinatura apenas se pode assinar */}
          {podeAssinar ? (
            <>
              {/* Checkbox de aceite */}
              <Alert>
                <AlertCircle className="h-4 w-4" />
                <AlertDescription>
                  Ao aceitar este termo, você concorda com todas as cláusulas e condições estabelecidas acima. Esta ação
                  é irrevogável e irretratável.
                </AlertDescription>
              </Alert>

              <div className="flex items-start space-x-3">
                <Checkbox
                  id="aceite"
                  checked={aceite}
                  onCheckedChange={(checked) => setAceite(checked as boolean)}
                  disabled={assinaturaMutation.isPending}
                />
                <label
                  htmlFor="aceite"
                  className="text-sm font-medium leading-none peer-disabled:cursor-not-allowed peer-disabled:opacity-70 cursor-pointer"
                >
                  Li e aceito integralmente os termos e condições do Termo de Cessão de Direitos de Crédito
                </label>
              </div>

              {/* Botões de ação */}
              <div className="flex justify-end space-x-4">
                <Button variant="outline" onClick={() => navigate({ to: "/" })} disabled={assinaturaMutation.isPending}>
                  Cancelar
                </Button>
                <Button
                  onClick={() => assinaturaMutation.mutate()}
                  disabled={!aceite || assinaturaMutation.isPending}
                  className="bg-gradient-to-r from-purple-600 to-purple-800 text-white"
                >
                  {assinaturaMutation.isPending ? (
                    <>
                      <Loader2 className="mr-2 h-4 w-4 animate-spin" />
                      Assinando...
                    </>
                  ) : (
                    <>
                      <CheckCircle className="mr-2 h-4 w-4" />
                      Assinar Digitalmente
                    </>
                  )}
                </Button>
              </div>
            </>
          ) : isAdmin ? (
            /* Mensagem para administradores */
            <Alert>
              <AlertCircle className="h-4 w-4" />
              <AlertDescription>
                <strong>Modo de visualização (Administrador)</strong>
                <br />
                Você está visualizando este termo como administrador. Apenas o profissional {
                  antecipacao.profissional
                }{" "}
                pode assinar este documento.
                {data.termoAssinado && (
                  <span className="block mt-2 text-green-600">
                    Este termo já foi assinado em {formatDate(data.antecipacao.dataEmissao)}.
                  </span>
                )}
              </AlertDescription>
            </Alert>
          ) : data.termoAssinado ? (
            /* Mensagem para termo já assinado */
            <Alert>
              <CheckCircle className="h-4 w-4 text-green-600" />
              <AlertDescription>
                <strong>Termo já assinado</strong>
                <br />
                Este termo foi assinado digitalmente. Um email de confirmação foi enviado para o profissional.
              </AlertDescription>
            </Alert>
          ) : null}

          {/* Botão para voltar quando não pode assinar */}
          {!podeAssinar && (
            <div className="flex justify-center">
              <Button onClick={() => navigate({ to: "/" })}>Voltar ao Início</Button>
            </div>
          )}
        </CardContent>
      </Card>
    </div>
  );
}
