import { setAuditContext, extractAuditContext } from "../lib/prisma";
import type { FastifyTypedReply, FastifyTypedRequest } from "@/types";

/**
 * Middleware que configura o contexto de auditoria para a requisição atual
 * Deve ser usado após o middleware de autenticação
 */
export async function auditMiddleware(request: FastifyTypedRequest, reply: FastifyTypedReply) {
  const auditContext = extractAuditContext(request);
  setAuditContext(auditContext);

  // Log da operação para debug (remover em produção se necessário)
  request.log.debug(
    {
      auditContext: {
        userId: auditContext.userId,
        userName: auditContext.userName,
        endpoint: auditContext.endpoint,
        method: auditContext.method,
      },
    },
    "Audit context set for request"
  );
}
