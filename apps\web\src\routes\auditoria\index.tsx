import { useState } from "react";
import { createFileRoute } from "@tanstack/react-router";
import { useQuery } from "@tanstack/react-query";
import { requireAdminRole } from "@/lib/route-guards";
import { api } from "@/lib/api";
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from "@/components/ui/card";
import { Badge } from "@/components/ui/badge";
import { Button } from "@/components/ui/button";
import { Input } from "@/components/ui/input";
import { Select, SelectContent, SelectItem, SelectTrigger, SelectValue } from "@/components/ui/select";
import { Table, TableBody, TableCell, TableHead, TableHeader, TableRow } from "@/components/ui/table";
import { format, parseISO } from "date-fns";
import { ptBR } from "date-fns/locale";
import { Activity, Database, FileText, Search, Trash2, User } from "lucide-react";
import { Sheet, She<PERSON><PERSON>ontent, <PERSON><PERSON><PERSON><PERSON><PERSON>, <PERSON><PERSON><PERSON><PERSON><PERSON>, <PERSON><PERSON><PERSON><PERSON><PERSON> } from "@/components/ui/sheet";
import { ScrollA<PERSON> } from "@/components/ui/scroll-area";

interface AuditLog {
  id: number;
  operation: "CREATE" | "UPDATE" | "DELETE" | "READ";
  tableName: string;
  recordId: string | null;
  oldData: any;
  newData: any;
  changes: any;
  userId: string | null;
  userName: string | null;
  userEmail: string | null;
  userRole: string | null;
  ipAddress: string | null;
  userAgent: string | null;
  endpoint: string | null;
  method: string | null;
  description: string | null;
  metadata: any;
  createdAt: string;
}

interface AuditStats {
  totalLogs: number;
  recentActivity: number;
  byOperation: {
    CREATE?: number;
    UPDATE?: number;
    DELETE?: number;
    READ?: number;
  };
  topTables: Array<{
    table: string;
    count: number;
  }>;
}

function AuditoriaPage() {
  const [page, setPage] = useState(1);
  const [filters, setFilters] = useState({
    tableName: "",
    operation: "",
    userId: "",
    recordId: "",
  });
  const [selectedLog, setSelectedLog] = useState<AuditLog | null>(null);

  // Buscar logs
  const { data: logsData, isLoading: logsLoading } = useQuery<{ data: AuditLog[]; meta: any }>({
    queryKey: ["audit-logs", page, filters],
    queryFn: async () => {
      const params = new URLSearchParams({
        page: page.toString(),
        limit: "20",
        ...(filters.tableName && { tableName: filters.tableName }),
        ...(filters.operation && { operation: filters.operation }),
        ...(filters.userId && { userId: filters.userId }),
        ...(filters.recordId && { recordId: filters.recordId }),
      });
      return api.get(`/audit-logs?${params}`);
    },
  });

  // Buscar estatísticas
  const { data: stats } = useQuery<AuditStats>({
    queryKey: ["audit-stats"],
    queryFn: () => api.get("/audit-logs/stats"),
  });

  const getOperationColor = (operation: string) => {
    switch (operation) {
      case "CREATE":
        return "bg-green-500";
      case "UPDATE":
        return "bg-blue-500";
      case "DELETE":
        return "bg-red-500";
      case "READ":
        return "bg-gray-500";
      default:
        return "bg-gray-400";
    }
  };

  const getOperationIcon = (operation: string) => {
    switch (operation) {
      case "CREATE":
        return "+";
      case "UPDATE":
        return "↻";
      case "DELETE":
        return "×";
      case "READ":
        return "👁";
      default:
        return "?";
    }
  };

  const formatTableName = (name: string) => {
    const nameMap: Record<string, string> = {
      Cliente: "Clientes",
      Profissional: "Profissionais",
      Plantao: "Plantões",
      LocalAtendimento: "Locais de Atendimento",
      Fechamento: "Fechamentos",
      Antecipacao: "Antecipações",
      DiaPlantao: "Dias de Plantão",
      PresencaDiaPlantao: "Registros de Presença",
    };
    return nameMap[name] || name;
  };

  return (
    <div className="container mx-auto p-6 space-y-6">
      <div className="flex justify-between items-center">
        <div>
          <h1 className="text-3xl font-bold">Auditoria do Sistema</h1>
          <p className="text-muted-foreground mt-1">Monitoramento de todas as operações realizadas no sistema</p>
        </div>
      </div>

      {/* Estatísticas */}
      {stats && (
        <div className="grid grid-cols-1 md:grid-cols-4 gap-4">
          <Card>
            <CardHeader className="flex flex-row items-center justify-between space-y-0 pb-2">
              <CardTitle className="text-sm font-medium">Total de Logs</CardTitle>
              <Database className="h-4 w-4 text-muted-foreground" />
            </CardHeader>
            <CardContent>
              <div className="text-2xl font-bold">{stats.totalLogs.toLocaleString()}</div>
              <p className="text-xs text-muted-foreground">Registros históricos</p>
            </CardContent>
          </Card>

          <Card>
            <CardHeader className="flex flex-row items-center justify-between space-y-0 pb-2">
              <CardTitle className="text-sm font-medium">Atividade Recente</CardTitle>
              <Activity className="h-4 w-4 text-muted-foreground" />
            </CardHeader>
            <CardContent>
              <div className="text-2xl font-bold">{stats.recentActivity}</div>
              <p className="text-xs text-muted-foreground">Últimas 24 horas</p>
            </CardContent>
          </Card>

          <Card>
            <CardHeader className="flex flex-row items-center justify-between space-y-0 pb-2">
              <CardTitle className="text-sm font-medium">Operações</CardTitle>
              <FileText className="h-4 w-4 text-muted-foreground" />
            </CardHeader>
            <CardContent>
              <div className="flex gap-2 flex-wrap">
                {Object.entries(stats.byOperation).map(([op, count]) => (
                  <Badge key={op} variant="secondary" className="text-xs">
                    {op}: {count}
                  </Badge>
                ))}
              </div>
            </CardContent>
          </Card>

          <Card>
            <CardHeader className="flex flex-row items-center justify-between space-y-0 pb-2">
              <CardTitle className="text-sm font-medium">Tabela Mais Acessada</CardTitle>
              <Database className="h-4 w-4 text-muted-foreground" />
            </CardHeader>
            <CardContent>
              {stats.topTables[0] && (
                <>
                  <div className="text-lg font-bold">{formatTableName(stats.topTables[0].table)}</div>
                  <p className="text-xs text-muted-foreground">{stats.topTables[0].count} operações</p>
                </>
              )}
            </CardContent>
          </Card>
        </div>
      )}

      {/* Filtros */}
      <Card>
        <CardHeader>
          <CardTitle>Filtros</CardTitle>
          <CardDescription>Filtre os logs por diferentes critérios</CardDescription>
        </CardHeader>
        <CardContent>
          <div className="grid grid-cols-1 md:grid-cols-4 gap-4">
            <div>
              <label className="text-sm font-medium">Tabela</label>
              <Input
                placeholder="Nome da tabela"
                value={filters.tableName}
                onChange={(e: React.ChangeEvent<HTMLInputElement>) =>
                  setFilters({ ...filters, tableName: e.target.value })
                }
              />
            </div>
            <div>
              <label className="text-sm font-medium">Operação</label>
              <Select
                value={filters.operation || "ALL"}
                onValueChange={(value: string) => setFilters({ ...filters, operation: value === "ALL" ? "" : value })}
              >
                <SelectTrigger>
                  <SelectValue placeholder="Todas" />
                </SelectTrigger>
                <SelectContent>
                  <SelectItem value="ALL">Todas</SelectItem>
                  <SelectItem value="CREATE">CREATE</SelectItem>
                  <SelectItem value="UPDATE">UPDATE</SelectItem>
                  <SelectItem value="DELETE">DELETE</SelectItem>
                  <SelectItem value="READ">READ</SelectItem>
                </SelectContent>
              </Select>
            </div>
            <div>
              <label className="text-sm font-medium">ID do Usuário</label>
              <Input
                placeholder="ID do usuário"
                value={filters.userId}
                onChange={(e: React.ChangeEvent<HTMLInputElement>) =>
                  setFilters({ ...filters, userId: e.target.value })
                }
              />
            </div>
            <div>
              <label className="text-sm font-medium">ID do Registro</label>
              <Input
                placeholder="ID do registro"
                value={filters.recordId}
                onChange={(e: React.ChangeEvent<HTMLInputElement>) =>
                  setFilters({ ...filters, recordId: e.target.value })
                }
              />
            </div>
          </div>
        </CardContent>
      </Card>

      {/* Tabela de Logs */}
      <Card>
        <CardHeader>
          <CardTitle>Logs de Auditoria</CardTitle>
          <CardDescription>Histórico detalhado de todas as operações realizadas</CardDescription>
        </CardHeader>
        <CardContent>
          <Table>
            <TableHeader>
              <TableRow>
                <TableHead>Data/Hora</TableHead>
                <TableHead>Operação</TableHead>
                <TableHead>Tabela</TableHead>
                <TableHead>Usuário</TableHead>
                <TableHead>Endpoint</TableHead>
                <TableHead>Descrição</TableHead>
                <TableHead>Ações</TableHead>
              </TableRow>
            </TableHeader>
            <TableBody>
              {logsLoading ? (
                <TableRow>
                  <TableCell colSpan={7} className="text-center">
                    Carregando...
                  </TableCell>
                </TableRow>
              ) : logsData?.data?.length === 0 ? (
                <TableRow>
                  <TableCell colSpan={7} className="text-center">
                    Nenhum log encontrado
                  </TableCell>
                </TableRow>
              ) : (
                logsData?.data?.map((log: AuditLog) => (
                  <TableRow key={log.id}>
                    <TableCell className="text-xs">
                      {format(parseISO(log.createdAt), "dd/MM/yyyy HH:mm:ss", {
                        locale: ptBR,
                      })}
                    </TableCell>
                    <TableCell>
                      <Badge className={`${getOperationColor(log.operation)} text-white`}>
                        <span className="mr-1">{getOperationIcon(log.operation)}</span>
                        {log.operation}
                      </Badge>
                    </TableCell>
                    <TableCell className="font-medium">{formatTableName(log.tableName)}</TableCell>
                    <TableCell>
                      <div className="flex items-center gap-1">
                        <User className="h-3 w-3" />
                        <span className="text-sm">{log.userName || "Sistema"}</span>
                      </div>
                    </TableCell>
                    <TableCell className="text-xs">
                      {log.method && log.endpoint && (
                        <code className="bg-muted px-1 py-0.5 rounded">
                          {log.method} {log.endpoint}
                        </code>
                      )}
                    </TableCell>
                    <TableCell className="max-w-xs truncate">{log.description || "-"}</TableCell>
                    <TableCell>
                      <Button size="sm" variant="ghost" onClick={() => setSelectedLog(log)}>
                        Ver Detalhes
                      </Button>
                    </TableCell>
                  </TableRow>
                ))
              )}
            </TableBody>
          </Table>

          {/* Paginação */}
          {logsData?.meta && (
            <div className="flex justify-between items-center mt-4">
              <div className="text-sm text-muted-foreground">
                Página {logsData.meta.page} de {logsData.meta.totalPages} ({logsData.meta.total} registros)
              </div>
              <div className="flex gap-2">
                <Button size="sm" variant="outline" onClick={() => setPage(page - 1)} disabled={page === 1}>
                  Anterior
                </Button>
                <Button
                  size="sm"
                  variant="outline"
                  onClick={() => setPage(page + 1)}
                  disabled={page === logsData.meta.totalPages}
                >
                  Próxima
                </Button>
              </div>
            </div>
          )}
        </CardContent>
      </Card>

      {/* Sheet de Detalhes */}
      <Sheet open={!!selectedLog} onOpenChange={() => setSelectedLog(null)}>
        <SheetContent className="w-[600px] sm:max-w-[600px] p-2">
          <SheetHeader>
            <SheetTitle>Detalhes do Log de Auditoria</SheetTitle>
            <SheetDescription>Informações completas sobre a operação realizada</SheetDescription>
          </SheetHeader>
          {selectedLog && (
            <ScrollArea className="h-[calc(100vh-120px)] mt-6">
              <div className="space-y-4">
                <div>
                  <h3 className="font-semibold mb-2">Informações Gerais</h3>
                  <div className="space-y-1 text-sm">
                    <div>
                      <span className="font-medium">ID:</span> {selectedLog.id}
                    </div>
                    <div>
                      <span className="font-medium">Data/Hora:</span>{" "}
                      {format(parseISO(selectedLog.createdAt), "dd/MM/yyyy HH:mm:ss", {
                        locale: ptBR,
                      })}
                    </div>
                    <div>
                      <span className="font-medium">Operação:</span>{" "}
                      <Badge className={`${getOperationColor(selectedLog.operation)} text-white`}>
                        {selectedLog.operation}
                      </Badge>
                    </div>
                    <div>
                      <span className="font-medium">Tabela:</span> {formatTableName(selectedLog.tableName)}
                    </div>
                    {selectedLog.recordId && (
                      <div>
                        <span className="font-medium">ID do Registro:</span> {selectedLog.recordId}
                      </div>
                    )}
                  </div>
                </div>

                <div>
                  <h3 className="font-semibold mb-2">Informações do Usuário</h3>
                  <div className="space-y-1 text-sm">
                    <div>
                      <span className="font-medium">Nome:</span> {selectedLog.userName || "Sistema"}
                    </div>
                    {selectedLog.userEmail && (
                      <div>
                        <span className="font-medium">Email:</span> {selectedLog.userEmail}
                      </div>
                    )}
                    {selectedLog.userRole && (
                      <div>
                        <span className="font-medium">Papel:</span> {selectedLog.userRole}
                      </div>
                    )}
                    {selectedLog.ipAddress && (
                      <div>
                        <span className="font-medium">IP:</span> {selectedLog.ipAddress}
                      </div>
                    )}
                  </div>
                </div>

                {selectedLog.description && (
                  <div>
                    <h3 className="font-semibold mb-2">Descrição</h3>
                    <p className="text-sm">{selectedLog.description}</p>
                  </div>
                )}

                {selectedLog.endpoint && (
                  <div>
                    <h3 className="font-semibold mb-2">Requisição</h3>
                    <div className="space-y-1 text-sm">
                      <div>
                        <span className="font-medium">Método:</span> {selectedLog.method}
                      </div>
                      <div>
                        <span className="font-medium">Endpoint:</span>{" "}
                        <code className="bg-muted px-1 py-0.5 rounded">{selectedLog.endpoint}</code>
                      </div>
                    </div>
                  </div>
                )}

                {selectedLog.changes && Object.keys(selectedLog.changes).length > 0 && (
                  <div>
                    <h3 className="font-semibold mb-2">Mudanças</h3>
                    <div className="bg-muted p-3 rounded-md">
                      <pre className="text-xs overflow-auto">{JSON.stringify(selectedLog.changes, null, 2)}</pre>
                    </div>
                  </div>
                )}

                {selectedLog.newData && (
                  <div>
                    <h3 className="font-semibold mb-2">Novos Dados</h3>
                    <div className="bg-muted p-3 rounded-md">
                      <pre className="text-xs overflow-auto">{JSON.stringify(selectedLog.newData, null, 2)}</pre>
                    </div>
                  </div>
                )}

                {selectedLog.oldData && (
                  <div>
                    <h3 className="font-semibold mb-2">Dados Anteriores</h3>
                    <div className="bg-muted p-3 rounded-md">
                      <pre className="text-xs overflow-auto">{JSON.stringify(selectedLog.oldData, null, 2)}</pre>
                    </div>
                  </div>
                )}

                {selectedLog.metadata && (
                  <div>
                    <h3 className="font-semibold mb-2">Metadados</h3>
                    <div className="bg-muted p-3 rounded-md">
                      <pre className="text-xs overflow-auto">{JSON.stringify(selectedLog.metadata, null, 2)}</pre>
                    </div>
                  </div>
                )}
              </div>
            </ScrollArea>
          )}
        </SheetContent>
      </Sheet>
    </div>
  );
}

export const Route = createFileRoute("/auditoria/")({
  component: AuditoriaPage,
  beforeLoad: async () => {
    await requireAdminRole();
  },
});
