import { dirname } from 'path';
import { fileURLToPath } from 'url';
import { getParams, getFunctionDescription, unzip } from './functions.mjs';

const __filename = fileURLToPath(import.meta.url);
const __dirname = dirname(__filename);

const getOriginLambda = (lambdaName = '') => {
    // return path.resolve(__dirname, '..', 'src', 'lambdas', lambdaName);
    return `${__dirname}/../src/lambdas/${lambdaName}`;
};

const main = async () => {
    try {
        const { lambdaName, profile, region } = await getParams();

        const functionDescription = await getFunctionDescription({
            name: lambdaName,
            profile,
            region,
        });

        const {
            Code: { Location: functionDescResult },
        } = functionDescription;

        await unzip(functionDescResult, getOriginLambda(lambdaName));
    } catch (error) {
        console.log('\n');
        console.log('\x1b[31m%s\x1b[0m', 'Catch no downloadFunction():');
        console.log(error);
        console.log('\n');
    } finally {
        process.exit();
    }
};

main();
