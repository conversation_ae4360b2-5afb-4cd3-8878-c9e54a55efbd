import { z } from "zod";

export const createLocalAtendimentoSchema = z.object({
  clienteId: z.uuid("Cliente é obrigatório").optional(), // simples, todo clientId vindo do front, na vdd é o uuid do cliente
  nome: z.string().min(1, "Nome é obrigatório"),
  endereco: z.string().min(1, "Endereço é obrigatório"),
  cidade: z.string().optional().nullable(),
  estado: z.string().optional().nullable(),
  cep: z.string().optional().nullable(),
  telefone: z.string().optional().nullable(),
  responsavel: z.string().optional().nullable(),
  observacoes: z.string().optional().nullable(),
  latitude: z.number().optional().nullable(),
  longitude: z.number().optional().nullable(),
  ativo: z.boolean().default(true),
  especialidadeIds: z.array(z.number()).optional(),
});

export const updateLocalAtendimentoSchema = createLocalAtendimentoSchema.partial();

export const localAtendimentoQuerySchema = z.object({
  page: z.coerce.number().min(1).optional().default(1),
  limit: z.coerce.number().min(1).max(100).optional().default(10),
  search: z.string().optional(),
  clienteId: z.number().positive().optional(),
  clienteUuid: z.uuid().optional(),
  ativo: z
    .string()
    .transform((val) => val === "true")
    .optional(),
});

export type CreateLocalAtendimentoInput = z.infer<typeof createLocalAtendimentoSchema>;
export type UpdateLocalAtendimentoInput = z.infer<typeof updateLocalAtendimentoSchema>;
export type LocalAtendimentoQuery = z.infer<typeof localAtendimentoQuerySchema>;
