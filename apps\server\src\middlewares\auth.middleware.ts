import jwt from "jsonwebtoken";

import { prisma } from "@/lib/prisma";
import { appEnv } from "@/lib/env";
import type { FastifyTypedReply, FastifyTypedRequest } from "@/types";
import type { RoleType } from "@shared/types";

export interface AuthenticatedRequest extends FastifyTypedRequest {
  user?: {
    id: number;
    email: string;
    nome: string;
    roles: RoleType[];
    clientes: number[];
  };
}

export async function authenticate(request: AuthenticatedRequest, reply: FastifyTypedReply) {
  try {
    const authHeader = request.headers.authorization;

    if (!authHeader || !authHeader.startsWith("Bearer ")) {
      return reply.status(401).send({ error: "Não autorizado" });
    }

    const token = authHeader.substring(7);

    const decoded = jwt.verify(token, appEnv.JWT_SECRET) as any;

    const usuario = await prisma.usuario.findUnique({
      where: { uuid: decoded.id },
      include: {
        roles: {
          include: {
            role: true,
          },
        },
        clientes: {
          select: {
            id: true,
          },
        },
      },
    });

    if (!usuario || !usuario.ativo) {
      return reply.status(401).send({ error: "Usuário inválido ou inativo" });
    }

    request.user = {
      id: usuario.id,
      email: usuario.email,
      nome: usuario.nome,
      roles: usuario.roles.map((ur) => ur.role.nome) as RoleType[],
      clientes: usuario.clientes.map((uc) => uc.id),
    };
  } catch (error) {
    console.log("Erro ao autenticar:", error);
    return reply.status(401).send({ error: "Token inválido" });
  }
}

export function authorize(...allowedRoles: RoleType[]) {
  return async (request: AuthenticatedRequest, reply: FastifyTypedReply) => {
    if (!request.user) {
      return reply.status(401).send({ error: "Não autenticado" });
    }

    const hasRole = request.user.roles.some((role) => allowedRoles.includes(role as RoleType));

    // if (!hasRole && !request.user.roles.includes("admin") && !request.user.roles.includes("master")) {
    if (!hasRole) {
      return reply.status(403).send({
        error: "Sem permissão para acessar este recurso",
        // requiredRoles: allowedRoles,
        // userRoles: request.user.roles,
      });
    }
  };
}

export function authenticateOptional(request: AuthenticatedRequest, reply: FastifyTypedReply, done: () => void) {
  const authHeader = request.headers.authorization;

  if (!authHeader || !authHeader.startsWith("Bearer ")) {
    done();
    return;
  }

  const token = authHeader.substring(7);

  try {
    const decoded = jwt.verify(token, appEnv.JWT_SECRET) as any;

    prisma.usuario
      .findUnique({
        where: { id: decoded.id },
        include: {
          roles: {
            include: {
              role: true,
            },
          },
          clientes: {
            select: {
              id: true,
            },
          },
        },
      })
      .then((usuario) => {
        if (usuario && usuario.ativo) {
          request.user = {
            id: usuario.id,
            email: usuario.email,
            nome: usuario.nome,
            roles: usuario.roles.map((ur) => ur.role.nome) as RoleType[],
            clientes: usuario.clientes.map((uc) => uc.id),
          };
        }
        done();
      })
      .catch(() => {
        done();
      });
  } catch {
    done();
  }
}
