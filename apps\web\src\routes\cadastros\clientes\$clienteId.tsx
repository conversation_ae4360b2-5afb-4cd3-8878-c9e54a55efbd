import { Outlet, useLocation } from "@tanstack/react-router";
import { createFileRoute } from "@tanstack/react-router";
import { requireAdminRole } from "@/lib/route-guards";
import { ClienteForm } from "@/pages/clientes/cliente-form";

function ClienteDetails() {
  // Check if we're on a nested route using React Router's useLocation
  const location = useLocation();
  const currentPath = location.pathname;
  const isNestedRoute =
    currentPath.includes("/locais/novo") || (currentPath.includes("/locais/") && currentPath.includes("/editar"));

  // If we're on a nested route, render the Outlet
  if (isNestedRoute) {
    return <Outlet />;
  }

  return <ClienteForm viewMode />;
}

export const Route = createFileRoute("/cadastros/clientes/$clienteId")({
  component: ClienteDetails,
  beforeLoad: async () => {
    await requireAdminRole();
  },
  validateSearch: (search: Record<string, unknown>): { action?: string; tab?: string } => {
    return {
      action: typeof search.action === "string" ? search.action : undefined,
      tab: typeof search.tab === "string" ? search.tab : undefined,
    };
  },
});
