import { useQuery, useMutation, useQueryClient } from "@tanstack/react-query";
import { api, type Cliente, type PaginatedResponse } from "@/lib/api";
import { toast } from "sonner";

export function useClientes(params?: { limit?: number; ativo?: boolean }) {
  return useQuery({
    queryKey: ["clientes", params],
    queryFn: () =>
      api.get<{ data: Cliente[] }>("/clientes", {
        limit: params?.limit || 100,
        ativo: params?.ativo ?? true,
      }),
  });
}

export function useCheckCnpj(cnpj: string, enabled = true) {
  return useQuery({
    queryKey: ["check-cnpj", cnpj],
    queryFn: () =>
      api.get<{ exists: boolean; cliente?: { uuid: string; nome: string } }>(`/clientes/check-cnpj/${cnpj}`),
    enabled: enabled && !!cnpj && cnpj.length >= 14,
    staleTime: 60 * 1000, // 1 minuto
  });
}

export function useCreateCliente() {
  const queryClient = useQueryClient();

  return useMutation({
    mutationFn: (data: Partial<Cliente>) => api.post<Cliente>("/clientes", data),
    onSuccess: () => {
      queryClient.invalidateQueries({ queryKey: ["clientes"] });
    },
    onError: (error: Error) => {
      toast.error(error.message || "Erro ao criar cliente");
    },
  });
}
