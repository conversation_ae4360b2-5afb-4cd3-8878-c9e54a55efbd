# PRD - Sistema de Antecipação de Recebíveis para Profissionais de Saúde

## Executive Summary

O Sistema de Antecipação de Recebíveis é uma plataforma web desenvolvida para gerenciar plantões de profissionais de saúde e facilitar a antecipação de pagamentos. O sistema permite o controle completo da jornada de trabalho, desde o agendamento até o recebimento antecipado dos valores devidos.

**Problema**: Profissionais de saúde que trabalham em regime de plantão enfrentam dificuldades de fluxo de caixa devido aos longos ciclos de pagamento, necessitando de soluções para antecipar seus recebíveis.

**Solução**: Uma plataforma integrada que digitaliza todo o processo, desde o registro de ponto até a antecipação financeira, proporcionando transparência e agilidade no pagamento.

---

## 1. Product Goals & Success Metrics

### 1.1 Objetivos Primários

- **Eficiência Operacional**: Reduzir em 70% o tempo gasto no processamento manual de plantões
- **Transparência Financeira**: Fornecer visibilidade completa do status de pagamentos para profissionais
- **Agilidade na Antecipação**: Processar solicitações de antecipação em até 24 horas
- **Compliance**: Garantir auditoria completa de todas as transações e alterações

### 1.2 Métricas de Sucesso

- **Adoção**: 90% dos profissionais ativos utilizando o sistema em 6 meses
- **Precisão**: 99% de acurácia nos cálculos de horas e valores
- **Satisfação**: NPS > 70 entre profissionais e gestores
- **Eficiência**: Redução de 50% no tempo de fechamento mensal

---

## 2. User Personas & Use Cases

### 2.1 Personas Principais

#### 2.1.1 Profissional de Saúde

- **Perfil**: Médicos, enfermeiros, técnicos que trabalham em plantões
- **Necessidades**: Controlar horas trabalhadas, visualizar ganhos, antecipar recebíveis
- **Dores**: Atraso nos pagamentos, falta de transparência, processos burocráticos

#### 2.1.2 Gestor/Coordenador

- **Perfil**: Responsáveis por equipes e aprovação de fechamentos
- **Necessidades**: Aprovar registros de ponto, gerenciar equipes, controlar custos
- **Dores**: Processo manual de aprovação, falta de visibilidade consolidada
- **Perfil**: Responsáveis por pagamentos e antecipações
- **Necessidades**: Processar antecipações, controlar fluxo de caixa, gerar relatórios
- **Dores**: Processos manuais, falta de controle de risco

#### 2.1.4 Administrador do Sistema

- **Perfil**: Responsáveis pela configuração e manutenção do sistema
- **Necessidades**: Gerenciar usuários, configurar parâmetros, monitorar sistema
- **Dores**: Falta de ferramentas de administração centralizadas

### 2.2 User Stories Principais

#### 2.2.1 Para Profissionais

- **US001**: Como profissional, quero registrar minha entrada e saída do plantão para controlar minhas horas trabalhadas
- **US002**: Como profissional, quero visualizar meu fechamento mensal para acompanhar meus ganhos
- **US003**: Como profissional, quero solicitar antecipação do meu fechamento aprovado para melhorar meu fluxo de caixa
- **US004**: Como profissional, quero acompanhar o status da minha solicitação de antecipação

#### 2.2.2 Para Gestores

- **US005**: Como gestor, quero aprovar os registros de ponto da minha equipe para validar as horas trabalhadas
- **US006**: Como gestor, quero visualizar um dashboard consolidado da minha equipe para ter controle operacional
- **US007**: Como gestor, quero aprovar ou rejeitar fechamentos mensais com justificativas
- **US008**: Como financeiro, quero processar solicitações de antecipação para liberar recursos
- **US009**: Como financeiro, quero gerar relatórios de antecipações por período para controle de fluxo de caixa
- **US010**: Como financeiro, quero configurar percentuais de antecipação por tipo de contrato

---

## 3. Feature Requirements

### 3.1 Core Features (MVP)

#### 3.1.1 Autenticação e Autorização

- **Login/Logout**: Sistema de autenticação seguro com JWT
- **Recuperação de Senha**: Processo via email com token temporário
- **Controle de Acesso**: Sistema RBAC com roles predefinidos
- **Verificação de Email**: Processo de ativação de conta

#### 3.1.2 Gestão de Plantões

- **Criação de Plantão**: Configuração de período, local, valores e horários
- **Tipos de Pagamento**: Suporte para plantonista, mensalista, coordenador, supervisor, diretor
- **Configuração de Fechamento**: Diário, semanal, quinzenal ou mensal
- **Vinculação de Profissionais**: Associação de profissionais aos plantões

#### 3.1.3 Controle de Ponto

- **Registro de Entrada/Saída**: Interface para marcação de horários
- **Cálculo Automático**: Horas trabalhadas considerando intervalos
- **Validação de Dados**: Verificação de consistência nos horários
- **Histórico de Alterações**: Log de todas as modificações

#### 3.1.4 Processo de Fechamento

- **Consolidação Mensal**: Agrupamento automático dos registros aprovados
- **Cálculo de Valores**: Totalização baseada em horas x valor hora
- **Workflow de Aprovação**: Processo estruturado com múltiplos níveis
- **Notificações**: Alertas para pendências e aprovações

#### 3.1.5 Antecipação de Recebíveis

- **Solicitação de Antecipação**: Interface para profissionais solicitarem antecipação
- **Cálculo de Percentuais**: 80% para terceiros, 100% para Aura
- **Processo de Aprovação**: Workflow específico para antecipações
- **Geração de CCB**: Documentos contratuais automáticos

### 3.2 Advanced Features (Post-MVP)

#### 3.2.1 Dashboard e Relatórios

- **Dashboard Executivo**: Visão consolidada para gestores
- **Relatórios Financeiros**: Análises de custos e projeções
- **Analytics de Produtividade**: Métricas de performance por profissional
- **Exportação de Dados**: Relatórios em PDF/Excel

#### 3.2.2 Integrações

- **Sistema Bancário**: Integração para pagamentos automáticos
- **ERP/Folha de Pagamento**: Sincronização com sistemas existentes
- **Biometria**: Integração com relógios de ponto biométricos
- **APIs Terceiros**: Webhooks para notificações

#### 3.2.3 Mobile App

- **App Nativo**: Aplicativo para registro de ponto mobile
- **Notificações Push**: Alertas em tempo real
- **Modo Offline**: Funcionamento sem internet com sincronização posterior
- **Geolocalização**: Validação de presença no local de trabalho

---

## 4. Technical Requirements

### 4.1 Architecture Overview

- **Frontend**: React Router v7, TailwindCSS 4, shadcn/ui
- **Backend**: Fastify with TypeScript
- **Database**: MySQL with Prisma ORM
- **State Management**: Zustand + TanStack Query
- **Build System**: Vite + npm workspaces

### 4.2 Performance Requirements

- **Response Time**: < 200ms para operações CRUD básicas
- **Throughput**: Suporte para 1000 usuários concorrentes
- **Availability**: 99.9% de uptime
- **Scalability**: Arquitetura preparada para crescimento horizontal

### 4.3 Security Requirements

- **Autenticação**: JWT com refresh tokens
- **Autorização**: RBAC granular por funcionalidade
- **Auditoria**: Log completo de todas as ações (AuditLog)
- **Criptografia**: Dados sensíveis criptografados em repouso
- **LGPD Compliance**: Conformidade com lei de proteção de dados

### 4.4 Data Requirements

- **Backup**: Backup diário automatizado
- **Retention**: Retenção de dados por 7 anos
- **Migration**: Estratégia para migração de dados existentes
- **Integrity**: Constraints e validações rigorosas no banco

---

## 5. Business Rules & Logic

### 5.1 Regras de Plantão

- Um profissional pode ter apenas um plantão por mês/ano/cliente
- Plantões devem ter data inicial e final definidas
- Valores podem ser por hora, plantão fixo ou mensal
- Horários devem respeitar limites trabalhistas

### 5.2 Regras de Ponto

- Apenas um registro de ponto por dia de plantão
- Horários de entrada devem ser anteriores aos de saída
- Intervalos são descontados automaticamente das horas trabalhadas
- Registros só podem ser alterados com justificativa

### 5.3 Regras de Fechamento

- Fechamentos só podem ser criados após término do período
- Todos os registros de ponto devem estar aprovados
- Valores são calculados automaticamente
- Aprovações seguem hierarquia definida

### 5.4 Regras de Antecipação

- Antecipação só pode ser solicitada para fechamentos aprovados
- Percentuais variam por tipo de contrato (80% terceiros, 100% Aura)
- CCB é gerada automaticamente na aprovação
- Limite de uma antecipação ativa por fechamento

---

## 6. User Experience & Interface

### 6.1 Design Principles

- **Simplicidade**: Interface limpa e intuitiva
- **Consistência**: Padrões visuais uniformes
- **Acessibilidade**: Conformidade com WCAG 2.1
- **Responsividade**: Suporte para desktop, tablet e mobile

### 6.2 Key User Flows

#### 6.2.1 Fluxo de Registro de Ponto

1. Login no sistema
2. Seleção do plantão ativo
3. Registro de entrada com timestamp
4. Registro de saída com cálculo automático
5. Confirmação e envio para aprovação

#### 6.2.2 Fluxo de Antecipação

1. Visualização de fechamentos aprovados
2. Seleção do fechamento para antecipação
3. Confirmação do valor e percentual
4. Submissão da solicitação
5. Acompanhamento do status até pagamento

### 6.3 Interface Components

- **Dashboard Cards**: Resumos visuais de informações chave
- **Tables**: Listagens com filtros e ordenação
- **Forms**: Formulários com validação em tempo real
- **Modals**: Confirmações e detalhamento de ações
- **Notifications**: Sistema de alertas e notificações

---

## 7. Implementation Plan

### 7.1 Phase 1 - MVP Core (3 meses)

- **Mês 1**: Setup da arquitetura + Autenticação + Gestão de usuários
- **Mês 2**: Plantões + Registro de ponto + Fechamentos básicos
- **Mês 3**: Antecipações + Testes + Deploy em produção

### 7.2 Phase 2 - Enhanced Features (2 meses)

- **Mês 4**: Dashboard avançado + Relatórios + Auditoria completa
- **Mês 5**: Integrações bancárias + Otimizações de performance

### 7.3 Phase 3 - Mobile & Advanced (2 meses)

- **Mês 6**: App mobile + APIs para terceiros
- **Mês 7**: Features avançadas + Analytics + Machine Learning

### 7.4 Milestones

- **M1**: Sistema de autenticação funcionando
- **M2**: Primeiro plantão criado e registros de ponto funcionais
- **M3**: Primeira antecipação processada com sucesso
- **M4**: 100 profissionais ativos no sistema
- **M5**: Integração bancária funcionando
- **M6**: App mobile lançado

---

## 8. Risk Assessment

### 8.1 Technical Risks

- **Alto**: Integração com sistemas bancários complexa
- **Médio**: Performance com grande volume de dados
- **Baixo**: Tecnologias escolhidas são maduras e estáveis

### 8.2 Business Risks

- **Alto**: Resistência dos usuários à mudança de processo
- **Médio**: Regulamentações trabalhistas podem mudar
- **Baixo**: Competidores com soluções similares

### 8.3 Mitigation Strategies

- **Treinamento**: Programa de capacitação para usuários
- **Legal**: Acompanhamento constante de mudanças regulatórias
- **Technical**: Arquitetura modular para facilitar manutenções
- **User Adoption**: Programa de incentivos para early adopters

---

## 9. Success Criteria & KPIs

### 9.1 Product KPIs

- **Adoption Rate**: % de profissionais ativos mensalmente
- **Feature Usage**: % de utilização de cada funcionalidade
- **Error Rate**: Taxa de erros no sistema
- **Performance**: Tempo médio de resposta

### 9.2 Business KPIs

- **Cost Reduction**: Redução de custos operacionais
- **Process Efficiency**: Tempo médio de processamento
- **User Satisfaction**: NPS e scores de satisfação
- **Revenue Impact**: Valor processado em antecipações

### 9.3 Criteria for Success

- **Technical**: Sistema estável com 99.9% uptime
- **Adoption**: 80% dos profissionais usando ativamente
- **Business**: ROI positivo em 12 meses
- **User**: NPS > 70 e redução de tickets de suporte

---

## 10. Appendices

### 10.1 Glossary

- **Plantão**: Período de trabalho definido para um profissional
- **Fechamento**: Consolidação mensal dos registros de um plantão
- **Antecipação**: Adiantamento de valores de fechamentos aprovados
- **CCB**: Cédula de Crédito Bancário, documento para antecipação
- **Glosa**: Desconto aplicado por irregularidades nos registros

### 10.2 References

- Documentação técnica do sistema (CLAUDE.md)
- Schema do banco de dados (Prisma schema)
- Análise de concorrentes
- Pesquisa com usuários
- Regulamentações trabalhistas aplicáveis
