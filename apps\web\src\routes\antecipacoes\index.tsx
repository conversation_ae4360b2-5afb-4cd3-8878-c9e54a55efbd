import { Link } from "@tanstack/react-router";
import { createFileRoute } from "@tanstack/react-router";
import { useQuery, useMutation } from "@tanstack/react-query";
import { requireFinanceiroRole } from "@/lib/route-guards";
import { useState } from "react";
import { api, type Antecipacao, type PaginatedResponse } from "@/lib/api";
import { Button } from "@/components/ui/button";
import { Card, CardContent, CardHeader, CardTitle } from "@/components/ui/card";
import { Badge } from "@/components/ui/badge";
import { Skeleton } from "@/components/ui/skeleton";
import { Input } from "@/components/ui/input";
import { Label } from "@/components/ui/label";
import { Table, TableBody, TableCell, TableHead, TableHeader, TableRow } from "@/components/ui/table";
import {
  DollarSign,
  Plus,
  Search,
  FileText,
  Download,
  Eye,
  Check,
  X,
  Clock,
  TrendingUp,
  AlertCircle,
  CheckCircle2,
  <PERSON>ader2,
  <PERSON>,
} from "lucide-react";
import { toast } from "sonner";
import { Select, SelectContent, SelectItem, SelectTrigger, SelectValue } from "@/components/ui/select";
import { formatDateString, formatCurrency } from "@/lib/utils";

function AntecipacoesIndex() {
  const [page, setPage] = useState(1);
  const [search, setSearch] = useState("");
  const [statusFilter, setStatusFilter] = useState<string>("TODOS");
  const [dataInicial, setDataInicial] = useState("");
  const [dataFinal, setDataFinal] = useState("");
  const limit = 10;

  const { data, isLoading, refetch } = useQuery<PaginatedResponse<Antecipacao>>({
    queryKey: ["antecipacoes", page, search, statusFilter, dataInicial, dataFinal],
    queryFn: () =>
      api.get("/antecipacoes", {
        page,
        limit,
        search,
        status: statusFilter === "TODOS" ? undefined : statusFilter,
        dataInicial: dataInicial || undefined,
        dataFinal: dataFinal || undefined,
      }),
  });

  const aprovarMutation = useMutation({
    mutationFn: async (uuid: string) => {
      return api.post(`/antecipacoes/${uuid}/aprovar`, {});
    },
    onSuccess: () => {
      toast.success("Antecipação aprovada com sucesso!");
      refetch();
    },
    onError: (error: any) => {
      toast.error(error.message || "Erro ao aprovar antecipação");
    },
  });

  const rejeitarMutation = useMutation({
    mutationFn: async (uuid: string) => {
      return api.post(`/antecipacoes/${uuid}/rejeitar`, { motivo: "Rejeitado pelo administrador" });
    },
    onSuccess: () => {
      toast.success("Antecipação rejeitada!");
      refetch();
    },
    onError: (error: any) => {
      toast.error(error.message || "Erro ao rejeitar antecipação");
    },
  });

  const gerarCCBMutation = useMutation({
    mutationFn: async (uuid: string) => {
      return api.post<any>(`/antecipacoes/${uuid}/gerar-ccb`);
    },
    onSuccess: (data: any) => {
      toast.success("CCB gerada com sucesso!");
      if (data?.url) {
        window.open(data.url, "_blank");
      }
      refetch();
    },
    onError: (error: any) => {
      toast.error(error.message || "Erro ao gerar CCB");
    },
  });

  const formatCurrency = (value: number) => {
    return new Intl.NumberFormat("pt-BR", {
      style: "currency",
      currency: "BRL",
    }).format(value);
  };

  const formatDate = (date: string) => {
    return formatDateString(date);
  };

  const getStatusBadge = (status: string) => {
    switch (status) {
      case "PENDENTE":
        return (
          <Badge variant="outline">
            <Clock className="h-3 w-3 mr-1" />
            Pendente
          </Badge>
        );
      case "APROVADA":
        return (
          <Badge variant="default">
            <CheckCircle2 className="h-3 w-3 mr-1" />
            Aprovada
          </Badge>
        );
      case "REJEITADA":
        return (
          <Badge variant="destructive">
            <X className="h-3 w-3 mr-1" />
            Rejeitada
          </Badge>
        );
      case "PAGA":
        return (
          <Badge className="bg-green-500">
            <DollarSign className="h-3 w-3 mr-1" />
            Paga
          </Badge>
        );
      default:
        return <Badge variant="secondary">{status}</Badge>;
    }
  };

  const totalPages = data ? Math.ceil(data.total / limit) : 0;

  const pendentes = data?.data.filter((a) => a.status === "PENDENTE") || [];
  const aprovadas = data?.data.filter((a) => a.status === "APROVADA") || [];
  const totalSolicitado = data?.data.reduce((acc, a) => acc + a.valorSolicitado, 0) || 0;
  const totalAprovado = aprovadas.reduce((acc, a) => acc + (a.valorAprovado || 0), 0) || 0;

  return (
    <div className="space-y-6">
      <div className="flex justify-between items-center">
        <div>
          <h1 className="text-3xl font-bold">Antecipações</h1>
          <p className="text-muted-foreground">Gerencie as solicitações de antecipação financeira</p>
        </div>
        <Link to="/antecipacoes/nova">
          <Button>
            <Plus className="mr-2 h-4 w-4" />
            Nova Antecipação
          </Button>
        </Link>
      </div>

      <div className="grid gap-4 md:grid-cols-4">
        <Card>
          <CardHeader className="flex flex-row items-center justify-between space-y-0 pb-2">
            <CardTitle className="text-sm font-medium">Total Solicitado</CardTitle>
            <DollarSign className="h-4 w-4 text-muted-foreground" />
          </CardHeader>
          <CardContent>
            <div className="text-2xl font-bold">{formatCurrency(totalSolicitado)}</div>
            <p className="text-xs text-muted-foreground">{data?.total || 0} solicitações</p>
          </CardContent>
        </Card>

        <Card>
          <CardHeader className="flex flex-row items-center justify-between space-y-0 pb-2">
            <CardTitle className="text-sm font-medium">Pendentes</CardTitle>
            <Clock className="h-4 w-4 text-yellow-500" />
          </CardHeader>
          <CardContent>
            <div className="text-2xl font-bold">{pendentes.length}</div>
            <p className="text-xs text-muted-foreground">Aguardando análise</p>
          </CardContent>
        </Card>

        <Card>
          <CardHeader className="flex flex-row items-center justify-between space-y-0 pb-2">
            <CardTitle className="text-sm font-medium">Total Aprovado</CardTitle>
            <CheckCircle2 className="h-4 w-4 text-green-500" />
          </CardHeader>
          <CardContent>
            <div className="text-2xl font-bold">{formatCurrency(totalAprovado)}</div>
            <p className="text-xs text-muted-foreground">{aprovadas.length} aprovadas</p>
          </CardContent>
        </Card>

        {/* <Card>
          <CardHeader className="flex flex-row items-center justify-between space-y-0 pb-2">
            <CardTitle className="text-sm font-medium">Taxa Média</CardTitle>
            <TrendingUp className="h-4 w-4 text-blue-500" />
          </CardHeader>
          <CardContent>
            <div className="text-2xl font-bold">2.5%</div>
            <p className="text-xs text-muted-foreground">Taxa de antecipação</p>
          </CardContent>
        </Card> */}
      </div>

      <Card>
        <CardHeader>
          <CardTitle>Lista de Antecipações</CardTitle>
        </CardHeader>
        <CardContent className="space-y-4">
          <div className="space-y-4">
            <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-5 gap-4">
              <div>
                <Label htmlFor="search">Buscar</Label>
                <Input
                  id="search"
                  placeholder="Buscar por profissional..."
                  value={search}
                  onChange={(e) => setSearch(e.target.value)}
                />
              </div>
              <div>
                <Label htmlFor="dataInicial">Data Inicial</Label>
                <Input
                  id="dataInicial"
                  type="date"
                  value={dataInicial}
                  onChange={(e) => setDataInicial(e.target.value)}
                />
              </div>
              <div>
                <Label htmlFor="dataFinal">Data Final</Label>
                <Input id="dataFinal" type="date" value={dataFinal} onChange={(e) => setDataFinal(e.target.value)} />
              </div>
              <div>
                <Label htmlFor="status">Status</Label>
                <Select value={statusFilter} onValueChange={setStatusFilter}>
                  <SelectTrigger>
                    <SelectValue />
                  </SelectTrigger>
                  <SelectContent>
                    <SelectItem value="TODOS">Todos</SelectItem>
                    <SelectItem value="PENDENTE">Pendente</SelectItem>
                    <SelectItem value="APROVADA">Aprovada</SelectItem>
                    <SelectItem value="REJEITADA">Rejeitada</SelectItem>
                    <SelectItem value="PAGA">Paga</SelectItem>
                  </SelectContent>
                </Select>
              </div>
              <div className="flex items-end gap-2">
                <Button
                  type="button"
                  variant="outline"
                  onClick={() => {
                    setSearch("");
                    setDataInicial("");
                    setDataFinal("");
                    setStatusFilter("TODOS");
                    setPage(1);
                  }}
                >
                  <Calendar className="h-4 w-4 mr-2" />
                  Limpar Filtros
                </Button>
              </div>
            </div>
          </div>

          {isLoading ? (
            <div className="space-y-4">
              {Array.from({ length: 3 }).map((_, i) => (
                <Skeleton key={i} className="h-20 w-full" />
              ))}
            </div>
          ) : data?.data.length === 0 ? (
            <div className="text-center py-12">
              <DollarSign className="mx-auto h-12 w-12 text-muted-foreground/20 mb-2" />
              <p className="text-muted-foreground">Nenhuma antecipação encontrada</p>
            </div>
          ) : (
            <Table>
              <TableHeader>
                <TableRow>
                  <TableHead>Ref.</TableHead>
                  <TableHead>Profissional</TableHead>
                  <TableHead>Cliente</TableHead>
                  <TableHead>Percentual</TableHead>
                  <TableHead>Taxa</TableHead>
                  <TableHead>Valor Solicitado</TableHead>
                  <TableHead>Valor Aprovado</TableHead>
                  <TableHead>Status</TableHead>
                  <TableHead className="text-right">Ações</TableHead>
                </TableRow>
              </TableHeader>
              <TableBody>
                {data?.data.map((antecipacao) => (
                  <TableRow key={antecipacao.id}>
                    <TableCell>
                      <div className="font-mono text-sm font-medium">#{antecipacao.id.toString().padStart(6, "0")}</div>
                    </TableCell>
                    <TableCell>
                      <div>
                        <div className="font-medium">
                          {antecipacao.profissional?.usuario?.nome || antecipacao.plantao?.profissional?.usuario?.nome}
                        </div>
                        <div className="text-sm text-muted-foreground">
                          {antecipacao.profissional?.usuario?.cpf || antecipacao.plantao?.profissional?.usuario?.cpf}
                        </div>
                      </div>
                    </TableCell>
                    <TableCell>
                      <div className="text-sm">
                        <div className="font-medium">{antecipacao.plantao?.cliente?.nome}</div>
                        <div className="text-muted-foreground">{antecipacao.plantao?.localAtendimento?.nome}</div>
                      </div>
                    </TableCell>
                    <TableCell>
                      <Badge variant="outline">{antecipacao.percentual}%</Badge>
                    </TableCell>
                    <TableCell>
                      {antecipacao.taxaAntecipacao ? (
                        <Badge variant="secondary">{antecipacao.taxaAntecipacao.toFixed(2)}%</Badge>
                      ) : (
                        <span className="text-muted-foreground">-</span>
                      )}
                    </TableCell>
                    <TableCell className="font-medium">{formatCurrency(antecipacao.valorSolicitado)}</TableCell>
                    <TableCell className="font-medium text-green-600">
                      {antecipacao.valorAprovado ? formatCurrency(antecipacao.valorAprovado) : "-"}
                    </TableCell>
                    <TableCell>{getStatusBadge(antecipacao.status)}</TableCell>
                    <TableCell className="text-right">
                      <div className="flex justify-end gap-2">
                        <Link to={"/antecipacoes/$uuid"} params={{ uuid: antecipacao.uuid }}>
                          <Button variant="ghost" size="sm">
                            <Eye className="h-4 w-4" />
                          </Button>
                        </Link>
                        {antecipacao.status === "PENDENTE" && (
                          <>
                            <Button
                              variant="ghost"
                              size="sm"
                              onClick={() => aprovarMutation.mutate(antecipacao.uuid)}
                              disabled={aprovarMutation.isPending}
                            >
                              {aprovarMutation.isPending ? (
                                <Loader2 className="h-4 w-4 animate-spin" />
                              ) : (
                                <Check className="h-4 w-4 text-green-600" />
                              )}
                            </Button>
                            <Button
                              variant="ghost"
                              size="sm"
                              onClick={() => rejeitarMutation.mutate(antecipacao.uuid)}
                              disabled={rejeitarMutation.isPending}
                            >
                              {rejeitarMutation.isPending ? (
                                <Loader2 className="h-4 w-4 animate-spin" />
                              ) : (
                                <X className="h-4 w-4 text-red-600" />
                              )}
                            </Button>
                          </>
                        )}
                        {antecipacao.status === "APROVADA" && !antecipacao.numeroCCB && (
                          <Button
                            variant="ghost"
                            size="sm"
                            onClick={() => gerarCCBMutation.mutate(antecipacao.uuid)}
                            disabled={gerarCCBMutation.isPending}
                          >
                            {gerarCCBMutation.isPending ? (
                              <Loader2 className="h-4 w-4 animate-spin" />
                            ) : (
                              <FileText className="h-4 w-4" />
                            )}
                          </Button>
                        )}
                        {antecipacao.numeroCCB && (
                          <Button variant="ghost" size="sm">
                            <Download className="h-4 w-4" />
                          </Button>
                        )}
                      </div>
                    </TableCell>
                  </TableRow>
                ))}
              </TableBody>
            </Table>
          )}

          {totalPages > 1 && (
            <div className="flex items-center justify-between">
              <p className="text-sm text-muted-foreground">
                Página {page} de {totalPages}
              </p>
              <div className="flex gap-2">
                <Button variant="outline" size="sm" onClick={() => setPage(page - 1)} disabled={page === 1}>
                  Anterior
                </Button>
                <Button variant="outline" size="sm" onClick={() => setPage(page + 1)} disabled={page === totalPages}>
                  Próxima
                </Button>
              </div>
            </div>
          )}
        </CardContent>
      </Card>
    </div>
  );
}

export const Route = createFileRoute("/antecipacoes/")({
  component: AntecipacoesIndex,
  beforeLoad: async () => {
    // Apenas usuários com role FINANCEIRO (ou admin/master) podem gerenciar antecipações
    await requireFinanceiroRole();
  },
});
