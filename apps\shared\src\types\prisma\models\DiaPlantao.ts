
/* !!! This is code generated by Prisma. Do not edit directly. !!! */
/* eslint-disable */
// @ts-nocheck 
/*
 * This file exports the `DiaPlantao` model and its related types.
 *
 * 🟢 You can import this file directly.
 */
import * as runtime from "@prisma/client/runtime/library"
import type * as $Enums from "../enums.ts"
import type * as Prisma from "../internal/prismaNamespace.ts"

/**
 * Model DiaPlantao
 * 
 */
export type DiaPlantaoModel = runtime.Types.Result.DefaultSelection<Prisma.$DiaPlantaoPayload>

export type AggregateDiaPlantao = {
  _count: DiaPlantaoCountAggregateOutputType | null
  _avg: DiaPlantaoAvgAggregateOutputType | null
  _sum: DiaPlantaoSumAggregateOutputType | null
  _min: DiaPlantaoMinAggregateOutputType | null
  _max: DiaPlantaoMaxAggregateOutputType | null
}

export type DiaPlantaoAvgAggregateOutputType = {
  id: number | null
  plantaoId: number | null
}

export type DiaPlantaoSumAggregateOutputType = {
  id: number | null
  plantaoId: number | null
}

export type DiaPlantaoMinAggregateOutputType = {
  id: number | null
  plantaoId: number | null
  data: Date | null
  horaEntrada: string | null
  horaSaida: string | null
  intervalo: string | null
  observacoes: string | null
  createdAt: Date | null
  updatedAt: Date | null
}

export type DiaPlantaoMaxAggregateOutputType = {
  id: number | null
  plantaoId: number | null
  data: Date | null
  horaEntrada: string | null
  horaSaida: string | null
  intervalo: string | null
  observacoes: string | null
  createdAt: Date | null
  updatedAt: Date | null
}

export type DiaPlantaoCountAggregateOutputType = {
  id: number
  plantaoId: number
  data: number
  horaEntrada: number
  horaSaida: number
  intervalo: number
  observacoes: number
  createdAt: number
  updatedAt: number
  _all: number
}


export type DiaPlantaoAvgAggregateInputType = {
  id?: true
  plantaoId?: true
}

export type DiaPlantaoSumAggregateInputType = {
  id?: true
  plantaoId?: true
}

export type DiaPlantaoMinAggregateInputType = {
  id?: true
  plantaoId?: true
  data?: true
  horaEntrada?: true
  horaSaida?: true
  intervalo?: true
  observacoes?: true
  createdAt?: true
  updatedAt?: true
}

export type DiaPlantaoMaxAggregateInputType = {
  id?: true
  plantaoId?: true
  data?: true
  horaEntrada?: true
  horaSaida?: true
  intervalo?: true
  observacoes?: true
  createdAt?: true
  updatedAt?: true
}

export type DiaPlantaoCountAggregateInputType = {
  id?: true
  plantaoId?: true
  data?: true
  horaEntrada?: true
  horaSaida?: true
  intervalo?: true
  observacoes?: true
  createdAt?: true
  updatedAt?: true
  _all?: true
}

export type DiaPlantaoAggregateArgs<ExtArgs extends runtime.Types.Extensions.InternalArgs = runtime.Types.Extensions.DefaultArgs> = {
  /**
   * Filter which DiaPlantao to aggregate.
   */
  where?: Prisma.DiaPlantaoWhereInput
  /**
   * {@link https://www.prisma.io/docs/concepts/components/prisma-client/sorting Sorting Docs}
   * 
   * Determine the order of DiaPlantaos to fetch.
   */
  orderBy?: Prisma.DiaPlantaoOrderByWithRelationInput | Prisma.DiaPlantaoOrderByWithRelationInput[]
  /**
   * {@link https://www.prisma.io/docs/concepts/components/prisma-client/pagination#cursor-based-pagination Cursor Docs}
   * 
   * Sets the start position
   */
  cursor?: Prisma.DiaPlantaoWhereUniqueInput
  /**
   * {@link https://www.prisma.io/docs/concepts/components/prisma-client/pagination Pagination Docs}
   * 
   * Take `±n` DiaPlantaos from the position of the cursor.
   */
  take?: number
  /**
   * {@link https://www.prisma.io/docs/concepts/components/prisma-client/pagination Pagination Docs}
   * 
   * Skip the first `n` DiaPlantaos.
   */
  skip?: number
  /**
   * {@link https://www.prisma.io/docs/concepts/components/prisma-client/aggregations Aggregation Docs}
   * 
   * Count returned DiaPlantaos
  **/
  _count?: true | DiaPlantaoCountAggregateInputType
  /**
   * {@link https://www.prisma.io/docs/concepts/components/prisma-client/aggregations Aggregation Docs}
   * 
   * Select which fields to average
  **/
  _avg?: DiaPlantaoAvgAggregateInputType
  /**
   * {@link https://www.prisma.io/docs/concepts/components/prisma-client/aggregations Aggregation Docs}
   * 
   * Select which fields to sum
  **/
  _sum?: DiaPlantaoSumAggregateInputType
  /**
   * {@link https://www.prisma.io/docs/concepts/components/prisma-client/aggregations Aggregation Docs}
   * 
   * Select which fields to find the minimum value
  **/
  _min?: DiaPlantaoMinAggregateInputType
  /**
   * {@link https://www.prisma.io/docs/concepts/components/prisma-client/aggregations Aggregation Docs}
   * 
   * Select which fields to find the maximum value
  **/
  _max?: DiaPlantaoMaxAggregateInputType
}

export type GetDiaPlantaoAggregateType<T extends DiaPlantaoAggregateArgs> = {
      [P in keyof T & keyof AggregateDiaPlantao]: P extends '_count' | 'count'
    ? T[P] extends true
      ? number
      : Prisma.GetScalarType<T[P], AggregateDiaPlantao[P]>
    : Prisma.GetScalarType<T[P], AggregateDiaPlantao[P]>
}




export type DiaPlantaoGroupByArgs<ExtArgs extends runtime.Types.Extensions.InternalArgs = runtime.Types.Extensions.DefaultArgs> = {
  where?: Prisma.DiaPlantaoWhereInput
  orderBy?: Prisma.DiaPlantaoOrderByWithAggregationInput | Prisma.DiaPlantaoOrderByWithAggregationInput[]
  by: Prisma.DiaPlantaoScalarFieldEnum[] | Prisma.DiaPlantaoScalarFieldEnum
  having?: Prisma.DiaPlantaoScalarWhereWithAggregatesInput
  take?: number
  skip?: number
  _count?: DiaPlantaoCountAggregateInputType | true
  _avg?: DiaPlantaoAvgAggregateInputType
  _sum?: DiaPlantaoSumAggregateInputType
  _min?: DiaPlantaoMinAggregateInputType
  _max?: DiaPlantaoMaxAggregateInputType
}

export type DiaPlantaoGroupByOutputType = {
  id: number
  plantaoId: number
  data: Date
  horaEntrada: string | null
  horaSaida: string | null
  intervalo: string | null
  observacoes: string | null
  createdAt: Date
  updatedAt: Date
  _count: DiaPlantaoCountAggregateOutputType | null
  _avg: DiaPlantaoAvgAggregateOutputType | null
  _sum: DiaPlantaoSumAggregateOutputType | null
  _min: DiaPlantaoMinAggregateOutputType | null
  _max: DiaPlantaoMaxAggregateOutputType | null
}

type GetDiaPlantaoGroupByPayload<T extends DiaPlantaoGroupByArgs> = Prisma.PrismaPromise<
  Array<
    Prisma.PickEnumerable<DiaPlantaoGroupByOutputType, T['by']> &
      {
        [P in ((keyof T) & (keyof DiaPlantaoGroupByOutputType))]: P extends '_count'
          ? T[P] extends boolean
            ? number
            : Prisma.GetScalarType<T[P], DiaPlantaoGroupByOutputType[P]>
          : Prisma.GetScalarType<T[P], DiaPlantaoGroupByOutputType[P]>
      }
    >
  >



export type DiaPlantaoWhereInput = {
  AND?: Prisma.DiaPlantaoWhereInput | Prisma.DiaPlantaoWhereInput[]
  OR?: Prisma.DiaPlantaoWhereInput[]
  NOT?: Prisma.DiaPlantaoWhereInput | Prisma.DiaPlantaoWhereInput[]
  id?: Prisma.IntFilter<"DiaPlantao"> | number
  plantaoId?: Prisma.IntFilter<"DiaPlantao"> | number
  data?: Prisma.DateTimeFilter<"DiaPlantao"> | Date | string
  horaEntrada?: Prisma.StringNullableFilter<"DiaPlantao"> | string | null
  horaSaida?: Prisma.StringNullableFilter<"DiaPlantao"> | string | null
  intervalo?: Prisma.StringNullableFilter<"DiaPlantao"> | string | null
  observacoes?: Prisma.StringNullableFilter<"DiaPlantao"> | string | null
  createdAt?: Prisma.DateTimeFilter<"DiaPlantao"> | Date | string
  updatedAt?: Prisma.DateTimeFilter<"DiaPlantao"> | Date | string
  plantao?: Prisma.XOR<Prisma.PlantaoScalarRelationFilter, Prisma.PlantaoWhereInput>
  presencaDiaPlantao?: Prisma.PresencaDiaPlantaoListRelationFilter
}

export type DiaPlantaoOrderByWithRelationInput = {
  id?: Prisma.SortOrder
  plantaoId?: Prisma.SortOrder
  data?: Prisma.SortOrder
  horaEntrada?: Prisma.SortOrderInput | Prisma.SortOrder
  horaSaida?: Prisma.SortOrderInput | Prisma.SortOrder
  intervalo?: Prisma.SortOrderInput | Prisma.SortOrder
  observacoes?: Prisma.SortOrderInput | Prisma.SortOrder
  createdAt?: Prisma.SortOrder
  updatedAt?: Prisma.SortOrder
  plantao?: Prisma.PlantaoOrderByWithRelationInput
  presencaDiaPlantao?: Prisma.PresencaDiaPlantaoOrderByRelationAggregateInput
  _relevance?: Prisma.DiaPlantaoOrderByRelevanceInput
}

export type DiaPlantaoWhereUniqueInput = Prisma.AtLeast<{
  id?: number
  plantao_data_unique?: Prisma.DiaPlantaoPlantao_data_uniqueCompoundUniqueInput
  AND?: Prisma.DiaPlantaoWhereInput | Prisma.DiaPlantaoWhereInput[]
  OR?: Prisma.DiaPlantaoWhereInput[]
  NOT?: Prisma.DiaPlantaoWhereInput | Prisma.DiaPlantaoWhereInput[]
  plantaoId?: Prisma.IntFilter<"DiaPlantao"> | number
  data?: Prisma.DateTimeFilter<"DiaPlantao"> | Date | string
  horaEntrada?: Prisma.StringNullableFilter<"DiaPlantao"> | string | null
  horaSaida?: Prisma.StringNullableFilter<"DiaPlantao"> | string | null
  intervalo?: Prisma.StringNullableFilter<"DiaPlantao"> | string | null
  observacoes?: Prisma.StringNullableFilter<"DiaPlantao"> | string | null
  createdAt?: Prisma.DateTimeFilter<"DiaPlantao"> | Date | string
  updatedAt?: Prisma.DateTimeFilter<"DiaPlantao"> | Date | string
  plantao?: Prisma.XOR<Prisma.PlantaoScalarRelationFilter, Prisma.PlantaoWhereInput>
  presencaDiaPlantao?: Prisma.PresencaDiaPlantaoListRelationFilter
}, "id" | "plantao_data_unique">

export type DiaPlantaoOrderByWithAggregationInput = {
  id?: Prisma.SortOrder
  plantaoId?: Prisma.SortOrder
  data?: Prisma.SortOrder
  horaEntrada?: Prisma.SortOrderInput | Prisma.SortOrder
  horaSaida?: Prisma.SortOrderInput | Prisma.SortOrder
  intervalo?: Prisma.SortOrderInput | Prisma.SortOrder
  observacoes?: Prisma.SortOrderInput | Prisma.SortOrder
  createdAt?: Prisma.SortOrder
  updatedAt?: Prisma.SortOrder
  _count?: Prisma.DiaPlantaoCountOrderByAggregateInput
  _avg?: Prisma.DiaPlantaoAvgOrderByAggregateInput
  _max?: Prisma.DiaPlantaoMaxOrderByAggregateInput
  _min?: Prisma.DiaPlantaoMinOrderByAggregateInput
  _sum?: Prisma.DiaPlantaoSumOrderByAggregateInput
}

export type DiaPlantaoScalarWhereWithAggregatesInput = {
  AND?: Prisma.DiaPlantaoScalarWhereWithAggregatesInput | Prisma.DiaPlantaoScalarWhereWithAggregatesInput[]
  OR?: Prisma.DiaPlantaoScalarWhereWithAggregatesInput[]
  NOT?: Prisma.DiaPlantaoScalarWhereWithAggregatesInput | Prisma.DiaPlantaoScalarWhereWithAggregatesInput[]
  id?: Prisma.IntWithAggregatesFilter<"DiaPlantao"> | number
  plantaoId?: Prisma.IntWithAggregatesFilter<"DiaPlantao"> | number
  data?: Prisma.DateTimeWithAggregatesFilter<"DiaPlantao"> | Date | string
  horaEntrada?: Prisma.StringNullableWithAggregatesFilter<"DiaPlantao"> | string | null
  horaSaida?: Prisma.StringNullableWithAggregatesFilter<"DiaPlantao"> | string | null
  intervalo?: Prisma.StringNullableWithAggregatesFilter<"DiaPlantao"> | string | null
  observacoes?: Prisma.StringNullableWithAggregatesFilter<"DiaPlantao"> | string | null
  createdAt?: Prisma.DateTimeWithAggregatesFilter<"DiaPlantao"> | Date | string
  updatedAt?: Prisma.DateTimeWithAggregatesFilter<"DiaPlantao"> | Date | string
}

export type DiaPlantaoCreateInput = {
  data: Date | string
  horaEntrada?: string | null
  horaSaida?: string | null
  intervalo?: string | null
  observacoes?: string | null
  createdAt?: Date | string
  updatedAt?: Date | string
  plantao: Prisma.PlantaoCreateNestedOneWithoutDiasPlantaoInput
  presencaDiaPlantao?: Prisma.PresencaDiaPlantaoCreateNestedManyWithoutDiaPlantaoInput
}

export type DiaPlantaoUncheckedCreateInput = {
  id?: number
  plantaoId: number
  data: Date | string
  horaEntrada?: string | null
  horaSaida?: string | null
  intervalo?: string | null
  observacoes?: string | null
  createdAt?: Date | string
  updatedAt?: Date | string
  presencaDiaPlantao?: Prisma.PresencaDiaPlantaoUncheckedCreateNestedManyWithoutDiaPlantaoInput
}

export type DiaPlantaoUpdateInput = {
  data?: Prisma.DateTimeFieldUpdateOperationsInput | Date | string
  horaEntrada?: Prisma.NullableStringFieldUpdateOperationsInput | string | null
  horaSaida?: Prisma.NullableStringFieldUpdateOperationsInput | string | null
  intervalo?: Prisma.NullableStringFieldUpdateOperationsInput | string | null
  observacoes?: Prisma.NullableStringFieldUpdateOperationsInput | string | null
  createdAt?: Prisma.DateTimeFieldUpdateOperationsInput | Date | string
  updatedAt?: Prisma.DateTimeFieldUpdateOperationsInput | Date | string
  plantao?: Prisma.PlantaoUpdateOneRequiredWithoutDiasPlantaoNestedInput
  presencaDiaPlantao?: Prisma.PresencaDiaPlantaoUpdateManyWithoutDiaPlantaoNestedInput
}

export type DiaPlantaoUncheckedUpdateInput = {
  id?: Prisma.IntFieldUpdateOperationsInput | number
  plantaoId?: Prisma.IntFieldUpdateOperationsInput | number
  data?: Prisma.DateTimeFieldUpdateOperationsInput | Date | string
  horaEntrada?: Prisma.NullableStringFieldUpdateOperationsInput | string | null
  horaSaida?: Prisma.NullableStringFieldUpdateOperationsInput | string | null
  intervalo?: Prisma.NullableStringFieldUpdateOperationsInput | string | null
  observacoes?: Prisma.NullableStringFieldUpdateOperationsInput | string | null
  createdAt?: Prisma.DateTimeFieldUpdateOperationsInput | Date | string
  updatedAt?: Prisma.DateTimeFieldUpdateOperationsInput | Date | string
  presencaDiaPlantao?: Prisma.PresencaDiaPlantaoUncheckedUpdateManyWithoutDiaPlantaoNestedInput
}

export type DiaPlantaoCreateManyInput = {
  id?: number
  plantaoId: number
  data: Date | string
  horaEntrada?: string | null
  horaSaida?: string | null
  intervalo?: string | null
  observacoes?: string | null
  createdAt?: Date | string
  updatedAt?: Date | string
}

export type DiaPlantaoUpdateManyMutationInput = {
  data?: Prisma.DateTimeFieldUpdateOperationsInput | Date | string
  horaEntrada?: Prisma.NullableStringFieldUpdateOperationsInput | string | null
  horaSaida?: Prisma.NullableStringFieldUpdateOperationsInput | string | null
  intervalo?: Prisma.NullableStringFieldUpdateOperationsInput | string | null
  observacoes?: Prisma.NullableStringFieldUpdateOperationsInput | string | null
  createdAt?: Prisma.DateTimeFieldUpdateOperationsInput | Date | string
  updatedAt?: Prisma.DateTimeFieldUpdateOperationsInput | Date | string
}

export type DiaPlantaoUncheckedUpdateManyInput = {
  id?: Prisma.IntFieldUpdateOperationsInput | number
  plantaoId?: Prisma.IntFieldUpdateOperationsInput | number
  data?: Prisma.DateTimeFieldUpdateOperationsInput | Date | string
  horaEntrada?: Prisma.NullableStringFieldUpdateOperationsInput | string | null
  horaSaida?: Prisma.NullableStringFieldUpdateOperationsInput | string | null
  intervalo?: Prisma.NullableStringFieldUpdateOperationsInput | string | null
  observacoes?: Prisma.NullableStringFieldUpdateOperationsInput | string | null
  createdAt?: Prisma.DateTimeFieldUpdateOperationsInput | Date | string
  updatedAt?: Prisma.DateTimeFieldUpdateOperationsInput | Date | string
}

export type DiaPlantaoListRelationFilter = {
  every?: Prisma.DiaPlantaoWhereInput
  some?: Prisma.DiaPlantaoWhereInput
  none?: Prisma.DiaPlantaoWhereInput
}

export type DiaPlantaoOrderByRelationAggregateInput = {
  _count?: Prisma.SortOrder
}

export type DiaPlantaoOrderByRelevanceInput = {
  fields: Prisma.DiaPlantaoOrderByRelevanceFieldEnum | Prisma.DiaPlantaoOrderByRelevanceFieldEnum[]
  sort: Prisma.SortOrder
  search: string
}

export type DiaPlantaoPlantao_data_uniqueCompoundUniqueInput = {
  plantaoId: number
  data: Date | string
}

export type DiaPlantaoCountOrderByAggregateInput = {
  id?: Prisma.SortOrder
  plantaoId?: Prisma.SortOrder
  data?: Prisma.SortOrder
  horaEntrada?: Prisma.SortOrder
  horaSaida?: Prisma.SortOrder
  intervalo?: Prisma.SortOrder
  observacoes?: Prisma.SortOrder
  createdAt?: Prisma.SortOrder
  updatedAt?: Prisma.SortOrder
}

export type DiaPlantaoAvgOrderByAggregateInput = {
  id?: Prisma.SortOrder
  plantaoId?: Prisma.SortOrder
}

export type DiaPlantaoMaxOrderByAggregateInput = {
  id?: Prisma.SortOrder
  plantaoId?: Prisma.SortOrder
  data?: Prisma.SortOrder
  horaEntrada?: Prisma.SortOrder
  horaSaida?: Prisma.SortOrder
  intervalo?: Prisma.SortOrder
  observacoes?: Prisma.SortOrder
  createdAt?: Prisma.SortOrder
  updatedAt?: Prisma.SortOrder
}

export type DiaPlantaoMinOrderByAggregateInput = {
  id?: Prisma.SortOrder
  plantaoId?: Prisma.SortOrder
  data?: Prisma.SortOrder
  horaEntrada?: Prisma.SortOrder
  horaSaida?: Prisma.SortOrder
  intervalo?: Prisma.SortOrder
  observacoes?: Prisma.SortOrder
  createdAt?: Prisma.SortOrder
  updatedAt?: Prisma.SortOrder
}

export type DiaPlantaoSumOrderByAggregateInput = {
  id?: Prisma.SortOrder
  plantaoId?: Prisma.SortOrder
}

export type DiaPlantaoScalarRelationFilter = {
  is?: Prisma.DiaPlantaoWhereInput
  isNot?: Prisma.DiaPlantaoWhereInput
}

export type DiaPlantaoCreateNestedManyWithoutPlantaoInput = {
  create?: Prisma.XOR<Prisma.DiaPlantaoCreateWithoutPlantaoInput, Prisma.DiaPlantaoUncheckedCreateWithoutPlantaoInput> | Prisma.DiaPlantaoCreateWithoutPlantaoInput[] | Prisma.DiaPlantaoUncheckedCreateWithoutPlantaoInput[]
  connectOrCreate?: Prisma.DiaPlantaoCreateOrConnectWithoutPlantaoInput | Prisma.DiaPlantaoCreateOrConnectWithoutPlantaoInput[]
  createMany?: Prisma.DiaPlantaoCreateManyPlantaoInputEnvelope
  connect?: Prisma.DiaPlantaoWhereUniqueInput | Prisma.DiaPlantaoWhereUniqueInput[]
}

export type DiaPlantaoUncheckedCreateNestedManyWithoutPlantaoInput = {
  create?: Prisma.XOR<Prisma.DiaPlantaoCreateWithoutPlantaoInput, Prisma.DiaPlantaoUncheckedCreateWithoutPlantaoInput> | Prisma.DiaPlantaoCreateWithoutPlantaoInput[] | Prisma.DiaPlantaoUncheckedCreateWithoutPlantaoInput[]
  connectOrCreate?: Prisma.DiaPlantaoCreateOrConnectWithoutPlantaoInput | Prisma.DiaPlantaoCreateOrConnectWithoutPlantaoInput[]
  createMany?: Prisma.DiaPlantaoCreateManyPlantaoInputEnvelope
  connect?: Prisma.DiaPlantaoWhereUniqueInput | Prisma.DiaPlantaoWhereUniqueInput[]
}

export type DiaPlantaoUpdateManyWithoutPlantaoNestedInput = {
  create?: Prisma.XOR<Prisma.DiaPlantaoCreateWithoutPlantaoInput, Prisma.DiaPlantaoUncheckedCreateWithoutPlantaoInput> | Prisma.DiaPlantaoCreateWithoutPlantaoInput[] | Prisma.DiaPlantaoUncheckedCreateWithoutPlantaoInput[]
  connectOrCreate?: Prisma.DiaPlantaoCreateOrConnectWithoutPlantaoInput | Prisma.DiaPlantaoCreateOrConnectWithoutPlantaoInput[]
  upsert?: Prisma.DiaPlantaoUpsertWithWhereUniqueWithoutPlantaoInput | Prisma.DiaPlantaoUpsertWithWhereUniqueWithoutPlantaoInput[]
  createMany?: Prisma.DiaPlantaoCreateManyPlantaoInputEnvelope
  set?: Prisma.DiaPlantaoWhereUniqueInput | Prisma.DiaPlantaoWhereUniqueInput[]
  disconnect?: Prisma.DiaPlantaoWhereUniqueInput | Prisma.DiaPlantaoWhereUniqueInput[]
  delete?: Prisma.DiaPlantaoWhereUniqueInput | Prisma.DiaPlantaoWhereUniqueInput[]
  connect?: Prisma.DiaPlantaoWhereUniqueInput | Prisma.DiaPlantaoWhereUniqueInput[]
  update?: Prisma.DiaPlantaoUpdateWithWhereUniqueWithoutPlantaoInput | Prisma.DiaPlantaoUpdateWithWhereUniqueWithoutPlantaoInput[]
  updateMany?: Prisma.DiaPlantaoUpdateManyWithWhereWithoutPlantaoInput | Prisma.DiaPlantaoUpdateManyWithWhereWithoutPlantaoInput[]
  deleteMany?: Prisma.DiaPlantaoScalarWhereInput | Prisma.DiaPlantaoScalarWhereInput[]
}

export type DiaPlantaoUncheckedUpdateManyWithoutPlantaoNestedInput = {
  create?: Prisma.XOR<Prisma.DiaPlantaoCreateWithoutPlantaoInput, Prisma.DiaPlantaoUncheckedCreateWithoutPlantaoInput> | Prisma.DiaPlantaoCreateWithoutPlantaoInput[] | Prisma.DiaPlantaoUncheckedCreateWithoutPlantaoInput[]
  connectOrCreate?: Prisma.DiaPlantaoCreateOrConnectWithoutPlantaoInput | Prisma.DiaPlantaoCreateOrConnectWithoutPlantaoInput[]
  upsert?: Prisma.DiaPlantaoUpsertWithWhereUniqueWithoutPlantaoInput | Prisma.DiaPlantaoUpsertWithWhereUniqueWithoutPlantaoInput[]
  createMany?: Prisma.DiaPlantaoCreateManyPlantaoInputEnvelope
  set?: Prisma.DiaPlantaoWhereUniqueInput | Prisma.DiaPlantaoWhereUniqueInput[]
  disconnect?: Prisma.DiaPlantaoWhereUniqueInput | Prisma.DiaPlantaoWhereUniqueInput[]
  delete?: Prisma.DiaPlantaoWhereUniqueInput | Prisma.DiaPlantaoWhereUniqueInput[]
  connect?: Prisma.DiaPlantaoWhereUniqueInput | Prisma.DiaPlantaoWhereUniqueInput[]
  update?: Prisma.DiaPlantaoUpdateWithWhereUniqueWithoutPlantaoInput | Prisma.DiaPlantaoUpdateWithWhereUniqueWithoutPlantaoInput[]
  updateMany?: Prisma.DiaPlantaoUpdateManyWithWhereWithoutPlantaoInput | Prisma.DiaPlantaoUpdateManyWithWhereWithoutPlantaoInput[]
  deleteMany?: Prisma.DiaPlantaoScalarWhereInput | Prisma.DiaPlantaoScalarWhereInput[]
}

export type DiaPlantaoCreateNestedOneWithoutPresencaDiaPlantaoInput = {
  create?: Prisma.XOR<Prisma.DiaPlantaoCreateWithoutPresencaDiaPlantaoInput, Prisma.DiaPlantaoUncheckedCreateWithoutPresencaDiaPlantaoInput>
  connectOrCreate?: Prisma.DiaPlantaoCreateOrConnectWithoutPresencaDiaPlantaoInput
  connect?: Prisma.DiaPlantaoWhereUniqueInput
}

export type DiaPlantaoUpdateOneRequiredWithoutPresencaDiaPlantaoNestedInput = {
  create?: Prisma.XOR<Prisma.DiaPlantaoCreateWithoutPresencaDiaPlantaoInput, Prisma.DiaPlantaoUncheckedCreateWithoutPresencaDiaPlantaoInput>
  connectOrCreate?: Prisma.DiaPlantaoCreateOrConnectWithoutPresencaDiaPlantaoInput
  upsert?: Prisma.DiaPlantaoUpsertWithoutPresencaDiaPlantaoInput
  connect?: Prisma.DiaPlantaoWhereUniqueInput
  update?: Prisma.XOR<Prisma.XOR<Prisma.DiaPlantaoUpdateToOneWithWhereWithoutPresencaDiaPlantaoInput, Prisma.DiaPlantaoUpdateWithoutPresencaDiaPlantaoInput>, Prisma.DiaPlantaoUncheckedUpdateWithoutPresencaDiaPlantaoInput>
}

export type DiaPlantaoCreateWithoutPlantaoInput = {
  data: Date | string
  horaEntrada?: string | null
  horaSaida?: string | null
  intervalo?: string | null
  observacoes?: string | null
  createdAt?: Date | string
  updatedAt?: Date | string
  presencaDiaPlantao?: Prisma.PresencaDiaPlantaoCreateNestedManyWithoutDiaPlantaoInput
}

export type DiaPlantaoUncheckedCreateWithoutPlantaoInput = {
  id?: number
  data: Date | string
  horaEntrada?: string | null
  horaSaida?: string | null
  intervalo?: string | null
  observacoes?: string | null
  createdAt?: Date | string
  updatedAt?: Date | string
  presencaDiaPlantao?: Prisma.PresencaDiaPlantaoUncheckedCreateNestedManyWithoutDiaPlantaoInput
}

export type DiaPlantaoCreateOrConnectWithoutPlantaoInput = {
  where: Prisma.DiaPlantaoWhereUniqueInput
  create: Prisma.XOR<Prisma.DiaPlantaoCreateWithoutPlantaoInput, Prisma.DiaPlantaoUncheckedCreateWithoutPlantaoInput>
}

export type DiaPlantaoCreateManyPlantaoInputEnvelope = {
  data: Prisma.DiaPlantaoCreateManyPlantaoInput | Prisma.DiaPlantaoCreateManyPlantaoInput[]
  skipDuplicates?: boolean
}

export type DiaPlantaoUpsertWithWhereUniqueWithoutPlantaoInput = {
  where: Prisma.DiaPlantaoWhereUniqueInput
  update: Prisma.XOR<Prisma.DiaPlantaoUpdateWithoutPlantaoInput, Prisma.DiaPlantaoUncheckedUpdateWithoutPlantaoInput>
  create: Prisma.XOR<Prisma.DiaPlantaoCreateWithoutPlantaoInput, Prisma.DiaPlantaoUncheckedCreateWithoutPlantaoInput>
}

export type DiaPlantaoUpdateWithWhereUniqueWithoutPlantaoInput = {
  where: Prisma.DiaPlantaoWhereUniqueInput
  data: Prisma.XOR<Prisma.DiaPlantaoUpdateWithoutPlantaoInput, Prisma.DiaPlantaoUncheckedUpdateWithoutPlantaoInput>
}

export type DiaPlantaoUpdateManyWithWhereWithoutPlantaoInput = {
  where: Prisma.DiaPlantaoScalarWhereInput
  data: Prisma.XOR<Prisma.DiaPlantaoUpdateManyMutationInput, Prisma.DiaPlantaoUncheckedUpdateManyWithoutPlantaoInput>
}

export type DiaPlantaoScalarWhereInput = {
  AND?: Prisma.DiaPlantaoScalarWhereInput | Prisma.DiaPlantaoScalarWhereInput[]
  OR?: Prisma.DiaPlantaoScalarWhereInput[]
  NOT?: Prisma.DiaPlantaoScalarWhereInput | Prisma.DiaPlantaoScalarWhereInput[]
  id?: Prisma.IntFilter<"DiaPlantao"> | number
  plantaoId?: Prisma.IntFilter<"DiaPlantao"> | number
  data?: Prisma.DateTimeFilter<"DiaPlantao"> | Date | string
  horaEntrada?: Prisma.StringNullableFilter<"DiaPlantao"> | string | null
  horaSaida?: Prisma.StringNullableFilter<"DiaPlantao"> | string | null
  intervalo?: Prisma.StringNullableFilter<"DiaPlantao"> | string | null
  observacoes?: Prisma.StringNullableFilter<"DiaPlantao"> | string | null
  createdAt?: Prisma.DateTimeFilter<"DiaPlantao"> | Date | string
  updatedAt?: Prisma.DateTimeFilter<"DiaPlantao"> | Date | string
}

export type DiaPlantaoCreateWithoutPresencaDiaPlantaoInput = {
  data: Date | string
  horaEntrada?: string | null
  horaSaida?: string | null
  intervalo?: string | null
  observacoes?: string | null
  createdAt?: Date | string
  updatedAt?: Date | string
  plantao: Prisma.PlantaoCreateNestedOneWithoutDiasPlantaoInput
}

export type DiaPlantaoUncheckedCreateWithoutPresencaDiaPlantaoInput = {
  id?: number
  plantaoId: number
  data: Date | string
  horaEntrada?: string | null
  horaSaida?: string | null
  intervalo?: string | null
  observacoes?: string | null
  createdAt?: Date | string
  updatedAt?: Date | string
}

export type DiaPlantaoCreateOrConnectWithoutPresencaDiaPlantaoInput = {
  where: Prisma.DiaPlantaoWhereUniqueInput
  create: Prisma.XOR<Prisma.DiaPlantaoCreateWithoutPresencaDiaPlantaoInput, Prisma.DiaPlantaoUncheckedCreateWithoutPresencaDiaPlantaoInput>
}

export type DiaPlantaoUpsertWithoutPresencaDiaPlantaoInput = {
  update: Prisma.XOR<Prisma.DiaPlantaoUpdateWithoutPresencaDiaPlantaoInput, Prisma.DiaPlantaoUncheckedUpdateWithoutPresencaDiaPlantaoInput>
  create: Prisma.XOR<Prisma.DiaPlantaoCreateWithoutPresencaDiaPlantaoInput, Prisma.DiaPlantaoUncheckedCreateWithoutPresencaDiaPlantaoInput>
  where?: Prisma.DiaPlantaoWhereInput
}

export type DiaPlantaoUpdateToOneWithWhereWithoutPresencaDiaPlantaoInput = {
  where?: Prisma.DiaPlantaoWhereInput
  data: Prisma.XOR<Prisma.DiaPlantaoUpdateWithoutPresencaDiaPlantaoInput, Prisma.DiaPlantaoUncheckedUpdateWithoutPresencaDiaPlantaoInput>
}

export type DiaPlantaoUpdateWithoutPresencaDiaPlantaoInput = {
  data?: Prisma.DateTimeFieldUpdateOperationsInput | Date | string
  horaEntrada?: Prisma.NullableStringFieldUpdateOperationsInput | string | null
  horaSaida?: Prisma.NullableStringFieldUpdateOperationsInput | string | null
  intervalo?: Prisma.NullableStringFieldUpdateOperationsInput | string | null
  observacoes?: Prisma.NullableStringFieldUpdateOperationsInput | string | null
  createdAt?: Prisma.DateTimeFieldUpdateOperationsInput | Date | string
  updatedAt?: Prisma.DateTimeFieldUpdateOperationsInput | Date | string
  plantao?: Prisma.PlantaoUpdateOneRequiredWithoutDiasPlantaoNestedInput
}

export type DiaPlantaoUncheckedUpdateWithoutPresencaDiaPlantaoInput = {
  id?: Prisma.IntFieldUpdateOperationsInput | number
  plantaoId?: Prisma.IntFieldUpdateOperationsInput | number
  data?: Prisma.DateTimeFieldUpdateOperationsInput | Date | string
  horaEntrada?: Prisma.NullableStringFieldUpdateOperationsInput | string | null
  horaSaida?: Prisma.NullableStringFieldUpdateOperationsInput | string | null
  intervalo?: Prisma.NullableStringFieldUpdateOperationsInput | string | null
  observacoes?: Prisma.NullableStringFieldUpdateOperationsInput | string | null
  createdAt?: Prisma.DateTimeFieldUpdateOperationsInput | Date | string
  updatedAt?: Prisma.DateTimeFieldUpdateOperationsInput | Date | string
}

export type DiaPlantaoCreateManyPlantaoInput = {
  id?: number
  data: Date | string
  horaEntrada?: string | null
  horaSaida?: string | null
  intervalo?: string | null
  observacoes?: string | null
  createdAt?: Date | string
  updatedAt?: Date | string
}

export type DiaPlantaoUpdateWithoutPlantaoInput = {
  data?: Prisma.DateTimeFieldUpdateOperationsInput | Date | string
  horaEntrada?: Prisma.NullableStringFieldUpdateOperationsInput | string | null
  horaSaida?: Prisma.NullableStringFieldUpdateOperationsInput | string | null
  intervalo?: Prisma.NullableStringFieldUpdateOperationsInput | string | null
  observacoes?: Prisma.NullableStringFieldUpdateOperationsInput | string | null
  createdAt?: Prisma.DateTimeFieldUpdateOperationsInput | Date | string
  updatedAt?: Prisma.DateTimeFieldUpdateOperationsInput | Date | string
  presencaDiaPlantao?: Prisma.PresencaDiaPlantaoUpdateManyWithoutDiaPlantaoNestedInput
}

export type DiaPlantaoUncheckedUpdateWithoutPlantaoInput = {
  id?: Prisma.IntFieldUpdateOperationsInput | number
  data?: Prisma.DateTimeFieldUpdateOperationsInput | Date | string
  horaEntrada?: Prisma.NullableStringFieldUpdateOperationsInput | string | null
  horaSaida?: Prisma.NullableStringFieldUpdateOperationsInput | string | null
  intervalo?: Prisma.NullableStringFieldUpdateOperationsInput | string | null
  observacoes?: Prisma.NullableStringFieldUpdateOperationsInput | string | null
  createdAt?: Prisma.DateTimeFieldUpdateOperationsInput | Date | string
  updatedAt?: Prisma.DateTimeFieldUpdateOperationsInput | Date | string
  presencaDiaPlantao?: Prisma.PresencaDiaPlantaoUncheckedUpdateManyWithoutDiaPlantaoNestedInput
}

export type DiaPlantaoUncheckedUpdateManyWithoutPlantaoInput = {
  id?: Prisma.IntFieldUpdateOperationsInput | number
  data?: Prisma.DateTimeFieldUpdateOperationsInput | Date | string
  horaEntrada?: Prisma.NullableStringFieldUpdateOperationsInput | string | null
  horaSaida?: Prisma.NullableStringFieldUpdateOperationsInput | string | null
  intervalo?: Prisma.NullableStringFieldUpdateOperationsInput | string | null
  observacoes?: Prisma.NullableStringFieldUpdateOperationsInput | string | null
  createdAt?: Prisma.DateTimeFieldUpdateOperationsInput | Date | string
  updatedAt?: Prisma.DateTimeFieldUpdateOperationsInput | Date | string
}


/**
 * Count Type DiaPlantaoCountOutputType
 */

export type DiaPlantaoCountOutputType = {
  presencaDiaPlantao: number
}

export type DiaPlantaoCountOutputTypeSelect<ExtArgs extends runtime.Types.Extensions.InternalArgs = runtime.Types.Extensions.DefaultArgs> = {
  presencaDiaPlantao?: boolean | DiaPlantaoCountOutputTypeCountPresencaDiaPlantaoArgs
}

/**
 * DiaPlantaoCountOutputType without action
 */
export type DiaPlantaoCountOutputTypeDefaultArgs<ExtArgs extends runtime.Types.Extensions.InternalArgs = runtime.Types.Extensions.DefaultArgs> = {
  /**
   * Select specific fields to fetch from the DiaPlantaoCountOutputType
   */
  select?: Prisma.DiaPlantaoCountOutputTypeSelect<ExtArgs> | null
}

/**
 * DiaPlantaoCountOutputType without action
 */
export type DiaPlantaoCountOutputTypeCountPresencaDiaPlantaoArgs<ExtArgs extends runtime.Types.Extensions.InternalArgs = runtime.Types.Extensions.DefaultArgs> = {
  where?: Prisma.PresencaDiaPlantaoWhereInput
}


export type DiaPlantaoSelect<ExtArgs extends runtime.Types.Extensions.InternalArgs = runtime.Types.Extensions.DefaultArgs> = runtime.Types.Extensions.GetSelect<{
  id?: boolean
  plantaoId?: boolean
  data?: boolean
  horaEntrada?: boolean
  horaSaida?: boolean
  intervalo?: boolean
  observacoes?: boolean
  createdAt?: boolean
  updatedAt?: boolean
  plantao?: boolean | Prisma.PlantaoDefaultArgs<ExtArgs>
  presencaDiaPlantao?: boolean | Prisma.DiaPlantao$presencaDiaPlantaoArgs<ExtArgs>
  _count?: boolean | Prisma.DiaPlantaoCountOutputTypeDefaultArgs<ExtArgs>
}, ExtArgs["result"]["diaPlantao"]>



export type DiaPlantaoSelectScalar = {
  id?: boolean
  plantaoId?: boolean
  data?: boolean
  horaEntrada?: boolean
  horaSaida?: boolean
  intervalo?: boolean
  observacoes?: boolean
  createdAt?: boolean
  updatedAt?: boolean
}

export type DiaPlantaoOmit<ExtArgs extends runtime.Types.Extensions.InternalArgs = runtime.Types.Extensions.DefaultArgs> = runtime.Types.Extensions.GetOmit<"id" | "plantaoId" | "data" | "horaEntrada" | "horaSaida" | "intervalo" | "observacoes" | "createdAt" | "updatedAt", ExtArgs["result"]["diaPlantao"]>
export type DiaPlantaoInclude<ExtArgs extends runtime.Types.Extensions.InternalArgs = runtime.Types.Extensions.DefaultArgs> = {
  plantao?: boolean | Prisma.PlantaoDefaultArgs<ExtArgs>
  presencaDiaPlantao?: boolean | Prisma.DiaPlantao$presencaDiaPlantaoArgs<ExtArgs>
  _count?: boolean | Prisma.DiaPlantaoCountOutputTypeDefaultArgs<ExtArgs>
}

export type $DiaPlantaoPayload<ExtArgs extends runtime.Types.Extensions.InternalArgs = runtime.Types.Extensions.DefaultArgs> = {
  name: "DiaPlantao"
  objects: {
    plantao: Prisma.$PlantaoPayload<ExtArgs>
    presencaDiaPlantao: Prisma.$PresencaDiaPlantaoPayload<ExtArgs>[]
  }
  scalars: runtime.Types.Extensions.GetPayloadResult<{
    id: number
    plantaoId: number
    data: Date
    horaEntrada: string | null
    horaSaida: string | null
    intervalo: string | null
    observacoes: string | null
    createdAt: Date
    updatedAt: Date
  }, ExtArgs["result"]["diaPlantao"]>
  composites: {}
}

export type DiaPlantaoGetPayload<S extends boolean | null | undefined | DiaPlantaoDefaultArgs> = runtime.Types.Result.GetResult<Prisma.$DiaPlantaoPayload, S>

export type DiaPlantaoCountArgs<ExtArgs extends runtime.Types.Extensions.InternalArgs = runtime.Types.Extensions.DefaultArgs> =
  Omit<DiaPlantaoFindManyArgs, 'select' | 'include' | 'distinct' | 'omit'> & {
    select?: DiaPlantaoCountAggregateInputType | true
  }

export interface DiaPlantaoDelegate<ExtArgs extends runtime.Types.Extensions.InternalArgs = runtime.Types.Extensions.DefaultArgs, GlobalOmitOptions = {}> {
  [K: symbol]: { types: Prisma.TypeMap<ExtArgs>['model']['DiaPlantao'], meta: { name: 'DiaPlantao' } }
  /**
   * Find zero or one DiaPlantao that matches the filter.
   * @param {DiaPlantaoFindUniqueArgs} args - Arguments to find a DiaPlantao
   * @example
   * // Get one DiaPlantao
   * const diaPlantao = await prisma.diaPlantao.findUnique({
   *   where: {
   *     // ... provide filter here
   *   }
   * })
   */
  findUnique<T extends DiaPlantaoFindUniqueArgs>(args: Prisma.SelectSubset<T, DiaPlantaoFindUniqueArgs<ExtArgs>>): Prisma.Prisma__DiaPlantaoClient<runtime.Types.Result.GetResult<Prisma.$DiaPlantaoPayload<ExtArgs>, T, "findUnique", GlobalOmitOptions> | null, null, ExtArgs, GlobalOmitOptions>

  /**
   * Find one DiaPlantao that matches the filter or throw an error with `error.code='P2025'`
   * if no matches were found.
   * @param {DiaPlantaoFindUniqueOrThrowArgs} args - Arguments to find a DiaPlantao
   * @example
   * // Get one DiaPlantao
   * const diaPlantao = await prisma.diaPlantao.findUniqueOrThrow({
   *   where: {
   *     // ... provide filter here
   *   }
   * })
   */
  findUniqueOrThrow<T extends DiaPlantaoFindUniqueOrThrowArgs>(args: Prisma.SelectSubset<T, DiaPlantaoFindUniqueOrThrowArgs<ExtArgs>>): Prisma.Prisma__DiaPlantaoClient<runtime.Types.Result.GetResult<Prisma.$DiaPlantaoPayload<ExtArgs>, T, "findUniqueOrThrow", GlobalOmitOptions>, never, ExtArgs, GlobalOmitOptions>

  /**
   * Find the first DiaPlantao that matches the filter.
   * Note, that providing `undefined` is treated as the value not being there.
   * Read more here: https://pris.ly/d/null-undefined
   * @param {DiaPlantaoFindFirstArgs} args - Arguments to find a DiaPlantao
   * @example
   * // Get one DiaPlantao
   * const diaPlantao = await prisma.diaPlantao.findFirst({
   *   where: {
   *     // ... provide filter here
   *   }
   * })
   */
  findFirst<T extends DiaPlantaoFindFirstArgs>(args?: Prisma.SelectSubset<T, DiaPlantaoFindFirstArgs<ExtArgs>>): Prisma.Prisma__DiaPlantaoClient<runtime.Types.Result.GetResult<Prisma.$DiaPlantaoPayload<ExtArgs>, T, "findFirst", GlobalOmitOptions> | null, null, ExtArgs, GlobalOmitOptions>

  /**
   * Find the first DiaPlantao that matches the filter or
   * throw `PrismaKnownClientError` with `P2025` code if no matches were found.
   * Note, that providing `undefined` is treated as the value not being there.
   * Read more here: https://pris.ly/d/null-undefined
   * @param {DiaPlantaoFindFirstOrThrowArgs} args - Arguments to find a DiaPlantao
   * @example
   * // Get one DiaPlantao
   * const diaPlantao = await prisma.diaPlantao.findFirstOrThrow({
   *   where: {
   *     // ... provide filter here
   *   }
   * })
   */
  findFirstOrThrow<T extends DiaPlantaoFindFirstOrThrowArgs>(args?: Prisma.SelectSubset<T, DiaPlantaoFindFirstOrThrowArgs<ExtArgs>>): Prisma.Prisma__DiaPlantaoClient<runtime.Types.Result.GetResult<Prisma.$DiaPlantaoPayload<ExtArgs>, T, "findFirstOrThrow", GlobalOmitOptions>, never, ExtArgs, GlobalOmitOptions>

  /**
   * Find zero or more DiaPlantaos that matches the filter.
   * Note, that providing `undefined` is treated as the value not being there.
   * Read more here: https://pris.ly/d/null-undefined
   * @param {DiaPlantaoFindManyArgs} args - Arguments to filter and select certain fields only.
   * @example
   * // Get all DiaPlantaos
   * const diaPlantaos = await prisma.diaPlantao.findMany()
   * 
   * // Get first 10 DiaPlantaos
   * const diaPlantaos = await prisma.diaPlantao.findMany({ take: 10 })
   * 
   * // Only select the `id`
   * const diaPlantaoWithIdOnly = await prisma.diaPlantao.findMany({ select: { id: true } })
   * 
   */
  findMany<T extends DiaPlantaoFindManyArgs>(args?: Prisma.SelectSubset<T, DiaPlantaoFindManyArgs<ExtArgs>>): Prisma.PrismaPromise<runtime.Types.Result.GetResult<Prisma.$DiaPlantaoPayload<ExtArgs>, T, "findMany", GlobalOmitOptions>>

  /**
   * Create a DiaPlantao.
   * @param {DiaPlantaoCreateArgs} args - Arguments to create a DiaPlantao.
   * @example
   * // Create one DiaPlantao
   * const DiaPlantao = await prisma.diaPlantao.create({
   *   data: {
   *     // ... data to create a DiaPlantao
   *   }
   * })
   * 
   */
  create<T extends DiaPlantaoCreateArgs>(args: Prisma.SelectSubset<T, DiaPlantaoCreateArgs<ExtArgs>>): Prisma.Prisma__DiaPlantaoClient<runtime.Types.Result.GetResult<Prisma.$DiaPlantaoPayload<ExtArgs>, T, "create", GlobalOmitOptions>, never, ExtArgs, GlobalOmitOptions>

  /**
   * Create many DiaPlantaos.
   * @param {DiaPlantaoCreateManyArgs} args - Arguments to create many DiaPlantaos.
   * @example
   * // Create many DiaPlantaos
   * const diaPlantao = await prisma.diaPlantao.createMany({
   *   data: [
   *     // ... provide data here
   *   ]
   * })
   *     
   */
  createMany<T extends DiaPlantaoCreateManyArgs>(args?: Prisma.SelectSubset<T, DiaPlantaoCreateManyArgs<ExtArgs>>): Prisma.PrismaPromise<Prisma.BatchPayload>

  /**
   * Delete a DiaPlantao.
   * @param {DiaPlantaoDeleteArgs} args - Arguments to delete one DiaPlantao.
   * @example
   * // Delete one DiaPlantao
   * const DiaPlantao = await prisma.diaPlantao.delete({
   *   where: {
   *     // ... filter to delete one DiaPlantao
   *   }
   * })
   * 
   */
  delete<T extends DiaPlantaoDeleteArgs>(args: Prisma.SelectSubset<T, DiaPlantaoDeleteArgs<ExtArgs>>): Prisma.Prisma__DiaPlantaoClient<runtime.Types.Result.GetResult<Prisma.$DiaPlantaoPayload<ExtArgs>, T, "delete", GlobalOmitOptions>, never, ExtArgs, GlobalOmitOptions>

  /**
   * Update one DiaPlantao.
   * @param {DiaPlantaoUpdateArgs} args - Arguments to update one DiaPlantao.
   * @example
   * // Update one DiaPlantao
   * const diaPlantao = await prisma.diaPlantao.update({
   *   where: {
   *     // ... provide filter here
   *   },
   *   data: {
   *     // ... provide data here
   *   }
   * })
   * 
   */
  update<T extends DiaPlantaoUpdateArgs>(args: Prisma.SelectSubset<T, DiaPlantaoUpdateArgs<ExtArgs>>): Prisma.Prisma__DiaPlantaoClient<runtime.Types.Result.GetResult<Prisma.$DiaPlantaoPayload<ExtArgs>, T, "update", GlobalOmitOptions>, never, ExtArgs, GlobalOmitOptions>

  /**
   * Delete zero or more DiaPlantaos.
   * @param {DiaPlantaoDeleteManyArgs} args - Arguments to filter DiaPlantaos to delete.
   * @example
   * // Delete a few DiaPlantaos
   * const { count } = await prisma.diaPlantao.deleteMany({
   *   where: {
   *     // ... provide filter here
   *   }
   * })
   * 
   */
  deleteMany<T extends DiaPlantaoDeleteManyArgs>(args?: Prisma.SelectSubset<T, DiaPlantaoDeleteManyArgs<ExtArgs>>): Prisma.PrismaPromise<Prisma.BatchPayload>

  /**
   * Update zero or more DiaPlantaos.
   * Note, that providing `undefined` is treated as the value not being there.
   * Read more here: https://pris.ly/d/null-undefined
   * @param {DiaPlantaoUpdateManyArgs} args - Arguments to update one or more rows.
   * @example
   * // Update many DiaPlantaos
   * const diaPlantao = await prisma.diaPlantao.updateMany({
   *   where: {
   *     // ... provide filter here
   *   },
   *   data: {
   *     // ... provide data here
   *   }
   * })
   * 
   */
  updateMany<T extends DiaPlantaoUpdateManyArgs>(args: Prisma.SelectSubset<T, DiaPlantaoUpdateManyArgs<ExtArgs>>): Prisma.PrismaPromise<Prisma.BatchPayload>

  /**
   * Create or update one DiaPlantao.
   * @param {DiaPlantaoUpsertArgs} args - Arguments to update or create a DiaPlantao.
   * @example
   * // Update or create a DiaPlantao
   * const diaPlantao = await prisma.diaPlantao.upsert({
   *   create: {
   *     // ... data to create a DiaPlantao
   *   },
   *   update: {
   *     // ... in case it already exists, update
   *   },
   *   where: {
   *     // ... the filter for the DiaPlantao we want to update
   *   }
   * })
   */
  upsert<T extends DiaPlantaoUpsertArgs>(args: Prisma.SelectSubset<T, DiaPlantaoUpsertArgs<ExtArgs>>): Prisma.Prisma__DiaPlantaoClient<runtime.Types.Result.GetResult<Prisma.$DiaPlantaoPayload<ExtArgs>, T, "upsert", GlobalOmitOptions>, never, ExtArgs, GlobalOmitOptions>


  /**
   * Count the number of DiaPlantaos.
   * Note, that providing `undefined` is treated as the value not being there.
   * Read more here: https://pris.ly/d/null-undefined
   * @param {DiaPlantaoCountArgs} args - Arguments to filter DiaPlantaos to count.
   * @example
   * // Count the number of DiaPlantaos
   * const count = await prisma.diaPlantao.count({
   *   where: {
   *     // ... the filter for the DiaPlantaos we want to count
   *   }
   * })
  **/
  count<T extends DiaPlantaoCountArgs>(
    args?: Prisma.Subset<T, DiaPlantaoCountArgs>,
  ): Prisma.PrismaPromise<
    T extends runtime.Types.Utils.Record<'select', any>
      ? T['select'] extends true
        ? number
        : Prisma.GetScalarType<T['select'], DiaPlantaoCountAggregateOutputType>
      : number
  >

  /**
   * Allows you to perform aggregations operations on a DiaPlantao.
   * Note, that providing `undefined` is treated as the value not being there.
   * Read more here: https://pris.ly/d/null-undefined
   * @param {DiaPlantaoAggregateArgs} args - Select which aggregations you would like to apply and on what fields.
   * @example
   * // Ordered by age ascending
   * // Where email contains prisma.io
   * // Limited to the 10 users
   * const aggregations = await prisma.user.aggregate({
   *   _avg: {
   *     age: true,
   *   },
   *   where: {
   *     email: {
   *       contains: "prisma.io",
   *     },
   *   },
   *   orderBy: {
   *     age: "asc",
   *   },
   *   take: 10,
   * })
  **/
  aggregate<T extends DiaPlantaoAggregateArgs>(args: Prisma.Subset<T, DiaPlantaoAggregateArgs>): Prisma.PrismaPromise<GetDiaPlantaoAggregateType<T>>

  /**
   * Group by DiaPlantao.
   * Note, that providing `undefined` is treated as the value not being there.
   * Read more here: https://pris.ly/d/null-undefined
   * @param {DiaPlantaoGroupByArgs} args - Group by arguments.
   * @example
   * // Group by city, order by createdAt, get count
   * const result = await prisma.user.groupBy({
   *   by: ['city', 'createdAt'],
   *   orderBy: {
   *     createdAt: true
   *   },
   *   _count: {
   *     _all: true
   *   },
   * })
   * 
  **/
  groupBy<
    T extends DiaPlantaoGroupByArgs,
    HasSelectOrTake extends Prisma.Or<
      Prisma.Extends<'skip', Prisma.Keys<T>>,
      Prisma.Extends<'take', Prisma.Keys<T>>
    >,
    OrderByArg extends Prisma.True extends HasSelectOrTake
      ? { orderBy: DiaPlantaoGroupByArgs['orderBy'] }
      : { orderBy?: DiaPlantaoGroupByArgs['orderBy'] },
    OrderFields extends Prisma.ExcludeUnderscoreKeys<Prisma.Keys<Prisma.MaybeTupleToUnion<T['orderBy']>>>,
    ByFields extends Prisma.MaybeTupleToUnion<T['by']>,
    ByValid extends Prisma.Has<ByFields, OrderFields>,
    HavingFields extends Prisma.GetHavingFields<T['having']>,
    HavingValid extends Prisma.Has<ByFields, HavingFields>,
    ByEmpty extends T['by'] extends never[] ? Prisma.True : Prisma.False,
    InputErrors extends ByEmpty extends Prisma.True
    ? `Error: "by" must not be empty.`
    : HavingValid extends Prisma.False
    ? {
        [P in HavingFields]: P extends ByFields
          ? never
          : P extends string
          ? `Error: Field "${P}" used in "having" needs to be provided in "by".`
          : [
              Error,
              'Field ',
              P,
              ` in "having" needs to be provided in "by"`,
            ]
      }[HavingFields]
    : 'take' extends Prisma.Keys<T>
    ? 'orderBy' extends Prisma.Keys<T>
      ? ByValid extends Prisma.True
        ? {}
        : {
            [P in OrderFields]: P extends ByFields
              ? never
              : `Error: Field "${P}" in "orderBy" needs to be provided in "by"`
          }[OrderFields]
      : 'Error: If you provide "take", you also need to provide "orderBy"'
    : 'skip' extends Prisma.Keys<T>
    ? 'orderBy' extends Prisma.Keys<T>
      ? ByValid extends Prisma.True
        ? {}
        : {
            [P in OrderFields]: P extends ByFields
              ? never
              : `Error: Field "${P}" in "orderBy" needs to be provided in "by"`
          }[OrderFields]
      : 'Error: If you provide "skip", you also need to provide "orderBy"'
    : ByValid extends Prisma.True
    ? {}
    : {
        [P in OrderFields]: P extends ByFields
          ? never
          : `Error: Field "${P}" in "orderBy" needs to be provided in "by"`
      }[OrderFields]
  >(args: Prisma.SubsetIntersection<T, DiaPlantaoGroupByArgs, OrderByArg> & InputErrors): {} extends InputErrors ? GetDiaPlantaoGroupByPayload<T> : Prisma.PrismaPromise<InputErrors>
/**
 * Fields of the DiaPlantao model
 */
readonly fields: DiaPlantaoFieldRefs;
}

/**
 * The delegate class that acts as a "Promise-like" for DiaPlantao.
 * Why is this prefixed with `Prisma__`?
 * Because we want to prevent naming conflicts as mentioned in
 * https://github.com/prisma/prisma-client-js/issues/707
 */
export interface Prisma__DiaPlantaoClient<T, Null = never, ExtArgs extends runtime.Types.Extensions.InternalArgs = runtime.Types.Extensions.DefaultArgs, GlobalOmitOptions = {}> extends Prisma.PrismaPromise<T> {
  readonly [Symbol.toStringTag]: "PrismaPromise"
  plantao<T extends Prisma.PlantaoDefaultArgs<ExtArgs> = {}>(args?: Prisma.Subset<T, Prisma.PlantaoDefaultArgs<ExtArgs>>): Prisma.Prisma__PlantaoClient<runtime.Types.Result.GetResult<Prisma.$PlantaoPayload<ExtArgs>, T, "findUniqueOrThrow", GlobalOmitOptions> | Null, Null, ExtArgs, GlobalOmitOptions>
  presencaDiaPlantao<T extends Prisma.DiaPlantao$presencaDiaPlantaoArgs<ExtArgs> = {}>(args?: Prisma.Subset<T, Prisma.DiaPlantao$presencaDiaPlantaoArgs<ExtArgs>>): Prisma.PrismaPromise<runtime.Types.Result.GetResult<Prisma.$PresencaDiaPlantaoPayload<ExtArgs>, T, "findMany", GlobalOmitOptions> | Null>
  /**
   * Attaches callbacks for the resolution and/or rejection of the Promise.
   * @param onfulfilled The callback to execute when the Promise is resolved.
   * @param onrejected The callback to execute when the Promise is rejected.
   * @returns A Promise for the completion of which ever callback is executed.
   */
  then<TResult1 = T, TResult2 = never>(onfulfilled?: ((value: T) => TResult1 | PromiseLike<TResult1>) | undefined | null, onrejected?: ((reason: any) => TResult2 | PromiseLike<TResult2>) | undefined | null): runtime.Types.Utils.JsPromise<TResult1 | TResult2>
  /**
   * Attaches a callback for only the rejection of the Promise.
   * @param onrejected The callback to execute when the Promise is rejected.
   * @returns A Promise for the completion of the callback.
   */
  catch<TResult = never>(onrejected?: ((reason: any) => TResult | PromiseLike<TResult>) | undefined | null): runtime.Types.Utils.JsPromise<T | TResult>
  /**
   * Attaches a callback that is invoked when the Promise is settled (fulfilled or rejected). The
   * resolved value cannot be modified from the callback.
   * @param onfinally The callback to execute when the Promise is settled (fulfilled or rejected).
   * @returns A Promise for the completion of the callback.
   */
  finally(onfinally?: (() => void) | undefined | null): runtime.Types.Utils.JsPromise<T>
}




/**
 * Fields of the DiaPlantao model
 */
export interface DiaPlantaoFieldRefs {
  readonly id: Prisma.FieldRef<"DiaPlantao", 'Int'>
  readonly plantaoId: Prisma.FieldRef<"DiaPlantao", 'Int'>
  readonly data: Prisma.FieldRef<"DiaPlantao", 'DateTime'>
  readonly horaEntrada: Prisma.FieldRef<"DiaPlantao", 'String'>
  readonly horaSaida: Prisma.FieldRef<"DiaPlantao", 'String'>
  readonly intervalo: Prisma.FieldRef<"DiaPlantao", 'String'>
  readonly observacoes: Prisma.FieldRef<"DiaPlantao", 'String'>
  readonly createdAt: Prisma.FieldRef<"DiaPlantao", 'DateTime'>
  readonly updatedAt: Prisma.FieldRef<"DiaPlantao", 'DateTime'>
}
    

// Custom InputTypes
/**
 * DiaPlantao findUnique
 */
export type DiaPlantaoFindUniqueArgs<ExtArgs extends runtime.Types.Extensions.InternalArgs = runtime.Types.Extensions.DefaultArgs> = {
  /**
   * Select specific fields to fetch from the DiaPlantao
   */
  select?: Prisma.DiaPlantaoSelect<ExtArgs> | null
  /**
   * Omit specific fields from the DiaPlantao
   */
  omit?: Prisma.DiaPlantaoOmit<ExtArgs> | null
  /**
   * Choose, which related nodes to fetch as well
   */
  include?: Prisma.DiaPlantaoInclude<ExtArgs> | null
  /**
   * Filter, which DiaPlantao to fetch.
   */
  where: Prisma.DiaPlantaoWhereUniqueInput
}

/**
 * DiaPlantao findUniqueOrThrow
 */
export type DiaPlantaoFindUniqueOrThrowArgs<ExtArgs extends runtime.Types.Extensions.InternalArgs = runtime.Types.Extensions.DefaultArgs> = {
  /**
   * Select specific fields to fetch from the DiaPlantao
   */
  select?: Prisma.DiaPlantaoSelect<ExtArgs> | null
  /**
   * Omit specific fields from the DiaPlantao
   */
  omit?: Prisma.DiaPlantaoOmit<ExtArgs> | null
  /**
   * Choose, which related nodes to fetch as well
   */
  include?: Prisma.DiaPlantaoInclude<ExtArgs> | null
  /**
   * Filter, which DiaPlantao to fetch.
   */
  where: Prisma.DiaPlantaoWhereUniqueInput
}

/**
 * DiaPlantao findFirst
 */
export type DiaPlantaoFindFirstArgs<ExtArgs extends runtime.Types.Extensions.InternalArgs = runtime.Types.Extensions.DefaultArgs> = {
  /**
   * Select specific fields to fetch from the DiaPlantao
   */
  select?: Prisma.DiaPlantaoSelect<ExtArgs> | null
  /**
   * Omit specific fields from the DiaPlantao
   */
  omit?: Prisma.DiaPlantaoOmit<ExtArgs> | null
  /**
   * Choose, which related nodes to fetch as well
   */
  include?: Prisma.DiaPlantaoInclude<ExtArgs> | null
  /**
   * Filter, which DiaPlantao to fetch.
   */
  where?: Prisma.DiaPlantaoWhereInput
  /**
   * {@link https://www.prisma.io/docs/concepts/components/prisma-client/sorting Sorting Docs}
   * 
   * Determine the order of DiaPlantaos to fetch.
   */
  orderBy?: Prisma.DiaPlantaoOrderByWithRelationInput | Prisma.DiaPlantaoOrderByWithRelationInput[]
  /**
   * {@link https://www.prisma.io/docs/concepts/components/prisma-client/pagination#cursor-based-pagination Cursor Docs}
   * 
   * Sets the position for searching for DiaPlantaos.
   */
  cursor?: Prisma.DiaPlantaoWhereUniqueInput
  /**
   * {@link https://www.prisma.io/docs/concepts/components/prisma-client/pagination Pagination Docs}
   * 
   * Take `±n` DiaPlantaos from the position of the cursor.
   */
  take?: number
  /**
   * {@link https://www.prisma.io/docs/concepts/components/prisma-client/pagination Pagination Docs}
   * 
   * Skip the first `n` DiaPlantaos.
   */
  skip?: number
  /**
   * {@link https://www.prisma.io/docs/concepts/components/prisma-client/distinct Distinct Docs}
   * 
   * Filter by unique combinations of DiaPlantaos.
   */
  distinct?: Prisma.DiaPlantaoScalarFieldEnum | Prisma.DiaPlantaoScalarFieldEnum[]
}

/**
 * DiaPlantao findFirstOrThrow
 */
export type DiaPlantaoFindFirstOrThrowArgs<ExtArgs extends runtime.Types.Extensions.InternalArgs = runtime.Types.Extensions.DefaultArgs> = {
  /**
   * Select specific fields to fetch from the DiaPlantao
   */
  select?: Prisma.DiaPlantaoSelect<ExtArgs> | null
  /**
   * Omit specific fields from the DiaPlantao
   */
  omit?: Prisma.DiaPlantaoOmit<ExtArgs> | null
  /**
   * Choose, which related nodes to fetch as well
   */
  include?: Prisma.DiaPlantaoInclude<ExtArgs> | null
  /**
   * Filter, which DiaPlantao to fetch.
   */
  where?: Prisma.DiaPlantaoWhereInput
  /**
   * {@link https://www.prisma.io/docs/concepts/components/prisma-client/sorting Sorting Docs}
   * 
   * Determine the order of DiaPlantaos to fetch.
   */
  orderBy?: Prisma.DiaPlantaoOrderByWithRelationInput | Prisma.DiaPlantaoOrderByWithRelationInput[]
  /**
   * {@link https://www.prisma.io/docs/concepts/components/prisma-client/pagination#cursor-based-pagination Cursor Docs}
   * 
   * Sets the position for searching for DiaPlantaos.
   */
  cursor?: Prisma.DiaPlantaoWhereUniqueInput
  /**
   * {@link https://www.prisma.io/docs/concepts/components/prisma-client/pagination Pagination Docs}
   * 
   * Take `±n` DiaPlantaos from the position of the cursor.
   */
  take?: number
  /**
   * {@link https://www.prisma.io/docs/concepts/components/prisma-client/pagination Pagination Docs}
   * 
   * Skip the first `n` DiaPlantaos.
   */
  skip?: number
  /**
   * {@link https://www.prisma.io/docs/concepts/components/prisma-client/distinct Distinct Docs}
   * 
   * Filter by unique combinations of DiaPlantaos.
   */
  distinct?: Prisma.DiaPlantaoScalarFieldEnum | Prisma.DiaPlantaoScalarFieldEnum[]
}

/**
 * DiaPlantao findMany
 */
export type DiaPlantaoFindManyArgs<ExtArgs extends runtime.Types.Extensions.InternalArgs = runtime.Types.Extensions.DefaultArgs> = {
  /**
   * Select specific fields to fetch from the DiaPlantao
   */
  select?: Prisma.DiaPlantaoSelect<ExtArgs> | null
  /**
   * Omit specific fields from the DiaPlantao
   */
  omit?: Prisma.DiaPlantaoOmit<ExtArgs> | null
  /**
   * Choose, which related nodes to fetch as well
   */
  include?: Prisma.DiaPlantaoInclude<ExtArgs> | null
  /**
   * Filter, which DiaPlantaos to fetch.
   */
  where?: Prisma.DiaPlantaoWhereInput
  /**
   * {@link https://www.prisma.io/docs/concepts/components/prisma-client/sorting Sorting Docs}
   * 
   * Determine the order of DiaPlantaos to fetch.
   */
  orderBy?: Prisma.DiaPlantaoOrderByWithRelationInput | Prisma.DiaPlantaoOrderByWithRelationInput[]
  /**
   * {@link https://www.prisma.io/docs/concepts/components/prisma-client/pagination#cursor-based-pagination Cursor Docs}
   * 
   * Sets the position for listing DiaPlantaos.
   */
  cursor?: Prisma.DiaPlantaoWhereUniqueInput
  /**
   * {@link https://www.prisma.io/docs/concepts/components/prisma-client/pagination Pagination Docs}
   * 
   * Take `±n` DiaPlantaos from the position of the cursor.
   */
  take?: number
  /**
   * {@link https://www.prisma.io/docs/concepts/components/prisma-client/pagination Pagination Docs}
   * 
   * Skip the first `n` DiaPlantaos.
   */
  skip?: number
  distinct?: Prisma.DiaPlantaoScalarFieldEnum | Prisma.DiaPlantaoScalarFieldEnum[]
}

/**
 * DiaPlantao create
 */
export type DiaPlantaoCreateArgs<ExtArgs extends runtime.Types.Extensions.InternalArgs = runtime.Types.Extensions.DefaultArgs> = {
  /**
   * Select specific fields to fetch from the DiaPlantao
   */
  select?: Prisma.DiaPlantaoSelect<ExtArgs> | null
  /**
   * Omit specific fields from the DiaPlantao
   */
  omit?: Prisma.DiaPlantaoOmit<ExtArgs> | null
  /**
   * Choose, which related nodes to fetch as well
   */
  include?: Prisma.DiaPlantaoInclude<ExtArgs> | null
  /**
   * The data needed to create a DiaPlantao.
   */
  data: Prisma.XOR<Prisma.DiaPlantaoCreateInput, Prisma.DiaPlantaoUncheckedCreateInput>
}

/**
 * DiaPlantao createMany
 */
export type DiaPlantaoCreateManyArgs<ExtArgs extends runtime.Types.Extensions.InternalArgs = runtime.Types.Extensions.DefaultArgs> = {
  /**
   * The data used to create many DiaPlantaos.
   */
  data: Prisma.DiaPlantaoCreateManyInput | Prisma.DiaPlantaoCreateManyInput[]
  skipDuplicates?: boolean
}

/**
 * DiaPlantao update
 */
export type DiaPlantaoUpdateArgs<ExtArgs extends runtime.Types.Extensions.InternalArgs = runtime.Types.Extensions.DefaultArgs> = {
  /**
   * Select specific fields to fetch from the DiaPlantao
   */
  select?: Prisma.DiaPlantaoSelect<ExtArgs> | null
  /**
   * Omit specific fields from the DiaPlantao
   */
  omit?: Prisma.DiaPlantaoOmit<ExtArgs> | null
  /**
   * Choose, which related nodes to fetch as well
   */
  include?: Prisma.DiaPlantaoInclude<ExtArgs> | null
  /**
   * The data needed to update a DiaPlantao.
   */
  data: Prisma.XOR<Prisma.DiaPlantaoUpdateInput, Prisma.DiaPlantaoUncheckedUpdateInput>
  /**
   * Choose, which DiaPlantao to update.
   */
  where: Prisma.DiaPlantaoWhereUniqueInput
}

/**
 * DiaPlantao updateMany
 */
export type DiaPlantaoUpdateManyArgs<ExtArgs extends runtime.Types.Extensions.InternalArgs = runtime.Types.Extensions.DefaultArgs> = {
  /**
   * The data used to update DiaPlantaos.
   */
  data: Prisma.XOR<Prisma.DiaPlantaoUpdateManyMutationInput, Prisma.DiaPlantaoUncheckedUpdateManyInput>
  /**
   * Filter which DiaPlantaos to update
   */
  where?: Prisma.DiaPlantaoWhereInput
  /**
   * Limit how many DiaPlantaos to update.
   */
  limit?: number
}

/**
 * DiaPlantao upsert
 */
export type DiaPlantaoUpsertArgs<ExtArgs extends runtime.Types.Extensions.InternalArgs = runtime.Types.Extensions.DefaultArgs> = {
  /**
   * Select specific fields to fetch from the DiaPlantao
   */
  select?: Prisma.DiaPlantaoSelect<ExtArgs> | null
  /**
   * Omit specific fields from the DiaPlantao
   */
  omit?: Prisma.DiaPlantaoOmit<ExtArgs> | null
  /**
   * Choose, which related nodes to fetch as well
   */
  include?: Prisma.DiaPlantaoInclude<ExtArgs> | null
  /**
   * The filter to search for the DiaPlantao to update in case it exists.
   */
  where: Prisma.DiaPlantaoWhereUniqueInput
  /**
   * In case the DiaPlantao found by the `where` argument doesn't exist, create a new DiaPlantao with this data.
   */
  create: Prisma.XOR<Prisma.DiaPlantaoCreateInput, Prisma.DiaPlantaoUncheckedCreateInput>
  /**
   * In case the DiaPlantao was found with the provided `where` argument, update it with this data.
   */
  update: Prisma.XOR<Prisma.DiaPlantaoUpdateInput, Prisma.DiaPlantaoUncheckedUpdateInput>
}

/**
 * DiaPlantao delete
 */
export type DiaPlantaoDeleteArgs<ExtArgs extends runtime.Types.Extensions.InternalArgs = runtime.Types.Extensions.DefaultArgs> = {
  /**
   * Select specific fields to fetch from the DiaPlantao
   */
  select?: Prisma.DiaPlantaoSelect<ExtArgs> | null
  /**
   * Omit specific fields from the DiaPlantao
   */
  omit?: Prisma.DiaPlantaoOmit<ExtArgs> | null
  /**
   * Choose, which related nodes to fetch as well
   */
  include?: Prisma.DiaPlantaoInclude<ExtArgs> | null
  /**
   * Filter which DiaPlantao to delete.
   */
  where: Prisma.DiaPlantaoWhereUniqueInput
}

/**
 * DiaPlantao deleteMany
 */
export type DiaPlantaoDeleteManyArgs<ExtArgs extends runtime.Types.Extensions.InternalArgs = runtime.Types.Extensions.DefaultArgs> = {
  /**
   * Filter which DiaPlantaos to delete
   */
  where?: Prisma.DiaPlantaoWhereInput
  /**
   * Limit how many DiaPlantaos to delete.
   */
  limit?: number
}

/**
 * DiaPlantao.presencaDiaPlantao
 */
export type DiaPlantao$presencaDiaPlantaoArgs<ExtArgs extends runtime.Types.Extensions.InternalArgs = runtime.Types.Extensions.DefaultArgs> = {
  /**
   * Select specific fields to fetch from the PresencaDiaPlantao
   */
  select?: Prisma.PresencaDiaPlantaoSelect<ExtArgs> | null
  /**
   * Omit specific fields from the PresencaDiaPlantao
   */
  omit?: Prisma.PresencaDiaPlantaoOmit<ExtArgs> | null
  /**
   * Choose, which related nodes to fetch as well
   */
  include?: Prisma.PresencaDiaPlantaoInclude<ExtArgs> | null
  where?: Prisma.PresencaDiaPlantaoWhereInput
  orderBy?: Prisma.PresencaDiaPlantaoOrderByWithRelationInput | Prisma.PresencaDiaPlantaoOrderByWithRelationInput[]
  cursor?: Prisma.PresencaDiaPlantaoWhereUniqueInput
  take?: number
  skip?: number
  distinct?: Prisma.PresencaDiaPlantaoScalarFieldEnum | Prisma.PresencaDiaPlantaoScalarFieldEnum[]
}

/**
 * DiaPlantao without action
 */
export type DiaPlantaoDefaultArgs<ExtArgs extends runtime.Types.Extensions.InternalArgs = runtime.Types.Extensions.DefaultArgs> = {
  /**
   * Select specific fields to fetch from the DiaPlantao
   */
  select?: Prisma.DiaPlantaoSelect<ExtArgs> | null
  /**
   * Omit specific fields from the DiaPlantao
   */
  omit?: Prisma.DiaPlantaoOmit<ExtArgs> | null
  /**
   * Choose, which related nodes to fetch as well
   */
  include?: Prisma.DiaPlantaoInclude<ExtArgs> | null
}
