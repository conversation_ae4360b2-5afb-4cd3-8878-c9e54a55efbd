import { z } from "zod";
import jwt from "jsonwebtoken";
import { prisma } from "../lib/prisma";
import { appEnv } from "@/lib/env";
import type { FastifyTypedInstance } from "@/types";
import { validatePass, generatePass } from "@/lib/auth";

const loginSchema = z.object({
  usuario: z.string(),
  password: z.string().min(6),
});

const resetPasswordSchema = z.object({
  currentPassword: z.string().min(6),
  newPassword: z.string().min(6),
});

export async function authRouter(app: FastifyTypedInstance) {
  app.get("/health", async () => {
    return { status: "ok", timestamp: new Date().toISOString() };
  });

  // Login
  app.post("/auth/login", async (request, reply) => {
    try {
      const { usuario: userInput, password } = loginSchema.parse(request.body);

      // Buscar usuário pelo email ou CPF
      const usuario = await prisma.usuario.findFirst({
        where: {
          OR: [{ email: userInput }, { cpf: userInput }],
        },
        include: {
          roles: { include: { role: true } },
          profissional: true,
          clientes: {
            select: {
              cliente: {
                select: {
                  id: true,
                  uuid: true,
                  nome: true,
                  fusoHorario: true,
                },
              },
            },
          },
        },
      });

      if (!usuario) {
        return reply.status(401).send({ error: "Credenciais inválidas" });
      }

      // Verificar se o usuário está ativo
      if (!usuario.ativo) {
        return reply.status(403).send({ error: "Usuário inativo" });
      }

      // Verificar senha
      const senhaValida = await validatePass(password, usuario.senha);
      if (!senhaValida) {
        return reply.status(401).send({ error: "Credenciais inválidas" });
      }

      // Verificar aceite dos termos LGPD
      const termoAtual = await prisma.gerenciamentoTermosLgpd.findFirst({
        where: {
          ativo: true,
          deletedAt: null,
          validoDe: { lte: new Date() },
          OR: [{ validoAte: null }, { validoAte: { gte: new Date() } }],
        },
        orderBy: {
          versao: "desc",
        },
      });

      let termosAceitos = true;
      if (termoAtual) {
        const aceiteExistente = await prisma.aceiteTermosLgpd.findFirst({
          where: {
            usuarioId: usuario.id,
            termoVersaoId: termoAtual.id,
            consentimentoLgpd: true,
          },
        });
        termosAceitos = !!aceiteExistente;
      }

      // Atualizar último acesso
      await prisma.usuario.update({
        where: { id: usuario.id },
        data: { ultimoAcesso: new Date() },
      });

      const perfis = usuario.roles.map((ur) => ({
        id: ur.role.id,
        nome: ur.role.nome,
        descricao: ur.role.descricao,
      }));

      const clientes = usuario.clientes.map((uc) => ({
        id: uc.cliente.uuid, //mandando uuid no id de propósito
        nome: uc.cliente.nome,
        fusoHorario: uc.cliente.fusoHorario,
      }));

      // Gerar token JWT
      const token = jwt.sign(
        {
          id: usuario.uuid,
          profissionalId: usuario.profissional?.id || null,
          nome: usuario.nome,
          roles: usuario.roles.map((ur) => ur.role.nome),
          perfis,
          clientes,
        },
        appEnv.JWT_SECRET,
        { expiresIn: appEnv.JWT_EXPIRES_IN }
      );

      // Preparar dados do usuário para resposta
      const { senha, ...usuarioSemSenha } = usuario;

      const userData = {
        ...usuarioSemSenha,
        roles: usuario.roles.map((ur) => ur.role.nome),
        profissionalId: usuario.profissional?.id || null,
        termosAceitos, // Incluir status de aceite dos termos
        deveResetarSenha: usuario.deveResetarSenha, // Incluir flag de reset de senha,
        perfis,
        clientes,
      };

      return reply.send({
        token,
        usuario: userData,
      });
    } catch (error) {
      console.error("Erro no login:", error);
      return reply.status(500).send({ error: "Erro ao realizar login" });
    }
  });

  // Logout (apenas para invalidar no cliente, já que JWT é stateless)
  app.post("/auth/logout", async (request, reply) => {
    // No backend, não precisamos fazer nada com JWT stateless
    // O cliente deve remover o token
    return reply.send({ message: "Logout realizado com sucesso" });
  });

  // Reset de senha obrigatório (quando deveResetarSenha = true)
  app.post("/auth/reset-password", async (request, reply) => {
    try {
      const { currentPassword, newPassword } = resetPasswordSchema.parse(request.body);

      // Extrair token do header
      const authorization = request.headers.authorization;
      if (!authorization) {
        return reply.status(401).send({ error: "Token não fornecido" });
      }

      const token = authorization.replace("Bearer ", "");
      let decoded: any;

      try {
        decoded = jwt.verify(token, appEnv.JWT_SECRET);
      } catch (error) {
        return reply.status(401).send({ error: "Token inválido" });
      }

      // Buscar usuário
      const usuario = await prisma.usuario.findUnique({
        where: { uuid: decoded.id },
      });

      if (!usuario) {
        return reply.status(404).send({ error: "Usuário não encontrado" });
      }

      // Verificar senha atual
      const senhaValida = await validatePass(currentPassword, usuario.senha);
      if (!senhaValida) {
        return reply.status(401).send({ error: "Senha atual incorreta" });
      }

      // Atualizar senha
      const hashedPassword = await generatePass(newPassword);
      await prisma.usuario.update({
        where: { uuid: decoded.id },
        data: {
          senha: hashedPassword,
          deveResetarSenha: false, // Remover flag de reset obrigatório
        },
      });

      return reply.send({ message: "Senha atualizada com sucesso" });
    } catch (error) {
      console.error("Erro ao resetar senha:", error);
      return reply.status(500).send({ error: "Erro ao resetar senha" });
    }
  });
}
