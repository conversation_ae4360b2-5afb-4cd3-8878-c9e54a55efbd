import { useEffect } from "react";
import { useNavigate } from "@tanstack/react-router";
import { useQuery, useQueryClient } from "@tanstack/react-query";
import { useForm } from "@tanstack/react-form";
import { api, type Cliente, type LocalAtendimento } from "@/lib/api";
import {
  useCreateLocalAtendimento,
  useUpdateLocalAtendimento,
  useLocaisAtendimento,
} from "@/hooks/use-locais-atendimento";
import { Button } from "@/components/ui/button";
import { Input } from "@/components/ui/input";
import { PhoneInput } from "@/components/ui/phone-input";
import { Label } from "@/components/ui/label";
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from "@/components/ui/card";
import { Switch } from "@/components/ui/switch";
import { ArrowLeft, Save, Loader2, MapPin } from "lucide-react";
import { toast } from "sonner";
import { Select, SelectContent, SelectItem, SelectTrigger, SelectValue } from "@/components/ui/select";
import { formatCEP } from "@/lib/formatters";
import { buscarCEP } from "@/lib/address-service";
import { ESTADOS_BRASILEIROS } from "@/lib/constants";
import { EspecialidadeSelect } from "@/components/especialidade-select";

interface LocalForm {
  nome: string;
  endereco: string;
  cidade?: string;
  estado?: string;
  cep?: string;
  telefone?: string;
  responsavel?: string;
  observacoes?: string;
  latitude?: number;
  longitude?: number;
  ativo: boolean;
  especialidadeIds: number[];
}

interface LocalAtendimentoFormProps {
  clienteId: string;
  localId?: string;
}

export function LocalAtendimentoForm({ clienteId, localId }: LocalAtendimentoFormProps) {
  const navigate = useNavigate();
  const queryClient = useQueryClient();
  const isEditing = !!localId;

  const createLocalAtendimento = useCreateLocalAtendimento();
  const updateLocalAtendimento = useUpdateLocalAtendimento();

  // Buscar dados do cliente para mostrar no header
  const { data: cliente } = useQuery({
    queryKey: ["cliente", clienteId],
    queryFn: () => api.get<Cliente>(`/clientes/${clienteId}`),
  });

  // Buscar dados do local se estiver editando
  const { data: local, isLoading: isLoadingLocal } = useQuery({
    queryKey: ["local", localId],
    queryFn: () => api.get<LocalAtendimento>(`/locais-atendimento/${localId}`),
    enabled: isEditing,
  });

  // Buscar locais existentes do cliente para validação de duplicados (somente ao criar)
  const { data: locaisExistentes } = useLocaisAtendimento({
    clienteUuid: clienteId, // Using clienteUuid since clienteId is actually a UUID
    limit: 100, // Buscar todos os locais do cliente
  });

  const handleSubmit = async (data: LocalForm) => {
    // Validar duplicados apenas ao criar novo local
    if (!isEditing && locaisExistentes?.data) {
      const nomeNormalizado = data.nome.trim().toLowerCase();
      const localComMesmoNome = locaisExistentes.data.find((local) => local.nome.toLowerCase() === nomeNormalizado);

      if (localComMesmoNome) {
        toast.error(`Já existe um local cadastrado com o nome "${data.nome}" para este cliente`);
        return;
      }

      // Verificar endereço duplicado
      if (data.endereco && data.cidade) {
        const enderecoNormalizado = data.endereco.trim().toLowerCase();
        const cidadeNormalizada = data.cidade.trim().toLowerCase();

        const localComMesmoEndereco = locaisExistentes.data.find(
          (local) =>
            local.endereco?.toLowerCase() === enderecoNormalizado && local.cidade?.toLowerCase() === cidadeNormalizada
        );

        if (localComMesmoEndereco) {
          toast.error(`Já existe um local cadastrado neste endereço: ${localComMesmoEndereco.nome}`);
          return;
        }
      }
    }

    const formattedData: any = {
      ...data,
      latitude: data.latitude ? Number(data.latitude) : undefined,
      longitude: data.longitude ? Number(data.longitude) : undefined,
      clienteId,
      especialidadeIds: data.especialidadeIds || [],
    };

    if (isEditing && localId) {
      updateLocalAtendimento.mutate(
        { id: localId, ...formattedData },
        {
          onSuccess: () => {
            navigate({ to: `/cadastros/clientes/${clienteId}`, search: { tab: "locais" } });
          },
        }
      );
    } else {
      createLocalAtendimento.mutate(formattedData, {
        onSuccess: () => {
          navigate({ to: `/cadastros/clientes/${clienteId}`, search: { tab: "locais" } });
        },
      });
    }
  };

  const form = useForm({
    defaultValues: {
      nome: "",
      endereco: "",
      cidade: "",
      estado: "",
      cep: "",
      telefone: "",
      responsavel: "",
      observacoes: "",
      latitude: undefined,
      longitude: undefined,
      ativo: true,
      especialidadeIds: [],
    } as LocalForm,
    onSubmit: async ({ value }) => {
      await handleSubmit(value);
    },
  });

  // Preencher formulário quando carregar dados do local (modo edição)
  useEffect(() => {
    if (local) {
      form.setFieldValue("nome", local.nome || "");
      form.setFieldValue("endereco", local.endereco || "");
      form.setFieldValue("cidade", local.cidade || "");
      form.setFieldValue("estado", local.estado || "");
      form.setFieldValue("cep", local.cep || "");
      form.setFieldValue("telefone", local.telefone || "");
      form.setFieldValue("responsavel", local.responsavel || "");
      form.setFieldValue("observacoes", local.observacoes || "");
      form.setFieldValue("latitude", local.latitude || undefined);
      form.setFieldValue("longitude", local.longitude || undefined);
      form.setFieldValue("ativo", local.ativo ?? true);
      // Mapear as especialidades para array de IDs
      form.setFieldValue("especialidadeIds", local.especialidades?.map((e: any) => e.especialidade.id) || []);
    }
  }, [local, form]);

  const handleCepSearch = async (cep: string) => {
    const data = await buscarCEP(cep);
    if (data) {
      // Atualiza o endereço com os dados do CEP
      const enderecoCompleto = `${data.logradouro}, ${data.bairro}`;
      form.setFieldValue("endereco", enderecoCompleto);
      form.setFieldValue("cidade", data.localidade);
      form.setFieldValue("estado", data.uf);
    }
  };

  if (isLoadingLocal) {
    return (
      <div className="flex items-center justify-center min-h-[400px]">
        <div className="text-muted-foreground">Carregando...</div>
      </div>
    );
  }

  const isSubmitting = createLocalAtendimento.isPending || updateLocalAtendimento.isPending;

  return (
    <div className="space-y-6">
      <div className="flex items-center gap-4">
        <Button
          variant="ghost"
          size="icon"
          onClick={() => navigate({ to: `/cadastros/clientes/${clienteId}`, search: { tab: "locais" } })}
        >
          <ArrowLeft className="h-4 w-4" />
        </Button>
        <div>
          <h1 className="text-2xl font-bold">{isEditing ? "Editar" : "Novo"} Local de Atendimento</h1>
          {cliente && <p className="text-muted-foreground">Cliente: {cliente.nome}</p>}
        </div>
      </div>

      <Card>
        <CardHeader>
          <CardTitle>
            <MapPin className="h-5 w-5 inline-block mr-2" />
            Informações do Local
          </CardTitle>
          <CardDescription>
            {isEditing ? "Atualize os dados do local de atendimento" : "Preencha os dados do novo local de atendimento"}
          </CardDescription>
        </CardHeader>
        <CardContent>
          <form
            className="space-y-6"
            onSubmit={(e) => {
              e.preventDefault();
              form.handleSubmit();
            }}
          >
            <div className="grid gap-4 md:grid-cols-2">
              <form.Field name="nome">
                {(field) => (
                  <div className="space-y-2">
                    <Label htmlFor="nome">Nome do Local *</Label>
                    <Input
                      id="nome"
                      value={field.state.value}
                      onChange={(e) => field.handleChange(e.target.value)}
                      placeholder="Ex: Hospital Central"
                    />
                    {field.state.meta.errors && (
                      <p className="text-sm text-destructive">{field.state.meta.errors.join(", ")}</p>
                    )}
                  </div>
                )}
              </form.Field>

              <form.Field name="cep">
                {(field) => (
                  <div className="space-y-2">
                    <Label htmlFor="cep">CEP</Label>
                    <Input
                      id="cep"
                      value={field.state.value}
                      onBlur={(e) => {
                        handleCepSearch(e.target.value);
                      }}
                      onChange={(e) => {
                        const formatted = formatCEP(e.target.value);
                        field.handleChange(formatted);
                      }}
                      placeholder="00000-000"
                      maxLength={9}
                    />
                  </div>
                )}
              </form.Field>

              <form.Field name="endereco">
                {(field) => (
                  <div className="space-y-2">
                    <Label htmlFor="endereco">Endereço *</Label>
                    <Input
                      id="endereco"
                      value={field.state.value}
                      onChange={(e) => field.handleChange(e.target.value)}
                      placeholder="Rua, número, bairro"
                    />
                    {field.state.meta.errors && (
                      <p className="text-sm text-destructive">{field.state.meta.errors.join(", ")}</p>
                    )}
                  </div>
                )}
              </form.Field>

              <form.Field name="cidade">
                {(field) => (
                  <div className="space-y-2">
                    <Label htmlFor="cidade">Cidade</Label>
                    <Input
                      id="cidade"
                      value={field.state.value}
                      onChange={(e) => field.handleChange(e.target.value)}
                      placeholder="Nome da cidade"
                    />
                  </div>
                )}
              </form.Field>

              <form.Field name="estado">
                {(field) => (
                  <div className="space-y-2">
                    <Label htmlFor="estado">Estado</Label>
                    <Select value={field.state.value} onValueChange={(value) => field.handleChange(value)}>
                      <SelectTrigger id="estado">
                        <SelectValue placeholder="Selecione o estado" />
                      </SelectTrigger>
                      <SelectContent>
                        {ESTADOS_BRASILEIROS.map((estado) => (
                          <SelectItem key={estado.value} value={estado.value}>
                            {estado.value}
                          </SelectItem>
                        ))}
                      </SelectContent>
                    </Select>
                  </div>
                )}
              </form.Field>

              <form.Field name="telefone">
                {(field) => (
                  <PhoneInput
                    id="telefone"
                    label="Telefone"
                    value={field.state.value || ""}
                    onChange={field.handleChange}
                  />
                )}
              </form.Field>

              <form.Field name="responsavel">
                {(field) => (
                  <div className="space-y-2">
                    <Label htmlFor="responsavel">Responsável</Label>
                    <Input
                      id="responsavel"
                      value={field.state.value}
                      onChange={(e) => field.handleChange(e.target.value)}
                      placeholder="Nome do responsável"
                    />
                  </div>
                )}
              </form.Field>

              <div className="md:col-span-2">
                <form.Field name="especialidadeIds">
                  {(field) => (
                    <div className="space-y-2">
                      <Label htmlFor="especialidades">Especialidades Permitidas</Label>
                      <EspecialidadeSelect
                        value={field.state.value}
                        onChange={field.handleChange}
                        placeholder="Selecione as especialidades permitidas neste local"
                        searchPlaceholder="Buscar especialidade..."
                        emptyText="Nenhuma especialidade encontrada"
                        addNewText="Adicionar nova especialidade"
                      />
                      <p className="text-sm text-muted-foreground">
                        Apenas plantões com essas especialidades poderão ser criados neste local
                      </p>
                    </div>
                  )}
                </form.Field>
              </div>

              {/* <form.Field name="ativo">
                {(field) => (
                  <div className="flex items-center space-x-2">
                    <Switch id="ativo" checked={field.state.value} onCheckedChange={field.handleChange} />
                    <Label htmlFor="ativo">Local ativo</Label>
                  </div>
                )}
              </form.Field> */}

              <div className="space-y-2">
                <Label>Coordenadas (Opcional)</Label>
                <div className="grid grid-cols-2 gap-2">
                  <form.Field name="latitude">
                    {(field) => (
                      <Input
                        type="number"
                        step="any"
                        value={field.state.value || ""}
                        onChange={(e) => field.handleChange(e.target.value ? Number(e.target.value) : undefined)}
                        placeholder="Latitude"
                      />
                    )}
                  </form.Field>
                  <form.Field name="longitude">
                    {(field) => (
                      <Input
                        type="number"
                        step="any"
                        value={field.state.value || ""}
                        onChange={(e) => field.handleChange(e.target.value ? Number(e.target.value) : undefined)}
                        placeholder="Longitude"
                      />
                    )}
                  </form.Field>
                </div>
              </div>
            </div>

            <form.Field name="observacoes">
              {(field) => (
                <div className="space-y-2">
                  <Label htmlFor="observacoes">Observações</Label>
                  <textarea
                    id="observacoes"
                    value={field.state.value}
                    onChange={(e) => field.handleChange(e.target.value)}
                    className="min-h-[100px] w-full rounded-md border border-input bg-background px-3 py-2 text-sm ring-offset-background placeholder:text-muted-foreground focus-visible:outline-none focus-visible:ring-2 focus-visible:ring-ring focus-visible:ring-offset-2 disabled:cursor-not-allowed disabled:opacity-50"
                    placeholder="Informações adicionais sobre o local"
                  />
                </div>
              )}
            </form.Field>

            <div className="flex justify-end gap-4">
              <Button
                type="button"
                variant="outline"
                onClick={() => navigate({ to: `/cadastros/clientes/${clienteId}`, search: { tab: "locais" } })}
              >
                Cancelar
              </Button>
              <form.Subscribe
                selector={(state) => ({
                  canSubmit: state.canSubmit,
                  isSubmitting: state.isSubmitting,
                })}
              >
                {({ canSubmit }) => (
                  <Button type="submit" disabled={!canSubmit || isSubmitting}>
                    {isSubmitting ? (
                      <>
                        <Loader2 className="mr-2 h-4 w-4 animate-spin" />
                        Salvando...
                      </>
                    ) : (
                      <>
                        <Save className="mr-2 h-4 w-4" />
                        {isEditing ? "Salvar Alterações" : "Salvar Local"}
                      </>
                    )}
                  </Button>
                )}
              </form.Subscribe>
            </div>
          </form>
        </CardContent>
      </Card>
    </div>
  );
}
