/* eslint-disable */

// @ts-nocheck

// noinspection JSUnusedGlobalSymbols

// This file was automatically generated by TanStack Router.
// You should NOT make any changes in this file as it will be overwritten.
// Additionally, you should also exclude this file from your linter and/or formatter to prevent it from being checked or modified.

import { Route as rootRouteImport } from "./routes/__root"
import { Route as TermosUsoRouteImport } from "./routes/termos-uso"
import { Route as TermosRouteImport } from "./routes/termos"
import { Route as OnboardingPendenteRouteImport } from "./routes/onboarding-pendente"
import { Route as LoginRouteImport } from "./routes/login"
import { Route as SplatRouteImport } from "./routes/$"
import { Route as IndexRouteImport } from "./routes/index"
import { Route as PlantoesIndexRouteImport } from "./routes/plantoes/index"
import { Route as PerfilIndexRouteImport } from "./routes/perfil/index"
import { Route as FechamentosIndexRouteImport } from "./routes/fechamentos/index"
import { Route as AuditoriaIndexRouteImport } from "./routes/auditoria/index"
import { Route as AntecipacoesIndexRouteImport } from "./routes/antecipacoes/index"
import { Route as ProfissionalPlantoesRouteImport } from "./routes/profissional/plantoes"
import { Route as ProfissionalFechamentosRouteImport } from "./routes/profissional/fechamentos"
import { Route as ProfissionalDashboardRouteImport } from "./routes/profissional/dashboard"
import { Route as ProfissionalAntecipacoesRouteImport } from "./routes/profissional/antecipacoes"
import { Route as PlantoesNovoRouteImport } from "./routes/plantoes/novo"
import { Route as FechamentosNovoRouteImport } from "./routes/fechamentos/novo"
import { Route as FechamentosUuidRouteImport } from "./routes/fechamentos/$uuid"
import { Route as AntecipacoesNovaRouteImport } from "./routes/antecipacoes/nova"
import { Route as AntecipacoesUuidRouteImport } from "./routes/antecipacoes/$uuid"
import { Route as PlantoesUuidIndexRouteImport } from "./routes/plantoes/$uuid/index"
import { Route as CadastrosUsuariosIndexRouteImport } from "./routes/cadastros/usuarios/index"
import { Route as CadastrosProfissionaisIndexRouteImport } from "./routes/cadastros/profissionais/index"
import { Route as CadastrosLocaisIndexRouteImport } from "./routes/cadastros/locais/index"
import { Route as CadastrosClientesIndexRouteImport } from "./routes/cadastros/clientes/index"
import { Route as PlantoesUuidEditarRouteImport } from "./routes/plantoes/$uuid/editar"
import { Route as CadastrosUsuariosNovoRouteImport } from "./routes/cadastros/usuarios/novo"
import { Route as CadastrosUsuariosIdRouteImport } from "./routes/cadastros/usuarios/$id"
import { Route as CadastrosProfissionaisNovoRouteImport } from "./routes/cadastros/profissionais/novo"
import { Route as CadastrosProfissionaisUuidRouteImport } from "./routes/cadastros/profissionais/$uuid"
import { Route as CadastrosClientesNovoRouteImport } from "./routes/cadastros/clientes/novo"
import { Route as CadastrosClientesClienteIdRouteImport } from "./routes/cadastros/clientes/$clienteId"
import { Route as AntecipacoesTermoTokenRouteImport } from "./routes/antecipacoes/termo.$token"
import { Route as AntecipacoesUuidTermoRouteImport } from "./routes/antecipacoes/$uuid/termo"
import { Route as ProfissionalPlantoesPlantaoIdRegistrosRouteImport } from "./routes/profissional/plantoes/$plantaoId.registros"
import { Route as CadastrosClientesClienteIdLocaisNovoRouteImport } from "./routes/cadastros/clientes/$clienteId/locais/novo"
import { Route as CadastrosClientesClienteIdLocaisLocalIdEditarRouteImport } from "./routes/cadastros/clientes/$clienteId/locais/$localId/editar"

const TermosUsoRoute = TermosUsoRouteImport.update({
  id: "/termos-uso",
  path: "/termos-uso",
  getParentRoute: () => rootRouteImport,
} as any)
const TermosRoute = TermosRouteImport.update({
  id: "/termos",
  path: "/termos",
  getParentRoute: () => rootRouteImport,
} as any)
const OnboardingPendenteRoute = OnboardingPendenteRouteImport.update({
  id: "/onboarding-pendente",
  path: "/onboarding-pendente",
  getParentRoute: () => rootRouteImport,
} as any)
const LoginRoute = LoginRouteImport.update({
  id: "/login",
  path: "/login",
  getParentRoute: () => rootRouteImport,
} as any)
const SplatRoute = SplatRouteImport.update({
  id: "/$",
  path: "/$",
  getParentRoute: () => rootRouteImport,
} as any)
const IndexRoute = IndexRouteImport.update({
  id: "/",
  path: "/",
  getParentRoute: () => rootRouteImport,
} as any)
const PlantoesIndexRoute = PlantoesIndexRouteImport.update({
  id: "/plantoes/",
  path: "/plantoes/",
  getParentRoute: () => rootRouteImport,
} as any)
const PerfilIndexRoute = PerfilIndexRouteImport.update({
  id: "/perfil/",
  path: "/perfil/",
  getParentRoute: () => rootRouteImport,
} as any)
const FechamentosIndexRoute = FechamentosIndexRouteImport.update({
  id: "/fechamentos/",
  path: "/fechamentos/",
  getParentRoute: () => rootRouteImport,
} as any)
const AuditoriaIndexRoute = AuditoriaIndexRouteImport.update({
  id: "/auditoria/",
  path: "/auditoria/",
  getParentRoute: () => rootRouteImport,
} as any)
const AntecipacoesIndexRoute = AntecipacoesIndexRouteImport.update({
  id: "/antecipacoes/",
  path: "/antecipacoes/",
  getParentRoute: () => rootRouteImport,
} as any)
const ProfissionalPlantoesRoute = ProfissionalPlantoesRouteImport.update({
  id: "/profissional/plantoes",
  path: "/profissional/plantoes",
  getParentRoute: () => rootRouteImport,
} as any)
const ProfissionalFechamentosRoute = ProfissionalFechamentosRouteImport.update({
  id: "/profissional/fechamentos",
  path: "/profissional/fechamentos",
  getParentRoute: () => rootRouteImport,
} as any)
const ProfissionalDashboardRoute = ProfissionalDashboardRouteImport.update({
  id: "/profissional/dashboard",
  path: "/profissional/dashboard",
  getParentRoute: () => rootRouteImport,
} as any)
const ProfissionalAntecipacoesRoute =
  ProfissionalAntecipacoesRouteImport.update({
    id: "/profissional/antecipacoes",
    path: "/profissional/antecipacoes",
    getParentRoute: () => rootRouteImport,
  } as any)
const PlantoesNovoRoute = PlantoesNovoRouteImport.update({
  id: "/plantoes/novo",
  path: "/plantoes/novo",
  getParentRoute: () => rootRouteImport,
} as any)
const FechamentosNovoRoute = FechamentosNovoRouteImport.update({
  id: "/fechamentos/novo",
  path: "/fechamentos/novo",
  getParentRoute: () => rootRouteImport,
} as any)
const FechamentosUuidRoute = FechamentosUuidRouteImport.update({
  id: "/fechamentos/$uuid",
  path: "/fechamentos/$uuid",
  getParentRoute: () => rootRouteImport,
} as any)
const AntecipacoesNovaRoute = AntecipacoesNovaRouteImport.update({
  id: "/antecipacoes/nova",
  path: "/antecipacoes/nova",
  getParentRoute: () => rootRouteImport,
} as any)
const AntecipacoesUuidRoute = AntecipacoesUuidRouteImport.update({
  id: "/antecipacoes/$uuid",
  path: "/antecipacoes/$uuid",
  getParentRoute: () => rootRouteImport,
} as any)
const PlantoesUuidIndexRoute = PlantoesUuidIndexRouteImport.update({
  id: "/plantoes/$uuid/",
  path: "/plantoes/$uuid/",
  getParentRoute: () => rootRouteImport,
} as any)
const CadastrosUsuariosIndexRoute = CadastrosUsuariosIndexRouteImport.update({
  id: "/cadastros/usuarios/",
  path: "/cadastros/usuarios/",
  getParentRoute: () => rootRouteImport,
} as any)
const CadastrosProfissionaisIndexRoute =
  CadastrosProfissionaisIndexRouteImport.update({
    id: "/cadastros/profissionais/",
    path: "/cadastros/profissionais/",
    getParentRoute: () => rootRouteImport,
  } as any)
const CadastrosLocaisIndexRoute = CadastrosLocaisIndexRouteImport.update({
  id: "/cadastros/locais/",
  path: "/cadastros/locais/",
  getParentRoute: () => rootRouteImport,
} as any)
const CadastrosClientesIndexRoute = CadastrosClientesIndexRouteImport.update({
  id: "/cadastros/clientes/",
  path: "/cadastros/clientes/",
  getParentRoute: () => rootRouteImport,
} as any)
const PlantoesUuidEditarRoute = PlantoesUuidEditarRouteImport.update({
  id: "/plantoes/$uuid/editar",
  path: "/plantoes/$uuid/editar",
  getParentRoute: () => rootRouteImport,
} as any)
const CadastrosUsuariosNovoRoute = CadastrosUsuariosNovoRouteImport.update({
  id: "/cadastros/usuarios/novo",
  path: "/cadastros/usuarios/novo",
  getParentRoute: () => rootRouteImport,
} as any)
const CadastrosUsuariosIdRoute = CadastrosUsuariosIdRouteImport.update({
  id: "/cadastros/usuarios/$id",
  path: "/cadastros/usuarios/$id",
  getParentRoute: () => rootRouteImport,
} as any)
const CadastrosProfissionaisNovoRoute =
  CadastrosProfissionaisNovoRouteImport.update({
    id: "/cadastros/profissionais/novo",
    path: "/cadastros/profissionais/novo",
    getParentRoute: () => rootRouteImport,
  } as any)
const CadastrosProfissionaisUuidRoute =
  CadastrosProfissionaisUuidRouteImport.update({
    id: "/cadastros/profissionais/$uuid",
    path: "/cadastros/profissionais/$uuid",
    getParentRoute: () => rootRouteImport,
  } as any)
const CadastrosClientesNovoRoute = CadastrosClientesNovoRouteImport.update({
  id: "/cadastros/clientes/novo",
  path: "/cadastros/clientes/novo",
  getParentRoute: () => rootRouteImport,
} as any)
const CadastrosClientesClienteIdRoute =
  CadastrosClientesClienteIdRouteImport.update({
    id: "/cadastros/clientes/$clienteId",
    path: "/cadastros/clientes/$clienteId",
    getParentRoute: () => rootRouteImport,
  } as any)
const AntecipacoesTermoTokenRoute = AntecipacoesTermoTokenRouteImport.update({
  id: "/antecipacoes/termo/$token",
  path: "/antecipacoes/termo/$token",
  getParentRoute: () => rootRouteImport,
} as any)
const AntecipacoesUuidTermoRoute = AntecipacoesUuidTermoRouteImport.update({
  id: "/termo",
  path: "/termo",
  getParentRoute: () => AntecipacoesUuidRoute,
} as any)
const ProfissionalPlantoesPlantaoIdRegistrosRoute =
  ProfissionalPlantoesPlantaoIdRegistrosRouteImport.update({
    id: "/$plantaoId/registros",
    path: "/$plantaoId/registros",
    getParentRoute: () => ProfissionalPlantoesRoute,
  } as any)
const CadastrosClientesClienteIdLocaisNovoRoute =
  CadastrosClientesClienteIdLocaisNovoRouteImport.update({
    id: "/locais/novo",
    path: "/locais/novo",
    getParentRoute: () => CadastrosClientesClienteIdRoute,
  } as any)
const CadastrosClientesClienteIdLocaisLocalIdEditarRoute =
  CadastrosClientesClienteIdLocaisLocalIdEditarRouteImport.update({
    id: "/locais/$localId/editar",
    path: "/locais/$localId/editar",
    getParentRoute: () => CadastrosClientesClienteIdRoute,
  } as any)

export interface FileRoutesByFullPath {
  "/": typeof IndexRoute
  "/$": typeof SplatRoute
  "/login": typeof LoginRoute
  "/onboarding-pendente": typeof OnboardingPendenteRoute
  "/termos": typeof TermosRoute
  "/termos-uso": typeof TermosUsoRoute
  "/antecipacoes/$uuid": typeof AntecipacoesUuidRouteWithChildren
  "/antecipacoes/nova": typeof AntecipacoesNovaRoute
  "/fechamentos/$uuid": typeof FechamentosUuidRoute
  "/fechamentos/novo": typeof FechamentosNovoRoute
  "/plantoes/novo": typeof PlantoesNovoRoute
  "/profissional/antecipacoes": typeof ProfissionalAntecipacoesRoute
  "/profissional/dashboard": typeof ProfissionalDashboardRoute
  "/profissional/fechamentos": typeof ProfissionalFechamentosRoute
  "/profissional/plantoes": typeof ProfissionalPlantoesRouteWithChildren
  "/antecipacoes": typeof AntecipacoesIndexRoute
  "/auditoria": typeof AuditoriaIndexRoute
  "/fechamentos": typeof FechamentosIndexRoute
  "/perfil": typeof PerfilIndexRoute
  "/plantoes": typeof PlantoesIndexRoute
  "/antecipacoes/$uuid/termo": typeof AntecipacoesUuidTermoRoute
  "/antecipacoes/termo/$token": typeof AntecipacoesTermoTokenRoute
  "/cadastros/clientes/$clienteId": typeof CadastrosClientesClienteIdRouteWithChildren
  "/cadastros/clientes/novo": typeof CadastrosClientesNovoRoute
  "/cadastros/profissionais/$uuid": typeof CadastrosProfissionaisUuidRoute
  "/cadastros/profissionais/novo": typeof CadastrosProfissionaisNovoRoute
  "/cadastros/usuarios/$id": typeof CadastrosUsuariosIdRoute
  "/cadastros/usuarios/novo": typeof CadastrosUsuariosNovoRoute
  "/plantoes/$uuid/editar": typeof PlantoesUuidEditarRoute
  "/cadastros/clientes": typeof CadastrosClientesIndexRoute
  "/cadastros/locais": typeof CadastrosLocaisIndexRoute
  "/cadastros/profissionais": typeof CadastrosProfissionaisIndexRoute
  "/cadastros/usuarios": typeof CadastrosUsuariosIndexRoute
  "/plantoes/$uuid": typeof PlantoesUuidIndexRoute
  "/profissional/plantoes/$plantaoId/registros": typeof ProfissionalPlantoesPlantaoIdRegistrosRoute
  "/cadastros/clientes/$clienteId/locais/novo": typeof CadastrosClientesClienteIdLocaisNovoRoute
  "/cadastros/clientes/$clienteId/locais/$localId/editar": typeof CadastrosClientesClienteIdLocaisLocalIdEditarRoute
}
export interface FileRoutesByTo {
  "/": typeof IndexRoute
  "/$": typeof SplatRoute
  "/login": typeof LoginRoute
  "/onboarding-pendente": typeof OnboardingPendenteRoute
  "/termos": typeof TermosRoute
  "/termos-uso": typeof TermosUsoRoute
  "/antecipacoes/$uuid": typeof AntecipacoesUuidRouteWithChildren
  "/antecipacoes/nova": typeof AntecipacoesNovaRoute
  "/fechamentos/$uuid": typeof FechamentosUuidRoute
  "/fechamentos/novo": typeof FechamentosNovoRoute
  "/plantoes/novo": typeof PlantoesNovoRoute
  "/profissional/antecipacoes": typeof ProfissionalAntecipacoesRoute
  "/profissional/dashboard": typeof ProfissionalDashboardRoute
  "/profissional/fechamentos": typeof ProfissionalFechamentosRoute
  "/profissional/plantoes": typeof ProfissionalPlantoesRouteWithChildren
  "/antecipacoes": typeof AntecipacoesIndexRoute
  "/auditoria": typeof AuditoriaIndexRoute
  "/fechamentos": typeof FechamentosIndexRoute
  "/perfil": typeof PerfilIndexRoute
  "/plantoes": typeof PlantoesIndexRoute
  "/antecipacoes/$uuid/termo": typeof AntecipacoesUuidTermoRoute
  "/antecipacoes/termo/$token": typeof AntecipacoesTermoTokenRoute
  "/cadastros/clientes/$clienteId": typeof CadastrosClientesClienteIdRouteWithChildren
  "/cadastros/clientes/novo": typeof CadastrosClientesNovoRoute
  "/cadastros/profissionais/$uuid": typeof CadastrosProfissionaisUuidRoute
  "/cadastros/profissionais/novo": typeof CadastrosProfissionaisNovoRoute
  "/cadastros/usuarios/$id": typeof CadastrosUsuariosIdRoute
  "/cadastros/usuarios/novo": typeof CadastrosUsuariosNovoRoute
  "/plantoes/$uuid/editar": typeof PlantoesUuidEditarRoute
  "/cadastros/clientes": typeof CadastrosClientesIndexRoute
  "/cadastros/locais": typeof CadastrosLocaisIndexRoute
  "/cadastros/profissionais": typeof CadastrosProfissionaisIndexRoute
  "/cadastros/usuarios": typeof CadastrosUsuariosIndexRoute
  "/plantoes/$uuid": typeof PlantoesUuidIndexRoute
  "/profissional/plantoes/$plantaoId/registros": typeof ProfissionalPlantoesPlantaoIdRegistrosRoute
  "/cadastros/clientes/$clienteId/locais/novo": typeof CadastrosClientesClienteIdLocaisNovoRoute
  "/cadastros/clientes/$clienteId/locais/$localId/editar": typeof CadastrosClientesClienteIdLocaisLocalIdEditarRoute
}
export interface FileRoutesById {
  __root__: typeof rootRouteImport
  "/": typeof IndexRoute
  "/$": typeof SplatRoute
  "/login": typeof LoginRoute
  "/onboarding-pendente": typeof OnboardingPendenteRoute
  "/termos": typeof TermosRoute
  "/termos-uso": typeof TermosUsoRoute
  "/antecipacoes/$uuid": typeof AntecipacoesUuidRouteWithChildren
  "/antecipacoes/nova": typeof AntecipacoesNovaRoute
  "/fechamentos/$uuid": typeof FechamentosUuidRoute
  "/fechamentos/novo": typeof FechamentosNovoRoute
  "/plantoes/novo": typeof PlantoesNovoRoute
  "/profissional/antecipacoes": typeof ProfissionalAntecipacoesRoute
  "/profissional/dashboard": typeof ProfissionalDashboardRoute
  "/profissional/fechamentos": typeof ProfissionalFechamentosRoute
  "/profissional/plantoes": typeof ProfissionalPlantoesRouteWithChildren
  "/antecipacoes/": typeof AntecipacoesIndexRoute
  "/auditoria/": typeof AuditoriaIndexRoute
  "/fechamentos/": typeof FechamentosIndexRoute
  "/perfil/": typeof PerfilIndexRoute
  "/plantoes/": typeof PlantoesIndexRoute
  "/antecipacoes/$uuid/termo": typeof AntecipacoesUuidTermoRoute
  "/antecipacoes/termo/$token": typeof AntecipacoesTermoTokenRoute
  "/cadastros/clientes/$clienteId": typeof CadastrosClientesClienteIdRouteWithChildren
  "/cadastros/clientes/novo": typeof CadastrosClientesNovoRoute
  "/cadastros/profissionais/$uuid": typeof CadastrosProfissionaisUuidRoute
  "/cadastros/profissionais/novo": typeof CadastrosProfissionaisNovoRoute
  "/cadastros/usuarios/$id": typeof CadastrosUsuariosIdRoute
  "/cadastros/usuarios/novo": typeof CadastrosUsuariosNovoRoute
  "/plantoes/$uuid/editar": typeof PlantoesUuidEditarRoute
  "/cadastros/clientes/": typeof CadastrosClientesIndexRoute
  "/cadastros/locais/": typeof CadastrosLocaisIndexRoute
  "/cadastros/profissionais/": typeof CadastrosProfissionaisIndexRoute
  "/cadastros/usuarios/": typeof CadastrosUsuariosIndexRoute
  "/plantoes/$uuid/": typeof PlantoesUuidIndexRoute
  "/profissional/plantoes/$plantaoId/registros": typeof ProfissionalPlantoesPlantaoIdRegistrosRoute
  "/cadastros/clientes/$clienteId/locais/novo": typeof CadastrosClientesClienteIdLocaisNovoRoute
  "/cadastros/clientes/$clienteId/locais/$localId/editar": typeof CadastrosClientesClienteIdLocaisLocalIdEditarRoute
}
export interface FileRouteTypes {
  fileRoutesByFullPath: FileRoutesByFullPath
  fullPaths:
    | "/"
    | "/$"
    | "/login"
    | "/onboarding-pendente"
    | "/termos"
    | "/termos-uso"
    | "/antecipacoes/$uuid"
    | "/antecipacoes/nova"
    | "/fechamentos/$uuid"
    | "/fechamentos/novo"
    | "/plantoes/novo"
    | "/profissional/antecipacoes"
    | "/profissional/dashboard"
    | "/profissional/fechamentos"
    | "/profissional/plantoes"
    | "/antecipacoes"
    | "/auditoria"
    | "/fechamentos"
    | "/perfil"
    | "/plantoes"
    | "/antecipacoes/$uuid/termo"
    | "/antecipacoes/termo/$token"
    | "/cadastros/clientes/$clienteId"
    | "/cadastros/clientes/novo"
    | "/cadastros/profissionais/$uuid"
    | "/cadastros/profissionais/novo"
    | "/cadastros/usuarios/$id"
    | "/cadastros/usuarios/novo"
    | "/plantoes/$uuid/editar"
    | "/cadastros/clientes"
    | "/cadastros/locais"
    | "/cadastros/profissionais"
    | "/cadastros/usuarios"
    | "/plantoes/$uuid"
    | "/profissional/plantoes/$plantaoId/registros"
    | "/cadastros/clientes/$clienteId/locais/novo"
    | "/cadastros/clientes/$clienteId/locais/$localId/editar"
  fileRoutesByTo: FileRoutesByTo
  to:
    | "/"
    | "/$"
    | "/login"
    | "/onboarding-pendente"
    | "/termos"
    | "/termos-uso"
    | "/antecipacoes/$uuid"
    | "/antecipacoes/nova"
    | "/fechamentos/$uuid"
    | "/fechamentos/novo"
    | "/plantoes/novo"
    | "/profissional/antecipacoes"
    | "/profissional/dashboard"
    | "/profissional/fechamentos"
    | "/profissional/plantoes"
    | "/antecipacoes"
    | "/auditoria"
    | "/fechamentos"
    | "/perfil"
    | "/plantoes"
    | "/antecipacoes/$uuid/termo"
    | "/antecipacoes/termo/$token"
    | "/cadastros/clientes/$clienteId"
    | "/cadastros/clientes/novo"
    | "/cadastros/profissionais/$uuid"
    | "/cadastros/profissionais/novo"
    | "/cadastros/usuarios/$id"
    | "/cadastros/usuarios/novo"
    | "/plantoes/$uuid/editar"
    | "/cadastros/clientes"
    | "/cadastros/locais"
    | "/cadastros/profissionais"
    | "/cadastros/usuarios"
    | "/plantoes/$uuid"
    | "/profissional/plantoes/$plantaoId/registros"
    | "/cadastros/clientes/$clienteId/locais/novo"
    | "/cadastros/clientes/$clienteId/locais/$localId/editar"
  id:
    | "__root__"
    | "/"
    | "/$"
    | "/login"
    | "/onboarding-pendente"
    | "/termos"
    | "/termos-uso"
    | "/antecipacoes/$uuid"
    | "/antecipacoes/nova"
    | "/fechamentos/$uuid"
    | "/fechamentos/novo"
    | "/plantoes/novo"
    | "/profissional/antecipacoes"
    | "/profissional/dashboard"
    | "/profissional/fechamentos"
    | "/profissional/plantoes"
    | "/antecipacoes/"
    | "/auditoria/"
    | "/fechamentos/"
    | "/perfil/"
    | "/plantoes/"
    | "/antecipacoes/$uuid/termo"
    | "/antecipacoes/termo/$token"
    | "/cadastros/clientes/$clienteId"
    | "/cadastros/clientes/novo"
    | "/cadastros/profissionais/$uuid"
    | "/cadastros/profissionais/novo"
    | "/cadastros/usuarios/$id"
    | "/cadastros/usuarios/novo"
    | "/plantoes/$uuid/editar"
    | "/cadastros/clientes/"
    | "/cadastros/locais/"
    | "/cadastros/profissionais/"
    | "/cadastros/usuarios/"
    | "/plantoes/$uuid/"
    | "/profissional/plantoes/$plantaoId/registros"
    | "/cadastros/clientes/$clienteId/locais/novo"
    | "/cadastros/clientes/$clienteId/locais/$localId/editar"
  fileRoutesById: FileRoutesById
}
export interface RootRouteChildren {
  IndexRoute: typeof IndexRoute
  SplatRoute: typeof SplatRoute
  LoginRoute: typeof LoginRoute
  OnboardingPendenteRoute: typeof OnboardingPendenteRoute
  TermosRoute: typeof TermosRoute
  TermosUsoRoute: typeof TermosUsoRoute
  AntecipacoesUuidRoute: typeof AntecipacoesUuidRouteWithChildren
  AntecipacoesNovaRoute: typeof AntecipacoesNovaRoute
  FechamentosUuidRoute: typeof FechamentosUuidRoute
  FechamentosNovoRoute: typeof FechamentosNovoRoute
  PlantoesNovoRoute: typeof PlantoesNovoRoute
  ProfissionalAntecipacoesRoute: typeof ProfissionalAntecipacoesRoute
  ProfissionalDashboardRoute: typeof ProfissionalDashboardRoute
  ProfissionalFechamentosRoute: typeof ProfissionalFechamentosRoute
  ProfissionalPlantoesRoute: typeof ProfissionalPlantoesRouteWithChildren
  AntecipacoesIndexRoute: typeof AntecipacoesIndexRoute
  AuditoriaIndexRoute: typeof AuditoriaIndexRoute
  FechamentosIndexRoute: typeof FechamentosIndexRoute
  PerfilIndexRoute: typeof PerfilIndexRoute
  PlantoesIndexRoute: typeof PlantoesIndexRoute
  AntecipacoesTermoTokenRoute: typeof AntecipacoesTermoTokenRoute
  CadastrosClientesClienteIdRoute: typeof CadastrosClientesClienteIdRouteWithChildren
  CadastrosClientesNovoRoute: typeof CadastrosClientesNovoRoute
  CadastrosProfissionaisUuidRoute: typeof CadastrosProfissionaisUuidRoute
  CadastrosProfissionaisNovoRoute: typeof CadastrosProfissionaisNovoRoute
  CadastrosUsuariosIdRoute: typeof CadastrosUsuariosIdRoute
  CadastrosUsuariosNovoRoute: typeof CadastrosUsuariosNovoRoute
  PlantoesUuidEditarRoute: typeof PlantoesUuidEditarRoute
  CadastrosClientesIndexRoute: typeof CadastrosClientesIndexRoute
  CadastrosLocaisIndexRoute: typeof CadastrosLocaisIndexRoute
  CadastrosProfissionaisIndexRoute: typeof CadastrosProfissionaisIndexRoute
  CadastrosUsuariosIndexRoute: typeof CadastrosUsuariosIndexRoute
  PlantoesUuidIndexRoute: typeof PlantoesUuidIndexRoute
}

declare module "@tanstack/react-router" {
  interface FileRoutesByPath {
    "/termos-uso": {
      id: "/termos-uso"
      path: "/termos-uso"
      fullPath: "/termos-uso"
      preLoaderRoute: typeof TermosUsoRouteImport
      parentRoute: typeof rootRouteImport
    }
    "/termos": {
      id: "/termos"
      path: "/termos"
      fullPath: "/termos"
      preLoaderRoute: typeof TermosRouteImport
      parentRoute: typeof rootRouteImport
    }
    "/onboarding-pendente": {
      id: "/onboarding-pendente"
      path: "/onboarding-pendente"
      fullPath: "/onboarding-pendente"
      preLoaderRoute: typeof OnboardingPendenteRouteImport
      parentRoute: typeof rootRouteImport
    }
    "/login": {
      id: "/login"
      path: "/login"
      fullPath: "/login"
      preLoaderRoute: typeof LoginRouteImport
      parentRoute: typeof rootRouteImport
    }
    "/$": {
      id: "/$"
      path: "/$"
      fullPath: "/$"
      preLoaderRoute: typeof SplatRouteImport
      parentRoute: typeof rootRouteImport
    }
    "/": {
      id: "/"
      path: "/"
      fullPath: "/"
      preLoaderRoute: typeof IndexRouteImport
      parentRoute: typeof rootRouteImport
    }
    "/plantoes/": {
      id: "/plantoes/"
      path: "/plantoes"
      fullPath: "/plantoes"
      preLoaderRoute: typeof PlantoesIndexRouteImport
      parentRoute: typeof rootRouteImport
    }
    "/perfil/": {
      id: "/perfil/"
      path: "/perfil"
      fullPath: "/perfil"
      preLoaderRoute: typeof PerfilIndexRouteImport
      parentRoute: typeof rootRouteImport
    }
    "/fechamentos/": {
      id: "/fechamentos/"
      path: "/fechamentos"
      fullPath: "/fechamentos"
      preLoaderRoute: typeof FechamentosIndexRouteImport
      parentRoute: typeof rootRouteImport
    }
    "/auditoria/": {
      id: "/auditoria/"
      path: "/auditoria"
      fullPath: "/auditoria"
      preLoaderRoute: typeof AuditoriaIndexRouteImport
      parentRoute: typeof rootRouteImport
    }
    "/antecipacoes/": {
      id: "/antecipacoes/"
      path: "/antecipacoes"
      fullPath: "/antecipacoes"
      preLoaderRoute: typeof AntecipacoesIndexRouteImport
      parentRoute: typeof rootRouteImport
    }
    "/profissional/plantoes": {
      id: "/profissional/plantoes"
      path: "/profissional/plantoes"
      fullPath: "/profissional/plantoes"
      preLoaderRoute: typeof ProfissionalPlantoesRouteImport
      parentRoute: typeof rootRouteImport
    }
    "/profissional/fechamentos": {
      id: "/profissional/fechamentos"
      path: "/profissional/fechamentos"
      fullPath: "/profissional/fechamentos"
      preLoaderRoute: typeof ProfissionalFechamentosRouteImport
      parentRoute: typeof rootRouteImport
    }
    "/profissional/dashboard": {
      id: "/profissional/dashboard"
      path: "/profissional/dashboard"
      fullPath: "/profissional/dashboard"
      preLoaderRoute: typeof ProfissionalDashboardRouteImport
      parentRoute: typeof rootRouteImport
    }
    "/profissional/antecipacoes": {
      id: "/profissional/antecipacoes"
      path: "/profissional/antecipacoes"
      fullPath: "/profissional/antecipacoes"
      preLoaderRoute: typeof ProfissionalAntecipacoesRouteImport
      parentRoute: typeof rootRouteImport
    }
    "/plantoes/novo": {
      id: "/plantoes/novo"
      path: "/plantoes/novo"
      fullPath: "/plantoes/novo"
      preLoaderRoute: typeof PlantoesNovoRouteImport
      parentRoute: typeof rootRouteImport
    }
    "/fechamentos/novo": {
      id: "/fechamentos/novo"
      path: "/fechamentos/novo"
      fullPath: "/fechamentos/novo"
      preLoaderRoute: typeof FechamentosNovoRouteImport
      parentRoute: typeof rootRouteImport
    }
    "/fechamentos/$uuid": {
      id: "/fechamentos/$uuid"
      path: "/fechamentos/$uuid"
      fullPath: "/fechamentos/$uuid"
      preLoaderRoute: typeof FechamentosUuidRouteImport
      parentRoute: typeof rootRouteImport
    }
    "/antecipacoes/nova": {
      id: "/antecipacoes/nova"
      path: "/antecipacoes/nova"
      fullPath: "/antecipacoes/nova"
      preLoaderRoute: typeof AntecipacoesNovaRouteImport
      parentRoute: typeof rootRouteImport
    }
    "/antecipacoes/$uuid": {
      id: "/antecipacoes/$uuid"
      path: "/antecipacoes/$uuid"
      fullPath: "/antecipacoes/$uuid"
      preLoaderRoute: typeof AntecipacoesUuidRouteImport
      parentRoute: typeof rootRouteImport
    }
    "/plantoes/$uuid/": {
      id: "/plantoes/$uuid/"
      path: "/plantoes/$uuid"
      fullPath: "/plantoes/$uuid"
      preLoaderRoute: typeof PlantoesUuidIndexRouteImport
      parentRoute: typeof rootRouteImport
    }
    "/cadastros/usuarios/": {
      id: "/cadastros/usuarios/"
      path: "/cadastros/usuarios"
      fullPath: "/cadastros/usuarios"
      preLoaderRoute: typeof CadastrosUsuariosIndexRouteImport
      parentRoute: typeof rootRouteImport
    }
    "/cadastros/profissionais/": {
      id: "/cadastros/profissionais/"
      path: "/cadastros/profissionais"
      fullPath: "/cadastros/profissionais"
      preLoaderRoute: typeof CadastrosProfissionaisIndexRouteImport
      parentRoute: typeof rootRouteImport
    }
    "/cadastros/locais/": {
      id: "/cadastros/locais/"
      path: "/cadastros/locais"
      fullPath: "/cadastros/locais"
      preLoaderRoute: typeof CadastrosLocaisIndexRouteImport
      parentRoute: typeof rootRouteImport
    }
    "/cadastros/clientes/": {
      id: "/cadastros/clientes/"
      path: "/cadastros/clientes"
      fullPath: "/cadastros/clientes"
      preLoaderRoute: typeof CadastrosClientesIndexRouteImport
      parentRoute: typeof rootRouteImport
    }
    "/plantoes/$uuid/editar": {
      id: "/plantoes/$uuid/editar"
      path: "/plantoes/$uuid/editar"
      fullPath: "/plantoes/$uuid/editar"
      preLoaderRoute: typeof PlantoesUuidEditarRouteImport
      parentRoute: typeof rootRouteImport
    }
    "/cadastros/usuarios/novo": {
      id: "/cadastros/usuarios/novo"
      path: "/cadastros/usuarios/novo"
      fullPath: "/cadastros/usuarios/novo"
      preLoaderRoute: typeof CadastrosUsuariosNovoRouteImport
      parentRoute: typeof rootRouteImport
    }
    "/cadastros/usuarios/$id": {
      id: "/cadastros/usuarios/$id"
      path: "/cadastros/usuarios/$id"
      fullPath: "/cadastros/usuarios/$id"
      preLoaderRoute: typeof CadastrosUsuariosIdRouteImport
      parentRoute: typeof rootRouteImport
    }
    "/cadastros/profissionais/novo": {
      id: "/cadastros/profissionais/novo"
      path: "/cadastros/profissionais/novo"
      fullPath: "/cadastros/profissionais/novo"
      preLoaderRoute: typeof CadastrosProfissionaisNovoRouteImport
      parentRoute: typeof rootRouteImport
    }
    "/cadastros/profissionais/$uuid": {
      id: "/cadastros/profissionais/$uuid"
      path: "/cadastros/profissionais/$uuid"
      fullPath: "/cadastros/profissionais/$uuid"
      preLoaderRoute: typeof CadastrosProfissionaisUuidRouteImport
      parentRoute: typeof rootRouteImport
    }
    "/cadastros/clientes/novo": {
      id: "/cadastros/clientes/novo"
      path: "/cadastros/clientes/novo"
      fullPath: "/cadastros/clientes/novo"
      preLoaderRoute: typeof CadastrosClientesNovoRouteImport
      parentRoute: typeof rootRouteImport
    }
    "/cadastros/clientes/$clienteId": {
      id: "/cadastros/clientes/$clienteId"
      path: "/cadastros/clientes/$clienteId"
      fullPath: "/cadastros/clientes/$clienteId"
      preLoaderRoute: typeof CadastrosClientesClienteIdRouteImport
      parentRoute: typeof rootRouteImport
    }
    "/antecipacoes/termo/$token": {
      id: "/antecipacoes/termo/$token"
      path: "/antecipacoes/termo/$token"
      fullPath: "/antecipacoes/termo/$token"
      preLoaderRoute: typeof AntecipacoesTermoTokenRouteImport
      parentRoute: typeof rootRouteImport
    }
    "/antecipacoes/$uuid/termo": {
      id: "/antecipacoes/$uuid/termo"
      path: "/termo"
      fullPath: "/antecipacoes/$uuid/termo"
      preLoaderRoute: typeof AntecipacoesUuidTermoRouteImport
      parentRoute: typeof AntecipacoesUuidRoute
    }
    "/profissional/plantoes/$plantaoId/registros": {
      id: "/profissional/plantoes/$plantaoId/registros"
      path: "/$plantaoId/registros"
      fullPath: "/profissional/plantoes/$plantaoId/registros"
      preLoaderRoute: typeof ProfissionalPlantoesPlantaoIdRegistrosRouteImport
      parentRoute: typeof ProfissionalPlantoesRoute
    }
    "/cadastros/clientes/$clienteId/locais/novo": {
      id: "/cadastros/clientes/$clienteId/locais/novo"
      path: "/locais/novo"
      fullPath: "/cadastros/clientes/$clienteId/locais/novo"
      preLoaderRoute: typeof CadastrosClientesClienteIdLocaisNovoRouteImport
      parentRoute: typeof CadastrosClientesClienteIdRoute
    }
    "/cadastros/clientes/$clienteId/locais/$localId/editar": {
      id: "/cadastros/clientes/$clienteId/locais/$localId/editar"
      path: "/locais/$localId/editar"
      fullPath: "/cadastros/clientes/$clienteId/locais/$localId/editar"
      preLoaderRoute: typeof CadastrosClientesClienteIdLocaisLocalIdEditarRouteImport
      parentRoute: typeof CadastrosClientesClienteIdRoute
    }
  }
}

interface AntecipacoesUuidRouteChildren {
  AntecipacoesUuidTermoRoute: typeof AntecipacoesUuidTermoRoute
}

const AntecipacoesUuidRouteChildren: AntecipacoesUuidRouteChildren = {
  AntecipacoesUuidTermoRoute: AntecipacoesUuidTermoRoute,
}

const AntecipacoesUuidRouteWithChildren =
  AntecipacoesUuidRoute._addFileChildren(AntecipacoesUuidRouteChildren)

interface ProfissionalPlantoesRouteChildren {
  ProfissionalPlantoesPlantaoIdRegistrosRoute: typeof ProfissionalPlantoesPlantaoIdRegistrosRoute
}

const ProfissionalPlantoesRouteChildren: ProfissionalPlantoesRouteChildren = {
  ProfissionalPlantoesPlantaoIdRegistrosRoute:
    ProfissionalPlantoesPlantaoIdRegistrosRoute,
}

const ProfissionalPlantoesRouteWithChildren =
  ProfissionalPlantoesRoute._addFileChildren(ProfissionalPlantoesRouteChildren)

interface CadastrosClientesClienteIdRouteChildren {
  CadastrosClientesClienteIdLocaisNovoRoute: typeof CadastrosClientesClienteIdLocaisNovoRoute
  CadastrosClientesClienteIdLocaisLocalIdEditarRoute: typeof CadastrosClientesClienteIdLocaisLocalIdEditarRoute
}

const CadastrosClientesClienteIdRouteChildren: CadastrosClientesClienteIdRouteChildren =
  {
    CadastrosClientesClienteIdLocaisNovoRoute:
      CadastrosClientesClienteIdLocaisNovoRoute,
    CadastrosClientesClienteIdLocaisLocalIdEditarRoute:
      CadastrosClientesClienteIdLocaisLocalIdEditarRoute,
  }

const CadastrosClientesClienteIdRouteWithChildren =
  CadastrosClientesClienteIdRoute._addFileChildren(
    CadastrosClientesClienteIdRouteChildren,
  )

const rootRouteChildren: RootRouteChildren = {
  IndexRoute: IndexRoute,
  SplatRoute: SplatRoute,
  LoginRoute: LoginRoute,
  OnboardingPendenteRoute: OnboardingPendenteRoute,
  TermosRoute: TermosRoute,
  TermosUsoRoute: TermosUsoRoute,
  AntecipacoesUuidRoute: AntecipacoesUuidRouteWithChildren,
  AntecipacoesNovaRoute: AntecipacoesNovaRoute,
  FechamentosUuidRoute: FechamentosUuidRoute,
  FechamentosNovoRoute: FechamentosNovoRoute,
  PlantoesNovoRoute: PlantoesNovoRoute,
  ProfissionalAntecipacoesRoute: ProfissionalAntecipacoesRoute,
  ProfissionalDashboardRoute: ProfissionalDashboardRoute,
  ProfissionalFechamentosRoute: ProfissionalFechamentosRoute,
  ProfissionalPlantoesRoute: ProfissionalPlantoesRouteWithChildren,
  AntecipacoesIndexRoute: AntecipacoesIndexRoute,
  AuditoriaIndexRoute: AuditoriaIndexRoute,
  FechamentosIndexRoute: FechamentosIndexRoute,
  PerfilIndexRoute: PerfilIndexRoute,
  PlantoesIndexRoute: PlantoesIndexRoute,
  AntecipacoesTermoTokenRoute: AntecipacoesTermoTokenRoute,
  CadastrosClientesClienteIdRoute: CadastrosClientesClienteIdRouteWithChildren,
  CadastrosClientesNovoRoute: CadastrosClientesNovoRoute,
  CadastrosProfissionaisUuidRoute: CadastrosProfissionaisUuidRoute,
  CadastrosProfissionaisNovoRoute: CadastrosProfissionaisNovoRoute,
  CadastrosUsuariosIdRoute: CadastrosUsuariosIdRoute,
  CadastrosUsuariosNovoRoute: CadastrosUsuariosNovoRoute,
  PlantoesUuidEditarRoute: PlantoesUuidEditarRoute,
  CadastrosClientesIndexRoute: CadastrosClientesIndexRoute,
  CadastrosLocaisIndexRoute: CadastrosLocaisIndexRoute,
  CadastrosProfissionaisIndexRoute: CadastrosProfissionaisIndexRoute,
  CadastrosUsuariosIndexRoute: CadastrosUsuariosIndexRoute,
  PlantoesUuidIndexRoute: PlantoesUuidIndexRoute,
}
export const routeTree = rootRouteImport
  ._addFileChildren(rootRouteChildren)
  ._addFileTypes<FileRouteTypes>()
