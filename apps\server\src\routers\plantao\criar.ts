import { z } from "zod";
import { prisma, withAudit } from "@/lib/prisma";
import { createPlantaoSchema, type CreatePlantaoInput } from "@/schemas/plantao.schema";
import type { FastifyTypedInstance } from "@/types";
import { formatDateToDateTime, parseUTCDate, toISOString } from "@shared/date";

export function criarPlantaoRouter(fastify: FastifyTypedInstance) {
  // Criar plantão
  fastify.post<{ Body: CreatePlantaoInput }>(
    "/plantoes",
    withAudit(async (request, reply) => {
      try {
        // Parse and validate the request body with Zod schema
        // This will strip unknown fields like diasPorMes
        const validatedData = createPlantaoSchema.parse(request.body);
        const { diasPlantao, ...plantaoData } = validatedData;

        // Resolver UUIDs para IDs internos
        let clienteId = plantaoData.clienteId;
        let localAtendimentoId = plantaoData.localAtendimentoId;
        let profissionalId = plantaoData.profissionalId;

        // Resolver clienteUuid se fornecido
        if (plantaoData.clienteUuid && !clienteId) {
          const cliente = await prisma.cliente.findUnique({
            where: { uuid: plantaoData.clienteUuid },
            select: { id: true },
          });
          if (!cliente) {
            return reply.status(400).send({ error: "Cliente não encontrado" });
          }
          clienteId = cliente.id;
        }

        // Resolver localAtendimentoUuid se fornecido
        if (plantaoData.localAtendimentoUuid && !localAtendimentoId) {
          const local = await prisma.localAtendimento.findUnique({
            where: { uuid: plantaoData.localAtendimentoUuid },
            select: { id: true },
          });
          if (!local) {
            return reply.status(400).send({ error: "Local de atendimento não encontrado" });
          }
          localAtendimentoId = local.id;
        }

        // Resolver profissionalUuid se fornecido
        if (plantaoData.profissionalUuid && !profissionalId) {
          const profissional = await prisma.profissional.findUnique({
            where: { uuid: plantaoData.profissionalUuid },
            select: { id: true },
          });
          if (!profissional) {
            return reply.status(400).send({ error: "Profissional não encontrado" });
          }
          profissionalId = profissional.id;
        }

        // Validar que temos todos os IDs necessários
        if (!clienteId || !localAtendimentoId || !profissionalId) {
          return reply.status(400).send({
            error: "Cliente, local de atendimento e profissional são obrigatórios",
          });
        }

        // Verificar se já existe plantão para o profissional no mesmo período/cliente
        // Usar a nova constraint baseada em dataInicial
        const existingPlantao = await prisma.plantao.findFirst({
          where: {
            profissionalId,
            dataInicial: plantaoData.dataInicial.includes("T")
              ? plantaoData.dataInicial
              : `${plantaoData.dataInicial}T00:00:00.000Z`,
            clienteId,
          },
        });

        if (existingPlantao) {
          return reply.status(400).send({
            error: "Já existe um plantão para este profissional neste período e cliente",
          });
        }

        // Converter datas para DateTime ISO-8601 se necessário
        const processedData = {
          clienteId,
          localAtendimentoId,
          profissionalId,
          modalidadeTrabalho: plantaoData.modalidadeTrabalho,
          tipoFechamento: plantaoData.tipoFechamento,
          tipoValor: plantaoData.tipoValor,
          valorBase: plantaoData.valorBase,
          prazoPagamentoDias: plantaoData.prazoPagamentoDias,
          horaInicio: plantaoData.horaInicio,
          horaFim: plantaoData.horaFim,
          intervalo: plantaoData.intervalo,
          tipoTurno: plantaoData.tipoTurno ?? "",
          observacoes: plantaoData?.observacoes ?? null,
          dataInicial: formatDateToDateTime(plantaoData.dataInicial),
          ...(plantaoData.dataFinal && { dataFinal: formatDateToDateTime(plantaoData.dataFinal) }),
        };

        // Processar dias do plantão usando apenas a data completa
        const processedDiasPlantao = diasPlantao
          ?.map((dia: any) => {
            // Converter a data string (YYYY-MM-DD) para Date usando date-fns
            const date = parseUTCDate(dia.data);

            return {
              data: toISOString(date),
              horaEntrada: dia.horaEntrada,
              horaSaida: dia.horaSaida,
              intervalo: dia.intervalo,
            };
          })
          .filter(Boolean); // Remove nulls

        // Criar plantão com dias se fornecidos
        const plantao = await prisma.plantao.create({
          data: {
            ...processedData,
            ...(processedDiasPlantao && {
              diasPlantao: {
                create: processedDiasPlantao,
              },
            }),
          },
          include: {
            cliente: true,
            profissional: true,
            localAtendimento: true,
            diasPlantao: true,
          },
        });

        return reply.status(201).send(plantao);
      } catch (error) {
        if (error instanceof z.ZodError) {
          return reply.status(400).send({
            error: "Dados inválidos",
            details: error.issues,
          });
        }
        throw error;
      }
    })
  );
}
