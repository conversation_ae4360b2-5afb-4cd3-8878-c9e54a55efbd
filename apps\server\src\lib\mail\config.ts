import { SESClient, SendEmailCommand } from "@aws-sdk/client-ses";
import { ApiEmailService } from "./api-mail-service";
import { appEnv } from "../env";

interface EmailConfig {
  region: string;
  accessKeyId: string;
  secretAccessKey: string;
  fromEmail: string;
}

class EmailService {
  private sesClient: SESClient;
  private fromEmail: string;

  constructor(config: EmailConfig) {
    this.sesClient = new SESClient({
      region: config.region,
      credentials: {
        accessKeyId: config.accessKeyId,
        secretAccessKey: config.secretAccessKey,
      },
    });
    this.fromEmail = config.fromEmail;
  }

  async sendEmail({
    to,
    subject,
    htmlBody,
    textBody,
  }: {
    to: string | string[];
    subject: string;
    htmlBody?: string;
    textBody?: string;
  }) {
    const recipients = Array.isArray(to) ? to : [to];

    const params = {
      Source: this.fromEmail,
      Destination: {
        ToAddresses: recipients,
      },
      Message: {
        Subject: {
          Data: subject,
          Charset: "UTF-8",
        },
        Body: {
          ...(htmlBody && {
            Html: {
              Data: htmlBody,
              Charset: "UTF-8",
            },
          }),
          ...(textBody && {
            Text: {
              Data: textBody,
              Charset: "UTF-8",
            },
          }),
        },
      },
    };

    try {
      const command = new SendEmailCommand(params);
      const result = await this.sesClient.send(command);
      console.log("Email sent successfully:", result.MessageId);
      return result;
    } catch (error) {
      console.error("Error sending email:", error);
      throw error;
    }
  }
}

// Initialize email service with environment variables
let emailService: EmailService | ApiEmailService | null = null;

export function getEmailService(): EmailService | ApiEmailService {
  if (!emailService) {
    const config: EmailConfig = {
      region: appEnv.AWS_REGION || "us-east-1",
      accessKeyId: appEnv.AWS_ACCESS_KEY_ID || "",
      secretAccessKey: appEnv.AWS_SECRET_ACCESS_KEY || "",
      fromEmail: appEnv.AWS_SES_FROM_EMAIL || "",
    };

    // Validate configuration
    if (!config.accessKeyId || !config.secretAccessKey || !config.fromEmail) {
      throw new Error(
        "Missing AWS SES configuration. Please set AWS_ACCESS_KEY_ID, AWS_SECRET_ACCESS_KEY, and AWS_SES_FROM_EMAIL environment variables."
      );
    }

    // emailService = new EmailService(config);

    const url = appEnv.GS2_AWS_UTILS_URL;
    const apiKey = appEnv.GS2_AWS_UTILS_API_KEY;

    if (!url || !apiKey) {
      throw new Error(
        "Missing AWS Utils configuration. Please set GS2_AWS_UTILS_URL and GS2_AWS_UTILS_API_KEY environment variables."
      );
    }

    emailService = new ApiEmailService(url, apiKey);
  }

  return emailService;
}
