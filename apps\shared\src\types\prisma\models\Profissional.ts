
/* !!! This is code generated by Prisma. Do not edit directly. !!! */
/* eslint-disable */
// @ts-nocheck 
/*
 * This file exports the `Profissional` model and its related types.
 *
 * 🟢 You can import this file directly.
 */
import * as runtime from "@prisma/client/runtime/library"
import type * as $Enums from "../enums.ts"
import type * as Prisma from "../internal/prismaNamespace.ts"

/**
 * Model Profissional
 * 
 */
export type ProfissionalModel = runtime.Types.Result.DefaultSelection<Prisma.$ProfissionalPayload>

export type AggregateProfissional = {
  _count: ProfissionalCountAggregateOutputType | null
  _avg: ProfissionalAvgAggregateOutputType | null
  _sum: ProfissionalSumAggregateOutputType | null
  _min: ProfissionalMinAggregateOutputType | null
  _max: ProfissionalMaxAggregateOutputType | null
}

export type ProfissionalAvgAggregateOutputType = {
  id: number | null
  usuarioId: number | null
}

export type ProfissionalSumAggregateOutputType = {
  id: number | null
  usuarioId: number | null
}

export type ProfissionalMinAggregateOutputType = {
  id: number | null
  uuid: string | null
  usuarioId: number | null
  rg: string | null
  orgaoEmissor: string | null
  cnes: string | null
  conselhoClasse: string | null
  numeroRegistro: string | null
  ufConselho: string | null
  banco: string | null
  agencia: string | null
  digitoAgencia: string | null
  conta: string | null
  digitoConta: string | null
  tipoConta: string | null
  chavePix: string | null
  tipoPix: string | null
  tipoAssinatura: string | null
  createdAt: Date | null
  updatedAt: Date | null
  deletedAt: Date | null
}

export type ProfissionalMaxAggregateOutputType = {
  id: number | null
  uuid: string | null
  usuarioId: number | null
  rg: string | null
  orgaoEmissor: string | null
  cnes: string | null
  conselhoClasse: string | null
  numeroRegistro: string | null
  ufConselho: string | null
  banco: string | null
  agencia: string | null
  digitoAgencia: string | null
  conta: string | null
  digitoConta: string | null
  tipoConta: string | null
  chavePix: string | null
  tipoPix: string | null
  tipoAssinatura: string | null
  createdAt: Date | null
  updatedAt: Date | null
  deletedAt: Date | null
}

export type ProfissionalCountAggregateOutputType = {
  id: number
  uuid: number
  usuarioId: number
  rg: number
  orgaoEmissor: number
  cnes: number
  conselhoClasse: number
  numeroRegistro: number
  ufConselho: number
  banco: number
  agencia: number
  digitoAgencia: number
  conta: number
  digitoConta: number
  tipoConta: number
  chavePix: number
  tipoPix: number
  tipoAssinatura: number
  metaData: number
  createdAt: number
  updatedAt: number
  deletedAt: number
  _all: number
}


export type ProfissionalAvgAggregateInputType = {
  id?: true
  usuarioId?: true
}

export type ProfissionalSumAggregateInputType = {
  id?: true
  usuarioId?: true
}

export type ProfissionalMinAggregateInputType = {
  id?: true
  uuid?: true
  usuarioId?: true
  rg?: true
  orgaoEmissor?: true
  cnes?: true
  conselhoClasse?: true
  numeroRegistro?: true
  ufConselho?: true
  banco?: true
  agencia?: true
  digitoAgencia?: true
  conta?: true
  digitoConta?: true
  tipoConta?: true
  chavePix?: true
  tipoPix?: true
  tipoAssinatura?: true
  createdAt?: true
  updatedAt?: true
  deletedAt?: true
}

export type ProfissionalMaxAggregateInputType = {
  id?: true
  uuid?: true
  usuarioId?: true
  rg?: true
  orgaoEmissor?: true
  cnes?: true
  conselhoClasse?: true
  numeroRegistro?: true
  ufConselho?: true
  banco?: true
  agencia?: true
  digitoAgencia?: true
  conta?: true
  digitoConta?: true
  tipoConta?: true
  chavePix?: true
  tipoPix?: true
  tipoAssinatura?: true
  createdAt?: true
  updatedAt?: true
  deletedAt?: true
}

export type ProfissionalCountAggregateInputType = {
  id?: true
  uuid?: true
  usuarioId?: true
  rg?: true
  orgaoEmissor?: true
  cnes?: true
  conselhoClasse?: true
  numeroRegistro?: true
  ufConselho?: true
  banco?: true
  agencia?: true
  digitoAgencia?: true
  conta?: true
  digitoConta?: true
  tipoConta?: true
  chavePix?: true
  tipoPix?: true
  tipoAssinatura?: true
  metaData?: true
  createdAt?: true
  updatedAt?: true
  deletedAt?: true
  _all?: true
}

export type ProfissionalAggregateArgs<ExtArgs extends runtime.Types.Extensions.InternalArgs = runtime.Types.Extensions.DefaultArgs> = {
  /**
   * Filter which Profissional to aggregate.
   */
  where?: Prisma.ProfissionalWhereInput
  /**
   * {@link https://www.prisma.io/docs/concepts/components/prisma-client/sorting Sorting Docs}
   * 
   * Determine the order of Profissionals to fetch.
   */
  orderBy?: Prisma.ProfissionalOrderByWithRelationInput | Prisma.ProfissionalOrderByWithRelationInput[]
  /**
   * {@link https://www.prisma.io/docs/concepts/components/prisma-client/pagination#cursor-based-pagination Cursor Docs}
   * 
   * Sets the start position
   */
  cursor?: Prisma.ProfissionalWhereUniqueInput
  /**
   * {@link https://www.prisma.io/docs/concepts/components/prisma-client/pagination Pagination Docs}
   * 
   * Take `±n` Profissionals from the position of the cursor.
   */
  take?: number
  /**
   * {@link https://www.prisma.io/docs/concepts/components/prisma-client/pagination Pagination Docs}
   * 
   * Skip the first `n` Profissionals.
   */
  skip?: number
  /**
   * {@link https://www.prisma.io/docs/concepts/components/prisma-client/aggregations Aggregation Docs}
   * 
   * Count returned Profissionals
  **/
  _count?: true | ProfissionalCountAggregateInputType
  /**
   * {@link https://www.prisma.io/docs/concepts/components/prisma-client/aggregations Aggregation Docs}
   * 
   * Select which fields to average
  **/
  _avg?: ProfissionalAvgAggregateInputType
  /**
   * {@link https://www.prisma.io/docs/concepts/components/prisma-client/aggregations Aggregation Docs}
   * 
   * Select which fields to sum
  **/
  _sum?: ProfissionalSumAggregateInputType
  /**
   * {@link https://www.prisma.io/docs/concepts/components/prisma-client/aggregations Aggregation Docs}
   * 
   * Select which fields to find the minimum value
  **/
  _min?: ProfissionalMinAggregateInputType
  /**
   * {@link https://www.prisma.io/docs/concepts/components/prisma-client/aggregations Aggregation Docs}
   * 
   * Select which fields to find the maximum value
  **/
  _max?: ProfissionalMaxAggregateInputType
}

export type GetProfissionalAggregateType<T extends ProfissionalAggregateArgs> = {
      [P in keyof T & keyof AggregateProfissional]: P extends '_count' | 'count'
    ? T[P] extends true
      ? number
      : Prisma.GetScalarType<T[P], AggregateProfissional[P]>
    : Prisma.GetScalarType<T[P], AggregateProfissional[P]>
}




export type ProfissionalGroupByArgs<ExtArgs extends runtime.Types.Extensions.InternalArgs = runtime.Types.Extensions.DefaultArgs> = {
  where?: Prisma.ProfissionalWhereInput
  orderBy?: Prisma.ProfissionalOrderByWithAggregationInput | Prisma.ProfissionalOrderByWithAggregationInput[]
  by: Prisma.ProfissionalScalarFieldEnum[] | Prisma.ProfissionalScalarFieldEnum
  having?: Prisma.ProfissionalScalarWhereWithAggregatesInput
  take?: number
  skip?: number
  _count?: ProfissionalCountAggregateInputType | true
  _avg?: ProfissionalAvgAggregateInputType
  _sum?: ProfissionalSumAggregateInputType
  _min?: ProfissionalMinAggregateInputType
  _max?: ProfissionalMaxAggregateInputType
}

export type ProfissionalGroupByOutputType = {
  id: number
  uuid: string
  usuarioId: number
  rg: string | null
  orgaoEmissor: string | null
  cnes: string | null
  conselhoClasse: string | null
  numeroRegistro: string | null
  ufConselho: string | null
  banco: string | null
  agencia: string | null
  digitoAgencia: string | null
  conta: string | null
  digitoConta: string | null
  tipoConta: string | null
  chavePix: string | null
  tipoPix: string | null
  tipoAssinatura: string | null
  metaData: runtime.JsonValue | null
  createdAt: Date
  updatedAt: Date
  deletedAt: Date | null
  _count: ProfissionalCountAggregateOutputType | null
  _avg: ProfissionalAvgAggregateOutputType | null
  _sum: ProfissionalSumAggregateOutputType | null
  _min: ProfissionalMinAggregateOutputType | null
  _max: ProfissionalMaxAggregateOutputType | null
}

type GetProfissionalGroupByPayload<T extends ProfissionalGroupByArgs> = Prisma.PrismaPromise<
  Array<
    Prisma.PickEnumerable<ProfissionalGroupByOutputType, T['by']> &
      {
        [P in ((keyof T) & (keyof ProfissionalGroupByOutputType))]: P extends '_count'
          ? T[P] extends boolean
            ? number
            : Prisma.GetScalarType<T[P], ProfissionalGroupByOutputType[P]>
          : Prisma.GetScalarType<T[P], ProfissionalGroupByOutputType[P]>
      }
    >
  >



export type ProfissionalWhereInput = {
  AND?: Prisma.ProfissionalWhereInput | Prisma.ProfissionalWhereInput[]
  OR?: Prisma.ProfissionalWhereInput[]
  NOT?: Prisma.ProfissionalWhereInput | Prisma.ProfissionalWhereInput[]
  id?: Prisma.IntFilter<"Profissional"> | number
  uuid?: Prisma.StringFilter<"Profissional"> | string
  usuarioId?: Prisma.IntFilter<"Profissional"> | number
  rg?: Prisma.StringNullableFilter<"Profissional"> | string | null
  orgaoEmissor?: Prisma.StringNullableFilter<"Profissional"> | string | null
  cnes?: Prisma.StringNullableFilter<"Profissional"> | string | null
  conselhoClasse?: Prisma.StringNullableFilter<"Profissional"> | string | null
  numeroRegistro?: Prisma.StringNullableFilter<"Profissional"> | string | null
  ufConselho?: Prisma.StringNullableFilter<"Profissional"> | string | null
  banco?: Prisma.StringNullableFilter<"Profissional"> | string | null
  agencia?: Prisma.StringNullableFilter<"Profissional"> | string | null
  digitoAgencia?: Prisma.StringNullableFilter<"Profissional"> | string | null
  conta?: Prisma.StringNullableFilter<"Profissional"> | string | null
  digitoConta?: Prisma.StringNullableFilter<"Profissional"> | string | null
  tipoConta?: Prisma.StringNullableFilter<"Profissional"> | string | null
  chavePix?: Prisma.StringNullableFilter<"Profissional"> | string | null
  tipoPix?: Prisma.StringNullableFilter<"Profissional"> | string | null
  tipoAssinatura?: Prisma.StringNullableFilter<"Profissional"> | string | null
  metaData?: Prisma.JsonNullableFilter<"Profissional">
  createdAt?: Prisma.DateTimeFilter<"Profissional"> | Date | string
  updatedAt?: Prisma.DateTimeFilter<"Profissional"> | Date | string
  deletedAt?: Prisma.DateTimeNullableFilter<"Profissional"> | Date | string | null
  usuario?: Prisma.XOR<Prisma.UsuarioScalarRelationFilter, Prisma.UsuarioWhereInput>
  especialidades?: Prisma.ProfissionalEspecialidadeListRelationFilter
  plantoes?: Prisma.PlantaoListRelationFilter
  fechamentos?: Prisma.FechamentoListRelationFilter
  antecipacoes?: Prisma.AntecipacaoListRelationFilter
}

export type ProfissionalOrderByWithRelationInput = {
  id?: Prisma.SortOrder
  uuid?: Prisma.SortOrder
  usuarioId?: Prisma.SortOrder
  rg?: Prisma.SortOrderInput | Prisma.SortOrder
  orgaoEmissor?: Prisma.SortOrderInput | Prisma.SortOrder
  cnes?: Prisma.SortOrderInput | Prisma.SortOrder
  conselhoClasse?: Prisma.SortOrderInput | Prisma.SortOrder
  numeroRegistro?: Prisma.SortOrderInput | Prisma.SortOrder
  ufConselho?: Prisma.SortOrderInput | Prisma.SortOrder
  banco?: Prisma.SortOrderInput | Prisma.SortOrder
  agencia?: Prisma.SortOrderInput | Prisma.SortOrder
  digitoAgencia?: Prisma.SortOrderInput | Prisma.SortOrder
  conta?: Prisma.SortOrderInput | Prisma.SortOrder
  digitoConta?: Prisma.SortOrderInput | Prisma.SortOrder
  tipoConta?: Prisma.SortOrderInput | Prisma.SortOrder
  chavePix?: Prisma.SortOrderInput | Prisma.SortOrder
  tipoPix?: Prisma.SortOrderInput | Prisma.SortOrder
  tipoAssinatura?: Prisma.SortOrderInput | Prisma.SortOrder
  metaData?: Prisma.SortOrderInput | Prisma.SortOrder
  createdAt?: Prisma.SortOrder
  updatedAt?: Prisma.SortOrder
  deletedAt?: Prisma.SortOrderInput | Prisma.SortOrder
  usuario?: Prisma.UsuarioOrderByWithRelationInput
  especialidades?: Prisma.ProfissionalEspecialidadeOrderByRelationAggregateInput
  plantoes?: Prisma.PlantaoOrderByRelationAggregateInput
  fechamentos?: Prisma.FechamentoOrderByRelationAggregateInput
  antecipacoes?: Prisma.AntecipacaoOrderByRelationAggregateInput
  _relevance?: Prisma.ProfissionalOrderByRelevanceInput
}

export type ProfissionalWhereUniqueInput = Prisma.AtLeast<{
  id?: number
  uuid?: string
  usuarioId?: number
  AND?: Prisma.ProfissionalWhereInput | Prisma.ProfissionalWhereInput[]
  OR?: Prisma.ProfissionalWhereInput[]
  NOT?: Prisma.ProfissionalWhereInput | Prisma.ProfissionalWhereInput[]
  rg?: Prisma.StringNullableFilter<"Profissional"> | string | null
  orgaoEmissor?: Prisma.StringNullableFilter<"Profissional"> | string | null
  cnes?: Prisma.StringNullableFilter<"Profissional"> | string | null
  conselhoClasse?: Prisma.StringNullableFilter<"Profissional"> | string | null
  numeroRegistro?: Prisma.StringNullableFilter<"Profissional"> | string | null
  ufConselho?: Prisma.StringNullableFilter<"Profissional"> | string | null
  banco?: Prisma.StringNullableFilter<"Profissional"> | string | null
  agencia?: Prisma.StringNullableFilter<"Profissional"> | string | null
  digitoAgencia?: Prisma.StringNullableFilter<"Profissional"> | string | null
  conta?: Prisma.StringNullableFilter<"Profissional"> | string | null
  digitoConta?: Prisma.StringNullableFilter<"Profissional"> | string | null
  tipoConta?: Prisma.StringNullableFilter<"Profissional"> | string | null
  chavePix?: Prisma.StringNullableFilter<"Profissional"> | string | null
  tipoPix?: Prisma.StringNullableFilter<"Profissional"> | string | null
  tipoAssinatura?: Prisma.StringNullableFilter<"Profissional"> | string | null
  metaData?: Prisma.JsonNullableFilter<"Profissional">
  createdAt?: Prisma.DateTimeFilter<"Profissional"> | Date | string
  updatedAt?: Prisma.DateTimeFilter<"Profissional"> | Date | string
  deletedAt?: Prisma.DateTimeNullableFilter<"Profissional"> | Date | string | null
  usuario?: Prisma.XOR<Prisma.UsuarioScalarRelationFilter, Prisma.UsuarioWhereInput>
  especialidades?: Prisma.ProfissionalEspecialidadeListRelationFilter
  plantoes?: Prisma.PlantaoListRelationFilter
  fechamentos?: Prisma.FechamentoListRelationFilter
  antecipacoes?: Prisma.AntecipacaoListRelationFilter
}, "id" | "uuid" | "usuarioId">

export type ProfissionalOrderByWithAggregationInput = {
  id?: Prisma.SortOrder
  uuid?: Prisma.SortOrder
  usuarioId?: Prisma.SortOrder
  rg?: Prisma.SortOrderInput | Prisma.SortOrder
  orgaoEmissor?: Prisma.SortOrderInput | Prisma.SortOrder
  cnes?: Prisma.SortOrderInput | Prisma.SortOrder
  conselhoClasse?: Prisma.SortOrderInput | Prisma.SortOrder
  numeroRegistro?: Prisma.SortOrderInput | Prisma.SortOrder
  ufConselho?: Prisma.SortOrderInput | Prisma.SortOrder
  banco?: Prisma.SortOrderInput | Prisma.SortOrder
  agencia?: Prisma.SortOrderInput | Prisma.SortOrder
  digitoAgencia?: Prisma.SortOrderInput | Prisma.SortOrder
  conta?: Prisma.SortOrderInput | Prisma.SortOrder
  digitoConta?: Prisma.SortOrderInput | Prisma.SortOrder
  tipoConta?: Prisma.SortOrderInput | Prisma.SortOrder
  chavePix?: Prisma.SortOrderInput | Prisma.SortOrder
  tipoPix?: Prisma.SortOrderInput | Prisma.SortOrder
  tipoAssinatura?: Prisma.SortOrderInput | Prisma.SortOrder
  metaData?: Prisma.SortOrderInput | Prisma.SortOrder
  createdAt?: Prisma.SortOrder
  updatedAt?: Prisma.SortOrder
  deletedAt?: Prisma.SortOrderInput | Prisma.SortOrder
  _count?: Prisma.ProfissionalCountOrderByAggregateInput
  _avg?: Prisma.ProfissionalAvgOrderByAggregateInput
  _max?: Prisma.ProfissionalMaxOrderByAggregateInput
  _min?: Prisma.ProfissionalMinOrderByAggregateInput
  _sum?: Prisma.ProfissionalSumOrderByAggregateInput
}

export type ProfissionalScalarWhereWithAggregatesInput = {
  AND?: Prisma.ProfissionalScalarWhereWithAggregatesInput | Prisma.ProfissionalScalarWhereWithAggregatesInput[]
  OR?: Prisma.ProfissionalScalarWhereWithAggregatesInput[]
  NOT?: Prisma.ProfissionalScalarWhereWithAggregatesInput | Prisma.ProfissionalScalarWhereWithAggregatesInput[]
  id?: Prisma.IntWithAggregatesFilter<"Profissional"> | number
  uuid?: Prisma.StringWithAggregatesFilter<"Profissional"> | string
  usuarioId?: Prisma.IntWithAggregatesFilter<"Profissional"> | number
  rg?: Prisma.StringNullableWithAggregatesFilter<"Profissional"> | string | null
  orgaoEmissor?: Prisma.StringNullableWithAggregatesFilter<"Profissional"> | string | null
  cnes?: Prisma.StringNullableWithAggregatesFilter<"Profissional"> | string | null
  conselhoClasse?: Prisma.StringNullableWithAggregatesFilter<"Profissional"> | string | null
  numeroRegistro?: Prisma.StringNullableWithAggregatesFilter<"Profissional"> | string | null
  ufConselho?: Prisma.StringNullableWithAggregatesFilter<"Profissional"> | string | null
  banco?: Prisma.StringNullableWithAggregatesFilter<"Profissional"> | string | null
  agencia?: Prisma.StringNullableWithAggregatesFilter<"Profissional"> | string | null
  digitoAgencia?: Prisma.StringNullableWithAggregatesFilter<"Profissional"> | string | null
  conta?: Prisma.StringNullableWithAggregatesFilter<"Profissional"> | string | null
  digitoConta?: Prisma.StringNullableWithAggregatesFilter<"Profissional"> | string | null
  tipoConta?: Prisma.StringNullableWithAggregatesFilter<"Profissional"> | string | null
  chavePix?: Prisma.StringNullableWithAggregatesFilter<"Profissional"> | string | null
  tipoPix?: Prisma.StringNullableWithAggregatesFilter<"Profissional"> | string | null
  tipoAssinatura?: Prisma.StringNullableWithAggregatesFilter<"Profissional"> | string | null
  metaData?: Prisma.JsonNullableWithAggregatesFilter<"Profissional">
  createdAt?: Prisma.DateTimeWithAggregatesFilter<"Profissional"> | Date | string
  updatedAt?: Prisma.DateTimeWithAggregatesFilter<"Profissional"> | Date | string
  deletedAt?: Prisma.DateTimeNullableWithAggregatesFilter<"Profissional"> | Date | string | null
}

export type ProfissionalCreateInput = {
  uuid?: string
  rg?: string | null
  orgaoEmissor?: string | null
  cnes?: string | null
  conselhoClasse?: string | null
  numeroRegistro?: string | null
  ufConselho?: string | null
  banco?: string | null
  agencia?: string | null
  digitoAgencia?: string | null
  conta?: string | null
  digitoConta?: string | null
  tipoConta?: string | null
  chavePix?: string | null
  tipoPix?: string | null
  tipoAssinatura?: string | null
  metaData?: Prisma.NullableJsonNullValueInput | runtime.InputJsonValue
  createdAt?: Date | string
  updatedAt?: Date | string
  deletedAt?: Date | string | null
  usuario: Prisma.UsuarioCreateNestedOneWithoutProfissionalInput
  especialidades?: Prisma.ProfissionalEspecialidadeCreateNestedManyWithoutProfissionalInput
  plantoes?: Prisma.PlantaoCreateNestedManyWithoutProfissionalInput
  fechamentos?: Prisma.FechamentoCreateNestedManyWithoutProfissionalInput
  antecipacoes?: Prisma.AntecipacaoCreateNestedManyWithoutProfissionalInput
}

export type ProfissionalUncheckedCreateInput = {
  id?: number
  uuid?: string
  usuarioId: number
  rg?: string | null
  orgaoEmissor?: string | null
  cnes?: string | null
  conselhoClasse?: string | null
  numeroRegistro?: string | null
  ufConselho?: string | null
  banco?: string | null
  agencia?: string | null
  digitoAgencia?: string | null
  conta?: string | null
  digitoConta?: string | null
  tipoConta?: string | null
  chavePix?: string | null
  tipoPix?: string | null
  tipoAssinatura?: string | null
  metaData?: Prisma.NullableJsonNullValueInput | runtime.InputJsonValue
  createdAt?: Date | string
  updatedAt?: Date | string
  deletedAt?: Date | string | null
  especialidades?: Prisma.ProfissionalEspecialidadeUncheckedCreateNestedManyWithoutProfissionalInput
  plantoes?: Prisma.PlantaoUncheckedCreateNestedManyWithoutProfissionalInput
  fechamentos?: Prisma.FechamentoUncheckedCreateNestedManyWithoutProfissionalInput
  antecipacoes?: Prisma.AntecipacaoUncheckedCreateNestedManyWithoutProfissionalInput
}

export type ProfissionalUpdateInput = {
  uuid?: Prisma.StringFieldUpdateOperationsInput | string
  rg?: Prisma.NullableStringFieldUpdateOperationsInput | string | null
  orgaoEmissor?: Prisma.NullableStringFieldUpdateOperationsInput | string | null
  cnes?: Prisma.NullableStringFieldUpdateOperationsInput | string | null
  conselhoClasse?: Prisma.NullableStringFieldUpdateOperationsInput | string | null
  numeroRegistro?: Prisma.NullableStringFieldUpdateOperationsInput | string | null
  ufConselho?: Prisma.NullableStringFieldUpdateOperationsInput | string | null
  banco?: Prisma.NullableStringFieldUpdateOperationsInput | string | null
  agencia?: Prisma.NullableStringFieldUpdateOperationsInput | string | null
  digitoAgencia?: Prisma.NullableStringFieldUpdateOperationsInput | string | null
  conta?: Prisma.NullableStringFieldUpdateOperationsInput | string | null
  digitoConta?: Prisma.NullableStringFieldUpdateOperationsInput | string | null
  tipoConta?: Prisma.NullableStringFieldUpdateOperationsInput | string | null
  chavePix?: Prisma.NullableStringFieldUpdateOperationsInput | string | null
  tipoPix?: Prisma.NullableStringFieldUpdateOperationsInput | string | null
  tipoAssinatura?: Prisma.NullableStringFieldUpdateOperationsInput | string | null
  metaData?: Prisma.NullableJsonNullValueInput | runtime.InputJsonValue
  createdAt?: Prisma.DateTimeFieldUpdateOperationsInput | Date | string
  updatedAt?: Prisma.DateTimeFieldUpdateOperationsInput | Date | string
  deletedAt?: Prisma.NullableDateTimeFieldUpdateOperationsInput | Date | string | null
  usuario?: Prisma.UsuarioUpdateOneRequiredWithoutProfissionalNestedInput
  especialidades?: Prisma.ProfissionalEspecialidadeUpdateManyWithoutProfissionalNestedInput
  plantoes?: Prisma.PlantaoUpdateManyWithoutProfissionalNestedInput
  fechamentos?: Prisma.FechamentoUpdateManyWithoutProfissionalNestedInput
  antecipacoes?: Prisma.AntecipacaoUpdateManyWithoutProfissionalNestedInput
}

export type ProfissionalUncheckedUpdateInput = {
  id?: Prisma.IntFieldUpdateOperationsInput | number
  uuid?: Prisma.StringFieldUpdateOperationsInput | string
  usuarioId?: Prisma.IntFieldUpdateOperationsInput | number
  rg?: Prisma.NullableStringFieldUpdateOperationsInput | string | null
  orgaoEmissor?: Prisma.NullableStringFieldUpdateOperationsInput | string | null
  cnes?: Prisma.NullableStringFieldUpdateOperationsInput | string | null
  conselhoClasse?: Prisma.NullableStringFieldUpdateOperationsInput | string | null
  numeroRegistro?: Prisma.NullableStringFieldUpdateOperationsInput | string | null
  ufConselho?: Prisma.NullableStringFieldUpdateOperationsInput | string | null
  banco?: Prisma.NullableStringFieldUpdateOperationsInput | string | null
  agencia?: Prisma.NullableStringFieldUpdateOperationsInput | string | null
  digitoAgencia?: Prisma.NullableStringFieldUpdateOperationsInput | string | null
  conta?: Prisma.NullableStringFieldUpdateOperationsInput | string | null
  digitoConta?: Prisma.NullableStringFieldUpdateOperationsInput | string | null
  tipoConta?: Prisma.NullableStringFieldUpdateOperationsInput | string | null
  chavePix?: Prisma.NullableStringFieldUpdateOperationsInput | string | null
  tipoPix?: Prisma.NullableStringFieldUpdateOperationsInput | string | null
  tipoAssinatura?: Prisma.NullableStringFieldUpdateOperationsInput | string | null
  metaData?: Prisma.NullableJsonNullValueInput | runtime.InputJsonValue
  createdAt?: Prisma.DateTimeFieldUpdateOperationsInput | Date | string
  updatedAt?: Prisma.DateTimeFieldUpdateOperationsInput | Date | string
  deletedAt?: Prisma.NullableDateTimeFieldUpdateOperationsInput | Date | string | null
  especialidades?: Prisma.ProfissionalEspecialidadeUncheckedUpdateManyWithoutProfissionalNestedInput
  plantoes?: Prisma.PlantaoUncheckedUpdateManyWithoutProfissionalNestedInput
  fechamentos?: Prisma.FechamentoUncheckedUpdateManyWithoutProfissionalNestedInput
  antecipacoes?: Prisma.AntecipacaoUncheckedUpdateManyWithoutProfissionalNestedInput
}

export type ProfissionalCreateManyInput = {
  id?: number
  uuid?: string
  usuarioId: number
  rg?: string | null
  orgaoEmissor?: string | null
  cnes?: string | null
  conselhoClasse?: string | null
  numeroRegistro?: string | null
  ufConselho?: string | null
  banco?: string | null
  agencia?: string | null
  digitoAgencia?: string | null
  conta?: string | null
  digitoConta?: string | null
  tipoConta?: string | null
  chavePix?: string | null
  tipoPix?: string | null
  tipoAssinatura?: string | null
  metaData?: Prisma.NullableJsonNullValueInput | runtime.InputJsonValue
  createdAt?: Date | string
  updatedAt?: Date | string
  deletedAt?: Date | string | null
}

export type ProfissionalUpdateManyMutationInput = {
  uuid?: Prisma.StringFieldUpdateOperationsInput | string
  rg?: Prisma.NullableStringFieldUpdateOperationsInput | string | null
  orgaoEmissor?: Prisma.NullableStringFieldUpdateOperationsInput | string | null
  cnes?: Prisma.NullableStringFieldUpdateOperationsInput | string | null
  conselhoClasse?: Prisma.NullableStringFieldUpdateOperationsInput | string | null
  numeroRegistro?: Prisma.NullableStringFieldUpdateOperationsInput | string | null
  ufConselho?: Prisma.NullableStringFieldUpdateOperationsInput | string | null
  banco?: Prisma.NullableStringFieldUpdateOperationsInput | string | null
  agencia?: Prisma.NullableStringFieldUpdateOperationsInput | string | null
  digitoAgencia?: Prisma.NullableStringFieldUpdateOperationsInput | string | null
  conta?: Prisma.NullableStringFieldUpdateOperationsInput | string | null
  digitoConta?: Prisma.NullableStringFieldUpdateOperationsInput | string | null
  tipoConta?: Prisma.NullableStringFieldUpdateOperationsInput | string | null
  chavePix?: Prisma.NullableStringFieldUpdateOperationsInput | string | null
  tipoPix?: Prisma.NullableStringFieldUpdateOperationsInput | string | null
  tipoAssinatura?: Prisma.NullableStringFieldUpdateOperationsInput | string | null
  metaData?: Prisma.NullableJsonNullValueInput | runtime.InputJsonValue
  createdAt?: Prisma.DateTimeFieldUpdateOperationsInput | Date | string
  updatedAt?: Prisma.DateTimeFieldUpdateOperationsInput | Date | string
  deletedAt?: Prisma.NullableDateTimeFieldUpdateOperationsInput | Date | string | null
}

export type ProfissionalUncheckedUpdateManyInput = {
  id?: Prisma.IntFieldUpdateOperationsInput | number
  uuid?: Prisma.StringFieldUpdateOperationsInput | string
  usuarioId?: Prisma.IntFieldUpdateOperationsInput | number
  rg?: Prisma.NullableStringFieldUpdateOperationsInput | string | null
  orgaoEmissor?: Prisma.NullableStringFieldUpdateOperationsInput | string | null
  cnes?: Prisma.NullableStringFieldUpdateOperationsInput | string | null
  conselhoClasse?: Prisma.NullableStringFieldUpdateOperationsInput | string | null
  numeroRegistro?: Prisma.NullableStringFieldUpdateOperationsInput | string | null
  ufConselho?: Prisma.NullableStringFieldUpdateOperationsInput | string | null
  banco?: Prisma.NullableStringFieldUpdateOperationsInput | string | null
  agencia?: Prisma.NullableStringFieldUpdateOperationsInput | string | null
  digitoAgencia?: Prisma.NullableStringFieldUpdateOperationsInput | string | null
  conta?: Prisma.NullableStringFieldUpdateOperationsInput | string | null
  digitoConta?: Prisma.NullableStringFieldUpdateOperationsInput | string | null
  tipoConta?: Prisma.NullableStringFieldUpdateOperationsInput | string | null
  chavePix?: Prisma.NullableStringFieldUpdateOperationsInput | string | null
  tipoPix?: Prisma.NullableStringFieldUpdateOperationsInput | string | null
  tipoAssinatura?: Prisma.NullableStringFieldUpdateOperationsInput | string | null
  metaData?: Prisma.NullableJsonNullValueInput | runtime.InputJsonValue
  createdAt?: Prisma.DateTimeFieldUpdateOperationsInput | Date | string
  updatedAt?: Prisma.DateTimeFieldUpdateOperationsInput | Date | string
  deletedAt?: Prisma.NullableDateTimeFieldUpdateOperationsInput | Date | string | null
}

export type ProfissionalNullableScalarRelationFilter = {
  is?: Prisma.ProfissionalWhereInput | null
  isNot?: Prisma.ProfissionalWhereInput | null
}

export type ProfissionalOrderByRelevanceInput = {
  fields: Prisma.ProfissionalOrderByRelevanceFieldEnum | Prisma.ProfissionalOrderByRelevanceFieldEnum[]
  sort: Prisma.SortOrder
  search: string
}

export type ProfissionalCountOrderByAggregateInput = {
  id?: Prisma.SortOrder
  uuid?: Prisma.SortOrder
  usuarioId?: Prisma.SortOrder
  rg?: Prisma.SortOrder
  orgaoEmissor?: Prisma.SortOrder
  cnes?: Prisma.SortOrder
  conselhoClasse?: Prisma.SortOrder
  numeroRegistro?: Prisma.SortOrder
  ufConselho?: Prisma.SortOrder
  banco?: Prisma.SortOrder
  agencia?: Prisma.SortOrder
  digitoAgencia?: Prisma.SortOrder
  conta?: Prisma.SortOrder
  digitoConta?: Prisma.SortOrder
  tipoConta?: Prisma.SortOrder
  chavePix?: Prisma.SortOrder
  tipoPix?: Prisma.SortOrder
  tipoAssinatura?: Prisma.SortOrder
  metaData?: Prisma.SortOrder
  createdAt?: Prisma.SortOrder
  updatedAt?: Prisma.SortOrder
  deletedAt?: Prisma.SortOrder
}

export type ProfissionalAvgOrderByAggregateInput = {
  id?: Prisma.SortOrder
  usuarioId?: Prisma.SortOrder
}

export type ProfissionalMaxOrderByAggregateInput = {
  id?: Prisma.SortOrder
  uuid?: Prisma.SortOrder
  usuarioId?: Prisma.SortOrder
  rg?: Prisma.SortOrder
  orgaoEmissor?: Prisma.SortOrder
  cnes?: Prisma.SortOrder
  conselhoClasse?: Prisma.SortOrder
  numeroRegistro?: Prisma.SortOrder
  ufConselho?: Prisma.SortOrder
  banco?: Prisma.SortOrder
  agencia?: Prisma.SortOrder
  digitoAgencia?: Prisma.SortOrder
  conta?: Prisma.SortOrder
  digitoConta?: Prisma.SortOrder
  tipoConta?: Prisma.SortOrder
  chavePix?: Prisma.SortOrder
  tipoPix?: Prisma.SortOrder
  tipoAssinatura?: Prisma.SortOrder
  createdAt?: Prisma.SortOrder
  updatedAt?: Prisma.SortOrder
  deletedAt?: Prisma.SortOrder
}

export type ProfissionalMinOrderByAggregateInput = {
  id?: Prisma.SortOrder
  uuid?: Prisma.SortOrder
  usuarioId?: Prisma.SortOrder
  rg?: Prisma.SortOrder
  orgaoEmissor?: Prisma.SortOrder
  cnes?: Prisma.SortOrder
  conselhoClasse?: Prisma.SortOrder
  numeroRegistro?: Prisma.SortOrder
  ufConselho?: Prisma.SortOrder
  banco?: Prisma.SortOrder
  agencia?: Prisma.SortOrder
  digitoAgencia?: Prisma.SortOrder
  conta?: Prisma.SortOrder
  digitoConta?: Prisma.SortOrder
  tipoConta?: Prisma.SortOrder
  chavePix?: Prisma.SortOrder
  tipoPix?: Prisma.SortOrder
  tipoAssinatura?: Prisma.SortOrder
  createdAt?: Prisma.SortOrder
  updatedAt?: Prisma.SortOrder
  deletedAt?: Prisma.SortOrder
}

export type ProfissionalSumOrderByAggregateInput = {
  id?: Prisma.SortOrder
  usuarioId?: Prisma.SortOrder
}

export type ProfissionalScalarRelationFilter = {
  is?: Prisma.ProfissionalWhereInput
  isNot?: Prisma.ProfissionalWhereInput
}

export type ProfissionalCreateNestedOneWithoutUsuarioInput = {
  create?: Prisma.XOR<Prisma.ProfissionalCreateWithoutUsuarioInput, Prisma.ProfissionalUncheckedCreateWithoutUsuarioInput>
  connectOrCreate?: Prisma.ProfissionalCreateOrConnectWithoutUsuarioInput
  connect?: Prisma.ProfissionalWhereUniqueInput
}

export type ProfissionalUncheckedCreateNestedOneWithoutUsuarioInput = {
  create?: Prisma.XOR<Prisma.ProfissionalCreateWithoutUsuarioInput, Prisma.ProfissionalUncheckedCreateWithoutUsuarioInput>
  connectOrCreate?: Prisma.ProfissionalCreateOrConnectWithoutUsuarioInput
  connect?: Prisma.ProfissionalWhereUniqueInput
}

export type ProfissionalUpdateOneWithoutUsuarioNestedInput = {
  create?: Prisma.XOR<Prisma.ProfissionalCreateWithoutUsuarioInput, Prisma.ProfissionalUncheckedCreateWithoutUsuarioInput>
  connectOrCreate?: Prisma.ProfissionalCreateOrConnectWithoutUsuarioInput
  upsert?: Prisma.ProfissionalUpsertWithoutUsuarioInput
  disconnect?: Prisma.ProfissionalWhereInput | boolean
  delete?: Prisma.ProfissionalWhereInput | boolean
  connect?: Prisma.ProfissionalWhereUniqueInput
  update?: Prisma.XOR<Prisma.XOR<Prisma.ProfissionalUpdateToOneWithWhereWithoutUsuarioInput, Prisma.ProfissionalUpdateWithoutUsuarioInput>, Prisma.ProfissionalUncheckedUpdateWithoutUsuarioInput>
}

export type ProfissionalUncheckedUpdateOneWithoutUsuarioNestedInput = {
  create?: Prisma.XOR<Prisma.ProfissionalCreateWithoutUsuarioInput, Prisma.ProfissionalUncheckedCreateWithoutUsuarioInput>
  connectOrCreate?: Prisma.ProfissionalCreateOrConnectWithoutUsuarioInput
  upsert?: Prisma.ProfissionalUpsertWithoutUsuarioInput
  disconnect?: Prisma.ProfissionalWhereInput | boolean
  delete?: Prisma.ProfissionalWhereInput | boolean
  connect?: Prisma.ProfissionalWhereUniqueInput
  update?: Prisma.XOR<Prisma.XOR<Prisma.ProfissionalUpdateToOneWithWhereWithoutUsuarioInput, Prisma.ProfissionalUpdateWithoutUsuarioInput>, Prisma.ProfissionalUncheckedUpdateWithoutUsuarioInput>
}

export type ProfissionalCreateNestedOneWithoutEspecialidadesInput = {
  create?: Prisma.XOR<Prisma.ProfissionalCreateWithoutEspecialidadesInput, Prisma.ProfissionalUncheckedCreateWithoutEspecialidadesInput>
  connectOrCreate?: Prisma.ProfissionalCreateOrConnectWithoutEspecialidadesInput
  connect?: Prisma.ProfissionalWhereUniqueInput
}

export type ProfissionalUpdateOneRequiredWithoutEspecialidadesNestedInput = {
  create?: Prisma.XOR<Prisma.ProfissionalCreateWithoutEspecialidadesInput, Prisma.ProfissionalUncheckedCreateWithoutEspecialidadesInput>
  connectOrCreate?: Prisma.ProfissionalCreateOrConnectWithoutEspecialidadesInput
  upsert?: Prisma.ProfissionalUpsertWithoutEspecialidadesInput
  connect?: Prisma.ProfissionalWhereUniqueInput
  update?: Prisma.XOR<Prisma.XOR<Prisma.ProfissionalUpdateToOneWithWhereWithoutEspecialidadesInput, Prisma.ProfissionalUpdateWithoutEspecialidadesInput>, Prisma.ProfissionalUncheckedUpdateWithoutEspecialidadesInput>
}

export type ProfissionalCreateNestedOneWithoutPlantoesInput = {
  create?: Prisma.XOR<Prisma.ProfissionalCreateWithoutPlantoesInput, Prisma.ProfissionalUncheckedCreateWithoutPlantoesInput>
  connectOrCreate?: Prisma.ProfissionalCreateOrConnectWithoutPlantoesInput
  connect?: Prisma.ProfissionalWhereUniqueInput
}

export type ProfissionalUpdateOneRequiredWithoutPlantoesNestedInput = {
  create?: Prisma.XOR<Prisma.ProfissionalCreateWithoutPlantoesInput, Prisma.ProfissionalUncheckedCreateWithoutPlantoesInput>
  connectOrCreate?: Prisma.ProfissionalCreateOrConnectWithoutPlantoesInput
  upsert?: Prisma.ProfissionalUpsertWithoutPlantoesInput
  connect?: Prisma.ProfissionalWhereUniqueInput
  update?: Prisma.XOR<Prisma.XOR<Prisma.ProfissionalUpdateToOneWithWhereWithoutPlantoesInput, Prisma.ProfissionalUpdateWithoutPlantoesInput>, Prisma.ProfissionalUncheckedUpdateWithoutPlantoesInput>
}

export type ProfissionalCreateNestedOneWithoutFechamentosInput = {
  create?: Prisma.XOR<Prisma.ProfissionalCreateWithoutFechamentosInput, Prisma.ProfissionalUncheckedCreateWithoutFechamentosInput>
  connectOrCreate?: Prisma.ProfissionalCreateOrConnectWithoutFechamentosInput
  connect?: Prisma.ProfissionalWhereUniqueInput
}

export type ProfissionalUpdateOneRequiredWithoutFechamentosNestedInput = {
  create?: Prisma.XOR<Prisma.ProfissionalCreateWithoutFechamentosInput, Prisma.ProfissionalUncheckedCreateWithoutFechamentosInput>
  connectOrCreate?: Prisma.ProfissionalCreateOrConnectWithoutFechamentosInput
  upsert?: Prisma.ProfissionalUpsertWithoutFechamentosInput
  connect?: Prisma.ProfissionalWhereUniqueInput
  update?: Prisma.XOR<Prisma.XOR<Prisma.ProfissionalUpdateToOneWithWhereWithoutFechamentosInput, Prisma.ProfissionalUpdateWithoutFechamentosInput>, Prisma.ProfissionalUncheckedUpdateWithoutFechamentosInput>
}

export type ProfissionalCreateNestedOneWithoutAntecipacoesInput = {
  create?: Prisma.XOR<Prisma.ProfissionalCreateWithoutAntecipacoesInput, Prisma.ProfissionalUncheckedCreateWithoutAntecipacoesInput>
  connectOrCreate?: Prisma.ProfissionalCreateOrConnectWithoutAntecipacoesInput
  connect?: Prisma.ProfissionalWhereUniqueInput
}

export type ProfissionalUpdateOneRequiredWithoutAntecipacoesNestedInput = {
  create?: Prisma.XOR<Prisma.ProfissionalCreateWithoutAntecipacoesInput, Prisma.ProfissionalUncheckedCreateWithoutAntecipacoesInput>
  connectOrCreate?: Prisma.ProfissionalCreateOrConnectWithoutAntecipacoesInput
  upsert?: Prisma.ProfissionalUpsertWithoutAntecipacoesInput
  connect?: Prisma.ProfissionalWhereUniqueInput
  update?: Prisma.XOR<Prisma.XOR<Prisma.ProfissionalUpdateToOneWithWhereWithoutAntecipacoesInput, Prisma.ProfissionalUpdateWithoutAntecipacoesInput>, Prisma.ProfissionalUncheckedUpdateWithoutAntecipacoesInput>
}

export type ProfissionalCreateWithoutUsuarioInput = {
  uuid?: string
  rg?: string | null
  orgaoEmissor?: string | null
  cnes?: string | null
  conselhoClasse?: string | null
  numeroRegistro?: string | null
  ufConselho?: string | null
  banco?: string | null
  agencia?: string | null
  digitoAgencia?: string | null
  conta?: string | null
  digitoConta?: string | null
  tipoConta?: string | null
  chavePix?: string | null
  tipoPix?: string | null
  tipoAssinatura?: string | null
  metaData?: Prisma.NullableJsonNullValueInput | runtime.InputJsonValue
  createdAt?: Date | string
  updatedAt?: Date | string
  deletedAt?: Date | string | null
  especialidades?: Prisma.ProfissionalEspecialidadeCreateNestedManyWithoutProfissionalInput
  plantoes?: Prisma.PlantaoCreateNestedManyWithoutProfissionalInput
  fechamentos?: Prisma.FechamentoCreateNestedManyWithoutProfissionalInput
  antecipacoes?: Prisma.AntecipacaoCreateNestedManyWithoutProfissionalInput
}

export type ProfissionalUncheckedCreateWithoutUsuarioInput = {
  id?: number
  uuid?: string
  rg?: string | null
  orgaoEmissor?: string | null
  cnes?: string | null
  conselhoClasse?: string | null
  numeroRegistro?: string | null
  ufConselho?: string | null
  banco?: string | null
  agencia?: string | null
  digitoAgencia?: string | null
  conta?: string | null
  digitoConta?: string | null
  tipoConta?: string | null
  chavePix?: string | null
  tipoPix?: string | null
  tipoAssinatura?: string | null
  metaData?: Prisma.NullableJsonNullValueInput | runtime.InputJsonValue
  createdAt?: Date | string
  updatedAt?: Date | string
  deletedAt?: Date | string | null
  especialidades?: Prisma.ProfissionalEspecialidadeUncheckedCreateNestedManyWithoutProfissionalInput
  plantoes?: Prisma.PlantaoUncheckedCreateNestedManyWithoutProfissionalInput
  fechamentos?: Prisma.FechamentoUncheckedCreateNestedManyWithoutProfissionalInput
  antecipacoes?: Prisma.AntecipacaoUncheckedCreateNestedManyWithoutProfissionalInput
}

export type ProfissionalCreateOrConnectWithoutUsuarioInput = {
  where: Prisma.ProfissionalWhereUniqueInput
  create: Prisma.XOR<Prisma.ProfissionalCreateWithoutUsuarioInput, Prisma.ProfissionalUncheckedCreateWithoutUsuarioInput>
}

export type ProfissionalUpsertWithoutUsuarioInput = {
  update: Prisma.XOR<Prisma.ProfissionalUpdateWithoutUsuarioInput, Prisma.ProfissionalUncheckedUpdateWithoutUsuarioInput>
  create: Prisma.XOR<Prisma.ProfissionalCreateWithoutUsuarioInput, Prisma.ProfissionalUncheckedCreateWithoutUsuarioInput>
  where?: Prisma.ProfissionalWhereInput
}

export type ProfissionalUpdateToOneWithWhereWithoutUsuarioInput = {
  where?: Prisma.ProfissionalWhereInput
  data: Prisma.XOR<Prisma.ProfissionalUpdateWithoutUsuarioInput, Prisma.ProfissionalUncheckedUpdateWithoutUsuarioInput>
}

export type ProfissionalUpdateWithoutUsuarioInput = {
  uuid?: Prisma.StringFieldUpdateOperationsInput | string
  rg?: Prisma.NullableStringFieldUpdateOperationsInput | string | null
  orgaoEmissor?: Prisma.NullableStringFieldUpdateOperationsInput | string | null
  cnes?: Prisma.NullableStringFieldUpdateOperationsInput | string | null
  conselhoClasse?: Prisma.NullableStringFieldUpdateOperationsInput | string | null
  numeroRegistro?: Prisma.NullableStringFieldUpdateOperationsInput | string | null
  ufConselho?: Prisma.NullableStringFieldUpdateOperationsInput | string | null
  banco?: Prisma.NullableStringFieldUpdateOperationsInput | string | null
  agencia?: Prisma.NullableStringFieldUpdateOperationsInput | string | null
  digitoAgencia?: Prisma.NullableStringFieldUpdateOperationsInput | string | null
  conta?: Prisma.NullableStringFieldUpdateOperationsInput | string | null
  digitoConta?: Prisma.NullableStringFieldUpdateOperationsInput | string | null
  tipoConta?: Prisma.NullableStringFieldUpdateOperationsInput | string | null
  chavePix?: Prisma.NullableStringFieldUpdateOperationsInput | string | null
  tipoPix?: Prisma.NullableStringFieldUpdateOperationsInput | string | null
  tipoAssinatura?: Prisma.NullableStringFieldUpdateOperationsInput | string | null
  metaData?: Prisma.NullableJsonNullValueInput | runtime.InputJsonValue
  createdAt?: Prisma.DateTimeFieldUpdateOperationsInput | Date | string
  updatedAt?: Prisma.DateTimeFieldUpdateOperationsInput | Date | string
  deletedAt?: Prisma.NullableDateTimeFieldUpdateOperationsInput | Date | string | null
  especialidades?: Prisma.ProfissionalEspecialidadeUpdateManyWithoutProfissionalNestedInput
  plantoes?: Prisma.PlantaoUpdateManyWithoutProfissionalNestedInput
  fechamentos?: Prisma.FechamentoUpdateManyWithoutProfissionalNestedInput
  antecipacoes?: Prisma.AntecipacaoUpdateManyWithoutProfissionalNestedInput
}

export type ProfissionalUncheckedUpdateWithoutUsuarioInput = {
  id?: Prisma.IntFieldUpdateOperationsInput | number
  uuid?: Prisma.StringFieldUpdateOperationsInput | string
  rg?: Prisma.NullableStringFieldUpdateOperationsInput | string | null
  orgaoEmissor?: Prisma.NullableStringFieldUpdateOperationsInput | string | null
  cnes?: Prisma.NullableStringFieldUpdateOperationsInput | string | null
  conselhoClasse?: Prisma.NullableStringFieldUpdateOperationsInput | string | null
  numeroRegistro?: Prisma.NullableStringFieldUpdateOperationsInput | string | null
  ufConselho?: Prisma.NullableStringFieldUpdateOperationsInput | string | null
  banco?: Prisma.NullableStringFieldUpdateOperationsInput | string | null
  agencia?: Prisma.NullableStringFieldUpdateOperationsInput | string | null
  digitoAgencia?: Prisma.NullableStringFieldUpdateOperationsInput | string | null
  conta?: Prisma.NullableStringFieldUpdateOperationsInput | string | null
  digitoConta?: Prisma.NullableStringFieldUpdateOperationsInput | string | null
  tipoConta?: Prisma.NullableStringFieldUpdateOperationsInput | string | null
  chavePix?: Prisma.NullableStringFieldUpdateOperationsInput | string | null
  tipoPix?: Prisma.NullableStringFieldUpdateOperationsInput | string | null
  tipoAssinatura?: Prisma.NullableStringFieldUpdateOperationsInput | string | null
  metaData?: Prisma.NullableJsonNullValueInput | runtime.InputJsonValue
  createdAt?: Prisma.DateTimeFieldUpdateOperationsInput | Date | string
  updatedAt?: Prisma.DateTimeFieldUpdateOperationsInput | Date | string
  deletedAt?: Prisma.NullableDateTimeFieldUpdateOperationsInput | Date | string | null
  especialidades?: Prisma.ProfissionalEspecialidadeUncheckedUpdateManyWithoutProfissionalNestedInput
  plantoes?: Prisma.PlantaoUncheckedUpdateManyWithoutProfissionalNestedInput
  fechamentos?: Prisma.FechamentoUncheckedUpdateManyWithoutProfissionalNestedInput
  antecipacoes?: Prisma.AntecipacaoUncheckedUpdateManyWithoutProfissionalNestedInput
}

export type ProfissionalCreateWithoutEspecialidadesInput = {
  uuid?: string
  rg?: string | null
  orgaoEmissor?: string | null
  cnes?: string | null
  conselhoClasse?: string | null
  numeroRegistro?: string | null
  ufConselho?: string | null
  banco?: string | null
  agencia?: string | null
  digitoAgencia?: string | null
  conta?: string | null
  digitoConta?: string | null
  tipoConta?: string | null
  chavePix?: string | null
  tipoPix?: string | null
  tipoAssinatura?: string | null
  metaData?: Prisma.NullableJsonNullValueInput | runtime.InputJsonValue
  createdAt?: Date | string
  updatedAt?: Date | string
  deletedAt?: Date | string | null
  usuario: Prisma.UsuarioCreateNestedOneWithoutProfissionalInput
  plantoes?: Prisma.PlantaoCreateNestedManyWithoutProfissionalInput
  fechamentos?: Prisma.FechamentoCreateNestedManyWithoutProfissionalInput
  antecipacoes?: Prisma.AntecipacaoCreateNestedManyWithoutProfissionalInput
}

export type ProfissionalUncheckedCreateWithoutEspecialidadesInput = {
  id?: number
  uuid?: string
  usuarioId: number
  rg?: string | null
  orgaoEmissor?: string | null
  cnes?: string | null
  conselhoClasse?: string | null
  numeroRegistro?: string | null
  ufConselho?: string | null
  banco?: string | null
  agencia?: string | null
  digitoAgencia?: string | null
  conta?: string | null
  digitoConta?: string | null
  tipoConta?: string | null
  chavePix?: string | null
  tipoPix?: string | null
  tipoAssinatura?: string | null
  metaData?: Prisma.NullableJsonNullValueInput | runtime.InputJsonValue
  createdAt?: Date | string
  updatedAt?: Date | string
  deletedAt?: Date | string | null
  plantoes?: Prisma.PlantaoUncheckedCreateNestedManyWithoutProfissionalInput
  fechamentos?: Prisma.FechamentoUncheckedCreateNestedManyWithoutProfissionalInput
  antecipacoes?: Prisma.AntecipacaoUncheckedCreateNestedManyWithoutProfissionalInput
}

export type ProfissionalCreateOrConnectWithoutEspecialidadesInput = {
  where: Prisma.ProfissionalWhereUniqueInput
  create: Prisma.XOR<Prisma.ProfissionalCreateWithoutEspecialidadesInput, Prisma.ProfissionalUncheckedCreateWithoutEspecialidadesInput>
}

export type ProfissionalUpsertWithoutEspecialidadesInput = {
  update: Prisma.XOR<Prisma.ProfissionalUpdateWithoutEspecialidadesInput, Prisma.ProfissionalUncheckedUpdateWithoutEspecialidadesInput>
  create: Prisma.XOR<Prisma.ProfissionalCreateWithoutEspecialidadesInput, Prisma.ProfissionalUncheckedCreateWithoutEspecialidadesInput>
  where?: Prisma.ProfissionalWhereInput
}

export type ProfissionalUpdateToOneWithWhereWithoutEspecialidadesInput = {
  where?: Prisma.ProfissionalWhereInput
  data: Prisma.XOR<Prisma.ProfissionalUpdateWithoutEspecialidadesInput, Prisma.ProfissionalUncheckedUpdateWithoutEspecialidadesInput>
}

export type ProfissionalUpdateWithoutEspecialidadesInput = {
  uuid?: Prisma.StringFieldUpdateOperationsInput | string
  rg?: Prisma.NullableStringFieldUpdateOperationsInput | string | null
  orgaoEmissor?: Prisma.NullableStringFieldUpdateOperationsInput | string | null
  cnes?: Prisma.NullableStringFieldUpdateOperationsInput | string | null
  conselhoClasse?: Prisma.NullableStringFieldUpdateOperationsInput | string | null
  numeroRegistro?: Prisma.NullableStringFieldUpdateOperationsInput | string | null
  ufConselho?: Prisma.NullableStringFieldUpdateOperationsInput | string | null
  banco?: Prisma.NullableStringFieldUpdateOperationsInput | string | null
  agencia?: Prisma.NullableStringFieldUpdateOperationsInput | string | null
  digitoAgencia?: Prisma.NullableStringFieldUpdateOperationsInput | string | null
  conta?: Prisma.NullableStringFieldUpdateOperationsInput | string | null
  digitoConta?: Prisma.NullableStringFieldUpdateOperationsInput | string | null
  tipoConta?: Prisma.NullableStringFieldUpdateOperationsInput | string | null
  chavePix?: Prisma.NullableStringFieldUpdateOperationsInput | string | null
  tipoPix?: Prisma.NullableStringFieldUpdateOperationsInput | string | null
  tipoAssinatura?: Prisma.NullableStringFieldUpdateOperationsInput | string | null
  metaData?: Prisma.NullableJsonNullValueInput | runtime.InputJsonValue
  createdAt?: Prisma.DateTimeFieldUpdateOperationsInput | Date | string
  updatedAt?: Prisma.DateTimeFieldUpdateOperationsInput | Date | string
  deletedAt?: Prisma.NullableDateTimeFieldUpdateOperationsInput | Date | string | null
  usuario?: Prisma.UsuarioUpdateOneRequiredWithoutProfissionalNestedInput
  plantoes?: Prisma.PlantaoUpdateManyWithoutProfissionalNestedInput
  fechamentos?: Prisma.FechamentoUpdateManyWithoutProfissionalNestedInput
  antecipacoes?: Prisma.AntecipacaoUpdateManyWithoutProfissionalNestedInput
}

export type ProfissionalUncheckedUpdateWithoutEspecialidadesInput = {
  id?: Prisma.IntFieldUpdateOperationsInput | number
  uuid?: Prisma.StringFieldUpdateOperationsInput | string
  usuarioId?: Prisma.IntFieldUpdateOperationsInput | number
  rg?: Prisma.NullableStringFieldUpdateOperationsInput | string | null
  orgaoEmissor?: Prisma.NullableStringFieldUpdateOperationsInput | string | null
  cnes?: Prisma.NullableStringFieldUpdateOperationsInput | string | null
  conselhoClasse?: Prisma.NullableStringFieldUpdateOperationsInput | string | null
  numeroRegistro?: Prisma.NullableStringFieldUpdateOperationsInput | string | null
  ufConselho?: Prisma.NullableStringFieldUpdateOperationsInput | string | null
  banco?: Prisma.NullableStringFieldUpdateOperationsInput | string | null
  agencia?: Prisma.NullableStringFieldUpdateOperationsInput | string | null
  digitoAgencia?: Prisma.NullableStringFieldUpdateOperationsInput | string | null
  conta?: Prisma.NullableStringFieldUpdateOperationsInput | string | null
  digitoConta?: Prisma.NullableStringFieldUpdateOperationsInput | string | null
  tipoConta?: Prisma.NullableStringFieldUpdateOperationsInput | string | null
  chavePix?: Prisma.NullableStringFieldUpdateOperationsInput | string | null
  tipoPix?: Prisma.NullableStringFieldUpdateOperationsInput | string | null
  tipoAssinatura?: Prisma.NullableStringFieldUpdateOperationsInput | string | null
  metaData?: Prisma.NullableJsonNullValueInput | runtime.InputJsonValue
  createdAt?: Prisma.DateTimeFieldUpdateOperationsInput | Date | string
  updatedAt?: Prisma.DateTimeFieldUpdateOperationsInput | Date | string
  deletedAt?: Prisma.NullableDateTimeFieldUpdateOperationsInput | Date | string | null
  plantoes?: Prisma.PlantaoUncheckedUpdateManyWithoutProfissionalNestedInput
  fechamentos?: Prisma.FechamentoUncheckedUpdateManyWithoutProfissionalNestedInput
  antecipacoes?: Prisma.AntecipacaoUncheckedUpdateManyWithoutProfissionalNestedInput
}

export type ProfissionalCreateWithoutPlantoesInput = {
  uuid?: string
  rg?: string | null
  orgaoEmissor?: string | null
  cnes?: string | null
  conselhoClasse?: string | null
  numeroRegistro?: string | null
  ufConselho?: string | null
  banco?: string | null
  agencia?: string | null
  digitoAgencia?: string | null
  conta?: string | null
  digitoConta?: string | null
  tipoConta?: string | null
  chavePix?: string | null
  tipoPix?: string | null
  tipoAssinatura?: string | null
  metaData?: Prisma.NullableJsonNullValueInput | runtime.InputJsonValue
  createdAt?: Date | string
  updatedAt?: Date | string
  deletedAt?: Date | string | null
  usuario: Prisma.UsuarioCreateNestedOneWithoutProfissionalInput
  especialidades?: Prisma.ProfissionalEspecialidadeCreateNestedManyWithoutProfissionalInput
  fechamentos?: Prisma.FechamentoCreateNestedManyWithoutProfissionalInput
  antecipacoes?: Prisma.AntecipacaoCreateNestedManyWithoutProfissionalInput
}

export type ProfissionalUncheckedCreateWithoutPlantoesInput = {
  id?: number
  uuid?: string
  usuarioId: number
  rg?: string | null
  orgaoEmissor?: string | null
  cnes?: string | null
  conselhoClasse?: string | null
  numeroRegistro?: string | null
  ufConselho?: string | null
  banco?: string | null
  agencia?: string | null
  digitoAgencia?: string | null
  conta?: string | null
  digitoConta?: string | null
  tipoConta?: string | null
  chavePix?: string | null
  tipoPix?: string | null
  tipoAssinatura?: string | null
  metaData?: Prisma.NullableJsonNullValueInput | runtime.InputJsonValue
  createdAt?: Date | string
  updatedAt?: Date | string
  deletedAt?: Date | string | null
  especialidades?: Prisma.ProfissionalEspecialidadeUncheckedCreateNestedManyWithoutProfissionalInput
  fechamentos?: Prisma.FechamentoUncheckedCreateNestedManyWithoutProfissionalInput
  antecipacoes?: Prisma.AntecipacaoUncheckedCreateNestedManyWithoutProfissionalInput
}

export type ProfissionalCreateOrConnectWithoutPlantoesInput = {
  where: Prisma.ProfissionalWhereUniqueInput
  create: Prisma.XOR<Prisma.ProfissionalCreateWithoutPlantoesInput, Prisma.ProfissionalUncheckedCreateWithoutPlantoesInput>
}

export type ProfissionalUpsertWithoutPlantoesInput = {
  update: Prisma.XOR<Prisma.ProfissionalUpdateWithoutPlantoesInput, Prisma.ProfissionalUncheckedUpdateWithoutPlantoesInput>
  create: Prisma.XOR<Prisma.ProfissionalCreateWithoutPlantoesInput, Prisma.ProfissionalUncheckedCreateWithoutPlantoesInput>
  where?: Prisma.ProfissionalWhereInput
}

export type ProfissionalUpdateToOneWithWhereWithoutPlantoesInput = {
  where?: Prisma.ProfissionalWhereInput
  data: Prisma.XOR<Prisma.ProfissionalUpdateWithoutPlantoesInput, Prisma.ProfissionalUncheckedUpdateWithoutPlantoesInput>
}

export type ProfissionalUpdateWithoutPlantoesInput = {
  uuid?: Prisma.StringFieldUpdateOperationsInput | string
  rg?: Prisma.NullableStringFieldUpdateOperationsInput | string | null
  orgaoEmissor?: Prisma.NullableStringFieldUpdateOperationsInput | string | null
  cnes?: Prisma.NullableStringFieldUpdateOperationsInput | string | null
  conselhoClasse?: Prisma.NullableStringFieldUpdateOperationsInput | string | null
  numeroRegistro?: Prisma.NullableStringFieldUpdateOperationsInput | string | null
  ufConselho?: Prisma.NullableStringFieldUpdateOperationsInput | string | null
  banco?: Prisma.NullableStringFieldUpdateOperationsInput | string | null
  agencia?: Prisma.NullableStringFieldUpdateOperationsInput | string | null
  digitoAgencia?: Prisma.NullableStringFieldUpdateOperationsInput | string | null
  conta?: Prisma.NullableStringFieldUpdateOperationsInput | string | null
  digitoConta?: Prisma.NullableStringFieldUpdateOperationsInput | string | null
  tipoConta?: Prisma.NullableStringFieldUpdateOperationsInput | string | null
  chavePix?: Prisma.NullableStringFieldUpdateOperationsInput | string | null
  tipoPix?: Prisma.NullableStringFieldUpdateOperationsInput | string | null
  tipoAssinatura?: Prisma.NullableStringFieldUpdateOperationsInput | string | null
  metaData?: Prisma.NullableJsonNullValueInput | runtime.InputJsonValue
  createdAt?: Prisma.DateTimeFieldUpdateOperationsInput | Date | string
  updatedAt?: Prisma.DateTimeFieldUpdateOperationsInput | Date | string
  deletedAt?: Prisma.NullableDateTimeFieldUpdateOperationsInput | Date | string | null
  usuario?: Prisma.UsuarioUpdateOneRequiredWithoutProfissionalNestedInput
  especialidades?: Prisma.ProfissionalEspecialidadeUpdateManyWithoutProfissionalNestedInput
  fechamentos?: Prisma.FechamentoUpdateManyWithoutProfissionalNestedInput
  antecipacoes?: Prisma.AntecipacaoUpdateManyWithoutProfissionalNestedInput
}

export type ProfissionalUncheckedUpdateWithoutPlantoesInput = {
  id?: Prisma.IntFieldUpdateOperationsInput | number
  uuid?: Prisma.StringFieldUpdateOperationsInput | string
  usuarioId?: Prisma.IntFieldUpdateOperationsInput | number
  rg?: Prisma.NullableStringFieldUpdateOperationsInput | string | null
  orgaoEmissor?: Prisma.NullableStringFieldUpdateOperationsInput | string | null
  cnes?: Prisma.NullableStringFieldUpdateOperationsInput | string | null
  conselhoClasse?: Prisma.NullableStringFieldUpdateOperationsInput | string | null
  numeroRegistro?: Prisma.NullableStringFieldUpdateOperationsInput | string | null
  ufConselho?: Prisma.NullableStringFieldUpdateOperationsInput | string | null
  banco?: Prisma.NullableStringFieldUpdateOperationsInput | string | null
  agencia?: Prisma.NullableStringFieldUpdateOperationsInput | string | null
  digitoAgencia?: Prisma.NullableStringFieldUpdateOperationsInput | string | null
  conta?: Prisma.NullableStringFieldUpdateOperationsInput | string | null
  digitoConta?: Prisma.NullableStringFieldUpdateOperationsInput | string | null
  tipoConta?: Prisma.NullableStringFieldUpdateOperationsInput | string | null
  chavePix?: Prisma.NullableStringFieldUpdateOperationsInput | string | null
  tipoPix?: Prisma.NullableStringFieldUpdateOperationsInput | string | null
  tipoAssinatura?: Prisma.NullableStringFieldUpdateOperationsInput | string | null
  metaData?: Prisma.NullableJsonNullValueInput | runtime.InputJsonValue
  createdAt?: Prisma.DateTimeFieldUpdateOperationsInput | Date | string
  updatedAt?: Prisma.DateTimeFieldUpdateOperationsInput | Date | string
  deletedAt?: Prisma.NullableDateTimeFieldUpdateOperationsInput | Date | string | null
  especialidades?: Prisma.ProfissionalEspecialidadeUncheckedUpdateManyWithoutProfissionalNestedInput
  fechamentos?: Prisma.FechamentoUncheckedUpdateManyWithoutProfissionalNestedInput
  antecipacoes?: Prisma.AntecipacaoUncheckedUpdateManyWithoutProfissionalNestedInput
}

export type ProfissionalCreateWithoutFechamentosInput = {
  uuid?: string
  rg?: string | null
  orgaoEmissor?: string | null
  cnes?: string | null
  conselhoClasse?: string | null
  numeroRegistro?: string | null
  ufConselho?: string | null
  banco?: string | null
  agencia?: string | null
  digitoAgencia?: string | null
  conta?: string | null
  digitoConta?: string | null
  tipoConta?: string | null
  chavePix?: string | null
  tipoPix?: string | null
  tipoAssinatura?: string | null
  metaData?: Prisma.NullableJsonNullValueInput | runtime.InputJsonValue
  createdAt?: Date | string
  updatedAt?: Date | string
  deletedAt?: Date | string | null
  usuario: Prisma.UsuarioCreateNestedOneWithoutProfissionalInput
  especialidades?: Prisma.ProfissionalEspecialidadeCreateNestedManyWithoutProfissionalInput
  plantoes?: Prisma.PlantaoCreateNestedManyWithoutProfissionalInput
  antecipacoes?: Prisma.AntecipacaoCreateNestedManyWithoutProfissionalInput
}

export type ProfissionalUncheckedCreateWithoutFechamentosInput = {
  id?: number
  uuid?: string
  usuarioId: number
  rg?: string | null
  orgaoEmissor?: string | null
  cnes?: string | null
  conselhoClasse?: string | null
  numeroRegistro?: string | null
  ufConselho?: string | null
  banco?: string | null
  agencia?: string | null
  digitoAgencia?: string | null
  conta?: string | null
  digitoConta?: string | null
  tipoConta?: string | null
  chavePix?: string | null
  tipoPix?: string | null
  tipoAssinatura?: string | null
  metaData?: Prisma.NullableJsonNullValueInput | runtime.InputJsonValue
  createdAt?: Date | string
  updatedAt?: Date | string
  deletedAt?: Date | string | null
  especialidades?: Prisma.ProfissionalEspecialidadeUncheckedCreateNestedManyWithoutProfissionalInput
  plantoes?: Prisma.PlantaoUncheckedCreateNestedManyWithoutProfissionalInput
  antecipacoes?: Prisma.AntecipacaoUncheckedCreateNestedManyWithoutProfissionalInput
}

export type ProfissionalCreateOrConnectWithoutFechamentosInput = {
  where: Prisma.ProfissionalWhereUniqueInput
  create: Prisma.XOR<Prisma.ProfissionalCreateWithoutFechamentosInput, Prisma.ProfissionalUncheckedCreateWithoutFechamentosInput>
}

export type ProfissionalUpsertWithoutFechamentosInput = {
  update: Prisma.XOR<Prisma.ProfissionalUpdateWithoutFechamentosInput, Prisma.ProfissionalUncheckedUpdateWithoutFechamentosInput>
  create: Prisma.XOR<Prisma.ProfissionalCreateWithoutFechamentosInput, Prisma.ProfissionalUncheckedCreateWithoutFechamentosInput>
  where?: Prisma.ProfissionalWhereInput
}

export type ProfissionalUpdateToOneWithWhereWithoutFechamentosInput = {
  where?: Prisma.ProfissionalWhereInput
  data: Prisma.XOR<Prisma.ProfissionalUpdateWithoutFechamentosInput, Prisma.ProfissionalUncheckedUpdateWithoutFechamentosInput>
}

export type ProfissionalUpdateWithoutFechamentosInput = {
  uuid?: Prisma.StringFieldUpdateOperationsInput | string
  rg?: Prisma.NullableStringFieldUpdateOperationsInput | string | null
  orgaoEmissor?: Prisma.NullableStringFieldUpdateOperationsInput | string | null
  cnes?: Prisma.NullableStringFieldUpdateOperationsInput | string | null
  conselhoClasse?: Prisma.NullableStringFieldUpdateOperationsInput | string | null
  numeroRegistro?: Prisma.NullableStringFieldUpdateOperationsInput | string | null
  ufConselho?: Prisma.NullableStringFieldUpdateOperationsInput | string | null
  banco?: Prisma.NullableStringFieldUpdateOperationsInput | string | null
  agencia?: Prisma.NullableStringFieldUpdateOperationsInput | string | null
  digitoAgencia?: Prisma.NullableStringFieldUpdateOperationsInput | string | null
  conta?: Prisma.NullableStringFieldUpdateOperationsInput | string | null
  digitoConta?: Prisma.NullableStringFieldUpdateOperationsInput | string | null
  tipoConta?: Prisma.NullableStringFieldUpdateOperationsInput | string | null
  chavePix?: Prisma.NullableStringFieldUpdateOperationsInput | string | null
  tipoPix?: Prisma.NullableStringFieldUpdateOperationsInput | string | null
  tipoAssinatura?: Prisma.NullableStringFieldUpdateOperationsInput | string | null
  metaData?: Prisma.NullableJsonNullValueInput | runtime.InputJsonValue
  createdAt?: Prisma.DateTimeFieldUpdateOperationsInput | Date | string
  updatedAt?: Prisma.DateTimeFieldUpdateOperationsInput | Date | string
  deletedAt?: Prisma.NullableDateTimeFieldUpdateOperationsInput | Date | string | null
  usuario?: Prisma.UsuarioUpdateOneRequiredWithoutProfissionalNestedInput
  especialidades?: Prisma.ProfissionalEspecialidadeUpdateManyWithoutProfissionalNestedInput
  plantoes?: Prisma.PlantaoUpdateManyWithoutProfissionalNestedInput
  antecipacoes?: Prisma.AntecipacaoUpdateManyWithoutProfissionalNestedInput
}

export type ProfissionalUncheckedUpdateWithoutFechamentosInput = {
  id?: Prisma.IntFieldUpdateOperationsInput | number
  uuid?: Prisma.StringFieldUpdateOperationsInput | string
  usuarioId?: Prisma.IntFieldUpdateOperationsInput | number
  rg?: Prisma.NullableStringFieldUpdateOperationsInput | string | null
  orgaoEmissor?: Prisma.NullableStringFieldUpdateOperationsInput | string | null
  cnes?: Prisma.NullableStringFieldUpdateOperationsInput | string | null
  conselhoClasse?: Prisma.NullableStringFieldUpdateOperationsInput | string | null
  numeroRegistro?: Prisma.NullableStringFieldUpdateOperationsInput | string | null
  ufConselho?: Prisma.NullableStringFieldUpdateOperationsInput | string | null
  banco?: Prisma.NullableStringFieldUpdateOperationsInput | string | null
  agencia?: Prisma.NullableStringFieldUpdateOperationsInput | string | null
  digitoAgencia?: Prisma.NullableStringFieldUpdateOperationsInput | string | null
  conta?: Prisma.NullableStringFieldUpdateOperationsInput | string | null
  digitoConta?: Prisma.NullableStringFieldUpdateOperationsInput | string | null
  tipoConta?: Prisma.NullableStringFieldUpdateOperationsInput | string | null
  chavePix?: Prisma.NullableStringFieldUpdateOperationsInput | string | null
  tipoPix?: Prisma.NullableStringFieldUpdateOperationsInput | string | null
  tipoAssinatura?: Prisma.NullableStringFieldUpdateOperationsInput | string | null
  metaData?: Prisma.NullableJsonNullValueInput | runtime.InputJsonValue
  createdAt?: Prisma.DateTimeFieldUpdateOperationsInput | Date | string
  updatedAt?: Prisma.DateTimeFieldUpdateOperationsInput | Date | string
  deletedAt?: Prisma.NullableDateTimeFieldUpdateOperationsInput | Date | string | null
  especialidades?: Prisma.ProfissionalEspecialidadeUncheckedUpdateManyWithoutProfissionalNestedInput
  plantoes?: Prisma.PlantaoUncheckedUpdateManyWithoutProfissionalNestedInput
  antecipacoes?: Prisma.AntecipacaoUncheckedUpdateManyWithoutProfissionalNestedInput
}

export type ProfissionalCreateWithoutAntecipacoesInput = {
  uuid?: string
  rg?: string | null
  orgaoEmissor?: string | null
  cnes?: string | null
  conselhoClasse?: string | null
  numeroRegistro?: string | null
  ufConselho?: string | null
  banco?: string | null
  agencia?: string | null
  digitoAgencia?: string | null
  conta?: string | null
  digitoConta?: string | null
  tipoConta?: string | null
  chavePix?: string | null
  tipoPix?: string | null
  tipoAssinatura?: string | null
  metaData?: Prisma.NullableJsonNullValueInput | runtime.InputJsonValue
  createdAt?: Date | string
  updatedAt?: Date | string
  deletedAt?: Date | string | null
  usuario: Prisma.UsuarioCreateNestedOneWithoutProfissionalInput
  especialidades?: Prisma.ProfissionalEspecialidadeCreateNestedManyWithoutProfissionalInput
  plantoes?: Prisma.PlantaoCreateNestedManyWithoutProfissionalInput
  fechamentos?: Prisma.FechamentoCreateNestedManyWithoutProfissionalInput
}

export type ProfissionalUncheckedCreateWithoutAntecipacoesInput = {
  id?: number
  uuid?: string
  usuarioId: number
  rg?: string | null
  orgaoEmissor?: string | null
  cnes?: string | null
  conselhoClasse?: string | null
  numeroRegistro?: string | null
  ufConselho?: string | null
  banco?: string | null
  agencia?: string | null
  digitoAgencia?: string | null
  conta?: string | null
  digitoConta?: string | null
  tipoConta?: string | null
  chavePix?: string | null
  tipoPix?: string | null
  tipoAssinatura?: string | null
  metaData?: Prisma.NullableJsonNullValueInput | runtime.InputJsonValue
  createdAt?: Date | string
  updatedAt?: Date | string
  deletedAt?: Date | string | null
  especialidades?: Prisma.ProfissionalEspecialidadeUncheckedCreateNestedManyWithoutProfissionalInput
  plantoes?: Prisma.PlantaoUncheckedCreateNestedManyWithoutProfissionalInput
  fechamentos?: Prisma.FechamentoUncheckedCreateNestedManyWithoutProfissionalInput
}

export type ProfissionalCreateOrConnectWithoutAntecipacoesInput = {
  where: Prisma.ProfissionalWhereUniqueInput
  create: Prisma.XOR<Prisma.ProfissionalCreateWithoutAntecipacoesInput, Prisma.ProfissionalUncheckedCreateWithoutAntecipacoesInput>
}

export type ProfissionalUpsertWithoutAntecipacoesInput = {
  update: Prisma.XOR<Prisma.ProfissionalUpdateWithoutAntecipacoesInput, Prisma.ProfissionalUncheckedUpdateWithoutAntecipacoesInput>
  create: Prisma.XOR<Prisma.ProfissionalCreateWithoutAntecipacoesInput, Prisma.ProfissionalUncheckedCreateWithoutAntecipacoesInput>
  where?: Prisma.ProfissionalWhereInput
}

export type ProfissionalUpdateToOneWithWhereWithoutAntecipacoesInput = {
  where?: Prisma.ProfissionalWhereInput
  data: Prisma.XOR<Prisma.ProfissionalUpdateWithoutAntecipacoesInput, Prisma.ProfissionalUncheckedUpdateWithoutAntecipacoesInput>
}

export type ProfissionalUpdateWithoutAntecipacoesInput = {
  uuid?: Prisma.StringFieldUpdateOperationsInput | string
  rg?: Prisma.NullableStringFieldUpdateOperationsInput | string | null
  orgaoEmissor?: Prisma.NullableStringFieldUpdateOperationsInput | string | null
  cnes?: Prisma.NullableStringFieldUpdateOperationsInput | string | null
  conselhoClasse?: Prisma.NullableStringFieldUpdateOperationsInput | string | null
  numeroRegistro?: Prisma.NullableStringFieldUpdateOperationsInput | string | null
  ufConselho?: Prisma.NullableStringFieldUpdateOperationsInput | string | null
  banco?: Prisma.NullableStringFieldUpdateOperationsInput | string | null
  agencia?: Prisma.NullableStringFieldUpdateOperationsInput | string | null
  digitoAgencia?: Prisma.NullableStringFieldUpdateOperationsInput | string | null
  conta?: Prisma.NullableStringFieldUpdateOperationsInput | string | null
  digitoConta?: Prisma.NullableStringFieldUpdateOperationsInput | string | null
  tipoConta?: Prisma.NullableStringFieldUpdateOperationsInput | string | null
  chavePix?: Prisma.NullableStringFieldUpdateOperationsInput | string | null
  tipoPix?: Prisma.NullableStringFieldUpdateOperationsInput | string | null
  tipoAssinatura?: Prisma.NullableStringFieldUpdateOperationsInput | string | null
  metaData?: Prisma.NullableJsonNullValueInput | runtime.InputJsonValue
  createdAt?: Prisma.DateTimeFieldUpdateOperationsInput | Date | string
  updatedAt?: Prisma.DateTimeFieldUpdateOperationsInput | Date | string
  deletedAt?: Prisma.NullableDateTimeFieldUpdateOperationsInput | Date | string | null
  usuario?: Prisma.UsuarioUpdateOneRequiredWithoutProfissionalNestedInput
  especialidades?: Prisma.ProfissionalEspecialidadeUpdateManyWithoutProfissionalNestedInput
  plantoes?: Prisma.PlantaoUpdateManyWithoutProfissionalNestedInput
  fechamentos?: Prisma.FechamentoUpdateManyWithoutProfissionalNestedInput
}

export type ProfissionalUncheckedUpdateWithoutAntecipacoesInput = {
  id?: Prisma.IntFieldUpdateOperationsInput | number
  uuid?: Prisma.StringFieldUpdateOperationsInput | string
  usuarioId?: Prisma.IntFieldUpdateOperationsInput | number
  rg?: Prisma.NullableStringFieldUpdateOperationsInput | string | null
  orgaoEmissor?: Prisma.NullableStringFieldUpdateOperationsInput | string | null
  cnes?: Prisma.NullableStringFieldUpdateOperationsInput | string | null
  conselhoClasse?: Prisma.NullableStringFieldUpdateOperationsInput | string | null
  numeroRegistro?: Prisma.NullableStringFieldUpdateOperationsInput | string | null
  ufConselho?: Prisma.NullableStringFieldUpdateOperationsInput | string | null
  banco?: Prisma.NullableStringFieldUpdateOperationsInput | string | null
  agencia?: Prisma.NullableStringFieldUpdateOperationsInput | string | null
  digitoAgencia?: Prisma.NullableStringFieldUpdateOperationsInput | string | null
  conta?: Prisma.NullableStringFieldUpdateOperationsInput | string | null
  digitoConta?: Prisma.NullableStringFieldUpdateOperationsInput | string | null
  tipoConta?: Prisma.NullableStringFieldUpdateOperationsInput | string | null
  chavePix?: Prisma.NullableStringFieldUpdateOperationsInput | string | null
  tipoPix?: Prisma.NullableStringFieldUpdateOperationsInput | string | null
  tipoAssinatura?: Prisma.NullableStringFieldUpdateOperationsInput | string | null
  metaData?: Prisma.NullableJsonNullValueInput | runtime.InputJsonValue
  createdAt?: Prisma.DateTimeFieldUpdateOperationsInput | Date | string
  updatedAt?: Prisma.DateTimeFieldUpdateOperationsInput | Date | string
  deletedAt?: Prisma.NullableDateTimeFieldUpdateOperationsInput | Date | string | null
  especialidades?: Prisma.ProfissionalEspecialidadeUncheckedUpdateManyWithoutProfissionalNestedInput
  plantoes?: Prisma.PlantaoUncheckedUpdateManyWithoutProfissionalNestedInput
  fechamentos?: Prisma.FechamentoUncheckedUpdateManyWithoutProfissionalNestedInput
}


/**
 * Count Type ProfissionalCountOutputType
 */

export type ProfissionalCountOutputType = {
  especialidades: number
  plantoes: number
  fechamentos: number
  antecipacoes: number
}

export type ProfissionalCountOutputTypeSelect<ExtArgs extends runtime.Types.Extensions.InternalArgs = runtime.Types.Extensions.DefaultArgs> = {
  especialidades?: boolean | ProfissionalCountOutputTypeCountEspecialidadesArgs
  plantoes?: boolean | ProfissionalCountOutputTypeCountPlantoesArgs
  fechamentos?: boolean | ProfissionalCountOutputTypeCountFechamentosArgs
  antecipacoes?: boolean | ProfissionalCountOutputTypeCountAntecipacoesArgs
}

/**
 * ProfissionalCountOutputType without action
 */
export type ProfissionalCountOutputTypeDefaultArgs<ExtArgs extends runtime.Types.Extensions.InternalArgs = runtime.Types.Extensions.DefaultArgs> = {
  /**
   * Select specific fields to fetch from the ProfissionalCountOutputType
   */
  select?: Prisma.ProfissionalCountOutputTypeSelect<ExtArgs> | null
}

/**
 * ProfissionalCountOutputType without action
 */
export type ProfissionalCountOutputTypeCountEspecialidadesArgs<ExtArgs extends runtime.Types.Extensions.InternalArgs = runtime.Types.Extensions.DefaultArgs> = {
  where?: Prisma.ProfissionalEspecialidadeWhereInput
}

/**
 * ProfissionalCountOutputType without action
 */
export type ProfissionalCountOutputTypeCountPlantoesArgs<ExtArgs extends runtime.Types.Extensions.InternalArgs = runtime.Types.Extensions.DefaultArgs> = {
  where?: Prisma.PlantaoWhereInput
}

/**
 * ProfissionalCountOutputType without action
 */
export type ProfissionalCountOutputTypeCountFechamentosArgs<ExtArgs extends runtime.Types.Extensions.InternalArgs = runtime.Types.Extensions.DefaultArgs> = {
  where?: Prisma.FechamentoWhereInput
}

/**
 * ProfissionalCountOutputType without action
 */
export type ProfissionalCountOutputTypeCountAntecipacoesArgs<ExtArgs extends runtime.Types.Extensions.InternalArgs = runtime.Types.Extensions.DefaultArgs> = {
  where?: Prisma.AntecipacaoWhereInput
}


export type ProfissionalSelect<ExtArgs extends runtime.Types.Extensions.InternalArgs = runtime.Types.Extensions.DefaultArgs> = runtime.Types.Extensions.GetSelect<{
  id?: boolean
  uuid?: boolean
  usuarioId?: boolean
  rg?: boolean
  orgaoEmissor?: boolean
  cnes?: boolean
  conselhoClasse?: boolean
  numeroRegistro?: boolean
  ufConselho?: boolean
  banco?: boolean
  agencia?: boolean
  digitoAgencia?: boolean
  conta?: boolean
  digitoConta?: boolean
  tipoConta?: boolean
  chavePix?: boolean
  tipoPix?: boolean
  tipoAssinatura?: boolean
  metaData?: boolean
  createdAt?: boolean
  updatedAt?: boolean
  deletedAt?: boolean
  usuario?: boolean | Prisma.UsuarioDefaultArgs<ExtArgs>
  especialidades?: boolean | Prisma.Profissional$especialidadesArgs<ExtArgs>
  plantoes?: boolean | Prisma.Profissional$plantoesArgs<ExtArgs>
  fechamentos?: boolean | Prisma.Profissional$fechamentosArgs<ExtArgs>
  antecipacoes?: boolean | Prisma.Profissional$antecipacoesArgs<ExtArgs>
  _count?: boolean | Prisma.ProfissionalCountOutputTypeDefaultArgs<ExtArgs>
}, ExtArgs["result"]["profissional"]>



export type ProfissionalSelectScalar = {
  id?: boolean
  uuid?: boolean
  usuarioId?: boolean
  rg?: boolean
  orgaoEmissor?: boolean
  cnes?: boolean
  conselhoClasse?: boolean
  numeroRegistro?: boolean
  ufConselho?: boolean
  banco?: boolean
  agencia?: boolean
  digitoAgencia?: boolean
  conta?: boolean
  digitoConta?: boolean
  tipoConta?: boolean
  chavePix?: boolean
  tipoPix?: boolean
  tipoAssinatura?: boolean
  metaData?: boolean
  createdAt?: boolean
  updatedAt?: boolean
  deletedAt?: boolean
}

export type ProfissionalOmit<ExtArgs extends runtime.Types.Extensions.InternalArgs = runtime.Types.Extensions.DefaultArgs> = runtime.Types.Extensions.GetOmit<"id" | "uuid" | "usuarioId" | "rg" | "orgaoEmissor" | "cnes" | "conselhoClasse" | "numeroRegistro" | "ufConselho" | "banco" | "agencia" | "digitoAgencia" | "conta" | "digitoConta" | "tipoConta" | "chavePix" | "tipoPix" | "tipoAssinatura" | "metaData" | "createdAt" | "updatedAt" | "deletedAt", ExtArgs["result"]["profissional"]>
export type ProfissionalInclude<ExtArgs extends runtime.Types.Extensions.InternalArgs = runtime.Types.Extensions.DefaultArgs> = {
  usuario?: boolean | Prisma.UsuarioDefaultArgs<ExtArgs>
  especialidades?: boolean | Prisma.Profissional$especialidadesArgs<ExtArgs>
  plantoes?: boolean | Prisma.Profissional$plantoesArgs<ExtArgs>
  fechamentos?: boolean | Prisma.Profissional$fechamentosArgs<ExtArgs>
  antecipacoes?: boolean | Prisma.Profissional$antecipacoesArgs<ExtArgs>
  _count?: boolean | Prisma.ProfissionalCountOutputTypeDefaultArgs<ExtArgs>
}

export type $ProfissionalPayload<ExtArgs extends runtime.Types.Extensions.InternalArgs = runtime.Types.Extensions.DefaultArgs> = {
  name: "Profissional"
  objects: {
    usuario: Prisma.$UsuarioPayload<ExtArgs>
    especialidades: Prisma.$ProfissionalEspecialidadePayload<ExtArgs>[]
    plantoes: Prisma.$PlantaoPayload<ExtArgs>[]
    fechamentos: Prisma.$FechamentoPayload<ExtArgs>[]
    antecipacoes: Prisma.$AntecipacaoPayload<ExtArgs>[]
  }
  scalars: runtime.Types.Extensions.GetPayloadResult<{
    id: number
    uuid: string
    usuarioId: number
    rg: string | null
    orgaoEmissor: string | null
    cnes: string | null
    conselhoClasse: string | null
    numeroRegistro: string | null
    ufConselho: string | null
    banco: string | null
    agencia: string | null
    digitoAgencia: string | null
    conta: string | null
    digitoConta: string | null
    tipoConta: string | null
    chavePix: string | null
    tipoPix: string | null
    tipoAssinatura: string | null
    metaData: runtime.JsonValue | null
    createdAt: Date
    updatedAt: Date
    deletedAt: Date | null
  }, ExtArgs["result"]["profissional"]>
  composites: {}
}

export type ProfissionalGetPayload<S extends boolean | null | undefined | ProfissionalDefaultArgs> = runtime.Types.Result.GetResult<Prisma.$ProfissionalPayload, S>

export type ProfissionalCountArgs<ExtArgs extends runtime.Types.Extensions.InternalArgs = runtime.Types.Extensions.DefaultArgs> =
  Omit<ProfissionalFindManyArgs, 'select' | 'include' | 'distinct' | 'omit'> & {
    select?: ProfissionalCountAggregateInputType | true
  }

export interface ProfissionalDelegate<ExtArgs extends runtime.Types.Extensions.InternalArgs = runtime.Types.Extensions.DefaultArgs, GlobalOmitOptions = {}> {
  [K: symbol]: { types: Prisma.TypeMap<ExtArgs>['model']['Profissional'], meta: { name: 'Profissional' } }
  /**
   * Find zero or one Profissional that matches the filter.
   * @param {ProfissionalFindUniqueArgs} args - Arguments to find a Profissional
   * @example
   * // Get one Profissional
   * const profissional = await prisma.profissional.findUnique({
   *   where: {
   *     // ... provide filter here
   *   }
   * })
   */
  findUnique<T extends ProfissionalFindUniqueArgs>(args: Prisma.SelectSubset<T, ProfissionalFindUniqueArgs<ExtArgs>>): Prisma.Prisma__ProfissionalClient<runtime.Types.Result.GetResult<Prisma.$ProfissionalPayload<ExtArgs>, T, "findUnique", GlobalOmitOptions> | null, null, ExtArgs, GlobalOmitOptions>

  /**
   * Find one Profissional that matches the filter or throw an error with `error.code='P2025'`
   * if no matches were found.
   * @param {ProfissionalFindUniqueOrThrowArgs} args - Arguments to find a Profissional
   * @example
   * // Get one Profissional
   * const profissional = await prisma.profissional.findUniqueOrThrow({
   *   where: {
   *     // ... provide filter here
   *   }
   * })
   */
  findUniqueOrThrow<T extends ProfissionalFindUniqueOrThrowArgs>(args: Prisma.SelectSubset<T, ProfissionalFindUniqueOrThrowArgs<ExtArgs>>): Prisma.Prisma__ProfissionalClient<runtime.Types.Result.GetResult<Prisma.$ProfissionalPayload<ExtArgs>, T, "findUniqueOrThrow", GlobalOmitOptions>, never, ExtArgs, GlobalOmitOptions>

  /**
   * Find the first Profissional that matches the filter.
   * Note, that providing `undefined` is treated as the value not being there.
   * Read more here: https://pris.ly/d/null-undefined
   * @param {ProfissionalFindFirstArgs} args - Arguments to find a Profissional
   * @example
   * // Get one Profissional
   * const profissional = await prisma.profissional.findFirst({
   *   where: {
   *     // ... provide filter here
   *   }
   * })
   */
  findFirst<T extends ProfissionalFindFirstArgs>(args?: Prisma.SelectSubset<T, ProfissionalFindFirstArgs<ExtArgs>>): Prisma.Prisma__ProfissionalClient<runtime.Types.Result.GetResult<Prisma.$ProfissionalPayload<ExtArgs>, T, "findFirst", GlobalOmitOptions> | null, null, ExtArgs, GlobalOmitOptions>

  /**
   * Find the first Profissional that matches the filter or
   * throw `PrismaKnownClientError` with `P2025` code if no matches were found.
   * Note, that providing `undefined` is treated as the value not being there.
   * Read more here: https://pris.ly/d/null-undefined
   * @param {ProfissionalFindFirstOrThrowArgs} args - Arguments to find a Profissional
   * @example
   * // Get one Profissional
   * const profissional = await prisma.profissional.findFirstOrThrow({
   *   where: {
   *     // ... provide filter here
   *   }
   * })
   */
  findFirstOrThrow<T extends ProfissionalFindFirstOrThrowArgs>(args?: Prisma.SelectSubset<T, ProfissionalFindFirstOrThrowArgs<ExtArgs>>): Prisma.Prisma__ProfissionalClient<runtime.Types.Result.GetResult<Prisma.$ProfissionalPayload<ExtArgs>, T, "findFirstOrThrow", GlobalOmitOptions>, never, ExtArgs, GlobalOmitOptions>

  /**
   * Find zero or more Profissionals that matches the filter.
   * Note, that providing `undefined` is treated as the value not being there.
   * Read more here: https://pris.ly/d/null-undefined
   * @param {ProfissionalFindManyArgs} args - Arguments to filter and select certain fields only.
   * @example
   * // Get all Profissionals
   * const profissionals = await prisma.profissional.findMany()
   * 
   * // Get first 10 Profissionals
   * const profissionals = await prisma.profissional.findMany({ take: 10 })
   * 
   * // Only select the `id`
   * const profissionalWithIdOnly = await prisma.profissional.findMany({ select: { id: true } })
   * 
   */
  findMany<T extends ProfissionalFindManyArgs>(args?: Prisma.SelectSubset<T, ProfissionalFindManyArgs<ExtArgs>>): Prisma.PrismaPromise<runtime.Types.Result.GetResult<Prisma.$ProfissionalPayload<ExtArgs>, T, "findMany", GlobalOmitOptions>>

  /**
   * Create a Profissional.
   * @param {ProfissionalCreateArgs} args - Arguments to create a Profissional.
   * @example
   * // Create one Profissional
   * const Profissional = await prisma.profissional.create({
   *   data: {
   *     // ... data to create a Profissional
   *   }
   * })
   * 
   */
  create<T extends ProfissionalCreateArgs>(args: Prisma.SelectSubset<T, ProfissionalCreateArgs<ExtArgs>>): Prisma.Prisma__ProfissionalClient<runtime.Types.Result.GetResult<Prisma.$ProfissionalPayload<ExtArgs>, T, "create", GlobalOmitOptions>, never, ExtArgs, GlobalOmitOptions>

  /**
   * Create many Profissionals.
   * @param {ProfissionalCreateManyArgs} args - Arguments to create many Profissionals.
   * @example
   * // Create many Profissionals
   * const profissional = await prisma.profissional.createMany({
   *   data: [
   *     // ... provide data here
   *   ]
   * })
   *     
   */
  createMany<T extends ProfissionalCreateManyArgs>(args?: Prisma.SelectSubset<T, ProfissionalCreateManyArgs<ExtArgs>>): Prisma.PrismaPromise<Prisma.BatchPayload>

  /**
   * Delete a Profissional.
   * @param {ProfissionalDeleteArgs} args - Arguments to delete one Profissional.
   * @example
   * // Delete one Profissional
   * const Profissional = await prisma.profissional.delete({
   *   where: {
   *     // ... filter to delete one Profissional
   *   }
   * })
   * 
   */
  delete<T extends ProfissionalDeleteArgs>(args: Prisma.SelectSubset<T, ProfissionalDeleteArgs<ExtArgs>>): Prisma.Prisma__ProfissionalClient<runtime.Types.Result.GetResult<Prisma.$ProfissionalPayload<ExtArgs>, T, "delete", GlobalOmitOptions>, never, ExtArgs, GlobalOmitOptions>

  /**
   * Update one Profissional.
   * @param {ProfissionalUpdateArgs} args - Arguments to update one Profissional.
   * @example
   * // Update one Profissional
   * const profissional = await prisma.profissional.update({
   *   where: {
   *     // ... provide filter here
   *   },
   *   data: {
   *     // ... provide data here
   *   }
   * })
   * 
   */
  update<T extends ProfissionalUpdateArgs>(args: Prisma.SelectSubset<T, ProfissionalUpdateArgs<ExtArgs>>): Prisma.Prisma__ProfissionalClient<runtime.Types.Result.GetResult<Prisma.$ProfissionalPayload<ExtArgs>, T, "update", GlobalOmitOptions>, never, ExtArgs, GlobalOmitOptions>

  /**
   * Delete zero or more Profissionals.
   * @param {ProfissionalDeleteManyArgs} args - Arguments to filter Profissionals to delete.
   * @example
   * // Delete a few Profissionals
   * const { count } = await prisma.profissional.deleteMany({
   *   where: {
   *     // ... provide filter here
   *   }
   * })
   * 
   */
  deleteMany<T extends ProfissionalDeleteManyArgs>(args?: Prisma.SelectSubset<T, ProfissionalDeleteManyArgs<ExtArgs>>): Prisma.PrismaPromise<Prisma.BatchPayload>

  /**
   * Update zero or more Profissionals.
   * Note, that providing `undefined` is treated as the value not being there.
   * Read more here: https://pris.ly/d/null-undefined
   * @param {ProfissionalUpdateManyArgs} args - Arguments to update one or more rows.
   * @example
   * // Update many Profissionals
   * const profissional = await prisma.profissional.updateMany({
   *   where: {
   *     // ... provide filter here
   *   },
   *   data: {
   *     // ... provide data here
   *   }
   * })
   * 
   */
  updateMany<T extends ProfissionalUpdateManyArgs>(args: Prisma.SelectSubset<T, ProfissionalUpdateManyArgs<ExtArgs>>): Prisma.PrismaPromise<Prisma.BatchPayload>

  /**
   * Create or update one Profissional.
   * @param {ProfissionalUpsertArgs} args - Arguments to update or create a Profissional.
   * @example
   * // Update or create a Profissional
   * const profissional = await prisma.profissional.upsert({
   *   create: {
   *     // ... data to create a Profissional
   *   },
   *   update: {
   *     // ... in case it already exists, update
   *   },
   *   where: {
   *     // ... the filter for the Profissional we want to update
   *   }
   * })
   */
  upsert<T extends ProfissionalUpsertArgs>(args: Prisma.SelectSubset<T, ProfissionalUpsertArgs<ExtArgs>>): Prisma.Prisma__ProfissionalClient<runtime.Types.Result.GetResult<Prisma.$ProfissionalPayload<ExtArgs>, T, "upsert", GlobalOmitOptions>, never, ExtArgs, GlobalOmitOptions>


  /**
   * Count the number of Profissionals.
   * Note, that providing `undefined` is treated as the value not being there.
   * Read more here: https://pris.ly/d/null-undefined
   * @param {ProfissionalCountArgs} args - Arguments to filter Profissionals to count.
   * @example
   * // Count the number of Profissionals
   * const count = await prisma.profissional.count({
   *   where: {
   *     // ... the filter for the Profissionals we want to count
   *   }
   * })
  **/
  count<T extends ProfissionalCountArgs>(
    args?: Prisma.Subset<T, ProfissionalCountArgs>,
  ): Prisma.PrismaPromise<
    T extends runtime.Types.Utils.Record<'select', any>
      ? T['select'] extends true
        ? number
        : Prisma.GetScalarType<T['select'], ProfissionalCountAggregateOutputType>
      : number
  >

  /**
   * Allows you to perform aggregations operations on a Profissional.
   * Note, that providing `undefined` is treated as the value not being there.
   * Read more here: https://pris.ly/d/null-undefined
   * @param {ProfissionalAggregateArgs} args - Select which aggregations you would like to apply and on what fields.
   * @example
   * // Ordered by age ascending
   * // Where email contains prisma.io
   * // Limited to the 10 users
   * const aggregations = await prisma.user.aggregate({
   *   _avg: {
   *     age: true,
   *   },
   *   where: {
   *     email: {
   *       contains: "prisma.io",
   *     },
   *   },
   *   orderBy: {
   *     age: "asc",
   *   },
   *   take: 10,
   * })
  **/
  aggregate<T extends ProfissionalAggregateArgs>(args: Prisma.Subset<T, ProfissionalAggregateArgs>): Prisma.PrismaPromise<GetProfissionalAggregateType<T>>

  /**
   * Group by Profissional.
   * Note, that providing `undefined` is treated as the value not being there.
   * Read more here: https://pris.ly/d/null-undefined
   * @param {ProfissionalGroupByArgs} args - Group by arguments.
   * @example
   * // Group by city, order by createdAt, get count
   * const result = await prisma.user.groupBy({
   *   by: ['city', 'createdAt'],
   *   orderBy: {
   *     createdAt: true
   *   },
   *   _count: {
   *     _all: true
   *   },
   * })
   * 
  **/
  groupBy<
    T extends ProfissionalGroupByArgs,
    HasSelectOrTake extends Prisma.Or<
      Prisma.Extends<'skip', Prisma.Keys<T>>,
      Prisma.Extends<'take', Prisma.Keys<T>>
    >,
    OrderByArg extends Prisma.True extends HasSelectOrTake
      ? { orderBy: ProfissionalGroupByArgs['orderBy'] }
      : { orderBy?: ProfissionalGroupByArgs['orderBy'] },
    OrderFields extends Prisma.ExcludeUnderscoreKeys<Prisma.Keys<Prisma.MaybeTupleToUnion<T['orderBy']>>>,
    ByFields extends Prisma.MaybeTupleToUnion<T['by']>,
    ByValid extends Prisma.Has<ByFields, OrderFields>,
    HavingFields extends Prisma.GetHavingFields<T['having']>,
    HavingValid extends Prisma.Has<ByFields, HavingFields>,
    ByEmpty extends T['by'] extends never[] ? Prisma.True : Prisma.False,
    InputErrors extends ByEmpty extends Prisma.True
    ? `Error: "by" must not be empty.`
    : HavingValid extends Prisma.False
    ? {
        [P in HavingFields]: P extends ByFields
          ? never
          : P extends string
          ? `Error: Field "${P}" used in "having" needs to be provided in "by".`
          : [
              Error,
              'Field ',
              P,
              ` in "having" needs to be provided in "by"`,
            ]
      }[HavingFields]
    : 'take' extends Prisma.Keys<T>
    ? 'orderBy' extends Prisma.Keys<T>
      ? ByValid extends Prisma.True
        ? {}
        : {
            [P in OrderFields]: P extends ByFields
              ? never
              : `Error: Field "${P}" in "orderBy" needs to be provided in "by"`
          }[OrderFields]
      : 'Error: If you provide "take", you also need to provide "orderBy"'
    : 'skip' extends Prisma.Keys<T>
    ? 'orderBy' extends Prisma.Keys<T>
      ? ByValid extends Prisma.True
        ? {}
        : {
            [P in OrderFields]: P extends ByFields
              ? never
              : `Error: Field "${P}" in "orderBy" needs to be provided in "by"`
          }[OrderFields]
      : 'Error: If you provide "skip", you also need to provide "orderBy"'
    : ByValid extends Prisma.True
    ? {}
    : {
        [P in OrderFields]: P extends ByFields
          ? never
          : `Error: Field "${P}" in "orderBy" needs to be provided in "by"`
      }[OrderFields]
  >(args: Prisma.SubsetIntersection<T, ProfissionalGroupByArgs, OrderByArg> & InputErrors): {} extends InputErrors ? GetProfissionalGroupByPayload<T> : Prisma.PrismaPromise<InputErrors>
/**
 * Fields of the Profissional model
 */
readonly fields: ProfissionalFieldRefs;
}

/**
 * The delegate class that acts as a "Promise-like" for Profissional.
 * Why is this prefixed with `Prisma__`?
 * Because we want to prevent naming conflicts as mentioned in
 * https://github.com/prisma/prisma-client-js/issues/707
 */
export interface Prisma__ProfissionalClient<T, Null = never, ExtArgs extends runtime.Types.Extensions.InternalArgs = runtime.Types.Extensions.DefaultArgs, GlobalOmitOptions = {}> extends Prisma.PrismaPromise<T> {
  readonly [Symbol.toStringTag]: "PrismaPromise"
  usuario<T extends Prisma.UsuarioDefaultArgs<ExtArgs> = {}>(args?: Prisma.Subset<T, Prisma.UsuarioDefaultArgs<ExtArgs>>): Prisma.Prisma__UsuarioClient<runtime.Types.Result.GetResult<Prisma.$UsuarioPayload<ExtArgs>, T, "findUniqueOrThrow", GlobalOmitOptions> | Null, Null, ExtArgs, GlobalOmitOptions>
  especialidades<T extends Prisma.Profissional$especialidadesArgs<ExtArgs> = {}>(args?: Prisma.Subset<T, Prisma.Profissional$especialidadesArgs<ExtArgs>>): Prisma.PrismaPromise<runtime.Types.Result.GetResult<Prisma.$ProfissionalEspecialidadePayload<ExtArgs>, T, "findMany", GlobalOmitOptions> | Null>
  plantoes<T extends Prisma.Profissional$plantoesArgs<ExtArgs> = {}>(args?: Prisma.Subset<T, Prisma.Profissional$plantoesArgs<ExtArgs>>): Prisma.PrismaPromise<runtime.Types.Result.GetResult<Prisma.$PlantaoPayload<ExtArgs>, T, "findMany", GlobalOmitOptions> | Null>
  fechamentos<T extends Prisma.Profissional$fechamentosArgs<ExtArgs> = {}>(args?: Prisma.Subset<T, Prisma.Profissional$fechamentosArgs<ExtArgs>>): Prisma.PrismaPromise<runtime.Types.Result.GetResult<Prisma.$FechamentoPayload<ExtArgs>, T, "findMany", GlobalOmitOptions> | Null>
  antecipacoes<T extends Prisma.Profissional$antecipacoesArgs<ExtArgs> = {}>(args?: Prisma.Subset<T, Prisma.Profissional$antecipacoesArgs<ExtArgs>>): Prisma.PrismaPromise<runtime.Types.Result.GetResult<Prisma.$AntecipacaoPayload<ExtArgs>, T, "findMany", GlobalOmitOptions> | Null>
  /**
   * Attaches callbacks for the resolution and/or rejection of the Promise.
   * @param onfulfilled The callback to execute when the Promise is resolved.
   * @param onrejected The callback to execute when the Promise is rejected.
   * @returns A Promise for the completion of which ever callback is executed.
   */
  then<TResult1 = T, TResult2 = never>(onfulfilled?: ((value: T) => TResult1 | PromiseLike<TResult1>) | undefined | null, onrejected?: ((reason: any) => TResult2 | PromiseLike<TResult2>) | undefined | null): runtime.Types.Utils.JsPromise<TResult1 | TResult2>
  /**
   * Attaches a callback for only the rejection of the Promise.
   * @param onrejected The callback to execute when the Promise is rejected.
   * @returns A Promise for the completion of the callback.
   */
  catch<TResult = never>(onrejected?: ((reason: any) => TResult | PromiseLike<TResult>) | undefined | null): runtime.Types.Utils.JsPromise<T | TResult>
  /**
   * Attaches a callback that is invoked when the Promise is settled (fulfilled or rejected). The
   * resolved value cannot be modified from the callback.
   * @param onfinally The callback to execute when the Promise is settled (fulfilled or rejected).
   * @returns A Promise for the completion of the callback.
   */
  finally(onfinally?: (() => void) | undefined | null): runtime.Types.Utils.JsPromise<T>
}




/**
 * Fields of the Profissional model
 */
export interface ProfissionalFieldRefs {
  readonly id: Prisma.FieldRef<"Profissional", 'Int'>
  readonly uuid: Prisma.FieldRef<"Profissional", 'String'>
  readonly usuarioId: Prisma.FieldRef<"Profissional", 'Int'>
  readonly rg: Prisma.FieldRef<"Profissional", 'String'>
  readonly orgaoEmissor: Prisma.FieldRef<"Profissional", 'String'>
  readonly cnes: Prisma.FieldRef<"Profissional", 'String'>
  readonly conselhoClasse: Prisma.FieldRef<"Profissional", 'String'>
  readonly numeroRegistro: Prisma.FieldRef<"Profissional", 'String'>
  readonly ufConselho: Prisma.FieldRef<"Profissional", 'String'>
  readonly banco: Prisma.FieldRef<"Profissional", 'String'>
  readonly agencia: Prisma.FieldRef<"Profissional", 'String'>
  readonly digitoAgencia: Prisma.FieldRef<"Profissional", 'String'>
  readonly conta: Prisma.FieldRef<"Profissional", 'String'>
  readonly digitoConta: Prisma.FieldRef<"Profissional", 'String'>
  readonly tipoConta: Prisma.FieldRef<"Profissional", 'String'>
  readonly chavePix: Prisma.FieldRef<"Profissional", 'String'>
  readonly tipoPix: Prisma.FieldRef<"Profissional", 'String'>
  readonly tipoAssinatura: Prisma.FieldRef<"Profissional", 'String'>
  readonly metaData: Prisma.FieldRef<"Profissional", 'Json'>
  readonly createdAt: Prisma.FieldRef<"Profissional", 'DateTime'>
  readonly updatedAt: Prisma.FieldRef<"Profissional", 'DateTime'>
  readonly deletedAt: Prisma.FieldRef<"Profissional", 'DateTime'>
}
    

// Custom InputTypes
/**
 * Profissional findUnique
 */
export type ProfissionalFindUniqueArgs<ExtArgs extends runtime.Types.Extensions.InternalArgs = runtime.Types.Extensions.DefaultArgs> = {
  /**
   * Select specific fields to fetch from the Profissional
   */
  select?: Prisma.ProfissionalSelect<ExtArgs> | null
  /**
   * Omit specific fields from the Profissional
   */
  omit?: Prisma.ProfissionalOmit<ExtArgs> | null
  /**
   * Choose, which related nodes to fetch as well
   */
  include?: Prisma.ProfissionalInclude<ExtArgs> | null
  /**
   * Filter, which Profissional to fetch.
   */
  where: Prisma.ProfissionalWhereUniqueInput
}

/**
 * Profissional findUniqueOrThrow
 */
export type ProfissionalFindUniqueOrThrowArgs<ExtArgs extends runtime.Types.Extensions.InternalArgs = runtime.Types.Extensions.DefaultArgs> = {
  /**
   * Select specific fields to fetch from the Profissional
   */
  select?: Prisma.ProfissionalSelect<ExtArgs> | null
  /**
   * Omit specific fields from the Profissional
   */
  omit?: Prisma.ProfissionalOmit<ExtArgs> | null
  /**
   * Choose, which related nodes to fetch as well
   */
  include?: Prisma.ProfissionalInclude<ExtArgs> | null
  /**
   * Filter, which Profissional to fetch.
   */
  where: Prisma.ProfissionalWhereUniqueInput
}

/**
 * Profissional findFirst
 */
export type ProfissionalFindFirstArgs<ExtArgs extends runtime.Types.Extensions.InternalArgs = runtime.Types.Extensions.DefaultArgs> = {
  /**
   * Select specific fields to fetch from the Profissional
   */
  select?: Prisma.ProfissionalSelect<ExtArgs> | null
  /**
   * Omit specific fields from the Profissional
   */
  omit?: Prisma.ProfissionalOmit<ExtArgs> | null
  /**
   * Choose, which related nodes to fetch as well
   */
  include?: Prisma.ProfissionalInclude<ExtArgs> | null
  /**
   * Filter, which Profissional to fetch.
   */
  where?: Prisma.ProfissionalWhereInput
  /**
   * {@link https://www.prisma.io/docs/concepts/components/prisma-client/sorting Sorting Docs}
   * 
   * Determine the order of Profissionals to fetch.
   */
  orderBy?: Prisma.ProfissionalOrderByWithRelationInput | Prisma.ProfissionalOrderByWithRelationInput[]
  /**
   * {@link https://www.prisma.io/docs/concepts/components/prisma-client/pagination#cursor-based-pagination Cursor Docs}
   * 
   * Sets the position for searching for Profissionals.
   */
  cursor?: Prisma.ProfissionalWhereUniqueInput
  /**
   * {@link https://www.prisma.io/docs/concepts/components/prisma-client/pagination Pagination Docs}
   * 
   * Take `±n` Profissionals from the position of the cursor.
   */
  take?: number
  /**
   * {@link https://www.prisma.io/docs/concepts/components/prisma-client/pagination Pagination Docs}
   * 
   * Skip the first `n` Profissionals.
   */
  skip?: number
  /**
   * {@link https://www.prisma.io/docs/concepts/components/prisma-client/distinct Distinct Docs}
   * 
   * Filter by unique combinations of Profissionals.
   */
  distinct?: Prisma.ProfissionalScalarFieldEnum | Prisma.ProfissionalScalarFieldEnum[]
}

/**
 * Profissional findFirstOrThrow
 */
export type ProfissionalFindFirstOrThrowArgs<ExtArgs extends runtime.Types.Extensions.InternalArgs = runtime.Types.Extensions.DefaultArgs> = {
  /**
   * Select specific fields to fetch from the Profissional
   */
  select?: Prisma.ProfissionalSelect<ExtArgs> | null
  /**
   * Omit specific fields from the Profissional
   */
  omit?: Prisma.ProfissionalOmit<ExtArgs> | null
  /**
   * Choose, which related nodes to fetch as well
   */
  include?: Prisma.ProfissionalInclude<ExtArgs> | null
  /**
   * Filter, which Profissional to fetch.
   */
  where?: Prisma.ProfissionalWhereInput
  /**
   * {@link https://www.prisma.io/docs/concepts/components/prisma-client/sorting Sorting Docs}
   * 
   * Determine the order of Profissionals to fetch.
   */
  orderBy?: Prisma.ProfissionalOrderByWithRelationInput | Prisma.ProfissionalOrderByWithRelationInput[]
  /**
   * {@link https://www.prisma.io/docs/concepts/components/prisma-client/pagination#cursor-based-pagination Cursor Docs}
   * 
   * Sets the position for searching for Profissionals.
   */
  cursor?: Prisma.ProfissionalWhereUniqueInput
  /**
   * {@link https://www.prisma.io/docs/concepts/components/prisma-client/pagination Pagination Docs}
   * 
   * Take `±n` Profissionals from the position of the cursor.
   */
  take?: number
  /**
   * {@link https://www.prisma.io/docs/concepts/components/prisma-client/pagination Pagination Docs}
   * 
   * Skip the first `n` Profissionals.
   */
  skip?: number
  /**
   * {@link https://www.prisma.io/docs/concepts/components/prisma-client/distinct Distinct Docs}
   * 
   * Filter by unique combinations of Profissionals.
   */
  distinct?: Prisma.ProfissionalScalarFieldEnum | Prisma.ProfissionalScalarFieldEnum[]
}

/**
 * Profissional findMany
 */
export type ProfissionalFindManyArgs<ExtArgs extends runtime.Types.Extensions.InternalArgs = runtime.Types.Extensions.DefaultArgs> = {
  /**
   * Select specific fields to fetch from the Profissional
   */
  select?: Prisma.ProfissionalSelect<ExtArgs> | null
  /**
   * Omit specific fields from the Profissional
   */
  omit?: Prisma.ProfissionalOmit<ExtArgs> | null
  /**
   * Choose, which related nodes to fetch as well
   */
  include?: Prisma.ProfissionalInclude<ExtArgs> | null
  /**
   * Filter, which Profissionals to fetch.
   */
  where?: Prisma.ProfissionalWhereInput
  /**
   * {@link https://www.prisma.io/docs/concepts/components/prisma-client/sorting Sorting Docs}
   * 
   * Determine the order of Profissionals to fetch.
   */
  orderBy?: Prisma.ProfissionalOrderByWithRelationInput | Prisma.ProfissionalOrderByWithRelationInput[]
  /**
   * {@link https://www.prisma.io/docs/concepts/components/prisma-client/pagination#cursor-based-pagination Cursor Docs}
   * 
   * Sets the position for listing Profissionals.
   */
  cursor?: Prisma.ProfissionalWhereUniqueInput
  /**
   * {@link https://www.prisma.io/docs/concepts/components/prisma-client/pagination Pagination Docs}
   * 
   * Take `±n` Profissionals from the position of the cursor.
   */
  take?: number
  /**
   * {@link https://www.prisma.io/docs/concepts/components/prisma-client/pagination Pagination Docs}
   * 
   * Skip the first `n` Profissionals.
   */
  skip?: number
  distinct?: Prisma.ProfissionalScalarFieldEnum | Prisma.ProfissionalScalarFieldEnum[]
}

/**
 * Profissional create
 */
export type ProfissionalCreateArgs<ExtArgs extends runtime.Types.Extensions.InternalArgs = runtime.Types.Extensions.DefaultArgs> = {
  /**
   * Select specific fields to fetch from the Profissional
   */
  select?: Prisma.ProfissionalSelect<ExtArgs> | null
  /**
   * Omit specific fields from the Profissional
   */
  omit?: Prisma.ProfissionalOmit<ExtArgs> | null
  /**
   * Choose, which related nodes to fetch as well
   */
  include?: Prisma.ProfissionalInclude<ExtArgs> | null
  /**
   * The data needed to create a Profissional.
   */
  data: Prisma.XOR<Prisma.ProfissionalCreateInput, Prisma.ProfissionalUncheckedCreateInput>
}

/**
 * Profissional createMany
 */
export type ProfissionalCreateManyArgs<ExtArgs extends runtime.Types.Extensions.InternalArgs = runtime.Types.Extensions.DefaultArgs> = {
  /**
   * The data used to create many Profissionals.
   */
  data: Prisma.ProfissionalCreateManyInput | Prisma.ProfissionalCreateManyInput[]
  skipDuplicates?: boolean
}

/**
 * Profissional update
 */
export type ProfissionalUpdateArgs<ExtArgs extends runtime.Types.Extensions.InternalArgs = runtime.Types.Extensions.DefaultArgs> = {
  /**
   * Select specific fields to fetch from the Profissional
   */
  select?: Prisma.ProfissionalSelect<ExtArgs> | null
  /**
   * Omit specific fields from the Profissional
   */
  omit?: Prisma.ProfissionalOmit<ExtArgs> | null
  /**
   * Choose, which related nodes to fetch as well
   */
  include?: Prisma.ProfissionalInclude<ExtArgs> | null
  /**
   * The data needed to update a Profissional.
   */
  data: Prisma.XOR<Prisma.ProfissionalUpdateInput, Prisma.ProfissionalUncheckedUpdateInput>
  /**
   * Choose, which Profissional to update.
   */
  where: Prisma.ProfissionalWhereUniqueInput
}

/**
 * Profissional updateMany
 */
export type ProfissionalUpdateManyArgs<ExtArgs extends runtime.Types.Extensions.InternalArgs = runtime.Types.Extensions.DefaultArgs> = {
  /**
   * The data used to update Profissionals.
   */
  data: Prisma.XOR<Prisma.ProfissionalUpdateManyMutationInput, Prisma.ProfissionalUncheckedUpdateManyInput>
  /**
   * Filter which Profissionals to update
   */
  where?: Prisma.ProfissionalWhereInput
  /**
   * Limit how many Profissionals to update.
   */
  limit?: number
}

/**
 * Profissional upsert
 */
export type ProfissionalUpsertArgs<ExtArgs extends runtime.Types.Extensions.InternalArgs = runtime.Types.Extensions.DefaultArgs> = {
  /**
   * Select specific fields to fetch from the Profissional
   */
  select?: Prisma.ProfissionalSelect<ExtArgs> | null
  /**
   * Omit specific fields from the Profissional
   */
  omit?: Prisma.ProfissionalOmit<ExtArgs> | null
  /**
   * Choose, which related nodes to fetch as well
   */
  include?: Prisma.ProfissionalInclude<ExtArgs> | null
  /**
   * The filter to search for the Profissional to update in case it exists.
   */
  where: Prisma.ProfissionalWhereUniqueInput
  /**
   * In case the Profissional found by the `where` argument doesn't exist, create a new Profissional with this data.
   */
  create: Prisma.XOR<Prisma.ProfissionalCreateInput, Prisma.ProfissionalUncheckedCreateInput>
  /**
   * In case the Profissional was found with the provided `where` argument, update it with this data.
   */
  update: Prisma.XOR<Prisma.ProfissionalUpdateInput, Prisma.ProfissionalUncheckedUpdateInput>
}

/**
 * Profissional delete
 */
export type ProfissionalDeleteArgs<ExtArgs extends runtime.Types.Extensions.InternalArgs = runtime.Types.Extensions.DefaultArgs> = {
  /**
   * Select specific fields to fetch from the Profissional
   */
  select?: Prisma.ProfissionalSelect<ExtArgs> | null
  /**
   * Omit specific fields from the Profissional
   */
  omit?: Prisma.ProfissionalOmit<ExtArgs> | null
  /**
   * Choose, which related nodes to fetch as well
   */
  include?: Prisma.ProfissionalInclude<ExtArgs> | null
  /**
   * Filter which Profissional to delete.
   */
  where: Prisma.ProfissionalWhereUniqueInput
}

/**
 * Profissional deleteMany
 */
export type ProfissionalDeleteManyArgs<ExtArgs extends runtime.Types.Extensions.InternalArgs = runtime.Types.Extensions.DefaultArgs> = {
  /**
   * Filter which Profissionals to delete
   */
  where?: Prisma.ProfissionalWhereInput
  /**
   * Limit how many Profissionals to delete.
   */
  limit?: number
}

/**
 * Profissional.especialidades
 */
export type Profissional$especialidadesArgs<ExtArgs extends runtime.Types.Extensions.InternalArgs = runtime.Types.Extensions.DefaultArgs> = {
  /**
   * Select specific fields to fetch from the ProfissionalEspecialidade
   */
  select?: Prisma.ProfissionalEspecialidadeSelect<ExtArgs> | null
  /**
   * Omit specific fields from the ProfissionalEspecialidade
   */
  omit?: Prisma.ProfissionalEspecialidadeOmit<ExtArgs> | null
  /**
   * Choose, which related nodes to fetch as well
   */
  include?: Prisma.ProfissionalEspecialidadeInclude<ExtArgs> | null
  where?: Prisma.ProfissionalEspecialidadeWhereInput
  orderBy?: Prisma.ProfissionalEspecialidadeOrderByWithRelationInput | Prisma.ProfissionalEspecialidadeOrderByWithRelationInput[]
  cursor?: Prisma.ProfissionalEspecialidadeWhereUniqueInput
  take?: number
  skip?: number
  distinct?: Prisma.ProfissionalEspecialidadeScalarFieldEnum | Prisma.ProfissionalEspecialidadeScalarFieldEnum[]
}

/**
 * Profissional.plantoes
 */
export type Profissional$plantoesArgs<ExtArgs extends runtime.Types.Extensions.InternalArgs = runtime.Types.Extensions.DefaultArgs> = {
  /**
   * Select specific fields to fetch from the Plantao
   */
  select?: Prisma.PlantaoSelect<ExtArgs> | null
  /**
   * Omit specific fields from the Plantao
   */
  omit?: Prisma.PlantaoOmit<ExtArgs> | null
  /**
   * Choose, which related nodes to fetch as well
   */
  include?: Prisma.PlantaoInclude<ExtArgs> | null
  where?: Prisma.PlantaoWhereInput
  orderBy?: Prisma.PlantaoOrderByWithRelationInput | Prisma.PlantaoOrderByWithRelationInput[]
  cursor?: Prisma.PlantaoWhereUniqueInput
  take?: number
  skip?: number
  distinct?: Prisma.PlantaoScalarFieldEnum | Prisma.PlantaoScalarFieldEnum[]
}

/**
 * Profissional.fechamentos
 */
export type Profissional$fechamentosArgs<ExtArgs extends runtime.Types.Extensions.InternalArgs = runtime.Types.Extensions.DefaultArgs> = {
  /**
   * Select specific fields to fetch from the Fechamento
   */
  select?: Prisma.FechamentoSelect<ExtArgs> | null
  /**
   * Omit specific fields from the Fechamento
   */
  omit?: Prisma.FechamentoOmit<ExtArgs> | null
  /**
   * Choose, which related nodes to fetch as well
   */
  include?: Prisma.FechamentoInclude<ExtArgs> | null
  where?: Prisma.FechamentoWhereInput
  orderBy?: Prisma.FechamentoOrderByWithRelationInput | Prisma.FechamentoOrderByWithRelationInput[]
  cursor?: Prisma.FechamentoWhereUniqueInput
  take?: number
  skip?: number
  distinct?: Prisma.FechamentoScalarFieldEnum | Prisma.FechamentoScalarFieldEnum[]
}

/**
 * Profissional.antecipacoes
 */
export type Profissional$antecipacoesArgs<ExtArgs extends runtime.Types.Extensions.InternalArgs = runtime.Types.Extensions.DefaultArgs> = {
  /**
   * Select specific fields to fetch from the Antecipacao
   */
  select?: Prisma.AntecipacaoSelect<ExtArgs> | null
  /**
   * Omit specific fields from the Antecipacao
   */
  omit?: Prisma.AntecipacaoOmit<ExtArgs> | null
  /**
   * Choose, which related nodes to fetch as well
   */
  include?: Prisma.AntecipacaoInclude<ExtArgs> | null
  where?: Prisma.AntecipacaoWhereInput
  orderBy?: Prisma.AntecipacaoOrderByWithRelationInput | Prisma.AntecipacaoOrderByWithRelationInput[]
  cursor?: Prisma.AntecipacaoWhereUniqueInput
  take?: number
  skip?: number
  distinct?: Prisma.AntecipacaoScalarFieldEnum | Prisma.AntecipacaoScalarFieldEnum[]
}

/**
 * Profissional without action
 */
export type ProfissionalDefaultArgs<ExtArgs extends runtime.Types.Extensions.InternalArgs = runtime.Types.Extensions.DefaultArgs> = {
  /**
   * Select specific fields to fetch from the Profissional
   */
  select?: Prisma.ProfissionalSelect<ExtArgs> | null
  /**
   * Omit specific fields from the Profissional
   */
  omit?: Prisma.ProfissionalOmit<ExtArgs> | null
  /**
   * Choose, which related nodes to fetch as well
   */
  include?: Prisma.ProfissionalInclude<ExtArgs> | null
}
