/**
 * Validates API key from request headers
 * @param {Object} headers - Request headers
 * @returns {boolean} True if valid API key
 */
export const validateApiKey = (headers) => {
  if (!headers) return false;

  // Get API key from environment variable
  const VALID_API_KEY = process.env.API_KEY;

  // Check multiple possible header names (case-insensitive)
  const apiKey =
    headers["x-api-key"] ||
    headers["X-Api-Key"] ||
    headers["X-API-KEY"] ||
    headers["authorization"] ||
    headers["Authorization"];

  if (!apiKey) return false;

  // Remove "Bearer " prefix if present
  const cleanKey = apiKey.replace(/^Bearer\s+/i, "");

  return cleanKey === VALID_API_KEY;
};

/**
 * Creates unauthorized response
 * @returns {Object} Lambda response with 401 status
 */
export const unauthorizedResponse = () => ({
  statusCode: 401,
});

/**
 * Creates rate limit response
 * @returns {Object} Lambda response with 429 status
 */
export const rateLimitResponse = () => ({
  statusCode: 429,
  body: JSON.stringify({
    message: "Muitas requisições. Tente novamente mais tarde.",
    errorCode: "RATE_LIMIT_EXCEEDED",
  }),
});

/**
 * Middleware to check authorization
 * @param {Object} event - Lambda event
 * @returns {Object|null} Error response or null if authorized
 */
export const checkAuth = (event) => {
  // Extract headers from event
  const headers = event.headers || event.Headers || {};

  // Validate API key
  if (!validateApiKey(headers)) {
    console.log("Unauthorized access attempt:", {
      ip: event.requestContext?.identity?.sourceIp,
      userAgent: headers["user-agent"] || headers["User-Agent"],
      timestamp: new Date().toISOString(),
    });

    return unauthorizedResponse();
  }

  return null; // Authorized
};
