import { prisma } from "../lib/prisma";
import {
  createFechamentoSchema,
  updateFechamentoSchema,
  fechamentoQuerySchema,
  type CreateFechamentoInput,
  type UpdateFechamentoInput,
  type FechamentoQuery,
} from "../schemas/fechamento.schema";
import { PlantaoAutoCompleteService } from "../services/plantao-auto-complete.service";
import {
  getMesFromDate,
  getAnoFromDate,
  createLocalDate,
  getCurrentYear,
  getCurrentDate,
  parseISO,
} from "@shared/date";
import {
  createLocalDateInTimezone,
  getCurrentDateInTimezone,
  parseISOInTimezone,
  getStartOfMonthInTimezone,
  getEndOfMonthInTimezone,
} from "@shared/date";
import type { FastifyTypedInstance } from "@/types";
import { authorize } from "@/middlewares/auth.middleware";

export async function fechamentoRouter(fastify: FastifyTypedInstance) {
  const autoCompleteService = new PlantaoAutoCompleteService(prisma as any);
  // Listar fechamentos
  fastify.get<{ Querystring: FechamentoQuery }>("/fechamentos", async (request, reply) => {
    const {
      page = 1,
      limit = 10,
      mes,
      ano,
      status,
      plantaoUuid,
      profissionalId,
      dataInicial,
      dataFinal,
      semAntecipacao,
      search,
    } = request.query;
    const pageNum = Number(page);
    const limitNum = Number(limit);
    const skip = (pageNum - 1) * limitNum;
    const clienteId = request.clienteId;

    // Construir WHERE clause de forma mais clara e consistente
    const where: any = {};

    // Filtros diretos do fechamento
    if (status) where.status = status;

    // Filtro para fechamentos sem antecipação
    if (semAntecipacao === true || semAntecipacao === "true") {
      where.antecipacaoId = null;
    }

    // Filtros do plantão relacionado
    const plantaoFilters: any = {};
    if (plantaoUuid) plantaoFilters.uuid = plantaoUuid;

    // Usar mes/ano para filtrar plantões ativos no período
    if (mes && ano) {
      // Buscar plantões ativos no mês/ano
      const fusoHorario = request.fusoHorario;
      const inicioMes = getStartOfMonthInTimezone(Number(ano), Number(mes), fusoHorario);
      const fimMes = getEndOfMonthInTimezone(Number(ano), Number(mes), fusoHorario);

      plantaoFilters.dataInicial = { lte: fimMes };
      plantaoFilters.OR = [{ dataFinal: { gte: inicioMes } }, { dataFinal: null }];
    } else if (mes) {
      // Se só tem mês, buscar no ano atual
      const currentYear = getCurrentYear();
      const fusoHorario = request.fusoHorario;
      const inicioMes = getStartOfMonthInTimezone(currentYear, Number(mes), fusoHorario);
      const fimMes = getEndOfMonthInTimezone(currentYear, Number(mes), fusoHorario);

      plantaoFilters.dataInicial = { lte: fimMes };
      plantaoFilters.OR = [{ dataFinal: { gte: inicioMes } }, { dataFinal: null }];
    } else if (ano) {
      // Se só tem ano, buscar plantões ativos em qualquer mês do ano
      const fusoHorario = request.fusoHorario;
      const inicioAno = getStartOfMonthInTimezone(Number(ano), 1, fusoHorario);
      const fimAno = getEndOfMonthInTimezone(Number(ano), 12, fusoHorario);

      plantaoFilters.dataInicial = { lte: fimAno };
      plantaoFilters.OR = [{ dataFinal: { gte: inicioAno } }, { dataFinal: null }];
    }
    if (profissionalId) {
      if (Array.isArray(profissionalId)) {
        plantaoFilters.profissionalId = { in: profissionalId.map(Number) };
      } else {
        plantaoFilters.profissionalId = Number(profissionalId);
      }
    }

    plantaoFilters.clienteId = clienteId;

    // Filtros por data do plantão - verificar se plantão tem interseção com período
    if (dataInicial || dataFinal) {
      const fusoHorario = request.fusoHorario;

      if (dataInicial && dataFinal) {
        // Plantão deve ter interseção com o período [dataInicial, dataFinal]
        const inicioFiltro = parseISOInTimezone(dataInicial, fusoHorario);
        const fimFiltro = parseISOInTimezone(dataFinal, fusoHorario);

        plantaoFilters.dataInicial = { lte: fimFiltro }; // plantão inicia antes ou no fim do período
        plantaoFilters.OR = [
          { dataFinal: { gte: inicioFiltro } }, // plantão termina depois ou no início do período
          { dataFinal: null }, // ou plantão sem data final (plantão permanente)
        ];
      } else if (dataInicial) {
        // Apenas data inicial - plantões que terminam depois desta data (ou são permanentes)
        const inicioFiltro = parseISOInTimezone(dataInicial, fusoHorario);
        plantaoFilters.OR = [{ dataFinal: { gte: inicioFiltro } }, { dataFinal: null }];
      } else if (dataFinal) {
        // Apenas data final - plantões que iniciam antes desta data
        const fimFiltro = parseISOInTimezone(dataFinal, fusoHorario);
        plantaoFilters.dataInicial = { lte: fimFiltro };
      }
    }

    // Se há filtros do plantão, adiciona ao where
    if (Object.keys(plantaoFilters).length > 0) {
      where.plantao = plantaoFilters;
    }

    // Filtro de busca por texto (busca em nome do profissional, cliente, local)
    // TODO: Fix search functionality - currently disabled due to query complexity
    if (false && search) {
      // If there are plantao filters, we need to combine them with the search
      if (where.plantao) {
        where.AND = [
          { plantao: where.plantao },
          {
            OR: [
              {
                plantao: {
                  profissional: {
                    usuario: {
                      nome: {
                        contains: search,
                      },
                    },
                  },
                },
              },
              {
                plantao: {
                  cliente: {
                    nome: {
                      contains: search,
                      mode: "insensitive",
                    },
                  },
                },
              },
              {
                plantao: {
                  localAtendimento: {
                    nome: {
                      contains: search,
                      mode: "insensitive",
                    },
                  },
                },
              },
            ],
          },
        ];
        delete where.plantao; // Remove the original plantao filter since it's now in AND
      } else {
        // No plantao filters, just add search
        where.OR = [
          {
            plantao: {
              profissional: {
                usuario: {
                  nome: {
                    contains: search,
                    mode: "insensitive",
                  },
                },
              },
            },
          },
          {
            plantao: {
              cliente: {
                nome: {
                  contains: search,
                  mode: "insensitive",
                },
              },
            },
          },
          {
            plantao: {
              localAtendimento: {
                nome: {
                  contains: search,
                  mode: "insensitive",
                },
              },
            },
          },
        ];
      }
    }

    const [fechamentos, total] = await Promise.all([
      prisma.fechamento.findMany({
        where,
        skip,
        take: limitNum,
        include: {
          plantao: {
            include: {
              profissional: {
                include: {
                  usuario: {
                    select: {
                      nome: true,
                      cpf: true,
                    },
                  },
                },
              },
              cliente: true,
              localAtendimento: true,
            },
          },
          antecipacao: true,
        },
        orderBy: { createdAt: "desc" },
      }),
      prisma.fechamento.count({ where }),
    ]);

    return reply.send({
      data: fechamentos,
      meta: {
        page: pageNum,
        limit: limitNum,
        total,
        totalPages: Math.ceil(total / limitNum),
      },
    });
  });

  // Buscar fechamento por UUID
  fastify.get<{ Params: { uuid: string } }>("/fechamentos/:uuid", async (request, reply) => {
    const { uuid } = request.params;
    const clienteId = request.clienteId;

    const where = {
      uuid,
      plantao: {
        clienteId,
      },
    };

    const fechamento = await prisma.fechamento.findFirst({
      where,
      include: {
        plantao: {
          include: {
            profissional: true,
            cliente: true,
            localAtendimento: true,
            diasPlantao: {
              orderBy: { data: "asc" },
            },
          },
        },
        antecipacao: true,
      },
    });

    if (!fechamento) {
      return reply.status(404).send({ error: "Fechamento não encontrado" });
    }

    return reply.send(fechamento);
  });

  // Criar fechamento
  fastify.post<{ Body: CreateFechamentoInput }>("/fechamentos", async (request, reply) => {
    const data = request.body;
    const clienteId = request.clienteId;

    // Resolver plantaoUuid para plantaoId se necessário e verificar cliente access
    let plantaoId = data.plantaoId;

    const plantaoWhere = {
      uuid: data.plantaoUuid,
      clienteId,
    };

    const plantaoByUuid = await prisma.plantao.findFirst({
      where: plantaoWhere,
      select: { id: true },
    });

    if (!plantaoByUuid) {
      return reply.status(400).send({ error: "Plantão não encontrado" });
    }

    // plantaoId = plantaoByUuid.id;

    // Resolver profissionalUuid para profissionalId se necessário
    let profissionalId = data.profissionalId;
    if (data.profissionalUuid && !profissionalId) {
      const profissional = await prisma.profissional.findUnique({
        where: { uuid: data.profissionalUuid },
        select: { id: true },
      });
      if (!profissional) {
        return reply.status(400).send({ error: "Profissional não encontrado" });
      }
      profissionalId = profissional.id;
    }

    // Verificar se o plantão existe
    const plantao = await prisma.plantao.findUnique({
      where: { id: plantaoByUuid.id },
      include: {
        diasPlantao: {
          include: {
            presencaDiaPlantao: {
              where: {
                status: "APROVADO",
              },
            },
          },
        },
      },
    });

    if (!plantao) {
      return reply.status(404).send({ error: "Plantão não encontrado" });
    }

    // Verificar se existem presenças pendentes de aprovação
    const presencasPendentes = plantao.diasPlantao.flatMap(
      (dia) => dia.presencaDiaPlantao?.filter((presenca) => presenca.status !== "APROVADO") || []
    );

    if (presencasPendentes.length > 0) {
      return reply.status(400).send({
        error: "Não é possível criar fechamento enquanto existem presenças pendentes de aprovação",
      });
    }

    const nomeUsuarioLogadoOuSistema = request.user?.nome || "Sistema";

    const fechamento = await prisma.fechamento.create({
      data: {
        plantaoId: plantaoByUuid.id,
        profissionalId: profissionalId || plantao.profissionalId,
        status: data.status || "PENDENTE",
        totalHoras: data.totalHoras,
        totalValor: data.totalValor,
        diasTrabalhados: data.diasTrabalhados,
        diasPrevistos: data.diasPrevistos,
        aprovadoPor: nomeUsuarioLogadoOuSistema,
        aprovadoEm: data.aprovadoEm,
        rejeitadoPor: data.rejeitadoPor,
        rejeitadoEm: data.rejeitadoEm,
        motivoRejeicao: data.motivoRejeicao,
        observacoes: data.observacoes,
      },
      include: {
        plantao: {
          include: {
            profissional: true,
            cliente: true,
            localAtendimento: true,
          },
        },
      },
    });

    return reply.status(201).send(fechamento);
  });

  // Atualizar fechamento
  fastify.put<{
    Params: { uuid: string };
    Body: UpdateFechamentoInput;
  }>("/fechamentos/:uuid", async (request, reply) => {
    const { uuid } = request.params;
    const data = request.body;
    const clienteId = request.clienteId;

    // Verificar se fechamento existe e pertence ao cliente
    const where = {
      uuid,
      plantao: {
        clienteId,
      },
    };

    const existingFechamento = await prisma.fechamento.findFirst({
      where,
    });

    if (!existingFechamento) {
      return reply.status(404).send({ error: "Fechamento não encontrado" });
    }

    // Não permitir edição se já foi aprovado, exceto para rejeitar
    if (existingFechamento.status === "APROVADO" && data.status === "PENDENTE") {
      return reply.status(400).send({
        error: "Não é possível alterar fechamento já aprovado para pendente",
      });
    }

    // Atualizar datas baseado no status
    const updateData = { ...data, plantaoId: existingFechamento.plantaoId };
    if (data.status === "APROVADO" && !existingFechamento.aprovadoEm) {
      updateData.aprovadoEm = getCurrentDateInTimezone(request.fusoHorario).toISOString();
    }
    if (data.status === "REJEITADO" && !existingFechamento.rejeitadoEm) {
      updateData.rejeitadoEm = getCurrentDateInTimezone(request.fusoHorario).toISOString();
    }

    const fechamento = await prisma.fechamento.update({
      where: { uuid },
      data: updateData,
      include: {
        plantao: {
          include: {
            profissional: true,
            cliente: true,
            localAtendimento: true,
          },
        },
        antecipacao: true,
      },
    });

    return reply.send(fechamento);
  });

  // Deletar fechamento
  fastify.delete<{ Params: { uuid: string } }>(
    "/fechamentos/:uuid",
    { preHandler: [authorize("master", "admin")] },
    async (request, reply) => {
      const { uuid } = request.params;
      const clienteId = request.clienteId;

      // Verificar se fechamento existe e pertence ao cliente
      const where = {
        uuid,
        plantao: {
          clienteId,
        },
      };

      const fechamento = await prisma.fechamento.findFirst({
        where,
        include: {
          antecipacao: true,
        },
      });

      if (!fechamento) {
        return reply.status(404).send({ error: "Fechamento não encontrado" });
      }

      // Não permitir deletar se tem antecipação
      if (fechamento.antecipacao) {
        return reply.status(400).send({
          error: "Não é possível excluir fechamento com antecipação associada",
        });
      }

      // Não permitir deletar se já foi aprovado
      if (fechamento.status === "APROVADO") {
        return reply.status(400).send({
          error: "Não é possível excluir fechamento já aprovado",
        });
      }

      await prisma.fechamento.delete({
        where: { uuid },
      });

      return reply.status(204).send();
    }
  );

  // Aprovar fechamento
  fastify.post<{ Params: { uuid: string } }>("/fechamentos/:uuid/aprovar", async (request, reply) => {
    const { uuid } = request.params;
    const clienteId = request.clienteId;

    const where = {
      uuid,
      plantao: {
        clienteId,
      },
    };

    const fechamento = await prisma.fechamento.findFirst({
      where,
    });

    if (!fechamento) {
      return reply.status(404).send({ error: "Fechamento não encontrado" });
    }

    if (fechamento.status !== "PENDENTE") {
      return reply.status(400).send({
        error: "Apenas fechamentos PENDENTEs podem ser APROVADOs",
      });
    }

    const updated = await prisma.fechamento.update({
      where: { uuid },
      data: {
        status: "APROVADO",
        aprovadoEm: getCurrentDateInTimezone(request.fusoHorario).toISOString(),
      },
      include: {
        plantao: {
          include: {
            profissional: true,
            cliente: true,
            localAtendimento: true,
          },
        },
        antecipacao: true,
      },
    });

    // Try to auto-complete the plantao
    try {
      await autoCompleteService.handleFechamentoApproved(updated.id);
    } catch (error) {
      console.error("Error in auto-completion:", error);
      // Don't fail the request if auto-completion fails
    }

    return reply.send(updated);
  });

  // Rejeitar fechamento
  fastify.post<{ Params: { uuid: string } }>("/fechamentos/:uuid/rejeitar", async (request, reply) => {
    const { uuid } = request.params;
    const clienteId = request.clienteId;

    const where = {
      uuid,
      plantao: {
        clienteId,
      },
    };

    const fechamento = await prisma.fechamento.findFirst({
      where,
    });

    if (!fechamento) {
      return reply.status(404).send({ error: "Fechamento não encontrado" });
    }

    if (fechamento.status === "REJEITADO") {
      return reply.status(400).send({
        error: "Fechamento já está rejeitado",
      });
    }

    const updated = await prisma.fechamento.update({
      where: { uuid },
      data: {
        status: "REJEITADO",
        rejeitadoEm: getCurrentDateInTimezone(request.fusoHorario).toISOString(),
      },
      include: {
        plantao: {
          include: {
            profissional: true,
            cliente: true,
            localAtendimento: true,
          },
        },
        antecipacao: true,
      },
    });

    // Try to revert auto-completion if the plantao was auto-completed
    try {
      await autoCompleteService.handleFechamentoRejected(updated.id);
    } catch (error) {
      console.error("Error in reverting auto-completion:", error);
      // Don't fail the request if reverting fails
    }

    return reply.send(updated);
  });

  // Calcular resumo financeiro
  fastify.get<{
    Querystring: { mes: string; ano: string; profissionalId?: string };
  }>("/fechamentos/resumo", async (request, reply) => {
    const { mes, ano, profissionalId } = request.query;
    const clienteId = request.clienteId;

    // Filtrar fechamentos por plantões ativos no mês/ano
    const fusoHorario = request.fusoHorario;
    const inicioMes = getStartOfMonthInTimezone(Number(ano), Number(mes), fusoHorario);
    const fimMes = getEndOfMonthInTimezone(Number(ano), Number(mes), fusoHorario);

    const fechamentos = await prisma.fechamento.findMany({
      where: {
        plantao: {
          dataInicial: { lte: fimMes },
          OR: [{ dataFinal: { gte: inicioMes } }, { dataFinal: null }],
          ...(profissionalId && { profissionalId: Number(profissionalId) }),
          clienteId,
        },
      },
      include: {
        plantao: {
          include: {
            profissional: true,
            cliente: true,
          },
        },
        antecipacao: true,
      },
    });

    const resumo = {
      totalValor: fechamentos.reduce((sum: number, f: any) => sum + f.totalValor, 0),
      totalHoras: fechamentos.reduce((sum: number, f: any) => sum + (f.totalHoras || 0), 0),
      totalAntecipacoes: fechamentos.reduce(
        (sum: number, f: any) => sum + (f.antecipacao ? f.antecipacao.valorSolicitado : 0),
        0
      ),
      quantidadeFechamentos: fechamentos.length,
      porStatus: {
        PENDENTES: fechamentos.filter((f: any) => f.status === "PENDENTE").length,
        APROVADOS: fechamentos.filter((f: any) => f.status === "APROVADO").length,
        REJEITADOS: fechamentos.filter((f: any) => f.status === "REJEITADO").length,
      },
    };

    return reply.send(resumo);
  });
}
