# Dependencies
node_modules/
.pnp
.pnp.js

# Build outputs
dist/
build/
out/
dev-dist/
.next/
.nuxt/

# Generated files
apps/server/generated/
apps/server/prisma/generated/
apps/web/.react-router/
apps/web/src/routeTree.gen.ts
*.generated.*

# Cache directories
.cache/
.turbo/
.parcel-cache/

# IDE
.vscode/
.idea/

# Misc
*.min.js
*.min.css
coverage/
*.log
.DS_Store
.env*
!.env.example

# Package manager
package-lock.json
yarn.lock
pnpm-lock.yaml

apps/shared/src/types/prisma/*