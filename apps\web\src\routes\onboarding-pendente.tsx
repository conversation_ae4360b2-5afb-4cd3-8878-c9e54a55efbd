import { createFileRoute, useNavigate } from "@tanstack/react-router";
// import { Button } from "@/components/ui/button";
import {
  // CardFooter,
  Card,
  CardContent,
  CardDescription,
  CardHeader,
  CardTitle,
} from "@/components/ui/card";
import {
  // LogOut
  Clock,
  Mail,
  CheckCircle2,
} from "lucide-react";
import { useMutation } from "@tanstack/react-query";
import { toast } from "sonner";
import { useAuthStore } from "@/stores/use-auth.store";

function OnboardingPendente() {
  const navigate = useNavigate();
  const logout = useAuthStore((state) => state.logout);

  const logoutMutation = useMutation({
    mutationFn: async () => {
      // Limpar sessão usando o store
      logout();
      sessionStorage.clear();
      return { success: true };
    },
    onSuccess: () => {
      toast.success("Logout realizado com sucesso!");
      navigate({ to: "/login" });
    },
  });

  // const handleLogout = () => {
  //   logoutMutation.mutate();
  // };

  return (
    <div className="min-h-screen flex items-center justify-center from-blue-50 to-indigo-100 p-4">
      <Card className="w-full max-w-2xl">
        <CardHeader className="text-center">
          <div className="flex justify-center mb-4">
            <div className="rounded-full bg-green-100 p-4">
              <CheckCircle2 className="h-12 w-12 text-green-600" />
            </div>
          </div>
          <CardTitle className="text-3xl">Cadastro Realizado com Sucesso!</CardTitle>
          <CardDescription className="text-base mt-2">
            Seus dados foram recebidos e estão sendo analisados
          </CardDescription>
        </CardHeader>

        <CardContent className="space-y-6">
          <div className="bg-blue-50 rounded-lg p-6">
            <div className="flex items-start gap-4">
              <Clock className="h-6 w-6 text-blue-600 mt-1 flex-shrink-0" />
              <div className="space-y-2">
                <h3 className="font-semibold text-lg text-muted-foreground">Próximos Passos</h3>
                <p className="text-muted-foreground">
                  Nossa equipe está finalizando a configuração da sua conta profissional. Este processo geralmente leva
                  até <strong>48 horas úteis</strong>.
                </p>
              </div>
            </div>
          </div>

          <div className="bg-amber-50 rounded-lg p-6">
            <div className="flex items-start gap-4">
              <Mail className="h-6 w-6 text-amber-600 mt-1 flex-shrink-0" />
              <div className="space-y-2">
                <h3 className="font-semibold text-lg text-muted-foreground">Você será notificado</h3>
                <p className="text-muted-foreground">
                  Assim que sua conta estiver completamente configurada, você receberá um
                  <strong> e-mail de confirmação</strong> com as instruções para acessar todas as funcionalidades da
                  plataforma.
                </p>
              </div>
            </div>
          </div>

          <div className="border rounded-lg p-6 space-y-3">
            <h3 className="font-semibold">O que acontece agora?</h3>
            <ul className="space-y-2 text-sm text-muted-foreground">
              <li className="flex items-start gap-2">
                <span className="text-green-600 mt-0.5">✓</span>
                <span>Seus documentos e informações profissionais serão verificados</span>
              </li>
              <li className="flex items-start gap-2">
                <span className="text-green-600 mt-0.5">✓</span>
                <span>Suas credenciais de acesso serão configuradas no sistema</span>
              </li>
              <li className="flex items-start gap-2">
                <span className="text-green-600 mt-0.5">✓</span>
                <span>Você será vinculado aos clientes e locais de atendimento apropriados</span>
              </li>
              <li className="flex items-start gap-2">
                <span className="text-green-600 mt-0.5">✓</span>
                <span>Sua escala de plantões será configurada conforme disponibilidade</span>
              </li>
            </ul>
          </div>

          <div className="bg-gray-50 rounded-lg p-4 text-center">
            <p className="text-sm text-muted-foreground">
              Enquanto isso, você pode fechar esta janela com segurança. Entraremos em contato assim que tudo estiver
              pronto!
            </p>
          </div>
        </CardContent>

        {/* <CardFooter>
          <Button onClick={handleLogout} variant="outline" className="w-full">
            <LogOut className="mr-2 h-4 w-4" />
            Sair da Plataforma
          </Button>
        </CardFooter> */}
      </Card>
    </div>
  );
}

export const Route = createFileRoute("/onboarding-pendente")({
  component: OnboardingPendente,
});
