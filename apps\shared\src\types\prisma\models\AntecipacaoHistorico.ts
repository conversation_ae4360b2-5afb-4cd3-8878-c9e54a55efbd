
/* !!! This is code generated by Prisma. Do not edit directly. !!! */
/* eslint-disable */
// @ts-nocheck 
/*
 * This file exports the `AntecipacaoHistorico` model and its related types.
 *
 * 🟢 You can import this file directly.
 */
import * as runtime from "@prisma/client/runtime/library"
import type * as $Enums from "../enums.ts"
import type * as Prisma from "../internal/prismaNamespace.ts"

/**
 * Model AntecipacaoHistorico
 * 
 */
export type AntecipacaoHistoricoModel = runtime.Types.Result.DefaultSelection<Prisma.$AntecipacaoHistoricoPayload>

export type AggregateAntecipacaoHistorico = {
  _count: AntecipacaoHistoricoCountAggregateOutputType | null
  _avg: AntecipacaoHistoricoAvgAggregateOutputType | null
  _sum: AntecipacaoHistoricoSumAggregateOutputType | null
  _min: AntecipacaoHistoricoMinAggregateOutputType | null
  _max: AntecipacaoHistoricoMaxAggregateOutputType | null
}

export type AntecipacaoHistoricoAvgAggregateOutputType = {
  id: number | null
  antecipacaoId: number | null
}

export type AntecipacaoHistoricoSumAggregateOutputType = {
  id: number | null
  antecipacaoId: number | null
}

export type AntecipacaoHistoricoMinAggregateOutputType = {
  id: number | null
  antecipacaoId: number | null
  status: string | null
  createdAt: Date | null
}

export type AntecipacaoHistoricoMaxAggregateOutputType = {
  id: number | null
  antecipacaoId: number | null
  status: string | null
  createdAt: Date | null
}

export type AntecipacaoHistoricoCountAggregateOutputType = {
  id: number
  antecipacaoId: number
  status: number
  metaData: number
  createdAt: number
  _all: number
}


export type AntecipacaoHistoricoAvgAggregateInputType = {
  id?: true
  antecipacaoId?: true
}

export type AntecipacaoHistoricoSumAggregateInputType = {
  id?: true
  antecipacaoId?: true
}

export type AntecipacaoHistoricoMinAggregateInputType = {
  id?: true
  antecipacaoId?: true
  status?: true
  createdAt?: true
}

export type AntecipacaoHistoricoMaxAggregateInputType = {
  id?: true
  antecipacaoId?: true
  status?: true
  createdAt?: true
}

export type AntecipacaoHistoricoCountAggregateInputType = {
  id?: true
  antecipacaoId?: true
  status?: true
  metaData?: true
  createdAt?: true
  _all?: true
}

export type AntecipacaoHistoricoAggregateArgs<ExtArgs extends runtime.Types.Extensions.InternalArgs = runtime.Types.Extensions.DefaultArgs> = {
  /**
   * Filter which AntecipacaoHistorico to aggregate.
   */
  where?: Prisma.AntecipacaoHistoricoWhereInput
  /**
   * {@link https://www.prisma.io/docs/concepts/components/prisma-client/sorting Sorting Docs}
   * 
   * Determine the order of AntecipacaoHistoricos to fetch.
   */
  orderBy?: Prisma.AntecipacaoHistoricoOrderByWithRelationInput | Prisma.AntecipacaoHistoricoOrderByWithRelationInput[]
  /**
   * {@link https://www.prisma.io/docs/concepts/components/prisma-client/pagination#cursor-based-pagination Cursor Docs}
   * 
   * Sets the start position
   */
  cursor?: Prisma.AntecipacaoHistoricoWhereUniqueInput
  /**
   * {@link https://www.prisma.io/docs/concepts/components/prisma-client/pagination Pagination Docs}
   * 
   * Take `±n` AntecipacaoHistoricos from the position of the cursor.
   */
  take?: number
  /**
   * {@link https://www.prisma.io/docs/concepts/components/prisma-client/pagination Pagination Docs}
   * 
   * Skip the first `n` AntecipacaoHistoricos.
   */
  skip?: number
  /**
   * {@link https://www.prisma.io/docs/concepts/components/prisma-client/aggregations Aggregation Docs}
   * 
   * Count returned AntecipacaoHistoricos
  **/
  _count?: true | AntecipacaoHistoricoCountAggregateInputType
  /**
   * {@link https://www.prisma.io/docs/concepts/components/prisma-client/aggregations Aggregation Docs}
   * 
   * Select which fields to average
  **/
  _avg?: AntecipacaoHistoricoAvgAggregateInputType
  /**
   * {@link https://www.prisma.io/docs/concepts/components/prisma-client/aggregations Aggregation Docs}
   * 
   * Select which fields to sum
  **/
  _sum?: AntecipacaoHistoricoSumAggregateInputType
  /**
   * {@link https://www.prisma.io/docs/concepts/components/prisma-client/aggregations Aggregation Docs}
   * 
   * Select which fields to find the minimum value
  **/
  _min?: AntecipacaoHistoricoMinAggregateInputType
  /**
   * {@link https://www.prisma.io/docs/concepts/components/prisma-client/aggregations Aggregation Docs}
   * 
   * Select which fields to find the maximum value
  **/
  _max?: AntecipacaoHistoricoMaxAggregateInputType
}

export type GetAntecipacaoHistoricoAggregateType<T extends AntecipacaoHistoricoAggregateArgs> = {
      [P in keyof T & keyof AggregateAntecipacaoHistorico]: P extends '_count' | 'count'
    ? T[P] extends true
      ? number
      : Prisma.GetScalarType<T[P], AggregateAntecipacaoHistorico[P]>
    : Prisma.GetScalarType<T[P], AggregateAntecipacaoHistorico[P]>
}




export type AntecipacaoHistoricoGroupByArgs<ExtArgs extends runtime.Types.Extensions.InternalArgs = runtime.Types.Extensions.DefaultArgs> = {
  where?: Prisma.AntecipacaoHistoricoWhereInput
  orderBy?: Prisma.AntecipacaoHistoricoOrderByWithAggregationInput | Prisma.AntecipacaoHistoricoOrderByWithAggregationInput[]
  by: Prisma.AntecipacaoHistoricoScalarFieldEnum[] | Prisma.AntecipacaoHistoricoScalarFieldEnum
  having?: Prisma.AntecipacaoHistoricoScalarWhereWithAggregatesInput
  take?: number
  skip?: number
  _count?: AntecipacaoHistoricoCountAggregateInputType | true
  _avg?: AntecipacaoHistoricoAvgAggregateInputType
  _sum?: AntecipacaoHistoricoSumAggregateInputType
  _min?: AntecipacaoHistoricoMinAggregateInputType
  _max?: AntecipacaoHistoricoMaxAggregateInputType
}

export type AntecipacaoHistoricoGroupByOutputType = {
  id: number
  antecipacaoId: number
  status: string
  metaData: runtime.JsonValue | null
  createdAt: Date
  _count: AntecipacaoHistoricoCountAggregateOutputType | null
  _avg: AntecipacaoHistoricoAvgAggregateOutputType | null
  _sum: AntecipacaoHistoricoSumAggregateOutputType | null
  _min: AntecipacaoHistoricoMinAggregateOutputType | null
  _max: AntecipacaoHistoricoMaxAggregateOutputType | null
}

type GetAntecipacaoHistoricoGroupByPayload<T extends AntecipacaoHistoricoGroupByArgs> = Prisma.PrismaPromise<
  Array<
    Prisma.PickEnumerable<AntecipacaoHistoricoGroupByOutputType, T['by']> &
      {
        [P in ((keyof T) & (keyof AntecipacaoHistoricoGroupByOutputType))]: P extends '_count'
          ? T[P] extends boolean
            ? number
            : Prisma.GetScalarType<T[P], AntecipacaoHistoricoGroupByOutputType[P]>
          : Prisma.GetScalarType<T[P], AntecipacaoHistoricoGroupByOutputType[P]>
      }
    >
  >



export type AntecipacaoHistoricoWhereInput = {
  AND?: Prisma.AntecipacaoHistoricoWhereInput | Prisma.AntecipacaoHistoricoWhereInput[]
  OR?: Prisma.AntecipacaoHistoricoWhereInput[]
  NOT?: Prisma.AntecipacaoHistoricoWhereInput | Prisma.AntecipacaoHistoricoWhereInput[]
  id?: Prisma.IntFilter<"AntecipacaoHistorico"> | number
  antecipacaoId?: Prisma.IntFilter<"AntecipacaoHistorico"> | number
  status?: Prisma.StringFilter<"AntecipacaoHistorico"> | string
  metaData?: Prisma.JsonNullableFilter<"AntecipacaoHistorico">
  createdAt?: Prisma.DateTimeFilter<"AntecipacaoHistorico"> | Date | string
  antecipacao?: Prisma.XOR<Prisma.AntecipacaoScalarRelationFilter, Prisma.AntecipacaoWhereInput>
}

export type AntecipacaoHistoricoOrderByWithRelationInput = {
  id?: Prisma.SortOrder
  antecipacaoId?: Prisma.SortOrder
  status?: Prisma.SortOrder
  metaData?: Prisma.SortOrderInput | Prisma.SortOrder
  createdAt?: Prisma.SortOrder
  antecipacao?: Prisma.AntecipacaoOrderByWithRelationInput
  _relevance?: Prisma.AntecipacaoHistoricoOrderByRelevanceInput
}

export type AntecipacaoHistoricoWhereUniqueInput = Prisma.AtLeast<{
  id?: number
  AND?: Prisma.AntecipacaoHistoricoWhereInput | Prisma.AntecipacaoHistoricoWhereInput[]
  OR?: Prisma.AntecipacaoHistoricoWhereInput[]
  NOT?: Prisma.AntecipacaoHistoricoWhereInput | Prisma.AntecipacaoHistoricoWhereInput[]
  antecipacaoId?: Prisma.IntFilter<"AntecipacaoHistorico"> | number
  status?: Prisma.StringFilter<"AntecipacaoHistorico"> | string
  metaData?: Prisma.JsonNullableFilter<"AntecipacaoHistorico">
  createdAt?: Prisma.DateTimeFilter<"AntecipacaoHistorico"> | Date | string
  antecipacao?: Prisma.XOR<Prisma.AntecipacaoScalarRelationFilter, Prisma.AntecipacaoWhereInput>
}, "id">

export type AntecipacaoHistoricoOrderByWithAggregationInput = {
  id?: Prisma.SortOrder
  antecipacaoId?: Prisma.SortOrder
  status?: Prisma.SortOrder
  metaData?: Prisma.SortOrderInput | Prisma.SortOrder
  createdAt?: Prisma.SortOrder
  _count?: Prisma.AntecipacaoHistoricoCountOrderByAggregateInput
  _avg?: Prisma.AntecipacaoHistoricoAvgOrderByAggregateInput
  _max?: Prisma.AntecipacaoHistoricoMaxOrderByAggregateInput
  _min?: Prisma.AntecipacaoHistoricoMinOrderByAggregateInput
  _sum?: Prisma.AntecipacaoHistoricoSumOrderByAggregateInput
}

export type AntecipacaoHistoricoScalarWhereWithAggregatesInput = {
  AND?: Prisma.AntecipacaoHistoricoScalarWhereWithAggregatesInput | Prisma.AntecipacaoHistoricoScalarWhereWithAggregatesInput[]
  OR?: Prisma.AntecipacaoHistoricoScalarWhereWithAggregatesInput[]
  NOT?: Prisma.AntecipacaoHistoricoScalarWhereWithAggregatesInput | Prisma.AntecipacaoHistoricoScalarWhereWithAggregatesInput[]
  id?: Prisma.IntWithAggregatesFilter<"AntecipacaoHistorico"> | number
  antecipacaoId?: Prisma.IntWithAggregatesFilter<"AntecipacaoHistorico"> | number
  status?: Prisma.StringWithAggregatesFilter<"AntecipacaoHistorico"> | string
  metaData?: Prisma.JsonNullableWithAggregatesFilter<"AntecipacaoHistorico">
  createdAt?: Prisma.DateTimeWithAggregatesFilter<"AntecipacaoHistorico"> | Date | string
}

export type AntecipacaoHistoricoCreateInput = {
  status: string
  metaData?: Prisma.NullableJsonNullValueInput | runtime.InputJsonValue
  createdAt?: Date | string
  antecipacao: Prisma.AntecipacaoCreateNestedOneWithoutHistoricoInput
}

export type AntecipacaoHistoricoUncheckedCreateInput = {
  id?: number
  antecipacaoId: number
  status: string
  metaData?: Prisma.NullableJsonNullValueInput | runtime.InputJsonValue
  createdAt?: Date | string
}

export type AntecipacaoHistoricoUpdateInput = {
  status?: Prisma.StringFieldUpdateOperationsInput | string
  metaData?: Prisma.NullableJsonNullValueInput | runtime.InputJsonValue
  createdAt?: Prisma.DateTimeFieldUpdateOperationsInput | Date | string
  antecipacao?: Prisma.AntecipacaoUpdateOneRequiredWithoutHistoricoNestedInput
}

export type AntecipacaoHistoricoUncheckedUpdateInput = {
  id?: Prisma.IntFieldUpdateOperationsInput | number
  antecipacaoId?: Prisma.IntFieldUpdateOperationsInput | number
  status?: Prisma.StringFieldUpdateOperationsInput | string
  metaData?: Prisma.NullableJsonNullValueInput | runtime.InputJsonValue
  createdAt?: Prisma.DateTimeFieldUpdateOperationsInput | Date | string
}

export type AntecipacaoHistoricoCreateManyInput = {
  id?: number
  antecipacaoId: number
  status: string
  metaData?: Prisma.NullableJsonNullValueInput | runtime.InputJsonValue
  createdAt?: Date | string
}

export type AntecipacaoHistoricoUpdateManyMutationInput = {
  status?: Prisma.StringFieldUpdateOperationsInput | string
  metaData?: Prisma.NullableJsonNullValueInput | runtime.InputJsonValue
  createdAt?: Prisma.DateTimeFieldUpdateOperationsInput | Date | string
}

export type AntecipacaoHistoricoUncheckedUpdateManyInput = {
  id?: Prisma.IntFieldUpdateOperationsInput | number
  antecipacaoId?: Prisma.IntFieldUpdateOperationsInput | number
  status?: Prisma.StringFieldUpdateOperationsInput | string
  metaData?: Prisma.NullableJsonNullValueInput | runtime.InputJsonValue
  createdAt?: Prisma.DateTimeFieldUpdateOperationsInput | Date | string
}

export type AntecipacaoHistoricoListRelationFilter = {
  every?: Prisma.AntecipacaoHistoricoWhereInput
  some?: Prisma.AntecipacaoHistoricoWhereInput
  none?: Prisma.AntecipacaoHistoricoWhereInput
}

export type AntecipacaoHistoricoOrderByRelationAggregateInput = {
  _count?: Prisma.SortOrder
}

export type AntecipacaoHistoricoOrderByRelevanceInput = {
  fields: Prisma.AntecipacaoHistoricoOrderByRelevanceFieldEnum | Prisma.AntecipacaoHistoricoOrderByRelevanceFieldEnum[]
  sort: Prisma.SortOrder
  search: string
}

export type AntecipacaoHistoricoCountOrderByAggregateInput = {
  id?: Prisma.SortOrder
  antecipacaoId?: Prisma.SortOrder
  status?: Prisma.SortOrder
  metaData?: Prisma.SortOrder
  createdAt?: Prisma.SortOrder
}

export type AntecipacaoHistoricoAvgOrderByAggregateInput = {
  id?: Prisma.SortOrder
  antecipacaoId?: Prisma.SortOrder
}

export type AntecipacaoHistoricoMaxOrderByAggregateInput = {
  id?: Prisma.SortOrder
  antecipacaoId?: Prisma.SortOrder
  status?: Prisma.SortOrder
  createdAt?: Prisma.SortOrder
}

export type AntecipacaoHistoricoMinOrderByAggregateInput = {
  id?: Prisma.SortOrder
  antecipacaoId?: Prisma.SortOrder
  status?: Prisma.SortOrder
  createdAt?: Prisma.SortOrder
}

export type AntecipacaoHistoricoSumOrderByAggregateInput = {
  id?: Prisma.SortOrder
  antecipacaoId?: Prisma.SortOrder
}

export type AntecipacaoHistoricoCreateNestedManyWithoutAntecipacaoInput = {
  create?: Prisma.XOR<Prisma.AntecipacaoHistoricoCreateWithoutAntecipacaoInput, Prisma.AntecipacaoHistoricoUncheckedCreateWithoutAntecipacaoInput> | Prisma.AntecipacaoHistoricoCreateWithoutAntecipacaoInput[] | Prisma.AntecipacaoHistoricoUncheckedCreateWithoutAntecipacaoInput[]
  connectOrCreate?: Prisma.AntecipacaoHistoricoCreateOrConnectWithoutAntecipacaoInput | Prisma.AntecipacaoHistoricoCreateOrConnectWithoutAntecipacaoInput[]
  createMany?: Prisma.AntecipacaoHistoricoCreateManyAntecipacaoInputEnvelope
  connect?: Prisma.AntecipacaoHistoricoWhereUniqueInput | Prisma.AntecipacaoHistoricoWhereUniqueInput[]
}

export type AntecipacaoHistoricoUncheckedCreateNestedManyWithoutAntecipacaoInput = {
  create?: Prisma.XOR<Prisma.AntecipacaoHistoricoCreateWithoutAntecipacaoInput, Prisma.AntecipacaoHistoricoUncheckedCreateWithoutAntecipacaoInput> | Prisma.AntecipacaoHistoricoCreateWithoutAntecipacaoInput[] | Prisma.AntecipacaoHistoricoUncheckedCreateWithoutAntecipacaoInput[]
  connectOrCreate?: Prisma.AntecipacaoHistoricoCreateOrConnectWithoutAntecipacaoInput | Prisma.AntecipacaoHistoricoCreateOrConnectWithoutAntecipacaoInput[]
  createMany?: Prisma.AntecipacaoHistoricoCreateManyAntecipacaoInputEnvelope
  connect?: Prisma.AntecipacaoHistoricoWhereUniqueInput | Prisma.AntecipacaoHistoricoWhereUniqueInput[]
}

export type AntecipacaoHistoricoUpdateManyWithoutAntecipacaoNestedInput = {
  create?: Prisma.XOR<Prisma.AntecipacaoHistoricoCreateWithoutAntecipacaoInput, Prisma.AntecipacaoHistoricoUncheckedCreateWithoutAntecipacaoInput> | Prisma.AntecipacaoHistoricoCreateWithoutAntecipacaoInput[] | Prisma.AntecipacaoHistoricoUncheckedCreateWithoutAntecipacaoInput[]
  connectOrCreate?: Prisma.AntecipacaoHistoricoCreateOrConnectWithoutAntecipacaoInput | Prisma.AntecipacaoHistoricoCreateOrConnectWithoutAntecipacaoInput[]
  upsert?: Prisma.AntecipacaoHistoricoUpsertWithWhereUniqueWithoutAntecipacaoInput | Prisma.AntecipacaoHistoricoUpsertWithWhereUniqueWithoutAntecipacaoInput[]
  createMany?: Prisma.AntecipacaoHistoricoCreateManyAntecipacaoInputEnvelope
  set?: Prisma.AntecipacaoHistoricoWhereUniqueInput | Prisma.AntecipacaoHistoricoWhereUniqueInput[]
  disconnect?: Prisma.AntecipacaoHistoricoWhereUniqueInput | Prisma.AntecipacaoHistoricoWhereUniqueInput[]
  delete?: Prisma.AntecipacaoHistoricoWhereUniqueInput | Prisma.AntecipacaoHistoricoWhereUniqueInput[]
  connect?: Prisma.AntecipacaoHistoricoWhereUniqueInput | Prisma.AntecipacaoHistoricoWhereUniqueInput[]
  update?: Prisma.AntecipacaoHistoricoUpdateWithWhereUniqueWithoutAntecipacaoInput | Prisma.AntecipacaoHistoricoUpdateWithWhereUniqueWithoutAntecipacaoInput[]
  updateMany?: Prisma.AntecipacaoHistoricoUpdateManyWithWhereWithoutAntecipacaoInput | Prisma.AntecipacaoHistoricoUpdateManyWithWhereWithoutAntecipacaoInput[]
  deleteMany?: Prisma.AntecipacaoHistoricoScalarWhereInput | Prisma.AntecipacaoHistoricoScalarWhereInput[]
}

export type AntecipacaoHistoricoUncheckedUpdateManyWithoutAntecipacaoNestedInput = {
  create?: Prisma.XOR<Prisma.AntecipacaoHistoricoCreateWithoutAntecipacaoInput, Prisma.AntecipacaoHistoricoUncheckedCreateWithoutAntecipacaoInput> | Prisma.AntecipacaoHistoricoCreateWithoutAntecipacaoInput[] | Prisma.AntecipacaoHistoricoUncheckedCreateWithoutAntecipacaoInput[]
  connectOrCreate?: Prisma.AntecipacaoHistoricoCreateOrConnectWithoutAntecipacaoInput | Prisma.AntecipacaoHistoricoCreateOrConnectWithoutAntecipacaoInput[]
  upsert?: Prisma.AntecipacaoHistoricoUpsertWithWhereUniqueWithoutAntecipacaoInput | Prisma.AntecipacaoHistoricoUpsertWithWhereUniqueWithoutAntecipacaoInput[]
  createMany?: Prisma.AntecipacaoHistoricoCreateManyAntecipacaoInputEnvelope
  set?: Prisma.AntecipacaoHistoricoWhereUniqueInput | Prisma.AntecipacaoHistoricoWhereUniqueInput[]
  disconnect?: Prisma.AntecipacaoHistoricoWhereUniqueInput | Prisma.AntecipacaoHistoricoWhereUniqueInput[]
  delete?: Prisma.AntecipacaoHistoricoWhereUniqueInput | Prisma.AntecipacaoHistoricoWhereUniqueInput[]
  connect?: Prisma.AntecipacaoHistoricoWhereUniqueInput | Prisma.AntecipacaoHistoricoWhereUniqueInput[]
  update?: Prisma.AntecipacaoHistoricoUpdateWithWhereUniqueWithoutAntecipacaoInput | Prisma.AntecipacaoHistoricoUpdateWithWhereUniqueWithoutAntecipacaoInput[]
  updateMany?: Prisma.AntecipacaoHistoricoUpdateManyWithWhereWithoutAntecipacaoInput | Prisma.AntecipacaoHistoricoUpdateManyWithWhereWithoutAntecipacaoInput[]
  deleteMany?: Prisma.AntecipacaoHistoricoScalarWhereInput | Prisma.AntecipacaoHistoricoScalarWhereInput[]
}

export type AntecipacaoHistoricoCreateWithoutAntecipacaoInput = {
  status: string
  metaData?: Prisma.NullableJsonNullValueInput | runtime.InputJsonValue
  createdAt?: Date | string
}

export type AntecipacaoHistoricoUncheckedCreateWithoutAntecipacaoInput = {
  id?: number
  status: string
  metaData?: Prisma.NullableJsonNullValueInput | runtime.InputJsonValue
  createdAt?: Date | string
}

export type AntecipacaoHistoricoCreateOrConnectWithoutAntecipacaoInput = {
  where: Prisma.AntecipacaoHistoricoWhereUniqueInput
  create: Prisma.XOR<Prisma.AntecipacaoHistoricoCreateWithoutAntecipacaoInput, Prisma.AntecipacaoHistoricoUncheckedCreateWithoutAntecipacaoInput>
}

export type AntecipacaoHistoricoCreateManyAntecipacaoInputEnvelope = {
  data: Prisma.AntecipacaoHistoricoCreateManyAntecipacaoInput | Prisma.AntecipacaoHistoricoCreateManyAntecipacaoInput[]
  skipDuplicates?: boolean
}

export type AntecipacaoHistoricoUpsertWithWhereUniqueWithoutAntecipacaoInput = {
  where: Prisma.AntecipacaoHistoricoWhereUniqueInput
  update: Prisma.XOR<Prisma.AntecipacaoHistoricoUpdateWithoutAntecipacaoInput, Prisma.AntecipacaoHistoricoUncheckedUpdateWithoutAntecipacaoInput>
  create: Prisma.XOR<Prisma.AntecipacaoHistoricoCreateWithoutAntecipacaoInput, Prisma.AntecipacaoHistoricoUncheckedCreateWithoutAntecipacaoInput>
}

export type AntecipacaoHistoricoUpdateWithWhereUniqueWithoutAntecipacaoInput = {
  where: Prisma.AntecipacaoHistoricoWhereUniqueInput
  data: Prisma.XOR<Prisma.AntecipacaoHistoricoUpdateWithoutAntecipacaoInput, Prisma.AntecipacaoHistoricoUncheckedUpdateWithoutAntecipacaoInput>
}

export type AntecipacaoHistoricoUpdateManyWithWhereWithoutAntecipacaoInput = {
  where: Prisma.AntecipacaoHistoricoScalarWhereInput
  data: Prisma.XOR<Prisma.AntecipacaoHistoricoUpdateManyMutationInput, Prisma.AntecipacaoHistoricoUncheckedUpdateManyWithoutAntecipacaoInput>
}

export type AntecipacaoHistoricoScalarWhereInput = {
  AND?: Prisma.AntecipacaoHistoricoScalarWhereInput | Prisma.AntecipacaoHistoricoScalarWhereInput[]
  OR?: Prisma.AntecipacaoHistoricoScalarWhereInput[]
  NOT?: Prisma.AntecipacaoHistoricoScalarWhereInput | Prisma.AntecipacaoHistoricoScalarWhereInput[]
  id?: Prisma.IntFilter<"AntecipacaoHistorico"> | number
  antecipacaoId?: Prisma.IntFilter<"AntecipacaoHistorico"> | number
  status?: Prisma.StringFilter<"AntecipacaoHistorico"> | string
  metaData?: Prisma.JsonNullableFilter<"AntecipacaoHistorico">
  createdAt?: Prisma.DateTimeFilter<"AntecipacaoHistorico"> | Date | string
}

export type AntecipacaoHistoricoCreateManyAntecipacaoInput = {
  id?: number
  status: string
  metaData?: Prisma.NullableJsonNullValueInput | runtime.InputJsonValue
  createdAt?: Date | string
}

export type AntecipacaoHistoricoUpdateWithoutAntecipacaoInput = {
  status?: Prisma.StringFieldUpdateOperationsInput | string
  metaData?: Prisma.NullableJsonNullValueInput | runtime.InputJsonValue
  createdAt?: Prisma.DateTimeFieldUpdateOperationsInput | Date | string
}

export type AntecipacaoHistoricoUncheckedUpdateWithoutAntecipacaoInput = {
  id?: Prisma.IntFieldUpdateOperationsInput | number
  status?: Prisma.StringFieldUpdateOperationsInput | string
  metaData?: Prisma.NullableJsonNullValueInput | runtime.InputJsonValue
  createdAt?: Prisma.DateTimeFieldUpdateOperationsInput | Date | string
}

export type AntecipacaoHistoricoUncheckedUpdateManyWithoutAntecipacaoInput = {
  id?: Prisma.IntFieldUpdateOperationsInput | number
  status?: Prisma.StringFieldUpdateOperationsInput | string
  metaData?: Prisma.NullableJsonNullValueInput | runtime.InputJsonValue
  createdAt?: Prisma.DateTimeFieldUpdateOperationsInput | Date | string
}



export type AntecipacaoHistoricoSelect<ExtArgs extends runtime.Types.Extensions.InternalArgs = runtime.Types.Extensions.DefaultArgs> = runtime.Types.Extensions.GetSelect<{
  id?: boolean
  antecipacaoId?: boolean
  status?: boolean
  metaData?: boolean
  createdAt?: boolean
  antecipacao?: boolean | Prisma.AntecipacaoDefaultArgs<ExtArgs>
}, ExtArgs["result"]["antecipacaoHistorico"]>



export type AntecipacaoHistoricoSelectScalar = {
  id?: boolean
  antecipacaoId?: boolean
  status?: boolean
  metaData?: boolean
  createdAt?: boolean
}

export type AntecipacaoHistoricoOmit<ExtArgs extends runtime.Types.Extensions.InternalArgs = runtime.Types.Extensions.DefaultArgs> = runtime.Types.Extensions.GetOmit<"id" | "antecipacaoId" | "status" | "metaData" | "createdAt", ExtArgs["result"]["antecipacaoHistorico"]>
export type AntecipacaoHistoricoInclude<ExtArgs extends runtime.Types.Extensions.InternalArgs = runtime.Types.Extensions.DefaultArgs> = {
  antecipacao?: boolean | Prisma.AntecipacaoDefaultArgs<ExtArgs>
}

export type $AntecipacaoHistoricoPayload<ExtArgs extends runtime.Types.Extensions.InternalArgs = runtime.Types.Extensions.DefaultArgs> = {
  name: "AntecipacaoHistorico"
  objects: {
    antecipacao: Prisma.$AntecipacaoPayload<ExtArgs>
  }
  scalars: runtime.Types.Extensions.GetPayloadResult<{
    id: number
    antecipacaoId: number
    status: string
    metaData: runtime.JsonValue | null
    createdAt: Date
  }, ExtArgs["result"]["antecipacaoHistorico"]>
  composites: {}
}

export type AntecipacaoHistoricoGetPayload<S extends boolean | null | undefined | AntecipacaoHistoricoDefaultArgs> = runtime.Types.Result.GetResult<Prisma.$AntecipacaoHistoricoPayload, S>

export type AntecipacaoHistoricoCountArgs<ExtArgs extends runtime.Types.Extensions.InternalArgs = runtime.Types.Extensions.DefaultArgs> =
  Omit<AntecipacaoHistoricoFindManyArgs, 'select' | 'include' | 'distinct' | 'omit'> & {
    select?: AntecipacaoHistoricoCountAggregateInputType | true
  }

export interface AntecipacaoHistoricoDelegate<ExtArgs extends runtime.Types.Extensions.InternalArgs = runtime.Types.Extensions.DefaultArgs, GlobalOmitOptions = {}> {
  [K: symbol]: { types: Prisma.TypeMap<ExtArgs>['model']['AntecipacaoHistorico'], meta: { name: 'AntecipacaoHistorico' } }
  /**
   * Find zero or one AntecipacaoHistorico that matches the filter.
   * @param {AntecipacaoHistoricoFindUniqueArgs} args - Arguments to find a AntecipacaoHistorico
   * @example
   * // Get one AntecipacaoHistorico
   * const antecipacaoHistorico = await prisma.antecipacaoHistorico.findUnique({
   *   where: {
   *     // ... provide filter here
   *   }
   * })
   */
  findUnique<T extends AntecipacaoHistoricoFindUniqueArgs>(args: Prisma.SelectSubset<T, AntecipacaoHistoricoFindUniqueArgs<ExtArgs>>): Prisma.Prisma__AntecipacaoHistoricoClient<runtime.Types.Result.GetResult<Prisma.$AntecipacaoHistoricoPayload<ExtArgs>, T, "findUnique", GlobalOmitOptions> | null, null, ExtArgs, GlobalOmitOptions>

  /**
   * Find one AntecipacaoHistorico that matches the filter or throw an error with `error.code='P2025'`
   * if no matches were found.
   * @param {AntecipacaoHistoricoFindUniqueOrThrowArgs} args - Arguments to find a AntecipacaoHistorico
   * @example
   * // Get one AntecipacaoHistorico
   * const antecipacaoHistorico = await prisma.antecipacaoHistorico.findUniqueOrThrow({
   *   where: {
   *     // ... provide filter here
   *   }
   * })
   */
  findUniqueOrThrow<T extends AntecipacaoHistoricoFindUniqueOrThrowArgs>(args: Prisma.SelectSubset<T, AntecipacaoHistoricoFindUniqueOrThrowArgs<ExtArgs>>): Prisma.Prisma__AntecipacaoHistoricoClient<runtime.Types.Result.GetResult<Prisma.$AntecipacaoHistoricoPayload<ExtArgs>, T, "findUniqueOrThrow", GlobalOmitOptions>, never, ExtArgs, GlobalOmitOptions>

  /**
   * Find the first AntecipacaoHistorico that matches the filter.
   * Note, that providing `undefined` is treated as the value not being there.
   * Read more here: https://pris.ly/d/null-undefined
   * @param {AntecipacaoHistoricoFindFirstArgs} args - Arguments to find a AntecipacaoHistorico
   * @example
   * // Get one AntecipacaoHistorico
   * const antecipacaoHistorico = await prisma.antecipacaoHistorico.findFirst({
   *   where: {
   *     // ... provide filter here
   *   }
   * })
   */
  findFirst<T extends AntecipacaoHistoricoFindFirstArgs>(args?: Prisma.SelectSubset<T, AntecipacaoHistoricoFindFirstArgs<ExtArgs>>): Prisma.Prisma__AntecipacaoHistoricoClient<runtime.Types.Result.GetResult<Prisma.$AntecipacaoHistoricoPayload<ExtArgs>, T, "findFirst", GlobalOmitOptions> | null, null, ExtArgs, GlobalOmitOptions>

  /**
   * Find the first AntecipacaoHistorico that matches the filter or
   * throw `PrismaKnownClientError` with `P2025` code if no matches were found.
   * Note, that providing `undefined` is treated as the value not being there.
   * Read more here: https://pris.ly/d/null-undefined
   * @param {AntecipacaoHistoricoFindFirstOrThrowArgs} args - Arguments to find a AntecipacaoHistorico
   * @example
   * // Get one AntecipacaoHistorico
   * const antecipacaoHistorico = await prisma.antecipacaoHistorico.findFirstOrThrow({
   *   where: {
   *     // ... provide filter here
   *   }
   * })
   */
  findFirstOrThrow<T extends AntecipacaoHistoricoFindFirstOrThrowArgs>(args?: Prisma.SelectSubset<T, AntecipacaoHistoricoFindFirstOrThrowArgs<ExtArgs>>): Prisma.Prisma__AntecipacaoHistoricoClient<runtime.Types.Result.GetResult<Prisma.$AntecipacaoHistoricoPayload<ExtArgs>, T, "findFirstOrThrow", GlobalOmitOptions>, never, ExtArgs, GlobalOmitOptions>

  /**
   * Find zero or more AntecipacaoHistoricos that matches the filter.
   * Note, that providing `undefined` is treated as the value not being there.
   * Read more here: https://pris.ly/d/null-undefined
   * @param {AntecipacaoHistoricoFindManyArgs} args - Arguments to filter and select certain fields only.
   * @example
   * // Get all AntecipacaoHistoricos
   * const antecipacaoHistoricos = await prisma.antecipacaoHistorico.findMany()
   * 
   * // Get first 10 AntecipacaoHistoricos
   * const antecipacaoHistoricos = await prisma.antecipacaoHistorico.findMany({ take: 10 })
   * 
   * // Only select the `id`
   * const antecipacaoHistoricoWithIdOnly = await prisma.antecipacaoHistorico.findMany({ select: { id: true } })
   * 
   */
  findMany<T extends AntecipacaoHistoricoFindManyArgs>(args?: Prisma.SelectSubset<T, AntecipacaoHistoricoFindManyArgs<ExtArgs>>): Prisma.PrismaPromise<runtime.Types.Result.GetResult<Prisma.$AntecipacaoHistoricoPayload<ExtArgs>, T, "findMany", GlobalOmitOptions>>

  /**
   * Create a AntecipacaoHistorico.
   * @param {AntecipacaoHistoricoCreateArgs} args - Arguments to create a AntecipacaoHistorico.
   * @example
   * // Create one AntecipacaoHistorico
   * const AntecipacaoHistorico = await prisma.antecipacaoHistorico.create({
   *   data: {
   *     // ... data to create a AntecipacaoHistorico
   *   }
   * })
   * 
   */
  create<T extends AntecipacaoHistoricoCreateArgs>(args: Prisma.SelectSubset<T, AntecipacaoHistoricoCreateArgs<ExtArgs>>): Prisma.Prisma__AntecipacaoHistoricoClient<runtime.Types.Result.GetResult<Prisma.$AntecipacaoHistoricoPayload<ExtArgs>, T, "create", GlobalOmitOptions>, never, ExtArgs, GlobalOmitOptions>

  /**
   * Create many AntecipacaoHistoricos.
   * @param {AntecipacaoHistoricoCreateManyArgs} args - Arguments to create many AntecipacaoHistoricos.
   * @example
   * // Create many AntecipacaoHistoricos
   * const antecipacaoHistorico = await prisma.antecipacaoHistorico.createMany({
   *   data: [
   *     // ... provide data here
   *   ]
   * })
   *     
   */
  createMany<T extends AntecipacaoHistoricoCreateManyArgs>(args?: Prisma.SelectSubset<T, AntecipacaoHistoricoCreateManyArgs<ExtArgs>>): Prisma.PrismaPromise<Prisma.BatchPayload>

  /**
   * Delete a AntecipacaoHistorico.
   * @param {AntecipacaoHistoricoDeleteArgs} args - Arguments to delete one AntecipacaoHistorico.
   * @example
   * // Delete one AntecipacaoHistorico
   * const AntecipacaoHistorico = await prisma.antecipacaoHistorico.delete({
   *   where: {
   *     // ... filter to delete one AntecipacaoHistorico
   *   }
   * })
   * 
   */
  delete<T extends AntecipacaoHistoricoDeleteArgs>(args: Prisma.SelectSubset<T, AntecipacaoHistoricoDeleteArgs<ExtArgs>>): Prisma.Prisma__AntecipacaoHistoricoClient<runtime.Types.Result.GetResult<Prisma.$AntecipacaoHistoricoPayload<ExtArgs>, T, "delete", GlobalOmitOptions>, never, ExtArgs, GlobalOmitOptions>

  /**
   * Update one AntecipacaoHistorico.
   * @param {AntecipacaoHistoricoUpdateArgs} args - Arguments to update one AntecipacaoHistorico.
   * @example
   * // Update one AntecipacaoHistorico
   * const antecipacaoHistorico = await prisma.antecipacaoHistorico.update({
   *   where: {
   *     // ... provide filter here
   *   },
   *   data: {
   *     // ... provide data here
   *   }
   * })
   * 
   */
  update<T extends AntecipacaoHistoricoUpdateArgs>(args: Prisma.SelectSubset<T, AntecipacaoHistoricoUpdateArgs<ExtArgs>>): Prisma.Prisma__AntecipacaoHistoricoClient<runtime.Types.Result.GetResult<Prisma.$AntecipacaoHistoricoPayload<ExtArgs>, T, "update", GlobalOmitOptions>, never, ExtArgs, GlobalOmitOptions>

  /**
   * Delete zero or more AntecipacaoHistoricos.
   * @param {AntecipacaoHistoricoDeleteManyArgs} args - Arguments to filter AntecipacaoHistoricos to delete.
   * @example
   * // Delete a few AntecipacaoHistoricos
   * const { count } = await prisma.antecipacaoHistorico.deleteMany({
   *   where: {
   *     // ... provide filter here
   *   }
   * })
   * 
   */
  deleteMany<T extends AntecipacaoHistoricoDeleteManyArgs>(args?: Prisma.SelectSubset<T, AntecipacaoHistoricoDeleteManyArgs<ExtArgs>>): Prisma.PrismaPromise<Prisma.BatchPayload>

  /**
   * Update zero or more AntecipacaoHistoricos.
   * Note, that providing `undefined` is treated as the value not being there.
   * Read more here: https://pris.ly/d/null-undefined
   * @param {AntecipacaoHistoricoUpdateManyArgs} args - Arguments to update one or more rows.
   * @example
   * // Update many AntecipacaoHistoricos
   * const antecipacaoHistorico = await prisma.antecipacaoHistorico.updateMany({
   *   where: {
   *     // ... provide filter here
   *   },
   *   data: {
   *     // ... provide data here
   *   }
   * })
   * 
   */
  updateMany<T extends AntecipacaoHistoricoUpdateManyArgs>(args: Prisma.SelectSubset<T, AntecipacaoHistoricoUpdateManyArgs<ExtArgs>>): Prisma.PrismaPromise<Prisma.BatchPayload>

  /**
   * Create or update one AntecipacaoHistorico.
   * @param {AntecipacaoHistoricoUpsertArgs} args - Arguments to update or create a AntecipacaoHistorico.
   * @example
   * // Update or create a AntecipacaoHistorico
   * const antecipacaoHistorico = await prisma.antecipacaoHistorico.upsert({
   *   create: {
   *     // ... data to create a AntecipacaoHistorico
   *   },
   *   update: {
   *     // ... in case it already exists, update
   *   },
   *   where: {
   *     // ... the filter for the AntecipacaoHistorico we want to update
   *   }
   * })
   */
  upsert<T extends AntecipacaoHistoricoUpsertArgs>(args: Prisma.SelectSubset<T, AntecipacaoHistoricoUpsertArgs<ExtArgs>>): Prisma.Prisma__AntecipacaoHistoricoClient<runtime.Types.Result.GetResult<Prisma.$AntecipacaoHistoricoPayload<ExtArgs>, T, "upsert", GlobalOmitOptions>, never, ExtArgs, GlobalOmitOptions>


  /**
   * Count the number of AntecipacaoHistoricos.
   * Note, that providing `undefined` is treated as the value not being there.
   * Read more here: https://pris.ly/d/null-undefined
   * @param {AntecipacaoHistoricoCountArgs} args - Arguments to filter AntecipacaoHistoricos to count.
   * @example
   * // Count the number of AntecipacaoHistoricos
   * const count = await prisma.antecipacaoHistorico.count({
   *   where: {
   *     // ... the filter for the AntecipacaoHistoricos we want to count
   *   }
   * })
  **/
  count<T extends AntecipacaoHistoricoCountArgs>(
    args?: Prisma.Subset<T, AntecipacaoHistoricoCountArgs>,
  ): Prisma.PrismaPromise<
    T extends runtime.Types.Utils.Record<'select', any>
      ? T['select'] extends true
        ? number
        : Prisma.GetScalarType<T['select'], AntecipacaoHistoricoCountAggregateOutputType>
      : number
  >

  /**
   * Allows you to perform aggregations operations on a AntecipacaoHistorico.
   * Note, that providing `undefined` is treated as the value not being there.
   * Read more here: https://pris.ly/d/null-undefined
   * @param {AntecipacaoHistoricoAggregateArgs} args - Select which aggregations you would like to apply and on what fields.
   * @example
   * // Ordered by age ascending
   * // Where email contains prisma.io
   * // Limited to the 10 users
   * const aggregations = await prisma.user.aggregate({
   *   _avg: {
   *     age: true,
   *   },
   *   where: {
   *     email: {
   *       contains: "prisma.io",
   *     },
   *   },
   *   orderBy: {
   *     age: "asc",
   *   },
   *   take: 10,
   * })
  **/
  aggregate<T extends AntecipacaoHistoricoAggregateArgs>(args: Prisma.Subset<T, AntecipacaoHistoricoAggregateArgs>): Prisma.PrismaPromise<GetAntecipacaoHistoricoAggregateType<T>>

  /**
   * Group by AntecipacaoHistorico.
   * Note, that providing `undefined` is treated as the value not being there.
   * Read more here: https://pris.ly/d/null-undefined
   * @param {AntecipacaoHistoricoGroupByArgs} args - Group by arguments.
   * @example
   * // Group by city, order by createdAt, get count
   * const result = await prisma.user.groupBy({
   *   by: ['city', 'createdAt'],
   *   orderBy: {
   *     createdAt: true
   *   },
   *   _count: {
   *     _all: true
   *   },
   * })
   * 
  **/
  groupBy<
    T extends AntecipacaoHistoricoGroupByArgs,
    HasSelectOrTake extends Prisma.Or<
      Prisma.Extends<'skip', Prisma.Keys<T>>,
      Prisma.Extends<'take', Prisma.Keys<T>>
    >,
    OrderByArg extends Prisma.True extends HasSelectOrTake
      ? { orderBy: AntecipacaoHistoricoGroupByArgs['orderBy'] }
      : { orderBy?: AntecipacaoHistoricoGroupByArgs['orderBy'] },
    OrderFields extends Prisma.ExcludeUnderscoreKeys<Prisma.Keys<Prisma.MaybeTupleToUnion<T['orderBy']>>>,
    ByFields extends Prisma.MaybeTupleToUnion<T['by']>,
    ByValid extends Prisma.Has<ByFields, OrderFields>,
    HavingFields extends Prisma.GetHavingFields<T['having']>,
    HavingValid extends Prisma.Has<ByFields, HavingFields>,
    ByEmpty extends T['by'] extends never[] ? Prisma.True : Prisma.False,
    InputErrors extends ByEmpty extends Prisma.True
    ? `Error: "by" must not be empty.`
    : HavingValid extends Prisma.False
    ? {
        [P in HavingFields]: P extends ByFields
          ? never
          : P extends string
          ? `Error: Field "${P}" used in "having" needs to be provided in "by".`
          : [
              Error,
              'Field ',
              P,
              ` in "having" needs to be provided in "by"`,
            ]
      }[HavingFields]
    : 'take' extends Prisma.Keys<T>
    ? 'orderBy' extends Prisma.Keys<T>
      ? ByValid extends Prisma.True
        ? {}
        : {
            [P in OrderFields]: P extends ByFields
              ? never
              : `Error: Field "${P}" in "orderBy" needs to be provided in "by"`
          }[OrderFields]
      : 'Error: If you provide "take", you also need to provide "orderBy"'
    : 'skip' extends Prisma.Keys<T>
    ? 'orderBy' extends Prisma.Keys<T>
      ? ByValid extends Prisma.True
        ? {}
        : {
            [P in OrderFields]: P extends ByFields
              ? never
              : `Error: Field "${P}" in "orderBy" needs to be provided in "by"`
          }[OrderFields]
      : 'Error: If you provide "skip", you also need to provide "orderBy"'
    : ByValid extends Prisma.True
    ? {}
    : {
        [P in OrderFields]: P extends ByFields
          ? never
          : `Error: Field "${P}" in "orderBy" needs to be provided in "by"`
      }[OrderFields]
  >(args: Prisma.SubsetIntersection<T, AntecipacaoHistoricoGroupByArgs, OrderByArg> & InputErrors): {} extends InputErrors ? GetAntecipacaoHistoricoGroupByPayload<T> : Prisma.PrismaPromise<InputErrors>
/**
 * Fields of the AntecipacaoHistorico model
 */
readonly fields: AntecipacaoHistoricoFieldRefs;
}

/**
 * The delegate class that acts as a "Promise-like" for AntecipacaoHistorico.
 * Why is this prefixed with `Prisma__`?
 * Because we want to prevent naming conflicts as mentioned in
 * https://github.com/prisma/prisma-client-js/issues/707
 */
export interface Prisma__AntecipacaoHistoricoClient<T, Null = never, ExtArgs extends runtime.Types.Extensions.InternalArgs = runtime.Types.Extensions.DefaultArgs, GlobalOmitOptions = {}> extends Prisma.PrismaPromise<T> {
  readonly [Symbol.toStringTag]: "PrismaPromise"
  antecipacao<T extends Prisma.AntecipacaoDefaultArgs<ExtArgs> = {}>(args?: Prisma.Subset<T, Prisma.AntecipacaoDefaultArgs<ExtArgs>>): Prisma.Prisma__AntecipacaoClient<runtime.Types.Result.GetResult<Prisma.$AntecipacaoPayload<ExtArgs>, T, "findUniqueOrThrow", GlobalOmitOptions> | Null, Null, ExtArgs, GlobalOmitOptions>
  /**
   * Attaches callbacks for the resolution and/or rejection of the Promise.
   * @param onfulfilled The callback to execute when the Promise is resolved.
   * @param onrejected The callback to execute when the Promise is rejected.
   * @returns A Promise for the completion of which ever callback is executed.
   */
  then<TResult1 = T, TResult2 = never>(onfulfilled?: ((value: T) => TResult1 | PromiseLike<TResult1>) | undefined | null, onrejected?: ((reason: any) => TResult2 | PromiseLike<TResult2>) | undefined | null): runtime.Types.Utils.JsPromise<TResult1 | TResult2>
  /**
   * Attaches a callback for only the rejection of the Promise.
   * @param onrejected The callback to execute when the Promise is rejected.
   * @returns A Promise for the completion of the callback.
   */
  catch<TResult = never>(onrejected?: ((reason: any) => TResult | PromiseLike<TResult>) | undefined | null): runtime.Types.Utils.JsPromise<T | TResult>
  /**
   * Attaches a callback that is invoked when the Promise is settled (fulfilled or rejected). The
   * resolved value cannot be modified from the callback.
   * @param onfinally The callback to execute when the Promise is settled (fulfilled or rejected).
   * @returns A Promise for the completion of the callback.
   */
  finally(onfinally?: (() => void) | undefined | null): runtime.Types.Utils.JsPromise<T>
}




/**
 * Fields of the AntecipacaoHistorico model
 */
export interface AntecipacaoHistoricoFieldRefs {
  readonly id: Prisma.FieldRef<"AntecipacaoHistorico", 'Int'>
  readonly antecipacaoId: Prisma.FieldRef<"AntecipacaoHistorico", 'Int'>
  readonly status: Prisma.FieldRef<"AntecipacaoHistorico", 'String'>
  readonly metaData: Prisma.FieldRef<"AntecipacaoHistorico", 'Json'>
  readonly createdAt: Prisma.FieldRef<"AntecipacaoHistorico", 'DateTime'>
}
    

// Custom InputTypes
/**
 * AntecipacaoHistorico findUnique
 */
export type AntecipacaoHistoricoFindUniqueArgs<ExtArgs extends runtime.Types.Extensions.InternalArgs = runtime.Types.Extensions.DefaultArgs> = {
  /**
   * Select specific fields to fetch from the AntecipacaoHistorico
   */
  select?: Prisma.AntecipacaoHistoricoSelect<ExtArgs> | null
  /**
   * Omit specific fields from the AntecipacaoHistorico
   */
  omit?: Prisma.AntecipacaoHistoricoOmit<ExtArgs> | null
  /**
   * Choose, which related nodes to fetch as well
   */
  include?: Prisma.AntecipacaoHistoricoInclude<ExtArgs> | null
  /**
   * Filter, which AntecipacaoHistorico to fetch.
   */
  where: Prisma.AntecipacaoHistoricoWhereUniqueInput
}

/**
 * AntecipacaoHistorico findUniqueOrThrow
 */
export type AntecipacaoHistoricoFindUniqueOrThrowArgs<ExtArgs extends runtime.Types.Extensions.InternalArgs = runtime.Types.Extensions.DefaultArgs> = {
  /**
   * Select specific fields to fetch from the AntecipacaoHistorico
   */
  select?: Prisma.AntecipacaoHistoricoSelect<ExtArgs> | null
  /**
   * Omit specific fields from the AntecipacaoHistorico
   */
  omit?: Prisma.AntecipacaoHistoricoOmit<ExtArgs> | null
  /**
   * Choose, which related nodes to fetch as well
   */
  include?: Prisma.AntecipacaoHistoricoInclude<ExtArgs> | null
  /**
   * Filter, which AntecipacaoHistorico to fetch.
   */
  where: Prisma.AntecipacaoHistoricoWhereUniqueInput
}

/**
 * AntecipacaoHistorico findFirst
 */
export type AntecipacaoHistoricoFindFirstArgs<ExtArgs extends runtime.Types.Extensions.InternalArgs = runtime.Types.Extensions.DefaultArgs> = {
  /**
   * Select specific fields to fetch from the AntecipacaoHistorico
   */
  select?: Prisma.AntecipacaoHistoricoSelect<ExtArgs> | null
  /**
   * Omit specific fields from the AntecipacaoHistorico
   */
  omit?: Prisma.AntecipacaoHistoricoOmit<ExtArgs> | null
  /**
   * Choose, which related nodes to fetch as well
   */
  include?: Prisma.AntecipacaoHistoricoInclude<ExtArgs> | null
  /**
   * Filter, which AntecipacaoHistorico to fetch.
   */
  where?: Prisma.AntecipacaoHistoricoWhereInput
  /**
   * {@link https://www.prisma.io/docs/concepts/components/prisma-client/sorting Sorting Docs}
   * 
   * Determine the order of AntecipacaoHistoricos to fetch.
   */
  orderBy?: Prisma.AntecipacaoHistoricoOrderByWithRelationInput | Prisma.AntecipacaoHistoricoOrderByWithRelationInput[]
  /**
   * {@link https://www.prisma.io/docs/concepts/components/prisma-client/pagination#cursor-based-pagination Cursor Docs}
   * 
   * Sets the position for searching for AntecipacaoHistoricos.
   */
  cursor?: Prisma.AntecipacaoHistoricoWhereUniqueInput
  /**
   * {@link https://www.prisma.io/docs/concepts/components/prisma-client/pagination Pagination Docs}
   * 
   * Take `±n` AntecipacaoHistoricos from the position of the cursor.
   */
  take?: number
  /**
   * {@link https://www.prisma.io/docs/concepts/components/prisma-client/pagination Pagination Docs}
   * 
   * Skip the first `n` AntecipacaoHistoricos.
   */
  skip?: number
  /**
   * {@link https://www.prisma.io/docs/concepts/components/prisma-client/distinct Distinct Docs}
   * 
   * Filter by unique combinations of AntecipacaoHistoricos.
   */
  distinct?: Prisma.AntecipacaoHistoricoScalarFieldEnum | Prisma.AntecipacaoHistoricoScalarFieldEnum[]
}

/**
 * AntecipacaoHistorico findFirstOrThrow
 */
export type AntecipacaoHistoricoFindFirstOrThrowArgs<ExtArgs extends runtime.Types.Extensions.InternalArgs = runtime.Types.Extensions.DefaultArgs> = {
  /**
   * Select specific fields to fetch from the AntecipacaoHistorico
   */
  select?: Prisma.AntecipacaoHistoricoSelect<ExtArgs> | null
  /**
   * Omit specific fields from the AntecipacaoHistorico
   */
  omit?: Prisma.AntecipacaoHistoricoOmit<ExtArgs> | null
  /**
   * Choose, which related nodes to fetch as well
   */
  include?: Prisma.AntecipacaoHistoricoInclude<ExtArgs> | null
  /**
   * Filter, which AntecipacaoHistorico to fetch.
   */
  where?: Prisma.AntecipacaoHistoricoWhereInput
  /**
   * {@link https://www.prisma.io/docs/concepts/components/prisma-client/sorting Sorting Docs}
   * 
   * Determine the order of AntecipacaoHistoricos to fetch.
   */
  orderBy?: Prisma.AntecipacaoHistoricoOrderByWithRelationInput | Prisma.AntecipacaoHistoricoOrderByWithRelationInput[]
  /**
   * {@link https://www.prisma.io/docs/concepts/components/prisma-client/pagination#cursor-based-pagination Cursor Docs}
   * 
   * Sets the position for searching for AntecipacaoHistoricos.
   */
  cursor?: Prisma.AntecipacaoHistoricoWhereUniqueInput
  /**
   * {@link https://www.prisma.io/docs/concepts/components/prisma-client/pagination Pagination Docs}
   * 
   * Take `±n` AntecipacaoHistoricos from the position of the cursor.
   */
  take?: number
  /**
   * {@link https://www.prisma.io/docs/concepts/components/prisma-client/pagination Pagination Docs}
   * 
   * Skip the first `n` AntecipacaoHistoricos.
   */
  skip?: number
  /**
   * {@link https://www.prisma.io/docs/concepts/components/prisma-client/distinct Distinct Docs}
   * 
   * Filter by unique combinations of AntecipacaoHistoricos.
   */
  distinct?: Prisma.AntecipacaoHistoricoScalarFieldEnum | Prisma.AntecipacaoHistoricoScalarFieldEnum[]
}

/**
 * AntecipacaoHistorico findMany
 */
export type AntecipacaoHistoricoFindManyArgs<ExtArgs extends runtime.Types.Extensions.InternalArgs = runtime.Types.Extensions.DefaultArgs> = {
  /**
   * Select specific fields to fetch from the AntecipacaoHistorico
   */
  select?: Prisma.AntecipacaoHistoricoSelect<ExtArgs> | null
  /**
   * Omit specific fields from the AntecipacaoHistorico
   */
  omit?: Prisma.AntecipacaoHistoricoOmit<ExtArgs> | null
  /**
   * Choose, which related nodes to fetch as well
   */
  include?: Prisma.AntecipacaoHistoricoInclude<ExtArgs> | null
  /**
   * Filter, which AntecipacaoHistoricos to fetch.
   */
  where?: Prisma.AntecipacaoHistoricoWhereInput
  /**
   * {@link https://www.prisma.io/docs/concepts/components/prisma-client/sorting Sorting Docs}
   * 
   * Determine the order of AntecipacaoHistoricos to fetch.
   */
  orderBy?: Prisma.AntecipacaoHistoricoOrderByWithRelationInput | Prisma.AntecipacaoHistoricoOrderByWithRelationInput[]
  /**
   * {@link https://www.prisma.io/docs/concepts/components/prisma-client/pagination#cursor-based-pagination Cursor Docs}
   * 
   * Sets the position for listing AntecipacaoHistoricos.
   */
  cursor?: Prisma.AntecipacaoHistoricoWhereUniqueInput
  /**
   * {@link https://www.prisma.io/docs/concepts/components/prisma-client/pagination Pagination Docs}
   * 
   * Take `±n` AntecipacaoHistoricos from the position of the cursor.
   */
  take?: number
  /**
   * {@link https://www.prisma.io/docs/concepts/components/prisma-client/pagination Pagination Docs}
   * 
   * Skip the first `n` AntecipacaoHistoricos.
   */
  skip?: number
  distinct?: Prisma.AntecipacaoHistoricoScalarFieldEnum | Prisma.AntecipacaoHistoricoScalarFieldEnum[]
}

/**
 * AntecipacaoHistorico create
 */
export type AntecipacaoHistoricoCreateArgs<ExtArgs extends runtime.Types.Extensions.InternalArgs = runtime.Types.Extensions.DefaultArgs> = {
  /**
   * Select specific fields to fetch from the AntecipacaoHistorico
   */
  select?: Prisma.AntecipacaoHistoricoSelect<ExtArgs> | null
  /**
   * Omit specific fields from the AntecipacaoHistorico
   */
  omit?: Prisma.AntecipacaoHistoricoOmit<ExtArgs> | null
  /**
   * Choose, which related nodes to fetch as well
   */
  include?: Prisma.AntecipacaoHistoricoInclude<ExtArgs> | null
  /**
   * The data needed to create a AntecipacaoHistorico.
   */
  data: Prisma.XOR<Prisma.AntecipacaoHistoricoCreateInput, Prisma.AntecipacaoHistoricoUncheckedCreateInput>
}

/**
 * AntecipacaoHistorico createMany
 */
export type AntecipacaoHistoricoCreateManyArgs<ExtArgs extends runtime.Types.Extensions.InternalArgs = runtime.Types.Extensions.DefaultArgs> = {
  /**
   * The data used to create many AntecipacaoHistoricos.
   */
  data: Prisma.AntecipacaoHistoricoCreateManyInput | Prisma.AntecipacaoHistoricoCreateManyInput[]
  skipDuplicates?: boolean
}

/**
 * AntecipacaoHistorico update
 */
export type AntecipacaoHistoricoUpdateArgs<ExtArgs extends runtime.Types.Extensions.InternalArgs = runtime.Types.Extensions.DefaultArgs> = {
  /**
   * Select specific fields to fetch from the AntecipacaoHistorico
   */
  select?: Prisma.AntecipacaoHistoricoSelect<ExtArgs> | null
  /**
   * Omit specific fields from the AntecipacaoHistorico
   */
  omit?: Prisma.AntecipacaoHistoricoOmit<ExtArgs> | null
  /**
   * Choose, which related nodes to fetch as well
   */
  include?: Prisma.AntecipacaoHistoricoInclude<ExtArgs> | null
  /**
   * The data needed to update a AntecipacaoHistorico.
   */
  data: Prisma.XOR<Prisma.AntecipacaoHistoricoUpdateInput, Prisma.AntecipacaoHistoricoUncheckedUpdateInput>
  /**
   * Choose, which AntecipacaoHistorico to update.
   */
  where: Prisma.AntecipacaoHistoricoWhereUniqueInput
}

/**
 * AntecipacaoHistorico updateMany
 */
export type AntecipacaoHistoricoUpdateManyArgs<ExtArgs extends runtime.Types.Extensions.InternalArgs = runtime.Types.Extensions.DefaultArgs> = {
  /**
   * The data used to update AntecipacaoHistoricos.
   */
  data: Prisma.XOR<Prisma.AntecipacaoHistoricoUpdateManyMutationInput, Prisma.AntecipacaoHistoricoUncheckedUpdateManyInput>
  /**
   * Filter which AntecipacaoHistoricos to update
   */
  where?: Prisma.AntecipacaoHistoricoWhereInput
  /**
   * Limit how many AntecipacaoHistoricos to update.
   */
  limit?: number
}

/**
 * AntecipacaoHistorico upsert
 */
export type AntecipacaoHistoricoUpsertArgs<ExtArgs extends runtime.Types.Extensions.InternalArgs = runtime.Types.Extensions.DefaultArgs> = {
  /**
   * Select specific fields to fetch from the AntecipacaoHistorico
   */
  select?: Prisma.AntecipacaoHistoricoSelect<ExtArgs> | null
  /**
   * Omit specific fields from the AntecipacaoHistorico
   */
  omit?: Prisma.AntecipacaoHistoricoOmit<ExtArgs> | null
  /**
   * Choose, which related nodes to fetch as well
   */
  include?: Prisma.AntecipacaoHistoricoInclude<ExtArgs> | null
  /**
   * The filter to search for the AntecipacaoHistorico to update in case it exists.
   */
  where: Prisma.AntecipacaoHistoricoWhereUniqueInput
  /**
   * In case the AntecipacaoHistorico found by the `where` argument doesn't exist, create a new AntecipacaoHistorico with this data.
   */
  create: Prisma.XOR<Prisma.AntecipacaoHistoricoCreateInput, Prisma.AntecipacaoHistoricoUncheckedCreateInput>
  /**
   * In case the AntecipacaoHistorico was found with the provided `where` argument, update it with this data.
   */
  update: Prisma.XOR<Prisma.AntecipacaoHistoricoUpdateInput, Prisma.AntecipacaoHistoricoUncheckedUpdateInput>
}

/**
 * AntecipacaoHistorico delete
 */
export type AntecipacaoHistoricoDeleteArgs<ExtArgs extends runtime.Types.Extensions.InternalArgs = runtime.Types.Extensions.DefaultArgs> = {
  /**
   * Select specific fields to fetch from the AntecipacaoHistorico
   */
  select?: Prisma.AntecipacaoHistoricoSelect<ExtArgs> | null
  /**
   * Omit specific fields from the AntecipacaoHistorico
   */
  omit?: Prisma.AntecipacaoHistoricoOmit<ExtArgs> | null
  /**
   * Choose, which related nodes to fetch as well
   */
  include?: Prisma.AntecipacaoHistoricoInclude<ExtArgs> | null
  /**
   * Filter which AntecipacaoHistorico to delete.
   */
  where: Prisma.AntecipacaoHistoricoWhereUniqueInput
}

/**
 * AntecipacaoHistorico deleteMany
 */
export type AntecipacaoHistoricoDeleteManyArgs<ExtArgs extends runtime.Types.Extensions.InternalArgs = runtime.Types.Extensions.DefaultArgs> = {
  /**
   * Filter which AntecipacaoHistoricos to delete
   */
  where?: Prisma.AntecipacaoHistoricoWhereInput
  /**
   * Limit how many AntecipacaoHistoricos to delete.
   */
  limit?: number
}

/**
 * AntecipacaoHistorico without action
 */
export type AntecipacaoHistoricoDefaultArgs<ExtArgs extends runtime.Types.Extensions.InternalArgs = runtime.Types.Extensions.DefaultArgs> = {
  /**
   * Select specific fields to fetch from the AntecipacaoHistorico
   */
  select?: Prisma.AntecipacaoHistoricoSelect<ExtArgs> | null
  /**
   * Omit specific fields from the AntecipacaoHistorico
   */
  omit?: Prisma.AntecipacaoHistoricoOmit<ExtArgs> | null
  /**
   * Choose, which related nodes to fetch as well
   */
  include?: Prisma.AntecipacaoHistoricoInclude<ExtArgs> | null
}
