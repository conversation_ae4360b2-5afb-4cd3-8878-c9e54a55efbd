import { z } from "zod";

export const createPlantaoSchema = z.object({
  clienteId: z.number().positive("Cliente é obrigatório").optional(),
  clienteUuid: z.string().uuid("UUID do cliente inválido").optional(),
  localAtendimentoId: z.number().positive("Local de atendimento é obrigatório").optional(),
  localAtendimentoUuid: z.string().uuid("UUID do local inválido").optional(),
  profissionalId: z.number().positive("Profissional é obrigatório").optional(),
  profissionalUuid: z.string().uuid("UUID do profissional inválido").optional(),
  modalidadeTrabalho: z.enum(["PLANTONISTA", "MENSALISTA", "COORDENADOR", "SUPERVISOR", "DIRETOR"]),
  tipoFechamento: z.enum(["DIARIO", "SEMANAL", "QUINZENAL", "MENSAL"]).default("MENSAL"),
  dataInicial: z.string(),
  dataFinal: z.string().optional().nullable(),
  tipoValor: z.enum(["HORA", "DIARIA", "PLANTAO", "MENSAL"]).default("HORA"),
  valorBase: z.number().positive("Valor base é obrigatório"),
  prazoPagamentoDias: z.number().min(1).max(365).optional().nullable(),
  horaInicio: z.string().regex(/^\d{2}:\d{2}$/),
  horaFim: z.string().regex(/^\d{2}:\d{2}$/),
  intervalo: z.string().regex(/^\d{2}:\d{2}$/),
  tipoTurno: z.string().optional().nullable(),
  observacoes: z.string().optional().nullable(),
  diasPlantao: z
    .array(
      z.object({
        data: z.string(), // Campo data obrigatório no formato YYYY-MM-DD
        horaEntrada: z
          .string()
          .regex(/^\d{2}:\d{2}$/)
          .optional()
          .nullable(),
        horaSaida: z
          .string()
          .regex(/^\d{2}:\d{2}$/)
          .optional()
          .nullable(),
        intervalo: z
          .string()
          .regex(/^\d{2}:\d{2}$/)
          .optional(),
      })
    )
    .optional(),
}); // Remove strict mode to allow stripping of unknown fields

export const updatePlantaoSchema = createPlantaoSchema.partial();

export const plantaoQuerySchema = z.object({
  page: z.coerce.number().min(1).optional().default(1),
  limit: z.coerce.number().min(1).max(100).optional().default(10),
  mes: z.coerce.number().min(1).max(12).optional(),
  ano: z.coerce.number().min(2024).max(2030).optional(),
  dataInicial: z.string().optional(),
  dataFinal: z.string().optional(),
  status: z.enum(["concluido", "em_andamento"]).optional(),
  clienteId: z.number().positive().optional(),
  profissionalId: z.number().positive().optional(),
  localAtendimentoId: z.number().positive().optional(),
  ativo: z
    .string()
    .transform((val) => val === "true")
    .optional(),
});

export type CreatePlantaoInput = z.infer<typeof createPlantaoSchema>;
export type UpdatePlantaoInput = z.infer<typeof updatePlantaoSchema>;
export type PlantaoQuery = z.infer<typeof plantaoQuerySchema>;
