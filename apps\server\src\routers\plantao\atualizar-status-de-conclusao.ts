import { z } from "zod";
import { prisma, withAudit } from "@/lib/prisma";
import { createPlantaoSchema, type CreatePlantaoInput, type UpdatePlantaoInput } from "@/schemas/plantao.schema";
import type { FastifyTypedInstance } from "@/types";
import { formatDateToDateTime, parseUTCDate, toISOString } from "@shared/date";

export function atualizarStatusDeConclusaoPlantaoRouter(fastify: FastifyTypedInstance) {
  // Atualizar status de conclusão do plantão (PATCH parcial)
  fastify.patch<{
    Params: { uuid: string };
    Body: { concluidoEm?: string | null };
  }>(
    "/plantoes/:uuid",
    withAudit(async (request, reply) => {
      const { uuid } = request.params;
      const { concluidoEm } = request.body;
      const clienteId = request.clienteId;

      // Verificar se plantão existe e pertence ao cliente
      const where = {
        uuid,
        clienteId,
      };

      const existingPlantao = await prisma.plantao.findFirst({
        where,
      });

      if (!existingPlantao) {
        return reply.status(404).send({ error: "Plantão não encontrado" });
      }

      // Atualizar apenas o campo concluidoEm
      const plantao = await prisma.plantao.update({
        where: { uuid },
        data: {
          concluidoEm: concluidoEm ? parseUTCDate(concluidoEm) : null,
        },
        include: {
          cliente: true,
          profissional: true,
          localAtendimento: true,
          diasPlantao: true,
        },
      });

      return reply.send(plantao);
    })
  );
}
