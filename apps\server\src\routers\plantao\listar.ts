import { z } from "zod";
import { prisma, withAudit } from "@/lib/prisma";
import {
  createPlantaoSchema,
  plantaoQuerySchema,
  type CreatePlantaoInput,
  type PlantaoQuery,
} from "@/schemas/plantao.schema";
import type { FastifyTypedInstance, FastifyTypedRequest } from "@/types";
import { formatDateToDateTime, getCurrentYear, parseUTCDate, toISOString } from "@shared/date";
import { getEndOfMonthInTimezone, getStartOfMonthInTimezone } from "@shared/date";

export function listarPlantaoRouter(fastify: FastifyTypedInstance) {
  // Listar plantões
  fastify.get<{ Querystring: PlantaoQuery }>(
    "/plantoes",
    withAudit(async (request: FastifyTypedRequest, reply) => {
      const parsedQuery = plantaoQuerySchema.parse(request.query);
      const {
        page = 1,
        limit = 10,
        mes,
        ano,
        dataInicial,
        dataFinal,
        profissionalId,
        localAtendimentoId,
        ativo,
        status,
      } = parsedQuery as any;
      const pageNum = Number(page);
      const limitNum = Number(limit);
      const skip = (pageNum - 1) * limitNum;

      const clienteId = request.clienteId;

      const where: any = {
        ...(ativo !== undefined && {
          ativo,
        }),
        clienteId,
        ...(profissionalId && { profissionalId }),
        ...(localAtendimentoId && { localAtendimentoId }),
      };

      // Filtro por mes/ano usando dataInicial - buscar plantões que estejam ativos no período
      const fusoHorario = request.fusoHorario;
      if (mes && ano) {
        // Plantão ativo no mês/ano: dataInicial <= fim do mês E (dataFinal >= início do mês OU dataFinal é null)
        const inicioMes = getStartOfMonthInTimezone(Number(ano), Number(mes), fusoHorario);
        const fimMes = getEndOfMonthInTimezone(Number(ano), Number(mes), fusoHorario);

        where.dataInicial = { lte: fimMes };
        where.AND = [
          ...(where.AND || []),
          {
            OR: [{ dataFinal: { gte: inicioMes } }, { dataFinal: null }],
          },
        ];
      } else if (mes) {
        // Se só tem mês, buscar no ano atual
        const currentYear = getCurrentYear();
        const inicioMes = getStartOfMonthInTimezone(currentYear, Number(mes), fusoHorario);
        const fimMes = getEndOfMonthInTimezone(currentYear, Number(mes), fusoHorario);

        where.dataInicial = { lte: fimMes };
        where.AND = [
          ...(where.AND || []),
          {
            OR: [{ dataFinal: { gte: inicioMes } }, { dataFinal: null }],
          },
        ];
      } else if (ano) {
        // Se só tem ano, buscar plantões ativos em qualquer mês do ano
        const inicioAno = getStartOfMonthInTimezone(Number(ano), 1, fusoHorario);
        const fimAno = getEndOfMonthInTimezone(Number(ano), 12, fusoHorario);

        where.dataInicial = { lte: fimAno };
        where.AND = [
          ...(where.AND || []),
          {
            OR: [{ dataFinal: { gte: inicioAno } }, { dataFinal: null }],
          },
        ];
      }

      // Filtro por período de datas
      if (dataInicial || dataFinal) {
        where.OR = [
          // Filtrar por dataInicial do plantão
          {
            dataInicial: {
              ...(dataInicial && { gte: parseUTCDate(`${dataInicial}T00:00:00.000Z`) }),
              ...(dataFinal && { lte: parseUTCDate(`${dataFinal}T23:59:59.999Z`) }),
            },
          },
          // Filtrar por diasPlantao.data
          {
            diasPlantao: {
              some: {
                data: {
                  ...(dataInicial && { gte: parseUTCDate(`${dataInicial}T00:00:00.000Z`) }),
                  ...(dataFinal && { lte: parseUTCDate(`${dataFinal}T23:59:59.999Z`) }),
                },
              },
            },
          },
        ];
      }

      // Filtrar por status de conclusão
      if (status === "concluido") {
        where.concluidoEm = { not: null };
      } else if (status === "em_andamento") {
        where.concluidoEm = null;
      }

      const [plantoes, total] = await Promise.all([
        prisma.plantao.findMany({
          where,
          skip,
          take: limitNum,
          include: {
            cliente: true,
            profissional: {
              include: {
                usuario: {
                  select: {
                    id: true,
                    nome: true,
                    cpf: true,
                    email: true,
                  },
                },
              },
            },
            localAtendimento: true,
            diasPlantao: {
              orderBy: { data: "asc" },
            },
            _count: {
              select: {
                fechamentos: true,
              },
            },
          },
          orderBy: [{ dataInicial: "desc" }, { createdAt: "desc" }],
        }),
        prisma.plantao.count({ where }),
      ]);

      return reply.send({
        data: plantoes,
        meta: {
          page: pageNum,
          limit: limitNum,
          total,
          totalPages: Math.ceil(total / limitNum),
        },
      });
    })
  );
}
