import { useState } from "react";
import { Dialog, DialogContent, DialogDescription, DialogHeader, DialogTitle } from "@/components/ui/dialog";
import { Button } from "@/components/ui/button";
import { RadioGroup, RadioGroupItem } from "@/components/ui/radio-group";
import { Label } from "@/components/ui/label";
import { User } from "lucide-react";

interface Perfil {
  id: string;
  nome: string;
  descricao?: string;
}

interface ProfileSelectionModalProps {
  open: boolean;
  perfis: Perfil[];
  onSelect: (perfil: Perfil) => void;
}

export function ProfileSelectionModal({ open, perfis, onSelect }: ProfileSelectionModalProps) {
  const [selectedProfileId, setSelectedProfileId] = useState<string>("");

  const handleConfirm = () => {
    const perfil = perfis.find((p) => p.id === selectedProfileId);
    if (perfil) {
      onSelect(perfil);
    }
  };

  return (
    <Dialog open={open} modal>
      <DialogContent
        className="sm:max-w-md"
        onEscapeKeyDown={(e) => e.preventDefault()}
        onPointerDownOutside={(e) => e.preventDefault()}
      >
        <DialogHeader>
          <DialogTitle className="flex items-center gap-2">
            <User className="h-5 w-5" />
            Selecione o Perfil
          </DialogTitle>
          <DialogDescription>
            Você possui múltiplos perfis. Selecione qual perfil deseja assumir para esta sessão.
          </DialogDescription>
        </DialogHeader>

        <div className="py-4">
          <RadioGroup value={selectedProfileId} onValueChange={setSelectedProfileId} className="space-y-3">
            {perfis.map((perfil) => (
              <div
                key={perfil.id}
                className="flex items-start space-x-3 rounded-lg border p-3 hover:bg-muted/50 transition-colors"
              >
                <RadioGroupItem value={perfil.id} id={perfil.id} />
                <Label htmlFor={perfil.id} className="flex-1 cursor-pointer space-y-1">
                  <div className="font-medium">{perfil.nome}</div>
                  {perfil.descricao && <div className="text-sm text-muted-foreground">{perfil.descricao}</div>}
                </Label>
              </div>
            ))}
          </RadioGroup>
        </div>

        <div className="flex justify-end">
          <Button onClick={handleConfirm} disabled={!selectedProfileId} className="w-full sm:w-auto">
            Confirmar Seleção
          </Button>
        </div>
      </DialogContent>
    </Dialog>
  );
}
