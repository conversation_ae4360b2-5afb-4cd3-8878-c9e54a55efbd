import { But<PERSON> } from "@/components/ui/button";
import { ChevronLeft, ChevronRight, CalendarPlus, Plus, Edit2, <PERSON><PERSON>he<PERSON>, Send } from "lucide-react";
import { getMonth, getYear } from "@shared/date";
import type { Plantao } from "@/lib/api";

interface MonthNavigationProps {
  plantao: Plantao;
  currentMonth: number;
  currentYear: number;
  selectedRegistros: Set<number>;
  onNavigateMonth: (direction: "prev" | "next") => void;
  onPresencaAutomatico: () => void;
  onAddRegistro: () => void;
  onAprovarSelecionados: () => void;
  onEnviarFechamento: () => void;
  isPresencaAutomaticoLoading: boolean;
  isAprovarLoading: boolean;
  isEnviarFechamentoLoading: boolean;
  isBatchMutationLoading: boolean;
}

const meses = [
  "Janeiro",
  "Fevereiro",
  "Março",
  "<PERSON><PERSON><PERSON>",
  "<PERSON><PERSON>",
  "<PERSON><PERSON>",
  "<PERSON><PERSON>",
  "Agos<PERSON>",
  "<PERSON><PERSON><PERSON>",
  "<PERSON><PERSON><PERSON>",
  "<PERSON><PERSON><PERSON>",
  "Dezembro",
];

export function MonthNavigation({
  plantao,
  currentMonth,
  currentYear,
  selectedRegistros,
  onNavigateMonth,
  onPresencaAutomatico,
  onAddRegistro,
  onAprovarSelecionados,
  onEnviarFechamento,
  isPresencaAutomaticoLoading,
  isAprovarLoading,
  isEnviarFechamentoLoading,
  isBatchMutationLoading,
}: MonthNavigationProps) {
  return (
    <div className="flex justify-between items-center">
      <div className="flex items-center gap-4">
        <div className="flex items-center gap-2">
          <Button
            size="sm"
            variant="outline"
            onClick={() => onNavigateMonth("prev")}
            disabled={
              currentMonth === getMonth(plantao.dataInicial) + 1 && currentYear === getYear(plantao.dataInicial)
            }
          >
            <ChevronLeft className="h-4 w-4" />
          </Button>
          <div className="min-w-[150px] text-center">
            <h3 className="text-lg font-semibold">
              {meses[currentMonth - 1]} {currentYear}
            </h3>
          </div>
          <Button
            size="sm"
            variant="outline"
            onClick={() => onNavigateMonth("next")}
            disabled={
              (plantao.dataFinal &&
                currentMonth === getMonth(plantao.dataFinal) + 1 &&
                currentYear === getYear(plantao.dataFinal)) ||
              false
            }
          >
            <ChevronRight className="h-4 w-4" />
          </Button>
        </div>
      </div>
      <div className="flex gap-2">
        {selectedRegistros.size > 0 && (
          <>
            <Button onClick={onAprovarSelecionados} variant="outline" disabled={isAprovarLoading}>
              <CheckCheck className="h-4 w-4 mr-2" />
              Aprovar ({selectedRegistros.size})
            </Button>
            <Button onClick={onEnviarFechamento} disabled={isEnviarFechamentoLoading}>
              <Send className="h-4 w-4 mr-2" />
              Enviar para Fechamento ({selectedRegistros.size})
            </Button>
          </>
        )}
        <Button onClick={onPresencaAutomatico} variant="outline" disabled={isPresencaAutomaticoLoading}>
          <CalendarPlus className="h-4 w-4 mr-2" />
          {isPresencaAutomaticoLoading ? "Criando..." : "Presença Automática"}
        </Button>
        <Button onClick={onAddRegistro}>
          <Plus className="h-4 w-4 mr-2" />
          Adicionar
        </Button>
      </div>
    </div>
  );
}
