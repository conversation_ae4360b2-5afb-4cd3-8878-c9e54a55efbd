import { usePara<PERSON>, useNavi<PERSON>, <PERSON> } from "@tanstack/react-router";
import { createFileRoute } from "@tanstack/react-router";
import { useQuery, useMutation } from "@tanstack/react-query";
import { requireAdminRole } from "@/lib/route-guards";
import { api, type Antecipacao } from "@/lib/api";
import { Button } from "@/components/ui/button";
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from "@/components/ui/card";
import { Badge } from "@/components/ui/badge";
import { Skeleton } from "@/components/ui/skeleton";
import { Separator } from "@/components/ui/separator";
import {
  ArrowLeft,
  DollarSign,
  FileText,
  Download,
  Check,
  X,
  Clock,
  User,
  Building2,
  MapPin,
  Calendar,
  CreditCard,
  AlertCircle,
  CheckCircle2,
  Loader2,
} from "lucide-react";
import { toast } from "sonner";
import { formatCurrency } from "@/lib/utils";
import { formatDate } from "@shared/date";

function AntecipacaoDetails() {
  const { uuid } = useParams({ from: "/antecipacoes/$uuid" });
  const navigate = useNavigate();

  const {
    data: antecipacao,
    isLoading,
    refetch,
  } = useQuery({
    queryKey: ["antecipacao", uuid],
    queryFn: () => api.get<Antecipacao>(`/antecipacoes/${uuid}`),
    enabled: !!uuid,
  });

  const aprovarMutation = useMutation({
    mutationFn: async () => {
      return api.post(`/antecipacoes/${uuid}/aprovar`, {});
    },
    onSuccess: () => {
      toast.success("Antecipação aprovada com sucesso!");
      refetch();
    },
    onError: (error: any) => {
      toast.error(error.message || "Erro ao aprovar antecipação");
    },
  });

  const rejeitarMutation = useMutation({
    mutationFn: async (motivo?: string) => {
      return api.post(`/antecipacoes/${uuid}/rejeitar`, { motivo });
    },
    onSuccess: () => {
      toast.success("Antecipação rejeitada!");
      refetch();
    },
    onError: (error: any) => {
      toast.error(error.message || "Erro ao rejeitar antecipação");
    },
  });

  const gerarCCBMutation = useMutation({
    mutationFn: async () => {
      return api.post<any>(`/antecipacoes/${uuid}/gerar-ccb`);
    },
    onSuccess: (data: any) => {
      toast.success("CCB gerada com sucesso!");
      if (data?.url) {
        window.open(data.url, "_blank");
      }
      refetch();
    },
    onError: (error: any) => {
      toast.error(error.message || "Erro ao gerar CCB");
    },
  });

  const getStatusBadge = (status: string) => {
    switch (status) {
      case "PENDENTE":
        return (
          <Badge variant="outline" className="text-yellow-600 border-yellow-300">
            <Clock className="h-3 w-3 mr-1" />
            Pendente
          </Badge>
        );
      case "APROVADA":
        return (
          <Badge variant="default" className="bg-blue-600">
            <CheckCircle2 className="h-3 w-3 mr-1" />
            Aprovada
          </Badge>
        );
      case "REJEITADA":
        return (
          <Badge variant="destructive">
            <X className="h-3 w-3 mr-1" />
            Rejeitada
          </Badge>
        );
      case "PAGA":
        return (
          <Badge className="bg-green-500">
            <DollarSign className="h-3 w-3 mr-1" />
            Paga
          </Badge>
        );
      default:
        return <Badge variant="secondary">{status}</Badge>;
    }
  };

  if (isLoading) {
    return (
      <div className="space-y-6">
        <div className="flex items-center gap-4">
          <Skeleton className="h-10 w-10" />
          <div className="space-y-2">
            <Skeleton className="h-8 w-64" />
            <Skeleton className="h-4 w-96" />
          </div>
        </div>
        <div className="grid gap-6 lg:grid-cols-2">
          <Skeleton className="h-96" />
          <Skeleton className="h-96" />
        </div>
      </div>
    );
  }

  if (!antecipacao) {
    return (
      <div className="flex flex-col items-center justify-center py-12">
        <AlertCircle className="h-12 w-12 text-muted-foreground mb-4" />
        <h2 className="text-2xl font-semibold mb-2">Antecipação não encontrada</h2>
        <p className="text-muted-foreground mb-4">A antecipação solicitada não existe ou foi removida.</p>
        <Link to="/antecipacoes">
          <Button>Voltar à Lista</Button>
        </Link>
      </div>
    );
  }

  return (
    <div className="space-y-6">
      {/* Header */}
      <div className="flex items-center gap-4">
        <Link to="/antecipacoes">
          <Button variant="ghost" size="icon">
            <ArrowLeft className="h-4 w-4" />
          </Button>
        </Link>
        <div className="flex-1">
          <div className="flex items-center gap-3">
            <h1 className="text-3xl font-bold">Antecipação #{antecipacao.id}</h1>
            {getStatusBadge(antecipacao.status)}
          </div>
          <p className="text-muted-foreground">
            Criada em {formatDate(antecipacao.createdAt)} • Percentual: {antecipacao.percentual}%
          </p>
        </div>
        <div className="flex gap-2">
          {/* {antecipacao.status === "PENDENTE" && (
            <>
              <Button variant="outline" onClick={() => aprovarMutation.mutate()} disabled={aprovarMutation.isPending}>
                {aprovarMutation.isPending ? (
                  <Loader2 className="h-4 w-4 animate-spin mr-2" />
                ) : (
                  <Check className="h-4 w-4 mr-2 text-green-600" />
                )}
                Aprovar
              </Button>
              <Button
                variant="outline"
                onClick={() => rejeitarMutation.mutate(undefined)}
                disabled={rejeitarMutation.isPending}
              >
                {rejeitarMutation.isPending ? (
                  <Loader2 className="h-4 w-4 animate-spin mr-2" />
                ) : (
                  <X className="h-4 w-4 mr-2 text-red-600" />
                )}
                Rejeitar
              </Button>
            </>
          )} */}
          {antecipacao.status === "APROVADA" && !antecipacao.numeroCCB && (
            <Button onClick={() => gerarCCBMutation.mutate()} disabled={gerarCCBMutation.isPending}>
              {gerarCCBMutation.isPending ? (
                <Loader2 className="h-4 w-4 animate-spin mr-2" />
              ) : (
                <FileText className="h-4 w-4 mr-2" />
              )}
              Gerar CCB
            </Button>
          )}
          {antecipacao.numeroCCB && (
            <Button variant="outline">
              <Download className="h-4 w-4 mr-2" />
              Baixar CCB
            </Button>
          )}
        </div>
      </div>

      <div className="grid gap-6 lg:grid-cols-2">
        {/* Informações da Antecipação */}
        <Card>
          <CardHeader>
            <CardTitle className="flex items-center gap-2">
              <DollarSign className="h-5 w-5" />
              Informações Financeiras
            </CardTitle>
          </CardHeader>
          <CardContent className="space-y-4">
            <div className="grid grid-cols-2 gap-4">
              <div>
                <p className="text-sm font-medium text-muted-foreground">Valor Solicitado</p>
                <p className="text-2xl font-bold">{formatCurrency(antecipacao.valorSolicitado)}</p>
              </div>
              <div>
                <p className="text-sm font-medium text-muted-foreground">Valor Aprovado</p>
                <p className="text-2xl font-bold text-green-600">
                  {antecipacao.valorAprovado ? formatCurrency(antecipacao.valorAprovado) : "-"}
                </p>
              </div>
            </div>

            <Separator />

            <div className="grid grid-cols-3 gap-4">
              <div>
                <p className="text-sm font-medium text-muted-foreground">Percentual</p>
                <p className="text-lg font-semibold">{antecipacao.percentual}%</p>
              </div>
              <div>
                <p className="text-sm font-medium text-muted-foreground">Taxa</p>
                <p className="text-lg font-semibold">
                  {antecipacao.taxaAntecipacao ? `${antecipacao.taxaAntecipacao}%` : "-"}
                </p>
              </div>
              <div>
                <p className="text-sm font-medium text-muted-foreground">Fechamentos</p>
                <p className="text-lg font-semibold">{antecipacao.fechamentos?.length || 0}</p>
              </div>
            </div>

            {antecipacao.observacoes && (
              <div>
                <p className="text-sm font-medium text-muted-foreground">Observações</p>
                <p className="text-sm bg-muted p-3 rounded-lg">{antecipacao.observacoes}</p>
              </div>
            )}
          </CardContent>
        </Card>

        {/* Informações do Plantão e Profissional */}
        <Card>
          <CardHeader>
            <CardTitle className="flex items-center gap-2">
              <User className="h-5 w-5" />
              Plantão e Profissional
            </CardTitle>
          </CardHeader>
          <CardContent className="space-y-4">
            {antecipacao.plantao && (
              <>
                <div className="space-y-2">
                  <div className="flex items-center gap-2">
                    <User className="h-4 w-4 text-muted-foreground" />
                    <div>
                      <p className="font-semibold">
                        {antecipacao.profissional?.usuario?.nome || antecipacao.plantao.profissional?.usuario?.nome}
                      </p>
                      <p className="text-sm text-muted-foreground">
                        CPF: {antecipacao.profissional?.usuario?.cpf || antecipacao.plantao.profissional?.usuario?.cpf}
                      </p>
                      {antecipacao.profissional?.conselhoClasse && antecipacao.profissional?.numeroRegistro && (
                        <p className="text-sm text-muted-foreground">
                          {antecipacao.profissional.conselhoClasse}: {antecipacao.profissional.numeroRegistro}
                        </p>
                      )}
                    </div>
                  </div>
                </div>

                <Separator />

                <div className="space-y-2">
                  <div className="flex items-center gap-2">
                    <Building2 className="h-4 w-4 text-muted-foreground" />
                    <div>
                      <p className="font-semibold">{antecipacao.plantao.cliente?.nome}</p>
                      <p className="text-sm text-muted-foreground">CNPJ: {antecipacao.plantao.cliente?.cnpj}</p>
                    </div>
                  </div>
                </div>

                <div className="space-y-2">
                  <div className="flex items-center gap-2">
                    <MapPin className="h-4 w-4 text-muted-foreground" />
                    <div>
                      <p className="font-semibold">{antecipacao.plantao.localAtendimento?.nome}</p>
                      <p className="text-sm text-muted-foreground">{antecipacao.plantao.localAtendimento?.endereco}</p>
                      <p className="text-sm text-muted-foreground">
                        {antecipacao.plantao.localAtendimento?.cidade}, {antecipacao.plantao.localAtendimento?.estado}
                      </p>
                    </div>
                  </div>
                </div>

                <Separator />

                {/* <div className="grid grid-cols-2 gap-4">
                  <div>
                    <p className="text-sm font-medium text-muted-foreground">Período Plantão</p>
                    <p className="font-semibold">
                      {antecipacao.plantao.mes}/{antecipacao.plantao.ano}
                    </p>
                    <p className="text-sm text-muted-foreground">{antecipacao.plantao.modalidadeTrabalho}</p>
                  </div>
                  <div>
                    <p className="text-sm font-medium text-muted-foreground">Valor Total Plantão</p>
                    <p className="font-semibold">{formatCurrency(antecipacao.plantao.valorTotal || 0)}</p>
                    {antecipacao.plantao.valorBase && antecipacao.plantao.tipoValor && (
                      <p className="text-sm text-muted-foreground">
                        {formatCurrency(antecipacao.plantao.valorBase)}/{antecipacao.plantao.tipoValor.toLowerCase()}
                      </p>
                    )}
                  </div>
                </div> */}
              </>
            )}
          </CardContent>
        </Card>

        {/* Informações da CCB (se houver) */}
        {antecipacao.numeroCCB && (
          <Card>
            <CardHeader>
              <CardTitle className="flex items-center gap-2">
                <CreditCard className="h-5 w-5" />
                Informações da CCB
              </CardTitle>
            </CardHeader>
            <CardContent className="space-y-4">
              <div className="grid grid-cols-1 gap-4">
                <div>
                  <p className="text-sm font-medium text-muted-foreground">Número da CCB</p>
                  <p className="text-lg font-bold font-mono">{antecipacao.numeroCCB}</p>
                </div>

                {antecipacao.dataCCB && (
                  <div>
                    <p className="text-sm font-medium text-muted-foreground">Data de Emissão</p>
                    <p className="font-semibold">{formatDate(antecipacao.dataCCB)}</p>
                  </div>
                )}
              </div>

              {antecipacao.arquivoCCB ? (
                <Button variant="outline" className="w-full">
                  <Download className="h-4 w-4 mr-2" />
                  Baixar Arquivo da CCB
                </Button>
              ) : (
                <p className="text-sm text-muted-foreground text-center py-2">Arquivo da CCB não disponível</p>
              )}
            </CardContent>
          </Card>
        )}

        {/* Comprovante de Pagamento (se houver) */}
        {antecipacao.status === "PAGA" && antecipacao.comprovantePagamento && (
          <Card>
            <CardHeader>
              <CardTitle className="flex items-center gap-2">
                <CheckCircle2 className="h-5 w-5" />
                Comprovante de Pagamento
              </CardTitle>
            </CardHeader>
            <CardContent className="space-y-4">
              <div>
                <p className="text-sm font-medium text-muted-foreground">Data do Pagamento</p>
                <p className="font-semibold">{antecipacao.pagoEm ? formatDate(antecipacao.pagoEm) : "-"}</p>
              </div>

              <Button variant="outline" className="w-full">
                <Download className="h-4 w-4 mr-2" />
                Baixar Comprovante
              </Button>
            </CardContent>
          </Card>
        )}

        {/* Histórico de Aprovações */}
        <Card>
          <CardHeader>
            <CardTitle className="flex items-center gap-2">
              <Calendar className="h-5 w-5" />
              Histórico
            </CardTitle>
          </CardHeader>
          <CardContent className="space-y-4">
            <div className="space-y-3">
              <div className="flex items-center gap-3 p-3 bg-muted rounded-lg">
                <Calendar className="h-4 w-4 text-muted-foreground" />
                <div>
                  <p className="font-medium">Antecipação criada</p>
                  <p className="text-sm text-muted-foreground">{formatDate(antecipacao.createdAt)}</p>
                </div>
              </div>

              {antecipacao.status === "APROVADA" && antecipacao.aprovadoEm && (
                <div className="flex items-center gap-3 p-3 bg-green-50 dark:bg-green-950/20 rounded-lg">
                  <CheckCircle2 className="h-4 w-4 text-green-600" />
                  <div>
                    <p className="font-medium">Antecipação aprovada</p>
                    <p className="text-sm text-muted-foreground">{formatDate(antecipacao.aprovadoEm)}</p>
                    {antecipacao.aprovadoPor && (
                      <p className="text-xs text-muted-foreground">por: {antecipacao.aprovadoPor}</p>
                    )}
                  </div>
                </div>
              )}

              {antecipacao.numeroCCB && antecipacao.dataCCB && (
                <div className="flex items-center gap-3 p-3 bg-blue-50 dark:bg-blue-950/20 rounded-lg">
                  <FileText className="h-4 w-4 text-blue-600" />
                  <div>
                    <p className="font-medium">CCB gerada</p>
                    <p className="text-sm text-muted-foreground">{formatDate(antecipacao.dataCCB)}</p>
                    <p className="text-xs text-muted-foreground font-mono">#{antecipacao.numeroCCB}</p>
                  </div>
                </div>
              )}

              {antecipacao.status === "PAGA" && antecipacao.pagoEm && (
                <div className="flex items-center gap-3 p-3 bg-emerald-50 dark:bg-emerald-950/20 rounded-lg">
                  <DollarSign className="h-4 w-4 text-emerald-600" />
                  <div>
                    <p className="font-medium">Pagamento realizado</p>
                    <p className="text-sm text-muted-foreground">{formatDate(antecipacao.pagoEm)}</p>
                    {antecipacao.pagoPor && <p className="text-xs text-muted-foreground">por: {antecipacao.pagoPor}</p>}
                  </div>
                </div>
              )}

              {antecipacao.status === "REJEITADA" && (
                <div className="flex items-center gap-3 p-3 bg-red-50 dark:bg-red-950/20 rounded-lg">
                  <X className="h-4 w-4 text-red-600" />
                  <div>
                    <p className="font-medium">Antecipação rejeitada</p>
                    <p className="text-sm text-muted-foreground">{formatDate(antecipacao.updatedAt)}</p>
                  </div>
                </div>
              )}
            </div>
          </CardContent>
        </Card>
      </div>
    </div>
  );
}

export const Route = createFileRoute("/antecipacoes/$uuid")({
  component: AntecipacaoDetails,
  beforeLoad: async () => {
    await requireAdminRole();
  },
});
