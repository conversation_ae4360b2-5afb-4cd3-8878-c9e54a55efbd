import * as React from "react";
import { Check, ChevronsUpDown, X, Plus } from "lucide-react";
import { cn } from "@/lib/utils";
import { Button } from "@/components/ui/button";
import { Command, CommandEmpty, CommandGroup, CommandInput, CommandItem } from "@/components/ui/command";
import { Popover, PopoverContent, PopoverTrigger } from "@/components/ui/popover";
import { Badge } from "@/components/ui/badge";

export interface MultiSelectProps {
  options: Array<{ value: number | string; label: string }>;
  value?: Array<number | string>;
  onChange?: (value: Array<number | string>) => void;
  placeholder?: string;
  searchPlaceholder?: string;
  emptyText?: string;
  onAddNew?: () => void;
  addNewText?: string;
  className?: string;
  disabled?: boolean;
  onSearch?: (search: string) => void;
  hideBadges?: boolean;
}

export function MultiSelect({
  options,
  value = [],
  onChange,
  placeholder = "Selecione...",
  searchPlaceholder = "Buscar...",
  emptyText = "Nenhum item encontrado.",
  onAddNew,
  addNewText = "Adicionar novo",
  className,
  disabled,
  onSearch,
  hideBadges = false,
}: MultiSelectProps) {
  const [open, setOpen] = React.useState(false);
  const [search, setSearch] = React.useState("");

  const handleSelect = (optionValue: number | string) => {
    const newValue = value.includes(optionValue) ? value.filter((v) => v !== optionValue) : [...value, optionValue];
    onChange?.(newValue);
  };

  const handleRemove = (optionValue: number | string) => {
    onChange?.(value.filter((v) => v !== optionValue));
  };

  const handleSearchChange = (newSearch: string) => {
    setSearch(newSearch);
    if (onSearch) {
      onSearch(newSearch);
    }
  };

  const filteredOptions = onSearch
    ? options
    : options.filter((option) => option.label.toLowerCase().includes(search.toLowerCase()));

  const selectedOptions = options.filter((option) => value.includes(option.value));

  return (
    <Popover open={open} onOpenChange={setOpen}>
      <PopoverTrigger asChild>
        <Button
          variant="outline"
          role="combobox"
          aria-expanded={open}
          className={cn("w-full justify-between font-normal", !value.length && "text-muted-foreground", className)}
          disabled={disabled}
        >
          <div className="flex flex-wrap gap-1 flex-1">
            {!hideBadges && selectedOptions.length > 0 ? (
              selectedOptions.map((option) => (
                <Badge
                  key={option.value}
                  variant="secondary"
                  className="mr-1"
                  onClick={(e) => {
                    e.stopPropagation();
                    handleRemove(option.value);
                  }}
                >
                  {option.label}
                  <X className="ml-1 h-3 w-3 cursor-pointer" />
                </Badge>
              ))
            ) : (
              <span className={!value.length ? "text-muted-foreground" : ""}>
                {hideBadges && value.length > 0
                  ? `${value.length} selecionado${value.length > 1 ? "s" : ""}`
                  : placeholder}
              </span>
            )}
          </div>
          <ChevronsUpDown className="ml-2 h-4 w-4 shrink-0 opacity-50" />
        </Button>
      </PopoverTrigger>
      <PopoverContent className="w-full p-0" align="start">
        <Command>
          <CommandInput placeholder={searchPlaceholder} value={search} onValueChange={handleSearchChange} />
          <CommandEmpty>
            <div className="py-2 text-center text-sm">
              {emptyText}
              {onAddNew && (
                <Button
                  variant="ghost"
                  size="sm"
                  className="mt-2 w-full"
                  onClick={() => {
                    setOpen(false);
                    onAddNew();
                  }}
                >
                  <Plus className="mr-2 h-4 w-4" />
                  {addNewText}
                </Button>
              )}
            </div>
          </CommandEmpty>
          <CommandGroup className="max-h-64 overflow-auto">
            {filteredOptions.map((option) => (
              <CommandItem key={option.value} value={option.label} onSelect={() => handleSelect(option.value)}>
                <Check className={cn("mr-2 h-4 w-4", value.includes(option.value) ? "opacity-100" : "opacity-0")} />
                {option.label}
              </CommandItem>
            ))}
            {!filteredOptions.length && onAddNew && search && (
              <CommandItem
                value={search}
                onSelect={() => {
                  setOpen(false);
                  onAddNew();
                }}
              >
                <Plus className="mr-2 h-4 w-4" />
                {addNewText}: "{search}"
              </CommandItem>
            )}
          </CommandGroup>
        </Command>
      </PopoverContent>
    </Popover>
  );
}
