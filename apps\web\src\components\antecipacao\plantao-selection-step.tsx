import { Card, CardContent, CardDescription, CardHeader, CardTitle } from "@/components/ui/card";
import { Button } from "@/components/ui/button";
import { Badge } from "@/components/ui/badge";
import { Building2, MapPin, User, ChevronRight } from "lucide-react";
import { formatCurrency, formatDate } from "@/lib/utils";
import type { Plantao } from "@/lib/api";
import { FilterCard } from "./filter-card";

interface PlantaoSelectionStepProps {
  plantoes: { data: Plantao[] } | undefined;
  filters: {
    dataInicial: string;
    dataFinal: string;
    search: string;
  };
  onFiltersChange: (filters: { dataInicial: string; dataFinal: string; search: string }) => void;
  onPlantaoSelect: (plantaoId: string) => void;
}

export function PlantaoSelectionStep({
  plantoes,
  filters,
  onFiltersChange,
  onPlantaoSelect,
}: PlantaoSelectionStepProps) {
  return (
    <div className="space-y-6">
      <FilterCard filters={filters} onFiltersChange={onFiltersChange} />

      <Card>
        <CardHeader>
          <CardTitle>Plantões Disponíveis</CardTitle>
          <CardDescription>
            {plantoes?.data?.length || 0} plantões com fechamentos aprovados para antecipação
          </CardDescription>
        </CardHeader>
        <CardContent className="space-y-4">
          {!plantoes?.data || plantoes.data.length === 0 ? (
            <div className="text-center py-8">
              <p className="text-muted-foreground">Nenhum plantão disponível para antecipação</p>
            </div>
          ) : (
            <div className="space-y-4">
              {plantoes.data.map((plantao) => (
                <div key={plantao.uuid} className="border rounded-lg p-4 hover:bg-muted/50 transition-colors">
                  <div className="flex items-center justify-between">
                    <div className="space-y-2">
                      <div className="flex items-center gap-2">
                        <User className="h-4 w-4 text-muted-foreground" />
                        <span className="font-semibold">{plantao.profissional?.usuario?.nome}</span>
                      </div>
                      <div className="flex items-center gap-2">
                        <Building2 className="h-4 w-4 text-muted-foreground" />
                        <span>{plantao.cliente?.nome}</span>
                      </div>
                      <div className="flex items-center gap-2">
                        <MapPin className="h-4 w-4 text-muted-foreground" />
                        <span>{plantao.localAtendimento?.nome}</span>
                      </div>
                      <div className="flex items-center gap-4 text-sm text-muted-foreground">
                        <span>{formatDate(plantao.createdAt)}</span>
                        <Badge variant="outline">
                          {plantao._count?.fechamentos} fechamento{plantao._count?.fechamentos === 1 ? "" : "s"}
                        </Badge>
                        <span>
                          Total:{" "}
                          {formatCurrency(
                            (plantao as any).fechamentos?.reduce((acc: number, f: any) => acc + f.totalValor, 0) || 0
                          )}
                        </span>
                      </div>
                    </div>
                    <Button onClick={() => onPlantaoSelect(plantao.uuid)} className="flex items-center gap-2">
                      Selecionar
                      <ChevronRight className="h-4 w-4" />
                    </Button>
                  </div>
                </div>
              ))}
            </div>
          )}
        </CardContent>
      </Card>
    </div>
  );
}
