import { useState } from "react";
import * as XLSX from "xlsx";
import { saveAs } from "file-saver";
import { Button } from "@/components/ui/button";
import { Input } from "@/components/ui/input";
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from "@/components/ui/card";
import { Alert, AlertDescription } from "@/components/ui/alert";
import { Download, Upload, FileSpreadsheet, AlertCircle, CheckCircle2, Loader2 } from "lucide-react";
import { toast } from "sonner";
import { getDateOnly, getCurrentDate } from "@/lib/utils";

interface ImportExportProps {
  entityType: "clientes" | "profissionais" | "locais" | "escalas";
  onImport: (data: any[]) => Promise<void>;
  exportData?: any[];
  templateData?: any[];
}

export function ImportExport({ entityType, onImport, exportData, templateData }: ImportExportProps) {
  const [isImporting, setIsImporting] = useState(false);
  const [importedData, setImportedData] = useState<any[]>([]);
  const [errors, setErrors] = useState<string[]>([]);

  const getTemplateColumns = () => {
    switch (entityType) {
      case "clientes":
        return {
          nome: "Nome da Empresa",
          tipo: "PF ou PJ",
          cnpj: "CPF/CNPJ",
          email: "<EMAIL>",
          telefone: "(11) 99999-9999",
          endereco: "Rua Exemplo, 123",
          cidade: "São Paulo",
          estado: "SP",
          cep: "00000-000",
        };
      case "profissionais":
        return {
          nome: "Nome Completo",
          cpf: "000.000.000-00",
          rg: "00.000.000-0",
          dataNascimento: "01/01/1990",
          email: "<EMAIL>",
          telefone: "(11) 99999-9999",
          endereco: "Rua Exemplo, 123",
          cidade: "São Paulo",
          estado: "SP",
          cep: "00000-000",
          banco: "Banco do Brasil",
          agencia: "0001",
          conta: "12345-6",
          tipoConta: "CORRENTE",
          pix: "<EMAIL>",
        };
      case "locais":
        return {
          clienteNome: "Nome do Cliente",
          nome: "Nome do Local",
          endereco: "Rua Exemplo, 123",
          cidade: "São Paulo",
          estado: "SP",
          cep: "00000-000",
          telefone: "(11) 99999-9999",
          responsavel: "Nome do Responsável",
        };
      case "escalas":
        return {
          clienteNome: "Nome do Cliente",
          localNome: "Nome do Local",
          profissionalNome: "Nome do Profissional",
          mes: "1-12",
          ano: "2024",
          modalidadeTrabalho: "PLANTONISTA",
          valorHora: "50.00",
          valorPlantao: "500.00",
          valorMensal: "5000.00",
          horaInicio: "07:00",
          horaFim: "19:00",
          diasSelecionados: "1,2,3,4,5",
          percentualAntecipacao: "70",
        };
      default:
        return {};
    }
  };

  const handleFileUpload = async (event: React.ChangeEvent<HTMLInputElement>) => {
    const file = event.target.files?.[0];
    if (!file) return;

    setIsImporting(true);
    setErrors([]);

    try {
      const data = await readExcelFile(file);
      const validatedData = validateData(data);

      if (validatedData.errors.length > 0) {
        setErrors(validatedData.errors);
        setImportedData([]);
        toast.error(`Encontrados ${validatedData.errors.length} erros na importação`);
      } else {
        setImportedData(validatedData.data);
        toast.success(`${validatedData.data.length} registros prontos para importação`);
      }
    } catch (error) {
      toast.error("Erro ao ler arquivo");
      setErrors(["Erro ao processar arquivo. Verifique o formato."]);
    } finally {
      setIsImporting(false);
    }
  };

  const readExcelFile = (file: File): Promise<any[]> => {
    return new Promise((resolve, reject) => {
      const reader = new FileReader();

      reader.onload = (e) => {
        try {
          const data = new Uint8Array(e.target?.result as ArrayBuffer);
          const workbook = XLSX.read(data, { type: "array" });
          const sheetName = workbook.SheetNames[0];
          const worksheet = workbook.Sheets[sheetName];
          const jsonData = XLSX.utils.sheet_to_json(worksheet);
          resolve(jsonData);
        } catch (error) {
          reject(error);
        }
      };

      reader.onerror = reject;
      reader.readAsArrayBuffer(file);
    });
  };

  const validateData = (data: any[]) => {
    const errors: string[] = [];
    const validData: any[] = [];

    data.forEach((row, index) => {
      const rowNum = index + 2; // Excel rows start at 1, plus header row

      switch (entityType) {
        case "clientes":
          if (!row.nome) {
            errors.push(`Linha ${rowNum}: Nome é obrigatório`);
          }
          if (!row.tipo || !["PF", "PJ"].includes(row.tipo)) {
            errors.push(`Linha ${rowNum}: Tipo deve ser PF ou PJ`);
          }
          if (!row.cnpj) {
            errors.push(`Linha ${rowNum}: Documento é obrigatório`);
          }
          break;

        case "profissionais":
          if (!row.nome) {
            errors.push(`Linha ${rowNum}: Nome é obrigatório`);
          }
          if (!row.cpf) {
            errors.push(`Linha ${rowNum}: CPF é obrigatório`);
          }
          if (!row.email) {
            errors.push(`Linha ${rowNum}: Email é obrigatório`);
          }
          break;

        case "locais":
          if (!row.clienteNome) {
            errors.push(`Linha ${rowNum}: Cliente é obrigatório`);
          }
          if (!row.nome) {
            errors.push(`Linha ${rowNum}: Nome do local é obrigatório`);
          }
          if (!row.endereco) {
            errors.push(`Linha ${rowNum}: Endereço é obrigatório`);
          }
          break;

        case "escalas":
          if (!row.clienteNome || !row.localNome || !row.profissionalNome) {
            errors.push(`Linha ${rowNum}: Cliente, Local e Profissional são obrigatórios`);
          }
          if (!row.mes || !row.ano) {
            errors.push(`Linha ${rowNum}: Mês e Ano são obrigatórios`);
          }
          break;
      }

      if (errors.length === 0 || errors.filter((e) => e.includes(`Linha ${rowNum}`)).length === 0) {
        validData.push(row);
      }
    });

    return { data: validData, errors };
  };

  const handleImport = async () => {
    if (importedData.length === 0) {
      toast.error("Nenhum dado para importar");
      return;
    }

    setIsImporting(true);
    try {
      await onImport(importedData);
      toast.success(`${importedData.length} registros importados com sucesso!`);
      setImportedData([]);
      setErrors([]);
    } catch (error) {
      toast.error("Erro ao importar dados");
    } finally {
      setIsImporting(false);
    }
  };

  const handleExport = () => {
    if (!exportData || exportData.length === 0) {
      toast.error("Nenhum dado para exportar");
      return;
    }

    const ws = XLSX.utils.json_to_sheet(exportData);
    const wb = XLSX.utils.book_new();
    XLSX.utils.book_append_sheet(wb, ws, entityType);

    const excelBuffer = XLSX.write(wb, { bookType: "xlsx", type: "array" });
    const data = new Blob([excelBuffer], {
      type: "application/vnd.openxmlformats-officedocument.spreadsheetml.sheet",
    });

    saveAs(data, `${entityType}_${getDateOnly(getCurrentDate().toISOString())}.xlsx`);
    toast.success("Dados exportados com sucesso!");
  };

  const handleDownloadTemplate = () => {
    const templateData = [getTemplateColumns()];
    const ws = XLSX.utils.json_to_sheet(templateData);
    const wb = XLSX.utils.book_new();
    XLSX.utils.book_append_sheet(wb, ws, "Template");

    const excelBuffer = XLSX.write(wb, { bookType: "xlsx", type: "array" });
    const data = new Blob([excelBuffer], {
      type: "application/vnd.openxmlformats-officedocument.spreadsheetml.sheet",
    });

    saveAs(data, `template_${entityType}.xlsx`);
    toast.success("Template baixado com sucesso!");
  };

  return (
    <Card>
      <CardHeader>
        <CardTitle>Importação e Exportação</CardTitle>
        <CardDescription>Importe ou exporte dados em massa usando planilhas Excel</CardDescription>
      </CardHeader>
      <CardContent className="space-y-4">
        <div className="grid gap-4 md:grid-cols-3">
          <Button onClick={handleDownloadTemplate} variant="outline">
            <FileSpreadsheet className="mr-2 h-4 w-4" />
            Baixar Template
          </Button>

          <div className="relative">
            <Input
              type="file"
              accept=".xlsx,.xls"
              onChange={handleFileUpload}
              disabled={isImporting}
              className="cursor-pointer"
            />
            {isImporting && <Loader2 className="absolute right-3 top-3 h-4 w-4 animate-spin" />}
          </div>

          <Button onClick={handleExport} disabled={!exportData || exportData.length === 0}>
            <Download className="mr-2 h-4 w-4" />
            Exportar Dados
          </Button>
        </div>

        {errors.length > 0 && (
          <Alert variant="destructive">
            <AlertCircle className="h-4 w-4" />
            <AlertDescription>
              <div className="space-y-1">
                <p className="font-semibold">Erros encontrados:</p>
                <ul className="list-disc list-inside text-sm max-h-32 overflow-y-auto">
                  {errors.map((error, index) => (
                    <li key={index}>{error}</li>
                  ))}
                </ul>
              </div>
            </AlertDescription>
          </Alert>
        )}

        {importedData.length > 0 && (
          <Alert>
            <CheckCircle2 className="h-4 w-4 text-green-600" />
            <AlertDescription>
              <div className="flex items-center justify-between">
                <span>{importedData.length} registros prontos para importação</span>
                <Button size="sm" onClick={handleImport} disabled={isImporting}>
                  {isImporting ? (
                    <>
                      <Loader2 className="mr-2 h-4 w-4 animate-spin" />
                      Importando...
                    </>
                  ) : (
                    <>
                      <Upload className="mr-2 h-4 w-4" />
                      Confirmar Importação
                    </>
                  )}
                </Button>
              </div>
            </AlertDescription>
          </Alert>
        )}

        <div className="rounded-lg border p-4 bg-muted/50">
          <h4 className="font-semibold mb-2">Instruções:</h4>
          <ul className="text-sm space-y-1 text-muted-foreground">
            <li>• Baixe o template para ver o formato correto dos dados</li>
            <li>• Preencha a planilha seguindo o modelo do template</li>
            <li>• Salve o arquivo em formato Excel (.xlsx)</li>
            <li>• Selecione o arquivo para visualizar e validar os dados</li>
            <li>• Confirme a importação após a validação</li>
          </ul>
        </div>
      </CardContent>
    </Card>
  );
}
