import { prisma } from "@/lib/prisma";
import type { FastifyTypedInstance } from "@/types";
import type { AuthenticatedRequest } from "@/middlewares/auth.middleware";

export async function termosRouter(app: FastifyTypedInstance) {
  // Buscar última versão ativa dos termos
  app.get("/termos/atual", async (request: AuthenticatedRequest, reply) => {
    try {
      const userId = request.user!.id;

      // Busca a versão ativa atual dos termos
      const termoAtual = await prisma.gerenciamentoTermosLgpd.findFirst({
        where: {
          ativo: true,
          deletedAt: null,
          validoDe: { lte: new Date() },
          OR: [{ validoAte: null }, { validoAte: { gte: new Date() } }],
        },
        orderBy: {
          versao: "desc",
        },
      });

      if (!termoAtual) {
        return reply.status(404).send({
          error: "Nenhum termo ativo encontrado",
        });
      }

      // Verifica se o usuário j<PERSON> aceitou esta versão
      const aceiteExistente = await prisma.aceiteTermosLgpd.findFirst({
        where: {
          usuarioId: userId,
          termoVersaoId: termoAtual.id,
          consentimentoLgpd: true,
        },
      });

      return reply.send({
        termo: {
          id: termoAtual.id,
          versao: termoAtual.versao,
          titulo: termoAtual.titulo,
          conteudo: termoAtual.conteudo,
          validoDe: termoAtual.validoDe,
          validoAte: termoAtual.validoAte,
        },
        aceito: !!aceiteExistente,
        dataAceite: aceiteExistente?.aceitoEm || null,
      });
    } catch (error) {
      console.error("Erro ao buscar termos LGPD:", error);
      return reply.status(500).send({
        error: "Erro ao buscar termos",
      });
    }
  });

  // Registrar aceite dos termos
  app.post<{
    Body: {
      termoVersaoId: number;
      consentimento: boolean;
      enderecoIp?: string;
      latitude?: number;
      longitude?: number;
      userAgent?: string;
    };
  }>("/termos/aceitar", async (request: AuthenticatedRequest, reply) => {
    try {
      const userId = request.user!.id;
      const { termoVersaoId, consentimento, enderecoIp, latitude, longitude, userAgent } = request.body as any;

      // Verifica se o termo existe e está ativo
      const termo = await prisma.gerenciamentoTermosLgpd.findFirst({
        where: {
          id: termoVersaoId,
          ativo: true,
          deletedAt: null,
        },
      });

      if (!termo) {
        return reply.status(404).send({
          error: "Termo não encontrado ou inativo",
        });
      }

      // Verifica se já existe aceite para esta versão
      const aceiteExistente = await prisma.aceiteTermosLgpd.findFirst({
        where: {
          usuarioId: userId,
          termoVersaoId: termoVersaoId,
        },
      });

      if (aceiteExistente) {
        return reply.status(400).send({
          error: "Termos já foram aceitos anteriormente",
        });
      }

      // Cria o registro de aceite
      const aceite = await prisma.aceiteTermosLgpd.create({
        data: {
          usuarioId: userId,
          termoVersaoId: termoVersaoId,
          consentimentoLgpd: consentimento,
          aceitoEm: new Date(),
          enderecoIp: enderecoIp || request.ip,
          latitude: latitude || 0,
          longitude: longitude || 0,
          userAgent: userAgent || request.headers["user-agent"] || "",
          termoVersao: termo.versao,
          dadosAdicionais: {
            origem: "aplicacao_web",
            timestamp: new Date().toISOString(),
          },
        },
      });

      return reply.send({
        success: true,
        aceiteId: aceite.id,
        message: "Termos aceitos com sucesso",
      });
    } catch (error) {
      console.error("Erro ao registrar aceite dos termos:", error);
      return reply.status(500).send({
        error: "Erro ao registrar aceite",
      });
    }
  });

  // Verificar status de aceite do usuário
  app.get("/termos/status", async (request: AuthenticatedRequest, reply) => {
    try {
      const userId = request.user!.id;

      // Busca a versão ativa atual dos termos
      const termoAtual = await prisma.gerenciamentoTermosLgpd.findFirst({
        where: {
          ativo: true,
          deletedAt: null,
          validoDe: { lte: new Date() },
          OR: [{ validoAte: null }, { validoAte: { gte: new Date() } }],
        },
        orderBy: {
          versao: "desc",
        },
      });

      if (!termoAtual) {
        return reply.send({
          precisaAceitar: false,
          termoAtual: null,
        });
      }

      // Verifica se o usuário já aceitou esta versão
      const aceiteExistente = await prisma.aceiteTermosLgpd.findFirst({
        where: {
          usuarioId: userId,
          termoVersaoId: termoAtual.id,
          consentimentoLgpd: true,
        },
      });

      return reply.send({
        precisaAceitar: !aceiteExistente,
        termoAtual: !aceiteExistente
          ? {
              id: termoAtual.id,
              versao: termoAtual.versao,
            }
          : null,
        ultimoAceite: aceiteExistente
          ? {
              dataAceite: aceiteExistente.aceitoEm,
              versao: aceiteExistente.termoVersao,
            }
          : null,
      });
    } catch (error) {
      console.error("Erro ao verificar status dos termos:", error);
      return reply.status(500).send({
        error: "Erro ao verificar status",
      });
    }
  });
}
