import { useQuery } from "@tanstack/react-query";
import { api } from "@/lib/api";
import { <PERSON>, CardContent, CardHeader, CardTitle } from "@/components/ui/card";
import { Progress } from "@/components/ui/progress";
import { Skeleton } from "@/components/ui/skeleton";
import { CheckCircle2, CircleDashed, TrendingUp, Calendar, DollarSign, Users } from "lucide-react";
import { formatCurrency } from "@/lib/utils";

interface PlantaoStatistics {
  total: number;
  completed: number;
  ongoing: number;
  completionRate: number;
  totalValue: number;
  completedValue: number;
  ongoingValue: number;
  profissionaisAtivos: number;
}

export function PlantaoStatistics({ mes, ano }: { mes?: number; ano?: number }) {
  const { data: stats, isLoading } = useQuery<PlantaoStatistics>({
    queryKey: ["plantao-statistics", mes, ano],
    queryFn: async (): Promise<PlantaoStatistics> => {
      const params: any = {};
      if (mes) params.mes = mes;
      if (ano) params.ano = ano;

      // Fetch statistics from API
      const response = await api.get("/plantoes/statistics", params);
      return response as PlantaoStatistics;
    },
  });

  if (isLoading) {
    return (
      <div className="grid gap-4 md:grid-cols-2 lg:grid-cols-4">
        {[...Array(4)].map((_, i) => (
          <Card key={i}>
            <CardHeader className="flex flex-row items-center justify-between space-y-0 pb-2">
              <Skeleton className="h-4 w-[100px]" />
              <Skeleton className="h-4 w-4" />
            </CardHeader>
            <CardContent>
              <Skeleton className="h-7 w-[60px]" />
              <Skeleton className="h-3 w-[120px] mt-1" />
            </CardContent>
          </Card>
        ))}
      </div>
    );
  }

  if (!stats) return null;

  return (
    <div className="space-y-4">
      <div className="grid gap-4 md:grid-cols-2 lg:grid-cols-4">
        <Card>
          <CardHeader className="flex flex-row items-center justify-between space-y-0 pb-2">
            <CardTitle className="text-sm font-medium">Total de Plantões</CardTitle>
            <Calendar className="h-4 w-4 text-muted-foreground" />
          </CardHeader>
          <CardContent>
            <div className="text-2xl font-bold">{stats?.total || 0}</div>
            <p className="text-xs text-muted-foreground">
              {stats?.completed || 0} concluídos, {stats?.ongoing || 0} em andamento
            </p>
          </CardContent>
        </Card>

        <Card>
          <CardHeader className="flex flex-row items-center justify-between space-y-0 pb-2">
            <CardTitle className="text-sm font-medium">Taxa de Conclusão</CardTitle>
            <TrendingUp className="h-4 w-4 text-muted-foreground" />
          </CardHeader>
          <CardContent>
            <div className="text-2xl font-bold">{(stats?.completionRate || 0).toFixed(1)}%</div>
            <Progress value={stats?.completionRate || 0} className="mt-2" />
          </CardContent>
        </Card>

        <Card>
          <CardHeader className="flex flex-row items-center justify-between space-y-0 pb-2">
            <CardTitle className="text-sm font-medium">Valor Total</CardTitle>
            <DollarSign className="h-4 w-4 text-muted-foreground" />
          </CardHeader>
          <CardContent>
            <div className="text-2xl font-bold">{formatCurrency(stats?.totalValue || 0)}</div>
            <p className="text-xs text-muted-foreground">{formatCurrency(stats?.completedValue || 0)} realizados</p>
          </CardContent>
        </Card>

        <Card>
          <CardHeader className="flex flex-row items-center justify-between space-y-0 pb-2">
            <CardTitle className="text-sm font-medium">Profissionais Ativos</CardTitle>
            <Users className="h-4 w-4 text-muted-foreground" />
          </CardHeader>
          <CardContent>
            <div className="text-2xl font-bold">{stats?.profissionaisAtivos || 0}</div>
            <p className="text-xs text-muted-foreground">Com plantões este período</p>
          </CardContent>
        </Card>
      </div>

      <div className="grid gap-4 md:grid-cols-2">
        <Card>
          <CardHeader>
            <CardTitle className="text-sm font-medium">Status dos Plantões</CardTitle>
          </CardHeader>
          <CardContent className="space-y-3">
            <div className="flex items-center justify-between">
              <div className="flex items-center gap-2">
                <CheckCircle2 className="h-4 w-4 text-green-500" />
                <span className="text-sm">Concluídos</span>
              </div>
              <div className="flex items-center gap-2">
                <span className="text-sm font-bold">{stats?.completed || 0}</span>
                <span className="text-xs text-muted-foreground">
                  ({(((stats?.completed || 0) / (stats?.total || 1)) * 100).toFixed(0)}%)
                </span>
              </div>
            </div>
            <div className="flex items-center justify-between">
              <div className="flex items-center gap-2">
                <CircleDashed className="h-4 w-4 text-blue-500" />
                <span className="text-sm">Em andamento</span>
              </div>
              <div className="flex items-center gap-2">
                <span className="text-sm font-bold">{stats?.ongoing || 0}</span>
                <span className="text-xs text-muted-foreground">
                  ({(((stats?.ongoing || 0) / (stats?.total || 1)) * 100).toFixed(0)}%)
                </span>
              </div>
            </div>
          </CardContent>
        </Card>

        <Card>
          <CardHeader>
            <CardTitle className="text-sm font-medium">Valores por Status</CardTitle>
          </CardHeader>
          <CardContent className="space-y-3">
            <div className="flex items-center justify-between">
              <div className="flex items-center gap-2">
                <CheckCircle2 className="h-4 w-4 text-green-500" />
                <span className="text-sm">Valor Concluído</span>
              </div>
              <span className="text-sm font-bold">{formatCurrency(stats?.completedValue || 0)}</span>
            </div>
            <div className="flex items-center justify-between">
              <div className="flex items-center gap-2">
                <CircleDashed className="h-4 w-4 text-blue-500" />
                <span className="text-sm">Valor em Andamento</span>
              </div>
              <span className="text-sm font-bold">{formatCurrency(stats?.ongoingValue || 0)}</span>
            </div>
            <div className="pt-2 border-t">
              <div className="flex items-center justify-between">
                <span className="text-sm font-medium">Total</span>
                <span className="text-sm font-bold">{formatCurrency(stats?.totalValue || 0)}</span>
              </div>
            </div>
          </CardContent>
        </Card>
      </div>
    </div>
  );
}
