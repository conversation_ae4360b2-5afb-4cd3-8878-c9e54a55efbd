import { z } from "zod";
import { prisma, withAudit } from "@/lib/prisma";
import {
  createPlantaoSchema,
  plantaoQuerySchema,
  type CreatePlantaoInput,
  type PlantaoQuery,
} from "@/schemas/plantao.schema";
import type { FastifyTypedInstance, FastifyTypedRequest } from "@/types";
import { formatDateToDateTime, getCurrentYear, parseUTCDate, toISOString } from "@shared/date";
import { getEndOfMonthInTimezone, getStartOfMonthInTimezone } from "@shared/date";

export function estatisticasPlantaoRouter(fastify: FastifyTypedInstance) {
  // Obter estatísticas de plantões
  fastify.get<{ Querystring: { mes?: string; ano?: string } }>(
    "/plantoes/statistics",
    withAudit(async (request, reply) => {
      const clienteId = request.clienteId;
      const { mes, ano } = request.query;

      const where = {
        ...(mes && { mes: Number(mes) }),
        ...(ano && { ano: Number(ano) }),
        clienteId,
      };

      // Buscar todos os plantões com os filtros aplicados
      const plantoes = await prisma.plantao.findMany({
        where,
        include: {
          profissional: true,
        },
      });

      // Calcular estatísticas
      const total = plantoes.length;
      const completed = plantoes.filter((p) => p.concluidoEm !== null).length;
      const ongoing = total - completed;
      const completionRate = total > 0 ? (completed / total) * 100 : 0;

      // Calcular valores
      const totalValue = plantoes.reduce((sum, p) => sum + (p.valorBase || 0), 0);
      const completedValue = plantoes
        .filter((p) => p.concluidoEm !== null)
        .reduce((sum, p) => sum + (p.valorBase || 0), 0);
      const ongoingValue = totalValue - completedValue;

      // Contar profissionais únicos
      const profissionaisAtivos = new Set(plantoes.map((p) => p.profissionalId)).size;

      return reply.send({
        total,
        completed,
        ongoing,
        completionRate,
        totalValue,
        completedValue,
        ongoingValue,
        profissionaisAtivos,
      });
    })
  );
}
