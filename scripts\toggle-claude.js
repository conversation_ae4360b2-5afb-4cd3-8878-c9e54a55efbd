#!/usr/bin/env node

import { readFileSync, writeFileSync, existsSync } from "fs";
import { join, dirname } from "path";
import { fileURLToPath } from "url";

const __filename = fileURLToPath(import.meta.url);
const __dirname = dirname(__filename);

const claudeDir = join(__dirname, "..", ".claude");
const currentConfig = join(claudeDir, "settings.local.json");

function loadEnvConfig() {
  const envPath = join(__dirname, "..", ".env");

  if (!existsSync(envPath)) {
    console.error("❌ Arquivo .env não encontrado");
    return null;
  }

  try {
    const envContent = readFileSync(envPath, "utf8");
    const envVars = {};

    envContent.split("\n").forEach((line) => {
      const trimmed = line.trim();
      if (trimmed && !trimmed.startsWith("#")) {
        const [key, value] = trimmed.split("=");
        if (key && value) {
          envVars[key.trim()] = value.trim();
        }
      }
    });

    return {
      ANTHROPIC_MODEL: envVars.ANTHROPIC_MODEL,
      ANTHROPIC_BASE_URL: envVars.ANTHROPIC_BASE_URL,
      ANTHROPIC_AUTH_TOKEN: envVars.ANTHROPIC_AUTH_TOKEN,
    };
  } catch (error) {
    console.error("❌ Erro ao ler .env:", error.message);
    return null;
  }
}

function getCurrentConfig() {
  if (!existsSync(currentConfig)) {
    return null;
  }

  try {
    const content = readFileSync(currentConfig, "utf8");
    const config = JSON.parse(content);
    return config.env && config.env.ANTHROPIC_MODEL ? "custom" : "default";
  } catch (error) {
    console.error("❌ Erro ao ler configuração atual:", error.message);
    return null;
  }
}

function setConfig(type) {
  try {
    let config = {};

    if (existsSync(currentConfig)) {
      const content = readFileSync(currentConfig, "utf8");
      config = JSON.parse(content);
    }

    if (type === "custom") {
      const envConfig = loadEnvConfig();
      if (!envConfig) {
        return false;
      }
      config.env = envConfig;
      console.log(`✅ Configuração alterada para: Custom (${envConfig.ANTHROPIC_MODEL})`);
    } else if (type === "default") {
      delete config.env;
      console.log("✅ Configuração alterada para: Default Claude");
    } else {
      console.error("❌ Configuração inválida. Use: default ou custom");
      return false;
    }

    writeFileSync(currentConfig, JSON.stringify(config, null, 2));
    return true;
  } catch (error) {
    console.error("❌ Erro ao salvar configuração:", error.message);
    return false;
  }
}

function showHelp() {
  const current = getCurrentConfig();
  const currentText = current === "custom" ? "Custom (glm-4.5)" : "Default Claude";

  console.log(`📋 Configuração atual: ${currentText}\n`);
  console.log("Uso: yarn claude [default|custom]\n");
  console.log("Opções:");
  console.log("  default  - Usar Claude padrão da Anthropic");
  console.log("  custom   - Usar configuração customizada (glm-4.5)");
}

// Processar argumentos
const arg = process.argv[2];

if (!arg) {
  showHelp();
} else if (arg === "default" || arg === "custom") {
  setConfig(arg);
} else {
  console.error("❌ Argumento inválido. Use: default ou custom");
  process.exit(1);
}
