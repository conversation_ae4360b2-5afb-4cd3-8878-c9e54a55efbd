import { prisma } from "@lib/prisma";
import {
  createProfissionalSchema,
  updateProfissionalSchema,
  profissionalQuerySchema,
  type CreateProfissionalInput,
  type UpdateProfissionalInput,
  type ProfissionalQuery,
} from "../schemas/profissional.schema";
import { parseUTCDate } from "@shared/date";
import type { FastifyTypedInstance } from "@/types";
import { UtilRepository } from "../repositories/util.repository";
import { generatePass } from "@/lib/auth";
import { sendWelcomeEmail } from "@/lib/mail";
import { authorize } from "@/middlewares/auth.middleware";
import { appEnv } from "@/lib/env";

export async function profissionalRouter(fastify: FastifyTypedInstance) {
  const utilRepository = new UtilRepository(prisma);
  // Listar profissionais
  fastify.get<{ Querystring: ProfissionalQuery }>("/profissionais", async (request, reply) => {
    const parsedQuery = profissionalQuerySchema.parse(request.query);
    const { page = 1, limit = 10, search, ativo, especialidade } = parsedQuery;
    const pageNum = Number(page);
    const limitNum = Number(limit);
    const skip = (pageNum - 1) * limitNum;
    const clienteId = request.clienteId;

    const where: any = {
      ...(search && {
        usuario: {
          OR: [{ nome: { contains: search } }, { cpf: { contains: search } }, { email: { contains: search } }],
        },
      }),
      usuario: {
        clientes: {
          some: {
            clienteId,
            ativo: true,
          },
        },
      },
    };

    const [profissionais, total] = await Promise.all([
      prisma.profissional.findMany({
        where,
        skip,
        take: limitNum,
        include: {
          usuario: {
            omit: {
              senha: true,
            },
            include: {
              clientes: {
                include: {
                  cliente: true,
                },
              },
            },
          },
          especialidades: {
            include: {
              especialidade: true,
            },
          },
          _count: {
            select: {
              plantoes: true,
              fechamentos: true,
              antecipacoes: true,
            },
          },
        },
        orderBy: { createdAt: "desc" },
      }),
      prisma.profissional.count({ where }),
    ]);

    return reply.send({
      data: profissionais,
      meta: {
        page: pageNum,
        limit: limitNum,
        total,
        totalPages: Math.ceil(total / limitNum),
      },
    });
  });

  // Listar profissionais filtrados por cliente e especialidades do local
  fastify.get<{ Querystring: { clienteUuid: string; localUuid?: string } }>(
    "/profissionais/por-cliente-local",
    async (request, reply) => {
      const { clienteUuid: queryClienteUuid, localUuid } = request.query;
      const clienteUuidFromHeader = request.clienteUuid as string;

      // Use the query parameter clienteUuid or fall back to header clienteUuid
      const targetClienteUuid = queryClienteUuid || clienteUuidFromHeader;

      if (!targetClienteUuid) {
        return reply.status(400).send({ error: "Cliente UUID é obrigatório" });
      }

      // Buscar o cliente
      const cliente = await prisma.cliente.findUnique({
        where: { uuid: targetClienteUuid },
        select: { id: true },
      });

      if (!cliente) {
        return reply.status(404).send({ error: "Cliente não encontrado" });
      }

      // Se localUuid foi fornecido, buscar as especialidades do local
      let especialidadeIds: number[] = [];
      if (localUuid) {
        const local = await prisma.localAtendimento.findUnique({
          where: { uuid: localUuid },
          select: {
            especialidades: {
              select: {
                especialidadeId: true,
              },
            },
          },
        });

        if (local) {
          especialidadeIds = local.especialidades.map((e) => e.especialidadeId);
        }
      }

      // Buscar profissionais com os filtros
      const profissionais = await prisma.profissional.findMany({
        where: {
          usuario: {
            clientes: {
              some: {
                clienteId: cliente.id,
                ativo: true,
              },
            },
            ativo: true,
          },
          ...(especialidadeIds.length > 0 && {
            especialidades: {
              some: {
                especialidadeId: {
                  in: especialidadeIds,
                },
              },
            },
          }),
        },
        include: {
          usuario: {
            select: {
              id: true,
              uuid: true,
              nome: true,
              cpf: true,
              email: true,
              telefone: true,
              ativo: true,
            },
          },
          especialidades: {
            include: {
              especialidade: true,
            },
          },
        },
        orderBy: {
          usuario: {
            nome: "asc",
          },
        },
      });

      return reply.send(profissionais);
    }
  );

  // Buscar profissional por UUID
  fastify.get<{ Params: { uuid: string } }>("/profissionais/:uuid", async (request, reply) => {
    const { uuid } = request.params;
    const clienteId = request.clienteId;

    const where = {
      uuid,
      usuario: {
        clientes: {
          some: {
            clienteId,
            ativo: true,
          },
        },
      },
    };

    const profissional = await prisma.profissional.findFirst({
      where,
      include: {
        usuario: {
          include: {
            clientes: {
              include: {
                cliente: true,
              },
            },
          },
        },
        especialidades: {
          include: {
            especialidade: true,
          },
        },
        plantoes: {
          include: {
            cliente: true,
            localAtendimento: true,
          },
          orderBy: { createdAt: "desc" },
          take: 10,
        },
        fechamentos: {
          orderBy: { createdAt: "desc" },
          take: 10,
        },
        antecipacoes: {
          orderBy: { createdAt: "desc" },
          take: 10,
        },
      },
    });

    if (!profissional) {
      return reply.status(404).send({ error: "Profissional não encontrado" });
    }

    return reply.send(profissional);
  });

  // Listar plantões do profissional logado
  fastify.get("/profissionais/me/plantoes", { preHandler: [authorize("profissional")] }, async (request, reply) => {
    const usuarioId = Number(request?.user?.id);

    const profissional = await prisma.profissional.findUnique({
      where: { usuarioId },
      select: { id: true },
    });

    if (!profissional) {
      return reply.status(404).send({ error: "Profissional não encontrado" });
    }

    const plantoes = await prisma.plantao.findMany({
      where: { profissionalId: profissional.id },
      include: {
        cliente: true,
        localAtendimento: true,
        diasPlantao: {
          include: {
            presencaDiaPlantao: true,
          },
        },
      },
      orderBy: { createdAt: "asc" },
    });

    return reply.send(plantoes);
  });

  // Listar fechamentos do profissional logado
  fastify.get("/profissionais/me/fechamentos", async (request, reply) => {
    const usuarioId = Number(request?.user?.id);

    const profissional = await prisma.profissional.findUnique({
      where: { usuarioId },
      select: { id: true },
    });

    if (!profissional) {
      return reply.status(404).send({ error: "Profissional não encontrado" });
    }

    const fechamentos = await prisma.fechamento.findMany({
      where: { profissionalId: profissional.id },
      orderBy: { createdAt: "desc" },
    });

    return reply.send(fechamentos);
  });

  // Listar antecipações do profissional logado
  fastify.get("/profissionais/me/antecipacoes", { preHandler: [authorize("profissional")] }, async (request, reply) => {
    const usuarioId = Number(request?.user?.id);

    const profissional = await prisma.profissional.findUnique({
      where: { usuarioId },
      select: { id: true },
    });

    if (!profissional) {
      return reply.status(404).send({ error: "Profissional não encontrado" });
    }

    const antecipacoes = await prisma.antecipacao.findMany({
      where: { profissionalId: profissional.id },
      orderBy: { createdAt: "desc" },
    });

    return reply.send(antecipacoes);
  });

  // Criar profissional
  fastify.post<{ Body: CreateProfissionalInput }>(
    "/profissionais",
    { preHandler: [authorize("master", "admin", "coordenador")] },
    async (request, reply) => {
      // Validar e fazer parse dos dados usando o schema
      const parseResult = createProfissionalSchema.safeParse(request.body);

      if (!parseResult.success) {
        return reply.status(400).send({
          error: "Dados inválidos",
        });
      }

      const data = parseResult.data;

      // Verificar se CPF já existe
      const existingCpf = await prisma.usuario.findUnique({
        where: { cpf: data.cpf },
      });

      if (existingCpf) {
        return reply.status(400).send({ error: "CPF já cadastrado" });
      }

      // Verificar se email já existe
      const existingEmail = await prisma.usuario.findUnique({
        where: { email: data.email },
      });

      if (existingEmail) {
        return reply.status(400).send({ error: "Email já cadastrado" });
      }

      const {
        especialidadeIds,
        clienteIds,
        onboardingPendente,
        // Usuario fields
        nome,
        cpf,
        email,
        telefone,
        dataNascimento,
        genero,
        estadoCivil,
        nacionalidade,
        cep,
        logradouro,
        numero,
        complemento,
        bairro,
        cidade,
        uf,
        ativo,
        // Profissional specific fields
        ...profissionalSpecificData
      } = data;

      const seisDigitosAleatorio = Math.floor(100000 + Math.random() * 900000).toString();
      const senha = seisDigitosAleatorio;

      // Create Usuario first with clientes relationship
      const usuario = await prisma.usuario.create({
        data: {
          nome,
          cpf,
          email,
          senha: await generatePass(senha),
          telefone,
          dataNascimento: dataNascimento ? parseUTCDate(dataNascimento) : undefined,
          genero,
          estadoCivil,
          nacionalidade,
          cep,
          logradouro,
          numero,
          complemento,
          bairro,
          cidade,
          uf,
          ativo,
          ...(clienteIds &&
            clienteIds.length > 0 && {
              clientes: {
                create: clienteIds.map((clienteId) => ({
                  clienteId,
                })),
              },
            }),
          metaData: onboardingPendente ? { onboardingPendente: true } : {},
        },
      });

      // Then create Profissional linked to Usuario
      const profissional = await prisma.profissional.create({
        data: {
          usuarioId: usuario.id,
          ...profissionalSpecificData,
          ...(especialidadeIds &&
            especialidadeIds.length > 0 && {
              especialidades: {
                create: especialidadeIds.map((especialidadeId) => ({
                  especialidadeId,
                })),
              },
            }),
        },
        include: {
          usuario: {
            include: {
              clientes: {
                include: {
                  cliente: true,
                },
              },
            },
          },
          especialidades: {
            include: {
              especialidade: true,
            },
          },
        },
      });

      await utilRepository.assignRolesToUser(usuario.id, ["profissional"]);

      if (appEnv.SEND_WELCOME_EMAIL) {
        await sendWelcomeEmail(usuario.nome, usuario.email, senha);
      }

      return reply.status(201).send(profissional);
    }
  );

  // Atualizar profissional
  fastify.put<{ Params: { uuid: string }; Body: UpdateProfissionalInput }>(
    "/profissionais/:uuid",
    { preHandler: [authorize("master", "admin", "coordenador")] },
    async (request, reply) => {
      const { uuid } = request.params;

      // Validar e fazer parse dos dados usando o schema
      const parseResult = updateProfissionalSchema.safeParse(request.body);

      if (!parseResult.success) {
        return reply.status(400).send({
          error: "Dados inválidos",
          issues: parseResult.error.issues,
        });
      }

      const data = parseResult.data;

      // Verificar se profissional existe
      const existingProfissional = await prisma.profissional.findUnique({
        where: { uuid },
        include: { usuario: true },
      });

      if (!existingProfissional) {
        return reply.status(404).send({ error: "Profissional não encontrado" });
      }

      // Se está alterando CPF, verificar duplicação
      if (data.cpf && existingProfissional.usuario && data.cpf !== existingProfissional.usuario.cpf) {
        const duplicateCpf = await prisma.usuario.findUnique({
          where: { cpf: data.cpf },
        });

        if (duplicateCpf) {
          return reply.status(400).send({ error: "CPF já cadastrado para outro profissional" });
        }
      }

      // Se está alterando email, verificar duplicação
      if (data.email && existingProfissional.usuario && data.email !== existingProfissional.usuario.email) {
        const duplicateEmail = await prisma.usuario.findUnique({
          where: { email: data.email },
        });

        if (duplicateEmail) {
          return reply.status(400).send({ error: "Email já cadastrado para outro profissional" });
        }
      }

      const {
        especialidadeIds,
        clienteIds,
        onboardingPendente,
        // Usuario fields
        nome,
        cpf,
        email,
        telefone,
        dataNascimento,
        genero,
        estadoCivil,
        nacionalidade,
        cep,
        logradouro,
        numero,
        complemento,
        bairro,
        cidade,
        uf,
        ativo,
        // Profissional specific fields
        ...profissionalSpecificData
      } = data;

      // Update Usuario if it exists and has changed fields
      if (existingProfissional.usuarioId) {
        const usuarioUpdateData: any = {};
        if (nome !== undefined) usuarioUpdateData.nome = nome;
        if (cpf !== undefined) usuarioUpdateData.cpf = cpf;
        if (email !== undefined) usuarioUpdateData.email = email;
        if (telefone !== undefined) usuarioUpdateData.telefone = telefone;
        if (dataNascimento !== undefined)
          usuarioUpdateData.dataNascimento = dataNascimento ? parseUTCDate(dataNascimento) : null;
        if (genero !== undefined) usuarioUpdateData.genero = genero;
        if (estadoCivil !== undefined) usuarioUpdateData.estadoCivil = estadoCivil;
        if (nacionalidade !== undefined) usuarioUpdateData.nacionalidade = nacionalidade;
        if (cep !== undefined) usuarioUpdateData.cep = cep;
        if (logradouro !== undefined) usuarioUpdateData.logradouro = logradouro;
        if (numero !== undefined) usuarioUpdateData.numero = numero;
        if (complemento !== undefined) usuarioUpdateData.complemento = complemento;
        if (bairro !== undefined) usuarioUpdateData.bairro = bairro;
        if (cidade !== undefined) usuarioUpdateData.cidade = cidade;
        if (uf !== undefined) usuarioUpdateData.uf = uf;
        if (ativo !== undefined) usuarioUpdateData.ativo = ativo;

        if (onboardingPendente !== undefined) {
          usuarioUpdateData.metaData = { ...(usuarioUpdateData?.metaData ?? {}), onboardingPendente };
        }

        if (Object.keys(usuarioUpdateData).length > 0) {
          await prisma.usuario.update({
            where: { id: existingProfissional.usuarioId },
            data: usuarioUpdateData,
          });
        }
      }

      // Se especialidadeIds foi fornecido, gerenciar as relações
      if (especialidadeIds !== undefined) {
        // Remover todas as especialidades existentes
        await prisma.profissionalEspecialidade.deleteMany({
          where: { profissionalId: existingProfissional.id },
        });

        // Adicionar as novas especialidades
        if (especialidadeIds.length > 0) {
          await prisma.profissionalEspecialidade.createMany({
            data: especialidadeIds.map((especialidadeId) => ({
              profissionalId: existingProfissional.id,
              especialidadeId,
            })),
          });
        }
      }

      // Se clienteIds foi fornecido, gerenciar as relações
      if (clienteIds !== undefined && existingProfissional.usuarioId) {
        // Remover todos os vínculos de cliente existentes
        await prisma.usuarioCliente.deleteMany({
          where: { usuarioId: existingProfissional.usuarioId },
        });

        // Adicionar os novos vínculos de cliente
        if (clienteIds.length > 0) {
          await prisma.usuarioCliente.createMany({
            data: clienteIds.map((clienteId) => ({
              usuarioId: existingProfissional.usuarioId!,
              clienteId,
            })),
          });
        }
      }

      const profissional = await prisma.profissional.update({
        where: { uuid },
        data: {
          ...profissionalSpecificData,
        },
        include: {
          usuario: {
            include: {
              clientes: {
                include: {
                  cliente: true,
                },
              },
            },
          },
          especialidades: {
            include: {
              especialidade: true,
            },
          },
        },
      });

      return reply.send(profissional);
    }
  );

  // Deletar profissional
  fastify.delete<{ Params: { uuid: string } }>(
    "/profissionais/:uuid",
    { preHandler: [authorize("master")] },
    async (request, reply) => {
      const { uuid } = request.params;
      const clienteId = request.clienteId;

      // Verificar se profissional existe e pertence ao cliente
      const where = {
        uuid,
        usuario: {
          clientes: {
            some: {
              clienteId,
              ativo: true,
            },
          },
        },
      };

      const profissional = await prisma.profissional.findFirst({
        where,
        include: {
          _count: {
            select: {
              plantoes: true,
              fechamentos: true,
              antecipacoes: true,
            },
          },
        },
      });

      if (!profissional) {
        return reply.status(404).send({ error: "Profissional não encontrado" });
      }

      // Verificar se tem dados associados
      if (
        profissional._count.plantoes > 0 ||
        profissional._count.fechamentos > 0 ||
        profissional._count.antecipacoes > 0
      ) {
        return reply.status(400).send({
          error: "Não é possível excluir profissional com dados associados",
        });
      }

      await prisma.profissional.delete({
        where: { uuid },
      });

      await prisma.usuario.delete({
        where: { id: profissional.usuarioId },
      });

      return reply.status(204).send();
    }
  );
}
