import React from "react";
import { Input } from "@/components/ui/input";
import { Label } from "@/components/ui/label";

interface PercentageInputProps {
  value: number | null;
  onChange: (value: number | null) => void;
  label?: string;
  placeholder?: string;
  disabled?: boolean;
  className?: string;
}

export const PercentageInput: React.FC<PercentageInputProps> = ({
  value,
  onChange,
  label,
  placeholder = "0",
  disabled = false,
  className,
}) => {
  const handleChange = (e: React.ChangeEvent<HTMLInputElement>) => {
    const inputValue = e.target.value;
    const numericValue = parseFloat(inputValue);
    if (isNaN(numericValue) || numericValue < 0 || numericValue > 100) {
      onChange(null); // Invalid input
    } else {
      onChange(numericValue);
    }
  };

  const displayValue = value !== null ? value.toString() : "";

  return (
    <div className={className ? className : "space-y-2"}>
      {label && <Label>{label}</Label>}
      <div className="relative">
        <Input
          type="number"
          value={displayValue}
          onChange={handleChange}
          placeholder={placeholder}
          disabled={disabled}
          min="0"
          max="100"
          step="0.01"
        />
        <span className="absolute right-3 top-1/2 transform -translate-y-1/2 text-muted-foreground">%</span>
      </div>
    </div>
  );
};
