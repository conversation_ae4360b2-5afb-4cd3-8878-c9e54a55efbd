import { authorize } from "@/middlewares/auth.middleware";
import { prisma } from "../lib/prisma";
import {
  createLocalAtendimentoSchema,
  updateLocalAtendimentoSchema,
  localAtendimentoQuerySchema,
  type CreateLocalAtendimentoInput,
  type UpdateLocalAtendimentoInput,
  type LocalAtendimentoQuery,
} from "../schemas/local-atendimento.schema";
import type { FastifyTypedInstance } from "@/types";

export async function localAtendimentoRouter(fastify: FastifyTypedInstance) {
  // Listar locais de atendimento
  fastify.get<{ Querystring: LocalAtendimentoQuery }>("/locais-atendimento", async (request, reply) => {
    const parsedQuery = localAtendimentoQuerySchema.parse(request.query);
    const { page = 1, limit = 10, search, clienteId, clienteUuid, ativo } = parsedQuery;
    const pageNum = Number(page);
    const limitNum = Number(limit);
    const skip = (pageNum - 1) * limitNum;

    // Se temos clienteUuid, buscar o clienteId correspondente
    let resolvedClienteId = clienteId;
    if (clienteUuid && !clienteId) {
      const cliente = await prisma.cliente.findUnique({
        where: { uuid: clienteUuid },
        select: { id: true },
      });
      if (cliente) {
        resolvedClienteId = cliente.id;
      }
    }

    const where = {
      ...(ativo !== undefined && {
        ativo,
      }),
      ...(resolvedClienteId && { clienteId: resolvedClienteId }),
      ...(search && {
        OR: [{ nome: { contains: search } }, { endereco: { contains: search } }, { cidade: { contains: search } }],
      }),
    };

    const [locais, total] = await Promise.all([
      prisma.localAtendimento.findMany({
        where,
        skip,
        take: limitNum,
        include: {
          cliente: true,
          especialidades: {
            include: {
              especialidade: true,
            },
          },
          _count: {
            select: {
              plantoes: true,
            },
          },
        },
        orderBy: { createdAt: "desc" },
      }),
      prisma.localAtendimento.count({ where }),
    ]);

    return reply.send({
      data: locais,
      meta: {
        page: pageNum,
        limit: limitNum,
        total,
        totalPages: Math.ceil(total / limitNum),
      },
    });
  });

  // Buscar local por UUID
  fastify.get<{ Params: { uuid: string } }>("/locais-atendimento/:uuid", async (request, reply) => {
    const { uuid } = request.params;

    const local = await prisma.localAtendimento.findUnique({
      where: { uuid },
      include: {
        cliente: true,
        especialidades: {
          include: {
            especialidade: true,
          },
        },
        plantoes: {
          include: {
            profissional: true,
          },
          orderBy: { createdAt: "desc" },
          take: 10,
        },
      },
    });

    if (!local) {
      return reply.status(404).send({ error: "Local de atendimento não encontrado" });
    }

    return reply.send(local);
  });

  // Criar local de atendimento
  fastify.post<{ Body: CreateLocalAtendimentoInput }>("/locais-atendimento", async (request, reply) => {
    const data = request.body;

    // Resolver clienteId a partir do UUID se necessário
    let clienteId = data.clienteId;

    const cliente = await prisma.cliente.findUnique({
      where: { uuid: data.clienteId },
      select: { id: true },
    });

    if (!cliente) {
      return reply.status(400).send({ error: "Cliente não encontrado" });
    }

    // Verificar se já existe um local com o mesmo nome para o mesmo cliente
    const locaisDoCliente = await prisma.localAtendimento.findMany({
      where: {
        clienteId: cliente.id,
      },
    });

    const localComMesmoNome = locaisDoCliente.find(
      (local) => local.nome.toLowerCase() === data.nome.trim().toLowerCase()
    );

    if (localComMesmoNome) {
      return reply.status(400).send({
        error: `Já existe um local cadastrado com o nome "${data.nome}" para este cliente`,
      });
    }

    // Verificar se já existe um local com o mesmo endereço completo para o mesmo cliente
    if (data.endereco && data.cidade) {
      const localComMesmoEndereco = locaisDoCliente.find(
        (local) =>
          local.endereco?.toLowerCase() === data.endereco.trim().toLowerCase() &&
          local.cidade?.toLowerCase() === data.cidade?.trim().toLowerCase()
      );

      if (localComMesmoEndereco) {
        return reply.status(400).send({
          error: `Já existe um local cadastrado neste endereço: ${localComMesmoEndereco.nome}`,
        });
      }
    }

    const local = await prisma.localAtendimento.create({
      data: {
        clienteId: cliente.id,
        nome: data.nome.trim(),
        endereco: data.endereco?.trim() || "",
        cidade: data.cidade?.trim() || "",
        estado: data.estado?.trim() || "",
        cep: data.cep?.trim() || "",
        telefone: data.telefone?.trim() || null,
        responsavel: data.responsavel?.trim() || null,
        observacoes: data.observacoes?.trim() || null,
        latitude: data.latitude || null,
        longitude: data.longitude || null,
        ativo: data.ativo !== undefined ? data.ativo : true,
        especialidades: data.especialidadeIds?.length
          ? {
              create: data.especialidadeIds.map((especialidadeId) => ({
                especialidadeId,
              })),
            }
          : undefined,
      },
      include: {
        cliente: true,
        especialidades: {
          include: {
            especialidade: true,
          },
        },
      },
    });

    return reply.status(201).send(local);
  });

  // Atualizar local de atendimento
  fastify.put<{
    Params: { uuid: string };
    Body: UpdateLocalAtendimentoInput;
  }>("/locais-atendimento/:uuid", async (request, reply) => {
    const { uuid } = request.params;
    const data = request.body;

    // Verificar se local existe
    const existingLocal = await prisma.localAtendimento.findUnique({
      where: { uuid },
    });

    if (!existingLocal) {
      return reply.status(404).send({ error: "Local de atendimento não encontrado" });
    }

    const cliente = await prisma.cliente.findUnique({
      where: { uuid: data.clienteId },
      select: { id: true },
    });

    if (!cliente) {
      return reply.status(400).send({ error: "Cliente não encontrado" });
    }

    // Buscar outros locais do mesmo cliente para validação
    const outrosLocaisDoCliente = await prisma.localAtendimento.findMany({
      where: {
        clienteId: cliente.id,
        NOT: {
          uuid: uuid,
        },
      },
    });

    // Verificar se já existe outro local com o mesmo nome para o mesmo cliente
    if (data.nome && data.nome.trim() !== existingLocal.nome) {
      const localComMesmoNome = outrosLocaisDoCliente.find(
        (local) => local.nome.toLowerCase() === data.nome?.trim().toLowerCase()
      );

      if (localComMesmoNome) {
        return reply.status(400).send({
          error: `Já existe um local cadastrado com o nome "${data.nome}" para este cliente`,
        });
      }
    }

    // Verificar se já existe outro local com o mesmo endereço completo para o mesmo cliente
    if (data.endereco && data.cidade) {
      const localComMesmoEndereco = outrosLocaisDoCliente.find(
        (local) =>
          local.endereco?.toLowerCase() === data.endereco?.trim().toLowerCase() &&
          local.cidade?.toLowerCase() === data.cidade?.trim().toLowerCase()
      );

      if (localComMesmoEndereco) {
        return reply.status(400).send({
          error: `Já existe um local cadastrado neste endereço: ${localComMesmoEndereco.nome}`,
        });
      }
    }

    // Construir objeto de atualização com trim nos campos de texto
    const updatePayload: any = {
      nome: data.nome?.trim(),
      endereco: data.endereco?.trim(),
      cidade: data.cidade?.trim(),
      estado: data.estado?.trim(),
      cep: data.cep?.trim(),
      telefone: data.telefone?.trim(),
      responsavel: data.responsavel?.trim(),
      observacoes: data.observacoes?.trim(),
      latitude: data.latitude,
      longitude: data.longitude,
      ativo: data.ativo,
    };

    // Remover campos undefined
    Object.keys(updatePayload).forEach((key) => {
      if (updatePayload[key] === undefined) {
        delete updatePayload[key];
      }
    });

    // Adicionar relacionamento se clienteId mudou
    if (cliente.id !== existingLocal.clienteId) {
      updatePayload.cliente = {
        connect: { id: cliente.id },
      };
    }

    // Atualizar especialidades se fornecidas
    if (data.especialidadeIds !== undefined) {
      // Primeiro, remover todas as especialidades existentes
      await prisma.localAtendimentoEspecialidades.deleteMany({
        where: { localAtendimentoId: existingLocal.id },
      });

      // Depois, adicionar as novas especialidades
      if (data.especialidadeIds.length > 0) {
        await prisma.localAtendimentoEspecialidades.createMany({
          data: data.especialidadeIds.map((especialidadeId) => ({
            localAtendimentoId: existingLocal.id,
            especialidadeId,
          })),
        });
      }
    }

    const local = await prisma.localAtendimento.update({
      where: { uuid },
      data: updatePayload,
      include: {
        cliente: true,
        especialidades: {
          include: {
            especialidade: true,
          },
        },
        plantoes: true,
      },
    });

    return reply.send(local);
  });

  // Deletar local de atendimento
  fastify.delete<{ Params: { uuid: string } }>(
    "/locais-atendimento/:uuid",
    { preHandler: [authorize("master", "admin")] },
    async (request, reply) => {
      const { uuid } = request.params;

      // Verificar se local existe
      const local = await prisma.localAtendimento.findUnique({
        where: { uuid },
        include: {
          _count: {
            select: {
              plantoes: true,
            },
          },
        },
      });

      if (!local) {
        return reply.status(404).send({ error: "Local de atendimento não encontrado" });
      }

      // Verificar se tem plantões associados
      if (local._count.plantoes > 0) {
        return reply.status(400).send({
          error: "Não é possível excluir local com plantões associados",
        });
      }

      await prisma.localAtendimento.delete({
        where: { uuid },
      });

      return reply.status(204).send();
    }
  );

  // Listar locais por cliente
  fastify.get<{ Params: { clienteId: string } }>("/clientes/:clienteId/locais-atendimento", async (request, reply) => {
    const { clienteId } = request.params;

    const locais = await prisma.localAtendimento.findMany({
      where: {
        clienteId: Number(clienteId),
        ativo: true,
      },
      orderBy: { nome: "asc" },
    });

    return reply.send(locais);
  });
}
