import { useState, useEffect } from "react";
import { useQuery } from "@tanstack/react-query";
import { createFileRoute } from "@tanstack/react-router";
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from "@/components/ui/card";
import { Badge } from "@/components/ui/badge";
import { Button } from "@/components/ui/button";
import { Skeleton } from "@/components/ui/skeleton";
import { ScrollArea } from "@/components/ui/scroll-area";
import { api } from "@/lib/api";
import { createCurrentLocalDate, getMonth, getYear, formatDate, parseISO } from "@/lib/utils";
import {
  TrendingUp,
  TrendingDown,
  DollarSign,
  Clock,
  Calendar,
  MapPin,
  Bell,
  LogIn,
  LogOut,
  AlertCircle,
  CheckCircle,
  XCircle,
  Building2,
  CreditCard,
  FileText,
  FileSignature,
  Activity,
} from "lucide-react";
import { useMutation, useQueryClient } from "@tanstack/react-query";
import { toast } from "sonner";
import { cn } from "@/lib/utils";
import { CheckInOutModal } from "@/components/checkin-checkout-modal";
import { TermosPendentesModal } from "@/components/termos-pendentes-modal";

interface DashboardSummary {
  financial: {
    currentMonth: {
      total: number;
      approved: number;
      pending: number;
      count: number;
    };
    variation: {
      percentage: number;
      value: number;
    };
  };
  anticipations: {
    requested: number;
    approved: number;
    pending: number;
    count: number;
  };
  hours: {
    worked: number;
  };
  month: number;
  year: number;
}

interface TodayShift {
  date: string;
  shifts: Array<{
    id: string;
    plantaoId: string;
    cliente: string;
    local: string;
    endereco: string;
    horario: {
      entrada: string;
      saida: string;
      intervalo: string;
    };
    status: {
      trabalhado: boolean;
      checkin: string | null;
      checkout: string | null;
      presencaStatus: string;
    };
  }>;
  hasShifts: boolean;
}

interface UpcomingShifts {
  shifts: Array<{
    id: string;
    plantaoId: string;
    data: string;
    cliente: string;
    local: string;
    horario: {
      entrada: string;
      saida: string;
    };
    valorEstimado: number;
  }>;
  count: number;
}

interface Notification {
  id: string;
  type: "fechamento" | "antecipacao";
  status: string;
  message: string;
  date: string;
  value: number;
  reason: string | null;
}

interface HoursStats {
  days: {
    planned: number;
    worked: number;
    pending: number;
  };
  hours: {
    planned: number;
    worked: number;
    difference: number;
  };
  attendance: {
    rate: number;
  };
}

function ProfissionalDashboard() {
  const queryClient = useQueryClient();
  const now = createCurrentLocalDate();
  const currentMonth = getMonth(now) + 1;
  const currentYear = getYear(now);

  // Estados para o modal de check-in/checkout
  const [modalOpen, setModalOpen] = useState(false);
  const [modalType, setModalType] = useState<"checkin" | "checkout">("checkin");
  const [selectedShift, setSelectedShift] = useState<any>(null);

  // Estado para o modal de termos pendentes
  const [showTermosModal, setShowTermosModal] = useState(false);

  // Fetch dashboard summary
  const { data: summary, isLoading: summaryLoading } = useQuery<DashboardSummary>({
    queryKey: ["profissional-dashboard-summary", currentMonth, currentYear],
    queryFn: () => api.get("/profissional/dashboard/summary", { mes: currentMonth, ano: currentYear }),
  });

  // Fetch today's shifts
  const { data: todayData, isLoading: todayLoading } = useQuery<TodayShift>({
    queryKey: ["profissional-dashboard-today"],
    queryFn: () => api.get("/profissional/dashboard/today"),
    refetchInterval: 60000, // Refresh every minute
  });

  // Fetch upcoming shifts
  const { data: upcomingData, isLoading: upcomingLoading } = useQuery<UpcomingShifts>({
    queryKey: ["profissional-dashboard-upcoming"],
    queryFn: () => api.get("/profissional/dashboard/upcoming"),
  });

  // Fetch notifications
  const { data: notificationsData, isLoading: notificationsLoading } = useQuery<{
    notifications: Notification[];
    unreadCount: number;
  }>({
    queryKey: ["profissional-dashboard-notifications"],
    queryFn: () => api.get("/profissional/dashboard/notifications"),
    refetchInterval: 120000, // Refresh every 2 minutes
  });

  // Fetch hours statistics
  const { data: hoursStats, isLoading: hoursLoading } = useQuery<HoursStats>({
    queryKey: ["profissional-dashboard-hours", currentMonth, currentYear],
    queryFn: () => api.get("/profissional/dashboard/hours-stats", { mes: currentMonth, ano: currentYear }),
  });

  // Fetch termos pendentes
  const { data: termosPendentesData, isLoading: termosPendentesLoading } = useQuery({
    queryKey: ["termos-pendentes"],
    queryFn: () => api.get<{ total: number }>("/antecipacoes/pendentes-assinatura"),
    refetchInterval: 300000, // Atualizar a cada 5 minutos
  });

  // Mostrar modal automaticamente quando há termos pendentes (apenas uma vez por sessão)
  const [modalAutoShown, setModalAutoShown] = useState(false);

  useEffect(() => {
    if (!termosPendentesLoading && termosPendentesData?.total! > 0 && !modalAutoShown && !showTermosModal) {
      // Aguardar um pouco para garantir que o dashboard carregou completamente
      setTimeout(() => {
        setShowTermosModal(true);
        setModalAutoShown(true);
      }, 1500);
    }
  }, [termosPendentesData, termosPendentesLoading, modalAutoShown, showTermosModal]);

  // Check-in mutation
  const checkinMutation = useMutation({
    mutationFn: async ({ plantaoId, diaPlantaoId, ...data }: any) => {
      return await api.post(`/profissional/plantoes/${plantaoId}/checkin`, {
        diaPlantaoId,
        ...data,
      });
    },
    onSuccess: (data: any) => {
      const message = data.localizacao?.dentroDoRaio
        ? "Check-in realizado com sucesso!"
        : "Check-in realizado, mas você está fora do local de trabalho";

      if (data.localizacao?.distancia) {
        toast.success(`${message} (${Math.round(data.localizacao.distancia)}m de distância)`);
      } else {
        toast.success(message);
      }

      queryClient.invalidateQueries({ queryKey: ["profissional-dashboard-today"] });
      setModalOpen(false);
      setSelectedShift(null);
    },
    onError: (error: any) => {
      toast.error(error.response?.data?.error || "Erro ao fazer check-in");
    },
  });

  // Check-out mutation
  const checkoutMutation = useMutation({
    mutationFn: async ({ plantaoId, diaPlantaoId, ...data }: any) => {
      return await api.post(`/profissional/plantoes/${plantaoId}/checkout`, {
        diaPlantaoId,
        ...data,
      });
    },
    onSuccess: () => {
      toast.success("Check-out realizado com sucesso!");
      queryClient.invalidateQueries({ queryKey: ["profissional-dashboard-today"] });
      setModalOpen(false);
      setSelectedShift(null);
    },
    onError: (error: any) => {
      toast.error(error.response?.data?.error || "Erro ao fazer check-out");
    },
  });

  // Handler para abrir modal
  const handleOpenModal = (shift: any, type: "checkin" | "checkout") => {
    setSelectedShift(shift);
    setModalType(type);
    setModalOpen(true);
  };

  // Handler para confirmar check-in/checkout
  const handleConfirmCheckInOut = (data: any) => {
    if (!selectedShift) return;

    const mutation = modalType === "checkin" ? checkinMutation : checkoutMutation;
    mutation.mutate({
      plantaoId: String(selectedShift.plantaoId),
      diaPlantaoId: String(selectedShift.id),
      ...data,
    });
  };

  const formatCurrency = (value: number) => {
    return new Intl.NumberFormat("pt-BR", {
      style: "currency",
      currency: "BRL",
    }).format(value);
  };

  const getStatusIcon = (status: string) => {
    switch (status) {
      case "APROVADO":
      case "PAGA":
        return <CheckCircle className="h-4 w-4 text-green-500" />;
      case "REJEITADO":
      case "CANCELADA":
        return <XCircle className="h-4 w-4 text-red-500" />;
      default:
        return <AlertCircle className="h-4 w-4 text-yellow-500" />;
    }
  };

  const getStatusBadgeVariant = (status: string): "default" | "secondary" | "destructive" | "outline" => {
    switch (status) {
      case "APROVADO":
      case "PAGA":
        return "default";
      case "REJEITADO":
      case "CANCELADA":
        return "destructive";
      case "PENDENTE":
        return "secondary";
      default:
        return "outline";
    }
  };

  return (
    <div className="space-y-6">
      {/* Header */}
      <div>
        <h1 className="text-3xl font-bold">Dashboard</h1>
        <p className="text-muted-foreground">
          Bem-vindo de volta! Aqui está seu resumo de {currentMonth}/{currentYear}
        </p>
      </div>

      {/* Alert para termos pendentes */}
      {!termosPendentesLoading && termosPendentesData?.total! > 0 && (
        <Card className="border-orange-200 bg-orange-50/50">
          <CardContent className="pt-6">
            <div className="flex items-center gap-4">
              <div className="p-2 bg-orange-100 rounded-full">
                <FileSignature className="h-6 w-6 text-orange-600" />
              </div>
              <div className="flex-1">
                <h3 className="font-semibold text-orange-900">
                  {termosPendentesData?.total === 1
                    ? "Você tem 1 termo pendente de assinatura"
                    : `Você tem ${termosPendentesData?.total} termos pendentes de assinatura`}
                </h3>
                <p className="text-sm text-orange-700">
                  Clique no botão para visualizar e assinar os termos de antecipação aprovados.
                </p>
              </div>
              <Button onClick={() => setShowTermosModal(true)} className="bg-orange-600 hover:bg-orange-700">
                <FileSignature className="mr-2 h-4 w-4" />
                Ver Termos
              </Button>
            </div>
          </CardContent>
        </Card>
      )}

      {/* Financial Summary Cards */}
      <div className="grid gap-4 md:grid-cols-2 lg:grid-cols-4">
        {/* Earnings Card */}
        <Card>
          <CardHeader className="flex flex-row items-center justify-between space-y-0 pb-2">
            <CardTitle className="text-sm font-medium">Ganhos Aprovados</CardTitle>
            <DollarSign className="h-4 w-4 text-muted-foreground" />
          </CardHeader>
          <CardContent>
            {summaryLoading ? (
              <Skeleton className="h-8 w-24" />
            ) : (
              <>
                <div className="text-2xl font-bold">
                  {formatCurrency(summary?.financial.currentMonth.approved || 0)}
                </div>
                <div className="flex items-center text-xs text-muted-foreground">
                  {summary?.financial.variation.percentage !== undefined && (
                    <>
                      {summary.financial.variation.percentage > 0 ? (
                        <TrendingUp className="mr-1 h-3 w-3 text-green-500" />
                      ) : (
                        <TrendingDown className="mr-1 h-3 w-3 text-red-500" />
                      )}
                      <span
                        className={cn(summary.financial.variation.percentage > 0 ? "text-green-500" : "text-red-500")}
                      >
                        {Math.abs(summary.financial.variation.percentage).toFixed(1)}%
                      </span>
                      <span className="ml-1">vs mês anterior</span>
                    </>
                  )}
                </div>
              </>
            )}
          </CardContent>
        </Card>

        {/* Pending Closures Card */}
        <Card>
          <CardHeader className="flex flex-row items-center justify-between space-y-0 pb-2">
            <CardTitle className="text-sm font-medium">Fechamentos Pendentes</CardTitle>
            <FileText className="h-4 w-4 text-muted-foreground" />
          </CardHeader>
          <CardContent>
            {summaryLoading ? (
              <Skeleton className="h-8 w-24" />
            ) : (
              <>
                <div className="text-2xl font-bold">{summary?.financial.currentMonth.pending || 0}</div>
                <p className="text-xs text-muted-foreground">Aguardando aprovação</p>
              </>
            )}
          </CardContent>
        </Card>

        {/* Anticipations Card */}
        <Card>
          <CardHeader className="flex flex-row items-center justify-between space-y-0 pb-2">
            <CardTitle className="text-sm font-medium">Antecipações</CardTitle>
            <CreditCard className="h-4 w-4 text-muted-foreground" />
          </CardHeader>
          <CardContent>
            {summaryLoading ? (
              <Skeleton className="h-8 w-24" />
            ) : (
              <>
                <div className="text-2xl font-bold">{formatCurrency(summary?.anticipations.approved || 0)}</div>
                <p className="text-xs text-muted-foreground">{summary?.anticipations.pending || 0} pendente(s)</p>
              </>
            )}
          </CardContent>
        </Card>

        {/* Terms Pending Card */}
        <Card>
          <CardHeader className="flex flex-row items-center justify-between space-y-0 pb-2">
            <CardTitle className="text-sm font-medium">Termos Pendentes</CardTitle>
            <FileSignature className="h-4 w-4 text-muted-foreground" />
          </CardHeader>
          <CardContent>
            {termosPendentesLoading ? (
              <Skeleton className="h-8 w-24" />
            ) : (
              <>
                <div className="text-2xl font-bold">{termosPendentesData?.total || 0}</div>
                <div className="flex items-center gap-2">
                  <p className="text-xs text-muted-foreground">
                    {termosPendentesData?.total === 1 ? "Aguardando assinatura" : "Aguardando assinaturas"}
                  </p>
                  {termosPendentesData?.total && termosPendentesData?.total > 0 && (
                    <Button size="sm" variant="outline" onClick={() => setShowTermosModal(true)} className="ml-auto">
                      Ver
                    </Button>
                  )}
                </div>
              </>
            )}
          </CardContent>
        </Card>

        {/* Hours Worked Card */}
        <Card>
          <CardHeader className="flex flex-row items-center justify-between space-y-0 pb-2">
            <CardTitle className="text-sm font-medium">Horas Trabalhadas</CardTitle>
            <Clock className="h-4 w-4 text-muted-foreground" />
          </CardHeader>
          <CardContent>
            {hoursLoading ? (
              <Skeleton className="h-8 w-24" />
            ) : (
              <>
                <div className="text-2xl font-bold">{hoursStats?.hours.worked.toFixed(1) || 0}h</div>
                <div className="flex items-center text-xs text-muted-foreground">
                  <Activity className="mr-1 h-3 w-3" />
                  <span>Taxa de presença: {hoursStats?.attendance.rate.toFixed(1) || 0}%</span>
                </div>
              </>
            )}
          </CardContent>
        </Card>
      </div>

      {/* Main Content Grid */}
      <div className="grid gap-4 md:grid-cols-2 lg:grid-cols-7">
        {/* Today's Shift */}
        <Card className="md:col-span-1 lg:col-span-4">
          <CardHeader>
            <CardTitle className="flex items-center gap-2">
              <Calendar className="h-5 w-5" />
              Plantão de Hoje
            </CardTitle>
            <CardDescription>
              {createCurrentLocalDate().toLocaleDateString("pt-BR", { weekday: "long", day: "numeric", month: "long" })}
            </CardDescription>
          </CardHeader>
          <CardContent>
            {todayLoading ? (
              <div className="space-y-3">
                <Skeleton className="h-24 w-full" />
              </div>
            ) : todayData?.hasShifts ? (
              <div className="space-y-4">
                {todayData.shifts.map((shift) => (
                  <div key={shift.id} className="rounded-lg border p-4 space-y-3">
                    <div className="flex justify-between items-start">
                      <div className="space-y-1">
                        <div className="flex items-center gap-2">
                          <Building2 className="h-4 w-4 text-muted-foreground" />
                          <span className="font-semibold">{shift.cliente}</span>
                        </div>
                        <div className="flex items-center gap-2 text-sm text-muted-foreground">
                          <MapPin className="h-3 w-3" />
                          <span>{shift.local}</span>
                        </div>
                        <p className="text-xs text-muted-foreground">{shift.endereco}</p>
                      </div>
                      <Badge variant={shift.status.trabalhado ? "default" : "secondary"}>
                        {shift.status.trabalhado ? "Concluído" : "Em Aberto"}
                      </Badge>
                    </div>

                    <div className="flex items-center gap-4 text-sm">
                      <div className="flex items-center gap-1">
                        <Clock className="h-3 w-3" />
                        <span>
                          {shift.horario.entrada} - {shift.horario.saida}
                        </span>
                      </div>
                      {shift.horario.intervalo && (
                        <span className="text-muted-foreground">Intervalo: {shift.horario.intervalo}h</span>
                      )}
                    </div>

                    {shift.status.checkin && (
                      <div className="flex items-center gap-4 text-sm">
                        <div className="flex items-center gap-1 text-green-600">
                          <LogIn className="h-3 w-3" />
                          <span>Check-in: {parseISO(shift.status.checkin).toLocaleTimeString("pt-BR")}</span>
                        </div>
                        {shift.status.checkout && (
                          <div className="flex items-center gap-1 text-red-600">
                            <LogOut className="h-3 w-3" />
                            <span>Check-out: {parseISO(shift.status.checkout).toLocaleTimeString("pt-BR")}</span>
                          </div>
                        )}
                      </div>
                    )}

                    {!shift.status.checkin ? (
                      <Button
                        onClick={() => handleOpenModal(shift, "checkin")}
                        className="w-full"
                        disabled={checkinMutation.isPending}
                      >
                        <LogIn className="h-4 w-4 mr-2" />
                        Fazer Check-in
                      </Button>
                    ) : !shift.status.checkout ? (
                      <Button
                        onClick={() => handleOpenModal(shift, "checkout")}
                        variant="destructive"
                        className="w-full"
                        disabled={checkoutMutation.isPending}
                      >
                        <LogOut className="h-4 w-4 mr-2" />
                        Fazer Check-out
                      </Button>
                    ) : (
                      <div className="text-center text-sm text-muted-foreground py-2">✓ Plantão concluído</div>
                    )}
                  </div>
                ))}
              </div>
            ) : (
              <div className="text-center py-8">
                <Calendar className="h-12 w-12 text-muted-foreground mx-auto mb-3" />
                <p className="text-muted-foreground">Nenhum plantão agendado para hoje</p>
              </div>
            )}
          </CardContent>
        </Card>

        {/* Notifications & Upcoming Shifts */}
        <div className="md:col-span-1 lg:col-span-3 space-y-4">
          {/* Notifications */}
          <Card>
            <CardHeader>
              <CardTitle className="flex items-center justify-between">
                <span className="flex items-center gap-2">
                  <Bell className="h-5 w-5" />
                  Notificações Recentes
                </span>
                {notificationsData?.unreadCount ? (
                  <Badge variant="destructive">{notificationsData.unreadCount}</Badge>
                ) : null}
              </CardTitle>
            </CardHeader>
            <CardContent>
              <ScrollArea className="h-[250px]">
                {notificationsLoading ? (
                  <div className="space-y-3">
                    <Skeleton className="h-12 w-full" />
                    <Skeleton className="h-12 w-full" />
                  </div>
                ) : notificationsData?.notifications.length ? (
                  <div className="space-y-3">
                    {notificationsData.notifications.map((notification) => (
                      <div key={notification.id} className="flex items-start gap-3 pb-3 border-b last:border-0">
                        <div className="mt-1">{getStatusIcon(notification.status)}</div>
                        <div className="flex-1 space-y-1">
                          <p className="text-sm font-medium">{notification.message}</p>
                          <div className="flex items-center gap-2 text-xs text-muted-foreground">
                            <span>{formatCurrency(notification.value)}</span>
                            <span>•</span>
                            <span>{parseISO(notification.date).toLocaleDateString("pt-BR")}</span>
                          </div>
                          {notification.reason && (
                            <p className="text-xs text-red-500 mt-1">Motivo: {notification.reason}</p>
                          )}
                        </div>
                        <Badge variant={getStatusBadgeVariant(notification.status)}>{notification.status}</Badge>
                      </div>
                    ))}
                  </div>
                ) : (
                  <div className="text-center py-4">
                    <Bell className="h-8 w-8 text-muted-foreground mx-auto mb-2" />
                    <p className="text-sm text-muted-foreground">Sem notificações no momento</p>
                  </div>
                )}
              </ScrollArea>
            </CardContent>
          </Card>

          {/* Upcoming Shifts */}
          <Card>
            <CardHeader>
              <CardTitle className="flex items-center gap-2">
                <Calendar className="h-5 w-5" />
                Próximos Plantões
              </CardTitle>
              <CardDescription>Próximos 7 dias</CardDescription>
            </CardHeader>
            <CardContent>
              <ScrollArea className="h-[200px]">
                {upcomingLoading ? (
                  <div className="space-y-3">
                    <Skeleton className="h-12 w-full" />
                    <Skeleton className="h-12 w-full" />
                  </div>
                ) : upcomingData?.shifts.length ? (
                  <div className="space-y-3">
                    {upcomingData.shifts.map((shift) => (
                      <div key={shift.id} className="flex items-center justify-between pb-3 border-b last:border-0">
                        <div className="space-y-1">
                          <p className="text-sm font-medium">{shift.cliente}</p>
                          <div className="flex items-center gap-2 text-xs text-muted-foreground">
                            <Calendar className="h-3 w-3" />
                            <span>
                              {parseISO(shift.data).toLocaleDateString("pt-BR", {
                                weekday: "short",
                                day: "numeric",
                                month: "short",
                              })}
                            </span>
                            <span>•</span>
                            <Clock className="h-3 w-3" />
                            <span>
                              {shift.horario.entrada} - {shift.horario.saida}
                            </span>
                          </div>
                          <p className="text-xs text-muted-foreground">{shift.local}</p>
                        </div>
                        {shift.valorEstimado > 0 && (
                          <span className="text-sm font-medium">{formatCurrency(shift.valorEstimado)}</span>
                        )}
                      </div>
                    ))}
                  </div>
                ) : (
                  <div className="text-center py-4">
                    <Calendar className="h-8 w-8 text-muted-foreground mx-auto mb-2" />
                    <p className="text-sm text-muted-foreground">Sem plantões agendados</p>
                  </div>
                )}
              </ScrollArea>
            </CardContent>
          </Card>
        </div>
      </div>

      {/* Hours Statistics */}
      <Card>
        <CardHeader>
          <CardTitle className="flex items-center gap-2">
            <Activity className="h-5 w-5" />
            Estatísticas do Mês
          </CardTitle>
          <CardDescription>Resumo de horas e dias trabalhados</CardDescription>
        </CardHeader>
        <CardContent>
          {hoursLoading ? (
            <div className="grid gap-4 md:grid-cols-3">
              <Skeleton className="h-20 w-full" />
              <Skeleton className="h-20 w-full" />
              <Skeleton className="h-20 w-full" />
            </div>
          ) : (
            <div className="grid gap-4 md:grid-cols-3">
              <div className="space-y-2">
                <p className="text-sm font-medium text-muted-foreground">Dias</p>
                <div className="flex items-baseline gap-2">
                  <span className="text-2xl font-bold">{hoursStats?.days.worked || 0}</span>
                  <span className="text-sm text-muted-foreground">/ {hoursStats?.days.planned || 0} planejados</span>
                </div>
                {hoursStats?.days.pending ? (
                  <p className="text-xs text-yellow-600">{hoursStats.days.pending} dia(s) pendente(s)</p>
                ) : null}
              </div>

              <div className="space-y-2">
                <p className="text-sm font-medium text-muted-foreground">Horas</p>
                <div className="flex items-baseline gap-2">
                  <span className="text-2xl font-bold">{hoursStats?.hours.worked.toFixed(1) || 0}h</span>
                  <span className="text-sm text-muted-foreground">
                    / {hoursStats?.hours.planned.toFixed(1) || 0}h planejadas
                  </span>
                </div>
                {hoursStats?.hours.difference !== undefined && (
                  <p className={cn("text-xs", hoursStats.hours.difference > 0 ? "text-green-600" : "text-red-600")}>
                    {hoursStats.hours.difference > 0 ? "+" : ""}
                    {hoursStats.hours.difference.toFixed(1)}h de diferença
                  </p>
                )}
              </div>

              <div className="space-y-2">
                <p className="text-sm font-medium text-muted-foreground">Taxa de Presença</p>
                <div className="flex items-baseline gap-2">
                  <span className="text-2xl font-bold">{hoursStats?.attendance.rate.toFixed(1) || 0}%</span>
                </div>
                <div className="w-full bg-secondary rounded-full h-2">
                  <div
                    className="bg-primary h-2 rounded-full transition-all"
                    style={{ width: `${hoursStats?.attendance.rate || 0}%` }}
                  />
                </div>
              </div>
            </div>
          )}
        </CardContent>
      </Card>

      {/* Modal de Check-in/Check-out */}
      {selectedShift && (
        <CheckInOutModal
          isOpen={modalOpen}
          onClose={() => {
            setModalOpen(false);
            setSelectedShift(null);
          }}
          onConfirm={handleConfirmCheckInOut}
          type={modalType}
          shiftInfo={{
            cliente: selectedShift.cliente,
            local: selectedShift.local,
            endereco: selectedShift.endereco,
            horario: selectedShift.horario,
          }}
          isLoading={checkinMutation.isPending || checkoutMutation.isPending}
        />
      )}

      {/* Modal de Termos Pendentes */}
      <TermosPendentesModal open={showTermosModal} onClose={() => setShowTermosModal(false)} />
    </div>
  );
}

export const Route = createFileRoute("/profissional/dashboard")({
  component: ProfissionalDashboard,
});
