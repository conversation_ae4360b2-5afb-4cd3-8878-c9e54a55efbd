import { useParams } from "@tanstack/react-router";
import { createFileRoute } from "@tanstack/react-router";
import { requireAdminRole } from "@/lib/route-guards";
import { LocalAtendimentoForm } from "@/pages/locais-atendimento/local-atendimento-form";

function EditarLocalCliente() {
  const { clienteId, localId } = useParams({ from: "/cadastros/clientes/$clienteId/locais/$localId/editar" });

  return <LocalAtendimentoForm clienteId={clienteId} localId={localId} />;
}

export const Route = createFileRoute("/cadastros/clientes/$clienteId/locais/$localId/editar")({
  component: EditarLocalCliente,
  beforeLoad: async () => {
    await requireAdminRole();
  },
});
