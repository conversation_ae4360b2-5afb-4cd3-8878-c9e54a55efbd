import { z } from "zod";

export const createEspecialidadeSchema = z.object({
  nome: z.string().min(1, "Nome é obrigatório"),
  descricao: z.string().optional().nullable(),
});

export const updateEspecialidadeSchema = createEspecialidadeSchema.partial();

export const especialidadeQuerySchema = z.object({
  page: z.coerce.number().min(1).optional().default(1),
  limit: z.coerce.number().min(1).max(100).optional().default(50),
  search: z.string().optional(),
});

export type CreateEspecialidadeInput = z.infer<typeof createEspecialidadeSchema>;
export type UpdateEspecialidadeInput = z.infer<typeof updateEspecialidadeSchema>;
export type EspecialidadeQuery = z.infer<typeof especialidadeQuerySchema>;
