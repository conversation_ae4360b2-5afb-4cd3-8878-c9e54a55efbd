export class ApiEmailService {
  private url: string;
  private apiKey: string;

  constructor(url: string, apiKey: string) {
    this.url = url;
    this.apiKey = apiKey;
  }

  async sendEmail({ to, subject, htmlBody }: { to: string | string[]; subject: string; htmlBody?: string }) {
    const recipients = Array.isArray(to) ? to : [to];

    try {
      const response = await fetch(this.url, {
        method: "POST",
        headers: {
          "Content-Type": "application/json",
          "x-api-key": this.apiKey,
        },
        body: JSON.stringify({
          action: "email",
          to: recipients,
          subject,
          body: htmlBody,
        }),
      });

      if (!response.ok) {
        const errorData = await response.json();
        console.error("Error sending email:", errorData);
        throw new Error(errorData.message || "Unknown error");
      }
      const data = await response.json();
      return data;
    } catch (error) {
      console.error("Error sending email:", error);
      throw error;
    }
  }
}
