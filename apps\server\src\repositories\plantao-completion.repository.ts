import { PrismaClient, type <PERSON>ao, Prisma } from "@prisma/client";

export class PlantaoCompletionRepository {
  constructor(private prisma: PrismaClient) {}

  /**
   * Find all completed plantoes (concluidoEm IS NOT NULL)
   */
  async findCompletedPlantoes(filters?: {
    profissionalId?: number;
    clienteId?: number;
    mes?: number;
    ano?: number;
  }): Promise<Plantao[]> {
    const where: Prisma.PlantaoWhereInput = {
      concluidoEm: { not: null },
      ...filters,
    };

    return this.prisma.plantao.findMany({
      where,
      include: {
        cliente: true,
        localAtendimento: true,
        profissional: {
          include: {
            usuario: true,
          },
        },
        diasPlantao: true,
      },
      orderBy: {
        concluidoEm: "desc",
      },
    });
  }

  /**
   * Find all ongoing plantoes (concluidoEm IS NULL)
   */
  async findOngoingPlantoes(filters?: {
    profissionalId?: number;
    clienteId?: number;
    mes?: number;
    ano?: number;
    ativo?: boolean;
  }): Promise<Plantao[]> {
    const where: Prisma.PlantaoWhereInput = {
      concluidoEm: null,
      ...filters,
    };

    return this.prisma.plantao.findMany({
      where,
      include: {
        cliente: true,
        localAtendimento: true,
        profissional: {
          include: {
            usuario: true,
          },
        },
        diasPlantao: true,
      },
      orderBy: [{ dataInicial: "desc" }, { createdAt: "desc" }],
    });
  }

  /**
   * Find plantoes completed within a date range
   */
  async findPlantoesCompletedInRange(
    startDate: Date,
    endDate: Date,
    filters?: {
      profissionalId?: number;
      clienteId?: number;
    }
  ): Promise<Plantao[]> {
    const where: Prisma.PlantaoWhereInput = {
      concluidoEm: {
        gte: startDate,
        lte: endDate,
      },
      ...filters,
    };

    return this.prisma.plantao.findMany({
      where,
      include: {
        cliente: true,
        localAtendimento: true,
        profissional: {
          include: {
            usuario: true,
          },
        },
        diasPlantao: true,
      },
      orderBy: {
        concluidoEm: "desc",
      },
    });
  }

  /**
   * Mark a plantao as completed
   */
  async markAsCompleted(plantaoId: number, completionDate: Date = new Date()): Promise<Plantao> {
    return this.prisma.plantao.update({
      where: { id: plantaoId },
      data: { concluidoEm: completionDate },
      include: {
        cliente: true,
        localAtendimento: true,
        profissional: {
          include: {
            usuario: true,
          },
        },
        diasPlantao: true,
      },
    });
  }

  /**
   * Mark a plantao as ongoing (remove completion date)
   */
  async markAsOngoing(plantaoId: number): Promise<Plantao> {
    return this.prisma.plantao.update({
      where: { id: plantaoId },
      data: { concluidoEm: null },
      include: {
        cliente: true,
        localAtendimento: true,
        profissional: {
          include: {
            usuario: true,
          },
        },
        diasPlantao: true,
      },
    });
  }

  /**
   * Get completion statistics for a professional
   */
  async getCompletionStats(
    profissionalId: number,
    year?: number
  ): Promise<{
    total: number;
    completed: number;
    ongoing: number;
    completionRate: number;
  }> {
    const where: Prisma.PlantaoWhereInput = {
      profissionalId,
      ...(year && { ano: year }),
    };

    const [total, completed] = await Promise.all([
      this.prisma.plantao.count({ where }),
      this.prisma.plantao.count({
        where: {
          ...where,
          concluidoEm: { not: null },
        },
      }),
    ]);

    const ongoing = total - completed;
    const completionRate = total > 0 ? (completed / total) * 100 : 0;

    return {
      total,
      completed,
      ongoing,
      completionRate: Math.round(completionRate * 100) / 100,
    };
  }

  /**
   * Find plantoes that should be marked as completed based on business rules
   * (e.g., when all days are worked and fechamento is approved)
   */
  async findPlantoesReadyForCompletion(): Promise<Plantao[]> {
    const plantoes = await this.prisma.plantao.findMany({
      where: {
        concluidoEm: null,
        fechamentos: {
          some: {
            status: "APROVADO",
          },
        },
      },
      include: {
        cliente: true,
        localAtendimento: true,
        profissional: {
          include: {
            usuario: true,
          },
        },
        diasPlantao: {
          include: {
            presencaDiaPlantao: true,
          },
        },
        fechamentos: {
          where: {
            status: "APROVADO",
          },
        },
      },
    });

    // Additional business logic to verify if plantao is truly ready for completion
    return plantoes.filter((plantao) => {
      // Check if the current date is past the dataFinal
      if (plantao.dataFinal && new Date() > plantao.dataFinal) {
        return true;
      }

      // Check if all scheduled days have approved presence records
      const allDaysWorked = plantao.diasPlantao.every(
        (dia) =>
          dia.presencaDiaPlantao &&
          dia.presencaDiaPlantao.length > 0 &&
          dia.presencaDiaPlantao.some((p: any) => p.status === "APROVADO")
      );
      if (allDaysWorked && plantao.diasPlantao.length > 0) {
        return true;
      }

      return false;
    });
  }

  /**
   * Bulk update completion status for multiple plantoes
   */
  async bulkUpdateCompletionStatus(plantaoIds: number[], concluidoEm: Date | null): Promise<Prisma.BatchPayload> {
    return this.prisma.plantao.updateMany({
      where: {
        id: { in: plantaoIds },
      },
      data: {
        concluidoEm,
      },
    });
  }

  /**
   * Get monthly completion summary
   */
  async getMonthlyCompletionSummary(
    mes: number,
    ano: number,
    clienteId?: number
  ): Promise<{
    mes: number;
    ano: number;
    totalPlantoes: number;
    plantoesCompletos: number;
    plantoesEmAndamento: number;
    valorTotalCompleto: number;
    valorTotalEmAndamento: number;
  }> {
    // Buscar plantões ativos no mês/ano
    const inicioMes = new Date(ano, mes - 1, 1);
    const fimMes = new Date(ano, mes, 0, 23, 59, 59, 999);

    const where: Prisma.PlantaoWhereInput = {
      dataInicial: { lte: fimMes },
      OR: [{ dataFinal: { gte: inicioMes } }, { dataFinal: null }],
      ...(clienteId && { clienteId }),
    };

    const plantoes = await this.prisma.plantao.findMany({
      where,
      select: {
        concluidoEm: true,
        valorBase: true,
        tipoValor: true,
      },
    });

    const plantoesCompletos = plantoes.filter((p) => p.concluidoEm !== null);
    const plantoesEmAndamento = plantoes.filter((p) => p.concluidoEm === null);

    return {
      mes,
      ano,
      totalPlantoes: plantoes.length,
      plantoesCompletos: plantoesCompletos.length,
      plantoesEmAndamento: plantoesEmAndamento.length,
      valorTotalCompleto: plantoesCompletos.reduce((sum, p) => sum + (p.valorBase || 0), 0),
      valorTotalEmAndamento: plantoesEmAndamento.reduce((sum, p) => sum + (p.valorBase || 0), 0),
    };
  }
}
