import { Card, CardContent, CardDescription, Card<PERSON><PERSON>er, CardTitle } from "@/components/ui/card";
import { Input } from "@/components/ui/input";
import { Label } from "@/components/ui/label";
import { Button } from "@/components/ui/button";

interface FilterCardProps {
  filters: {
    dataInicial: string;
    dataFinal: string;
    search: string;
  };
  onFiltersChange: (filters: { dataInicial: string; dataFinal: string; search: string }) => void;
}

export function FilterCard({ filters, onFiltersChange }: FilterCardProps) {
  return (
    <Card>
      <CardHeader>
        <CardTitle>Filtros</CardTitle>
        <CardDescription>Filtre os plantões por período ou busca</CardDescription>
      </CardHeader>
      <CardContent className="space-y-4">
        <div className="grid grid-cols-1 md:grid-cols-3 gap-4">
          <div>
            <Label htmlFor="dataInicial">Data Inicial</Label>
            <Input
              id="dataInicial"
              type="date"
              value={filters.dataInicial}
              onChange={(e) => onFiltersChange({ ...filters, dataInicial: e.target.value })}
            />
          </div>
          <div>
            <Label htmlFor="dataFinal">Data Final</Label>
            <Input
              id="dataFinal"
              type="date"
              value={filters.dataFinal}
              onChange={(e) => onFiltersChange({ ...filters, dataFinal: e.target.value })}
            />
          </div>
          <div>
            <Label htmlFor="search">Buscar</Label>
            <Input
              id="search"
              placeholder="Buscar por profissional, cliente, local..."
              value={filters.search}
              onChange={(e) => onFiltersChange({ ...filters, search: e.target.value })}
            />
          </div>
        </div>
        <div className="flex gap-2">
          <Button
            type="button"
            variant="outline"
            size="sm"
            onClick={() => onFiltersChange({ dataInicial: "", dataFinal: "", search: "" })}
          >
            Limpar Filtros
          </Button>
        </div>
      </CardContent>
    </Card>
  );
}
