
/* !!! This is code generated by Prisma. Do not edit directly. !!! */
/* eslint-disable */
// @ts-nocheck 
/*
 * This file exports the `Antecipacao` model and its related types.
 *
 * 🟢 You can import this file directly.
 */
import * as runtime from "@prisma/client/runtime/library"
import type * as $Enums from "../enums.ts"
import type * as Prisma from "../internal/prismaNamespace.ts"

/**
 * Model Antecipacao
 * 
 */
export type AntecipacaoModel = runtime.Types.Result.DefaultSelection<Prisma.$AntecipacaoPayload>

export type AggregateAntecipacao = {
  _count: AntecipacaoCountAggregateOutputType | null
  _avg: AntecipacaoAvgAggregateOutputType | null
  _sum: AntecipacaoSumAggregateOutputType | null
  _min: AntecipacaoMinAggregateOutputType | null
  _max: AntecipacaoMaxAggregateOutputType | null
}

export type AntecipacaoAvgAggregateOutputType = {
  id: number | null
  plantaoId: number | null
  profissionalId: number | null
  valorSolicitado: number | null
  valorAprovado: number | null
  percentual: number | null
  taxaAntecipacao: number | null
}

export type AntecipacaoSumAggregateOutputType = {
  id: number | null
  plantaoId: number | null
  profissionalId: number | null
  valorSolicitado: number | null
  valorAprovado: number | null
  percentual: number | null
  taxaAntecipacao: number | null
}

export type AntecipacaoMinAggregateOutputType = {
  id: number | null
  uuid: string | null
  plantaoId: number | null
  profissionalId: number | null
  valorSolicitado: number | null
  valorAprovado: number | null
  percentual: number | null
  taxaAntecipacao: number | null
  dataPagamentoPrevista: Date | null
  status: string | null
  numeroCCB: string | null
  dataCCB: Date | null
  arquivoCCB: string | null
  termoAssinado: boolean | null
  dataAssinaturaTermo: Date | null
  hashAssinatura: string | null
  ipAssinatura: string | null
  observacoes: string | null
  createdAt: Date | null
  updatedAt: Date | null
  deletedAt: Date | null
}

export type AntecipacaoMaxAggregateOutputType = {
  id: number | null
  uuid: string | null
  plantaoId: number | null
  profissionalId: number | null
  valorSolicitado: number | null
  valorAprovado: number | null
  percentual: number | null
  taxaAntecipacao: number | null
  dataPagamentoPrevista: Date | null
  status: string | null
  numeroCCB: string | null
  dataCCB: Date | null
  arquivoCCB: string | null
  termoAssinado: boolean | null
  dataAssinaturaTermo: Date | null
  hashAssinatura: string | null
  ipAssinatura: string | null
  observacoes: string | null
  createdAt: Date | null
  updatedAt: Date | null
  deletedAt: Date | null
}

export type AntecipacaoCountAggregateOutputType = {
  id: number
  uuid: number
  plantaoId: number
  profissionalId: number
  valorSolicitado: number
  valorAprovado: number
  percentual: number
  taxaAntecipacao: number
  dataPagamentoPrevista: number
  status: number
  numeroCCB: number
  dataCCB: number
  arquivoCCB: number
  termoAssinado: number
  dataAssinaturaTermo: number
  hashAssinatura: number
  ipAssinatura: number
  observacoes: number
  metaData: number
  createdAt: number
  updatedAt: number
  deletedAt: number
  _all: number
}


export type AntecipacaoAvgAggregateInputType = {
  id?: true
  plantaoId?: true
  profissionalId?: true
  valorSolicitado?: true
  valorAprovado?: true
  percentual?: true
  taxaAntecipacao?: true
}

export type AntecipacaoSumAggregateInputType = {
  id?: true
  plantaoId?: true
  profissionalId?: true
  valorSolicitado?: true
  valorAprovado?: true
  percentual?: true
  taxaAntecipacao?: true
}

export type AntecipacaoMinAggregateInputType = {
  id?: true
  uuid?: true
  plantaoId?: true
  profissionalId?: true
  valorSolicitado?: true
  valorAprovado?: true
  percentual?: true
  taxaAntecipacao?: true
  dataPagamentoPrevista?: true
  status?: true
  numeroCCB?: true
  dataCCB?: true
  arquivoCCB?: true
  termoAssinado?: true
  dataAssinaturaTermo?: true
  hashAssinatura?: true
  ipAssinatura?: true
  observacoes?: true
  createdAt?: true
  updatedAt?: true
  deletedAt?: true
}

export type AntecipacaoMaxAggregateInputType = {
  id?: true
  uuid?: true
  plantaoId?: true
  profissionalId?: true
  valorSolicitado?: true
  valorAprovado?: true
  percentual?: true
  taxaAntecipacao?: true
  dataPagamentoPrevista?: true
  status?: true
  numeroCCB?: true
  dataCCB?: true
  arquivoCCB?: true
  termoAssinado?: true
  dataAssinaturaTermo?: true
  hashAssinatura?: true
  ipAssinatura?: true
  observacoes?: true
  createdAt?: true
  updatedAt?: true
  deletedAt?: true
}

export type AntecipacaoCountAggregateInputType = {
  id?: true
  uuid?: true
  plantaoId?: true
  profissionalId?: true
  valorSolicitado?: true
  valorAprovado?: true
  percentual?: true
  taxaAntecipacao?: true
  dataPagamentoPrevista?: true
  status?: true
  numeroCCB?: true
  dataCCB?: true
  arquivoCCB?: true
  termoAssinado?: true
  dataAssinaturaTermo?: true
  hashAssinatura?: true
  ipAssinatura?: true
  observacoes?: true
  metaData?: true
  createdAt?: true
  updatedAt?: true
  deletedAt?: true
  _all?: true
}

export type AntecipacaoAggregateArgs<ExtArgs extends runtime.Types.Extensions.InternalArgs = runtime.Types.Extensions.DefaultArgs> = {
  /**
   * Filter which Antecipacao to aggregate.
   */
  where?: Prisma.AntecipacaoWhereInput
  /**
   * {@link https://www.prisma.io/docs/concepts/components/prisma-client/sorting Sorting Docs}
   * 
   * Determine the order of Antecipacaos to fetch.
   */
  orderBy?: Prisma.AntecipacaoOrderByWithRelationInput | Prisma.AntecipacaoOrderByWithRelationInput[]
  /**
   * {@link https://www.prisma.io/docs/concepts/components/prisma-client/pagination#cursor-based-pagination Cursor Docs}
   * 
   * Sets the start position
   */
  cursor?: Prisma.AntecipacaoWhereUniqueInput
  /**
   * {@link https://www.prisma.io/docs/concepts/components/prisma-client/pagination Pagination Docs}
   * 
   * Take `±n` Antecipacaos from the position of the cursor.
   */
  take?: number
  /**
   * {@link https://www.prisma.io/docs/concepts/components/prisma-client/pagination Pagination Docs}
   * 
   * Skip the first `n` Antecipacaos.
   */
  skip?: number
  /**
   * {@link https://www.prisma.io/docs/concepts/components/prisma-client/aggregations Aggregation Docs}
   * 
   * Count returned Antecipacaos
  **/
  _count?: true | AntecipacaoCountAggregateInputType
  /**
   * {@link https://www.prisma.io/docs/concepts/components/prisma-client/aggregations Aggregation Docs}
   * 
   * Select which fields to average
  **/
  _avg?: AntecipacaoAvgAggregateInputType
  /**
   * {@link https://www.prisma.io/docs/concepts/components/prisma-client/aggregations Aggregation Docs}
   * 
   * Select which fields to sum
  **/
  _sum?: AntecipacaoSumAggregateInputType
  /**
   * {@link https://www.prisma.io/docs/concepts/components/prisma-client/aggregations Aggregation Docs}
   * 
   * Select which fields to find the minimum value
  **/
  _min?: AntecipacaoMinAggregateInputType
  /**
   * {@link https://www.prisma.io/docs/concepts/components/prisma-client/aggregations Aggregation Docs}
   * 
   * Select which fields to find the maximum value
  **/
  _max?: AntecipacaoMaxAggregateInputType
}

export type GetAntecipacaoAggregateType<T extends AntecipacaoAggregateArgs> = {
      [P in keyof T & keyof AggregateAntecipacao]: P extends '_count' | 'count'
    ? T[P] extends true
      ? number
      : Prisma.GetScalarType<T[P], AggregateAntecipacao[P]>
    : Prisma.GetScalarType<T[P], AggregateAntecipacao[P]>
}




export type AntecipacaoGroupByArgs<ExtArgs extends runtime.Types.Extensions.InternalArgs = runtime.Types.Extensions.DefaultArgs> = {
  where?: Prisma.AntecipacaoWhereInput
  orderBy?: Prisma.AntecipacaoOrderByWithAggregationInput | Prisma.AntecipacaoOrderByWithAggregationInput[]
  by: Prisma.AntecipacaoScalarFieldEnum[] | Prisma.AntecipacaoScalarFieldEnum
  having?: Prisma.AntecipacaoScalarWhereWithAggregatesInput
  take?: number
  skip?: number
  _count?: AntecipacaoCountAggregateInputType | true
  _avg?: AntecipacaoAvgAggregateInputType
  _sum?: AntecipacaoSumAggregateInputType
  _min?: AntecipacaoMinAggregateInputType
  _max?: AntecipacaoMaxAggregateInputType
}

export type AntecipacaoGroupByOutputType = {
  id: number
  uuid: string
  plantaoId: number
  profissionalId: number
  valorSolicitado: number
  valorAprovado: number | null
  percentual: number
  taxaAntecipacao: number | null
  dataPagamentoPrevista: Date
  status: string
  numeroCCB: string | null
  dataCCB: Date | null
  arquivoCCB: string | null
  termoAssinado: boolean
  dataAssinaturaTermo: Date | null
  hashAssinatura: string | null
  ipAssinatura: string | null
  observacoes: string | null
  metaData: runtime.JsonValue | null
  createdAt: Date
  updatedAt: Date
  deletedAt: Date | null
  _count: AntecipacaoCountAggregateOutputType | null
  _avg: AntecipacaoAvgAggregateOutputType | null
  _sum: AntecipacaoSumAggregateOutputType | null
  _min: AntecipacaoMinAggregateOutputType | null
  _max: AntecipacaoMaxAggregateOutputType | null
}

type GetAntecipacaoGroupByPayload<T extends AntecipacaoGroupByArgs> = Prisma.PrismaPromise<
  Array<
    Prisma.PickEnumerable<AntecipacaoGroupByOutputType, T['by']> &
      {
        [P in ((keyof T) & (keyof AntecipacaoGroupByOutputType))]: P extends '_count'
          ? T[P] extends boolean
            ? number
            : Prisma.GetScalarType<T[P], AntecipacaoGroupByOutputType[P]>
          : Prisma.GetScalarType<T[P], AntecipacaoGroupByOutputType[P]>
      }
    >
  >



export type AntecipacaoWhereInput = {
  AND?: Prisma.AntecipacaoWhereInput | Prisma.AntecipacaoWhereInput[]
  OR?: Prisma.AntecipacaoWhereInput[]
  NOT?: Prisma.AntecipacaoWhereInput | Prisma.AntecipacaoWhereInput[]
  id?: Prisma.IntFilter<"Antecipacao"> | number
  uuid?: Prisma.StringFilter<"Antecipacao"> | string
  plantaoId?: Prisma.IntFilter<"Antecipacao"> | number
  profissionalId?: Prisma.IntFilter<"Antecipacao"> | number
  valorSolicitado?: Prisma.FloatFilter<"Antecipacao"> | number
  valorAprovado?: Prisma.FloatNullableFilter<"Antecipacao"> | number | null
  percentual?: Prisma.FloatFilter<"Antecipacao"> | number
  taxaAntecipacao?: Prisma.FloatNullableFilter<"Antecipacao"> | number | null
  dataPagamentoPrevista?: Prisma.DateTimeFilter<"Antecipacao"> | Date | string
  status?: Prisma.StringFilter<"Antecipacao"> | string
  numeroCCB?: Prisma.StringNullableFilter<"Antecipacao"> | string | null
  dataCCB?: Prisma.DateTimeNullableFilter<"Antecipacao"> | Date | string | null
  arquivoCCB?: Prisma.StringNullableFilter<"Antecipacao"> | string | null
  termoAssinado?: Prisma.BoolFilter<"Antecipacao"> | boolean
  dataAssinaturaTermo?: Prisma.DateTimeNullableFilter<"Antecipacao"> | Date | string | null
  hashAssinatura?: Prisma.StringNullableFilter<"Antecipacao"> | string | null
  ipAssinatura?: Prisma.StringNullableFilter<"Antecipacao"> | string | null
  observacoes?: Prisma.StringNullableFilter<"Antecipacao"> | string | null
  metaData?: Prisma.JsonNullableFilter<"Antecipacao">
  createdAt?: Prisma.DateTimeFilter<"Antecipacao"> | Date | string
  updatedAt?: Prisma.DateTimeFilter<"Antecipacao"> | Date | string
  deletedAt?: Prisma.DateTimeNullableFilter<"Antecipacao"> | Date | string | null
  plantao?: Prisma.XOR<Prisma.PlantaoScalarRelationFilter, Prisma.PlantaoWhereInput>
  profissional?: Prisma.XOR<Prisma.ProfissionalScalarRelationFilter, Prisma.ProfissionalWhereInput>
  fechamentos?: Prisma.FechamentoListRelationFilter
  historico?: Prisma.AntecipacaoHistoricoListRelationFilter
}

export type AntecipacaoOrderByWithRelationInput = {
  id?: Prisma.SortOrder
  uuid?: Prisma.SortOrder
  plantaoId?: Prisma.SortOrder
  profissionalId?: Prisma.SortOrder
  valorSolicitado?: Prisma.SortOrder
  valorAprovado?: Prisma.SortOrderInput | Prisma.SortOrder
  percentual?: Prisma.SortOrder
  taxaAntecipacao?: Prisma.SortOrderInput | Prisma.SortOrder
  dataPagamentoPrevista?: Prisma.SortOrder
  status?: Prisma.SortOrder
  numeroCCB?: Prisma.SortOrderInput | Prisma.SortOrder
  dataCCB?: Prisma.SortOrderInput | Prisma.SortOrder
  arquivoCCB?: Prisma.SortOrderInput | Prisma.SortOrder
  termoAssinado?: Prisma.SortOrder
  dataAssinaturaTermo?: Prisma.SortOrderInput | Prisma.SortOrder
  hashAssinatura?: Prisma.SortOrderInput | Prisma.SortOrder
  ipAssinatura?: Prisma.SortOrderInput | Prisma.SortOrder
  observacoes?: Prisma.SortOrderInput | Prisma.SortOrder
  metaData?: Prisma.SortOrderInput | Prisma.SortOrder
  createdAt?: Prisma.SortOrder
  updatedAt?: Prisma.SortOrder
  deletedAt?: Prisma.SortOrderInput | Prisma.SortOrder
  plantao?: Prisma.PlantaoOrderByWithRelationInput
  profissional?: Prisma.ProfissionalOrderByWithRelationInput
  fechamentos?: Prisma.FechamentoOrderByRelationAggregateInput
  historico?: Prisma.AntecipacaoHistoricoOrderByRelationAggregateInput
  _relevance?: Prisma.AntecipacaoOrderByRelevanceInput
}

export type AntecipacaoWhereUniqueInput = Prisma.AtLeast<{
  id?: number
  uuid?: string
  AND?: Prisma.AntecipacaoWhereInput | Prisma.AntecipacaoWhereInput[]
  OR?: Prisma.AntecipacaoWhereInput[]
  NOT?: Prisma.AntecipacaoWhereInput | Prisma.AntecipacaoWhereInput[]
  plantaoId?: Prisma.IntFilter<"Antecipacao"> | number
  profissionalId?: Prisma.IntFilter<"Antecipacao"> | number
  valorSolicitado?: Prisma.FloatFilter<"Antecipacao"> | number
  valorAprovado?: Prisma.FloatNullableFilter<"Antecipacao"> | number | null
  percentual?: Prisma.FloatFilter<"Antecipacao"> | number
  taxaAntecipacao?: Prisma.FloatNullableFilter<"Antecipacao"> | number | null
  dataPagamentoPrevista?: Prisma.DateTimeFilter<"Antecipacao"> | Date | string
  status?: Prisma.StringFilter<"Antecipacao"> | string
  numeroCCB?: Prisma.StringNullableFilter<"Antecipacao"> | string | null
  dataCCB?: Prisma.DateTimeNullableFilter<"Antecipacao"> | Date | string | null
  arquivoCCB?: Prisma.StringNullableFilter<"Antecipacao"> | string | null
  termoAssinado?: Prisma.BoolFilter<"Antecipacao"> | boolean
  dataAssinaturaTermo?: Prisma.DateTimeNullableFilter<"Antecipacao"> | Date | string | null
  hashAssinatura?: Prisma.StringNullableFilter<"Antecipacao"> | string | null
  ipAssinatura?: Prisma.StringNullableFilter<"Antecipacao"> | string | null
  observacoes?: Prisma.StringNullableFilter<"Antecipacao"> | string | null
  metaData?: Prisma.JsonNullableFilter<"Antecipacao">
  createdAt?: Prisma.DateTimeFilter<"Antecipacao"> | Date | string
  updatedAt?: Prisma.DateTimeFilter<"Antecipacao"> | Date | string
  deletedAt?: Prisma.DateTimeNullableFilter<"Antecipacao"> | Date | string | null
  plantao?: Prisma.XOR<Prisma.PlantaoScalarRelationFilter, Prisma.PlantaoWhereInput>
  profissional?: Prisma.XOR<Prisma.ProfissionalScalarRelationFilter, Prisma.ProfissionalWhereInput>
  fechamentos?: Prisma.FechamentoListRelationFilter
  historico?: Prisma.AntecipacaoHistoricoListRelationFilter
}, "id" | "uuid">

export type AntecipacaoOrderByWithAggregationInput = {
  id?: Prisma.SortOrder
  uuid?: Prisma.SortOrder
  plantaoId?: Prisma.SortOrder
  profissionalId?: Prisma.SortOrder
  valorSolicitado?: Prisma.SortOrder
  valorAprovado?: Prisma.SortOrderInput | Prisma.SortOrder
  percentual?: Prisma.SortOrder
  taxaAntecipacao?: Prisma.SortOrderInput | Prisma.SortOrder
  dataPagamentoPrevista?: Prisma.SortOrder
  status?: Prisma.SortOrder
  numeroCCB?: Prisma.SortOrderInput | Prisma.SortOrder
  dataCCB?: Prisma.SortOrderInput | Prisma.SortOrder
  arquivoCCB?: Prisma.SortOrderInput | Prisma.SortOrder
  termoAssinado?: Prisma.SortOrder
  dataAssinaturaTermo?: Prisma.SortOrderInput | Prisma.SortOrder
  hashAssinatura?: Prisma.SortOrderInput | Prisma.SortOrder
  ipAssinatura?: Prisma.SortOrderInput | Prisma.SortOrder
  observacoes?: Prisma.SortOrderInput | Prisma.SortOrder
  metaData?: Prisma.SortOrderInput | Prisma.SortOrder
  createdAt?: Prisma.SortOrder
  updatedAt?: Prisma.SortOrder
  deletedAt?: Prisma.SortOrderInput | Prisma.SortOrder
  _count?: Prisma.AntecipacaoCountOrderByAggregateInput
  _avg?: Prisma.AntecipacaoAvgOrderByAggregateInput
  _max?: Prisma.AntecipacaoMaxOrderByAggregateInput
  _min?: Prisma.AntecipacaoMinOrderByAggregateInput
  _sum?: Prisma.AntecipacaoSumOrderByAggregateInput
}

export type AntecipacaoScalarWhereWithAggregatesInput = {
  AND?: Prisma.AntecipacaoScalarWhereWithAggregatesInput | Prisma.AntecipacaoScalarWhereWithAggregatesInput[]
  OR?: Prisma.AntecipacaoScalarWhereWithAggregatesInput[]
  NOT?: Prisma.AntecipacaoScalarWhereWithAggregatesInput | Prisma.AntecipacaoScalarWhereWithAggregatesInput[]
  id?: Prisma.IntWithAggregatesFilter<"Antecipacao"> | number
  uuid?: Prisma.StringWithAggregatesFilter<"Antecipacao"> | string
  plantaoId?: Prisma.IntWithAggregatesFilter<"Antecipacao"> | number
  profissionalId?: Prisma.IntWithAggregatesFilter<"Antecipacao"> | number
  valorSolicitado?: Prisma.FloatWithAggregatesFilter<"Antecipacao"> | number
  valorAprovado?: Prisma.FloatNullableWithAggregatesFilter<"Antecipacao"> | number | null
  percentual?: Prisma.FloatWithAggregatesFilter<"Antecipacao"> | number
  taxaAntecipacao?: Prisma.FloatNullableWithAggregatesFilter<"Antecipacao"> | number | null
  dataPagamentoPrevista?: Prisma.DateTimeWithAggregatesFilter<"Antecipacao"> | Date | string
  status?: Prisma.StringWithAggregatesFilter<"Antecipacao"> | string
  numeroCCB?: Prisma.StringNullableWithAggregatesFilter<"Antecipacao"> | string | null
  dataCCB?: Prisma.DateTimeNullableWithAggregatesFilter<"Antecipacao"> | Date | string | null
  arquivoCCB?: Prisma.StringNullableWithAggregatesFilter<"Antecipacao"> | string | null
  termoAssinado?: Prisma.BoolWithAggregatesFilter<"Antecipacao"> | boolean
  dataAssinaturaTermo?: Prisma.DateTimeNullableWithAggregatesFilter<"Antecipacao"> | Date | string | null
  hashAssinatura?: Prisma.StringNullableWithAggregatesFilter<"Antecipacao"> | string | null
  ipAssinatura?: Prisma.StringNullableWithAggregatesFilter<"Antecipacao"> | string | null
  observacoes?: Prisma.StringNullableWithAggregatesFilter<"Antecipacao"> | string | null
  metaData?: Prisma.JsonNullableWithAggregatesFilter<"Antecipacao">
  createdAt?: Prisma.DateTimeWithAggregatesFilter<"Antecipacao"> | Date | string
  updatedAt?: Prisma.DateTimeWithAggregatesFilter<"Antecipacao"> | Date | string
  deletedAt?: Prisma.DateTimeNullableWithAggregatesFilter<"Antecipacao"> | Date | string | null
}

export type AntecipacaoCreateInput = {
  uuid?: string
  valorSolicitado: number
  valorAprovado?: number | null
  percentual: number
  taxaAntecipacao?: number | null
  dataPagamentoPrevista: Date | string
  status?: string
  numeroCCB?: string | null
  dataCCB?: Date | string | null
  arquivoCCB?: string | null
  termoAssinado?: boolean
  dataAssinaturaTermo?: Date | string | null
  hashAssinatura?: string | null
  ipAssinatura?: string | null
  observacoes?: string | null
  metaData?: Prisma.NullableJsonNullValueInput | runtime.InputJsonValue
  createdAt?: Date | string
  updatedAt?: Date | string
  deletedAt?: Date | string | null
  plantao: Prisma.PlantaoCreateNestedOneWithoutAntecipacoesInput
  profissional: Prisma.ProfissionalCreateNestedOneWithoutAntecipacoesInput
  fechamentos?: Prisma.FechamentoCreateNestedManyWithoutAntecipacaoInput
  historico?: Prisma.AntecipacaoHistoricoCreateNestedManyWithoutAntecipacaoInput
}

export type AntecipacaoUncheckedCreateInput = {
  id?: number
  uuid?: string
  plantaoId: number
  profissionalId: number
  valorSolicitado: number
  valorAprovado?: number | null
  percentual: number
  taxaAntecipacao?: number | null
  dataPagamentoPrevista: Date | string
  status?: string
  numeroCCB?: string | null
  dataCCB?: Date | string | null
  arquivoCCB?: string | null
  termoAssinado?: boolean
  dataAssinaturaTermo?: Date | string | null
  hashAssinatura?: string | null
  ipAssinatura?: string | null
  observacoes?: string | null
  metaData?: Prisma.NullableJsonNullValueInput | runtime.InputJsonValue
  createdAt?: Date | string
  updatedAt?: Date | string
  deletedAt?: Date | string | null
  fechamentos?: Prisma.FechamentoUncheckedCreateNestedManyWithoutAntecipacaoInput
  historico?: Prisma.AntecipacaoHistoricoUncheckedCreateNestedManyWithoutAntecipacaoInput
}

export type AntecipacaoUpdateInput = {
  uuid?: Prisma.StringFieldUpdateOperationsInput | string
  valorSolicitado?: Prisma.FloatFieldUpdateOperationsInput | number
  valorAprovado?: Prisma.NullableFloatFieldUpdateOperationsInput | number | null
  percentual?: Prisma.FloatFieldUpdateOperationsInput | number
  taxaAntecipacao?: Prisma.NullableFloatFieldUpdateOperationsInput | number | null
  dataPagamentoPrevista?: Prisma.DateTimeFieldUpdateOperationsInput | Date | string
  status?: Prisma.StringFieldUpdateOperationsInput | string
  numeroCCB?: Prisma.NullableStringFieldUpdateOperationsInput | string | null
  dataCCB?: Prisma.NullableDateTimeFieldUpdateOperationsInput | Date | string | null
  arquivoCCB?: Prisma.NullableStringFieldUpdateOperationsInput | string | null
  termoAssinado?: Prisma.BoolFieldUpdateOperationsInput | boolean
  dataAssinaturaTermo?: Prisma.NullableDateTimeFieldUpdateOperationsInput | Date | string | null
  hashAssinatura?: Prisma.NullableStringFieldUpdateOperationsInput | string | null
  ipAssinatura?: Prisma.NullableStringFieldUpdateOperationsInput | string | null
  observacoes?: Prisma.NullableStringFieldUpdateOperationsInput | string | null
  metaData?: Prisma.NullableJsonNullValueInput | runtime.InputJsonValue
  createdAt?: Prisma.DateTimeFieldUpdateOperationsInput | Date | string
  updatedAt?: Prisma.DateTimeFieldUpdateOperationsInput | Date | string
  deletedAt?: Prisma.NullableDateTimeFieldUpdateOperationsInput | Date | string | null
  plantao?: Prisma.PlantaoUpdateOneRequiredWithoutAntecipacoesNestedInput
  profissional?: Prisma.ProfissionalUpdateOneRequiredWithoutAntecipacoesNestedInput
  fechamentos?: Prisma.FechamentoUpdateManyWithoutAntecipacaoNestedInput
  historico?: Prisma.AntecipacaoHistoricoUpdateManyWithoutAntecipacaoNestedInput
}

export type AntecipacaoUncheckedUpdateInput = {
  id?: Prisma.IntFieldUpdateOperationsInput | number
  uuid?: Prisma.StringFieldUpdateOperationsInput | string
  plantaoId?: Prisma.IntFieldUpdateOperationsInput | number
  profissionalId?: Prisma.IntFieldUpdateOperationsInput | number
  valorSolicitado?: Prisma.FloatFieldUpdateOperationsInput | number
  valorAprovado?: Prisma.NullableFloatFieldUpdateOperationsInput | number | null
  percentual?: Prisma.FloatFieldUpdateOperationsInput | number
  taxaAntecipacao?: Prisma.NullableFloatFieldUpdateOperationsInput | number | null
  dataPagamentoPrevista?: Prisma.DateTimeFieldUpdateOperationsInput | Date | string
  status?: Prisma.StringFieldUpdateOperationsInput | string
  numeroCCB?: Prisma.NullableStringFieldUpdateOperationsInput | string | null
  dataCCB?: Prisma.NullableDateTimeFieldUpdateOperationsInput | Date | string | null
  arquivoCCB?: Prisma.NullableStringFieldUpdateOperationsInput | string | null
  termoAssinado?: Prisma.BoolFieldUpdateOperationsInput | boolean
  dataAssinaturaTermo?: Prisma.NullableDateTimeFieldUpdateOperationsInput | Date | string | null
  hashAssinatura?: Prisma.NullableStringFieldUpdateOperationsInput | string | null
  ipAssinatura?: Prisma.NullableStringFieldUpdateOperationsInput | string | null
  observacoes?: Prisma.NullableStringFieldUpdateOperationsInput | string | null
  metaData?: Prisma.NullableJsonNullValueInput | runtime.InputJsonValue
  createdAt?: Prisma.DateTimeFieldUpdateOperationsInput | Date | string
  updatedAt?: Prisma.DateTimeFieldUpdateOperationsInput | Date | string
  deletedAt?: Prisma.NullableDateTimeFieldUpdateOperationsInput | Date | string | null
  fechamentos?: Prisma.FechamentoUncheckedUpdateManyWithoutAntecipacaoNestedInput
  historico?: Prisma.AntecipacaoHistoricoUncheckedUpdateManyWithoutAntecipacaoNestedInput
}

export type AntecipacaoCreateManyInput = {
  id?: number
  uuid?: string
  plantaoId: number
  profissionalId: number
  valorSolicitado: number
  valorAprovado?: number | null
  percentual: number
  taxaAntecipacao?: number | null
  dataPagamentoPrevista: Date | string
  status?: string
  numeroCCB?: string | null
  dataCCB?: Date | string | null
  arquivoCCB?: string | null
  termoAssinado?: boolean
  dataAssinaturaTermo?: Date | string | null
  hashAssinatura?: string | null
  ipAssinatura?: string | null
  observacoes?: string | null
  metaData?: Prisma.NullableJsonNullValueInput | runtime.InputJsonValue
  createdAt?: Date | string
  updatedAt?: Date | string
  deletedAt?: Date | string | null
}

export type AntecipacaoUpdateManyMutationInput = {
  uuid?: Prisma.StringFieldUpdateOperationsInput | string
  valorSolicitado?: Prisma.FloatFieldUpdateOperationsInput | number
  valorAprovado?: Prisma.NullableFloatFieldUpdateOperationsInput | number | null
  percentual?: Prisma.FloatFieldUpdateOperationsInput | number
  taxaAntecipacao?: Prisma.NullableFloatFieldUpdateOperationsInput | number | null
  dataPagamentoPrevista?: Prisma.DateTimeFieldUpdateOperationsInput | Date | string
  status?: Prisma.StringFieldUpdateOperationsInput | string
  numeroCCB?: Prisma.NullableStringFieldUpdateOperationsInput | string | null
  dataCCB?: Prisma.NullableDateTimeFieldUpdateOperationsInput | Date | string | null
  arquivoCCB?: Prisma.NullableStringFieldUpdateOperationsInput | string | null
  termoAssinado?: Prisma.BoolFieldUpdateOperationsInput | boolean
  dataAssinaturaTermo?: Prisma.NullableDateTimeFieldUpdateOperationsInput | Date | string | null
  hashAssinatura?: Prisma.NullableStringFieldUpdateOperationsInput | string | null
  ipAssinatura?: Prisma.NullableStringFieldUpdateOperationsInput | string | null
  observacoes?: Prisma.NullableStringFieldUpdateOperationsInput | string | null
  metaData?: Prisma.NullableJsonNullValueInput | runtime.InputJsonValue
  createdAt?: Prisma.DateTimeFieldUpdateOperationsInput | Date | string
  updatedAt?: Prisma.DateTimeFieldUpdateOperationsInput | Date | string
  deletedAt?: Prisma.NullableDateTimeFieldUpdateOperationsInput | Date | string | null
}

export type AntecipacaoUncheckedUpdateManyInput = {
  id?: Prisma.IntFieldUpdateOperationsInput | number
  uuid?: Prisma.StringFieldUpdateOperationsInput | string
  plantaoId?: Prisma.IntFieldUpdateOperationsInput | number
  profissionalId?: Prisma.IntFieldUpdateOperationsInput | number
  valorSolicitado?: Prisma.FloatFieldUpdateOperationsInput | number
  valorAprovado?: Prisma.NullableFloatFieldUpdateOperationsInput | number | null
  percentual?: Prisma.FloatFieldUpdateOperationsInput | number
  taxaAntecipacao?: Prisma.NullableFloatFieldUpdateOperationsInput | number | null
  dataPagamentoPrevista?: Prisma.DateTimeFieldUpdateOperationsInput | Date | string
  status?: Prisma.StringFieldUpdateOperationsInput | string
  numeroCCB?: Prisma.NullableStringFieldUpdateOperationsInput | string | null
  dataCCB?: Prisma.NullableDateTimeFieldUpdateOperationsInput | Date | string | null
  arquivoCCB?: Prisma.NullableStringFieldUpdateOperationsInput | string | null
  termoAssinado?: Prisma.BoolFieldUpdateOperationsInput | boolean
  dataAssinaturaTermo?: Prisma.NullableDateTimeFieldUpdateOperationsInput | Date | string | null
  hashAssinatura?: Prisma.NullableStringFieldUpdateOperationsInput | string | null
  ipAssinatura?: Prisma.NullableStringFieldUpdateOperationsInput | string | null
  observacoes?: Prisma.NullableStringFieldUpdateOperationsInput | string | null
  metaData?: Prisma.NullableJsonNullValueInput | runtime.InputJsonValue
  createdAt?: Prisma.DateTimeFieldUpdateOperationsInput | Date | string
  updatedAt?: Prisma.DateTimeFieldUpdateOperationsInput | Date | string
  deletedAt?: Prisma.NullableDateTimeFieldUpdateOperationsInput | Date | string | null
}

export type AntecipacaoListRelationFilter = {
  every?: Prisma.AntecipacaoWhereInput
  some?: Prisma.AntecipacaoWhereInput
  none?: Prisma.AntecipacaoWhereInput
}

export type AntecipacaoOrderByRelationAggregateInput = {
  _count?: Prisma.SortOrder
}

export type AntecipacaoNullableScalarRelationFilter = {
  is?: Prisma.AntecipacaoWhereInput | null
  isNot?: Prisma.AntecipacaoWhereInput | null
}

export type AntecipacaoOrderByRelevanceInput = {
  fields: Prisma.AntecipacaoOrderByRelevanceFieldEnum | Prisma.AntecipacaoOrderByRelevanceFieldEnum[]
  sort: Prisma.SortOrder
  search: string
}

export type AntecipacaoCountOrderByAggregateInput = {
  id?: Prisma.SortOrder
  uuid?: Prisma.SortOrder
  plantaoId?: Prisma.SortOrder
  profissionalId?: Prisma.SortOrder
  valorSolicitado?: Prisma.SortOrder
  valorAprovado?: Prisma.SortOrder
  percentual?: Prisma.SortOrder
  taxaAntecipacao?: Prisma.SortOrder
  dataPagamentoPrevista?: Prisma.SortOrder
  status?: Prisma.SortOrder
  numeroCCB?: Prisma.SortOrder
  dataCCB?: Prisma.SortOrder
  arquivoCCB?: Prisma.SortOrder
  termoAssinado?: Prisma.SortOrder
  dataAssinaturaTermo?: Prisma.SortOrder
  hashAssinatura?: Prisma.SortOrder
  ipAssinatura?: Prisma.SortOrder
  observacoes?: Prisma.SortOrder
  metaData?: Prisma.SortOrder
  createdAt?: Prisma.SortOrder
  updatedAt?: Prisma.SortOrder
  deletedAt?: Prisma.SortOrder
}

export type AntecipacaoAvgOrderByAggregateInput = {
  id?: Prisma.SortOrder
  plantaoId?: Prisma.SortOrder
  profissionalId?: Prisma.SortOrder
  valorSolicitado?: Prisma.SortOrder
  valorAprovado?: Prisma.SortOrder
  percentual?: Prisma.SortOrder
  taxaAntecipacao?: Prisma.SortOrder
}

export type AntecipacaoMaxOrderByAggregateInput = {
  id?: Prisma.SortOrder
  uuid?: Prisma.SortOrder
  plantaoId?: Prisma.SortOrder
  profissionalId?: Prisma.SortOrder
  valorSolicitado?: Prisma.SortOrder
  valorAprovado?: Prisma.SortOrder
  percentual?: Prisma.SortOrder
  taxaAntecipacao?: Prisma.SortOrder
  dataPagamentoPrevista?: Prisma.SortOrder
  status?: Prisma.SortOrder
  numeroCCB?: Prisma.SortOrder
  dataCCB?: Prisma.SortOrder
  arquivoCCB?: Prisma.SortOrder
  termoAssinado?: Prisma.SortOrder
  dataAssinaturaTermo?: Prisma.SortOrder
  hashAssinatura?: Prisma.SortOrder
  ipAssinatura?: Prisma.SortOrder
  observacoes?: Prisma.SortOrder
  createdAt?: Prisma.SortOrder
  updatedAt?: Prisma.SortOrder
  deletedAt?: Prisma.SortOrder
}

export type AntecipacaoMinOrderByAggregateInput = {
  id?: Prisma.SortOrder
  uuid?: Prisma.SortOrder
  plantaoId?: Prisma.SortOrder
  profissionalId?: Prisma.SortOrder
  valorSolicitado?: Prisma.SortOrder
  valorAprovado?: Prisma.SortOrder
  percentual?: Prisma.SortOrder
  taxaAntecipacao?: Prisma.SortOrder
  dataPagamentoPrevista?: Prisma.SortOrder
  status?: Prisma.SortOrder
  numeroCCB?: Prisma.SortOrder
  dataCCB?: Prisma.SortOrder
  arquivoCCB?: Prisma.SortOrder
  termoAssinado?: Prisma.SortOrder
  dataAssinaturaTermo?: Prisma.SortOrder
  hashAssinatura?: Prisma.SortOrder
  ipAssinatura?: Prisma.SortOrder
  observacoes?: Prisma.SortOrder
  createdAt?: Prisma.SortOrder
  updatedAt?: Prisma.SortOrder
  deletedAt?: Prisma.SortOrder
}

export type AntecipacaoSumOrderByAggregateInput = {
  id?: Prisma.SortOrder
  plantaoId?: Prisma.SortOrder
  profissionalId?: Prisma.SortOrder
  valorSolicitado?: Prisma.SortOrder
  valorAprovado?: Prisma.SortOrder
  percentual?: Prisma.SortOrder
  taxaAntecipacao?: Prisma.SortOrder
}

export type AntecipacaoScalarRelationFilter = {
  is?: Prisma.AntecipacaoWhereInput
  isNot?: Prisma.AntecipacaoWhereInput
}

export type AntecipacaoCreateNestedManyWithoutProfissionalInput = {
  create?: Prisma.XOR<Prisma.AntecipacaoCreateWithoutProfissionalInput, Prisma.AntecipacaoUncheckedCreateWithoutProfissionalInput> | Prisma.AntecipacaoCreateWithoutProfissionalInput[] | Prisma.AntecipacaoUncheckedCreateWithoutProfissionalInput[]
  connectOrCreate?: Prisma.AntecipacaoCreateOrConnectWithoutProfissionalInput | Prisma.AntecipacaoCreateOrConnectWithoutProfissionalInput[]
  createMany?: Prisma.AntecipacaoCreateManyProfissionalInputEnvelope
  connect?: Prisma.AntecipacaoWhereUniqueInput | Prisma.AntecipacaoWhereUniqueInput[]
}

export type AntecipacaoUncheckedCreateNestedManyWithoutProfissionalInput = {
  create?: Prisma.XOR<Prisma.AntecipacaoCreateWithoutProfissionalInput, Prisma.AntecipacaoUncheckedCreateWithoutProfissionalInput> | Prisma.AntecipacaoCreateWithoutProfissionalInput[] | Prisma.AntecipacaoUncheckedCreateWithoutProfissionalInput[]
  connectOrCreate?: Prisma.AntecipacaoCreateOrConnectWithoutProfissionalInput | Prisma.AntecipacaoCreateOrConnectWithoutProfissionalInput[]
  createMany?: Prisma.AntecipacaoCreateManyProfissionalInputEnvelope
  connect?: Prisma.AntecipacaoWhereUniqueInput | Prisma.AntecipacaoWhereUniqueInput[]
}

export type AntecipacaoUpdateManyWithoutProfissionalNestedInput = {
  create?: Prisma.XOR<Prisma.AntecipacaoCreateWithoutProfissionalInput, Prisma.AntecipacaoUncheckedCreateWithoutProfissionalInput> | Prisma.AntecipacaoCreateWithoutProfissionalInput[] | Prisma.AntecipacaoUncheckedCreateWithoutProfissionalInput[]
  connectOrCreate?: Prisma.AntecipacaoCreateOrConnectWithoutProfissionalInput | Prisma.AntecipacaoCreateOrConnectWithoutProfissionalInput[]
  upsert?: Prisma.AntecipacaoUpsertWithWhereUniqueWithoutProfissionalInput | Prisma.AntecipacaoUpsertWithWhereUniqueWithoutProfissionalInput[]
  createMany?: Prisma.AntecipacaoCreateManyProfissionalInputEnvelope
  set?: Prisma.AntecipacaoWhereUniqueInput | Prisma.AntecipacaoWhereUniqueInput[]
  disconnect?: Prisma.AntecipacaoWhereUniqueInput | Prisma.AntecipacaoWhereUniqueInput[]
  delete?: Prisma.AntecipacaoWhereUniqueInput | Prisma.AntecipacaoWhereUniqueInput[]
  connect?: Prisma.AntecipacaoWhereUniqueInput | Prisma.AntecipacaoWhereUniqueInput[]
  update?: Prisma.AntecipacaoUpdateWithWhereUniqueWithoutProfissionalInput | Prisma.AntecipacaoUpdateWithWhereUniqueWithoutProfissionalInput[]
  updateMany?: Prisma.AntecipacaoUpdateManyWithWhereWithoutProfissionalInput | Prisma.AntecipacaoUpdateManyWithWhereWithoutProfissionalInput[]
  deleteMany?: Prisma.AntecipacaoScalarWhereInput | Prisma.AntecipacaoScalarWhereInput[]
}

export type AntecipacaoUncheckedUpdateManyWithoutProfissionalNestedInput = {
  create?: Prisma.XOR<Prisma.AntecipacaoCreateWithoutProfissionalInput, Prisma.AntecipacaoUncheckedCreateWithoutProfissionalInput> | Prisma.AntecipacaoCreateWithoutProfissionalInput[] | Prisma.AntecipacaoUncheckedCreateWithoutProfissionalInput[]
  connectOrCreate?: Prisma.AntecipacaoCreateOrConnectWithoutProfissionalInput | Prisma.AntecipacaoCreateOrConnectWithoutProfissionalInput[]
  upsert?: Prisma.AntecipacaoUpsertWithWhereUniqueWithoutProfissionalInput | Prisma.AntecipacaoUpsertWithWhereUniqueWithoutProfissionalInput[]
  createMany?: Prisma.AntecipacaoCreateManyProfissionalInputEnvelope
  set?: Prisma.AntecipacaoWhereUniqueInput | Prisma.AntecipacaoWhereUniqueInput[]
  disconnect?: Prisma.AntecipacaoWhereUniqueInput | Prisma.AntecipacaoWhereUniqueInput[]
  delete?: Prisma.AntecipacaoWhereUniqueInput | Prisma.AntecipacaoWhereUniqueInput[]
  connect?: Prisma.AntecipacaoWhereUniqueInput | Prisma.AntecipacaoWhereUniqueInput[]
  update?: Prisma.AntecipacaoUpdateWithWhereUniqueWithoutProfissionalInput | Prisma.AntecipacaoUpdateWithWhereUniqueWithoutProfissionalInput[]
  updateMany?: Prisma.AntecipacaoUpdateManyWithWhereWithoutProfissionalInput | Prisma.AntecipacaoUpdateManyWithWhereWithoutProfissionalInput[]
  deleteMany?: Prisma.AntecipacaoScalarWhereInput | Prisma.AntecipacaoScalarWhereInput[]
}

export type AntecipacaoCreateNestedManyWithoutPlantaoInput = {
  create?: Prisma.XOR<Prisma.AntecipacaoCreateWithoutPlantaoInput, Prisma.AntecipacaoUncheckedCreateWithoutPlantaoInput> | Prisma.AntecipacaoCreateWithoutPlantaoInput[] | Prisma.AntecipacaoUncheckedCreateWithoutPlantaoInput[]
  connectOrCreate?: Prisma.AntecipacaoCreateOrConnectWithoutPlantaoInput | Prisma.AntecipacaoCreateOrConnectWithoutPlantaoInput[]
  createMany?: Prisma.AntecipacaoCreateManyPlantaoInputEnvelope
  connect?: Prisma.AntecipacaoWhereUniqueInput | Prisma.AntecipacaoWhereUniqueInput[]
}

export type AntecipacaoUncheckedCreateNestedManyWithoutPlantaoInput = {
  create?: Prisma.XOR<Prisma.AntecipacaoCreateWithoutPlantaoInput, Prisma.AntecipacaoUncheckedCreateWithoutPlantaoInput> | Prisma.AntecipacaoCreateWithoutPlantaoInput[] | Prisma.AntecipacaoUncheckedCreateWithoutPlantaoInput[]
  connectOrCreate?: Prisma.AntecipacaoCreateOrConnectWithoutPlantaoInput | Prisma.AntecipacaoCreateOrConnectWithoutPlantaoInput[]
  createMany?: Prisma.AntecipacaoCreateManyPlantaoInputEnvelope
  connect?: Prisma.AntecipacaoWhereUniqueInput | Prisma.AntecipacaoWhereUniqueInput[]
}

export type AntecipacaoUpdateManyWithoutPlantaoNestedInput = {
  create?: Prisma.XOR<Prisma.AntecipacaoCreateWithoutPlantaoInput, Prisma.AntecipacaoUncheckedCreateWithoutPlantaoInput> | Prisma.AntecipacaoCreateWithoutPlantaoInput[] | Prisma.AntecipacaoUncheckedCreateWithoutPlantaoInput[]
  connectOrCreate?: Prisma.AntecipacaoCreateOrConnectWithoutPlantaoInput | Prisma.AntecipacaoCreateOrConnectWithoutPlantaoInput[]
  upsert?: Prisma.AntecipacaoUpsertWithWhereUniqueWithoutPlantaoInput | Prisma.AntecipacaoUpsertWithWhereUniqueWithoutPlantaoInput[]
  createMany?: Prisma.AntecipacaoCreateManyPlantaoInputEnvelope
  set?: Prisma.AntecipacaoWhereUniqueInput | Prisma.AntecipacaoWhereUniqueInput[]
  disconnect?: Prisma.AntecipacaoWhereUniqueInput | Prisma.AntecipacaoWhereUniqueInput[]
  delete?: Prisma.AntecipacaoWhereUniqueInput | Prisma.AntecipacaoWhereUniqueInput[]
  connect?: Prisma.AntecipacaoWhereUniqueInput | Prisma.AntecipacaoWhereUniqueInput[]
  update?: Prisma.AntecipacaoUpdateWithWhereUniqueWithoutPlantaoInput | Prisma.AntecipacaoUpdateWithWhereUniqueWithoutPlantaoInput[]
  updateMany?: Prisma.AntecipacaoUpdateManyWithWhereWithoutPlantaoInput | Prisma.AntecipacaoUpdateManyWithWhereWithoutPlantaoInput[]
  deleteMany?: Prisma.AntecipacaoScalarWhereInput | Prisma.AntecipacaoScalarWhereInput[]
}

export type AntecipacaoUncheckedUpdateManyWithoutPlantaoNestedInput = {
  create?: Prisma.XOR<Prisma.AntecipacaoCreateWithoutPlantaoInput, Prisma.AntecipacaoUncheckedCreateWithoutPlantaoInput> | Prisma.AntecipacaoCreateWithoutPlantaoInput[] | Prisma.AntecipacaoUncheckedCreateWithoutPlantaoInput[]
  connectOrCreate?: Prisma.AntecipacaoCreateOrConnectWithoutPlantaoInput | Prisma.AntecipacaoCreateOrConnectWithoutPlantaoInput[]
  upsert?: Prisma.AntecipacaoUpsertWithWhereUniqueWithoutPlantaoInput | Prisma.AntecipacaoUpsertWithWhereUniqueWithoutPlantaoInput[]
  createMany?: Prisma.AntecipacaoCreateManyPlantaoInputEnvelope
  set?: Prisma.AntecipacaoWhereUniqueInput | Prisma.AntecipacaoWhereUniqueInput[]
  disconnect?: Prisma.AntecipacaoWhereUniqueInput | Prisma.AntecipacaoWhereUniqueInput[]
  delete?: Prisma.AntecipacaoWhereUniqueInput | Prisma.AntecipacaoWhereUniqueInput[]
  connect?: Prisma.AntecipacaoWhereUniqueInput | Prisma.AntecipacaoWhereUniqueInput[]
  update?: Prisma.AntecipacaoUpdateWithWhereUniqueWithoutPlantaoInput | Prisma.AntecipacaoUpdateWithWhereUniqueWithoutPlantaoInput[]
  updateMany?: Prisma.AntecipacaoUpdateManyWithWhereWithoutPlantaoInput | Prisma.AntecipacaoUpdateManyWithWhereWithoutPlantaoInput[]
  deleteMany?: Prisma.AntecipacaoScalarWhereInput | Prisma.AntecipacaoScalarWhereInput[]
}

export type AntecipacaoCreateNestedOneWithoutFechamentosInput = {
  create?: Prisma.XOR<Prisma.AntecipacaoCreateWithoutFechamentosInput, Prisma.AntecipacaoUncheckedCreateWithoutFechamentosInput>
  connectOrCreate?: Prisma.AntecipacaoCreateOrConnectWithoutFechamentosInput
  connect?: Prisma.AntecipacaoWhereUniqueInput
}

export type AntecipacaoUpdateOneWithoutFechamentosNestedInput = {
  create?: Prisma.XOR<Prisma.AntecipacaoCreateWithoutFechamentosInput, Prisma.AntecipacaoUncheckedCreateWithoutFechamentosInput>
  connectOrCreate?: Prisma.AntecipacaoCreateOrConnectWithoutFechamentosInput
  upsert?: Prisma.AntecipacaoUpsertWithoutFechamentosInput
  disconnect?: Prisma.AntecipacaoWhereInput | boolean
  delete?: Prisma.AntecipacaoWhereInput | boolean
  connect?: Prisma.AntecipacaoWhereUniqueInput
  update?: Prisma.XOR<Prisma.XOR<Prisma.AntecipacaoUpdateToOneWithWhereWithoutFechamentosInput, Prisma.AntecipacaoUpdateWithoutFechamentosInput>, Prisma.AntecipacaoUncheckedUpdateWithoutFechamentosInput>
}

export type AntecipacaoCreateNestedOneWithoutHistoricoInput = {
  create?: Prisma.XOR<Prisma.AntecipacaoCreateWithoutHistoricoInput, Prisma.AntecipacaoUncheckedCreateWithoutHistoricoInput>
  connectOrCreate?: Prisma.AntecipacaoCreateOrConnectWithoutHistoricoInput
  connect?: Prisma.AntecipacaoWhereUniqueInput
}

export type AntecipacaoUpdateOneRequiredWithoutHistoricoNestedInput = {
  create?: Prisma.XOR<Prisma.AntecipacaoCreateWithoutHistoricoInput, Prisma.AntecipacaoUncheckedCreateWithoutHistoricoInput>
  connectOrCreate?: Prisma.AntecipacaoCreateOrConnectWithoutHistoricoInput
  upsert?: Prisma.AntecipacaoUpsertWithoutHistoricoInput
  connect?: Prisma.AntecipacaoWhereUniqueInput
  update?: Prisma.XOR<Prisma.XOR<Prisma.AntecipacaoUpdateToOneWithWhereWithoutHistoricoInput, Prisma.AntecipacaoUpdateWithoutHistoricoInput>, Prisma.AntecipacaoUncheckedUpdateWithoutHistoricoInput>
}

export type AntecipacaoCreateWithoutProfissionalInput = {
  uuid?: string
  valorSolicitado: number
  valorAprovado?: number | null
  percentual: number
  taxaAntecipacao?: number | null
  dataPagamentoPrevista: Date | string
  status?: string
  numeroCCB?: string | null
  dataCCB?: Date | string | null
  arquivoCCB?: string | null
  termoAssinado?: boolean
  dataAssinaturaTermo?: Date | string | null
  hashAssinatura?: string | null
  ipAssinatura?: string | null
  observacoes?: string | null
  metaData?: Prisma.NullableJsonNullValueInput | runtime.InputJsonValue
  createdAt?: Date | string
  updatedAt?: Date | string
  deletedAt?: Date | string | null
  plantao: Prisma.PlantaoCreateNestedOneWithoutAntecipacoesInput
  fechamentos?: Prisma.FechamentoCreateNestedManyWithoutAntecipacaoInput
  historico?: Prisma.AntecipacaoHistoricoCreateNestedManyWithoutAntecipacaoInput
}

export type AntecipacaoUncheckedCreateWithoutProfissionalInput = {
  id?: number
  uuid?: string
  plantaoId: number
  valorSolicitado: number
  valorAprovado?: number | null
  percentual: number
  taxaAntecipacao?: number | null
  dataPagamentoPrevista: Date | string
  status?: string
  numeroCCB?: string | null
  dataCCB?: Date | string | null
  arquivoCCB?: string | null
  termoAssinado?: boolean
  dataAssinaturaTermo?: Date | string | null
  hashAssinatura?: string | null
  ipAssinatura?: string | null
  observacoes?: string | null
  metaData?: Prisma.NullableJsonNullValueInput | runtime.InputJsonValue
  createdAt?: Date | string
  updatedAt?: Date | string
  deletedAt?: Date | string | null
  fechamentos?: Prisma.FechamentoUncheckedCreateNestedManyWithoutAntecipacaoInput
  historico?: Prisma.AntecipacaoHistoricoUncheckedCreateNestedManyWithoutAntecipacaoInput
}

export type AntecipacaoCreateOrConnectWithoutProfissionalInput = {
  where: Prisma.AntecipacaoWhereUniqueInput
  create: Prisma.XOR<Prisma.AntecipacaoCreateWithoutProfissionalInput, Prisma.AntecipacaoUncheckedCreateWithoutProfissionalInput>
}

export type AntecipacaoCreateManyProfissionalInputEnvelope = {
  data: Prisma.AntecipacaoCreateManyProfissionalInput | Prisma.AntecipacaoCreateManyProfissionalInput[]
  skipDuplicates?: boolean
}

export type AntecipacaoUpsertWithWhereUniqueWithoutProfissionalInput = {
  where: Prisma.AntecipacaoWhereUniqueInput
  update: Prisma.XOR<Prisma.AntecipacaoUpdateWithoutProfissionalInput, Prisma.AntecipacaoUncheckedUpdateWithoutProfissionalInput>
  create: Prisma.XOR<Prisma.AntecipacaoCreateWithoutProfissionalInput, Prisma.AntecipacaoUncheckedCreateWithoutProfissionalInput>
}

export type AntecipacaoUpdateWithWhereUniqueWithoutProfissionalInput = {
  where: Prisma.AntecipacaoWhereUniqueInput
  data: Prisma.XOR<Prisma.AntecipacaoUpdateWithoutProfissionalInput, Prisma.AntecipacaoUncheckedUpdateWithoutProfissionalInput>
}

export type AntecipacaoUpdateManyWithWhereWithoutProfissionalInput = {
  where: Prisma.AntecipacaoScalarWhereInput
  data: Prisma.XOR<Prisma.AntecipacaoUpdateManyMutationInput, Prisma.AntecipacaoUncheckedUpdateManyWithoutProfissionalInput>
}

export type AntecipacaoScalarWhereInput = {
  AND?: Prisma.AntecipacaoScalarWhereInput | Prisma.AntecipacaoScalarWhereInput[]
  OR?: Prisma.AntecipacaoScalarWhereInput[]
  NOT?: Prisma.AntecipacaoScalarWhereInput | Prisma.AntecipacaoScalarWhereInput[]
  id?: Prisma.IntFilter<"Antecipacao"> | number
  uuid?: Prisma.StringFilter<"Antecipacao"> | string
  plantaoId?: Prisma.IntFilter<"Antecipacao"> | number
  profissionalId?: Prisma.IntFilter<"Antecipacao"> | number
  valorSolicitado?: Prisma.FloatFilter<"Antecipacao"> | number
  valorAprovado?: Prisma.FloatNullableFilter<"Antecipacao"> | number | null
  percentual?: Prisma.FloatFilter<"Antecipacao"> | number
  taxaAntecipacao?: Prisma.FloatNullableFilter<"Antecipacao"> | number | null
  dataPagamentoPrevista?: Prisma.DateTimeFilter<"Antecipacao"> | Date | string
  status?: Prisma.StringFilter<"Antecipacao"> | string
  numeroCCB?: Prisma.StringNullableFilter<"Antecipacao"> | string | null
  dataCCB?: Prisma.DateTimeNullableFilter<"Antecipacao"> | Date | string | null
  arquivoCCB?: Prisma.StringNullableFilter<"Antecipacao"> | string | null
  termoAssinado?: Prisma.BoolFilter<"Antecipacao"> | boolean
  dataAssinaturaTermo?: Prisma.DateTimeNullableFilter<"Antecipacao"> | Date | string | null
  hashAssinatura?: Prisma.StringNullableFilter<"Antecipacao"> | string | null
  ipAssinatura?: Prisma.StringNullableFilter<"Antecipacao"> | string | null
  observacoes?: Prisma.StringNullableFilter<"Antecipacao"> | string | null
  metaData?: Prisma.JsonNullableFilter<"Antecipacao">
  createdAt?: Prisma.DateTimeFilter<"Antecipacao"> | Date | string
  updatedAt?: Prisma.DateTimeFilter<"Antecipacao"> | Date | string
  deletedAt?: Prisma.DateTimeNullableFilter<"Antecipacao"> | Date | string | null
}

export type AntecipacaoCreateWithoutPlantaoInput = {
  uuid?: string
  valorSolicitado: number
  valorAprovado?: number | null
  percentual: number
  taxaAntecipacao?: number | null
  dataPagamentoPrevista: Date | string
  status?: string
  numeroCCB?: string | null
  dataCCB?: Date | string | null
  arquivoCCB?: string | null
  termoAssinado?: boolean
  dataAssinaturaTermo?: Date | string | null
  hashAssinatura?: string | null
  ipAssinatura?: string | null
  observacoes?: string | null
  metaData?: Prisma.NullableJsonNullValueInput | runtime.InputJsonValue
  createdAt?: Date | string
  updatedAt?: Date | string
  deletedAt?: Date | string | null
  profissional: Prisma.ProfissionalCreateNestedOneWithoutAntecipacoesInput
  fechamentos?: Prisma.FechamentoCreateNestedManyWithoutAntecipacaoInput
  historico?: Prisma.AntecipacaoHistoricoCreateNestedManyWithoutAntecipacaoInput
}

export type AntecipacaoUncheckedCreateWithoutPlantaoInput = {
  id?: number
  uuid?: string
  profissionalId: number
  valorSolicitado: number
  valorAprovado?: number | null
  percentual: number
  taxaAntecipacao?: number | null
  dataPagamentoPrevista: Date | string
  status?: string
  numeroCCB?: string | null
  dataCCB?: Date | string | null
  arquivoCCB?: string | null
  termoAssinado?: boolean
  dataAssinaturaTermo?: Date | string | null
  hashAssinatura?: string | null
  ipAssinatura?: string | null
  observacoes?: string | null
  metaData?: Prisma.NullableJsonNullValueInput | runtime.InputJsonValue
  createdAt?: Date | string
  updatedAt?: Date | string
  deletedAt?: Date | string | null
  fechamentos?: Prisma.FechamentoUncheckedCreateNestedManyWithoutAntecipacaoInput
  historico?: Prisma.AntecipacaoHistoricoUncheckedCreateNestedManyWithoutAntecipacaoInput
}

export type AntecipacaoCreateOrConnectWithoutPlantaoInput = {
  where: Prisma.AntecipacaoWhereUniqueInput
  create: Prisma.XOR<Prisma.AntecipacaoCreateWithoutPlantaoInput, Prisma.AntecipacaoUncheckedCreateWithoutPlantaoInput>
}

export type AntecipacaoCreateManyPlantaoInputEnvelope = {
  data: Prisma.AntecipacaoCreateManyPlantaoInput | Prisma.AntecipacaoCreateManyPlantaoInput[]
  skipDuplicates?: boolean
}

export type AntecipacaoUpsertWithWhereUniqueWithoutPlantaoInput = {
  where: Prisma.AntecipacaoWhereUniqueInput
  update: Prisma.XOR<Prisma.AntecipacaoUpdateWithoutPlantaoInput, Prisma.AntecipacaoUncheckedUpdateWithoutPlantaoInput>
  create: Prisma.XOR<Prisma.AntecipacaoCreateWithoutPlantaoInput, Prisma.AntecipacaoUncheckedCreateWithoutPlantaoInput>
}

export type AntecipacaoUpdateWithWhereUniqueWithoutPlantaoInput = {
  where: Prisma.AntecipacaoWhereUniqueInput
  data: Prisma.XOR<Prisma.AntecipacaoUpdateWithoutPlantaoInput, Prisma.AntecipacaoUncheckedUpdateWithoutPlantaoInput>
}

export type AntecipacaoUpdateManyWithWhereWithoutPlantaoInput = {
  where: Prisma.AntecipacaoScalarWhereInput
  data: Prisma.XOR<Prisma.AntecipacaoUpdateManyMutationInput, Prisma.AntecipacaoUncheckedUpdateManyWithoutPlantaoInput>
}

export type AntecipacaoCreateWithoutFechamentosInput = {
  uuid?: string
  valorSolicitado: number
  valorAprovado?: number | null
  percentual: number
  taxaAntecipacao?: number | null
  dataPagamentoPrevista: Date | string
  status?: string
  numeroCCB?: string | null
  dataCCB?: Date | string | null
  arquivoCCB?: string | null
  termoAssinado?: boolean
  dataAssinaturaTermo?: Date | string | null
  hashAssinatura?: string | null
  ipAssinatura?: string | null
  observacoes?: string | null
  metaData?: Prisma.NullableJsonNullValueInput | runtime.InputJsonValue
  createdAt?: Date | string
  updatedAt?: Date | string
  deletedAt?: Date | string | null
  plantao: Prisma.PlantaoCreateNestedOneWithoutAntecipacoesInput
  profissional: Prisma.ProfissionalCreateNestedOneWithoutAntecipacoesInput
  historico?: Prisma.AntecipacaoHistoricoCreateNestedManyWithoutAntecipacaoInput
}

export type AntecipacaoUncheckedCreateWithoutFechamentosInput = {
  id?: number
  uuid?: string
  plantaoId: number
  profissionalId: number
  valorSolicitado: number
  valorAprovado?: number | null
  percentual: number
  taxaAntecipacao?: number | null
  dataPagamentoPrevista: Date | string
  status?: string
  numeroCCB?: string | null
  dataCCB?: Date | string | null
  arquivoCCB?: string | null
  termoAssinado?: boolean
  dataAssinaturaTermo?: Date | string | null
  hashAssinatura?: string | null
  ipAssinatura?: string | null
  observacoes?: string | null
  metaData?: Prisma.NullableJsonNullValueInput | runtime.InputJsonValue
  createdAt?: Date | string
  updatedAt?: Date | string
  deletedAt?: Date | string | null
  historico?: Prisma.AntecipacaoHistoricoUncheckedCreateNestedManyWithoutAntecipacaoInput
}

export type AntecipacaoCreateOrConnectWithoutFechamentosInput = {
  where: Prisma.AntecipacaoWhereUniqueInput
  create: Prisma.XOR<Prisma.AntecipacaoCreateWithoutFechamentosInput, Prisma.AntecipacaoUncheckedCreateWithoutFechamentosInput>
}

export type AntecipacaoUpsertWithoutFechamentosInput = {
  update: Prisma.XOR<Prisma.AntecipacaoUpdateWithoutFechamentosInput, Prisma.AntecipacaoUncheckedUpdateWithoutFechamentosInput>
  create: Prisma.XOR<Prisma.AntecipacaoCreateWithoutFechamentosInput, Prisma.AntecipacaoUncheckedCreateWithoutFechamentosInput>
  where?: Prisma.AntecipacaoWhereInput
}

export type AntecipacaoUpdateToOneWithWhereWithoutFechamentosInput = {
  where?: Prisma.AntecipacaoWhereInput
  data: Prisma.XOR<Prisma.AntecipacaoUpdateWithoutFechamentosInput, Prisma.AntecipacaoUncheckedUpdateWithoutFechamentosInput>
}

export type AntecipacaoUpdateWithoutFechamentosInput = {
  uuid?: Prisma.StringFieldUpdateOperationsInput | string
  valorSolicitado?: Prisma.FloatFieldUpdateOperationsInput | number
  valorAprovado?: Prisma.NullableFloatFieldUpdateOperationsInput | number | null
  percentual?: Prisma.FloatFieldUpdateOperationsInput | number
  taxaAntecipacao?: Prisma.NullableFloatFieldUpdateOperationsInput | number | null
  dataPagamentoPrevista?: Prisma.DateTimeFieldUpdateOperationsInput | Date | string
  status?: Prisma.StringFieldUpdateOperationsInput | string
  numeroCCB?: Prisma.NullableStringFieldUpdateOperationsInput | string | null
  dataCCB?: Prisma.NullableDateTimeFieldUpdateOperationsInput | Date | string | null
  arquivoCCB?: Prisma.NullableStringFieldUpdateOperationsInput | string | null
  termoAssinado?: Prisma.BoolFieldUpdateOperationsInput | boolean
  dataAssinaturaTermo?: Prisma.NullableDateTimeFieldUpdateOperationsInput | Date | string | null
  hashAssinatura?: Prisma.NullableStringFieldUpdateOperationsInput | string | null
  ipAssinatura?: Prisma.NullableStringFieldUpdateOperationsInput | string | null
  observacoes?: Prisma.NullableStringFieldUpdateOperationsInput | string | null
  metaData?: Prisma.NullableJsonNullValueInput | runtime.InputJsonValue
  createdAt?: Prisma.DateTimeFieldUpdateOperationsInput | Date | string
  updatedAt?: Prisma.DateTimeFieldUpdateOperationsInput | Date | string
  deletedAt?: Prisma.NullableDateTimeFieldUpdateOperationsInput | Date | string | null
  plantao?: Prisma.PlantaoUpdateOneRequiredWithoutAntecipacoesNestedInput
  profissional?: Prisma.ProfissionalUpdateOneRequiredWithoutAntecipacoesNestedInput
  historico?: Prisma.AntecipacaoHistoricoUpdateManyWithoutAntecipacaoNestedInput
}

export type AntecipacaoUncheckedUpdateWithoutFechamentosInput = {
  id?: Prisma.IntFieldUpdateOperationsInput | number
  uuid?: Prisma.StringFieldUpdateOperationsInput | string
  plantaoId?: Prisma.IntFieldUpdateOperationsInput | number
  profissionalId?: Prisma.IntFieldUpdateOperationsInput | number
  valorSolicitado?: Prisma.FloatFieldUpdateOperationsInput | number
  valorAprovado?: Prisma.NullableFloatFieldUpdateOperationsInput | number | null
  percentual?: Prisma.FloatFieldUpdateOperationsInput | number
  taxaAntecipacao?: Prisma.NullableFloatFieldUpdateOperationsInput | number | null
  dataPagamentoPrevista?: Prisma.DateTimeFieldUpdateOperationsInput | Date | string
  status?: Prisma.StringFieldUpdateOperationsInput | string
  numeroCCB?: Prisma.NullableStringFieldUpdateOperationsInput | string | null
  dataCCB?: Prisma.NullableDateTimeFieldUpdateOperationsInput | Date | string | null
  arquivoCCB?: Prisma.NullableStringFieldUpdateOperationsInput | string | null
  termoAssinado?: Prisma.BoolFieldUpdateOperationsInput | boolean
  dataAssinaturaTermo?: Prisma.NullableDateTimeFieldUpdateOperationsInput | Date | string | null
  hashAssinatura?: Prisma.NullableStringFieldUpdateOperationsInput | string | null
  ipAssinatura?: Prisma.NullableStringFieldUpdateOperationsInput | string | null
  observacoes?: Prisma.NullableStringFieldUpdateOperationsInput | string | null
  metaData?: Prisma.NullableJsonNullValueInput | runtime.InputJsonValue
  createdAt?: Prisma.DateTimeFieldUpdateOperationsInput | Date | string
  updatedAt?: Prisma.DateTimeFieldUpdateOperationsInput | Date | string
  deletedAt?: Prisma.NullableDateTimeFieldUpdateOperationsInput | Date | string | null
  historico?: Prisma.AntecipacaoHistoricoUncheckedUpdateManyWithoutAntecipacaoNestedInput
}

export type AntecipacaoCreateWithoutHistoricoInput = {
  uuid?: string
  valorSolicitado: number
  valorAprovado?: number | null
  percentual: number
  taxaAntecipacao?: number | null
  dataPagamentoPrevista: Date | string
  status?: string
  numeroCCB?: string | null
  dataCCB?: Date | string | null
  arquivoCCB?: string | null
  termoAssinado?: boolean
  dataAssinaturaTermo?: Date | string | null
  hashAssinatura?: string | null
  ipAssinatura?: string | null
  observacoes?: string | null
  metaData?: Prisma.NullableJsonNullValueInput | runtime.InputJsonValue
  createdAt?: Date | string
  updatedAt?: Date | string
  deletedAt?: Date | string | null
  plantao: Prisma.PlantaoCreateNestedOneWithoutAntecipacoesInput
  profissional: Prisma.ProfissionalCreateNestedOneWithoutAntecipacoesInput
  fechamentos?: Prisma.FechamentoCreateNestedManyWithoutAntecipacaoInput
}

export type AntecipacaoUncheckedCreateWithoutHistoricoInput = {
  id?: number
  uuid?: string
  plantaoId: number
  profissionalId: number
  valorSolicitado: number
  valorAprovado?: number | null
  percentual: number
  taxaAntecipacao?: number | null
  dataPagamentoPrevista: Date | string
  status?: string
  numeroCCB?: string | null
  dataCCB?: Date | string | null
  arquivoCCB?: string | null
  termoAssinado?: boolean
  dataAssinaturaTermo?: Date | string | null
  hashAssinatura?: string | null
  ipAssinatura?: string | null
  observacoes?: string | null
  metaData?: Prisma.NullableJsonNullValueInput | runtime.InputJsonValue
  createdAt?: Date | string
  updatedAt?: Date | string
  deletedAt?: Date | string | null
  fechamentos?: Prisma.FechamentoUncheckedCreateNestedManyWithoutAntecipacaoInput
}

export type AntecipacaoCreateOrConnectWithoutHistoricoInput = {
  where: Prisma.AntecipacaoWhereUniqueInput
  create: Prisma.XOR<Prisma.AntecipacaoCreateWithoutHistoricoInput, Prisma.AntecipacaoUncheckedCreateWithoutHistoricoInput>
}

export type AntecipacaoUpsertWithoutHistoricoInput = {
  update: Prisma.XOR<Prisma.AntecipacaoUpdateWithoutHistoricoInput, Prisma.AntecipacaoUncheckedUpdateWithoutHistoricoInput>
  create: Prisma.XOR<Prisma.AntecipacaoCreateWithoutHistoricoInput, Prisma.AntecipacaoUncheckedCreateWithoutHistoricoInput>
  where?: Prisma.AntecipacaoWhereInput
}

export type AntecipacaoUpdateToOneWithWhereWithoutHistoricoInput = {
  where?: Prisma.AntecipacaoWhereInput
  data: Prisma.XOR<Prisma.AntecipacaoUpdateWithoutHistoricoInput, Prisma.AntecipacaoUncheckedUpdateWithoutHistoricoInput>
}

export type AntecipacaoUpdateWithoutHistoricoInput = {
  uuid?: Prisma.StringFieldUpdateOperationsInput | string
  valorSolicitado?: Prisma.FloatFieldUpdateOperationsInput | number
  valorAprovado?: Prisma.NullableFloatFieldUpdateOperationsInput | number | null
  percentual?: Prisma.FloatFieldUpdateOperationsInput | number
  taxaAntecipacao?: Prisma.NullableFloatFieldUpdateOperationsInput | number | null
  dataPagamentoPrevista?: Prisma.DateTimeFieldUpdateOperationsInput | Date | string
  status?: Prisma.StringFieldUpdateOperationsInput | string
  numeroCCB?: Prisma.NullableStringFieldUpdateOperationsInput | string | null
  dataCCB?: Prisma.NullableDateTimeFieldUpdateOperationsInput | Date | string | null
  arquivoCCB?: Prisma.NullableStringFieldUpdateOperationsInput | string | null
  termoAssinado?: Prisma.BoolFieldUpdateOperationsInput | boolean
  dataAssinaturaTermo?: Prisma.NullableDateTimeFieldUpdateOperationsInput | Date | string | null
  hashAssinatura?: Prisma.NullableStringFieldUpdateOperationsInput | string | null
  ipAssinatura?: Prisma.NullableStringFieldUpdateOperationsInput | string | null
  observacoes?: Prisma.NullableStringFieldUpdateOperationsInput | string | null
  metaData?: Prisma.NullableJsonNullValueInput | runtime.InputJsonValue
  createdAt?: Prisma.DateTimeFieldUpdateOperationsInput | Date | string
  updatedAt?: Prisma.DateTimeFieldUpdateOperationsInput | Date | string
  deletedAt?: Prisma.NullableDateTimeFieldUpdateOperationsInput | Date | string | null
  plantao?: Prisma.PlantaoUpdateOneRequiredWithoutAntecipacoesNestedInput
  profissional?: Prisma.ProfissionalUpdateOneRequiredWithoutAntecipacoesNestedInput
  fechamentos?: Prisma.FechamentoUpdateManyWithoutAntecipacaoNestedInput
}

export type AntecipacaoUncheckedUpdateWithoutHistoricoInput = {
  id?: Prisma.IntFieldUpdateOperationsInput | number
  uuid?: Prisma.StringFieldUpdateOperationsInput | string
  plantaoId?: Prisma.IntFieldUpdateOperationsInput | number
  profissionalId?: Prisma.IntFieldUpdateOperationsInput | number
  valorSolicitado?: Prisma.FloatFieldUpdateOperationsInput | number
  valorAprovado?: Prisma.NullableFloatFieldUpdateOperationsInput | number | null
  percentual?: Prisma.FloatFieldUpdateOperationsInput | number
  taxaAntecipacao?: Prisma.NullableFloatFieldUpdateOperationsInput | number | null
  dataPagamentoPrevista?: Prisma.DateTimeFieldUpdateOperationsInput | Date | string
  status?: Prisma.StringFieldUpdateOperationsInput | string
  numeroCCB?: Prisma.NullableStringFieldUpdateOperationsInput | string | null
  dataCCB?: Prisma.NullableDateTimeFieldUpdateOperationsInput | Date | string | null
  arquivoCCB?: Prisma.NullableStringFieldUpdateOperationsInput | string | null
  termoAssinado?: Prisma.BoolFieldUpdateOperationsInput | boolean
  dataAssinaturaTermo?: Prisma.NullableDateTimeFieldUpdateOperationsInput | Date | string | null
  hashAssinatura?: Prisma.NullableStringFieldUpdateOperationsInput | string | null
  ipAssinatura?: Prisma.NullableStringFieldUpdateOperationsInput | string | null
  observacoes?: Prisma.NullableStringFieldUpdateOperationsInput | string | null
  metaData?: Prisma.NullableJsonNullValueInput | runtime.InputJsonValue
  createdAt?: Prisma.DateTimeFieldUpdateOperationsInput | Date | string
  updatedAt?: Prisma.DateTimeFieldUpdateOperationsInput | Date | string
  deletedAt?: Prisma.NullableDateTimeFieldUpdateOperationsInput | Date | string | null
  fechamentos?: Prisma.FechamentoUncheckedUpdateManyWithoutAntecipacaoNestedInput
}

export type AntecipacaoCreateManyProfissionalInput = {
  id?: number
  uuid?: string
  plantaoId: number
  valorSolicitado: number
  valorAprovado?: number | null
  percentual: number
  taxaAntecipacao?: number | null
  dataPagamentoPrevista: Date | string
  status?: string
  numeroCCB?: string | null
  dataCCB?: Date | string | null
  arquivoCCB?: string | null
  termoAssinado?: boolean
  dataAssinaturaTermo?: Date | string | null
  hashAssinatura?: string | null
  ipAssinatura?: string | null
  observacoes?: string | null
  metaData?: Prisma.NullableJsonNullValueInput | runtime.InputJsonValue
  createdAt?: Date | string
  updatedAt?: Date | string
  deletedAt?: Date | string | null
}

export type AntecipacaoUpdateWithoutProfissionalInput = {
  uuid?: Prisma.StringFieldUpdateOperationsInput | string
  valorSolicitado?: Prisma.FloatFieldUpdateOperationsInput | number
  valorAprovado?: Prisma.NullableFloatFieldUpdateOperationsInput | number | null
  percentual?: Prisma.FloatFieldUpdateOperationsInput | number
  taxaAntecipacao?: Prisma.NullableFloatFieldUpdateOperationsInput | number | null
  dataPagamentoPrevista?: Prisma.DateTimeFieldUpdateOperationsInput | Date | string
  status?: Prisma.StringFieldUpdateOperationsInput | string
  numeroCCB?: Prisma.NullableStringFieldUpdateOperationsInput | string | null
  dataCCB?: Prisma.NullableDateTimeFieldUpdateOperationsInput | Date | string | null
  arquivoCCB?: Prisma.NullableStringFieldUpdateOperationsInput | string | null
  termoAssinado?: Prisma.BoolFieldUpdateOperationsInput | boolean
  dataAssinaturaTermo?: Prisma.NullableDateTimeFieldUpdateOperationsInput | Date | string | null
  hashAssinatura?: Prisma.NullableStringFieldUpdateOperationsInput | string | null
  ipAssinatura?: Prisma.NullableStringFieldUpdateOperationsInput | string | null
  observacoes?: Prisma.NullableStringFieldUpdateOperationsInput | string | null
  metaData?: Prisma.NullableJsonNullValueInput | runtime.InputJsonValue
  createdAt?: Prisma.DateTimeFieldUpdateOperationsInput | Date | string
  updatedAt?: Prisma.DateTimeFieldUpdateOperationsInput | Date | string
  deletedAt?: Prisma.NullableDateTimeFieldUpdateOperationsInput | Date | string | null
  plantao?: Prisma.PlantaoUpdateOneRequiredWithoutAntecipacoesNestedInput
  fechamentos?: Prisma.FechamentoUpdateManyWithoutAntecipacaoNestedInput
  historico?: Prisma.AntecipacaoHistoricoUpdateManyWithoutAntecipacaoNestedInput
}

export type AntecipacaoUncheckedUpdateWithoutProfissionalInput = {
  id?: Prisma.IntFieldUpdateOperationsInput | number
  uuid?: Prisma.StringFieldUpdateOperationsInput | string
  plantaoId?: Prisma.IntFieldUpdateOperationsInput | number
  valorSolicitado?: Prisma.FloatFieldUpdateOperationsInput | number
  valorAprovado?: Prisma.NullableFloatFieldUpdateOperationsInput | number | null
  percentual?: Prisma.FloatFieldUpdateOperationsInput | number
  taxaAntecipacao?: Prisma.NullableFloatFieldUpdateOperationsInput | number | null
  dataPagamentoPrevista?: Prisma.DateTimeFieldUpdateOperationsInput | Date | string
  status?: Prisma.StringFieldUpdateOperationsInput | string
  numeroCCB?: Prisma.NullableStringFieldUpdateOperationsInput | string | null
  dataCCB?: Prisma.NullableDateTimeFieldUpdateOperationsInput | Date | string | null
  arquivoCCB?: Prisma.NullableStringFieldUpdateOperationsInput | string | null
  termoAssinado?: Prisma.BoolFieldUpdateOperationsInput | boolean
  dataAssinaturaTermo?: Prisma.NullableDateTimeFieldUpdateOperationsInput | Date | string | null
  hashAssinatura?: Prisma.NullableStringFieldUpdateOperationsInput | string | null
  ipAssinatura?: Prisma.NullableStringFieldUpdateOperationsInput | string | null
  observacoes?: Prisma.NullableStringFieldUpdateOperationsInput | string | null
  metaData?: Prisma.NullableJsonNullValueInput | runtime.InputJsonValue
  createdAt?: Prisma.DateTimeFieldUpdateOperationsInput | Date | string
  updatedAt?: Prisma.DateTimeFieldUpdateOperationsInput | Date | string
  deletedAt?: Prisma.NullableDateTimeFieldUpdateOperationsInput | Date | string | null
  fechamentos?: Prisma.FechamentoUncheckedUpdateManyWithoutAntecipacaoNestedInput
  historico?: Prisma.AntecipacaoHistoricoUncheckedUpdateManyWithoutAntecipacaoNestedInput
}

export type AntecipacaoUncheckedUpdateManyWithoutProfissionalInput = {
  id?: Prisma.IntFieldUpdateOperationsInput | number
  uuid?: Prisma.StringFieldUpdateOperationsInput | string
  plantaoId?: Prisma.IntFieldUpdateOperationsInput | number
  valorSolicitado?: Prisma.FloatFieldUpdateOperationsInput | number
  valorAprovado?: Prisma.NullableFloatFieldUpdateOperationsInput | number | null
  percentual?: Prisma.FloatFieldUpdateOperationsInput | number
  taxaAntecipacao?: Prisma.NullableFloatFieldUpdateOperationsInput | number | null
  dataPagamentoPrevista?: Prisma.DateTimeFieldUpdateOperationsInput | Date | string
  status?: Prisma.StringFieldUpdateOperationsInput | string
  numeroCCB?: Prisma.NullableStringFieldUpdateOperationsInput | string | null
  dataCCB?: Prisma.NullableDateTimeFieldUpdateOperationsInput | Date | string | null
  arquivoCCB?: Prisma.NullableStringFieldUpdateOperationsInput | string | null
  termoAssinado?: Prisma.BoolFieldUpdateOperationsInput | boolean
  dataAssinaturaTermo?: Prisma.NullableDateTimeFieldUpdateOperationsInput | Date | string | null
  hashAssinatura?: Prisma.NullableStringFieldUpdateOperationsInput | string | null
  ipAssinatura?: Prisma.NullableStringFieldUpdateOperationsInput | string | null
  observacoes?: Prisma.NullableStringFieldUpdateOperationsInput | string | null
  metaData?: Prisma.NullableJsonNullValueInput | runtime.InputJsonValue
  createdAt?: Prisma.DateTimeFieldUpdateOperationsInput | Date | string
  updatedAt?: Prisma.DateTimeFieldUpdateOperationsInput | Date | string
  deletedAt?: Prisma.NullableDateTimeFieldUpdateOperationsInput | Date | string | null
}

export type AntecipacaoCreateManyPlantaoInput = {
  id?: number
  uuid?: string
  profissionalId: number
  valorSolicitado: number
  valorAprovado?: number | null
  percentual: number
  taxaAntecipacao?: number | null
  dataPagamentoPrevista: Date | string
  status?: string
  numeroCCB?: string | null
  dataCCB?: Date | string | null
  arquivoCCB?: string | null
  termoAssinado?: boolean
  dataAssinaturaTermo?: Date | string | null
  hashAssinatura?: string | null
  ipAssinatura?: string | null
  observacoes?: string | null
  metaData?: Prisma.NullableJsonNullValueInput | runtime.InputJsonValue
  createdAt?: Date | string
  updatedAt?: Date | string
  deletedAt?: Date | string | null
}

export type AntecipacaoUpdateWithoutPlantaoInput = {
  uuid?: Prisma.StringFieldUpdateOperationsInput | string
  valorSolicitado?: Prisma.FloatFieldUpdateOperationsInput | number
  valorAprovado?: Prisma.NullableFloatFieldUpdateOperationsInput | number | null
  percentual?: Prisma.FloatFieldUpdateOperationsInput | number
  taxaAntecipacao?: Prisma.NullableFloatFieldUpdateOperationsInput | number | null
  dataPagamentoPrevista?: Prisma.DateTimeFieldUpdateOperationsInput | Date | string
  status?: Prisma.StringFieldUpdateOperationsInput | string
  numeroCCB?: Prisma.NullableStringFieldUpdateOperationsInput | string | null
  dataCCB?: Prisma.NullableDateTimeFieldUpdateOperationsInput | Date | string | null
  arquivoCCB?: Prisma.NullableStringFieldUpdateOperationsInput | string | null
  termoAssinado?: Prisma.BoolFieldUpdateOperationsInput | boolean
  dataAssinaturaTermo?: Prisma.NullableDateTimeFieldUpdateOperationsInput | Date | string | null
  hashAssinatura?: Prisma.NullableStringFieldUpdateOperationsInput | string | null
  ipAssinatura?: Prisma.NullableStringFieldUpdateOperationsInput | string | null
  observacoes?: Prisma.NullableStringFieldUpdateOperationsInput | string | null
  metaData?: Prisma.NullableJsonNullValueInput | runtime.InputJsonValue
  createdAt?: Prisma.DateTimeFieldUpdateOperationsInput | Date | string
  updatedAt?: Prisma.DateTimeFieldUpdateOperationsInput | Date | string
  deletedAt?: Prisma.NullableDateTimeFieldUpdateOperationsInput | Date | string | null
  profissional?: Prisma.ProfissionalUpdateOneRequiredWithoutAntecipacoesNestedInput
  fechamentos?: Prisma.FechamentoUpdateManyWithoutAntecipacaoNestedInput
  historico?: Prisma.AntecipacaoHistoricoUpdateManyWithoutAntecipacaoNestedInput
}

export type AntecipacaoUncheckedUpdateWithoutPlantaoInput = {
  id?: Prisma.IntFieldUpdateOperationsInput | number
  uuid?: Prisma.StringFieldUpdateOperationsInput | string
  profissionalId?: Prisma.IntFieldUpdateOperationsInput | number
  valorSolicitado?: Prisma.FloatFieldUpdateOperationsInput | number
  valorAprovado?: Prisma.NullableFloatFieldUpdateOperationsInput | number | null
  percentual?: Prisma.FloatFieldUpdateOperationsInput | number
  taxaAntecipacao?: Prisma.NullableFloatFieldUpdateOperationsInput | number | null
  dataPagamentoPrevista?: Prisma.DateTimeFieldUpdateOperationsInput | Date | string
  status?: Prisma.StringFieldUpdateOperationsInput | string
  numeroCCB?: Prisma.NullableStringFieldUpdateOperationsInput | string | null
  dataCCB?: Prisma.NullableDateTimeFieldUpdateOperationsInput | Date | string | null
  arquivoCCB?: Prisma.NullableStringFieldUpdateOperationsInput | string | null
  termoAssinado?: Prisma.BoolFieldUpdateOperationsInput | boolean
  dataAssinaturaTermo?: Prisma.NullableDateTimeFieldUpdateOperationsInput | Date | string | null
  hashAssinatura?: Prisma.NullableStringFieldUpdateOperationsInput | string | null
  ipAssinatura?: Prisma.NullableStringFieldUpdateOperationsInput | string | null
  observacoes?: Prisma.NullableStringFieldUpdateOperationsInput | string | null
  metaData?: Prisma.NullableJsonNullValueInput | runtime.InputJsonValue
  createdAt?: Prisma.DateTimeFieldUpdateOperationsInput | Date | string
  updatedAt?: Prisma.DateTimeFieldUpdateOperationsInput | Date | string
  deletedAt?: Prisma.NullableDateTimeFieldUpdateOperationsInput | Date | string | null
  fechamentos?: Prisma.FechamentoUncheckedUpdateManyWithoutAntecipacaoNestedInput
  historico?: Prisma.AntecipacaoHistoricoUncheckedUpdateManyWithoutAntecipacaoNestedInput
}

export type AntecipacaoUncheckedUpdateManyWithoutPlantaoInput = {
  id?: Prisma.IntFieldUpdateOperationsInput | number
  uuid?: Prisma.StringFieldUpdateOperationsInput | string
  profissionalId?: Prisma.IntFieldUpdateOperationsInput | number
  valorSolicitado?: Prisma.FloatFieldUpdateOperationsInput | number
  valorAprovado?: Prisma.NullableFloatFieldUpdateOperationsInput | number | null
  percentual?: Prisma.FloatFieldUpdateOperationsInput | number
  taxaAntecipacao?: Prisma.NullableFloatFieldUpdateOperationsInput | number | null
  dataPagamentoPrevista?: Prisma.DateTimeFieldUpdateOperationsInput | Date | string
  status?: Prisma.StringFieldUpdateOperationsInput | string
  numeroCCB?: Prisma.NullableStringFieldUpdateOperationsInput | string | null
  dataCCB?: Prisma.NullableDateTimeFieldUpdateOperationsInput | Date | string | null
  arquivoCCB?: Prisma.NullableStringFieldUpdateOperationsInput | string | null
  termoAssinado?: Prisma.BoolFieldUpdateOperationsInput | boolean
  dataAssinaturaTermo?: Prisma.NullableDateTimeFieldUpdateOperationsInput | Date | string | null
  hashAssinatura?: Prisma.NullableStringFieldUpdateOperationsInput | string | null
  ipAssinatura?: Prisma.NullableStringFieldUpdateOperationsInput | string | null
  observacoes?: Prisma.NullableStringFieldUpdateOperationsInput | string | null
  metaData?: Prisma.NullableJsonNullValueInput | runtime.InputJsonValue
  createdAt?: Prisma.DateTimeFieldUpdateOperationsInput | Date | string
  updatedAt?: Prisma.DateTimeFieldUpdateOperationsInput | Date | string
  deletedAt?: Prisma.NullableDateTimeFieldUpdateOperationsInput | Date | string | null
}


/**
 * Count Type AntecipacaoCountOutputType
 */

export type AntecipacaoCountOutputType = {
  fechamentos: number
  historico: number
}

export type AntecipacaoCountOutputTypeSelect<ExtArgs extends runtime.Types.Extensions.InternalArgs = runtime.Types.Extensions.DefaultArgs> = {
  fechamentos?: boolean | AntecipacaoCountOutputTypeCountFechamentosArgs
  historico?: boolean | AntecipacaoCountOutputTypeCountHistoricoArgs
}

/**
 * AntecipacaoCountOutputType without action
 */
export type AntecipacaoCountOutputTypeDefaultArgs<ExtArgs extends runtime.Types.Extensions.InternalArgs = runtime.Types.Extensions.DefaultArgs> = {
  /**
   * Select specific fields to fetch from the AntecipacaoCountOutputType
   */
  select?: Prisma.AntecipacaoCountOutputTypeSelect<ExtArgs> | null
}

/**
 * AntecipacaoCountOutputType without action
 */
export type AntecipacaoCountOutputTypeCountFechamentosArgs<ExtArgs extends runtime.Types.Extensions.InternalArgs = runtime.Types.Extensions.DefaultArgs> = {
  where?: Prisma.FechamentoWhereInput
}

/**
 * AntecipacaoCountOutputType without action
 */
export type AntecipacaoCountOutputTypeCountHistoricoArgs<ExtArgs extends runtime.Types.Extensions.InternalArgs = runtime.Types.Extensions.DefaultArgs> = {
  where?: Prisma.AntecipacaoHistoricoWhereInput
}


export type AntecipacaoSelect<ExtArgs extends runtime.Types.Extensions.InternalArgs = runtime.Types.Extensions.DefaultArgs> = runtime.Types.Extensions.GetSelect<{
  id?: boolean
  uuid?: boolean
  plantaoId?: boolean
  profissionalId?: boolean
  valorSolicitado?: boolean
  valorAprovado?: boolean
  percentual?: boolean
  taxaAntecipacao?: boolean
  dataPagamentoPrevista?: boolean
  status?: boolean
  numeroCCB?: boolean
  dataCCB?: boolean
  arquivoCCB?: boolean
  termoAssinado?: boolean
  dataAssinaturaTermo?: boolean
  hashAssinatura?: boolean
  ipAssinatura?: boolean
  observacoes?: boolean
  metaData?: boolean
  createdAt?: boolean
  updatedAt?: boolean
  deletedAt?: boolean
  plantao?: boolean | Prisma.PlantaoDefaultArgs<ExtArgs>
  profissional?: boolean | Prisma.ProfissionalDefaultArgs<ExtArgs>
  fechamentos?: boolean | Prisma.Antecipacao$fechamentosArgs<ExtArgs>
  historico?: boolean | Prisma.Antecipacao$historicoArgs<ExtArgs>
  _count?: boolean | Prisma.AntecipacaoCountOutputTypeDefaultArgs<ExtArgs>
}, ExtArgs["result"]["antecipacao"]>



export type AntecipacaoSelectScalar = {
  id?: boolean
  uuid?: boolean
  plantaoId?: boolean
  profissionalId?: boolean
  valorSolicitado?: boolean
  valorAprovado?: boolean
  percentual?: boolean
  taxaAntecipacao?: boolean
  dataPagamentoPrevista?: boolean
  status?: boolean
  numeroCCB?: boolean
  dataCCB?: boolean
  arquivoCCB?: boolean
  termoAssinado?: boolean
  dataAssinaturaTermo?: boolean
  hashAssinatura?: boolean
  ipAssinatura?: boolean
  observacoes?: boolean
  metaData?: boolean
  createdAt?: boolean
  updatedAt?: boolean
  deletedAt?: boolean
}

export type AntecipacaoOmit<ExtArgs extends runtime.Types.Extensions.InternalArgs = runtime.Types.Extensions.DefaultArgs> = runtime.Types.Extensions.GetOmit<"id" | "uuid" | "plantaoId" | "profissionalId" | "valorSolicitado" | "valorAprovado" | "percentual" | "taxaAntecipacao" | "dataPagamentoPrevista" | "status" | "numeroCCB" | "dataCCB" | "arquivoCCB" | "termoAssinado" | "dataAssinaturaTermo" | "hashAssinatura" | "ipAssinatura" | "observacoes" | "metaData" | "createdAt" | "updatedAt" | "deletedAt", ExtArgs["result"]["antecipacao"]>
export type AntecipacaoInclude<ExtArgs extends runtime.Types.Extensions.InternalArgs = runtime.Types.Extensions.DefaultArgs> = {
  plantao?: boolean | Prisma.PlantaoDefaultArgs<ExtArgs>
  profissional?: boolean | Prisma.ProfissionalDefaultArgs<ExtArgs>
  fechamentos?: boolean | Prisma.Antecipacao$fechamentosArgs<ExtArgs>
  historico?: boolean | Prisma.Antecipacao$historicoArgs<ExtArgs>
  _count?: boolean | Prisma.AntecipacaoCountOutputTypeDefaultArgs<ExtArgs>
}

export type $AntecipacaoPayload<ExtArgs extends runtime.Types.Extensions.InternalArgs = runtime.Types.Extensions.DefaultArgs> = {
  name: "Antecipacao"
  objects: {
    plantao: Prisma.$PlantaoPayload<ExtArgs>
    profissional: Prisma.$ProfissionalPayload<ExtArgs>
    fechamentos: Prisma.$FechamentoPayload<ExtArgs>[]
    historico: Prisma.$AntecipacaoHistoricoPayload<ExtArgs>[]
  }
  scalars: runtime.Types.Extensions.GetPayloadResult<{
    id: number
    uuid: string
    plantaoId: number
    profissionalId: number
    valorSolicitado: number
    valorAprovado: number | null
    percentual: number
    taxaAntecipacao: number | null
    dataPagamentoPrevista: Date
    status: string
    numeroCCB: string | null
    dataCCB: Date | null
    arquivoCCB: string | null
    termoAssinado: boolean
    dataAssinaturaTermo: Date | null
    hashAssinatura: string | null
    ipAssinatura: string | null
    observacoes: string | null
    metaData: runtime.JsonValue | null
    createdAt: Date
    updatedAt: Date
    deletedAt: Date | null
  }, ExtArgs["result"]["antecipacao"]>
  composites: {}
}

export type AntecipacaoGetPayload<S extends boolean | null | undefined | AntecipacaoDefaultArgs> = runtime.Types.Result.GetResult<Prisma.$AntecipacaoPayload, S>

export type AntecipacaoCountArgs<ExtArgs extends runtime.Types.Extensions.InternalArgs = runtime.Types.Extensions.DefaultArgs> =
  Omit<AntecipacaoFindManyArgs, 'select' | 'include' | 'distinct' | 'omit'> & {
    select?: AntecipacaoCountAggregateInputType | true
  }

export interface AntecipacaoDelegate<ExtArgs extends runtime.Types.Extensions.InternalArgs = runtime.Types.Extensions.DefaultArgs, GlobalOmitOptions = {}> {
  [K: symbol]: { types: Prisma.TypeMap<ExtArgs>['model']['Antecipacao'], meta: { name: 'Antecipacao' } }
  /**
   * Find zero or one Antecipacao that matches the filter.
   * @param {AntecipacaoFindUniqueArgs} args - Arguments to find a Antecipacao
   * @example
   * // Get one Antecipacao
   * const antecipacao = await prisma.antecipacao.findUnique({
   *   where: {
   *     // ... provide filter here
   *   }
   * })
   */
  findUnique<T extends AntecipacaoFindUniqueArgs>(args: Prisma.SelectSubset<T, AntecipacaoFindUniqueArgs<ExtArgs>>): Prisma.Prisma__AntecipacaoClient<runtime.Types.Result.GetResult<Prisma.$AntecipacaoPayload<ExtArgs>, T, "findUnique", GlobalOmitOptions> | null, null, ExtArgs, GlobalOmitOptions>

  /**
   * Find one Antecipacao that matches the filter or throw an error with `error.code='P2025'`
   * if no matches were found.
   * @param {AntecipacaoFindUniqueOrThrowArgs} args - Arguments to find a Antecipacao
   * @example
   * // Get one Antecipacao
   * const antecipacao = await prisma.antecipacao.findUniqueOrThrow({
   *   where: {
   *     // ... provide filter here
   *   }
   * })
   */
  findUniqueOrThrow<T extends AntecipacaoFindUniqueOrThrowArgs>(args: Prisma.SelectSubset<T, AntecipacaoFindUniqueOrThrowArgs<ExtArgs>>): Prisma.Prisma__AntecipacaoClient<runtime.Types.Result.GetResult<Prisma.$AntecipacaoPayload<ExtArgs>, T, "findUniqueOrThrow", GlobalOmitOptions>, never, ExtArgs, GlobalOmitOptions>

  /**
   * Find the first Antecipacao that matches the filter.
   * Note, that providing `undefined` is treated as the value not being there.
   * Read more here: https://pris.ly/d/null-undefined
   * @param {AntecipacaoFindFirstArgs} args - Arguments to find a Antecipacao
   * @example
   * // Get one Antecipacao
   * const antecipacao = await prisma.antecipacao.findFirst({
   *   where: {
   *     // ... provide filter here
   *   }
   * })
   */
  findFirst<T extends AntecipacaoFindFirstArgs>(args?: Prisma.SelectSubset<T, AntecipacaoFindFirstArgs<ExtArgs>>): Prisma.Prisma__AntecipacaoClient<runtime.Types.Result.GetResult<Prisma.$AntecipacaoPayload<ExtArgs>, T, "findFirst", GlobalOmitOptions> | null, null, ExtArgs, GlobalOmitOptions>

  /**
   * Find the first Antecipacao that matches the filter or
   * throw `PrismaKnownClientError` with `P2025` code if no matches were found.
   * Note, that providing `undefined` is treated as the value not being there.
   * Read more here: https://pris.ly/d/null-undefined
   * @param {AntecipacaoFindFirstOrThrowArgs} args - Arguments to find a Antecipacao
   * @example
   * // Get one Antecipacao
   * const antecipacao = await prisma.antecipacao.findFirstOrThrow({
   *   where: {
   *     // ... provide filter here
   *   }
   * })
   */
  findFirstOrThrow<T extends AntecipacaoFindFirstOrThrowArgs>(args?: Prisma.SelectSubset<T, AntecipacaoFindFirstOrThrowArgs<ExtArgs>>): Prisma.Prisma__AntecipacaoClient<runtime.Types.Result.GetResult<Prisma.$AntecipacaoPayload<ExtArgs>, T, "findFirstOrThrow", GlobalOmitOptions>, never, ExtArgs, GlobalOmitOptions>

  /**
   * Find zero or more Antecipacaos that matches the filter.
   * Note, that providing `undefined` is treated as the value not being there.
   * Read more here: https://pris.ly/d/null-undefined
   * @param {AntecipacaoFindManyArgs} args - Arguments to filter and select certain fields only.
   * @example
   * // Get all Antecipacaos
   * const antecipacaos = await prisma.antecipacao.findMany()
   * 
   * // Get first 10 Antecipacaos
   * const antecipacaos = await prisma.antecipacao.findMany({ take: 10 })
   * 
   * // Only select the `id`
   * const antecipacaoWithIdOnly = await prisma.antecipacao.findMany({ select: { id: true } })
   * 
   */
  findMany<T extends AntecipacaoFindManyArgs>(args?: Prisma.SelectSubset<T, AntecipacaoFindManyArgs<ExtArgs>>): Prisma.PrismaPromise<runtime.Types.Result.GetResult<Prisma.$AntecipacaoPayload<ExtArgs>, T, "findMany", GlobalOmitOptions>>

  /**
   * Create a Antecipacao.
   * @param {AntecipacaoCreateArgs} args - Arguments to create a Antecipacao.
   * @example
   * // Create one Antecipacao
   * const Antecipacao = await prisma.antecipacao.create({
   *   data: {
   *     // ... data to create a Antecipacao
   *   }
   * })
   * 
   */
  create<T extends AntecipacaoCreateArgs>(args: Prisma.SelectSubset<T, AntecipacaoCreateArgs<ExtArgs>>): Prisma.Prisma__AntecipacaoClient<runtime.Types.Result.GetResult<Prisma.$AntecipacaoPayload<ExtArgs>, T, "create", GlobalOmitOptions>, never, ExtArgs, GlobalOmitOptions>

  /**
   * Create many Antecipacaos.
   * @param {AntecipacaoCreateManyArgs} args - Arguments to create many Antecipacaos.
   * @example
   * // Create many Antecipacaos
   * const antecipacao = await prisma.antecipacao.createMany({
   *   data: [
   *     // ... provide data here
   *   ]
   * })
   *     
   */
  createMany<T extends AntecipacaoCreateManyArgs>(args?: Prisma.SelectSubset<T, AntecipacaoCreateManyArgs<ExtArgs>>): Prisma.PrismaPromise<Prisma.BatchPayload>

  /**
   * Delete a Antecipacao.
   * @param {AntecipacaoDeleteArgs} args - Arguments to delete one Antecipacao.
   * @example
   * // Delete one Antecipacao
   * const Antecipacao = await prisma.antecipacao.delete({
   *   where: {
   *     // ... filter to delete one Antecipacao
   *   }
   * })
   * 
   */
  delete<T extends AntecipacaoDeleteArgs>(args: Prisma.SelectSubset<T, AntecipacaoDeleteArgs<ExtArgs>>): Prisma.Prisma__AntecipacaoClient<runtime.Types.Result.GetResult<Prisma.$AntecipacaoPayload<ExtArgs>, T, "delete", GlobalOmitOptions>, never, ExtArgs, GlobalOmitOptions>

  /**
   * Update one Antecipacao.
   * @param {AntecipacaoUpdateArgs} args - Arguments to update one Antecipacao.
   * @example
   * // Update one Antecipacao
   * const antecipacao = await prisma.antecipacao.update({
   *   where: {
   *     // ... provide filter here
   *   },
   *   data: {
   *     // ... provide data here
   *   }
   * })
   * 
   */
  update<T extends AntecipacaoUpdateArgs>(args: Prisma.SelectSubset<T, AntecipacaoUpdateArgs<ExtArgs>>): Prisma.Prisma__AntecipacaoClient<runtime.Types.Result.GetResult<Prisma.$AntecipacaoPayload<ExtArgs>, T, "update", GlobalOmitOptions>, never, ExtArgs, GlobalOmitOptions>

  /**
   * Delete zero or more Antecipacaos.
   * @param {AntecipacaoDeleteManyArgs} args - Arguments to filter Antecipacaos to delete.
   * @example
   * // Delete a few Antecipacaos
   * const { count } = await prisma.antecipacao.deleteMany({
   *   where: {
   *     // ... provide filter here
   *   }
   * })
   * 
   */
  deleteMany<T extends AntecipacaoDeleteManyArgs>(args?: Prisma.SelectSubset<T, AntecipacaoDeleteManyArgs<ExtArgs>>): Prisma.PrismaPromise<Prisma.BatchPayload>

  /**
   * Update zero or more Antecipacaos.
   * Note, that providing `undefined` is treated as the value not being there.
   * Read more here: https://pris.ly/d/null-undefined
   * @param {AntecipacaoUpdateManyArgs} args - Arguments to update one or more rows.
   * @example
   * // Update many Antecipacaos
   * const antecipacao = await prisma.antecipacao.updateMany({
   *   where: {
   *     // ... provide filter here
   *   },
   *   data: {
   *     // ... provide data here
   *   }
   * })
   * 
   */
  updateMany<T extends AntecipacaoUpdateManyArgs>(args: Prisma.SelectSubset<T, AntecipacaoUpdateManyArgs<ExtArgs>>): Prisma.PrismaPromise<Prisma.BatchPayload>

  /**
   * Create or update one Antecipacao.
   * @param {AntecipacaoUpsertArgs} args - Arguments to update or create a Antecipacao.
   * @example
   * // Update or create a Antecipacao
   * const antecipacao = await prisma.antecipacao.upsert({
   *   create: {
   *     // ... data to create a Antecipacao
   *   },
   *   update: {
   *     // ... in case it already exists, update
   *   },
   *   where: {
   *     // ... the filter for the Antecipacao we want to update
   *   }
   * })
   */
  upsert<T extends AntecipacaoUpsertArgs>(args: Prisma.SelectSubset<T, AntecipacaoUpsertArgs<ExtArgs>>): Prisma.Prisma__AntecipacaoClient<runtime.Types.Result.GetResult<Prisma.$AntecipacaoPayload<ExtArgs>, T, "upsert", GlobalOmitOptions>, never, ExtArgs, GlobalOmitOptions>


  /**
   * Count the number of Antecipacaos.
   * Note, that providing `undefined` is treated as the value not being there.
   * Read more here: https://pris.ly/d/null-undefined
   * @param {AntecipacaoCountArgs} args - Arguments to filter Antecipacaos to count.
   * @example
   * // Count the number of Antecipacaos
   * const count = await prisma.antecipacao.count({
   *   where: {
   *     // ... the filter for the Antecipacaos we want to count
   *   }
   * })
  **/
  count<T extends AntecipacaoCountArgs>(
    args?: Prisma.Subset<T, AntecipacaoCountArgs>,
  ): Prisma.PrismaPromise<
    T extends runtime.Types.Utils.Record<'select', any>
      ? T['select'] extends true
        ? number
        : Prisma.GetScalarType<T['select'], AntecipacaoCountAggregateOutputType>
      : number
  >

  /**
   * Allows you to perform aggregations operations on a Antecipacao.
   * Note, that providing `undefined` is treated as the value not being there.
   * Read more here: https://pris.ly/d/null-undefined
   * @param {AntecipacaoAggregateArgs} args - Select which aggregations you would like to apply and on what fields.
   * @example
   * // Ordered by age ascending
   * // Where email contains prisma.io
   * // Limited to the 10 users
   * const aggregations = await prisma.user.aggregate({
   *   _avg: {
   *     age: true,
   *   },
   *   where: {
   *     email: {
   *       contains: "prisma.io",
   *     },
   *   },
   *   orderBy: {
   *     age: "asc",
   *   },
   *   take: 10,
   * })
  **/
  aggregate<T extends AntecipacaoAggregateArgs>(args: Prisma.Subset<T, AntecipacaoAggregateArgs>): Prisma.PrismaPromise<GetAntecipacaoAggregateType<T>>

  /**
   * Group by Antecipacao.
   * Note, that providing `undefined` is treated as the value not being there.
   * Read more here: https://pris.ly/d/null-undefined
   * @param {AntecipacaoGroupByArgs} args - Group by arguments.
   * @example
   * // Group by city, order by createdAt, get count
   * const result = await prisma.user.groupBy({
   *   by: ['city', 'createdAt'],
   *   orderBy: {
   *     createdAt: true
   *   },
   *   _count: {
   *     _all: true
   *   },
   * })
   * 
  **/
  groupBy<
    T extends AntecipacaoGroupByArgs,
    HasSelectOrTake extends Prisma.Or<
      Prisma.Extends<'skip', Prisma.Keys<T>>,
      Prisma.Extends<'take', Prisma.Keys<T>>
    >,
    OrderByArg extends Prisma.True extends HasSelectOrTake
      ? { orderBy: AntecipacaoGroupByArgs['orderBy'] }
      : { orderBy?: AntecipacaoGroupByArgs['orderBy'] },
    OrderFields extends Prisma.ExcludeUnderscoreKeys<Prisma.Keys<Prisma.MaybeTupleToUnion<T['orderBy']>>>,
    ByFields extends Prisma.MaybeTupleToUnion<T['by']>,
    ByValid extends Prisma.Has<ByFields, OrderFields>,
    HavingFields extends Prisma.GetHavingFields<T['having']>,
    HavingValid extends Prisma.Has<ByFields, HavingFields>,
    ByEmpty extends T['by'] extends never[] ? Prisma.True : Prisma.False,
    InputErrors extends ByEmpty extends Prisma.True
    ? `Error: "by" must not be empty.`
    : HavingValid extends Prisma.False
    ? {
        [P in HavingFields]: P extends ByFields
          ? never
          : P extends string
          ? `Error: Field "${P}" used in "having" needs to be provided in "by".`
          : [
              Error,
              'Field ',
              P,
              ` in "having" needs to be provided in "by"`,
            ]
      }[HavingFields]
    : 'take' extends Prisma.Keys<T>
    ? 'orderBy' extends Prisma.Keys<T>
      ? ByValid extends Prisma.True
        ? {}
        : {
            [P in OrderFields]: P extends ByFields
              ? never
              : `Error: Field "${P}" in "orderBy" needs to be provided in "by"`
          }[OrderFields]
      : 'Error: If you provide "take", you also need to provide "orderBy"'
    : 'skip' extends Prisma.Keys<T>
    ? 'orderBy' extends Prisma.Keys<T>
      ? ByValid extends Prisma.True
        ? {}
        : {
            [P in OrderFields]: P extends ByFields
              ? never
              : `Error: Field "${P}" in "orderBy" needs to be provided in "by"`
          }[OrderFields]
      : 'Error: If you provide "skip", you also need to provide "orderBy"'
    : ByValid extends Prisma.True
    ? {}
    : {
        [P in OrderFields]: P extends ByFields
          ? never
          : `Error: Field "${P}" in "orderBy" needs to be provided in "by"`
      }[OrderFields]
  >(args: Prisma.SubsetIntersection<T, AntecipacaoGroupByArgs, OrderByArg> & InputErrors): {} extends InputErrors ? GetAntecipacaoGroupByPayload<T> : Prisma.PrismaPromise<InputErrors>
/**
 * Fields of the Antecipacao model
 */
readonly fields: AntecipacaoFieldRefs;
}

/**
 * The delegate class that acts as a "Promise-like" for Antecipacao.
 * Why is this prefixed with `Prisma__`?
 * Because we want to prevent naming conflicts as mentioned in
 * https://github.com/prisma/prisma-client-js/issues/707
 */
export interface Prisma__AntecipacaoClient<T, Null = never, ExtArgs extends runtime.Types.Extensions.InternalArgs = runtime.Types.Extensions.DefaultArgs, GlobalOmitOptions = {}> extends Prisma.PrismaPromise<T> {
  readonly [Symbol.toStringTag]: "PrismaPromise"
  plantao<T extends Prisma.PlantaoDefaultArgs<ExtArgs> = {}>(args?: Prisma.Subset<T, Prisma.PlantaoDefaultArgs<ExtArgs>>): Prisma.Prisma__PlantaoClient<runtime.Types.Result.GetResult<Prisma.$PlantaoPayload<ExtArgs>, T, "findUniqueOrThrow", GlobalOmitOptions> | Null, Null, ExtArgs, GlobalOmitOptions>
  profissional<T extends Prisma.ProfissionalDefaultArgs<ExtArgs> = {}>(args?: Prisma.Subset<T, Prisma.ProfissionalDefaultArgs<ExtArgs>>): Prisma.Prisma__ProfissionalClient<runtime.Types.Result.GetResult<Prisma.$ProfissionalPayload<ExtArgs>, T, "findUniqueOrThrow", GlobalOmitOptions> | Null, Null, ExtArgs, GlobalOmitOptions>
  fechamentos<T extends Prisma.Antecipacao$fechamentosArgs<ExtArgs> = {}>(args?: Prisma.Subset<T, Prisma.Antecipacao$fechamentosArgs<ExtArgs>>): Prisma.PrismaPromise<runtime.Types.Result.GetResult<Prisma.$FechamentoPayload<ExtArgs>, T, "findMany", GlobalOmitOptions> | Null>
  historico<T extends Prisma.Antecipacao$historicoArgs<ExtArgs> = {}>(args?: Prisma.Subset<T, Prisma.Antecipacao$historicoArgs<ExtArgs>>): Prisma.PrismaPromise<runtime.Types.Result.GetResult<Prisma.$AntecipacaoHistoricoPayload<ExtArgs>, T, "findMany", GlobalOmitOptions> | Null>
  /**
   * Attaches callbacks for the resolution and/or rejection of the Promise.
   * @param onfulfilled The callback to execute when the Promise is resolved.
   * @param onrejected The callback to execute when the Promise is rejected.
   * @returns A Promise for the completion of which ever callback is executed.
   */
  then<TResult1 = T, TResult2 = never>(onfulfilled?: ((value: T) => TResult1 | PromiseLike<TResult1>) | undefined | null, onrejected?: ((reason: any) => TResult2 | PromiseLike<TResult2>) | undefined | null): runtime.Types.Utils.JsPromise<TResult1 | TResult2>
  /**
   * Attaches a callback for only the rejection of the Promise.
   * @param onrejected The callback to execute when the Promise is rejected.
   * @returns A Promise for the completion of the callback.
   */
  catch<TResult = never>(onrejected?: ((reason: any) => TResult | PromiseLike<TResult>) | undefined | null): runtime.Types.Utils.JsPromise<T | TResult>
  /**
   * Attaches a callback that is invoked when the Promise is settled (fulfilled or rejected). The
   * resolved value cannot be modified from the callback.
   * @param onfinally The callback to execute when the Promise is settled (fulfilled or rejected).
   * @returns A Promise for the completion of the callback.
   */
  finally(onfinally?: (() => void) | undefined | null): runtime.Types.Utils.JsPromise<T>
}




/**
 * Fields of the Antecipacao model
 */
export interface AntecipacaoFieldRefs {
  readonly id: Prisma.FieldRef<"Antecipacao", 'Int'>
  readonly uuid: Prisma.FieldRef<"Antecipacao", 'String'>
  readonly plantaoId: Prisma.FieldRef<"Antecipacao", 'Int'>
  readonly profissionalId: Prisma.FieldRef<"Antecipacao", 'Int'>
  readonly valorSolicitado: Prisma.FieldRef<"Antecipacao", 'Float'>
  readonly valorAprovado: Prisma.FieldRef<"Antecipacao", 'Float'>
  readonly percentual: Prisma.FieldRef<"Antecipacao", 'Float'>
  readonly taxaAntecipacao: Prisma.FieldRef<"Antecipacao", 'Float'>
  readonly dataPagamentoPrevista: Prisma.FieldRef<"Antecipacao", 'DateTime'>
  readonly status: Prisma.FieldRef<"Antecipacao", 'String'>
  readonly numeroCCB: Prisma.FieldRef<"Antecipacao", 'String'>
  readonly dataCCB: Prisma.FieldRef<"Antecipacao", 'DateTime'>
  readonly arquivoCCB: Prisma.FieldRef<"Antecipacao", 'String'>
  readonly termoAssinado: Prisma.FieldRef<"Antecipacao", 'Boolean'>
  readonly dataAssinaturaTermo: Prisma.FieldRef<"Antecipacao", 'DateTime'>
  readonly hashAssinatura: Prisma.FieldRef<"Antecipacao", 'String'>
  readonly ipAssinatura: Prisma.FieldRef<"Antecipacao", 'String'>
  readonly observacoes: Prisma.FieldRef<"Antecipacao", 'String'>
  readonly metaData: Prisma.FieldRef<"Antecipacao", 'Json'>
  readonly createdAt: Prisma.FieldRef<"Antecipacao", 'DateTime'>
  readonly updatedAt: Prisma.FieldRef<"Antecipacao", 'DateTime'>
  readonly deletedAt: Prisma.FieldRef<"Antecipacao", 'DateTime'>
}
    

// Custom InputTypes
/**
 * Antecipacao findUnique
 */
export type AntecipacaoFindUniqueArgs<ExtArgs extends runtime.Types.Extensions.InternalArgs = runtime.Types.Extensions.DefaultArgs> = {
  /**
   * Select specific fields to fetch from the Antecipacao
   */
  select?: Prisma.AntecipacaoSelect<ExtArgs> | null
  /**
   * Omit specific fields from the Antecipacao
   */
  omit?: Prisma.AntecipacaoOmit<ExtArgs> | null
  /**
   * Choose, which related nodes to fetch as well
   */
  include?: Prisma.AntecipacaoInclude<ExtArgs> | null
  /**
   * Filter, which Antecipacao to fetch.
   */
  where: Prisma.AntecipacaoWhereUniqueInput
}

/**
 * Antecipacao findUniqueOrThrow
 */
export type AntecipacaoFindUniqueOrThrowArgs<ExtArgs extends runtime.Types.Extensions.InternalArgs = runtime.Types.Extensions.DefaultArgs> = {
  /**
   * Select specific fields to fetch from the Antecipacao
   */
  select?: Prisma.AntecipacaoSelect<ExtArgs> | null
  /**
   * Omit specific fields from the Antecipacao
   */
  omit?: Prisma.AntecipacaoOmit<ExtArgs> | null
  /**
   * Choose, which related nodes to fetch as well
   */
  include?: Prisma.AntecipacaoInclude<ExtArgs> | null
  /**
   * Filter, which Antecipacao to fetch.
   */
  where: Prisma.AntecipacaoWhereUniqueInput
}

/**
 * Antecipacao findFirst
 */
export type AntecipacaoFindFirstArgs<ExtArgs extends runtime.Types.Extensions.InternalArgs = runtime.Types.Extensions.DefaultArgs> = {
  /**
   * Select specific fields to fetch from the Antecipacao
   */
  select?: Prisma.AntecipacaoSelect<ExtArgs> | null
  /**
   * Omit specific fields from the Antecipacao
   */
  omit?: Prisma.AntecipacaoOmit<ExtArgs> | null
  /**
   * Choose, which related nodes to fetch as well
   */
  include?: Prisma.AntecipacaoInclude<ExtArgs> | null
  /**
   * Filter, which Antecipacao to fetch.
   */
  where?: Prisma.AntecipacaoWhereInput
  /**
   * {@link https://www.prisma.io/docs/concepts/components/prisma-client/sorting Sorting Docs}
   * 
   * Determine the order of Antecipacaos to fetch.
   */
  orderBy?: Prisma.AntecipacaoOrderByWithRelationInput | Prisma.AntecipacaoOrderByWithRelationInput[]
  /**
   * {@link https://www.prisma.io/docs/concepts/components/prisma-client/pagination#cursor-based-pagination Cursor Docs}
   * 
   * Sets the position for searching for Antecipacaos.
   */
  cursor?: Prisma.AntecipacaoWhereUniqueInput
  /**
   * {@link https://www.prisma.io/docs/concepts/components/prisma-client/pagination Pagination Docs}
   * 
   * Take `±n` Antecipacaos from the position of the cursor.
   */
  take?: number
  /**
   * {@link https://www.prisma.io/docs/concepts/components/prisma-client/pagination Pagination Docs}
   * 
   * Skip the first `n` Antecipacaos.
   */
  skip?: number
  /**
   * {@link https://www.prisma.io/docs/concepts/components/prisma-client/distinct Distinct Docs}
   * 
   * Filter by unique combinations of Antecipacaos.
   */
  distinct?: Prisma.AntecipacaoScalarFieldEnum | Prisma.AntecipacaoScalarFieldEnum[]
}

/**
 * Antecipacao findFirstOrThrow
 */
export type AntecipacaoFindFirstOrThrowArgs<ExtArgs extends runtime.Types.Extensions.InternalArgs = runtime.Types.Extensions.DefaultArgs> = {
  /**
   * Select specific fields to fetch from the Antecipacao
   */
  select?: Prisma.AntecipacaoSelect<ExtArgs> | null
  /**
   * Omit specific fields from the Antecipacao
   */
  omit?: Prisma.AntecipacaoOmit<ExtArgs> | null
  /**
   * Choose, which related nodes to fetch as well
   */
  include?: Prisma.AntecipacaoInclude<ExtArgs> | null
  /**
   * Filter, which Antecipacao to fetch.
   */
  where?: Prisma.AntecipacaoWhereInput
  /**
   * {@link https://www.prisma.io/docs/concepts/components/prisma-client/sorting Sorting Docs}
   * 
   * Determine the order of Antecipacaos to fetch.
   */
  orderBy?: Prisma.AntecipacaoOrderByWithRelationInput | Prisma.AntecipacaoOrderByWithRelationInput[]
  /**
   * {@link https://www.prisma.io/docs/concepts/components/prisma-client/pagination#cursor-based-pagination Cursor Docs}
   * 
   * Sets the position for searching for Antecipacaos.
   */
  cursor?: Prisma.AntecipacaoWhereUniqueInput
  /**
   * {@link https://www.prisma.io/docs/concepts/components/prisma-client/pagination Pagination Docs}
   * 
   * Take `±n` Antecipacaos from the position of the cursor.
   */
  take?: number
  /**
   * {@link https://www.prisma.io/docs/concepts/components/prisma-client/pagination Pagination Docs}
   * 
   * Skip the first `n` Antecipacaos.
   */
  skip?: number
  /**
   * {@link https://www.prisma.io/docs/concepts/components/prisma-client/distinct Distinct Docs}
   * 
   * Filter by unique combinations of Antecipacaos.
   */
  distinct?: Prisma.AntecipacaoScalarFieldEnum | Prisma.AntecipacaoScalarFieldEnum[]
}

/**
 * Antecipacao findMany
 */
export type AntecipacaoFindManyArgs<ExtArgs extends runtime.Types.Extensions.InternalArgs = runtime.Types.Extensions.DefaultArgs> = {
  /**
   * Select specific fields to fetch from the Antecipacao
   */
  select?: Prisma.AntecipacaoSelect<ExtArgs> | null
  /**
   * Omit specific fields from the Antecipacao
   */
  omit?: Prisma.AntecipacaoOmit<ExtArgs> | null
  /**
   * Choose, which related nodes to fetch as well
   */
  include?: Prisma.AntecipacaoInclude<ExtArgs> | null
  /**
   * Filter, which Antecipacaos to fetch.
   */
  where?: Prisma.AntecipacaoWhereInput
  /**
   * {@link https://www.prisma.io/docs/concepts/components/prisma-client/sorting Sorting Docs}
   * 
   * Determine the order of Antecipacaos to fetch.
   */
  orderBy?: Prisma.AntecipacaoOrderByWithRelationInput | Prisma.AntecipacaoOrderByWithRelationInput[]
  /**
   * {@link https://www.prisma.io/docs/concepts/components/prisma-client/pagination#cursor-based-pagination Cursor Docs}
   * 
   * Sets the position for listing Antecipacaos.
   */
  cursor?: Prisma.AntecipacaoWhereUniqueInput
  /**
   * {@link https://www.prisma.io/docs/concepts/components/prisma-client/pagination Pagination Docs}
   * 
   * Take `±n` Antecipacaos from the position of the cursor.
   */
  take?: number
  /**
   * {@link https://www.prisma.io/docs/concepts/components/prisma-client/pagination Pagination Docs}
   * 
   * Skip the first `n` Antecipacaos.
   */
  skip?: number
  distinct?: Prisma.AntecipacaoScalarFieldEnum | Prisma.AntecipacaoScalarFieldEnum[]
}

/**
 * Antecipacao create
 */
export type AntecipacaoCreateArgs<ExtArgs extends runtime.Types.Extensions.InternalArgs = runtime.Types.Extensions.DefaultArgs> = {
  /**
   * Select specific fields to fetch from the Antecipacao
   */
  select?: Prisma.AntecipacaoSelect<ExtArgs> | null
  /**
   * Omit specific fields from the Antecipacao
   */
  omit?: Prisma.AntecipacaoOmit<ExtArgs> | null
  /**
   * Choose, which related nodes to fetch as well
   */
  include?: Prisma.AntecipacaoInclude<ExtArgs> | null
  /**
   * The data needed to create a Antecipacao.
   */
  data: Prisma.XOR<Prisma.AntecipacaoCreateInput, Prisma.AntecipacaoUncheckedCreateInput>
}

/**
 * Antecipacao createMany
 */
export type AntecipacaoCreateManyArgs<ExtArgs extends runtime.Types.Extensions.InternalArgs = runtime.Types.Extensions.DefaultArgs> = {
  /**
   * The data used to create many Antecipacaos.
   */
  data: Prisma.AntecipacaoCreateManyInput | Prisma.AntecipacaoCreateManyInput[]
  skipDuplicates?: boolean
}

/**
 * Antecipacao update
 */
export type AntecipacaoUpdateArgs<ExtArgs extends runtime.Types.Extensions.InternalArgs = runtime.Types.Extensions.DefaultArgs> = {
  /**
   * Select specific fields to fetch from the Antecipacao
   */
  select?: Prisma.AntecipacaoSelect<ExtArgs> | null
  /**
   * Omit specific fields from the Antecipacao
   */
  omit?: Prisma.AntecipacaoOmit<ExtArgs> | null
  /**
   * Choose, which related nodes to fetch as well
   */
  include?: Prisma.AntecipacaoInclude<ExtArgs> | null
  /**
   * The data needed to update a Antecipacao.
   */
  data: Prisma.XOR<Prisma.AntecipacaoUpdateInput, Prisma.AntecipacaoUncheckedUpdateInput>
  /**
   * Choose, which Antecipacao to update.
   */
  where: Prisma.AntecipacaoWhereUniqueInput
}

/**
 * Antecipacao updateMany
 */
export type AntecipacaoUpdateManyArgs<ExtArgs extends runtime.Types.Extensions.InternalArgs = runtime.Types.Extensions.DefaultArgs> = {
  /**
   * The data used to update Antecipacaos.
   */
  data: Prisma.XOR<Prisma.AntecipacaoUpdateManyMutationInput, Prisma.AntecipacaoUncheckedUpdateManyInput>
  /**
   * Filter which Antecipacaos to update
   */
  where?: Prisma.AntecipacaoWhereInput
  /**
   * Limit how many Antecipacaos to update.
   */
  limit?: number
}

/**
 * Antecipacao upsert
 */
export type AntecipacaoUpsertArgs<ExtArgs extends runtime.Types.Extensions.InternalArgs = runtime.Types.Extensions.DefaultArgs> = {
  /**
   * Select specific fields to fetch from the Antecipacao
   */
  select?: Prisma.AntecipacaoSelect<ExtArgs> | null
  /**
   * Omit specific fields from the Antecipacao
   */
  omit?: Prisma.AntecipacaoOmit<ExtArgs> | null
  /**
   * Choose, which related nodes to fetch as well
   */
  include?: Prisma.AntecipacaoInclude<ExtArgs> | null
  /**
   * The filter to search for the Antecipacao to update in case it exists.
   */
  where: Prisma.AntecipacaoWhereUniqueInput
  /**
   * In case the Antecipacao found by the `where` argument doesn't exist, create a new Antecipacao with this data.
   */
  create: Prisma.XOR<Prisma.AntecipacaoCreateInput, Prisma.AntecipacaoUncheckedCreateInput>
  /**
   * In case the Antecipacao was found with the provided `where` argument, update it with this data.
   */
  update: Prisma.XOR<Prisma.AntecipacaoUpdateInput, Prisma.AntecipacaoUncheckedUpdateInput>
}

/**
 * Antecipacao delete
 */
export type AntecipacaoDeleteArgs<ExtArgs extends runtime.Types.Extensions.InternalArgs = runtime.Types.Extensions.DefaultArgs> = {
  /**
   * Select specific fields to fetch from the Antecipacao
   */
  select?: Prisma.AntecipacaoSelect<ExtArgs> | null
  /**
   * Omit specific fields from the Antecipacao
   */
  omit?: Prisma.AntecipacaoOmit<ExtArgs> | null
  /**
   * Choose, which related nodes to fetch as well
   */
  include?: Prisma.AntecipacaoInclude<ExtArgs> | null
  /**
   * Filter which Antecipacao to delete.
   */
  where: Prisma.AntecipacaoWhereUniqueInput
}

/**
 * Antecipacao deleteMany
 */
export type AntecipacaoDeleteManyArgs<ExtArgs extends runtime.Types.Extensions.InternalArgs = runtime.Types.Extensions.DefaultArgs> = {
  /**
   * Filter which Antecipacaos to delete
   */
  where?: Prisma.AntecipacaoWhereInput
  /**
   * Limit how many Antecipacaos to delete.
   */
  limit?: number
}

/**
 * Antecipacao.fechamentos
 */
export type Antecipacao$fechamentosArgs<ExtArgs extends runtime.Types.Extensions.InternalArgs = runtime.Types.Extensions.DefaultArgs> = {
  /**
   * Select specific fields to fetch from the Fechamento
   */
  select?: Prisma.FechamentoSelect<ExtArgs> | null
  /**
   * Omit specific fields from the Fechamento
   */
  omit?: Prisma.FechamentoOmit<ExtArgs> | null
  /**
   * Choose, which related nodes to fetch as well
   */
  include?: Prisma.FechamentoInclude<ExtArgs> | null
  where?: Prisma.FechamentoWhereInput
  orderBy?: Prisma.FechamentoOrderByWithRelationInput | Prisma.FechamentoOrderByWithRelationInput[]
  cursor?: Prisma.FechamentoWhereUniqueInput
  take?: number
  skip?: number
  distinct?: Prisma.FechamentoScalarFieldEnum | Prisma.FechamentoScalarFieldEnum[]
}

/**
 * Antecipacao.historico
 */
export type Antecipacao$historicoArgs<ExtArgs extends runtime.Types.Extensions.InternalArgs = runtime.Types.Extensions.DefaultArgs> = {
  /**
   * Select specific fields to fetch from the AntecipacaoHistorico
   */
  select?: Prisma.AntecipacaoHistoricoSelect<ExtArgs> | null
  /**
   * Omit specific fields from the AntecipacaoHistorico
   */
  omit?: Prisma.AntecipacaoHistoricoOmit<ExtArgs> | null
  /**
   * Choose, which related nodes to fetch as well
   */
  include?: Prisma.AntecipacaoHistoricoInclude<ExtArgs> | null
  where?: Prisma.AntecipacaoHistoricoWhereInput
  orderBy?: Prisma.AntecipacaoHistoricoOrderByWithRelationInput | Prisma.AntecipacaoHistoricoOrderByWithRelationInput[]
  cursor?: Prisma.AntecipacaoHistoricoWhereUniqueInput
  take?: number
  skip?: number
  distinct?: Prisma.AntecipacaoHistoricoScalarFieldEnum | Prisma.AntecipacaoHistoricoScalarFieldEnum[]
}

/**
 * Antecipacao without action
 */
export type AntecipacaoDefaultArgs<ExtArgs extends runtime.Types.Extensions.InternalArgs = runtime.Types.Extensions.DefaultArgs> = {
  /**
   * Select specific fields to fetch from the Antecipacao
   */
  select?: Prisma.AntecipacaoSelect<ExtArgs> | null
  /**
   * Omit specific fields from the Antecipacao
   */
  omit?: Prisma.AntecipacaoOmit<ExtArgs> | null
  /**
   * Choose, which related nodes to fetch as well
   */
  include?: Prisma.AntecipacaoInclude<ExtArgs> | null
}
