import { useState, useEffect, useMemo } from "react";
import { useQuery, useMutation, useQueryClient } from "@tanstack/react-query";
import { api, type PresencasAgrupadosResponse, type PresencaAgrupado, type Plantao } from "@/lib/api";
import { Skeleton } from "@/components/ui/skeleton";
import { DeleteConfirmDialog } from "@/components/ui/delete-confirm-dialog";
import { MonthNavigation } from "@/components/presencas/month-navigation";
import { PresencasTable } from "@/components/presencas/presencas-table";
import { BatchEditDialog } from "@/components/presencas/batch-edit-dialog";
import { SummaryCard } from "@/components/presencas/summary-card";
import { toast } from "sonner";
import { createLocalDate, getMonth, getYear } from "@shared/date";
import { startOfMonth, addMonths, subMonths } from "date-fns";

interface TabPresencasProps {
  plantao: Plantao;
  calendarioMes?: number;
  calendarioAno?: number;
}

interface RegistroParaEditar {
  dia: number;
  diaPlantaoId: number;
  registroId: number;
  entrada: {
    id: number;
    horario: string;
    observacao?: string;
  } | null;
  saida: {
    id: number;
    horario: string;
    observacao?: string;
  } | null;
  intervalo?: string;
  observacoes?: string;
  tempoGlosado?: number;
  justificativaGlosa?: string;
}

interface EditingData {
  horaEntrada: string;
  horaSaida: string;
  intervalo: string;
  observacao: string;
  tempoGlosado: string;
  justificativaGlosa: string;
}

export function TabPresencas({ plantao, calendarioMes, calendarioAno }: TabPresencasProps) {
  const queryClient = useQueryClient();
  const [editingNewRow, setEditingNewRow] = useState(false);
  const [newRowData, setNewRowData] = useState({
    dia: "",
    horaEntrada: "",
    horaSaida: "",
    intervalo: "01:00",
    observacao: "",
  });
  // const [batchMode, setBatchMode] = useState(false); // Removido - não mais necessário
  const [selectedRegistros, setSelectedRegistros] = useState<Set<number>>(new Set());
  const [selectAll, setSelectAll] = useState(false);
  const [deleteConfirmOpen, setDeleteConfirmOpen] = useState(false);
  const [registroToDelete, setRegistroToDelete] = useState<number | null>(null);
  const [batchEditMode, setBatchEditMode] = useState(false);
  const [batchEditData, setBatchEditData] = useState<{
    entrada?: string;
    saida?: string;
    intervalo?: string;
    observacao?: string;
  }>({});
  // Usar o mês do calendário se fornecido, senão usar o primeiro mês do período do plantão
  const dataInicial = plantao.dataInicial;
  const [currentMonth, setCurrentMonth] = useState(calendarioMes || getMonth(dataInicial) + 1);
  const [currentYear, setCurrentYear] = useState(calendarioAno || getYear(dataInicial));
  // Batch data removido - não mais necessário

  // Atualizar mês/ano quando as props do calendário mudarem
  useEffect(() => {
    if (calendarioMes && calendarioAno) {
      setCurrentMonth(calendarioMes);
      setCurrentYear(calendarioAno);
    }
  }, [calendarioMes, calendarioAno]);

  // Buscar presencas do mês selecionado via API
  const { data: presencasResponse, isLoading: isLoadingRegistros } = useQuery({
    queryKey: ["plantao-presencas", plantao.uuid, currentMonth, currentYear],
    queryFn: () =>
      api.get<PresencasAgrupadosResponse>(
        `/plantoes/${plantao.uuid}/presencas-agrupados?mes=${currentMonth}&ano=${currentYear}`
      ),
  });

  // Processar presencas do mês selecionado
  const data = useMemo(() => {
    if (!presencasResponse || !presencasResponse.presencas || presencasResponse.presencas.length === 0) return null;

    // Converter presencas agrupados para registros individuais
    const registrosIndividuais: any[] = [];
    presencasResponse.presencas.forEach((presenca: PresencaAgrupado) => {
      presenca.registros.forEach((registro: any) => {
        registrosIndividuais.push({
          registroId: registro.id,
          diaPlantaoId: presenca.diaPlantaoId,
          dia: presenca.dia,
          horaEntrada: registro.horaEntrada,
          horaSaida: registro.horaSaida,
          intervalo: registro.intervalo || "01:00",
          horasTrabalhadas: registro.horasTrabalhadas || 0,
          valorEstimado: registro.valorEstimado || 0,
          observacao: registro.observacao,
          status: registro.status || "PENDENTE",
          tempoGlosado: registro.tempoGlosado || 0,
          justificativaGlosa: registro.justificativaGlosa,
          fechamentoId: registro.fechamentoId,
        });
      });
    });

    return {
      registros: registrosIndividuais,
      estatisticas: {
        totalDiasEscalados: presencasResponse.estatisticas?.totalDiasEscalados || presencasResponse.presencas.length,
        diasComRegistroCompleto:
          presencasResponse.estatisticas?.diasComRegistroCompleto ||
          registrosIndividuais.filter((r: any) => r.horaEntrada && r.horaSaida).length,
        diasPendentes: presencasResponse.estatisticas?.diasPendentes || 0,
        totalHorasTrabalhadas:
          presencasResponse.estatisticas?.totalHorasTrabalhadas ||
          registrosIndividuais.reduce((acc: number, r: any) => acc + (r.horasTrabalhadas || 0), 0),
        valorEstimado:
          presencasResponse.estatisticas?.valorEstimado ||
          registrosIndividuais.reduce((acc: number, r: any) => acc + (r.valorEstimado || 0), 0),
        // Propriedades extras para compatibilidade
        totalRegistros: registrosIndividuais.length,
        registrosCompletos: registrosIndividuais.filter((r: any) => r.horaEntrada && r.horaSaida).length,
        registrosPendentes:
          registrosIndividuais.length - registrosIndividuais.filter((r: any) => r.horaEntrada && r.horaSaida).length,
        diasUnicos: presencasResponse.presencas.length,
        registrosComConflito: 0,
      },
    };
  }, [presencasResponse, currentMonth, currentYear, plantao]);

  const isLoading = isLoadingRegistros;
  const refetch = async () => {
    // Recarregar todas as queries relacionadas
    await queryClient.invalidateQueries({
      queryKey: ["plantao-presencas", plantao.uuid, currentMonth, currentYear],
    });
    await queryClient.invalidateQueries({ queryKey: ["plantao-presencas", plantao.uuid] });
    await queryClient.invalidateQueries({ queryKey: ["plantao", plantao.uuid] });
  };

  // Mutation para adicionar um dia específico
  const adicionarDiaEspecificoMutation = useMutation({
    mutationFn: async (data: { dia: number; mes: number; ano: number }) => {
      return api.post(`/plantoes/${plantao.uuid}/adicionar-dia`, data);
    },
    onSuccess: () => {
      toast.success("Dia adicionado com sucesso");
      // Invalidar todas as queries relacionadas
      queryClient.invalidateQueries({ queryKey: ["plantao", plantao.uuid] });
      queryClient.invalidateQueries({ queryKey: ["plantao-presencas", plantao.uuid, currentMonth, currentYear] });
      queryClient.invalidateQueries({ queryKey: ["plantao-presencas", plantao.uuid] });
    },
    onError: (error: any) => {
      toast.error(error.message || "Erro ao adicionar dia");
    },
  });

  // Mutation para adicionar múltiplos dias (para edição em lote)
  const adicionarMultiplosDiasMutation = useMutation({
    mutationFn: async () => {
      // Funcionalidade de adicionar múltiplos dias removida temporariamente
      throw new Error("Função não disponível no momento");

      // Código removido
    },
    onSuccess: (response: any) => {
      toast.success("Dias adicionados com sucesso");
      // Invalidar todas as queries relacionadas
      queryClient.invalidateQueries({ queryKey: ["plantao", plantao.uuid] });
      queryClient.invalidateQueries({ queryKey: ["plantao-presencas", plantao.uuid, currentMonth, currentYear] });
      queryClient.invalidateQueries({ queryKey: ["plantao-presencas", plantao.uuid] });

      // Removido - funcionalidade de batch não mais disponível
    },
    onError: (error: any) => {
      toast.error(error.message || "Erro ao adicionar dias");
    },
  });

  // Mutation para criar/atualizar registro
  const createOrUpdateMutation = useMutation({
    mutationFn: async (data: {
      registroId?: number;
      diaPlantaoId?: number;
      dia?: number;
      mes?: number;
      ano?: number;
      plantaoId?: string;
      horaEntrada?: string;
      horaSaida?: string;
      intervalo?: string;
      observacao?: string;
      tempoGlosado?: number;
      justificativaGlosa?: string;
    }) => {
      if (data.registroId) {
        // Atualizar registro existente
        return api.put(`/registros-ponto/${data.registroId}`, {
          diaPlantaoId: data.diaPlantaoId,
          plantaoId: data.plantaoId,
          horaEntrada: data.horaEntrada,
          horaSaida: data.horaSaida,
          intervalo: data.intervalo,
          observacao: data.observacao,
          tempoGlosado: data.tempoGlosado,
          justificativaGlosa: data.justificativaGlosa,
          dia: data.dia,
          mes: data.mes,
          ano: data.ano,
        });
      } else {
        // Criar novo registro usando dia/mes/ano
        return api.post("/registros-ponto", {
          plantaoId: data.plantaoId,
          dia: data.dia,
          mes: data.mes,
          ano: data.ano,
          horaEntrada: data.horaEntrada,
          horaSaida: data.horaSaida,
          intervalo: data.intervalo,
          observacao: data.observacao,
          tempoGlosado: data.tempoGlosado,
          justificativaGlosa: data.justificativaGlosa,
        });
      }
    },
    onSuccess: () => {
      toast.success("Registro de ponto salvo com sucesso");
      // Invalidar todas as queries relacionadas
      queryClient.invalidateQueries({ queryKey: ["plantao", plantao.uuid] });
      queryClient.invalidateQueries({ queryKey: ["plantao-presencas", plantao.uuid, currentMonth, currentYear] });
      queryClient.invalidateQueries({ queryKey: ["plantao-presencas", plantao.uuid] });
    },
    onError: (error: any) => {
      toast.error(error.message || "Erro ao salvar registro de ponto");
    },
  });

  // Mutation para deletar registro
  const deleteMutation = useMutation({
    mutationFn: async (id: number) => {
      return api.delete(`/registros-ponto/${id}`);
    },
    onSuccess: () => {
      toast.success("Registro de ponto excluído com sucesso");
      // Invalidar todas as queries relacionadas
      queryClient.invalidateQueries({ queryKey: ["plantao", plantao.uuid] });
      queryClient.invalidateQueries({ queryKey: ["plantao-presencas", plantao.uuid, currentMonth, currentYear] });
      queryClient.invalidateQueries({ queryKey: ["plantao-presencas", plantao.uuid] });
    },
    onError: (error: any) => {
      toast.error(error.message || "Erro ao excluir registro de ponto");
    },
  });

  // Mutation para batch update
  const batchMutation = useMutation({
    mutationFn: async (data: {
      registros: Array<{
        data: string;
        entrada?: string;
        saida?: string;
        intervalo?: string;
        observacao?: string;
      }>;
    }) => {
      return api.post(`/plantoes/${plantao.uuid}/registros-ponto/batch`, data);
    },
    onSuccess: () => {
      toast.success("Registros de ponto atualizados com sucesso");
      // Invalidar todas as queries relacionadas
      queryClient.invalidateQueries({ queryKey: ["plantao", plantao.uuid] });
      queryClient.invalidateQueries({ queryKey: ["plantao-presencas", plantao.uuid, currentMonth, currentYear] });
      queryClient.invalidateQueries({ queryKey: ["plantao-presencas", plantao.uuid] });
      // Limpar seleções e sair do modo de edição em lote
      setSelectedRegistros(new Set());
      setSelectAll(false);
      setBatchEditMode(false);
      setBatchEditData({});
    },
    onError: (error: any) => {
      toast.error(error.message || "Erro ao atualizar registros de ponto");
    },
  });

  // Mutation para aprovar registros em lote
  const aprovarRegistrosMutation = useMutation({
    mutationFn: async (registroIds: number[]) => {
      return api.post("/registros-ponto/aprovar-lote", { registroIds });
    },
    onSuccess: (response: any) => {
      // Verificar se houve aprovações parciais
      if (response.errors && response.errors.length > 0) {
        // Mostrar mensagem de sucesso parcial
        toast.warning(`${response.message}. Verifique os registros incompletos.`);

        // Mostrar detalhes dos erros se necessário
        response.errors.forEach((error: any) => {
          console.warn(`Registro ${error.registroId}: ${error.error}`);
        });
      } else {
        // Todos foram aprovados com sucesso
        toast.success(response.message || "Registros aprovados com sucesso");
      }

      // Invalidar todas as queries relacionadas
      queryClient.invalidateQueries({ queryKey: ["plantao", plantao.uuid] });
      queryClient.invalidateQueries({ queryKey: ["plantao-presencas", plantao.uuid, currentMonth, currentYear] });
      queryClient.invalidateQueries({ queryKey: ["plantao-presencas", plantao.uuid] });
      setSelectedRegistros(new Set());
      setSelectAll(false);
    },
    onError: (error: any) => {
      toast.error(error.message || "Erro ao aprovar registros");
    },
  });

  // Mutation para enviar registros para fechamento
  const enviarFechamentoMutation = useMutation({
    mutationFn: async (registroIds: number[]) => {
      return api.post("/registros-ponto/enviar-fechamento", {
        registroIds,
        plantaoId: plantao.uuid,
        mes: currentMonth,
        ano: currentYear,
      });
    },
    onSuccess: () => {
      toast.success("Registros enviados para fechamento");
      // Invalidar todas as queries relacionadas
      queryClient.invalidateQueries({ queryKey: ["plantao", plantao.uuid] });
      queryClient.invalidateQueries({ queryKey: ["plantao-presencas", plantao.uuid, currentMonth, currentYear] });
      queryClient.invalidateQueries({ queryKey: ["plantao-presencas", plantao.uuid] });
      setSelectedRegistros(new Set());
      setSelectAll(false);
    },
    onError: (error: any) => {
      toast.error(error.message || "Erro ao enviar para fechamento");
    },
  });

  // Mutation para presenca automático
  const presencaAutomaticoMutation = useMutation({
    mutationFn: async () => {
      return api.post(`/plantoes/${plantao.uuid}/presenca-automatico`, {
        mes: currentMonth,
        ano: currentYear,
      });
    },
    onSuccess: (response: any) => {
      toast.success(
        `Presenca automático criado! ${response.registrosCriados} novos registros, ${response.registrosAtualizados} atualizados`
      );
      // Invalidar todas as queries relacionadas
      queryClient.invalidateQueries({ queryKey: ["plantao", plantao.uuid] });
      queryClient.invalidateQueries({ queryKey: ["plantao-presencas", plantao.uuid, currentMonth, currentYear] });
      queryClient.invalidateQueries({ queryKey: ["plantao-presencas", plantao.uuid] });
    },
    onError: (error: any) => {
      toast.error(error.message || "Erro ao criar presença automática");
    },
  });

  const handleCreateOrUpdateRegistro = async (formData: FormData) => {
    const formDia = formData.get("dia") as string;
    const dia = parseInt(formDia);
    const horaEntrada = formData.get("horaEntrada") as string;
    const horaSaida = formData.get("horaSaida") as string;
    const intervalo = formData.get("intervalo") as string;
    const observacao = formData.get("observacao") as string;

    if (!formDia || !dia || isNaN(dia) || dia < 1 || dia > 31) {
      toast.error("Selecione um dia");
      return;
    }

    // Verificar se há entrada ou saída
    if (!horaEntrada && !horaSaida) {
      toast.error("Preencha pelo menos entrada ou saída");
      return;
    }

    // This function is now only used for creating new appointments
    // Editing existing appointments is handled inline in the table

    // Agora podemos criar o presenca diretamente usando dia/mês/ano
    // O backend se encarrega de criar o DiaPlantao se necessário
    createOrUpdateMutation.mutate({
      plantaoId: plantao.uuid,
      dia,
      mes: currentMonth,
      ano: currentYear,
      horaEntrada: horaEntrada || undefined,
      horaSaida: horaSaida || undefined,
      intervalo: intervalo || "01:00",
      observacao: observacao || undefined,
    });
  };

  const handleDeleteRegistro = (registroId: number) => {
    setRegistroToDelete(registroId);
    setDeleteConfirmOpen(true);
  };

  const confirmDelete = () => {
    if (registroToDelete) {
      deleteMutation.mutate(registroToDelete);
      setDeleteConfirmOpen(false);
      setRegistroToDelete(null);
    }
  };

  // Função de batch save removida

  const handleSelectAll = (checked: boolean) => {
    setSelectAll(checked);
    if (checked && data) {
      // Selecionar apenas registros que não foram enviados para fechamento
      const selectableRegistroIds = data.registros.filter((r: any) => !r.fechamentoId).map((r: any) => r.registroId);
      setSelectedRegistros(new Set(selectableRegistroIds));
    } else {
      setSelectedRegistros(new Set());
    }
  };

  const handleSelectRegistro = (registroId: number, checked: boolean) => {
    const newSelected = new Set(selectedRegistros);
    if (checked) {
      newSelected.add(registroId);
    } else {
      newSelected.delete(registroId);
      setSelectAll(false);
    }
    setSelectedRegistros(newSelected);

    // Verificar se todos os registros selecionáveis estão selecionados para marcar selectAll
    if (data) {
      const selectableRegistros = data.registros.filter((r: any) => !r.fechamentoId);
      if (newSelected.size === selectableRegistros.length && selectableRegistros.length > 0) {
        setSelectAll(true);
      } else {
        setSelectAll(false);
      }
    }
  };

  const handleAprovarSelecionados = () => {
    if (selectedRegistros.size === 0) {
      toast.error("Selecione pelo menos um registro para aprovar");
      return;
    }

    aprovarRegistrosMutation.mutate(Array.from(selectedRegistros));
  };

  const handleEnviarFechamento = () => {
    if (selectedRegistros.size === 0) {
      toast.error("Selecione pelo menos um registro para enviar ao fechamento");
      return;
    }

    enviarFechamentoMutation.mutate(Array.from(selectedRegistros));
  };

  const handleBatchSave = () => {
    if (selectedRegistros.size === 0 || !data) {
      return;
    }

    // Preparar dados para o batch update
    const registrosParaAtualizar = Array.from(selectedRegistros)
      .map((registroId) => {
        const registro = data.registros.find((r: any) => r.registroId === registroId);
        if (!registro) return null;

        return {
          data: `${currentYear}-${currentMonth.toString().padStart(2, "0")}-${registro.dia.toString().padStart(2, "0")}`,
          entrada: batchEditData.entrada || undefined,
          saida: batchEditData.saida || undefined,
          intervalo: batchEditData.intervalo || undefined,
          observacao: batchEditData.observacao || undefined,
        };
      })
      .filter((registro): registro is NonNullable<typeof registro> => registro !== null);

    if (registrosParaAtualizar.length === 0) {
      toast.error("Nenhum registro válido para atualizar");
      return;
    }

    batchMutation.mutate({
      registros: registrosParaAtualizar,
    });
  };

  const handleBatchCancel = () => {
    setBatchEditMode(false);
    setBatchEditData({});
  };

  const navigateMonth = (direction: "prev" | "next") => {
    const dataInicial = plantao.dataInicial;
    const dataFinal = plantao.dataFinal ? plantao.dataFinal : createLocalDate(2099, 12, 31);

    if (direction === "prev") {
      let newDate = subMonths(startOfMonth(createLocalDate(currentYear, currentMonth, 1)), 1);
      if (newDate >= startOfMonth(dataInicial)) {
        setCurrentMonth(newDate.getMonth() + 1);
        setCurrentYear(newDate.getFullYear());
        setSelectedRegistros(new Set());
        setSelectAll(false);
        // Limpar seleções
      }
    } else {
      let newDate = addMonths(startOfMonth(createLocalDate(currentYear, currentMonth, 1)), 1);
      if (newDate <= startOfMonth(dataFinal)) {
        setCurrentMonth(newDate.getMonth() + 1);
        setCurrentYear(newDate.getFullYear());
        setSelectedRegistros(new Set());
        setSelectAll(false);
        // Limpar seleções
      }
    }
  };

  // Função de edição em lote removida

  if (isLoading) {
    return (
      <div className="space-y-4">
        <Skeleton className="h-10 w-full" />
        <Skeleton className="h-96 w-full" />
      </div>
    );
  }

  return (
    <div className="space-y-4">
      <MonthNavigation
        plantao={plantao}
        currentMonth={currentMonth}
        currentYear={currentYear}
        selectedRegistros={selectedRegistros}
        onNavigateMonth={navigateMonth}
        onPresencaAutomatico={() => presencaAutomaticoMutation.mutate()}
        onAddRegistro={() => {
          setEditingNewRow(true);
          setNewRowData({
            dia: "",
            horaEntrada: plantao.horaInicio || "",
            horaSaida: plantao.horaFim || "",
            intervalo: plantao.intervalo || "01:00",
            observacao: "",
          });
        }}
        onAprovarSelecionados={handleAprovarSelecionados}
        onEnviarFechamento={handleEnviarFechamento}
        isPresencaAutomaticoLoading={presencaAutomaticoMutation.isPending}
        isAprovarLoading={aprovarRegistrosMutation.isPending}
        isEnviarFechamentoLoading={enviarFechamentoMutation.isPending}
        isBatchMutationLoading={batchMutation.isPending}
      />

      <PresencasTable
        data={data}
        currentMonth={currentMonth}
        currentYear={currentYear}
        selectedRegistros={selectedRegistros}
        selectAll={selectAll}
        onSelectAll={handleSelectAll}
        onSelectRegistro={handleSelectRegistro}
        onEditRegistro={(registroId) => {
          // Inline editing will be handled in the table component
        }}
        onDeleteRegistro={handleDeleteRegistro}
        onAddRegistro={() => {
          setEditingNewRow(true);
          setNewRowData({
            dia: "",
            horaEntrada: plantao.horaInicio || "",
            horaSaida: plantao.horaFim || "",
            intervalo: plantao.intervalo || "01:00",
            observacao: "",
          });
        }}
        editingNewRow={editingNewRow}
        newRowData={newRowData}
        onNewRowDataChange={setNewRowData}
        plantao={plantao}
        onSaveNewRow={async () => {
          if (!newRowData.dia) {
            toast.error("Selecione um dia");
            return;
          }

          const formData = new FormData();
          formData.set("dia", newRowData.dia);
          formData.set("horaEntrada", newRowData.horaEntrada);
          formData.set("horaSaida", newRowData.horaSaida);
          formData.set("intervalo", newRowData.intervalo);
          formData.set("observacao", newRowData.observacao);

          await handleCreateOrUpdateRegistro(formData);
          setEditingNewRow(false);
        }}
        onCancelNewRow={() => {
          setEditingNewRow(false);
          setNewRowData({
            dia: "",
            horaEntrada: "",
            horaSaida: "",
            intervalo: "01:00",
            observacao: "",
          });
        }}
        onSaveEditingRow={async (registroId, editingData) => {
          // Create form data for updating existing record
          const formData = new FormData();
          const registro = data?.registros.find((r) => r.registroId === registroId);
          if (!registro) return;

          formData.set("dia", registro.dia.toString());
          formData.set("horaEntrada", editingData.horaEntrada);
          formData.set("horaSaida", editingData.horaSaida);
          formData.set("intervalo", editingData.intervalo);
          formData.set("observacao", editingData.observacao);

          // Set the selectedPresenca to simulate editing mode
          const registroParaEditar = {
            dia: registro.dia,
            diaPlantaoId: registro.diaPlantaoId,
            registroId: registro.registroId,
            entrada: registro.horaEntrada
              ? {
                  id: registro.registroId,
                  horario: registro.horaEntrada,
                  observacao: registro.observacao,
                }
              : null,
            saida: registro.horaSaida
              ? {
                  id: registro.registroId,
                  horario: registro.horaSaida,
                  observacao: registro.observacao,
                }
              : null,
            intervalo: registro.intervalo,
            observacoes: registro.observacao,
            tempoGlosado: registro.tempoGlosado,
            justificativaGlosa: registro.justificativaGlosa,
          };

          // Temporarily set the editing state and call the existing handler
          const originalPresenca = null; // Clear any existing state

          // Parse tempo glosado from HH:MM format to decimal hours
          const parseHoursFromInput = (timeString: string) => {
            if (!timeString || timeString === "00:00") return 0;
            const [hours, minutes] = timeString.split(":").map(Number);
            return hours + minutes / 60;
          };

          // Call the mutation directly with the data
          createOrUpdateMutation.mutate({
            registroId: registro.registroId,
            diaPlantaoId: registro.diaPlantaoId,
            horaEntrada: editingData.horaEntrada || undefined,
            horaSaida: editingData.horaSaida || undefined,
            intervalo: editingData.intervalo || "01:00",
            observacao: editingData.observacao || undefined,
            tempoGlosado: parseHoursFromInput(editingData.tempoGlosado || "00:00"),
            justificativaGlosa: editingData.justificativaGlosa || undefined,
          });
        }}
      />

      <BatchEditDialog
        isOpen={batchEditMode}
        onOpenChange={setBatchEditMode}
        selectedRegistros={selectedRegistros}
        batchEditData={batchEditData}
        onBatchEditDataChange={setBatchEditData}
        onSave={handleBatchSave}
        onCancel={handleBatchCancel}
        isLoading={batchMutation.isPending}
      />

      {data && <SummaryCard data={data} />}

      {/* Dialog de confirmação de exclusão */}
      <DeleteConfirmDialog
        open={deleteConfirmOpen}
        onOpenChange={setDeleteConfirmOpen}
        onConfirm={confirmDelete}
        description="Tem certeza que deseja excluir este registro de ponto? Esta ação não pode ser desfeita."
      />
    </div>
  );
}
