import { formatCurrency } from "@shared/utils";
import { appEnv } from "../env";
import { getEmailService } from "./config";

interface AntecipacaoEmailData {
  profissionalNome: string;
  profissionalEmail: string;
  valorSolicitado: number;
  valorLiquido: number;
  percentual: number;
  taxaAntecipacao: number;
  dataPagamentoPrevista: string;
  clienteNome: string;
  localAtendimento: string;
  mes: number;
  ano: number;
}

export async function sendAntecipacaoEmail(data: AntecipacaoEmailData) {
  const service = getEmailService();

  const linkPlataforma = `${appEnv.FRONT_END_URL || "http://localhost:5173"}/login`;

  const subject = `Antecipação Aprovada - Assinatura Necessária - ${data.clienteNome}`;

  const htmlBody = /*html*/ `
    <!DOCTYPE html>
    <html>
    <head>
      <meta charset="utf-8">
      <style>
        body { font-family: Arial, sans-serif; line-height: 1.6; color: #333; }
        .container { max-width: 600px; margin: 0 auto; padding: 20px; }
        .header { background: linear-gradient(135deg, #667eea 0%, #764ba2 100%); padding: 30px; text-align: center; border-radius: 10px 10px 0 0; }
        .header h1 { color: white; margin: 0; font-size: 24px; }
        .content { padding: 30px; background: #f9f9f9; }
        .info-card { background: white; padding: 20px; border-radius: 8px; margin: 20px 0; box-shadow: 0 2px 4px rgba(0,0,0,0.1); }
        .info-row { display: flex; justify-content: space-between; padding: 10px 0; border-bottom: 1px solid #eee; }
        .info-row:last-child { border-bottom: none; }
        .info-label { color: #666; font-weight: 500; }
        .info-value { color: #333; font-weight: bold; }
        .highlight { background: #fff3cd; padding: 15px; border-left: 4px solid #ffc107; margin: 20px 0; border-radius: 4px; }
        .btn-container { text-align: center; margin: 30px 0; }
        .btn-primary { 
          display: inline-block; 
          padding: 15px 40px; 
          background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
          color: white; 
          text-decoration: none; 
          border-radius: 50px; 
          font-weight: bold;
          font-size: 16px;
          box-shadow: 0 4px 15px rgba(0,0,0,0.2);
        }
        .footer { background: #f4f4f4; padding: 20px; text-align: center; font-size: 12px; color: #666; border-radius: 0 0 10px 10px; }
        .warning { color: #dc3545; font-weight: bold; }
      </style>
    </head>
    <body>
      <div class="container">
        <div class="header">
          <h1>✅ Antecipação Aprovada - Assinatura Necessária</h1>
        </div>
        
        <div class="content">
          <p>Olá <strong>${data.profissionalNome}</strong>,</p>
          
          <p>Sua solicitação de antecipação foi <strong>aprovada</strong>! Para finalizar o processo, é necessário assinar digitalmente o termo de cessão de direitos. Confira os detalhes abaixo:</p>
          
          <div class="info-card">
            <h3 style="margin-top: 0; color: #667eea;">📋 Informações do Plantão</h3>
            <div class="info-row">
              <span class="info-label">Cliente:</span>
              <span class="info-value">${data.clienteNome}</span>
            </div>
            <div class="info-row">
              <span class="info-label">Local:</span>
              <span class="info-value">${data.localAtendimento}</span>
            </div>
            <div class="info-row">
              <span class="info-label">Período:</span>
              <span class="info-value">${String(data.mes).padStart(2, "0")}/${data.ano}</span>
            </div>
          </div>
          
          <div class="info-card">
            <h3 style="margin-top: 0; color: #667eea;">💵 Valores da Antecipação</h3>
            <div class="info-row">
              <span class="info-label">Valor Solicitado:</span>
              <span class="info-value" style="color: #28a745;">${formatCurrency(data.valorSolicitado)}</span>
            </div>
            <div class="info-row">
              <span class="info-label">Percentual:</span>
              <span class="info-value">${data.percentual}%</span>
            </div>
            <div class="info-row">
              <span class="info-label">Taxa de Antecipação:</span>
              <span class="info-value">${data.taxaAntecipacao}%</span>
            </div>
            <div class="info-row">
              <span class="info-label">Valor Líquido:</span>
              <span class="info-value" style="color: #007bff; font-size: 18px;">${formatCurrency(data.valorLiquido)}</span>
            </div>
            <div class="info-row">
              <span class="info-label">Previsão de Pagamento:</span>
              <span class="info-value">${new Date(data.dataPagamentoPrevista).toLocaleDateString("pt-BR")}</span>
            </div>
          </div>
          
          <div class="highlight">
            <strong>⚠️ IMPORTANTE:</strong> Para finalizar o processo e receber sua antecipação, você precisa fazer login na plataforma e assinar digitalmente o Termo de Cessão de Direitos de Crédito.
          </div>
          
          <div class="btn-container">
            <a href="${linkPlataforma}" class="btn-primary">
              🔐 Fazer Login na Plataforma
            </a>
          </div>
          
          <p style="text-align: center; color: #666; font-size: 14px;">
            Ao fazer login, você verá automaticamente o termo pendente para assinatura.
          </p>
          
          <p style="margin-top: 30px;">
            <strong>Próximos passos:</strong>
          </p>
          <ol>
            <li>Clique no botão acima para fazer login</li>
            <li>Automaticamente aparecerá o termo para assinatura</li>
            <li>Leia atentamente todas as cláusulas</li>
            <li>Marque a opção de aceite e confirme</li>
          </ol>
          
          <p>Após a assinatura, você receberá um email de confirmação e o valor será processado conforme o prazo estabelecido.</p>
          
          <p style="margin-top: 30px;">Em caso de dúvidas, entre em contato com nosso suporte.</p>
          
          <p>Atenciosamente,<br><strong>Equipe GS2</strong></p>
        </div>
        
        <div class="footer">
          <p>Este é um email automático. Por favor, não responda.</p>
          <p>© ${new Date().getFullYear()} GS2 Group - Todos os direitos reservados</p>
        </div>
      </div>
    </body>
    </html>
  `;

  const textBody = `
    Antecipação Aprovada - Assinatura Necessária

    Olá ${data.profissionalNome},

    Sua solicitação de antecipação foi APROVADA! Para finalizar o processo, é necessário assinar digitalmente o termo de cessão de direitos.

    INFORMAÇÕES DO PLANTÃO:
    - Cliente: ${data.clienteNome}
    - Local: ${data.localAtendimento}
    - Período: ${String(data.mes).padStart(2, "0")}/${data.ano}

    VALORES DA ANTECIPAÇÃO:
    - Valor Solicitado: ${formatCurrency(data.valorSolicitado)}
    - Percentual: ${data.percentual}%
    - Taxa de Antecipação: ${data.taxaAntecipacao}%
    - Valor Líquido: ${formatCurrency(data.valorLiquido)}
    - Previsão de Pagamento: ${new Date(data.dataPagamentoPrevista).toLocaleDateString("pt-BR")}

    IMPORTANTE: Para finalizar o processo e receber sua antecipação, você precisa fazer login na plataforma e assinar digitalmente o Termo de Cessão de Direitos de Crédito.

    Acesse o link abaixo para fazer login:
    ${linkPlataforma}

    Ao fazer login, você verá automaticamente o termo pendente para assinatura.

    Próximos passos:
    1. Clique no link acima para fazer login
    2. Automaticamente aparecerá o termo para assinatura
    3. Leia atentamente todas as cláusulas
    4. Marque a opção de aceite e confirme

    Após a assinatura, você receberá um email de confirmação e o valor será processado conforme o prazo estabelecido.

    Atenciosamente,
    Equipe GS2

    Este é um email automático, por favor não responda.
  `;

  await service.sendEmail({
    to: data.profissionalEmail,
    subject,
    htmlBody,
    textBody,
  });
}

export async function sendTermoAssinadoEmail(data: {
  profissionalNome: string;
  profissionalEmail: string;
  dataAssinatura: Date;
  numeroAntecipacao: string;
  clienteNome: string;
}) {
  const service = getEmailService();

  const subject = `Termo de Antecipação Assinado com Sucesso - ${data.clienteNome}`;

  const htmlBody = /*html*/ `
    <!DOCTYPE html>
    <html>
    <head>
      <meta charset="utf-8">
      <style>
        body { font-family: Arial, sans-serif; line-height: 1.6; color: #333; }
        .container { max-width: 600px; margin: 0 auto; padding: 20px; }
        .header { background: linear-gradient(135deg, #28a745 0%, #20c997 100%); padding: 30px; text-align: center; border-radius: 10px 10px 0 0; }
        .header h1 { color: white; margin: 0; font-size: 24px; }
        .content { padding: 30px; background: #f9f9f9; }
        .success-card { background: #d4edda; border: 1px solid #c3e6cb; padding: 20px; border-radius: 8px; margin: 20px 0; }
        .info-card { background: white; padding: 20px; border-radius: 8px; margin: 20px 0; box-shadow: 0 2px 4px rgba(0,0,0,0.1); }
        .footer { background: #f4f4f4; padding: 20px; text-align: center; font-size: 12px; color: #666; border-radius: 0 0 10px 10px; }
      </style>
    </head>
    <body>
      <div class="container">
        <div class="header">
          <h1>✅ Termo Assinado com Sucesso</h1>
        </div>
        
        <div class="content">
          <p>Olá <strong>${data.profissionalNome}</strong>,</p>
          
          <div class="success-card">
            <h3 style="margin-top: 0; color: #155724;">🎉 Confirmação de Assinatura</h3>
            <p>Seu Termo de Cessão de Direitos de Crédito foi assinado digitalmente com sucesso!</p>
          </div>
          
          <div class="info-card">
            <h3 style="margin-top: 0; color: #28a745;">📄 Detalhes da Assinatura</h3>
            <p><strong>Número da Antecipação:</strong> ${data.numeroAntecipacao}</p>
            <p><strong>Cliente:</strong> ${data.clienteNome}</p>
            <p><strong>Data e Hora da Assinatura:</strong> ${data.dataAssinatura.toLocaleString("pt-BR")}</p>
          </div>
          
          <p><strong>Próximos passos:</strong></p>
          <ul>
            <li>Sua solicitação será analisada pela equipe financeira</li>
            <li>Você receberá uma notificação sobre a aprovação</li>
            <li>Após aprovação, o valor será creditado conforme data prevista</li>
          </ul>
          
          <p style="margin-top: 30px;">Guarde este email para seus registros.</p>
          
          <p>Atenciosamente,<br><strong>Equipe GS2</strong></p>
        </div>
        
        <div class="footer">
          <p>Este é um email automático. Por favor, não responda.</p>
          <p>© ${new Date().getFullYear()} GS2 Group - Todos os direitos reservados</p>
        </div>
      </div>
    </body>
    </html>
  `;

  const textBody = `
    Termo Assinado com Sucesso

    Olá ${data.profissionalNome},

    Seu Termo de Cessão de Direitos de Crédito foi assinado digitalmente com sucesso!

    DETALHES DA ASSINATURA:
    - Número da Antecipação: ${data.numeroAntecipacao}
    - Cliente: ${data.clienteNome}
    - Data e Hora da Assinatura: ${data.dataAssinatura.toLocaleString("pt-BR")}

    Próximos passos:
    - Sua solicitação será analisada pela equipe financeira
    - Você receberá uma notificação sobre a aprovação
    - Após aprovação, o valor será creditado conforme data prevista

    Guarde este email para seus registros.

    Atenciosamente,
    Equipe GS2

    Este é um email automático, por favor não responda.
  `;

  await service.sendEmail({
    to: data.profissionalEmail,
    subject,
    htmlBody,
    textBody,
  });
}
