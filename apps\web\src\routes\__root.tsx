import { createRootRoute } from "@tanstack/react-router";
import { requireAuth } from "../lib/route-guards";
import { QueryClientProvider } from "@tanstack/react-query";

// import { TanStackRouterDevtools } from "@tanstack/router-devtools";
// import { ReactQueryDevtools } from "@tanstack/react-query-devtools";

import { ThemeProvider } from "../components/theme-provider";
import { Toaster } from "../components/ui/sonner";
import { queryClient } from "../lib/query-client";
import { Layout as AppLayout } from "../components/layout";

import "../index.css";

export const Route = createRootRoute({
  component: () => (
    <>
      <QueryClientProvider client={queryClient}>
        <ThemeProvider attribute="class" defaultTheme="dark" disableTransitionOnChange storageKey="vite-ui-theme">
          <AppLayout />
          <Toaster richColors />
        </ThemeProvider>
        {/* <ReactQueryDevtools initialIsOpen={false} /> */}
      </QueryClientProvider>
      {/* <TanStackRouterDevtools /> */}
    </>
  ),
  beforeLoad: async ({ location }) => {
    if (location.pathname !== "/login") {
      await requireAuth();
    }
  },
});
