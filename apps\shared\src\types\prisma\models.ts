
/* !!! This is code generated by Prisma. Do not edit directly. !!! */
/* eslint-disable */
// @ts-nocheck 
/*
 * This is a barrel export file for all models and their related types.
 *
 * 🟢 You can import this file directly.
 */
export type * from './models/Usuario.ts'
export type * from './models/Role.ts'
export type * from './models/UsuarioRole.ts'
export type * from './models/Cliente.ts'
export type * from './models/Especialidade.ts'
export type * from './models/Profissional.ts'
export type * from './models/ProfissionalEspecialidade.ts'
export type * from './models/UsuarioCliente.ts'
export type * from './models/LocalAtendimento.ts'
export type * from './models/LocalAtendimentoEspecialidades.ts'
export type * from './models/Plantao.ts'
export type * from './models/DiaPlantao.ts'
export type * from './models/PresencaDiaPlantao.ts'
export type * from './models/Fechamento.ts'
export type * from './models/Antecipacao.ts'
export type * from './models/AntecipacaoHistorico.ts'
export type * from './models/AuditLog.ts'
export type * from './models/GerenciamentoTermosLgpd.ts'
export type * from './models/AceiteTermosLgpd.ts'
export type * from './commonInputTypes.ts'