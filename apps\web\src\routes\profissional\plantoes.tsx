import { useState } from "react";
import { useQuery, useMutation, useQueryClient } from "@tanstack/react-query";
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from "@/components/ui/card";
import { Button } from "@/components/ui/button";
import { Badge } from "@/components/ui/badge";
import { toast } from "sonner";
import { LogIn, LogOut, Calendar, Clock, MapPin, Building2 } from "lucide-react";
import { createCurrentLocalDate, getDate, getMonth, getYear } from "@/lib/utils";
import { createFileRoute } from "@tanstack/react-router";
import { api } from "@/lib/api";

interface Plantao {
  id: string;
  clienteId: string;
  cliente: {
    nome: string;
    tipo: string;
  };
  localAtendimentoId: string;
  localAtendimento: {
    nome: string;
    endereco: string;
    numero: string;
    bairro: string;
    cidade: string;
    estado: string;
  };
  mes: number;
  ano: number;
  modalidadeTrabalho: string;
  tipoFechamento: string;
  valorHora?: number;
  valorPlantao?: number;
  valorMensal?: number;
  diasPlantao: Array<{
    id: string;
    dia: number;
    horaEntrada: string;
    horaSaida: string;
    intervalo: number;
    trabalhado: boolean;
    presenca?: {
      id: string;
      horaEntrada?: string;
      horaSaida?: string;
      status: string;
    };
  }>;
}

function ProfissionalPlantoesPage() {
  const queryClient = useQueryClient();
  const [selectedMonth, setSelectedMonth] = useState(() => {
    const now = createCurrentLocalDate();
    return { month: getMonth(now) + 1, year: getYear(now) };
  });

  const today = createCurrentLocalDate();
  const todayDay = getDate(today);
  const todayMonth = getMonth(today) + 1;
  const todayYear = getYear(today);

  const { data: plantoes, isLoading } = useQuery<Plantao[]>({
    queryKey: ["profissional-plantoes", selectedMonth],
    queryFn: async () => {
      try {
        return await api.get("/profissional/plantoes", {
          mes: selectedMonth.month,
          ano: selectedMonth.year,
        });
      } catch (error) {
        console.error("Erro ao buscar plantões:", error);
        return [];
      }
    },
  });

  const checkinMutation = useMutation({
    mutationFn: async ({ plantaoId, diaPlantaoId }: { plantaoId: string; diaPlantaoId: string }) => {
      return await api.post(`/profissional/plantoes/${plantaoId}/checkin`, { diaPlantaoId });
    },
    onSuccess: () => {
      toast.success("Check-in realizado com sucesso!");
      queryClient.invalidateQueries({ queryKey: ["profissional-plantoes"] });
    },
    onError: () => {
      toast.error("Não foi possível fazer o check-in.");
    },
  });

  const checkoutMutation = useMutation({
    mutationFn: async ({ plantaoId, diaPlantaoId }: { plantaoId: string; diaPlantaoId: string }) => {
      return await api.post(`/profissional/plantoes/${plantaoId}/checkout`, { diaPlantaoId });
    },
    onSuccess: () => {
      toast.success("Check-out realizado com sucesso!");
      queryClient.invalidateQueries({ queryKey: ["profissional-plantoes"] });
    },
    onError: () => {
      toast.error("Não foi possível fazer o check-out.");
    },
  });

  const handleCheckin = (plantaoId: string, diaPlantaoId: string) => {
    checkinMutation.mutate({ plantaoId, diaPlantaoId });
  };

  const handleCheckout = (plantaoId: string, diaPlantaoId: string) => {
    checkoutMutation.mutate({ plantaoId, diaPlantaoId });
  };

  const renderPlantaoCard = (plantao: Plantao) => {
    const isCurrentMonth = selectedMonth.month === todayMonth && selectedMonth.year === todayYear;
    const todayPlantao = isCurrentMonth ? plantao.diasPlantao.find((dia) => dia.dia === todayDay) : null;

    return (
      <Card key={plantao.id}>
        <CardContent className="p-6">
          <div className="flex justify-between items-start mb-4">
            <div className="space-y-1">
              <div className="flex items-center gap-2">
                <h3 className="font-semibold text-lg">{plantao.cliente.nome}</h3>
                <Badge variant="outline">
                  {plantao.mes}/{plantao.ano}
                </Badge>
              </div>
              <div className="flex items-center gap-4 text-sm text-muted-foreground">
                <div className="flex items-center gap-1">
                  <Building2 className="h-3 w-3" />
                  {plantao.localAtendimento.nome}
                </div>
                <div className="flex items-center gap-1">
                  <MapPin className="h-3 w-3" />
                  {plantao.localAtendimento.cidade}/{plantao.localAtendimento.estado}
                </div>
              </div>
            </div>
          </div>

          <div className="grid grid-cols-2 gap-4 mb-4 text-sm">
            <div>
              <span className="text-muted-foreground">Pagamento:</span>
              <p className="font-medium">{plantao.modalidadeTrabalho}</p>
            </div>
            <div>
              <span className="text-muted-foreground">Fechamento:</span>
              <p className="font-medium">{plantao.tipoFechamento}</p>
            </div>
            {plantao.valorHora && (
              <div>
                <span className="text-muted-foreground">Valor/Hora:</span>
                <p className="font-medium">R$ {plantao.valorHora.toFixed(2)}</p>
              </div>
            )}
            {plantao.valorPlantao && (
              <div>
                <span className="text-muted-foreground">Valor/Plantão:</span>
                <p className="font-medium">R$ {plantao.valorPlantao.toFixed(2)}</p>
              </div>
            )}
          </div>

          {todayPlantao && (
            <div className="border rounded-lg p-4 bg-muted/50 mb-4">
              <div className="flex justify-between items-center mb-3">
                <div className="flex items-center gap-2">
                  <Calendar className="h-4 w-4 text-primary" />
                  <span className="font-medium">Plantão de Hoje</span>
                </div>
                <Badge variant={todayPlantao.trabalhado ? "default" : "secondary"}>
                  {todayPlantao.trabalhado ? "Concluído" : "Em Aberto"}
                </Badge>
              </div>

              <div className="space-y-2 mb-3">
                <div className="flex items-center gap-2 text-sm">
                  <Clock className="h-3 w-3" />
                  <span>
                    {todayPlantao.horaEntrada} - {todayPlantao.horaSaida}
                  </span>
                  <span className="text-muted-foreground">(Intervalo: {todayPlantao.intervalo}h)</span>
                </div>

                {todayPlantao.presenca && (
                  <div className="space-y-1 text-sm">
                    {todayPlantao.presenca.horaEntrada && (
                      <div className="flex items-center gap-2">
                        <LogIn className="h-3 w-3 text-green-600" />
                        <span>Check-in: {new Date(todayPlantao.presenca.horaEntrada).toLocaleTimeString("pt-BR")}</span>
                      </div>
                    )}
                    {todayPlantao.presenca.horaSaida && (
                      <div className="flex items-center gap-2">
                        <LogOut className="h-3 w-3 text-red-600" />
                        <span>Check-out: {new Date(todayPlantao.presenca.horaSaida).toLocaleTimeString("pt-BR")}</span>
                      </div>
                    )}
                  </div>
                )}
              </div>

              {!todayPlantao.presenca || !todayPlantao.presenca.horaEntrada ? (
                <Button
                  onClick={() => handleCheckin(plantao.id, todayPlantao.id)}
                  className="w-full"
                  disabled={checkinMutation.isPending}
                >
                  <LogIn className="h-4 w-4 mr-2" />
                  Fazer Check-in
                </Button>
              ) : !todayPlantao.presenca.horaSaida ? (
                <Button
                  onClick={() => handleCheckout(plantao.id, todayPlantao.id)}
                  className="w-full"
                  variant="destructive"
                  disabled={checkoutMutation.isPending}
                >
                  <LogOut className="h-4 w-4 mr-2" />
                  Fazer Check-out
                </Button>
              ) : (
                <div className="text-center text-sm text-muted-foreground py-2">✓ Plantão concluído</div>
              )}
            </div>
          )}

          <div>
            <h4 className="text-sm font-medium mb-3">Calendário do Mês</h4>
            <div className="grid grid-cols-7 gap-1">
              <div className="text-center text-xs font-medium text-muted-foreground p-1">D</div>
              <div className="text-center text-xs font-medium text-muted-foreground p-1">S</div>
              <div className="text-center text-xs font-medium text-muted-foreground p-1">T</div>
              <div className="text-center text-xs font-medium text-muted-foreground p-1">Q</div>
              <div className="text-center text-xs font-medium text-muted-foreground p-1">Q</div>
              <div className="text-center text-xs font-medium text-muted-foreground p-1">S</div>
              <div className="text-center text-xs font-medium text-muted-foreground p-1">S</div>

              {plantao.diasPlantao.map((dia) => {
                const isToday = dia.dia === todayDay && isCurrentMonth;
                const isPast = dia.dia < todayDay && isCurrentMonth;
                const hasCheckin = dia.presenca?.horaEntrada;
                const hasCheckout = dia.presenca?.horaSaida;

                return (
                  <div
                    key={dia.id}
                    className={`
                      relative text-center p-2 rounded-md text-sm font-medium transition-colors
                      ${isToday ? "ring-2 ring-primary ring-offset-2" : ""}
                      ${
                        hasCheckout
                          ? "bg-green-500/10 text-green-700 dark:bg-green-500/20 dark:text-green-400"
                          : hasCheckin
                            ? "bg-blue-500/10 text-blue-700 dark:bg-blue-500/20 dark:text-blue-400"
                            : isPast
                              ? "bg-red-500/10 text-red-700 dark:bg-red-500/20 dark:text-red-400"
                              : "bg-muted hover:bg-muted/80"
                      }
                    `}
                  >
                    {dia.dia}
                    {hasCheckout && <div className="absolute -top-1 -right-1 w-2 h-2 bg-green-500 rounded-full" />}
                  </div>
                );
              })}
            </div>
            <div className="flex items-center gap-4 mt-3 text-xs">
              <div className="flex items-center gap-1">
                <div className="w-3 h-3 rounded bg-green-500/20" />
                <span className="text-muted-foreground">Concluído</span>
              </div>
              <div className="flex items-center gap-1">
                <div className="w-3 h-3 rounded bg-blue-500/20" />
                <span className="text-muted-foreground">Em Andamento</span>
              </div>
              <div className="flex items-center gap-1">
                <div className="w-3 h-3 rounded bg-red-500/20" />
                <span className="text-muted-foreground">Não Trabalhado</span>
              </div>
            </div>
          </div>
        </CardContent>
      </Card>
    );
  };

  return (
    <div className="space-y-6">
      <div className="flex justify-between items-center">
        <div>
          <h1 className="text-3xl font-bold">Meus Plantões</h1>
          <p className="text-muted-foreground">Visualize seus plantões e faça check-in/check-out</p>
        </div>
        <div className="flex gap-2">
          <Button
            variant="outline"
            onClick={() =>
              setSelectedMonth((prev) => ({
                month: prev.month === 1 ? 12 : prev.month - 1,
                year: prev.month === 1 ? prev.year - 1 : prev.year,
              }))
            }
          >
            Mês Anterior
          </Button>
          <Button
            variant="outline"
            onClick={() =>
              setSelectedMonth((prev) => ({
                month: prev.month === 12 ? 1 : prev.month + 1,
                year: prev.month === 12 ? prev.year + 1 : prev.year,
              }))
            }
          >
            Próximo Mês
          </Button>
        </div>
      </div>

      <Card>
        <CardHeader>
          <CardTitle>
            Plantões - {selectedMonth.month}/{selectedMonth.year}
          </CardTitle>
        </CardHeader>
        <CardContent>
          {isLoading ? (
            <div className="flex justify-center py-8">
              <div className="text-center">
                <div className="animate-spin rounded-full h-8 w-8 border-b-2 border-primary mx-auto mb-4"></div>
                <p className="text-muted-foreground">Carregando plantões...</p>
              </div>
            </div>
          ) : plantoes && plantoes.length > 0 ? (
            <div className="space-y-4">{plantoes.map(renderPlantaoCard)}</div>
          ) : (
            <div className="text-center py-8">
              <p className="text-muted-foreground">Nenhum plantão encontrado para este mês.</p>
            </div>
          )}
        </CardContent>
      </Card>
    </div>
  );
}

export const Route = createFileRoute("/profissional/plantoes")({
  component: ProfissionalPlantoesPage,
});
