import { createFile<PERSON><PERSON><PERSON>, useNavigate } from "@tanstack/react-router";
import { useState } from "react";
import { useMutation } from "@tanstack/react-query";
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from "@/components/ui/card";
import { Input } from "@/components/ui/input";
import { Button } from "@/components/ui/button";
import { Label } from "@/components/ui/label";
import { Alert, AlertDescription } from "@/components/ui/alert";
import { Eye, EyeOff, LogIn, AlertCircle } from "lucide-react";
import { api } from "@/lib/api";
import { useAuthStore } from "@/stores/use-auth.store";
import { requireGuest } from "@/lib/route-guards";
import { ProfileSelectionModal } from "@/components/profile-selection-modal";
import { ClientSelectionModal } from "@/components/client-selection-modal";
import { TermosPendentesModal } from "@/components/termos-pendentes-modal";
import type { RoleType } from "@shared/types";

interface Perfil {
  id: string;
  nome: string;
  descricao?: string;
}

interface Cliente {
  id: string;
  nome: string;
  fusoHorario?: string;
}

interface LoginResponse {
  token: string;
  usuario: {
    id: string;
    nome: string;
    email: string;
    roles: RoleType[];
    profissionalId: string | null;
    perfis?: Perfil[];
    clientes?: Cliente[];
  };
}

function Login() {
  const navigate = useNavigate();
  const { setAuth, setPerfis, setClientes, selecionarPerfil, selecionarCliente } = useAuthStore();

  const [usuario, setUsuario] = useState("");
  const [password, setPassword] = useState("");
  const [showPassword, setShowPassword] = useState(false);
  const [showProfileModal, setShowProfileModal] = useState(false);
  const [showClientModal, setShowClientModal] = useState(false);
  const [pendingAuthData, setPendingAuthData] = useState<{
    user: any;
    token: string;
  } | null>(null);
  const [showTermosModal, setShowTermosModal] = useState(false);

  const loginMutation = useMutation({
    mutationFn: async (credentials: { usuario: string; password: string }) => {
      return await api.post<LoginResponse>("/auth/login", credentials);
    },
    onSuccess: (data) => {
      // Determinar o tipo de usuário baseado nas roles
      let tipo: "ADMIN" | "PROFISSIONAL" = "PROFISSIONAL";

      const roles = data.usuario.roles;
      if (roles.includes("admin") || roles.includes("master")) {
        tipo = "ADMIN";
      } else if (roles.includes("profissional")) {
        tipo = "PROFISSIONAL";
      }

      const userData = {
        ...data.usuario,
        tipo,
        roles,
      };

      // Guardar dados de autenticação temporariamente
      setPendingAuthData({ user: userData, token: data.token });

      // Configurar perfis e clientes no store
      if (data.usuario.perfis && data.usuario.perfis.length > 0) {
        setPerfis(data.usuario.perfis);
      }
      if (data.usuario.clientes && data.usuario.clientes.length > 0) {
        setClientes(data.usuario.clientes);
      }

      // Verificar se precisa selecionar perfil
      if (data.usuario.perfis && data.usuario.perfis.length > 1) {
        setShowProfileModal(true);
      }
      // Se tem apenas um perfil, seleciona automaticamente e verifica clientes
      else if (data.usuario.perfis && data.usuario.perfis.length === 1) {
        selecionarPerfil(data.usuario.perfis[0]);

        // Verificar se tem múltiplos clientes
        if (data.usuario.clientes && data.usuario.clientes.length > 1) {
          setShowClientModal(true);
        } else if (data.usuario.clientes && data.usuario.clientes.length === 1) {
          // Seleciona automaticamente o único cliente e finaliza login
          selecionarCliente(data.usuario.clientes[0]);
          finalizarLogin(userData, data.token, tipo);
        } else {
          // Sem clientes, finaliza login
          finalizarLogin(userData, data.token, tipo);
        }
      }
      // Se não tem perfis, verifica clientes
      else if (data.usuario.clientes && data.usuario.clientes.length > 1) {
        setShowClientModal(true);
      } else if (data.usuario.clientes && data.usuario.clientes.length === 1) {
        // Seleciona automaticamente o único cliente
        selecionarCliente(data.usuario.clientes[0]);
        finalizarLogin(userData, data.token, tipo);
      } else {
        // Sem perfis e sem clientes, finaliza login direto
        finalizarLogin(userData, data.token, tipo);
      }
    },
    onError: (error: any) => {
      console.error("Erro no login:", error);
    },
  });

  const finalizarLogin = (userData: any, token: string, tipo: string) => {
    // Salvar autenticação no store
    setAuth(userData, token);

    // Se for profissional, verificar termos pendentes
    if (tipo === "PROFISSIONAL") {
      // Mostrar modal de termos pendentes
      setShowTermosModal(true);
      // O modal irá redirecionar automaticamente se houver apenas 1 termo
      // ou permitir que o usuário escolha se houver múltiplos
    } else {
      // Admin/Master vai direto para o dashboard
      navigate({ to: "/" });
    }
  };

  const handleProfileSelect = (perfil: Perfil) => {
    selecionarPerfil(perfil);
    setShowProfileModal(false);

    // Após selecionar perfil, verificar se precisa selecionar cliente
    if (pendingAuthData?.user.clientes && pendingAuthData.user.clientes.length > 1) {
      setShowClientModal(true);
    } else if (pendingAuthData?.user.clientes && pendingAuthData.user.clientes.length === 1) {
      // Seleciona automaticamente o único cliente
      selecionarCliente(pendingAuthData.user.clientes[0]);
      finalizarLogin(pendingAuthData.user, pendingAuthData.token, pendingAuthData.user.tipo);
    } else {
      // Sem clientes, finaliza login
      finalizarLogin(pendingAuthData!.user, pendingAuthData!.token, pendingAuthData!.user.tipo);
    }
  };

  const handleClientSelect = (cliente: Cliente) => {
    selecionarCliente(cliente);
    setShowClientModal(false);

    // Finalizar login após selecionar cliente
    if (pendingAuthData) {
      finalizarLogin(pendingAuthData.user, pendingAuthData.token, pendingAuthData.user.tipo);
    }
  };

  const handleSubmit = (e: React.FormEvent) => {
    e.preventDefault();
    if (usuario && password) {
      loginMutation.mutate({ usuario, password });
    }
  };

  return (
    <div className="min-h-screen flex items-center justify-center from-background to-muted p-4">
      <div className="w-full max-w-md">
        <div className="text-center mb-8">
          <img src="/logo.png" alt="Logo" className="mx-auto mb-4 h-22 w-22" />
        </div>

        <Card>
          <CardHeader>
            <CardTitle>Login</CardTitle>
            <CardDescription>Entre com suas credenciais para acessar o sistema</CardDescription>
          </CardHeader>
          <CardContent>
            <form onSubmit={handleSubmit} className="space-y-4">
              {loginMutation.isError && (
                <Alert variant="destructive">
                  <AlertCircle className="h-4 w-4" />
                  <AlertDescription>
                    {loginMutation.error?.message === "Credenciais inválidas"
                      ? "Usuário ou senha incorretos"
                      : loginMutation.error?.message === "Usuário inativo"
                        ? "Sua conta está inativa. Entre em contato com o administrador."
                        : "Erro ao fazer login. Tente novamente."}
                  </AlertDescription>
                </Alert>
              )}

              <div className="space-y-2">
                <Label htmlFor="usuario">Usuário</Label>
                <Input
                  id="usuario"
                  type="text"
                  placeholder="Email ou CPF"
                  value={usuario}
                  onChange={(e) => setUsuario(e.target.value)}
                  required
                  autoComplete="username"
                  disabled={loginMutation.isPending}
                />
              </div>

              <div className="space-y-2">
                <Label htmlFor="password">Senha</Label>
                <div className="relative">
                  <Input
                    id="password"
                    type={showPassword ? "text" : "password"}
                    placeholder="••••••••"
                    value={password}
                    onChange={(e) => setPassword(e.target.value)}
                    required
                    autoComplete="current-password"
                    disabled={loginMutation.isPending}
                    className="pr-10"
                  />
                  <button
                    type="button"
                    onClick={() => setShowPassword(!showPassword)}
                    className="absolute right-2 top-1/2 -translate-y-1/2 text-muted-foreground hover:text-foreground transition-colors"
                    tabIndex={-1}
                  >
                    {showPassword ? <EyeOff className="h-4 w-4" /> : <Eye className="h-4 w-4" />}
                  </button>
                </div>
              </div>

              <Button type="submit" className="w-full" disabled={loginMutation.isPending}>
                {loginMutation.isPending ? (
                  <>
                    <div className="mr-2 h-4 w-4 animate-spin rounded-full border-2 border-current border-t-transparent" />
                    Entrando...
                  </>
                ) : (
                  <>
                    <LogIn className="mr-2 h-4 w-4" />
                    Entrar
                  </>
                )}
              </Button>
            </form>
          </CardContent>
        </Card>

        {/* <div className="mt-6 text-center text-sm text-muted-foreground">
          <p>Esqueceu sua senha? Entre em contato com o administrador.</p>
        </div> */}
      </div>

      {/* Modais de seleção */}
      <ProfileSelectionModal
        open={showProfileModal}
        perfis={pendingAuthData?.user.perfis || []}
        onSelect={handleProfileSelect}
      />

      <ClientSelectionModal
        open={showClientModal}
        clientes={pendingAuthData?.user.clientes || []}
        onSelect={handleClientSelect}
      />

      <TermosPendentesModal
        open={showTermosModal}
        onClose={() => {
          setShowTermosModal(false);
          // Redirecionar para o dashboard do profissional após fechar o modal
          navigate({ to: "/profissional/dashboard" });
        }}
      />
    </div>
  );
}

export const Route = createFileRoute("/login")({
  component: Login,
  beforeLoad: async () => {
    await requireGuest();
  },
});
