import { z } from "zod";
import { prisma, withAudit } from "@/lib/prisma";
import { createPlantaoSchema, type CreatePlantaoInput, type UpdatePlantaoInput } from "@/schemas/plantao.schema";
import type { FastifyTypedInstance } from "@/types";
import { formatDateToDateTime, parseUTCDate, toISOString } from "@shared/date";

export function atualizarPlantaoRouter(fastify: FastifyTypedInstance) {
  // Atualizar plantão
  fastify.put<{
    Params: { uuid: string };
    Body: UpdatePlantaoInput;
  }>(
    "/plantoes/:uuid",
    withAudit(async (request, reply) => {
      const { uuid } = request.params;
      const {
        diasPlantao,
        clienteUuid,
        profissionalId,
        profissionalUuid,
        localAtendimentoId,
        localAtendimentoUuid,
        mes, // Remove mes (not in schema)
        ano, // Remove ano (not in schema)
        valorTotal, // Remove valorTotal (not in schema)
        ...plantaoData
      } = request.body;

      const clienteId = request.clienteId;

      // Verificar se plantão existe
      const existingPlantao = await prisma.plantao.findUnique({
        where: { uuid },
      });

      if (!existingPlantao) {
        return reply.status(404).send({ error: "Plantão não encontrado" });
      }

      // Resolver UUIDs para IDs internos
      let resolvedClienteId = clienteId;
      let resolvedLocalAtendimentoId = localAtendimentoId;
      let resolvedProfissionalId = profissionalId;

      // Resolver localAtendimentoUuid se fornecido
      if (localAtendimentoUuid && !resolvedLocalAtendimentoId) {
        const local = await prisma.localAtendimento.findUnique({
          where: { uuid: localAtendimentoUuid },
          select: { id: true },
        });
        if (!local) {
          return reply.status(400).send({ error: "Local de atendimento não encontrado" });
        }
        resolvedLocalAtendimentoId = local.id;
      }

      // Resolver profissionalUuid se fornecido
      if (profissionalUuid && !resolvedProfissionalId) {
        const profissional = await prisma.profissional.findUnique({
          where: { uuid: profissionalUuid },
          select: { id: true },
        });
        if (!profissional) {
          return reply.status(400).send({ error: "Profissional não encontrado" });
        }
        resolvedProfissionalId = profissional.id;
      }

      // Converter datas para DateTime ISO-8601 se necessário
      const processedPlantaoData = {
        ...plantaoData,
        ...(plantaoData.dataInicial && {
          dataInicial: plantaoData.dataInicial.includes("T")
            ? plantaoData.dataInicial
            : `${plantaoData.dataInicial}T00:00:00.000Z`,
        }),
        ...(plantaoData.dataFinal && {
          dataFinal: plantaoData.dataFinal.includes("T")
            ? plantaoData.dataFinal
            : `${plantaoData.dataFinal}T23:59:59.999Z`,
        }),
      };

      // Preparar dados para atualização com relacionamentos
      const updateData: any = {
        ...processedPlantaoData,
        ...(resolvedClienteId && resolvedClienteId !== existingPlantao.clienteId
          ? {
              cliente: { connect: { id: resolvedClienteId } },
            }
          : {}),
        ...(resolvedProfissionalId && resolvedProfissionalId !== existingPlantao.profissionalId
          ? {
              profissional: { connect: { id: resolvedProfissionalId } },
            }
          : {}),
        ...(resolvedLocalAtendimentoId && resolvedLocalAtendimentoId !== existingPlantao.localAtendimentoId
          ? {
              localAtendimento: { connect: { id: resolvedLocalAtendimentoId } },
            }
          : {}),
      };

      // Atualizar plantão
      const plantao = await prisma.plantao.update({
        where: { uuid },
        data: updateData,
        include: {
          cliente: true,
          profissional: true,
          localAtendimento: true,
          diasPlantao: true,
        },
      });

      // Se foram fornecidos dias, atualizar também
      if (diasPlantao) {
        // Deletar dias existentes e criar novos
        await prisma.diaPlantao.deleteMany({
          where: { plantaoId: existingPlantao.id },
        });

        // Processar dias usando apenas a data completa
        const processedDiasPlantao = diasPlantao
          .map((dia: any) => {
            // Converter a data string (YYYY-MM-DD) para Date usando date-fns
            const date = parseUTCDate(dia.data);

            return {
              plantaoId: existingPlantao.id,
              data: toISOString(date),
              horaEntrada: dia.horaEntrada,
              horaSaida: dia.horaSaida,
              intervalo: dia.intervalo,
            };
          })
          .filter(Boolean); // Remove nulls

        await prisma.diaPlantao.createMany({
          data: processedDiasPlantao,
        });
      }

      return reply.send(plantao);
    })
  );
}
