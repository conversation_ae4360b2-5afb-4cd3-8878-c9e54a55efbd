
/* !!! This is code generated by Prisma. Do not edit directly. !!! */
/* eslint-disable */
// @ts-nocheck 
/*
 * This file exports the `AceiteTermosLgpd` model and its related types.
 *
 * 🟢 You can import this file directly.
 */
import * as runtime from "@prisma/client/runtime/library"
import type * as $Enums from "../enums.ts"
import type * as Prisma from "../internal/prismaNamespace.ts"

/**
 * Model AceiteTermosLgpd
 * 
 */
export type AceiteTermosLgpdModel = runtime.Types.Result.DefaultSelection<Prisma.$AceiteTermosLgpdPayload>

export type AggregateAceiteTermosLgpd = {
  _count: AceiteTermosLgpdCountAggregateOutputType | null
  _avg: AceiteTermosLgpdAvgAggregateOutputType | null
  _sum: AceiteTermosLgpdSumAggregateOutputType | null
  _min: AceiteTermosLgpdMinAggregateOutputType | null
  _max: AceiteTermosLgpdMaxAggregateOutputType | null
}

export type AceiteTermosLgpdAvgAggregateOutputType = {
  id: number | null
  usuarioId: number | null
  termoVersaoId: number | null
  latitude: runtime.Decimal | null
  longitude: runtime.Decimal | null
}

export type AceiteTermosLgpdSumAggregateOutputType = {
  id: number | null
  usuarioId: number | null
  termoVersaoId: number | null
  latitude: runtime.Decimal | null
  longitude: runtime.Decimal | null
}

export type AceiteTermosLgpdMinAggregateOutputType = {
  id: number | null
  usuarioId: number | null
  termoVersaoId: number | null
  consentimentoLgpd: boolean | null
  aceitoEm: Date | null
  enderecoIp: string | null
  latitude: runtime.Decimal | null
  longitude: runtime.Decimal | null
  userAgent: string | null
  termoVersao: string | null
}

export type AceiteTermosLgpdMaxAggregateOutputType = {
  id: number | null
  usuarioId: number | null
  termoVersaoId: number | null
  consentimentoLgpd: boolean | null
  aceitoEm: Date | null
  enderecoIp: string | null
  latitude: runtime.Decimal | null
  longitude: runtime.Decimal | null
  userAgent: string | null
  termoVersao: string | null
}

export type AceiteTermosLgpdCountAggregateOutputType = {
  id: number
  usuarioId: number
  termoVersaoId: number
  consentimentoLgpd: number
  aceitoEm: number
  enderecoIp: number
  latitude: number
  longitude: number
  userAgent: number
  dadosAdicionais: number
  termoVersao: number
  _all: number
}


export type AceiteTermosLgpdAvgAggregateInputType = {
  id?: true
  usuarioId?: true
  termoVersaoId?: true
  latitude?: true
  longitude?: true
}

export type AceiteTermosLgpdSumAggregateInputType = {
  id?: true
  usuarioId?: true
  termoVersaoId?: true
  latitude?: true
  longitude?: true
}

export type AceiteTermosLgpdMinAggregateInputType = {
  id?: true
  usuarioId?: true
  termoVersaoId?: true
  consentimentoLgpd?: true
  aceitoEm?: true
  enderecoIp?: true
  latitude?: true
  longitude?: true
  userAgent?: true
  termoVersao?: true
}

export type AceiteTermosLgpdMaxAggregateInputType = {
  id?: true
  usuarioId?: true
  termoVersaoId?: true
  consentimentoLgpd?: true
  aceitoEm?: true
  enderecoIp?: true
  latitude?: true
  longitude?: true
  userAgent?: true
  termoVersao?: true
}

export type AceiteTermosLgpdCountAggregateInputType = {
  id?: true
  usuarioId?: true
  termoVersaoId?: true
  consentimentoLgpd?: true
  aceitoEm?: true
  enderecoIp?: true
  latitude?: true
  longitude?: true
  userAgent?: true
  dadosAdicionais?: true
  termoVersao?: true
  _all?: true
}

export type AceiteTermosLgpdAggregateArgs<ExtArgs extends runtime.Types.Extensions.InternalArgs = runtime.Types.Extensions.DefaultArgs> = {
  /**
   * Filter which AceiteTermosLgpd to aggregate.
   */
  where?: Prisma.AceiteTermosLgpdWhereInput
  /**
   * {@link https://www.prisma.io/docs/concepts/components/prisma-client/sorting Sorting Docs}
   * 
   * Determine the order of AceiteTermosLgpds to fetch.
   */
  orderBy?: Prisma.AceiteTermosLgpdOrderByWithRelationInput | Prisma.AceiteTermosLgpdOrderByWithRelationInput[]
  /**
   * {@link https://www.prisma.io/docs/concepts/components/prisma-client/pagination#cursor-based-pagination Cursor Docs}
   * 
   * Sets the start position
   */
  cursor?: Prisma.AceiteTermosLgpdWhereUniqueInput
  /**
   * {@link https://www.prisma.io/docs/concepts/components/prisma-client/pagination Pagination Docs}
   * 
   * Take `±n` AceiteTermosLgpds from the position of the cursor.
   */
  take?: number
  /**
   * {@link https://www.prisma.io/docs/concepts/components/prisma-client/pagination Pagination Docs}
   * 
   * Skip the first `n` AceiteTermosLgpds.
   */
  skip?: number
  /**
   * {@link https://www.prisma.io/docs/concepts/components/prisma-client/aggregations Aggregation Docs}
   * 
   * Count returned AceiteTermosLgpds
  **/
  _count?: true | AceiteTermosLgpdCountAggregateInputType
  /**
   * {@link https://www.prisma.io/docs/concepts/components/prisma-client/aggregations Aggregation Docs}
   * 
   * Select which fields to average
  **/
  _avg?: AceiteTermosLgpdAvgAggregateInputType
  /**
   * {@link https://www.prisma.io/docs/concepts/components/prisma-client/aggregations Aggregation Docs}
   * 
   * Select which fields to sum
  **/
  _sum?: AceiteTermosLgpdSumAggregateInputType
  /**
   * {@link https://www.prisma.io/docs/concepts/components/prisma-client/aggregations Aggregation Docs}
   * 
   * Select which fields to find the minimum value
  **/
  _min?: AceiteTermosLgpdMinAggregateInputType
  /**
   * {@link https://www.prisma.io/docs/concepts/components/prisma-client/aggregations Aggregation Docs}
   * 
   * Select which fields to find the maximum value
  **/
  _max?: AceiteTermosLgpdMaxAggregateInputType
}

export type GetAceiteTermosLgpdAggregateType<T extends AceiteTermosLgpdAggregateArgs> = {
      [P in keyof T & keyof AggregateAceiteTermosLgpd]: P extends '_count' | 'count'
    ? T[P] extends true
      ? number
      : Prisma.GetScalarType<T[P], AggregateAceiteTermosLgpd[P]>
    : Prisma.GetScalarType<T[P], AggregateAceiteTermosLgpd[P]>
}




export type AceiteTermosLgpdGroupByArgs<ExtArgs extends runtime.Types.Extensions.InternalArgs = runtime.Types.Extensions.DefaultArgs> = {
  where?: Prisma.AceiteTermosLgpdWhereInput
  orderBy?: Prisma.AceiteTermosLgpdOrderByWithAggregationInput | Prisma.AceiteTermosLgpdOrderByWithAggregationInput[]
  by: Prisma.AceiteTermosLgpdScalarFieldEnum[] | Prisma.AceiteTermosLgpdScalarFieldEnum
  having?: Prisma.AceiteTermosLgpdScalarWhereWithAggregatesInput
  take?: number
  skip?: number
  _count?: AceiteTermosLgpdCountAggregateInputType | true
  _avg?: AceiteTermosLgpdAvgAggregateInputType
  _sum?: AceiteTermosLgpdSumAggregateInputType
  _min?: AceiteTermosLgpdMinAggregateInputType
  _max?: AceiteTermosLgpdMaxAggregateInputType
}

export type AceiteTermosLgpdGroupByOutputType = {
  id: number
  usuarioId: number
  termoVersaoId: number
  consentimentoLgpd: boolean
  aceitoEm: Date | null
  enderecoIp: string
  latitude: runtime.Decimal
  longitude: runtime.Decimal
  userAgent: string | null
  dadosAdicionais: runtime.JsonValue | null
  termoVersao: string
  _count: AceiteTermosLgpdCountAggregateOutputType | null
  _avg: AceiteTermosLgpdAvgAggregateOutputType | null
  _sum: AceiteTermosLgpdSumAggregateOutputType | null
  _min: AceiteTermosLgpdMinAggregateOutputType | null
  _max: AceiteTermosLgpdMaxAggregateOutputType | null
}

type GetAceiteTermosLgpdGroupByPayload<T extends AceiteTermosLgpdGroupByArgs> = Prisma.PrismaPromise<
  Array<
    Prisma.PickEnumerable<AceiteTermosLgpdGroupByOutputType, T['by']> &
      {
        [P in ((keyof T) & (keyof AceiteTermosLgpdGroupByOutputType))]: P extends '_count'
          ? T[P] extends boolean
            ? number
            : Prisma.GetScalarType<T[P], AceiteTermosLgpdGroupByOutputType[P]>
          : Prisma.GetScalarType<T[P], AceiteTermosLgpdGroupByOutputType[P]>
      }
    >
  >



export type AceiteTermosLgpdWhereInput = {
  AND?: Prisma.AceiteTermosLgpdWhereInput | Prisma.AceiteTermosLgpdWhereInput[]
  OR?: Prisma.AceiteTermosLgpdWhereInput[]
  NOT?: Prisma.AceiteTermosLgpdWhereInput | Prisma.AceiteTermosLgpdWhereInput[]
  id?: Prisma.IntFilter<"AceiteTermosLgpd"> | number
  usuarioId?: Prisma.IntFilter<"AceiteTermosLgpd"> | number
  termoVersaoId?: Prisma.IntFilter<"AceiteTermosLgpd"> | number
  consentimentoLgpd?: Prisma.BoolFilter<"AceiteTermosLgpd"> | boolean
  aceitoEm?: Prisma.DateTimeNullableFilter<"AceiteTermosLgpd"> | Date | string | null
  enderecoIp?: Prisma.StringFilter<"AceiteTermosLgpd"> | string
  latitude?: Prisma.DecimalFilter<"AceiteTermosLgpd"> | runtime.Decimal | runtime.DecimalJsLike | number | string
  longitude?: Prisma.DecimalFilter<"AceiteTermosLgpd"> | runtime.Decimal | runtime.DecimalJsLike | number | string
  userAgent?: Prisma.StringNullableFilter<"AceiteTermosLgpd"> | string | null
  dadosAdicionais?: Prisma.JsonNullableFilter<"AceiteTermosLgpd">
  termoVersao?: Prisma.StringFilter<"AceiteTermosLgpd"> | string
  termo?: Prisma.XOR<Prisma.GerenciamentoTermosLgpdScalarRelationFilter, Prisma.GerenciamentoTermosLgpdWhereInput>
  usuario?: Prisma.XOR<Prisma.UsuarioScalarRelationFilter, Prisma.UsuarioWhereInput>
}

export type AceiteTermosLgpdOrderByWithRelationInput = {
  id?: Prisma.SortOrder
  usuarioId?: Prisma.SortOrder
  termoVersaoId?: Prisma.SortOrder
  consentimentoLgpd?: Prisma.SortOrder
  aceitoEm?: Prisma.SortOrderInput | Prisma.SortOrder
  enderecoIp?: Prisma.SortOrder
  latitude?: Prisma.SortOrder
  longitude?: Prisma.SortOrder
  userAgent?: Prisma.SortOrderInput | Prisma.SortOrder
  dadosAdicionais?: Prisma.SortOrderInput | Prisma.SortOrder
  termoVersao?: Prisma.SortOrder
  termo?: Prisma.GerenciamentoTermosLgpdOrderByWithRelationInput
  usuario?: Prisma.UsuarioOrderByWithRelationInput
  _relevance?: Prisma.AceiteTermosLgpdOrderByRelevanceInput
}

export type AceiteTermosLgpdWhereUniqueInput = Prisma.AtLeast<{
  id?: number
  AND?: Prisma.AceiteTermosLgpdWhereInput | Prisma.AceiteTermosLgpdWhereInput[]
  OR?: Prisma.AceiteTermosLgpdWhereInput[]
  NOT?: Prisma.AceiteTermosLgpdWhereInput | Prisma.AceiteTermosLgpdWhereInput[]
  usuarioId?: Prisma.IntFilter<"AceiteTermosLgpd"> | number
  termoVersaoId?: Prisma.IntFilter<"AceiteTermosLgpd"> | number
  consentimentoLgpd?: Prisma.BoolFilter<"AceiteTermosLgpd"> | boolean
  aceitoEm?: Prisma.DateTimeNullableFilter<"AceiteTermosLgpd"> | Date | string | null
  enderecoIp?: Prisma.StringFilter<"AceiteTermosLgpd"> | string
  latitude?: Prisma.DecimalFilter<"AceiteTermosLgpd"> | runtime.Decimal | runtime.DecimalJsLike | number | string
  longitude?: Prisma.DecimalFilter<"AceiteTermosLgpd"> | runtime.Decimal | runtime.DecimalJsLike | number | string
  userAgent?: Prisma.StringNullableFilter<"AceiteTermosLgpd"> | string | null
  dadosAdicionais?: Prisma.JsonNullableFilter<"AceiteTermosLgpd">
  termoVersao?: Prisma.StringFilter<"AceiteTermosLgpd"> | string
  termo?: Prisma.XOR<Prisma.GerenciamentoTermosLgpdScalarRelationFilter, Prisma.GerenciamentoTermosLgpdWhereInput>
  usuario?: Prisma.XOR<Prisma.UsuarioScalarRelationFilter, Prisma.UsuarioWhereInput>
}, "id">

export type AceiteTermosLgpdOrderByWithAggregationInput = {
  id?: Prisma.SortOrder
  usuarioId?: Prisma.SortOrder
  termoVersaoId?: Prisma.SortOrder
  consentimentoLgpd?: Prisma.SortOrder
  aceitoEm?: Prisma.SortOrderInput | Prisma.SortOrder
  enderecoIp?: Prisma.SortOrder
  latitude?: Prisma.SortOrder
  longitude?: Prisma.SortOrder
  userAgent?: Prisma.SortOrderInput | Prisma.SortOrder
  dadosAdicionais?: Prisma.SortOrderInput | Prisma.SortOrder
  termoVersao?: Prisma.SortOrder
  _count?: Prisma.AceiteTermosLgpdCountOrderByAggregateInput
  _avg?: Prisma.AceiteTermosLgpdAvgOrderByAggregateInput
  _max?: Prisma.AceiteTermosLgpdMaxOrderByAggregateInput
  _min?: Prisma.AceiteTermosLgpdMinOrderByAggregateInput
  _sum?: Prisma.AceiteTermosLgpdSumOrderByAggregateInput
}

export type AceiteTermosLgpdScalarWhereWithAggregatesInput = {
  AND?: Prisma.AceiteTermosLgpdScalarWhereWithAggregatesInput | Prisma.AceiteTermosLgpdScalarWhereWithAggregatesInput[]
  OR?: Prisma.AceiteTermosLgpdScalarWhereWithAggregatesInput[]
  NOT?: Prisma.AceiteTermosLgpdScalarWhereWithAggregatesInput | Prisma.AceiteTermosLgpdScalarWhereWithAggregatesInput[]
  id?: Prisma.IntWithAggregatesFilter<"AceiteTermosLgpd"> | number
  usuarioId?: Prisma.IntWithAggregatesFilter<"AceiteTermosLgpd"> | number
  termoVersaoId?: Prisma.IntWithAggregatesFilter<"AceiteTermosLgpd"> | number
  consentimentoLgpd?: Prisma.BoolWithAggregatesFilter<"AceiteTermosLgpd"> | boolean
  aceitoEm?: Prisma.DateTimeNullableWithAggregatesFilter<"AceiteTermosLgpd"> | Date | string | null
  enderecoIp?: Prisma.StringWithAggregatesFilter<"AceiteTermosLgpd"> | string
  latitude?: Prisma.DecimalWithAggregatesFilter<"AceiteTermosLgpd"> | runtime.Decimal | runtime.DecimalJsLike | number | string
  longitude?: Prisma.DecimalWithAggregatesFilter<"AceiteTermosLgpd"> | runtime.Decimal | runtime.DecimalJsLike | number | string
  userAgent?: Prisma.StringNullableWithAggregatesFilter<"AceiteTermosLgpd"> | string | null
  dadosAdicionais?: Prisma.JsonNullableWithAggregatesFilter<"AceiteTermosLgpd">
  termoVersao?: Prisma.StringWithAggregatesFilter<"AceiteTermosLgpd"> | string
}

export type AceiteTermosLgpdCreateInput = {
  consentimentoLgpd?: boolean
  aceitoEm?: Date | string | null
  enderecoIp: string
  latitude: runtime.Decimal | runtime.DecimalJsLike | number | string
  longitude: runtime.Decimal | runtime.DecimalJsLike | number | string
  userAgent?: string | null
  dadosAdicionais?: Prisma.NullableJsonNullValueInput | runtime.InputJsonValue
  termoVersao: string
  termo: Prisma.GerenciamentoTermosLgpdCreateNestedOneWithoutAceiteTermosLgpdInput
  usuario: Prisma.UsuarioCreateNestedOneWithoutAceiteTermosLgpdInput
}

export type AceiteTermosLgpdUncheckedCreateInput = {
  id?: number
  usuarioId: number
  termoVersaoId: number
  consentimentoLgpd?: boolean
  aceitoEm?: Date | string | null
  enderecoIp: string
  latitude: runtime.Decimal | runtime.DecimalJsLike | number | string
  longitude: runtime.Decimal | runtime.DecimalJsLike | number | string
  userAgent?: string | null
  dadosAdicionais?: Prisma.NullableJsonNullValueInput | runtime.InputJsonValue
  termoVersao: string
}

export type AceiteTermosLgpdUpdateInput = {
  consentimentoLgpd?: Prisma.BoolFieldUpdateOperationsInput | boolean
  aceitoEm?: Prisma.NullableDateTimeFieldUpdateOperationsInput | Date | string | null
  enderecoIp?: Prisma.StringFieldUpdateOperationsInput | string
  latitude?: Prisma.DecimalFieldUpdateOperationsInput | runtime.Decimal | runtime.DecimalJsLike | number | string
  longitude?: Prisma.DecimalFieldUpdateOperationsInput | runtime.Decimal | runtime.DecimalJsLike | number | string
  userAgent?: Prisma.NullableStringFieldUpdateOperationsInput | string | null
  dadosAdicionais?: Prisma.NullableJsonNullValueInput | runtime.InputJsonValue
  termoVersao?: Prisma.StringFieldUpdateOperationsInput | string
  termo?: Prisma.GerenciamentoTermosLgpdUpdateOneRequiredWithoutAceiteTermosLgpdNestedInput
  usuario?: Prisma.UsuarioUpdateOneRequiredWithoutAceiteTermosLgpdNestedInput
}

export type AceiteTermosLgpdUncheckedUpdateInput = {
  id?: Prisma.IntFieldUpdateOperationsInput | number
  usuarioId?: Prisma.IntFieldUpdateOperationsInput | number
  termoVersaoId?: Prisma.IntFieldUpdateOperationsInput | number
  consentimentoLgpd?: Prisma.BoolFieldUpdateOperationsInput | boolean
  aceitoEm?: Prisma.NullableDateTimeFieldUpdateOperationsInput | Date | string | null
  enderecoIp?: Prisma.StringFieldUpdateOperationsInput | string
  latitude?: Prisma.DecimalFieldUpdateOperationsInput | runtime.Decimal | runtime.DecimalJsLike | number | string
  longitude?: Prisma.DecimalFieldUpdateOperationsInput | runtime.Decimal | runtime.DecimalJsLike | number | string
  userAgent?: Prisma.NullableStringFieldUpdateOperationsInput | string | null
  dadosAdicionais?: Prisma.NullableJsonNullValueInput | runtime.InputJsonValue
  termoVersao?: Prisma.StringFieldUpdateOperationsInput | string
}

export type AceiteTermosLgpdCreateManyInput = {
  id?: number
  usuarioId: number
  termoVersaoId: number
  consentimentoLgpd?: boolean
  aceitoEm?: Date | string | null
  enderecoIp: string
  latitude: runtime.Decimal | runtime.DecimalJsLike | number | string
  longitude: runtime.Decimal | runtime.DecimalJsLike | number | string
  userAgent?: string | null
  dadosAdicionais?: Prisma.NullableJsonNullValueInput | runtime.InputJsonValue
  termoVersao: string
}

export type AceiteTermosLgpdUpdateManyMutationInput = {
  consentimentoLgpd?: Prisma.BoolFieldUpdateOperationsInput | boolean
  aceitoEm?: Prisma.NullableDateTimeFieldUpdateOperationsInput | Date | string | null
  enderecoIp?: Prisma.StringFieldUpdateOperationsInput | string
  latitude?: Prisma.DecimalFieldUpdateOperationsInput | runtime.Decimal | runtime.DecimalJsLike | number | string
  longitude?: Prisma.DecimalFieldUpdateOperationsInput | runtime.Decimal | runtime.DecimalJsLike | number | string
  userAgent?: Prisma.NullableStringFieldUpdateOperationsInput | string | null
  dadosAdicionais?: Prisma.NullableJsonNullValueInput | runtime.InputJsonValue
  termoVersao?: Prisma.StringFieldUpdateOperationsInput | string
}

export type AceiteTermosLgpdUncheckedUpdateManyInput = {
  id?: Prisma.IntFieldUpdateOperationsInput | number
  usuarioId?: Prisma.IntFieldUpdateOperationsInput | number
  termoVersaoId?: Prisma.IntFieldUpdateOperationsInput | number
  consentimentoLgpd?: Prisma.BoolFieldUpdateOperationsInput | boolean
  aceitoEm?: Prisma.NullableDateTimeFieldUpdateOperationsInput | Date | string | null
  enderecoIp?: Prisma.StringFieldUpdateOperationsInput | string
  latitude?: Prisma.DecimalFieldUpdateOperationsInput | runtime.Decimal | runtime.DecimalJsLike | number | string
  longitude?: Prisma.DecimalFieldUpdateOperationsInput | runtime.Decimal | runtime.DecimalJsLike | number | string
  userAgent?: Prisma.NullableStringFieldUpdateOperationsInput | string | null
  dadosAdicionais?: Prisma.NullableJsonNullValueInput | runtime.InputJsonValue
  termoVersao?: Prisma.StringFieldUpdateOperationsInput | string
}

export type AceiteTermosLgpdListRelationFilter = {
  every?: Prisma.AceiteTermosLgpdWhereInput
  some?: Prisma.AceiteTermosLgpdWhereInput
  none?: Prisma.AceiteTermosLgpdWhereInput
}

export type AceiteTermosLgpdOrderByRelationAggregateInput = {
  _count?: Prisma.SortOrder
}

export type AceiteTermosLgpdOrderByRelevanceInput = {
  fields: Prisma.AceiteTermosLgpdOrderByRelevanceFieldEnum | Prisma.AceiteTermosLgpdOrderByRelevanceFieldEnum[]
  sort: Prisma.SortOrder
  search: string
}

export type AceiteTermosLgpdCountOrderByAggregateInput = {
  id?: Prisma.SortOrder
  usuarioId?: Prisma.SortOrder
  termoVersaoId?: Prisma.SortOrder
  consentimentoLgpd?: Prisma.SortOrder
  aceitoEm?: Prisma.SortOrder
  enderecoIp?: Prisma.SortOrder
  latitude?: Prisma.SortOrder
  longitude?: Prisma.SortOrder
  userAgent?: Prisma.SortOrder
  dadosAdicionais?: Prisma.SortOrder
  termoVersao?: Prisma.SortOrder
}

export type AceiteTermosLgpdAvgOrderByAggregateInput = {
  id?: Prisma.SortOrder
  usuarioId?: Prisma.SortOrder
  termoVersaoId?: Prisma.SortOrder
  latitude?: Prisma.SortOrder
  longitude?: Prisma.SortOrder
}

export type AceiteTermosLgpdMaxOrderByAggregateInput = {
  id?: Prisma.SortOrder
  usuarioId?: Prisma.SortOrder
  termoVersaoId?: Prisma.SortOrder
  consentimentoLgpd?: Prisma.SortOrder
  aceitoEm?: Prisma.SortOrder
  enderecoIp?: Prisma.SortOrder
  latitude?: Prisma.SortOrder
  longitude?: Prisma.SortOrder
  userAgent?: Prisma.SortOrder
  termoVersao?: Prisma.SortOrder
}

export type AceiteTermosLgpdMinOrderByAggregateInput = {
  id?: Prisma.SortOrder
  usuarioId?: Prisma.SortOrder
  termoVersaoId?: Prisma.SortOrder
  consentimentoLgpd?: Prisma.SortOrder
  aceitoEm?: Prisma.SortOrder
  enderecoIp?: Prisma.SortOrder
  latitude?: Prisma.SortOrder
  longitude?: Prisma.SortOrder
  userAgent?: Prisma.SortOrder
  termoVersao?: Prisma.SortOrder
}

export type AceiteTermosLgpdSumOrderByAggregateInput = {
  id?: Prisma.SortOrder
  usuarioId?: Prisma.SortOrder
  termoVersaoId?: Prisma.SortOrder
  latitude?: Prisma.SortOrder
  longitude?: Prisma.SortOrder
}

export type AceiteTermosLgpdCreateNestedManyWithoutUsuarioInput = {
  create?: Prisma.XOR<Prisma.AceiteTermosLgpdCreateWithoutUsuarioInput, Prisma.AceiteTermosLgpdUncheckedCreateWithoutUsuarioInput> | Prisma.AceiteTermosLgpdCreateWithoutUsuarioInput[] | Prisma.AceiteTermosLgpdUncheckedCreateWithoutUsuarioInput[]
  connectOrCreate?: Prisma.AceiteTermosLgpdCreateOrConnectWithoutUsuarioInput | Prisma.AceiteTermosLgpdCreateOrConnectWithoutUsuarioInput[]
  createMany?: Prisma.AceiteTermosLgpdCreateManyUsuarioInputEnvelope
  connect?: Prisma.AceiteTermosLgpdWhereUniqueInput | Prisma.AceiteTermosLgpdWhereUniqueInput[]
}

export type AceiteTermosLgpdUncheckedCreateNestedManyWithoutUsuarioInput = {
  create?: Prisma.XOR<Prisma.AceiteTermosLgpdCreateWithoutUsuarioInput, Prisma.AceiteTermosLgpdUncheckedCreateWithoutUsuarioInput> | Prisma.AceiteTermosLgpdCreateWithoutUsuarioInput[] | Prisma.AceiteTermosLgpdUncheckedCreateWithoutUsuarioInput[]
  connectOrCreate?: Prisma.AceiteTermosLgpdCreateOrConnectWithoutUsuarioInput | Prisma.AceiteTermosLgpdCreateOrConnectWithoutUsuarioInput[]
  createMany?: Prisma.AceiteTermosLgpdCreateManyUsuarioInputEnvelope
  connect?: Prisma.AceiteTermosLgpdWhereUniqueInput | Prisma.AceiteTermosLgpdWhereUniqueInput[]
}

export type AceiteTermosLgpdUpdateManyWithoutUsuarioNestedInput = {
  create?: Prisma.XOR<Prisma.AceiteTermosLgpdCreateWithoutUsuarioInput, Prisma.AceiteTermosLgpdUncheckedCreateWithoutUsuarioInput> | Prisma.AceiteTermosLgpdCreateWithoutUsuarioInput[] | Prisma.AceiteTermosLgpdUncheckedCreateWithoutUsuarioInput[]
  connectOrCreate?: Prisma.AceiteTermosLgpdCreateOrConnectWithoutUsuarioInput | Prisma.AceiteTermosLgpdCreateOrConnectWithoutUsuarioInput[]
  upsert?: Prisma.AceiteTermosLgpdUpsertWithWhereUniqueWithoutUsuarioInput | Prisma.AceiteTermosLgpdUpsertWithWhereUniqueWithoutUsuarioInput[]
  createMany?: Prisma.AceiteTermosLgpdCreateManyUsuarioInputEnvelope
  set?: Prisma.AceiteTermosLgpdWhereUniqueInput | Prisma.AceiteTermosLgpdWhereUniqueInput[]
  disconnect?: Prisma.AceiteTermosLgpdWhereUniqueInput | Prisma.AceiteTermosLgpdWhereUniqueInput[]
  delete?: Prisma.AceiteTermosLgpdWhereUniqueInput | Prisma.AceiteTermosLgpdWhereUniqueInput[]
  connect?: Prisma.AceiteTermosLgpdWhereUniqueInput | Prisma.AceiteTermosLgpdWhereUniqueInput[]
  update?: Prisma.AceiteTermosLgpdUpdateWithWhereUniqueWithoutUsuarioInput | Prisma.AceiteTermosLgpdUpdateWithWhereUniqueWithoutUsuarioInput[]
  updateMany?: Prisma.AceiteTermosLgpdUpdateManyWithWhereWithoutUsuarioInput | Prisma.AceiteTermosLgpdUpdateManyWithWhereWithoutUsuarioInput[]
  deleteMany?: Prisma.AceiteTermosLgpdScalarWhereInput | Prisma.AceiteTermosLgpdScalarWhereInput[]
}

export type AceiteTermosLgpdUncheckedUpdateManyWithoutUsuarioNestedInput = {
  create?: Prisma.XOR<Prisma.AceiteTermosLgpdCreateWithoutUsuarioInput, Prisma.AceiteTermosLgpdUncheckedCreateWithoutUsuarioInput> | Prisma.AceiteTermosLgpdCreateWithoutUsuarioInput[] | Prisma.AceiteTermosLgpdUncheckedCreateWithoutUsuarioInput[]
  connectOrCreate?: Prisma.AceiteTermosLgpdCreateOrConnectWithoutUsuarioInput | Prisma.AceiteTermosLgpdCreateOrConnectWithoutUsuarioInput[]
  upsert?: Prisma.AceiteTermosLgpdUpsertWithWhereUniqueWithoutUsuarioInput | Prisma.AceiteTermosLgpdUpsertWithWhereUniqueWithoutUsuarioInput[]
  createMany?: Prisma.AceiteTermosLgpdCreateManyUsuarioInputEnvelope
  set?: Prisma.AceiteTermosLgpdWhereUniqueInput | Prisma.AceiteTermosLgpdWhereUniqueInput[]
  disconnect?: Prisma.AceiteTermosLgpdWhereUniqueInput | Prisma.AceiteTermosLgpdWhereUniqueInput[]
  delete?: Prisma.AceiteTermosLgpdWhereUniqueInput | Prisma.AceiteTermosLgpdWhereUniqueInput[]
  connect?: Prisma.AceiteTermosLgpdWhereUniqueInput | Prisma.AceiteTermosLgpdWhereUniqueInput[]
  update?: Prisma.AceiteTermosLgpdUpdateWithWhereUniqueWithoutUsuarioInput | Prisma.AceiteTermosLgpdUpdateWithWhereUniqueWithoutUsuarioInput[]
  updateMany?: Prisma.AceiteTermosLgpdUpdateManyWithWhereWithoutUsuarioInput | Prisma.AceiteTermosLgpdUpdateManyWithWhereWithoutUsuarioInput[]
  deleteMany?: Prisma.AceiteTermosLgpdScalarWhereInput | Prisma.AceiteTermosLgpdScalarWhereInput[]
}

export type AceiteTermosLgpdCreateNestedManyWithoutTermoInput = {
  create?: Prisma.XOR<Prisma.AceiteTermosLgpdCreateWithoutTermoInput, Prisma.AceiteTermosLgpdUncheckedCreateWithoutTermoInput> | Prisma.AceiteTermosLgpdCreateWithoutTermoInput[] | Prisma.AceiteTermosLgpdUncheckedCreateWithoutTermoInput[]
  connectOrCreate?: Prisma.AceiteTermosLgpdCreateOrConnectWithoutTermoInput | Prisma.AceiteTermosLgpdCreateOrConnectWithoutTermoInput[]
  createMany?: Prisma.AceiteTermosLgpdCreateManyTermoInputEnvelope
  connect?: Prisma.AceiteTermosLgpdWhereUniqueInput | Prisma.AceiteTermosLgpdWhereUniqueInput[]
}

export type AceiteTermosLgpdUncheckedCreateNestedManyWithoutTermoInput = {
  create?: Prisma.XOR<Prisma.AceiteTermosLgpdCreateWithoutTermoInput, Prisma.AceiteTermosLgpdUncheckedCreateWithoutTermoInput> | Prisma.AceiteTermosLgpdCreateWithoutTermoInput[] | Prisma.AceiteTermosLgpdUncheckedCreateWithoutTermoInput[]
  connectOrCreate?: Prisma.AceiteTermosLgpdCreateOrConnectWithoutTermoInput | Prisma.AceiteTermosLgpdCreateOrConnectWithoutTermoInput[]
  createMany?: Prisma.AceiteTermosLgpdCreateManyTermoInputEnvelope
  connect?: Prisma.AceiteTermosLgpdWhereUniqueInput | Prisma.AceiteTermosLgpdWhereUniqueInput[]
}

export type AceiteTermosLgpdUpdateManyWithoutTermoNestedInput = {
  create?: Prisma.XOR<Prisma.AceiteTermosLgpdCreateWithoutTermoInput, Prisma.AceiteTermosLgpdUncheckedCreateWithoutTermoInput> | Prisma.AceiteTermosLgpdCreateWithoutTermoInput[] | Prisma.AceiteTermosLgpdUncheckedCreateWithoutTermoInput[]
  connectOrCreate?: Prisma.AceiteTermosLgpdCreateOrConnectWithoutTermoInput | Prisma.AceiteTermosLgpdCreateOrConnectWithoutTermoInput[]
  upsert?: Prisma.AceiteTermosLgpdUpsertWithWhereUniqueWithoutTermoInput | Prisma.AceiteTermosLgpdUpsertWithWhereUniqueWithoutTermoInput[]
  createMany?: Prisma.AceiteTermosLgpdCreateManyTermoInputEnvelope
  set?: Prisma.AceiteTermosLgpdWhereUniqueInput | Prisma.AceiteTermosLgpdWhereUniqueInput[]
  disconnect?: Prisma.AceiteTermosLgpdWhereUniqueInput | Prisma.AceiteTermosLgpdWhereUniqueInput[]
  delete?: Prisma.AceiteTermosLgpdWhereUniqueInput | Prisma.AceiteTermosLgpdWhereUniqueInput[]
  connect?: Prisma.AceiteTermosLgpdWhereUniqueInput | Prisma.AceiteTermosLgpdWhereUniqueInput[]
  update?: Prisma.AceiteTermosLgpdUpdateWithWhereUniqueWithoutTermoInput | Prisma.AceiteTermosLgpdUpdateWithWhereUniqueWithoutTermoInput[]
  updateMany?: Prisma.AceiteTermosLgpdUpdateManyWithWhereWithoutTermoInput | Prisma.AceiteTermosLgpdUpdateManyWithWhereWithoutTermoInput[]
  deleteMany?: Prisma.AceiteTermosLgpdScalarWhereInput | Prisma.AceiteTermosLgpdScalarWhereInput[]
}

export type AceiteTermosLgpdUncheckedUpdateManyWithoutTermoNestedInput = {
  create?: Prisma.XOR<Prisma.AceiteTermosLgpdCreateWithoutTermoInput, Prisma.AceiteTermosLgpdUncheckedCreateWithoutTermoInput> | Prisma.AceiteTermosLgpdCreateWithoutTermoInput[] | Prisma.AceiteTermosLgpdUncheckedCreateWithoutTermoInput[]
  connectOrCreate?: Prisma.AceiteTermosLgpdCreateOrConnectWithoutTermoInput | Prisma.AceiteTermosLgpdCreateOrConnectWithoutTermoInput[]
  upsert?: Prisma.AceiteTermosLgpdUpsertWithWhereUniqueWithoutTermoInput | Prisma.AceiteTermosLgpdUpsertWithWhereUniqueWithoutTermoInput[]
  createMany?: Prisma.AceiteTermosLgpdCreateManyTermoInputEnvelope
  set?: Prisma.AceiteTermosLgpdWhereUniqueInput | Prisma.AceiteTermosLgpdWhereUniqueInput[]
  disconnect?: Prisma.AceiteTermosLgpdWhereUniqueInput | Prisma.AceiteTermosLgpdWhereUniqueInput[]
  delete?: Prisma.AceiteTermosLgpdWhereUniqueInput | Prisma.AceiteTermosLgpdWhereUniqueInput[]
  connect?: Prisma.AceiteTermosLgpdWhereUniqueInput | Prisma.AceiteTermosLgpdWhereUniqueInput[]
  update?: Prisma.AceiteTermosLgpdUpdateWithWhereUniqueWithoutTermoInput | Prisma.AceiteTermosLgpdUpdateWithWhereUniqueWithoutTermoInput[]
  updateMany?: Prisma.AceiteTermosLgpdUpdateManyWithWhereWithoutTermoInput | Prisma.AceiteTermosLgpdUpdateManyWithWhereWithoutTermoInput[]
  deleteMany?: Prisma.AceiteTermosLgpdScalarWhereInput | Prisma.AceiteTermosLgpdScalarWhereInput[]
}

export type DecimalFieldUpdateOperationsInput = {
  set?: runtime.Decimal | runtime.DecimalJsLike | number | string
  increment?: runtime.Decimal | runtime.DecimalJsLike | number | string
  decrement?: runtime.Decimal | runtime.DecimalJsLike | number | string
  multiply?: runtime.Decimal | runtime.DecimalJsLike | number | string
  divide?: runtime.Decimal | runtime.DecimalJsLike | number | string
}

export type AceiteTermosLgpdCreateWithoutUsuarioInput = {
  consentimentoLgpd?: boolean
  aceitoEm?: Date | string | null
  enderecoIp: string
  latitude: runtime.Decimal | runtime.DecimalJsLike | number | string
  longitude: runtime.Decimal | runtime.DecimalJsLike | number | string
  userAgent?: string | null
  dadosAdicionais?: Prisma.NullableJsonNullValueInput | runtime.InputJsonValue
  termoVersao: string
  termo: Prisma.GerenciamentoTermosLgpdCreateNestedOneWithoutAceiteTermosLgpdInput
}

export type AceiteTermosLgpdUncheckedCreateWithoutUsuarioInput = {
  id?: number
  termoVersaoId: number
  consentimentoLgpd?: boolean
  aceitoEm?: Date | string | null
  enderecoIp: string
  latitude: runtime.Decimal | runtime.DecimalJsLike | number | string
  longitude: runtime.Decimal | runtime.DecimalJsLike | number | string
  userAgent?: string | null
  dadosAdicionais?: Prisma.NullableJsonNullValueInput | runtime.InputJsonValue
  termoVersao: string
}

export type AceiteTermosLgpdCreateOrConnectWithoutUsuarioInput = {
  where: Prisma.AceiteTermosLgpdWhereUniqueInput
  create: Prisma.XOR<Prisma.AceiteTermosLgpdCreateWithoutUsuarioInput, Prisma.AceiteTermosLgpdUncheckedCreateWithoutUsuarioInput>
}

export type AceiteTermosLgpdCreateManyUsuarioInputEnvelope = {
  data: Prisma.AceiteTermosLgpdCreateManyUsuarioInput | Prisma.AceiteTermosLgpdCreateManyUsuarioInput[]
  skipDuplicates?: boolean
}

export type AceiteTermosLgpdUpsertWithWhereUniqueWithoutUsuarioInput = {
  where: Prisma.AceiteTermosLgpdWhereUniqueInput
  update: Prisma.XOR<Prisma.AceiteTermosLgpdUpdateWithoutUsuarioInput, Prisma.AceiteTermosLgpdUncheckedUpdateWithoutUsuarioInput>
  create: Prisma.XOR<Prisma.AceiteTermosLgpdCreateWithoutUsuarioInput, Prisma.AceiteTermosLgpdUncheckedCreateWithoutUsuarioInput>
}

export type AceiteTermosLgpdUpdateWithWhereUniqueWithoutUsuarioInput = {
  where: Prisma.AceiteTermosLgpdWhereUniqueInput
  data: Prisma.XOR<Prisma.AceiteTermosLgpdUpdateWithoutUsuarioInput, Prisma.AceiteTermosLgpdUncheckedUpdateWithoutUsuarioInput>
}

export type AceiteTermosLgpdUpdateManyWithWhereWithoutUsuarioInput = {
  where: Prisma.AceiteTermosLgpdScalarWhereInput
  data: Prisma.XOR<Prisma.AceiteTermosLgpdUpdateManyMutationInput, Prisma.AceiteTermosLgpdUncheckedUpdateManyWithoutUsuarioInput>
}

export type AceiteTermosLgpdScalarWhereInput = {
  AND?: Prisma.AceiteTermosLgpdScalarWhereInput | Prisma.AceiteTermosLgpdScalarWhereInput[]
  OR?: Prisma.AceiteTermosLgpdScalarWhereInput[]
  NOT?: Prisma.AceiteTermosLgpdScalarWhereInput | Prisma.AceiteTermosLgpdScalarWhereInput[]
  id?: Prisma.IntFilter<"AceiteTermosLgpd"> | number
  usuarioId?: Prisma.IntFilter<"AceiteTermosLgpd"> | number
  termoVersaoId?: Prisma.IntFilter<"AceiteTermosLgpd"> | number
  consentimentoLgpd?: Prisma.BoolFilter<"AceiteTermosLgpd"> | boolean
  aceitoEm?: Prisma.DateTimeNullableFilter<"AceiteTermosLgpd"> | Date | string | null
  enderecoIp?: Prisma.StringFilter<"AceiteTermosLgpd"> | string
  latitude?: Prisma.DecimalFilter<"AceiteTermosLgpd"> | runtime.Decimal | runtime.DecimalJsLike | number | string
  longitude?: Prisma.DecimalFilter<"AceiteTermosLgpd"> | runtime.Decimal | runtime.DecimalJsLike | number | string
  userAgent?: Prisma.StringNullableFilter<"AceiteTermosLgpd"> | string | null
  dadosAdicionais?: Prisma.JsonNullableFilter<"AceiteTermosLgpd">
  termoVersao?: Prisma.StringFilter<"AceiteTermosLgpd"> | string
}

export type AceiteTermosLgpdCreateWithoutTermoInput = {
  consentimentoLgpd?: boolean
  aceitoEm?: Date | string | null
  enderecoIp: string
  latitude: runtime.Decimal | runtime.DecimalJsLike | number | string
  longitude: runtime.Decimal | runtime.DecimalJsLike | number | string
  userAgent?: string | null
  dadosAdicionais?: Prisma.NullableJsonNullValueInput | runtime.InputJsonValue
  termoVersao: string
  usuario: Prisma.UsuarioCreateNestedOneWithoutAceiteTermosLgpdInput
}

export type AceiteTermosLgpdUncheckedCreateWithoutTermoInput = {
  id?: number
  usuarioId: number
  consentimentoLgpd?: boolean
  aceitoEm?: Date | string | null
  enderecoIp: string
  latitude: runtime.Decimal | runtime.DecimalJsLike | number | string
  longitude: runtime.Decimal | runtime.DecimalJsLike | number | string
  userAgent?: string | null
  dadosAdicionais?: Prisma.NullableJsonNullValueInput | runtime.InputJsonValue
  termoVersao: string
}

export type AceiteTermosLgpdCreateOrConnectWithoutTermoInput = {
  where: Prisma.AceiteTermosLgpdWhereUniqueInput
  create: Prisma.XOR<Prisma.AceiteTermosLgpdCreateWithoutTermoInput, Prisma.AceiteTermosLgpdUncheckedCreateWithoutTermoInput>
}

export type AceiteTermosLgpdCreateManyTermoInputEnvelope = {
  data: Prisma.AceiteTermosLgpdCreateManyTermoInput | Prisma.AceiteTermosLgpdCreateManyTermoInput[]
  skipDuplicates?: boolean
}

export type AceiteTermosLgpdUpsertWithWhereUniqueWithoutTermoInput = {
  where: Prisma.AceiteTermosLgpdWhereUniqueInput
  update: Prisma.XOR<Prisma.AceiteTermosLgpdUpdateWithoutTermoInput, Prisma.AceiteTermosLgpdUncheckedUpdateWithoutTermoInput>
  create: Prisma.XOR<Prisma.AceiteTermosLgpdCreateWithoutTermoInput, Prisma.AceiteTermosLgpdUncheckedCreateWithoutTermoInput>
}

export type AceiteTermosLgpdUpdateWithWhereUniqueWithoutTermoInput = {
  where: Prisma.AceiteTermosLgpdWhereUniqueInput
  data: Prisma.XOR<Prisma.AceiteTermosLgpdUpdateWithoutTermoInput, Prisma.AceiteTermosLgpdUncheckedUpdateWithoutTermoInput>
}

export type AceiteTermosLgpdUpdateManyWithWhereWithoutTermoInput = {
  where: Prisma.AceiteTermosLgpdScalarWhereInput
  data: Prisma.XOR<Prisma.AceiteTermosLgpdUpdateManyMutationInput, Prisma.AceiteTermosLgpdUncheckedUpdateManyWithoutTermoInput>
}

export type AceiteTermosLgpdCreateManyUsuarioInput = {
  id?: number
  termoVersaoId: number
  consentimentoLgpd?: boolean
  aceitoEm?: Date | string | null
  enderecoIp: string
  latitude: runtime.Decimal | runtime.DecimalJsLike | number | string
  longitude: runtime.Decimal | runtime.DecimalJsLike | number | string
  userAgent?: string | null
  dadosAdicionais?: Prisma.NullableJsonNullValueInput | runtime.InputJsonValue
  termoVersao: string
}

export type AceiteTermosLgpdUpdateWithoutUsuarioInput = {
  consentimentoLgpd?: Prisma.BoolFieldUpdateOperationsInput | boolean
  aceitoEm?: Prisma.NullableDateTimeFieldUpdateOperationsInput | Date | string | null
  enderecoIp?: Prisma.StringFieldUpdateOperationsInput | string
  latitude?: Prisma.DecimalFieldUpdateOperationsInput | runtime.Decimal | runtime.DecimalJsLike | number | string
  longitude?: Prisma.DecimalFieldUpdateOperationsInput | runtime.Decimal | runtime.DecimalJsLike | number | string
  userAgent?: Prisma.NullableStringFieldUpdateOperationsInput | string | null
  dadosAdicionais?: Prisma.NullableJsonNullValueInput | runtime.InputJsonValue
  termoVersao?: Prisma.StringFieldUpdateOperationsInput | string
  termo?: Prisma.GerenciamentoTermosLgpdUpdateOneRequiredWithoutAceiteTermosLgpdNestedInput
}

export type AceiteTermosLgpdUncheckedUpdateWithoutUsuarioInput = {
  id?: Prisma.IntFieldUpdateOperationsInput | number
  termoVersaoId?: Prisma.IntFieldUpdateOperationsInput | number
  consentimentoLgpd?: Prisma.BoolFieldUpdateOperationsInput | boolean
  aceitoEm?: Prisma.NullableDateTimeFieldUpdateOperationsInput | Date | string | null
  enderecoIp?: Prisma.StringFieldUpdateOperationsInput | string
  latitude?: Prisma.DecimalFieldUpdateOperationsInput | runtime.Decimal | runtime.DecimalJsLike | number | string
  longitude?: Prisma.DecimalFieldUpdateOperationsInput | runtime.Decimal | runtime.DecimalJsLike | number | string
  userAgent?: Prisma.NullableStringFieldUpdateOperationsInput | string | null
  dadosAdicionais?: Prisma.NullableJsonNullValueInput | runtime.InputJsonValue
  termoVersao?: Prisma.StringFieldUpdateOperationsInput | string
}

export type AceiteTermosLgpdUncheckedUpdateManyWithoutUsuarioInput = {
  id?: Prisma.IntFieldUpdateOperationsInput | number
  termoVersaoId?: Prisma.IntFieldUpdateOperationsInput | number
  consentimentoLgpd?: Prisma.BoolFieldUpdateOperationsInput | boolean
  aceitoEm?: Prisma.NullableDateTimeFieldUpdateOperationsInput | Date | string | null
  enderecoIp?: Prisma.StringFieldUpdateOperationsInput | string
  latitude?: Prisma.DecimalFieldUpdateOperationsInput | runtime.Decimal | runtime.DecimalJsLike | number | string
  longitude?: Prisma.DecimalFieldUpdateOperationsInput | runtime.Decimal | runtime.DecimalJsLike | number | string
  userAgent?: Prisma.NullableStringFieldUpdateOperationsInput | string | null
  dadosAdicionais?: Prisma.NullableJsonNullValueInput | runtime.InputJsonValue
  termoVersao?: Prisma.StringFieldUpdateOperationsInput | string
}

export type AceiteTermosLgpdCreateManyTermoInput = {
  id?: number
  usuarioId: number
  consentimentoLgpd?: boolean
  aceitoEm?: Date | string | null
  enderecoIp: string
  latitude: runtime.Decimal | runtime.DecimalJsLike | number | string
  longitude: runtime.Decimal | runtime.DecimalJsLike | number | string
  userAgent?: string | null
  dadosAdicionais?: Prisma.NullableJsonNullValueInput | runtime.InputJsonValue
  termoVersao: string
}

export type AceiteTermosLgpdUpdateWithoutTermoInput = {
  consentimentoLgpd?: Prisma.BoolFieldUpdateOperationsInput | boolean
  aceitoEm?: Prisma.NullableDateTimeFieldUpdateOperationsInput | Date | string | null
  enderecoIp?: Prisma.StringFieldUpdateOperationsInput | string
  latitude?: Prisma.DecimalFieldUpdateOperationsInput | runtime.Decimal | runtime.DecimalJsLike | number | string
  longitude?: Prisma.DecimalFieldUpdateOperationsInput | runtime.Decimal | runtime.DecimalJsLike | number | string
  userAgent?: Prisma.NullableStringFieldUpdateOperationsInput | string | null
  dadosAdicionais?: Prisma.NullableJsonNullValueInput | runtime.InputJsonValue
  termoVersao?: Prisma.StringFieldUpdateOperationsInput | string
  usuario?: Prisma.UsuarioUpdateOneRequiredWithoutAceiteTermosLgpdNestedInput
}

export type AceiteTermosLgpdUncheckedUpdateWithoutTermoInput = {
  id?: Prisma.IntFieldUpdateOperationsInput | number
  usuarioId?: Prisma.IntFieldUpdateOperationsInput | number
  consentimentoLgpd?: Prisma.BoolFieldUpdateOperationsInput | boolean
  aceitoEm?: Prisma.NullableDateTimeFieldUpdateOperationsInput | Date | string | null
  enderecoIp?: Prisma.StringFieldUpdateOperationsInput | string
  latitude?: Prisma.DecimalFieldUpdateOperationsInput | runtime.Decimal | runtime.DecimalJsLike | number | string
  longitude?: Prisma.DecimalFieldUpdateOperationsInput | runtime.Decimal | runtime.DecimalJsLike | number | string
  userAgent?: Prisma.NullableStringFieldUpdateOperationsInput | string | null
  dadosAdicionais?: Prisma.NullableJsonNullValueInput | runtime.InputJsonValue
  termoVersao?: Prisma.StringFieldUpdateOperationsInput | string
}

export type AceiteTermosLgpdUncheckedUpdateManyWithoutTermoInput = {
  id?: Prisma.IntFieldUpdateOperationsInput | number
  usuarioId?: Prisma.IntFieldUpdateOperationsInput | number
  consentimentoLgpd?: Prisma.BoolFieldUpdateOperationsInput | boolean
  aceitoEm?: Prisma.NullableDateTimeFieldUpdateOperationsInput | Date | string | null
  enderecoIp?: Prisma.StringFieldUpdateOperationsInput | string
  latitude?: Prisma.DecimalFieldUpdateOperationsInput | runtime.Decimal | runtime.DecimalJsLike | number | string
  longitude?: Prisma.DecimalFieldUpdateOperationsInput | runtime.Decimal | runtime.DecimalJsLike | number | string
  userAgent?: Prisma.NullableStringFieldUpdateOperationsInput | string | null
  dadosAdicionais?: Prisma.NullableJsonNullValueInput | runtime.InputJsonValue
  termoVersao?: Prisma.StringFieldUpdateOperationsInput | string
}



export type AceiteTermosLgpdSelect<ExtArgs extends runtime.Types.Extensions.InternalArgs = runtime.Types.Extensions.DefaultArgs> = runtime.Types.Extensions.GetSelect<{
  id?: boolean
  usuarioId?: boolean
  termoVersaoId?: boolean
  consentimentoLgpd?: boolean
  aceitoEm?: boolean
  enderecoIp?: boolean
  latitude?: boolean
  longitude?: boolean
  userAgent?: boolean
  dadosAdicionais?: boolean
  termoVersao?: boolean
  termo?: boolean | Prisma.GerenciamentoTermosLgpdDefaultArgs<ExtArgs>
  usuario?: boolean | Prisma.UsuarioDefaultArgs<ExtArgs>
}, ExtArgs["result"]["aceiteTermosLgpd"]>



export type AceiteTermosLgpdSelectScalar = {
  id?: boolean
  usuarioId?: boolean
  termoVersaoId?: boolean
  consentimentoLgpd?: boolean
  aceitoEm?: boolean
  enderecoIp?: boolean
  latitude?: boolean
  longitude?: boolean
  userAgent?: boolean
  dadosAdicionais?: boolean
  termoVersao?: boolean
}

export type AceiteTermosLgpdOmit<ExtArgs extends runtime.Types.Extensions.InternalArgs = runtime.Types.Extensions.DefaultArgs> = runtime.Types.Extensions.GetOmit<"id" | "usuarioId" | "termoVersaoId" | "consentimentoLgpd" | "aceitoEm" | "enderecoIp" | "latitude" | "longitude" | "userAgent" | "dadosAdicionais" | "termoVersao", ExtArgs["result"]["aceiteTermosLgpd"]>
export type AceiteTermosLgpdInclude<ExtArgs extends runtime.Types.Extensions.InternalArgs = runtime.Types.Extensions.DefaultArgs> = {
  termo?: boolean | Prisma.GerenciamentoTermosLgpdDefaultArgs<ExtArgs>
  usuario?: boolean | Prisma.UsuarioDefaultArgs<ExtArgs>
}

export type $AceiteTermosLgpdPayload<ExtArgs extends runtime.Types.Extensions.InternalArgs = runtime.Types.Extensions.DefaultArgs> = {
  name: "AceiteTermosLgpd"
  objects: {
    termo: Prisma.$GerenciamentoTermosLgpdPayload<ExtArgs>
    usuario: Prisma.$UsuarioPayload<ExtArgs>
  }
  scalars: runtime.Types.Extensions.GetPayloadResult<{
    id: number
    usuarioId: number
    termoVersaoId: number
    consentimentoLgpd: boolean
    aceitoEm: Date | null
    enderecoIp: string
    latitude: runtime.Decimal
    longitude: runtime.Decimal
    userAgent: string | null
    dadosAdicionais: runtime.JsonValue | null
    termoVersao: string
  }, ExtArgs["result"]["aceiteTermosLgpd"]>
  composites: {}
}

export type AceiteTermosLgpdGetPayload<S extends boolean | null | undefined | AceiteTermosLgpdDefaultArgs> = runtime.Types.Result.GetResult<Prisma.$AceiteTermosLgpdPayload, S>

export type AceiteTermosLgpdCountArgs<ExtArgs extends runtime.Types.Extensions.InternalArgs = runtime.Types.Extensions.DefaultArgs> =
  Omit<AceiteTermosLgpdFindManyArgs, 'select' | 'include' | 'distinct' | 'omit'> & {
    select?: AceiteTermosLgpdCountAggregateInputType | true
  }

export interface AceiteTermosLgpdDelegate<ExtArgs extends runtime.Types.Extensions.InternalArgs = runtime.Types.Extensions.DefaultArgs, GlobalOmitOptions = {}> {
  [K: symbol]: { types: Prisma.TypeMap<ExtArgs>['model']['AceiteTermosLgpd'], meta: { name: 'AceiteTermosLgpd' } }
  /**
   * Find zero or one AceiteTermosLgpd that matches the filter.
   * @param {AceiteTermosLgpdFindUniqueArgs} args - Arguments to find a AceiteTermosLgpd
   * @example
   * // Get one AceiteTermosLgpd
   * const aceiteTermosLgpd = await prisma.aceiteTermosLgpd.findUnique({
   *   where: {
   *     // ... provide filter here
   *   }
   * })
   */
  findUnique<T extends AceiteTermosLgpdFindUniqueArgs>(args: Prisma.SelectSubset<T, AceiteTermosLgpdFindUniqueArgs<ExtArgs>>): Prisma.Prisma__AceiteTermosLgpdClient<runtime.Types.Result.GetResult<Prisma.$AceiteTermosLgpdPayload<ExtArgs>, T, "findUnique", GlobalOmitOptions> | null, null, ExtArgs, GlobalOmitOptions>

  /**
   * Find one AceiteTermosLgpd that matches the filter or throw an error with `error.code='P2025'`
   * if no matches were found.
   * @param {AceiteTermosLgpdFindUniqueOrThrowArgs} args - Arguments to find a AceiteTermosLgpd
   * @example
   * // Get one AceiteTermosLgpd
   * const aceiteTermosLgpd = await prisma.aceiteTermosLgpd.findUniqueOrThrow({
   *   where: {
   *     // ... provide filter here
   *   }
   * })
   */
  findUniqueOrThrow<T extends AceiteTermosLgpdFindUniqueOrThrowArgs>(args: Prisma.SelectSubset<T, AceiteTermosLgpdFindUniqueOrThrowArgs<ExtArgs>>): Prisma.Prisma__AceiteTermosLgpdClient<runtime.Types.Result.GetResult<Prisma.$AceiteTermosLgpdPayload<ExtArgs>, T, "findUniqueOrThrow", GlobalOmitOptions>, never, ExtArgs, GlobalOmitOptions>

  /**
   * Find the first AceiteTermosLgpd that matches the filter.
   * Note, that providing `undefined` is treated as the value not being there.
   * Read more here: https://pris.ly/d/null-undefined
   * @param {AceiteTermosLgpdFindFirstArgs} args - Arguments to find a AceiteTermosLgpd
   * @example
   * // Get one AceiteTermosLgpd
   * const aceiteTermosLgpd = await prisma.aceiteTermosLgpd.findFirst({
   *   where: {
   *     // ... provide filter here
   *   }
   * })
   */
  findFirst<T extends AceiteTermosLgpdFindFirstArgs>(args?: Prisma.SelectSubset<T, AceiteTermosLgpdFindFirstArgs<ExtArgs>>): Prisma.Prisma__AceiteTermosLgpdClient<runtime.Types.Result.GetResult<Prisma.$AceiteTermosLgpdPayload<ExtArgs>, T, "findFirst", GlobalOmitOptions> | null, null, ExtArgs, GlobalOmitOptions>

  /**
   * Find the first AceiteTermosLgpd that matches the filter or
   * throw `PrismaKnownClientError` with `P2025` code if no matches were found.
   * Note, that providing `undefined` is treated as the value not being there.
   * Read more here: https://pris.ly/d/null-undefined
   * @param {AceiteTermosLgpdFindFirstOrThrowArgs} args - Arguments to find a AceiteTermosLgpd
   * @example
   * // Get one AceiteTermosLgpd
   * const aceiteTermosLgpd = await prisma.aceiteTermosLgpd.findFirstOrThrow({
   *   where: {
   *     // ... provide filter here
   *   }
   * })
   */
  findFirstOrThrow<T extends AceiteTermosLgpdFindFirstOrThrowArgs>(args?: Prisma.SelectSubset<T, AceiteTermosLgpdFindFirstOrThrowArgs<ExtArgs>>): Prisma.Prisma__AceiteTermosLgpdClient<runtime.Types.Result.GetResult<Prisma.$AceiteTermosLgpdPayload<ExtArgs>, T, "findFirstOrThrow", GlobalOmitOptions>, never, ExtArgs, GlobalOmitOptions>

  /**
   * Find zero or more AceiteTermosLgpds that matches the filter.
   * Note, that providing `undefined` is treated as the value not being there.
   * Read more here: https://pris.ly/d/null-undefined
   * @param {AceiteTermosLgpdFindManyArgs} args - Arguments to filter and select certain fields only.
   * @example
   * // Get all AceiteTermosLgpds
   * const aceiteTermosLgpds = await prisma.aceiteTermosLgpd.findMany()
   * 
   * // Get first 10 AceiteTermosLgpds
   * const aceiteTermosLgpds = await prisma.aceiteTermosLgpd.findMany({ take: 10 })
   * 
   * // Only select the `id`
   * const aceiteTermosLgpdWithIdOnly = await prisma.aceiteTermosLgpd.findMany({ select: { id: true } })
   * 
   */
  findMany<T extends AceiteTermosLgpdFindManyArgs>(args?: Prisma.SelectSubset<T, AceiteTermosLgpdFindManyArgs<ExtArgs>>): Prisma.PrismaPromise<runtime.Types.Result.GetResult<Prisma.$AceiteTermosLgpdPayload<ExtArgs>, T, "findMany", GlobalOmitOptions>>

  /**
   * Create a AceiteTermosLgpd.
   * @param {AceiteTermosLgpdCreateArgs} args - Arguments to create a AceiteTermosLgpd.
   * @example
   * // Create one AceiteTermosLgpd
   * const AceiteTermosLgpd = await prisma.aceiteTermosLgpd.create({
   *   data: {
   *     // ... data to create a AceiteTermosLgpd
   *   }
   * })
   * 
   */
  create<T extends AceiteTermosLgpdCreateArgs>(args: Prisma.SelectSubset<T, AceiteTermosLgpdCreateArgs<ExtArgs>>): Prisma.Prisma__AceiteTermosLgpdClient<runtime.Types.Result.GetResult<Prisma.$AceiteTermosLgpdPayload<ExtArgs>, T, "create", GlobalOmitOptions>, never, ExtArgs, GlobalOmitOptions>

  /**
   * Create many AceiteTermosLgpds.
   * @param {AceiteTermosLgpdCreateManyArgs} args - Arguments to create many AceiteTermosLgpds.
   * @example
   * // Create many AceiteTermosLgpds
   * const aceiteTermosLgpd = await prisma.aceiteTermosLgpd.createMany({
   *   data: [
   *     // ... provide data here
   *   ]
   * })
   *     
   */
  createMany<T extends AceiteTermosLgpdCreateManyArgs>(args?: Prisma.SelectSubset<T, AceiteTermosLgpdCreateManyArgs<ExtArgs>>): Prisma.PrismaPromise<Prisma.BatchPayload>

  /**
   * Delete a AceiteTermosLgpd.
   * @param {AceiteTermosLgpdDeleteArgs} args - Arguments to delete one AceiteTermosLgpd.
   * @example
   * // Delete one AceiteTermosLgpd
   * const AceiteTermosLgpd = await prisma.aceiteTermosLgpd.delete({
   *   where: {
   *     // ... filter to delete one AceiteTermosLgpd
   *   }
   * })
   * 
   */
  delete<T extends AceiteTermosLgpdDeleteArgs>(args: Prisma.SelectSubset<T, AceiteTermosLgpdDeleteArgs<ExtArgs>>): Prisma.Prisma__AceiteTermosLgpdClient<runtime.Types.Result.GetResult<Prisma.$AceiteTermosLgpdPayload<ExtArgs>, T, "delete", GlobalOmitOptions>, never, ExtArgs, GlobalOmitOptions>

  /**
   * Update one AceiteTermosLgpd.
   * @param {AceiteTermosLgpdUpdateArgs} args - Arguments to update one AceiteTermosLgpd.
   * @example
   * // Update one AceiteTermosLgpd
   * const aceiteTermosLgpd = await prisma.aceiteTermosLgpd.update({
   *   where: {
   *     // ... provide filter here
   *   },
   *   data: {
   *     // ... provide data here
   *   }
   * })
   * 
   */
  update<T extends AceiteTermosLgpdUpdateArgs>(args: Prisma.SelectSubset<T, AceiteTermosLgpdUpdateArgs<ExtArgs>>): Prisma.Prisma__AceiteTermosLgpdClient<runtime.Types.Result.GetResult<Prisma.$AceiteTermosLgpdPayload<ExtArgs>, T, "update", GlobalOmitOptions>, never, ExtArgs, GlobalOmitOptions>

  /**
   * Delete zero or more AceiteTermosLgpds.
   * @param {AceiteTermosLgpdDeleteManyArgs} args - Arguments to filter AceiteTermosLgpds to delete.
   * @example
   * // Delete a few AceiteTermosLgpds
   * const { count } = await prisma.aceiteTermosLgpd.deleteMany({
   *   where: {
   *     // ... provide filter here
   *   }
   * })
   * 
   */
  deleteMany<T extends AceiteTermosLgpdDeleteManyArgs>(args?: Prisma.SelectSubset<T, AceiteTermosLgpdDeleteManyArgs<ExtArgs>>): Prisma.PrismaPromise<Prisma.BatchPayload>

  /**
   * Update zero or more AceiteTermosLgpds.
   * Note, that providing `undefined` is treated as the value not being there.
   * Read more here: https://pris.ly/d/null-undefined
   * @param {AceiteTermosLgpdUpdateManyArgs} args - Arguments to update one or more rows.
   * @example
   * // Update many AceiteTermosLgpds
   * const aceiteTermosLgpd = await prisma.aceiteTermosLgpd.updateMany({
   *   where: {
   *     // ... provide filter here
   *   },
   *   data: {
   *     // ... provide data here
   *   }
   * })
   * 
   */
  updateMany<T extends AceiteTermosLgpdUpdateManyArgs>(args: Prisma.SelectSubset<T, AceiteTermosLgpdUpdateManyArgs<ExtArgs>>): Prisma.PrismaPromise<Prisma.BatchPayload>

  /**
   * Create or update one AceiteTermosLgpd.
   * @param {AceiteTermosLgpdUpsertArgs} args - Arguments to update or create a AceiteTermosLgpd.
   * @example
   * // Update or create a AceiteTermosLgpd
   * const aceiteTermosLgpd = await prisma.aceiteTermosLgpd.upsert({
   *   create: {
   *     // ... data to create a AceiteTermosLgpd
   *   },
   *   update: {
   *     // ... in case it already exists, update
   *   },
   *   where: {
   *     // ... the filter for the AceiteTermosLgpd we want to update
   *   }
   * })
   */
  upsert<T extends AceiteTermosLgpdUpsertArgs>(args: Prisma.SelectSubset<T, AceiteTermosLgpdUpsertArgs<ExtArgs>>): Prisma.Prisma__AceiteTermosLgpdClient<runtime.Types.Result.GetResult<Prisma.$AceiteTermosLgpdPayload<ExtArgs>, T, "upsert", GlobalOmitOptions>, never, ExtArgs, GlobalOmitOptions>


  /**
   * Count the number of AceiteTermosLgpds.
   * Note, that providing `undefined` is treated as the value not being there.
   * Read more here: https://pris.ly/d/null-undefined
   * @param {AceiteTermosLgpdCountArgs} args - Arguments to filter AceiteTermosLgpds to count.
   * @example
   * // Count the number of AceiteTermosLgpds
   * const count = await prisma.aceiteTermosLgpd.count({
   *   where: {
   *     // ... the filter for the AceiteTermosLgpds we want to count
   *   }
   * })
  **/
  count<T extends AceiteTermosLgpdCountArgs>(
    args?: Prisma.Subset<T, AceiteTermosLgpdCountArgs>,
  ): Prisma.PrismaPromise<
    T extends runtime.Types.Utils.Record<'select', any>
      ? T['select'] extends true
        ? number
        : Prisma.GetScalarType<T['select'], AceiteTermosLgpdCountAggregateOutputType>
      : number
  >

  /**
   * Allows you to perform aggregations operations on a AceiteTermosLgpd.
   * Note, that providing `undefined` is treated as the value not being there.
   * Read more here: https://pris.ly/d/null-undefined
   * @param {AceiteTermosLgpdAggregateArgs} args - Select which aggregations you would like to apply and on what fields.
   * @example
   * // Ordered by age ascending
   * // Where email contains prisma.io
   * // Limited to the 10 users
   * const aggregations = await prisma.user.aggregate({
   *   _avg: {
   *     age: true,
   *   },
   *   where: {
   *     email: {
   *       contains: "prisma.io",
   *     },
   *   },
   *   orderBy: {
   *     age: "asc",
   *   },
   *   take: 10,
   * })
  **/
  aggregate<T extends AceiteTermosLgpdAggregateArgs>(args: Prisma.Subset<T, AceiteTermosLgpdAggregateArgs>): Prisma.PrismaPromise<GetAceiteTermosLgpdAggregateType<T>>

  /**
   * Group by AceiteTermosLgpd.
   * Note, that providing `undefined` is treated as the value not being there.
   * Read more here: https://pris.ly/d/null-undefined
   * @param {AceiteTermosLgpdGroupByArgs} args - Group by arguments.
   * @example
   * // Group by city, order by createdAt, get count
   * const result = await prisma.user.groupBy({
   *   by: ['city', 'createdAt'],
   *   orderBy: {
   *     createdAt: true
   *   },
   *   _count: {
   *     _all: true
   *   },
   * })
   * 
  **/
  groupBy<
    T extends AceiteTermosLgpdGroupByArgs,
    HasSelectOrTake extends Prisma.Or<
      Prisma.Extends<'skip', Prisma.Keys<T>>,
      Prisma.Extends<'take', Prisma.Keys<T>>
    >,
    OrderByArg extends Prisma.True extends HasSelectOrTake
      ? { orderBy: AceiteTermosLgpdGroupByArgs['orderBy'] }
      : { orderBy?: AceiteTermosLgpdGroupByArgs['orderBy'] },
    OrderFields extends Prisma.ExcludeUnderscoreKeys<Prisma.Keys<Prisma.MaybeTupleToUnion<T['orderBy']>>>,
    ByFields extends Prisma.MaybeTupleToUnion<T['by']>,
    ByValid extends Prisma.Has<ByFields, OrderFields>,
    HavingFields extends Prisma.GetHavingFields<T['having']>,
    HavingValid extends Prisma.Has<ByFields, HavingFields>,
    ByEmpty extends T['by'] extends never[] ? Prisma.True : Prisma.False,
    InputErrors extends ByEmpty extends Prisma.True
    ? `Error: "by" must not be empty.`
    : HavingValid extends Prisma.False
    ? {
        [P in HavingFields]: P extends ByFields
          ? never
          : P extends string
          ? `Error: Field "${P}" used in "having" needs to be provided in "by".`
          : [
              Error,
              'Field ',
              P,
              ` in "having" needs to be provided in "by"`,
            ]
      }[HavingFields]
    : 'take' extends Prisma.Keys<T>
    ? 'orderBy' extends Prisma.Keys<T>
      ? ByValid extends Prisma.True
        ? {}
        : {
            [P in OrderFields]: P extends ByFields
              ? never
              : `Error: Field "${P}" in "orderBy" needs to be provided in "by"`
          }[OrderFields]
      : 'Error: If you provide "take", you also need to provide "orderBy"'
    : 'skip' extends Prisma.Keys<T>
    ? 'orderBy' extends Prisma.Keys<T>
      ? ByValid extends Prisma.True
        ? {}
        : {
            [P in OrderFields]: P extends ByFields
              ? never
              : `Error: Field "${P}" in "orderBy" needs to be provided in "by"`
          }[OrderFields]
      : 'Error: If you provide "skip", you also need to provide "orderBy"'
    : ByValid extends Prisma.True
    ? {}
    : {
        [P in OrderFields]: P extends ByFields
          ? never
          : `Error: Field "${P}" in "orderBy" needs to be provided in "by"`
      }[OrderFields]
  >(args: Prisma.SubsetIntersection<T, AceiteTermosLgpdGroupByArgs, OrderByArg> & InputErrors): {} extends InputErrors ? GetAceiteTermosLgpdGroupByPayload<T> : Prisma.PrismaPromise<InputErrors>
/**
 * Fields of the AceiteTermosLgpd model
 */
readonly fields: AceiteTermosLgpdFieldRefs;
}

/**
 * The delegate class that acts as a "Promise-like" for AceiteTermosLgpd.
 * Why is this prefixed with `Prisma__`?
 * Because we want to prevent naming conflicts as mentioned in
 * https://github.com/prisma/prisma-client-js/issues/707
 */
export interface Prisma__AceiteTermosLgpdClient<T, Null = never, ExtArgs extends runtime.Types.Extensions.InternalArgs = runtime.Types.Extensions.DefaultArgs, GlobalOmitOptions = {}> extends Prisma.PrismaPromise<T> {
  readonly [Symbol.toStringTag]: "PrismaPromise"
  termo<T extends Prisma.GerenciamentoTermosLgpdDefaultArgs<ExtArgs> = {}>(args?: Prisma.Subset<T, Prisma.GerenciamentoTermosLgpdDefaultArgs<ExtArgs>>): Prisma.Prisma__GerenciamentoTermosLgpdClient<runtime.Types.Result.GetResult<Prisma.$GerenciamentoTermosLgpdPayload<ExtArgs>, T, "findUniqueOrThrow", GlobalOmitOptions> | Null, Null, ExtArgs, GlobalOmitOptions>
  usuario<T extends Prisma.UsuarioDefaultArgs<ExtArgs> = {}>(args?: Prisma.Subset<T, Prisma.UsuarioDefaultArgs<ExtArgs>>): Prisma.Prisma__UsuarioClient<runtime.Types.Result.GetResult<Prisma.$UsuarioPayload<ExtArgs>, T, "findUniqueOrThrow", GlobalOmitOptions> | Null, Null, ExtArgs, GlobalOmitOptions>
  /**
   * Attaches callbacks for the resolution and/or rejection of the Promise.
   * @param onfulfilled The callback to execute when the Promise is resolved.
   * @param onrejected The callback to execute when the Promise is rejected.
   * @returns A Promise for the completion of which ever callback is executed.
   */
  then<TResult1 = T, TResult2 = never>(onfulfilled?: ((value: T) => TResult1 | PromiseLike<TResult1>) | undefined | null, onrejected?: ((reason: any) => TResult2 | PromiseLike<TResult2>) | undefined | null): runtime.Types.Utils.JsPromise<TResult1 | TResult2>
  /**
   * Attaches a callback for only the rejection of the Promise.
   * @param onrejected The callback to execute when the Promise is rejected.
   * @returns A Promise for the completion of the callback.
   */
  catch<TResult = never>(onrejected?: ((reason: any) => TResult | PromiseLike<TResult>) | undefined | null): runtime.Types.Utils.JsPromise<T | TResult>
  /**
   * Attaches a callback that is invoked when the Promise is settled (fulfilled or rejected). The
   * resolved value cannot be modified from the callback.
   * @param onfinally The callback to execute when the Promise is settled (fulfilled or rejected).
   * @returns A Promise for the completion of the callback.
   */
  finally(onfinally?: (() => void) | undefined | null): runtime.Types.Utils.JsPromise<T>
}




/**
 * Fields of the AceiteTermosLgpd model
 */
export interface AceiteTermosLgpdFieldRefs {
  readonly id: Prisma.FieldRef<"AceiteTermosLgpd", 'Int'>
  readonly usuarioId: Prisma.FieldRef<"AceiteTermosLgpd", 'Int'>
  readonly termoVersaoId: Prisma.FieldRef<"AceiteTermosLgpd", 'Int'>
  readonly consentimentoLgpd: Prisma.FieldRef<"AceiteTermosLgpd", 'Boolean'>
  readonly aceitoEm: Prisma.FieldRef<"AceiteTermosLgpd", 'DateTime'>
  readonly enderecoIp: Prisma.FieldRef<"AceiteTermosLgpd", 'String'>
  readonly latitude: Prisma.FieldRef<"AceiteTermosLgpd", 'Decimal'>
  readonly longitude: Prisma.FieldRef<"AceiteTermosLgpd", 'Decimal'>
  readonly userAgent: Prisma.FieldRef<"AceiteTermosLgpd", 'String'>
  readonly dadosAdicionais: Prisma.FieldRef<"AceiteTermosLgpd", 'Json'>
  readonly termoVersao: Prisma.FieldRef<"AceiteTermosLgpd", 'String'>
}
    

// Custom InputTypes
/**
 * AceiteTermosLgpd findUnique
 */
export type AceiteTermosLgpdFindUniqueArgs<ExtArgs extends runtime.Types.Extensions.InternalArgs = runtime.Types.Extensions.DefaultArgs> = {
  /**
   * Select specific fields to fetch from the AceiteTermosLgpd
   */
  select?: Prisma.AceiteTermosLgpdSelect<ExtArgs> | null
  /**
   * Omit specific fields from the AceiteTermosLgpd
   */
  omit?: Prisma.AceiteTermosLgpdOmit<ExtArgs> | null
  /**
   * Choose, which related nodes to fetch as well
   */
  include?: Prisma.AceiteTermosLgpdInclude<ExtArgs> | null
  /**
   * Filter, which AceiteTermosLgpd to fetch.
   */
  where: Prisma.AceiteTermosLgpdWhereUniqueInput
}

/**
 * AceiteTermosLgpd findUniqueOrThrow
 */
export type AceiteTermosLgpdFindUniqueOrThrowArgs<ExtArgs extends runtime.Types.Extensions.InternalArgs = runtime.Types.Extensions.DefaultArgs> = {
  /**
   * Select specific fields to fetch from the AceiteTermosLgpd
   */
  select?: Prisma.AceiteTermosLgpdSelect<ExtArgs> | null
  /**
   * Omit specific fields from the AceiteTermosLgpd
   */
  omit?: Prisma.AceiteTermosLgpdOmit<ExtArgs> | null
  /**
   * Choose, which related nodes to fetch as well
   */
  include?: Prisma.AceiteTermosLgpdInclude<ExtArgs> | null
  /**
   * Filter, which AceiteTermosLgpd to fetch.
   */
  where: Prisma.AceiteTermosLgpdWhereUniqueInput
}

/**
 * AceiteTermosLgpd findFirst
 */
export type AceiteTermosLgpdFindFirstArgs<ExtArgs extends runtime.Types.Extensions.InternalArgs = runtime.Types.Extensions.DefaultArgs> = {
  /**
   * Select specific fields to fetch from the AceiteTermosLgpd
   */
  select?: Prisma.AceiteTermosLgpdSelect<ExtArgs> | null
  /**
   * Omit specific fields from the AceiteTermosLgpd
   */
  omit?: Prisma.AceiteTermosLgpdOmit<ExtArgs> | null
  /**
   * Choose, which related nodes to fetch as well
   */
  include?: Prisma.AceiteTermosLgpdInclude<ExtArgs> | null
  /**
   * Filter, which AceiteTermosLgpd to fetch.
   */
  where?: Prisma.AceiteTermosLgpdWhereInput
  /**
   * {@link https://www.prisma.io/docs/concepts/components/prisma-client/sorting Sorting Docs}
   * 
   * Determine the order of AceiteTermosLgpds to fetch.
   */
  orderBy?: Prisma.AceiteTermosLgpdOrderByWithRelationInput | Prisma.AceiteTermosLgpdOrderByWithRelationInput[]
  /**
   * {@link https://www.prisma.io/docs/concepts/components/prisma-client/pagination#cursor-based-pagination Cursor Docs}
   * 
   * Sets the position for searching for AceiteTermosLgpds.
   */
  cursor?: Prisma.AceiteTermosLgpdWhereUniqueInput
  /**
   * {@link https://www.prisma.io/docs/concepts/components/prisma-client/pagination Pagination Docs}
   * 
   * Take `±n` AceiteTermosLgpds from the position of the cursor.
   */
  take?: number
  /**
   * {@link https://www.prisma.io/docs/concepts/components/prisma-client/pagination Pagination Docs}
   * 
   * Skip the first `n` AceiteTermosLgpds.
   */
  skip?: number
  /**
   * {@link https://www.prisma.io/docs/concepts/components/prisma-client/distinct Distinct Docs}
   * 
   * Filter by unique combinations of AceiteTermosLgpds.
   */
  distinct?: Prisma.AceiteTermosLgpdScalarFieldEnum | Prisma.AceiteTermosLgpdScalarFieldEnum[]
}

/**
 * AceiteTermosLgpd findFirstOrThrow
 */
export type AceiteTermosLgpdFindFirstOrThrowArgs<ExtArgs extends runtime.Types.Extensions.InternalArgs = runtime.Types.Extensions.DefaultArgs> = {
  /**
   * Select specific fields to fetch from the AceiteTermosLgpd
   */
  select?: Prisma.AceiteTermosLgpdSelect<ExtArgs> | null
  /**
   * Omit specific fields from the AceiteTermosLgpd
   */
  omit?: Prisma.AceiteTermosLgpdOmit<ExtArgs> | null
  /**
   * Choose, which related nodes to fetch as well
   */
  include?: Prisma.AceiteTermosLgpdInclude<ExtArgs> | null
  /**
   * Filter, which AceiteTermosLgpd to fetch.
   */
  where?: Prisma.AceiteTermosLgpdWhereInput
  /**
   * {@link https://www.prisma.io/docs/concepts/components/prisma-client/sorting Sorting Docs}
   * 
   * Determine the order of AceiteTermosLgpds to fetch.
   */
  orderBy?: Prisma.AceiteTermosLgpdOrderByWithRelationInput | Prisma.AceiteTermosLgpdOrderByWithRelationInput[]
  /**
   * {@link https://www.prisma.io/docs/concepts/components/prisma-client/pagination#cursor-based-pagination Cursor Docs}
   * 
   * Sets the position for searching for AceiteTermosLgpds.
   */
  cursor?: Prisma.AceiteTermosLgpdWhereUniqueInput
  /**
   * {@link https://www.prisma.io/docs/concepts/components/prisma-client/pagination Pagination Docs}
   * 
   * Take `±n` AceiteTermosLgpds from the position of the cursor.
   */
  take?: number
  /**
   * {@link https://www.prisma.io/docs/concepts/components/prisma-client/pagination Pagination Docs}
   * 
   * Skip the first `n` AceiteTermosLgpds.
   */
  skip?: number
  /**
   * {@link https://www.prisma.io/docs/concepts/components/prisma-client/distinct Distinct Docs}
   * 
   * Filter by unique combinations of AceiteTermosLgpds.
   */
  distinct?: Prisma.AceiteTermosLgpdScalarFieldEnum | Prisma.AceiteTermosLgpdScalarFieldEnum[]
}

/**
 * AceiteTermosLgpd findMany
 */
export type AceiteTermosLgpdFindManyArgs<ExtArgs extends runtime.Types.Extensions.InternalArgs = runtime.Types.Extensions.DefaultArgs> = {
  /**
   * Select specific fields to fetch from the AceiteTermosLgpd
   */
  select?: Prisma.AceiteTermosLgpdSelect<ExtArgs> | null
  /**
   * Omit specific fields from the AceiteTermosLgpd
   */
  omit?: Prisma.AceiteTermosLgpdOmit<ExtArgs> | null
  /**
   * Choose, which related nodes to fetch as well
   */
  include?: Prisma.AceiteTermosLgpdInclude<ExtArgs> | null
  /**
   * Filter, which AceiteTermosLgpds to fetch.
   */
  where?: Prisma.AceiteTermosLgpdWhereInput
  /**
   * {@link https://www.prisma.io/docs/concepts/components/prisma-client/sorting Sorting Docs}
   * 
   * Determine the order of AceiteTermosLgpds to fetch.
   */
  orderBy?: Prisma.AceiteTermosLgpdOrderByWithRelationInput | Prisma.AceiteTermosLgpdOrderByWithRelationInput[]
  /**
   * {@link https://www.prisma.io/docs/concepts/components/prisma-client/pagination#cursor-based-pagination Cursor Docs}
   * 
   * Sets the position for listing AceiteTermosLgpds.
   */
  cursor?: Prisma.AceiteTermosLgpdWhereUniqueInput
  /**
   * {@link https://www.prisma.io/docs/concepts/components/prisma-client/pagination Pagination Docs}
   * 
   * Take `±n` AceiteTermosLgpds from the position of the cursor.
   */
  take?: number
  /**
   * {@link https://www.prisma.io/docs/concepts/components/prisma-client/pagination Pagination Docs}
   * 
   * Skip the first `n` AceiteTermosLgpds.
   */
  skip?: number
  distinct?: Prisma.AceiteTermosLgpdScalarFieldEnum | Prisma.AceiteTermosLgpdScalarFieldEnum[]
}

/**
 * AceiteTermosLgpd create
 */
export type AceiteTermosLgpdCreateArgs<ExtArgs extends runtime.Types.Extensions.InternalArgs = runtime.Types.Extensions.DefaultArgs> = {
  /**
   * Select specific fields to fetch from the AceiteTermosLgpd
   */
  select?: Prisma.AceiteTermosLgpdSelect<ExtArgs> | null
  /**
   * Omit specific fields from the AceiteTermosLgpd
   */
  omit?: Prisma.AceiteTermosLgpdOmit<ExtArgs> | null
  /**
   * Choose, which related nodes to fetch as well
   */
  include?: Prisma.AceiteTermosLgpdInclude<ExtArgs> | null
  /**
   * The data needed to create a AceiteTermosLgpd.
   */
  data: Prisma.XOR<Prisma.AceiteTermosLgpdCreateInput, Prisma.AceiteTermosLgpdUncheckedCreateInput>
}

/**
 * AceiteTermosLgpd createMany
 */
export type AceiteTermosLgpdCreateManyArgs<ExtArgs extends runtime.Types.Extensions.InternalArgs = runtime.Types.Extensions.DefaultArgs> = {
  /**
   * The data used to create many AceiteTermosLgpds.
   */
  data: Prisma.AceiteTermosLgpdCreateManyInput | Prisma.AceiteTermosLgpdCreateManyInput[]
  skipDuplicates?: boolean
}

/**
 * AceiteTermosLgpd update
 */
export type AceiteTermosLgpdUpdateArgs<ExtArgs extends runtime.Types.Extensions.InternalArgs = runtime.Types.Extensions.DefaultArgs> = {
  /**
   * Select specific fields to fetch from the AceiteTermosLgpd
   */
  select?: Prisma.AceiteTermosLgpdSelect<ExtArgs> | null
  /**
   * Omit specific fields from the AceiteTermosLgpd
   */
  omit?: Prisma.AceiteTermosLgpdOmit<ExtArgs> | null
  /**
   * Choose, which related nodes to fetch as well
   */
  include?: Prisma.AceiteTermosLgpdInclude<ExtArgs> | null
  /**
   * The data needed to update a AceiteTermosLgpd.
   */
  data: Prisma.XOR<Prisma.AceiteTermosLgpdUpdateInput, Prisma.AceiteTermosLgpdUncheckedUpdateInput>
  /**
   * Choose, which AceiteTermosLgpd to update.
   */
  where: Prisma.AceiteTermosLgpdWhereUniqueInput
}

/**
 * AceiteTermosLgpd updateMany
 */
export type AceiteTermosLgpdUpdateManyArgs<ExtArgs extends runtime.Types.Extensions.InternalArgs = runtime.Types.Extensions.DefaultArgs> = {
  /**
   * The data used to update AceiteTermosLgpds.
   */
  data: Prisma.XOR<Prisma.AceiteTermosLgpdUpdateManyMutationInput, Prisma.AceiteTermosLgpdUncheckedUpdateManyInput>
  /**
   * Filter which AceiteTermosLgpds to update
   */
  where?: Prisma.AceiteTermosLgpdWhereInput
  /**
   * Limit how many AceiteTermosLgpds to update.
   */
  limit?: number
}

/**
 * AceiteTermosLgpd upsert
 */
export type AceiteTermosLgpdUpsertArgs<ExtArgs extends runtime.Types.Extensions.InternalArgs = runtime.Types.Extensions.DefaultArgs> = {
  /**
   * Select specific fields to fetch from the AceiteTermosLgpd
   */
  select?: Prisma.AceiteTermosLgpdSelect<ExtArgs> | null
  /**
   * Omit specific fields from the AceiteTermosLgpd
   */
  omit?: Prisma.AceiteTermosLgpdOmit<ExtArgs> | null
  /**
   * Choose, which related nodes to fetch as well
   */
  include?: Prisma.AceiteTermosLgpdInclude<ExtArgs> | null
  /**
   * The filter to search for the AceiteTermosLgpd to update in case it exists.
   */
  where: Prisma.AceiteTermosLgpdWhereUniqueInput
  /**
   * In case the AceiteTermosLgpd found by the `where` argument doesn't exist, create a new AceiteTermosLgpd with this data.
   */
  create: Prisma.XOR<Prisma.AceiteTermosLgpdCreateInput, Prisma.AceiteTermosLgpdUncheckedCreateInput>
  /**
   * In case the AceiteTermosLgpd was found with the provided `where` argument, update it with this data.
   */
  update: Prisma.XOR<Prisma.AceiteTermosLgpdUpdateInput, Prisma.AceiteTermosLgpdUncheckedUpdateInput>
}

/**
 * AceiteTermosLgpd delete
 */
export type AceiteTermosLgpdDeleteArgs<ExtArgs extends runtime.Types.Extensions.InternalArgs = runtime.Types.Extensions.DefaultArgs> = {
  /**
   * Select specific fields to fetch from the AceiteTermosLgpd
   */
  select?: Prisma.AceiteTermosLgpdSelect<ExtArgs> | null
  /**
   * Omit specific fields from the AceiteTermosLgpd
   */
  omit?: Prisma.AceiteTermosLgpdOmit<ExtArgs> | null
  /**
   * Choose, which related nodes to fetch as well
   */
  include?: Prisma.AceiteTermosLgpdInclude<ExtArgs> | null
  /**
   * Filter which AceiteTermosLgpd to delete.
   */
  where: Prisma.AceiteTermosLgpdWhereUniqueInput
}

/**
 * AceiteTermosLgpd deleteMany
 */
export type AceiteTermosLgpdDeleteManyArgs<ExtArgs extends runtime.Types.Extensions.InternalArgs = runtime.Types.Extensions.DefaultArgs> = {
  /**
   * Filter which AceiteTermosLgpds to delete
   */
  where?: Prisma.AceiteTermosLgpdWhereInput
  /**
   * Limit how many AceiteTermosLgpds to delete.
   */
  limit?: number
}

/**
 * AceiteTermosLgpd without action
 */
export type AceiteTermosLgpdDefaultArgs<ExtArgs extends runtime.Types.Extensions.InternalArgs = runtime.Types.Extensions.DefaultArgs> = {
  /**
   * Select specific fields to fetch from the AceiteTermosLgpd
   */
  select?: Prisma.AceiteTermosLgpdSelect<ExtArgs> | null
  /**
   * Omit specific fields from the AceiteTermosLgpd
   */
  omit?: Prisma.AceiteTermosLgpdOmit<ExtArgs> | null
  /**
   * Choose, which related nodes to fetch as well
   */
  include?: Prisma.AceiteTermosLgpdInclude<ExtArgs> | null
}
