import { z } from "zod";

// Cliente validations
export const clienteSchema = z.object({
  nome: z.string().min(3, "Nome deve ter pelo menos 3 caracteres"),
  tipo: z.enum(["PF", "PJ"]),
  cnpj: z.string().refine((doc) => {
    const numbers = doc.replace(/\D/g, "");
    if (numbers.length === 11) {
      // CPF validation
      return validateCPF(numbers);
    } else if (numbers.length === 14) {
      // CNPJ validation
      return validateCNPJ(numbers);
    }
    return false;
  }, "Documento inválido"),
  email: z.string().email("Email inválido").optional().or(z.literal("")),
  telefone: z.string().optional(),
  endereco: z.string().optional(),
  cidade: z.string().optional(),
  estado: z.string().optional(),
  cep: z.string().optional(),
  ativo: z.boolean().default(true),
});

// Profissional validations
export const profissionalSchema = z.object({
  nome: z.string().min(3, "Nome deve ter pelo menos 3 caracteres"),
  cpf: z.string().refine((cpf) => {
    const numbers = cpf.replace(/\D/g, "");
    return validateCPF(numbers);
  }, "CPF inválido"),
  rg: z.string().optional(),
  dataNascimento: z.string().optional(),
  email: z.string().email("Email inválido"),
  telefone: z.string().min(10, "Telefone inválido"),
  endereco: z.string().optional(),
  cidade: z.string().optional(),
  estado: z.string().optional(),
  cep: z.string().optional(),
  banco: z.string().optional(),
  agencia: z.string().optional(),
  conta: z.string().optional(),
  tipoConta: z.enum(["CORRENTE", "POUPANCA"]).optional(),
  pix: z.string().optional(),
  observacoes: z.string().optional(),
  ativo: z.boolean().default(true),
});

// Local Atendimento validations
export const localAtendimentoSchema = z.object({
  clienteId: z.number("Cliente inválido"),
  nome: z.string().min(3, "Nome deve ter pelo menos 3 caracteres"),
  endereco: z.string().min(5, "Endereço é obrigatório"),
  cidade: z.string().min(2, "Cidade é obrigatória"),
  estado: z.string().length(2, "Estado deve ter 2 caracteres"),
  cep: z.string().optional(),
  telefone: z.string().optional(),
  responsavel: z.string().optional(),
  latitude: z.number().optional(),
  longitude: z.number().optional(),
  observacoes: z.string().optional(),
  ativo: z.boolean().default(true),
});

// Plantão validations
export const plantaoSchema = z.object({
  clienteId: z.number("Cliente inválido"),
  localAtendimentoId: z.number("Local inválido"),
  profissionalId: z.number("Profissional inválido"),
  mes: z.number().min(1).max(12),
  ano: z.number().min(2024).max(2050),
  modalidadeTrabalho: z.enum(["PLANTONISTA", "MENSALISTA", "COORDENADOR", "SUPERVISOR", "DIRETOR"]),
  tipoValor: z.enum(["HORA", "DIARIA", "PLANTAO", "MENSAL"]).default("HORA"),
  valorBase: z.number().positive("Valor base é obrigatório"),
  prazoPagamentoDias: z.number().min(1).max(365).optional(),
  horaInicio: z.string().optional(),
  horaFim: z.string().optional(),
  observacoes: z.string().optional(),
  ativo: z.boolean().default(true),
  diasSelecionados: z.array(z.number()).min(1, "Selecione pelo menos um dia"),
});

// Fechamento validations
export const fechamentoSchema = z.object({
  plantaoId: z.number("Plantão inválido"),
  mes: z.number().min(1).max(12),
  ano: z.number().min(2024).max(2050),
  diasTrabalhados: z.number().min(0),
  valorTotal: z.number().positive().optional(),
  observacoes: z.string().optional(),
  diasConfirmados: z.array(z.number()).min(1, "Confirme pelo menos um dia"),
});

// Antecipação validations
export const antecipacaoSchema = z.object({
  fechamentoId: z.number("Fechamento inválido"),
  valorSolicitado: z.number().positive("Valor deve ser maior que zero"),
  taxaAntecipacao: z.number().min(0).max(10),
  valorAprovado: z.number().positive(),
  valorDesconto: z.number().min(0),
  valorLiquido: z.number().positive(),
  dataPagamentoPrevista: z.string(),
  observacoes: z.string().optional(),
});

// Helper functions for document validation
function validateCPF(cpf: string): boolean {
  if (cpf.length !== 11) return false;

  // Check if all digits are the same
  if (/^(\d)\1+$/.test(cpf)) return false;

  // Validate check digits
  let sum = 0;
  for (let i = 0; i < 9; i++) {
    sum += parseInt(cpf[i]) * (10 - i);
  }
  let remainder = (sum * 10) % 11;
  if (remainder === 10) remainder = 0;
  if (remainder !== parseInt(cpf[9])) return false;

  sum = 0;
  for (let i = 0; i < 10; i++) {
    sum += parseInt(cpf[i]) * (11 - i);
  }
  remainder = (sum * 10) % 11;
  if (remainder === 10) remainder = 0;
  if (remainder !== parseInt(cpf[10])) return false;

  return true;
}

function validateCNPJ(cnpj: string): boolean {
  if (cnpj.length !== 14) return false;

  // Check if all digits are the same
  if (/^(\d)\1+$/.test(cnpj)) return false;

  // Validate check digits
  const weights1 = [5, 4, 3, 2, 9, 8, 7, 6, 5, 4, 3, 2];
  const weights2 = [6, 5, 4, 3, 2, 9, 8, 7, 6, 5, 4, 3, 2];

  let sum = 0;
  for (let i = 0; i < 12; i++) {
    sum += parseInt(cnpj[i]) * weights1[i];
  }
  let remainder = sum % 11;
  if (remainder < 2) remainder = 0;
  else remainder = 11 - remainder;
  if (remainder !== parseInt(cnpj[12])) return false;

  sum = 0;
  for (let i = 0; i < 13; i++) {
    sum += parseInt(cnpj[i]) * weights2[i];
  }
  remainder = sum % 11;
  if (remainder < 2) remainder = 0;
  else remainder = 11 - remainder;
  if (remainder !== parseInt(cnpj[13])) return false;

  return true;
}

export function formatCPF(cpf: string): string {
  const numbers = cpf.replace(/\D/g, "");
  return numbers.replace(/(\d{3})(\d{3})(\d{3})(\d{2})/, "$1.$2.$3-$4");
}

export function formatCNPJ(cnpj: string): string {
  const numbers = cnpj.replace(/\D/g, "");
  return numbers.replace(/(\d{2})(\d{3})(\d{3})(\d{4})(\d{2})/, "$1.$2.$3/$4-$5");
}

export function formatPhone(phone: string): string {
  const numbers = phone.replace(/\D/g, "");
  if (numbers.length === 11) {
    return numbers.replace(/(\d{2})(\d{5})(\d{4})/, "($1) $2-$3");
  } else if (numbers.length === 10) {
    return numbers.replace(/(\d{2})(\d{4})(\d{4})/, "($1) $2-$3");
  }
  return phone;
}

export function formatCEP(cep: string): string {
  const numbers = cep.replace(/\D/g, "");
  return numbers.replace(/(\d{5})(\d{3})/, "$1-$2");
}
