# my-better-t-app

This project was created with [Better-T-Stack](https://github.com/AmanV<PERSON>hney01/create-better-t-stack), a modern TypeScript stack that combines React, React Router, Fastify, and more.

## Features

- **TypeScript** - For type safety and improved developer experience
- **React Router** - Declarative routing for React
- **TailwindCSS** - Utility-first CSS for rapid UI development
- **shadcn/ui** - Reusable UI components
- **Fastify** - Fast, low-overhead web framework
- **Node.js** - Runtime environment
- **Prisma** - TypeScript-first ORM
- **MySQL** - Database engine
- **PWA** - Progressive Web App support

## Getting Started

First, install the dependencies:

```bash
npm install
```

## Database Setup

This project uses MySQL with Prisma.

1. Make sure you have a MySQL database set up.
2. Update your `apps/server/.env` file with your MySQL connection details.

3. Generate the Prisma client and push the schema:

```bash
npm run db:push
```

Then, run the development server:

```bash
npm run dev
```

Open [http://localhost:5173](http://localhost:5173) in your browser to see the web application.
The API is running at [http://localhost:3000](http://localhost:3000).

## PWA Support with React Router v7

There is a known compatibility issue between VitePWA and React Router v7.
See: https://github.com/vite-pwa/vite-plugin-pwa/issues/809

## Project Structure

```
my-better-t-app/
├── apps/
│   ├── web/         # Frontend application (React + React Router)
│   └── server/      # Backend API (Fastify)
```

## Available Scripts

- `npm run dev`: Start all applications in development mode
- `npm run build`: Build all applications
- `npm run dev:web`: Start only the web application
- `npm run dev:server`: Start only the server
- `npm run check-types`: Check TypeScript types across all apps
- `npm run db:push`: Push schema changes to database
- `npm run db:studio`: Open database studio UI
- `cd apps/web && npm run generate-pwa-assets`: Generate PWA assets
