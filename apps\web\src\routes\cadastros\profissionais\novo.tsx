import { ProfissionalForm } from "@/pages/profissionais/profissional-form";
import { createFileRoute } from "@tanstack/react-router";
import { requireAdminRole } from "@/lib/route-guards";

function ProfissionalNovo() {
  return <ProfissionalForm />;
}

export const Route = createFileRoute("/cadastros/profissionais/novo")({
  component: ProfissionalNovo,
  beforeLoad: async () => {
    await requireAdminRole();
  },
});
