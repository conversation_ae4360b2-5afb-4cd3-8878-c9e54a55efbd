import { <PERSON>, useNavigate } from "@tanstack/react-router";
import { createFileRoute } from "@tanstack/react-router";
import { useQuery, useMutation, useQueryClient } from "@tanstack/react-query";
import { requireAdminRole } from "@/lib/route-guards";
import { useState } from "react";
import { api, type Profissional, type PaginatedResponse } from "@/lib/api";
import { Button } from "@/components/ui/button";
import { Input } from "@/components/ui/input";
import { Table, TableBody, TableCell, TableHead, TableHeader, TableRow } from "@/components/ui/table";
import { Badge } from "@/components/ui/badge";
import { Card, CardContent, CardHeader, CardTitle } from "@/components/ui/card";
import {
  DropdownMenu,
  DropdownMenuContent,
  DropdownMenuItem,
  DropdownMenuTrigger,
} from "@/components/ui/dropdown-menu";
import { Skeleton } from "@/components/ui/skeleton";
import { <PERSON>, Search, Plus, MoreHorizontal, Pencil, Trash2, Eye, CreditCard } from "lucide-react";
import { toast } from "sonner";
import { DeleteConfirmDialog } from "@/components/ui/delete-confirm-dialog";
import { formatCPF, formatPhone } from "@/lib/utils";

function ProfissionaisIndex() {
  const navigate = useNavigate();
  const queryClient = useQueryClient();
  const [page, setPage] = useState(1);
  const [search, setSearch] = useState("");
  const [deleteDialogOpen, setDeleteDialogOpen] = useState(false);
  const [profissionalToDelete, setProfissionalToDelete] = useState<string | null>(null);
  const limit = 10;

  const { data, isLoading } = useQuery<PaginatedResponse<Profissional>>({
    queryKey: ["profissionais", page, search],
    queryFn: () => api.get("/profissionais", { page, limit, search }),
  });

  const deleteMutation = useMutation({
    mutationFn: (id: string) => api.delete(`/profissionais/${id}`),
    onSuccess: () => {
      toast.success("Profissional excluído com sucesso");
      queryClient.invalidateQueries({ queryKey: ["profissionais"] });
    },
    onError: () => {
      toast.error("Erro ao excluir profissional");
    },
  });

  const handleDelete = (id: string) => {
    setProfissionalToDelete(id);
    setDeleteDialogOpen(true);
  };

  const confirmDelete = () => {
    if (profissionalToDelete) {
      deleteMutation.mutate(profissionalToDelete);
      setDeleteDialogOpen(false);
      setProfissionalToDelete(null);
    }
  };

  const formatCPF = (cpf: string) => {
    return cpf.replace(/(\d{3})(\d{3})(\d{3})(\d{2})/, "$1.$2.$3-$4");
  };

  const formatPhone = (phone: string) => {
    const numbers = phone.replace(/\D/g, "");
    if (numbers.length === 11) {
      return numbers.replace(/(\d{2})(\d{5})(\d{4})/, "($1) $2-$3");
    }
    return phone;
  };

  return (
    <div className="space-y-6">
      <div className="flex justify-between items-center">
        <div>
          <h1 className="text-3xl font-bold">Profissionais</h1>
          <p className="text-muted-foreground">Gerencie os profissionais de saúde cadastrados</p>
        </div>
        <Link to="/cadastros/profissionais/novo">
          <Button>
            <Plus className="mr-2 h-4 w-4" />
            Novo Profissional
          </Button>
        </Link>
      </div>

      <Card>
        <CardHeader>
          <CardTitle>Lista de Profissionais</CardTitle>
          <div className="flex gap-4 mt-4">
            <div className="relative flex-1 max-w-sm">
              <Search className="absolute left-3 top-1/2 transform -translate-y-1/2 text-muted-foreground h-4 w-4" />
              <Input
                placeholder="Buscar por nome, CPF ou email..."
                value={search}
                onChange={(e) => setSearch(e.target.value)}
                className="pl-9"
              />
            </div>
          </div>
        </CardHeader>
        <CardContent>
          <div className="rounded-md border">
            <Table>
              <TableHeader>
                <TableRow>
                  <TableHead>Nome</TableHead>
                  <TableHead>CPF</TableHead>
                  <TableHead>Email</TableHead>
                  <TableHead>Telefone</TableHead>
                  <TableHead>Especialidade</TableHead>
                  <TableHead>Escalas</TableHead>
                  <TableHead>Status</TableHead>
                  <TableHead className="w-[50px]"></TableHead>
                </TableRow>
              </TableHeader>
              <TableBody>
                {isLoading ? (
                  Array.from({ length: 5 }).map((_, i) => (
                    <TableRow key={i}>
                      <TableCell>
                        <Skeleton className="h-4 w-32" />
                      </TableCell>
                      <TableCell>
                        <Skeleton className="h-4 w-28" />
                      </TableCell>
                      <TableCell>
                        <Skeleton className="h-4 w-36" />
                      </TableCell>
                      <TableCell>
                        <Skeleton className="h-4 w-24" />
                      </TableCell>
                      <TableCell>
                        <Skeleton className="h-4 w-24" />
                      </TableCell>
                      <TableCell>
                        <Skeleton className="h-4 w-8" />
                      </TableCell>
                      <TableCell>
                        <Skeleton className="h-4 w-16" />
                      </TableCell>
                      <TableCell>
                        <Skeleton className="h-4 w-4" />
                      </TableCell>
                    </TableRow>
                  ))
                ) : data?.data.length === 0 ? (
                  <TableRow>
                    <TableCell colSpan={8} className="text-center py-8">
                      <Users className="mx-auto h-12 w-12 text-muted-foreground/20 mb-2" />
                      <p className="text-muted-foreground">Nenhum profissional encontrado</p>
                    </TableCell>
                  </TableRow>
                ) : (
                  data?.data.map((profissional) => (
                    <TableRow key={profissional.id}>
                      <TableCell className="font-medium">{profissional.usuario?.nome || "-"}</TableCell>
                      <TableCell className="font-mono text-sm">
                        {profissional.usuario?.cpf ? formatCPF(profissional.usuario.cpf) : "-"}
                      </TableCell>
                      <TableCell>{profissional.usuario?.email || "-"}</TableCell>
                      <TableCell>
                        {profissional.usuario?.telefone ? formatPhone(profissional.usuario.telefone) : "-"}
                      </TableCell>
                      <TableCell>
                        {profissional.especialidades && profissional.especialidades.length > 0
                          ? profissional.especialidades.map((e) => e.especialidade.nome).join(", ")
                          : "-"}
                      </TableCell>
                      <TableCell>{profissional._count?.plantoes || 0}</TableCell>
                      <TableCell>
                        <Badge variant={profissional.usuario?.ativo ? "default" : "secondary"}>
                          {profissional.usuario?.ativo ? "Ativo" : "Inativo"}
                        </Badge>
                      </TableCell>
                      <TableCell>
                        <DropdownMenu>
                          <DropdownMenuTrigger asChild>
                            <Button variant="ghost" size="icon">
                              <MoreHorizontal className="h-4 w-4" />
                            </Button>
                          </DropdownMenuTrigger>
                          <DropdownMenuContent align="end">
                            <DropdownMenuItem
                              onClick={() => navigate({ to: `/cadastros/profissionais/${profissional.uuid}` })}
                            >
                              <Eye className="mr-2 h-4 w-4" />
                              Visualizar
                            </DropdownMenuItem>
                            <DropdownMenuItem
                              onClick={() =>
                                navigate({
                                  to: `/cadastros/profissionais/${profissional.uuid}`,
                                })
                              }
                            >
                              <Pencil className="mr-2 h-4 w-4" />
                              Editar
                            </DropdownMenuItem>
                            <DropdownMenuItem
                              className="text-destructive"
                              onClick={() => handleDelete(profissional.uuid)}
                            >
                              <Trash2 className="mr-2 h-4 w-4" />
                              Excluir
                            </DropdownMenuItem>
                          </DropdownMenuContent>
                        </DropdownMenu>
                      </TableCell>
                    </TableRow>
                  ))
                )}
              </TableBody>
            </Table>
          </div>

          {data?.meta?.totalPages! > 1 && (
            <div className="flex items-center justify-between mt-4">
              <p className="text-sm text-muted-foreground">
                Mostrando {(page - 1) * limit + 1} a {Math.min(page * limit, data?.meta?.total || 0)} de{" "}
                {data?.meta?.total || 0} profissionais
              </p>
              <div className="flex gap-2">
                <Button variant="outline" size="sm" onClick={() => setPage(page - 1)} disabled={page === 1}>
                  Anterior
                </Button>
                <Button
                  variant="outline"
                  size="sm"
                  onClick={() => setPage(page + 1)}
                  disabled={page === data?.meta?.totalPages}
                >
                  Próxima
                </Button>
              </div>
            </div>
          )}
        </CardContent>
      </Card>

      <DeleteConfirmDialog
        open={deleteDialogOpen}
        onOpenChange={setDeleteDialogOpen}
        onConfirm={confirmDelete}
        title="Confirmar Exclusão"
        description="Tem certeza que deseja excluir este profissional? Esta ação não pode ser desfeita."
      />
    </div>
  );
}

export const Route = createFileRoute("/cadastros/profissionais/")({
  component: ProfissionaisIndex,
  beforeLoad: async () => {
    await requireAdminRole();
  },
});
