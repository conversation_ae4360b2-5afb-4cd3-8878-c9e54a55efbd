import { z } from "zod";
import { prisma, withAudit } from "@/lib/prisma";
import {
  createPlantaoSchema,
  plantaoQuerySchema,
  type CreatePlantaoInput,
  type PlantaoQuery,
} from "@/schemas/plantao.schema";
import type { FastifyTypedInstance, FastifyTypedRequest } from "@/types";
import { formatDateToDateTime, getCurrentYear, parseUTCDate, toISOString } from "@shared/date";
import { getEndOfMonthInTimezone, getStartOfMonthInTimezone } from "@shared/date";

export function buscarPresencasRouter(fastify: FastifyTypedInstance) {
  // Buscar presenças (registros de ponto) de um mês específico do plantão
  fastify.get<{
    Params: { uuid: string };
    Querystring: { mes: string; ano: string };
  }>(
    "/plantoes/:uuid/presencas",
    withAudit(async (request, reply) => {
      const { uuid } = request.params;
      const { mes, ano } = request.query;
      const clienteId = request.clienteId;

      if (!mes || !ano) {
        return reply.status(400).send({
          error: "Mês e ano são obrigatórios",
        });
      }

      const mesNum = Number(mes);
      const anoNum = Number(ano);

      // First get the plantao to get its internal ID and verify cliente access
      const plantaoWhere = {
        uuid,
        clienteId,
      };

      const plantao = await prisma.plantao.findFirst({
        where: plantaoWhere,
        select: { id: true },
      });

      if (!plantao) {
        return reply.status(404).send({ error: "Plantão não encontrado" });
      }

      // Buscar APENAS as presenças (registros de ponto) do mês/ano especificado
      const fusoHorario = request.fusoHorario;
      const presencas = await prisma.presencaDiaPlantao.findMany({
        where: {
          diaPlantao: {
            plantaoId: plantao.id,
            data: {
              gte: getStartOfMonthInTimezone(anoNum, mesNum, fusoHorario).toISOString(),
              lt: getStartOfMonthInTimezone(anoNum, mesNum + 1, fusoHorario).toISOString(),
            },
          },
        },
        orderBy: [{ diaPlantao: { data: "asc" } }, { horaEntrada: "asc" }],
      });

      return reply.send(presencas);
    })
  );
}
