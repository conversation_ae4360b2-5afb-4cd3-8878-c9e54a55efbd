# GS2 AWS Lambda Utils

Lambda de utilidades para o sistema GS2/Capitale.

## Configuração

### Variáveis de Ambiente

```bash
# Chave de API para autenticação (obrigatório)
GS2_LAMBDA_API_KEY=sua-chave-secreta-aqui

# Configurações de email (opcional)
AWS_REGION=us-east-2
EMAIL_SOURCE=<EMAIL>
LOGO_URL=https://gs2group.com/images/GS2_Logo.png
BRAND_LOGO_URL=https://capitale-imagens.s3.us-east-2.amazonaws.com/logo/Capitale-logo.jpg
SUPPORT_EMAIL=<EMAIL>
SUPPORT_PHONE=+55 (11) 99999-9999
WEBSITE_URL=https://gs2group.com
```

## Autenticação

Todas as requisições devem incluir uma API key no header:

```http
x-api-key: sua-chave-secreta-aqui
```

Ou:

```http
Authorization: Bearer sua-chave-secreta-aqui
```

## Ações Disponíveis

### 1. Envio de Email

Envia emails através do AWS SES com template HTML.

#### Payload

```json
{
  "action": "email",
  "to": ["<EMAIL>"],
  "subject": "Assunto do Email",
  "body": "<h2>Título</h2><p>Conteúdo HTML do email</p>"
}
```

#### Parâmetros

- `action`: "email" (obrigatório)
- `to`: Array de emails destinatários (obrigatório)
- `subject`: Assunto do email (obrigatório)
- `body`: Corpo do email em HTML (obrigatório)

#### Resposta de Sucesso

```json
{
  "message": "E-mail enviado com sucesso!",
  "messageId": "0000014a-f6a4-4a5d-8c3c-385c3b5c0000",
  "recipients": ["<EMAIL>"]
}
```

## Exemplos de Uso

### Chamada via cURL

```bash
curl -X POST https://sua-lambda-url.amazonaws.com/ \
  -H "Content-Type: application/json" \
  -H "x-api-key: sua-chave-secreta-aqui" \
  -d '{
    "action": "email",
    "to": ["<EMAIL>"],
    "subject": "Bem-vindo!",
    "body": "<h1>Olá!</h1><p>Seja bem-vindo ao sistema.</p>"
  }'
```

### Chamada via JavaScript

```javascript
const response = await fetch("https://sua-lambda-url.amazonaws.com/", {
  method: "POST",
  headers: {
    "Content-Type": "application/json",
    "x-api-key": "sua-chave-secreta-aqui",
  },
  body: JSON.stringify({
    action: "email",
    to: ["<EMAIL>"],
    subject: "Notificação",
    body: "<h2>Nova Notificação</h2><p>Você tem uma nova mensagem.</p>",
  }),
});

const result = await response.json();
```

### Chamada via Python

```python
import requests

url = 'https://sua-lambda-url.amazonaws.com/'
headers = {
    'Content-Type': 'application/json',
    'x-api-key': 'sua-chave-secreta-aqui'
}
payload = {
    'action': 'email',
    'to': ['<EMAIL>'],
    'subject': 'Relatório',
    'body': '<h2>Relatório Mensal</h2><p>Segue o relatório.</p>'
}

response = requests.post(url, json=payload, headers=headers)
print(response.json())
```

## Códigos de Erro

- `401 UNAUTHORIZED`: API key inválida ou ausente
- `400 INVALID_JSON`: Corpo da requisição não é um JSON válido
- `400 MISSING_ACTION`: Campo 'action' não informado
- `400 INVALID_ACTION`: Ação não reconhecida
- `500 EMAIL_SEND_FAILED`: Falha ao enviar email
- `500 INTERNAL_ERROR`: Erro interno no processamento

## Observações

- O email é enviado com template HTML que inclui header e footer da Capitale Holding
- O corpo do email (`body`) aceita HTML completo
- Múltiplos destinatários podem ser especificados no array `to`
- A lambda valida formato de email antes do envio
- Logs de tentativas não autorizadas são registrados para auditoria
