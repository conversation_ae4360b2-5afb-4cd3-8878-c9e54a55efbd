import Zip from 'adm-zip';
import path from 'path';
import fs from 'fs';
import { verifyDir, getParams, updateFunction, getArg } from './functions.mjs';
import { dirname } from 'path';
import { fileURLToPath } from 'url';

const __filename = fileURLToPath(import.meta.url);
const __dirname = dirname(__filename);

const getOriginDir = (lambdaName = '') => {
    // return path.resolve(__dirname, '..', 'src', 'lambdas', lambdaName);
    return `${__dirname}/../${lambdaName}`;
};

const dirZippedLambda = 'zipped-lambda-output';
const zippedLambdaDIR = path.resolve(__dirname, dirZippedLambda);

const getTargetDirWithZip = (lambdaName = '') => {
    // return path.resolve(__dirname, '..', dirZippedLambda, `${lambdaName}.zip`);
    return `${__dirname}/${dirZippedLambda}/${lambdaName}.zip`;
};

const main = async () => {
    try {
        const { lambdaName, profile, region } = await getParams();

        const originDir = getOriginDir(lambdaName);
        verifyDir(originDir);

        const zip = new Zip();
        zip.addLocalFolder(originDir);

        const targetDirWithZip = getTargetDirWithZip(lambdaName);
        zip.writeZip(targetDirWithZip);

        const payload = {
            profile,
            region,
            pathFile: targetDirWithZip,
            FunctionName: lambdaName,
        };

        const runtime = getArg('runtime');
        const role = getArg('role');

        if (role) {
            payload.Role = role;
        }

        if (runtime) {
            payload.Runtime = runtime;
        }

        await updateFunction(payload);

        fs.rmdirSync(zippedLambdaDIR, { recursive: true });
    } catch (error) {
        console.log('\n');
        console.log('\x1b[31m%s\x1b[0m', 'Catch no updateFunction():');
        console.log(error.message);
        console.log('\n');
    } finally {
        process.exit();
    }
};

main();
