import { appEnv } from "../env";
import { getEmailService } from "./config";

export async function sendWelcomeEmail(name: string, email: string, password: string) {
  const service = getEmailService();

  const subject = "Bem-vindo ao Sistema GS2 - Suas credenciais de acesso";

  const htmlBody = /*html*/ `
    <!DOCTYPE html>
    <html>
    <head>
      <meta charset="utf-8">
      <style>
        body { font-family: Arial, sans-serif; line-height: 1.6; color: #333; }
        .container { max-width: 600px; margin: 0 auto; padding: 20px; }
        .header { background-color: #f4f4f4; padding: 20px; text-align: center; }
        .content { padding: 20px; }
        .credentials { background-color: #f9f9f9; padding: 15px; border-left: 4px solid #007bff; margin: 20px 0; }
        .footer { background-color: #f4f4f4; padding: 20px; text-align: center; font-size: 12px; color: #666; }
      </style>
    </head>
    <body>
      <div class="container">
        <div class="header">
          <h1>Bem-vindo ao Sistema GS2</h1>
        </div>
        <div class="content">
          <p>Olá ${name},</p>
          <p>Sua conta foi criada com sucesso no sistema GS2. Abaixo estão suas credenciais de acesso:</p>
          
          <div class="credentials">
            <strong>Email:</strong> ${email}<br>
            <strong>Senha temporária:</strong> ${password}
          </div>
          
          <p><strong>Importante:</strong> Por motivos de segurança, recomendamos que você altere sua senha no primeiro acesso.</p>
          
          <p>Para acessar o sistema, visite: <a href="${appEnv.FRONT_END_URL || "http://localhost:5173"}">${appEnv.FRONT_END_URL || "http://localhost:5173"}</a></p>
          
          <p>Se você tiver alguma dúvida ou precisar de ajuda, entre em contato com nossa equipe de suporte.</p>
          
          <p>Atenciosamente,<br>Equipe GS2</p>
        </div>
        <div class="footer">
          <p>Este é um email automático, por favor não responda.</p>
        </div>
      </div>
    </body>
    </html>
  `;

  const textBody = `
    Bem-vindo ao Sistema GS2

    Olá ${name},

    Sua conta foi criada com sucesso no sistema GS2. Abaixo estão suas credenciais de acesso:

    Email: ${email}
    Senha temporária: ${password}

    IMPORTANTE: Por motivos de segurança, recomendamos que você altere sua senha no primeiro acesso.

    Para acessar o sistema, visite: ${appEnv.FRONT_END_URL || "http://localhost:5173"}

    Se você tiver alguma dúvida ou precisar de ajuda, entre em contato com nossa equipe de suporte.

    Atenciosamente,
    Equipe GS2

    Este é um email automático, por favor não responda.
  `;

  await service.sendEmail({
    to: email,
    subject,
    htmlBody,
    textBody,
  });
}
