{"name": "web", "version": "1.0.0", "private": true, "type": "module", "license": "UNLICENSED", "scripts": {"build": "tsr generate && vite build", "dev": "tsr generate && vite", "start": "vite preview", "typecheck": "tsr generate && tsc", "check-types": "tsr generate && tsc", "generate-pwa-assets": "pwa-assets-generator", "lint": "eslint . --ext .ts,.tsx", "lint:fix": "eslint . --ext .ts,.tsx --fix"}, "dependencies": {"@radix-ui/react-dialog": "^1.1.15", "@radix-ui/react-dropdown-menu": "^2.1.16", "@radix-ui/react-icons": "^1.3.2", "@radix-ui/react-popover": "^1.1.15", "@radix-ui/react-scroll-area": "^1.2.10", "@tanstack/react-form": "^1.12.0", "@tanstack/react-query": "^5.80.5", "@tanstack/react-router": "^1.131.28", "@tanstack/router-devtools": "^1.131.28", "@tanstack/zod-form-adapter": "^0.42.1", "class-variance-authority": "^0.7.1", "clsx": "^2.1.1", "cmdk": "^1.1.1", "date-fns": "^4.1.0", "date-fns-tz": "^3.2.0", "file-saver": "^2.0.5", "isbot": "^5.1.28", "lucide-react": "^0.511.0", "next-themes": "^0.4.6", "radix-ui": "^1.4.2", "react": "19.0.0", "react-dom": "19.0.0", "sonner": "^2.0.3", "tailwind-merge": "^3.3.0", "tw-animate-css": "^1.3.2", "vite-plugin-pwa": "^1.0.1", "xlsx": "^0.18.5", "zod": "^4.0.2", "zustand": "^5.0.7"}, "devDependencies": {"@eslint/js": "^9.33.0", "@tailwindcss/vite": "^4.1.8", "@tanstack/react-query-devtools": "^5.80.5", "@tanstack/router-cli": "^1.131.28", "@tanstack/router-plugin": "^1.131.28", "@types/file-saver": "^2.0.7", "@types/node": "^20", "@types/react": "^19.0.12", "@types/react-dom": "^19.0.4", "@typescript-eslint/eslint-plugin": "^8.40.0", "@typescript-eslint/parser": "^8.40.0", "@vite-pwa/assets-generator": "^1.0.0", "@vitejs/plugin-react": "^5.0.2", "eslint-plugin-react": "^7.37.5", "eslint-plugin-react-hooks": "^5.2.0", "lightningcss": "^1.30.1", "tailwindcss": "^4.1.8", "typescript": "^5.8.2", "vite": "^7.1.4", "vite-tsconfig-paths": "^5.1.4"}, "overrides": {"rollup": "npm:@rollup/wasm-node"}}