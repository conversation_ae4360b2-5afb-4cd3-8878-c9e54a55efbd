import "dotenv/config";

process.env.TZ = "UTC";

import { fastify } from "fastify";
import fastifyCors from "@fastify/cors";
import { authRouter } from "./routers/auth.router";
import { clienteRouter } from "./routers/cliente.router";
import { profissionalRouter } from "./routers/profissional.router";
import { plantaoRouter } from "./routers/plantao";
import { localAtendimentoRouter } from "./routers/local-atendimento.router";
import { fechamentoRouter } from "./routers/fechamento.router";
import { antecipacaoRouter } from "./routers/antecipacao.router";
import { especialidadeRouter } from "./routers/especialidade.router";
import { presencaDiaPlantaoRouter } from "./routers/registro-ponto.router";
import { dashboardRouter } from "./routers/dashboard.router";
import { auditRouter } from "./routers/audit.router";
import { usuariosRouter } from "./routers/usuarios.router";
import { termosRouter } from "./routers/termos.router";
import { profissionalPlantoesRouter } from "./routers/profissional-plantoes.router";
import { profissionalDashboardRouter } from "./routers/profissional-dashboard.router";
import { getLoggerConfig } from "./config/logger.config";
import { authenticate } from "./middlewares/auth.middleware";
import { auditMiddleware } from "./middlewares/audit.middleware";
import { clienteContextMiddleware } from "./middlewares/cliente-context.middleware";
import { appEnv } from "./lib/env";

const port = appEnv.PORT;

const baseCorsConfig = {
  origin: appEnv.FRONT_END_URL,
  methods: ["GET", "POST", "PUT", "DELETE", "OPTIONS"],
  allowedHeaders: ["Content-Type", "Authorization", "X-Requested-With", "X-Cliente-Id"],
  credentials: true,
  maxAge: 86400,
};

const app = fastify({
  logger: getLoggerConfig(),
  disableRequestLogging: false,
  requestIdLogLabel: "reqId",
  genReqId: (req) => {
    // Gera IDs de requisição mais curtos e legíveis
    return Math.random().toString(36).substring(2, 9);
  },
});

app.register(fastifyCors, baseCorsConfig);

app.setErrorHandler((error, request, reply) => {
  const isClientError = error.statusCode && error.statusCode < 500;

  if (isClientError) {
    request.log.warn(
      {
        reqId: request.id,
        error: {
          type: error.name,
          message: error.message,
          statusCode: error.statusCode,
        },
        request: {
          method: request.method,
          url: request.url,
        },
      },
      `Client error: ${error.message}`
    );
  } else {
    request.log.error(
      {
        reqId: request.id,
        err: error,
        request: {
          method: request.method,
          url: request.url,
          params: request.params,
          query: request.query,
          body: request.body,
        },
      },
      `Server error: ${error.message}`
    );
  }

  const statusCode = error.statusCode || 500;
  const response = {
    error: true,
    message: isClientError ? error.message : "Internal Server Error",
    statusCode,
    ...(appEnv.NODE_ENV === "development" && !isClientError ? { stack: error.stack } : {}),
  };

  reply.status(statusCode).send(response);
});

app.register(async function apiRoutes(fastify) {
  await fastify.register(authRouter, { prefix: "/api" });

  await fastify.register(
    async function protectedRoutes(fastify) {
      // Middleware de autenticação (deve vir primeiro)
      fastify.addHook("preHandler", authenticate);

      // Middleware de contexto do cliente (deve vir após a autenticação)
      fastify.addHook("preHandler", clienteContextMiddleware);

      // Middleware de auditoria (deve vir após a autenticação e cliente)
      fastify.addHook("preHandler", auditMiddleware);

      await fastify.register(clienteRouter);
      await fastify.register(profissionalRouter);
      await fastify.register(plantaoRouter);
      await fastify.register(localAtendimentoRouter);
      await fastify.register(fechamentoRouter);
      await fastify.register(antecipacaoRouter);
      await fastify.register(especialidadeRouter);
      await fastify.register(presencaDiaPlantaoRouter);
      await fastify.register(dashboardRouter);
      await fastify.register(auditRouter);
      await fastify.register(usuariosRouter);
      await fastify.register(termosRouter);
      await fastify.register(profissionalPlantoesRouter);
      await fastify.register(profissionalDashboardRouter);
    },
    { prefix: "/api" }
  );
});

const start = async () => {
  try {
    await app.ready();

    await app.listen({ port, host: "0.0.0.0" });

    app.log.info("Server initialization complete");
  } catch (err) {
    app.log.error(err, "❌ Failed to start server");
    process.exit(1);
  }
};

start();
