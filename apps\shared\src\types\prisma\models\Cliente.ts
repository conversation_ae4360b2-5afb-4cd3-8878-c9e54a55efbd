
/* !!! This is code generated by Prisma. Do not edit directly. !!! */
/* eslint-disable */
// @ts-nocheck 
/*
 * This file exports the `Cliente` model and its related types.
 *
 * 🟢 You can import this file directly.
 */
import * as runtime from "@prisma/client/runtime/library"
import type * as $Enums from "../enums.ts"
import type * as Prisma from "../internal/prismaNamespace.ts"

/**
 * Model Cliente
 * 
 */
export type ClienteModel = runtime.Types.Result.DefaultSelection<Prisma.$ClientePayload>

export type AggregateCliente = {
  _count: ClienteCountAggregateOutputType | null
  _avg: ClienteAvgAggregateOutputType | null
  _sum: ClienteSumAggregateOutputType | null
  _min: ClienteMinAggregateOutputType | null
  _max: ClienteMaxAggregateOutputType | null
}

export type ClienteAvgAggregateOutputType = {
  id: number | null
  taxaPadrao: number | null
}

export type ClienteSumAggregateOutputType = {
  id: number | null
  taxaPadrao: number | null
}

export type ClienteMinAggregateOutputType = {
  id: number | null
  uuid: string | null
  nome: string | null
  cnpj: string | null
  email: string | null
  telefone: string | null
  taxaPadrao: number | null
  cep: string | null
  logradouro: string | null
  numero: string | null
  complemento: string | null
  bairro: string | null
  cidade: string | null
  uf: string | null
  fusoHorario: string | null
  ativo: boolean | null
  createdAt: Date | null
  updatedAt: Date | null
  deletedAt: Date | null
}

export type ClienteMaxAggregateOutputType = {
  id: number | null
  uuid: string | null
  nome: string | null
  cnpj: string | null
  email: string | null
  telefone: string | null
  taxaPadrao: number | null
  cep: string | null
  logradouro: string | null
  numero: string | null
  complemento: string | null
  bairro: string | null
  cidade: string | null
  uf: string | null
  fusoHorario: string | null
  ativo: boolean | null
  createdAt: Date | null
  updatedAt: Date | null
  deletedAt: Date | null
}

export type ClienteCountAggregateOutputType = {
  id: number
  uuid: number
  nome: number
  cnpj: number
  email: number
  telefone: number
  taxaPadrao: number
  cep: number
  logradouro: number
  numero: number
  complemento: number
  bairro: number
  cidade: number
  uf: number
  fusoHorario: number
  ativo: number
  createdAt: number
  updatedAt: number
  deletedAt: number
  _all: number
}


export type ClienteAvgAggregateInputType = {
  id?: true
  taxaPadrao?: true
}

export type ClienteSumAggregateInputType = {
  id?: true
  taxaPadrao?: true
}

export type ClienteMinAggregateInputType = {
  id?: true
  uuid?: true
  nome?: true
  cnpj?: true
  email?: true
  telefone?: true
  taxaPadrao?: true
  cep?: true
  logradouro?: true
  numero?: true
  complemento?: true
  bairro?: true
  cidade?: true
  uf?: true
  fusoHorario?: true
  ativo?: true
  createdAt?: true
  updatedAt?: true
  deletedAt?: true
}

export type ClienteMaxAggregateInputType = {
  id?: true
  uuid?: true
  nome?: true
  cnpj?: true
  email?: true
  telefone?: true
  taxaPadrao?: true
  cep?: true
  logradouro?: true
  numero?: true
  complemento?: true
  bairro?: true
  cidade?: true
  uf?: true
  fusoHorario?: true
  ativo?: true
  createdAt?: true
  updatedAt?: true
  deletedAt?: true
}

export type ClienteCountAggregateInputType = {
  id?: true
  uuid?: true
  nome?: true
  cnpj?: true
  email?: true
  telefone?: true
  taxaPadrao?: true
  cep?: true
  logradouro?: true
  numero?: true
  complemento?: true
  bairro?: true
  cidade?: true
  uf?: true
  fusoHorario?: true
  ativo?: true
  createdAt?: true
  updatedAt?: true
  deletedAt?: true
  _all?: true
}

export type ClienteAggregateArgs<ExtArgs extends runtime.Types.Extensions.InternalArgs = runtime.Types.Extensions.DefaultArgs> = {
  /**
   * Filter which Cliente to aggregate.
   */
  where?: Prisma.ClienteWhereInput
  /**
   * {@link https://www.prisma.io/docs/concepts/components/prisma-client/sorting Sorting Docs}
   * 
   * Determine the order of Clientes to fetch.
   */
  orderBy?: Prisma.ClienteOrderByWithRelationInput | Prisma.ClienteOrderByWithRelationInput[]
  /**
   * {@link https://www.prisma.io/docs/concepts/components/prisma-client/pagination#cursor-based-pagination Cursor Docs}
   * 
   * Sets the start position
   */
  cursor?: Prisma.ClienteWhereUniqueInput
  /**
   * {@link https://www.prisma.io/docs/concepts/components/prisma-client/pagination Pagination Docs}
   * 
   * Take `±n` Clientes from the position of the cursor.
   */
  take?: number
  /**
   * {@link https://www.prisma.io/docs/concepts/components/prisma-client/pagination Pagination Docs}
   * 
   * Skip the first `n` Clientes.
   */
  skip?: number
  /**
   * {@link https://www.prisma.io/docs/concepts/components/prisma-client/aggregations Aggregation Docs}
   * 
   * Count returned Clientes
  **/
  _count?: true | ClienteCountAggregateInputType
  /**
   * {@link https://www.prisma.io/docs/concepts/components/prisma-client/aggregations Aggregation Docs}
   * 
   * Select which fields to average
  **/
  _avg?: ClienteAvgAggregateInputType
  /**
   * {@link https://www.prisma.io/docs/concepts/components/prisma-client/aggregations Aggregation Docs}
   * 
   * Select which fields to sum
  **/
  _sum?: ClienteSumAggregateInputType
  /**
   * {@link https://www.prisma.io/docs/concepts/components/prisma-client/aggregations Aggregation Docs}
   * 
   * Select which fields to find the minimum value
  **/
  _min?: ClienteMinAggregateInputType
  /**
   * {@link https://www.prisma.io/docs/concepts/components/prisma-client/aggregations Aggregation Docs}
   * 
   * Select which fields to find the maximum value
  **/
  _max?: ClienteMaxAggregateInputType
}

export type GetClienteAggregateType<T extends ClienteAggregateArgs> = {
      [P in keyof T & keyof AggregateCliente]: P extends '_count' | 'count'
    ? T[P] extends true
      ? number
      : Prisma.GetScalarType<T[P], AggregateCliente[P]>
    : Prisma.GetScalarType<T[P], AggregateCliente[P]>
}




export type ClienteGroupByArgs<ExtArgs extends runtime.Types.Extensions.InternalArgs = runtime.Types.Extensions.DefaultArgs> = {
  where?: Prisma.ClienteWhereInput
  orderBy?: Prisma.ClienteOrderByWithAggregationInput | Prisma.ClienteOrderByWithAggregationInput[]
  by: Prisma.ClienteScalarFieldEnum[] | Prisma.ClienteScalarFieldEnum
  having?: Prisma.ClienteScalarWhereWithAggregatesInput
  take?: number
  skip?: number
  _count?: ClienteCountAggregateInputType | true
  _avg?: ClienteAvgAggregateInputType
  _sum?: ClienteSumAggregateInputType
  _min?: ClienteMinAggregateInputType
  _max?: ClienteMaxAggregateInputType
}

export type ClienteGroupByOutputType = {
  id: number
  uuid: string
  nome: string
  cnpj: string
  email: string | null
  telefone: string | null
  taxaPadrao: number
  cep: string | null
  logradouro: string | null
  numero: string | null
  complemento: string | null
  bairro: string | null
  cidade: string | null
  uf: string | null
  fusoHorario: string
  ativo: boolean
  createdAt: Date
  updatedAt: Date
  deletedAt: Date | null
  _count: ClienteCountAggregateOutputType | null
  _avg: ClienteAvgAggregateOutputType | null
  _sum: ClienteSumAggregateOutputType | null
  _min: ClienteMinAggregateOutputType | null
  _max: ClienteMaxAggregateOutputType | null
}

type GetClienteGroupByPayload<T extends ClienteGroupByArgs> = Prisma.PrismaPromise<
  Array<
    Prisma.PickEnumerable<ClienteGroupByOutputType, T['by']> &
      {
        [P in ((keyof T) & (keyof ClienteGroupByOutputType))]: P extends '_count'
          ? T[P] extends boolean
            ? number
            : Prisma.GetScalarType<T[P], ClienteGroupByOutputType[P]>
          : Prisma.GetScalarType<T[P], ClienteGroupByOutputType[P]>
      }
    >
  >



export type ClienteWhereInput = {
  AND?: Prisma.ClienteWhereInput | Prisma.ClienteWhereInput[]
  OR?: Prisma.ClienteWhereInput[]
  NOT?: Prisma.ClienteWhereInput | Prisma.ClienteWhereInput[]
  id?: Prisma.IntFilter<"Cliente"> | number
  uuid?: Prisma.StringFilter<"Cliente"> | string
  nome?: Prisma.StringFilter<"Cliente"> | string
  cnpj?: Prisma.StringFilter<"Cliente"> | string
  email?: Prisma.StringNullableFilter<"Cliente"> | string | null
  telefone?: Prisma.StringNullableFilter<"Cliente"> | string | null
  taxaPadrao?: Prisma.FloatFilter<"Cliente"> | number
  cep?: Prisma.StringNullableFilter<"Cliente"> | string | null
  logradouro?: Prisma.StringNullableFilter<"Cliente"> | string | null
  numero?: Prisma.StringNullableFilter<"Cliente"> | string | null
  complemento?: Prisma.StringNullableFilter<"Cliente"> | string | null
  bairro?: Prisma.StringNullableFilter<"Cliente"> | string | null
  cidade?: Prisma.StringNullableFilter<"Cliente"> | string | null
  uf?: Prisma.StringNullableFilter<"Cliente"> | string | null
  fusoHorario?: Prisma.StringFilter<"Cliente"> | string
  ativo?: Prisma.BoolFilter<"Cliente"> | boolean
  createdAt?: Prisma.DateTimeFilter<"Cliente"> | Date | string
  updatedAt?: Prisma.DateTimeFilter<"Cliente"> | Date | string
  deletedAt?: Prisma.DateTimeNullableFilter<"Cliente"> | Date | string | null
  locaisAtendimento?: Prisma.LocalAtendimentoListRelationFilter
  plantoes?: Prisma.PlantaoListRelationFilter
  usuarios?: Prisma.UsuarioClienteListRelationFilter
}

export type ClienteOrderByWithRelationInput = {
  id?: Prisma.SortOrder
  uuid?: Prisma.SortOrder
  nome?: Prisma.SortOrder
  cnpj?: Prisma.SortOrder
  email?: Prisma.SortOrderInput | Prisma.SortOrder
  telefone?: Prisma.SortOrderInput | Prisma.SortOrder
  taxaPadrao?: Prisma.SortOrder
  cep?: Prisma.SortOrderInput | Prisma.SortOrder
  logradouro?: Prisma.SortOrderInput | Prisma.SortOrder
  numero?: Prisma.SortOrderInput | Prisma.SortOrder
  complemento?: Prisma.SortOrderInput | Prisma.SortOrder
  bairro?: Prisma.SortOrderInput | Prisma.SortOrder
  cidade?: Prisma.SortOrderInput | Prisma.SortOrder
  uf?: Prisma.SortOrderInput | Prisma.SortOrder
  fusoHorario?: Prisma.SortOrder
  ativo?: Prisma.SortOrder
  createdAt?: Prisma.SortOrder
  updatedAt?: Prisma.SortOrder
  deletedAt?: Prisma.SortOrderInput | Prisma.SortOrder
  locaisAtendimento?: Prisma.LocalAtendimentoOrderByRelationAggregateInput
  plantoes?: Prisma.PlantaoOrderByRelationAggregateInput
  usuarios?: Prisma.UsuarioClienteOrderByRelationAggregateInput
  _relevance?: Prisma.ClienteOrderByRelevanceInput
}

export type ClienteWhereUniqueInput = Prisma.AtLeast<{
  id?: number
  uuid?: string
  cnpj?: string
  AND?: Prisma.ClienteWhereInput | Prisma.ClienteWhereInput[]
  OR?: Prisma.ClienteWhereInput[]
  NOT?: Prisma.ClienteWhereInput | Prisma.ClienteWhereInput[]
  nome?: Prisma.StringFilter<"Cliente"> | string
  email?: Prisma.StringNullableFilter<"Cliente"> | string | null
  telefone?: Prisma.StringNullableFilter<"Cliente"> | string | null
  taxaPadrao?: Prisma.FloatFilter<"Cliente"> | number
  cep?: Prisma.StringNullableFilter<"Cliente"> | string | null
  logradouro?: Prisma.StringNullableFilter<"Cliente"> | string | null
  numero?: Prisma.StringNullableFilter<"Cliente"> | string | null
  complemento?: Prisma.StringNullableFilter<"Cliente"> | string | null
  bairro?: Prisma.StringNullableFilter<"Cliente"> | string | null
  cidade?: Prisma.StringNullableFilter<"Cliente"> | string | null
  uf?: Prisma.StringNullableFilter<"Cliente"> | string | null
  fusoHorario?: Prisma.StringFilter<"Cliente"> | string
  ativo?: Prisma.BoolFilter<"Cliente"> | boolean
  createdAt?: Prisma.DateTimeFilter<"Cliente"> | Date | string
  updatedAt?: Prisma.DateTimeFilter<"Cliente"> | Date | string
  deletedAt?: Prisma.DateTimeNullableFilter<"Cliente"> | Date | string | null
  locaisAtendimento?: Prisma.LocalAtendimentoListRelationFilter
  plantoes?: Prisma.PlantaoListRelationFilter
  usuarios?: Prisma.UsuarioClienteListRelationFilter
}, "id" | "uuid" | "cnpj">

export type ClienteOrderByWithAggregationInput = {
  id?: Prisma.SortOrder
  uuid?: Prisma.SortOrder
  nome?: Prisma.SortOrder
  cnpj?: Prisma.SortOrder
  email?: Prisma.SortOrderInput | Prisma.SortOrder
  telefone?: Prisma.SortOrderInput | Prisma.SortOrder
  taxaPadrao?: Prisma.SortOrder
  cep?: Prisma.SortOrderInput | Prisma.SortOrder
  logradouro?: Prisma.SortOrderInput | Prisma.SortOrder
  numero?: Prisma.SortOrderInput | Prisma.SortOrder
  complemento?: Prisma.SortOrderInput | Prisma.SortOrder
  bairro?: Prisma.SortOrderInput | Prisma.SortOrder
  cidade?: Prisma.SortOrderInput | Prisma.SortOrder
  uf?: Prisma.SortOrderInput | Prisma.SortOrder
  fusoHorario?: Prisma.SortOrder
  ativo?: Prisma.SortOrder
  createdAt?: Prisma.SortOrder
  updatedAt?: Prisma.SortOrder
  deletedAt?: Prisma.SortOrderInput | Prisma.SortOrder
  _count?: Prisma.ClienteCountOrderByAggregateInput
  _avg?: Prisma.ClienteAvgOrderByAggregateInput
  _max?: Prisma.ClienteMaxOrderByAggregateInput
  _min?: Prisma.ClienteMinOrderByAggregateInput
  _sum?: Prisma.ClienteSumOrderByAggregateInput
}

export type ClienteScalarWhereWithAggregatesInput = {
  AND?: Prisma.ClienteScalarWhereWithAggregatesInput | Prisma.ClienteScalarWhereWithAggregatesInput[]
  OR?: Prisma.ClienteScalarWhereWithAggregatesInput[]
  NOT?: Prisma.ClienteScalarWhereWithAggregatesInput | Prisma.ClienteScalarWhereWithAggregatesInput[]
  id?: Prisma.IntWithAggregatesFilter<"Cliente"> | number
  uuid?: Prisma.StringWithAggregatesFilter<"Cliente"> | string
  nome?: Prisma.StringWithAggregatesFilter<"Cliente"> | string
  cnpj?: Prisma.StringWithAggregatesFilter<"Cliente"> | string
  email?: Prisma.StringNullableWithAggregatesFilter<"Cliente"> | string | null
  telefone?: Prisma.StringNullableWithAggregatesFilter<"Cliente"> | string | null
  taxaPadrao?: Prisma.FloatWithAggregatesFilter<"Cliente"> | number
  cep?: Prisma.StringNullableWithAggregatesFilter<"Cliente"> | string | null
  logradouro?: Prisma.StringNullableWithAggregatesFilter<"Cliente"> | string | null
  numero?: Prisma.StringNullableWithAggregatesFilter<"Cliente"> | string | null
  complemento?: Prisma.StringNullableWithAggregatesFilter<"Cliente"> | string | null
  bairro?: Prisma.StringNullableWithAggregatesFilter<"Cliente"> | string | null
  cidade?: Prisma.StringNullableWithAggregatesFilter<"Cliente"> | string | null
  uf?: Prisma.StringNullableWithAggregatesFilter<"Cliente"> | string | null
  fusoHorario?: Prisma.StringWithAggregatesFilter<"Cliente"> | string
  ativo?: Prisma.BoolWithAggregatesFilter<"Cliente"> | boolean
  createdAt?: Prisma.DateTimeWithAggregatesFilter<"Cliente"> | Date | string
  updatedAt?: Prisma.DateTimeWithAggregatesFilter<"Cliente"> | Date | string
  deletedAt?: Prisma.DateTimeNullableWithAggregatesFilter<"Cliente"> | Date | string | null
}

export type ClienteCreateInput = {
  uuid?: string
  nome: string
  cnpj: string
  email?: string | null
  telefone?: string | null
  taxaPadrao: number
  cep?: string | null
  logradouro?: string | null
  numero?: string | null
  complemento?: string | null
  bairro?: string | null
  cidade?: string | null
  uf?: string | null
  fusoHorario?: string
  ativo?: boolean
  createdAt?: Date | string
  updatedAt?: Date | string
  deletedAt?: Date | string | null
  locaisAtendimento?: Prisma.LocalAtendimentoCreateNestedManyWithoutClienteInput
  plantoes?: Prisma.PlantaoCreateNestedManyWithoutClienteInput
  usuarios?: Prisma.UsuarioClienteCreateNestedManyWithoutClienteInput
}

export type ClienteUncheckedCreateInput = {
  id?: number
  uuid?: string
  nome: string
  cnpj: string
  email?: string | null
  telefone?: string | null
  taxaPadrao: number
  cep?: string | null
  logradouro?: string | null
  numero?: string | null
  complemento?: string | null
  bairro?: string | null
  cidade?: string | null
  uf?: string | null
  fusoHorario?: string
  ativo?: boolean
  createdAt?: Date | string
  updatedAt?: Date | string
  deletedAt?: Date | string | null
  locaisAtendimento?: Prisma.LocalAtendimentoUncheckedCreateNestedManyWithoutClienteInput
  plantoes?: Prisma.PlantaoUncheckedCreateNestedManyWithoutClienteInput
  usuarios?: Prisma.UsuarioClienteUncheckedCreateNestedManyWithoutClienteInput
}

export type ClienteUpdateInput = {
  uuid?: Prisma.StringFieldUpdateOperationsInput | string
  nome?: Prisma.StringFieldUpdateOperationsInput | string
  cnpj?: Prisma.StringFieldUpdateOperationsInput | string
  email?: Prisma.NullableStringFieldUpdateOperationsInput | string | null
  telefone?: Prisma.NullableStringFieldUpdateOperationsInput | string | null
  taxaPadrao?: Prisma.FloatFieldUpdateOperationsInput | number
  cep?: Prisma.NullableStringFieldUpdateOperationsInput | string | null
  logradouro?: Prisma.NullableStringFieldUpdateOperationsInput | string | null
  numero?: Prisma.NullableStringFieldUpdateOperationsInput | string | null
  complemento?: Prisma.NullableStringFieldUpdateOperationsInput | string | null
  bairro?: Prisma.NullableStringFieldUpdateOperationsInput | string | null
  cidade?: Prisma.NullableStringFieldUpdateOperationsInput | string | null
  uf?: Prisma.NullableStringFieldUpdateOperationsInput | string | null
  fusoHorario?: Prisma.StringFieldUpdateOperationsInput | string
  ativo?: Prisma.BoolFieldUpdateOperationsInput | boolean
  createdAt?: Prisma.DateTimeFieldUpdateOperationsInput | Date | string
  updatedAt?: Prisma.DateTimeFieldUpdateOperationsInput | Date | string
  deletedAt?: Prisma.NullableDateTimeFieldUpdateOperationsInput | Date | string | null
  locaisAtendimento?: Prisma.LocalAtendimentoUpdateManyWithoutClienteNestedInput
  plantoes?: Prisma.PlantaoUpdateManyWithoutClienteNestedInput
  usuarios?: Prisma.UsuarioClienteUpdateManyWithoutClienteNestedInput
}

export type ClienteUncheckedUpdateInput = {
  id?: Prisma.IntFieldUpdateOperationsInput | number
  uuid?: Prisma.StringFieldUpdateOperationsInput | string
  nome?: Prisma.StringFieldUpdateOperationsInput | string
  cnpj?: Prisma.StringFieldUpdateOperationsInput | string
  email?: Prisma.NullableStringFieldUpdateOperationsInput | string | null
  telefone?: Prisma.NullableStringFieldUpdateOperationsInput | string | null
  taxaPadrao?: Prisma.FloatFieldUpdateOperationsInput | number
  cep?: Prisma.NullableStringFieldUpdateOperationsInput | string | null
  logradouro?: Prisma.NullableStringFieldUpdateOperationsInput | string | null
  numero?: Prisma.NullableStringFieldUpdateOperationsInput | string | null
  complemento?: Prisma.NullableStringFieldUpdateOperationsInput | string | null
  bairro?: Prisma.NullableStringFieldUpdateOperationsInput | string | null
  cidade?: Prisma.NullableStringFieldUpdateOperationsInput | string | null
  uf?: Prisma.NullableStringFieldUpdateOperationsInput | string | null
  fusoHorario?: Prisma.StringFieldUpdateOperationsInput | string
  ativo?: Prisma.BoolFieldUpdateOperationsInput | boolean
  createdAt?: Prisma.DateTimeFieldUpdateOperationsInput | Date | string
  updatedAt?: Prisma.DateTimeFieldUpdateOperationsInput | Date | string
  deletedAt?: Prisma.NullableDateTimeFieldUpdateOperationsInput | Date | string | null
  locaisAtendimento?: Prisma.LocalAtendimentoUncheckedUpdateManyWithoutClienteNestedInput
  plantoes?: Prisma.PlantaoUncheckedUpdateManyWithoutClienteNestedInput
  usuarios?: Prisma.UsuarioClienteUncheckedUpdateManyWithoutClienteNestedInput
}

export type ClienteCreateManyInput = {
  id?: number
  uuid?: string
  nome: string
  cnpj: string
  email?: string | null
  telefone?: string | null
  taxaPadrao: number
  cep?: string | null
  logradouro?: string | null
  numero?: string | null
  complemento?: string | null
  bairro?: string | null
  cidade?: string | null
  uf?: string | null
  fusoHorario?: string
  ativo?: boolean
  createdAt?: Date | string
  updatedAt?: Date | string
  deletedAt?: Date | string | null
}

export type ClienteUpdateManyMutationInput = {
  uuid?: Prisma.StringFieldUpdateOperationsInput | string
  nome?: Prisma.StringFieldUpdateOperationsInput | string
  cnpj?: Prisma.StringFieldUpdateOperationsInput | string
  email?: Prisma.NullableStringFieldUpdateOperationsInput | string | null
  telefone?: Prisma.NullableStringFieldUpdateOperationsInput | string | null
  taxaPadrao?: Prisma.FloatFieldUpdateOperationsInput | number
  cep?: Prisma.NullableStringFieldUpdateOperationsInput | string | null
  logradouro?: Prisma.NullableStringFieldUpdateOperationsInput | string | null
  numero?: Prisma.NullableStringFieldUpdateOperationsInput | string | null
  complemento?: Prisma.NullableStringFieldUpdateOperationsInput | string | null
  bairro?: Prisma.NullableStringFieldUpdateOperationsInput | string | null
  cidade?: Prisma.NullableStringFieldUpdateOperationsInput | string | null
  uf?: Prisma.NullableStringFieldUpdateOperationsInput | string | null
  fusoHorario?: Prisma.StringFieldUpdateOperationsInput | string
  ativo?: Prisma.BoolFieldUpdateOperationsInput | boolean
  createdAt?: Prisma.DateTimeFieldUpdateOperationsInput | Date | string
  updatedAt?: Prisma.DateTimeFieldUpdateOperationsInput | Date | string
  deletedAt?: Prisma.NullableDateTimeFieldUpdateOperationsInput | Date | string | null
}

export type ClienteUncheckedUpdateManyInput = {
  id?: Prisma.IntFieldUpdateOperationsInput | number
  uuid?: Prisma.StringFieldUpdateOperationsInput | string
  nome?: Prisma.StringFieldUpdateOperationsInput | string
  cnpj?: Prisma.StringFieldUpdateOperationsInput | string
  email?: Prisma.NullableStringFieldUpdateOperationsInput | string | null
  telefone?: Prisma.NullableStringFieldUpdateOperationsInput | string | null
  taxaPadrao?: Prisma.FloatFieldUpdateOperationsInput | number
  cep?: Prisma.NullableStringFieldUpdateOperationsInput | string | null
  logradouro?: Prisma.NullableStringFieldUpdateOperationsInput | string | null
  numero?: Prisma.NullableStringFieldUpdateOperationsInput | string | null
  complemento?: Prisma.NullableStringFieldUpdateOperationsInput | string | null
  bairro?: Prisma.NullableStringFieldUpdateOperationsInput | string | null
  cidade?: Prisma.NullableStringFieldUpdateOperationsInput | string | null
  uf?: Prisma.NullableStringFieldUpdateOperationsInput | string | null
  fusoHorario?: Prisma.StringFieldUpdateOperationsInput | string
  ativo?: Prisma.BoolFieldUpdateOperationsInput | boolean
  createdAt?: Prisma.DateTimeFieldUpdateOperationsInput | Date | string
  updatedAt?: Prisma.DateTimeFieldUpdateOperationsInput | Date | string
  deletedAt?: Prisma.NullableDateTimeFieldUpdateOperationsInput | Date | string | null
}

export type ClienteOrderByRelevanceInput = {
  fields: Prisma.ClienteOrderByRelevanceFieldEnum | Prisma.ClienteOrderByRelevanceFieldEnum[]
  sort: Prisma.SortOrder
  search: string
}

export type ClienteCountOrderByAggregateInput = {
  id?: Prisma.SortOrder
  uuid?: Prisma.SortOrder
  nome?: Prisma.SortOrder
  cnpj?: Prisma.SortOrder
  email?: Prisma.SortOrder
  telefone?: Prisma.SortOrder
  taxaPadrao?: Prisma.SortOrder
  cep?: Prisma.SortOrder
  logradouro?: Prisma.SortOrder
  numero?: Prisma.SortOrder
  complemento?: Prisma.SortOrder
  bairro?: Prisma.SortOrder
  cidade?: Prisma.SortOrder
  uf?: Prisma.SortOrder
  fusoHorario?: Prisma.SortOrder
  ativo?: Prisma.SortOrder
  createdAt?: Prisma.SortOrder
  updatedAt?: Prisma.SortOrder
  deletedAt?: Prisma.SortOrder
}

export type ClienteAvgOrderByAggregateInput = {
  id?: Prisma.SortOrder
  taxaPadrao?: Prisma.SortOrder
}

export type ClienteMaxOrderByAggregateInput = {
  id?: Prisma.SortOrder
  uuid?: Prisma.SortOrder
  nome?: Prisma.SortOrder
  cnpj?: Prisma.SortOrder
  email?: Prisma.SortOrder
  telefone?: Prisma.SortOrder
  taxaPadrao?: Prisma.SortOrder
  cep?: Prisma.SortOrder
  logradouro?: Prisma.SortOrder
  numero?: Prisma.SortOrder
  complemento?: Prisma.SortOrder
  bairro?: Prisma.SortOrder
  cidade?: Prisma.SortOrder
  uf?: Prisma.SortOrder
  fusoHorario?: Prisma.SortOrder
  ativo?: Prisma.SortOrder
  createdAt?: Prisma.SortOrder
  updatedAt?: Prisma.SortOrder
  deletedAt?: Prisma.SortOrder
}

export type ClienteMinOrderByAggregateInput = {
  id?: Prisma.SortOrder
  uuid?: Prisma.SortOrder
  nome?: Prisma.SortOrder
  cnpj?: Prisma.SortOrder
  email?: Prisma.SortOrder
  telefone?: Prisma.SortOrder
  taxaPadrao?: Prisma.SortOrder
  cep?: Prisma.SortOrder
  logradouro?: Prisma.SortOrder
  numero?: Prisma.SortOrder
  complemento?: Prisma.SortOrder
  bairro?: Prisma.SortOrder
  cidade?: Prisma.SortOrder
  uf?: Prisma.SortOrder
  fusoHorario?: Prisma.SortOrder
  ativo?: Prisma.SortOrder
  createdAt?: Prisma.SortOrder
  updatedAt?: Prisma.SortOrder
  deletedAt?: Prisma.SortOrder
}

export type ClienteSumOrderByAggregateInput = {
  id?: Prisma.SortOrder
  taxaPadrao?: Prisma.SortOrder
}

export type ClienteScalarRelationFilter = {
  is?: Prisma.ClienteWhereInput
  isNot?: Prisma.ClienteWhereInput
}

export type FloatFieldUpdateOperationsInput = {
  set?: number
  increment?: number
  decrement?: number
  multiply?: number
  divide?: number
}

export type ClienteCreateNestedOneWithoutUsuariosInput = {
  create?: Prisma.XOR<Prisma.ClienteCreateWithoutUsuariosInput, Prisma.ClienteUncheckedCreateWithoutUsuariosInput>
  connectOrCreate?: Prisma.ClienteCreateOrConnectWithoutUsuariosInput
  connect?: Prisma.ClienteWhereUniqueInput
}

export type ClienteUpdateOneRequiredWithoutUsuariosNestedInput = {
  create?: Prisma.XOR<Prisma.ClienteCreateWithoutUsuariosInput, Prisma.ClienteUncheckedCreateWithoutUsuariosInput>
  connectOrCreate?: Prisma.ClienteCreateOrConnectWithoutUsuariosInput
  upsert?: Prisma.ClienteUpsertWithoutUsuariosInput
  connect?: Prisma.ClienteWhereUniqueInput
  update?: Prisma.XOR<Prisma.XOR<Prisma.ClienteUpdateToOneWithWhereWithoutUsuariosInput, Prisma.ClienteUpdateWithoutUsuariosInput>, Prisma.ClienteUncheckedUpdateWithoutUsuariosInput>
}

export type ClienteCreateNestedOneWithoutLocaisAtendimentoInput = {
  create?: Prisma.XOR<Prisma.ClienteCreateWithoutLocaisAtendimentoInput, Prisma.ClienteUncheckedCreateWithoutLocaisAtendimentoInput>
  connectOrCreate?: Prisma.ClienteCreateOrConnectWithoutLocaisAtendimentoInput
  connect?: Prisma.ClienteWhereUniqueInput
}

export type ClienteUpdateOneRequiredWithoutLocaisAtendimentoNestedInput = {
  create?: Prisma.XOR<Prisma.ClienteCreateWithoutLocaisAtendimentoInput, Prisma.ClienteUncheckedCreateWithoutLocaisAtendimentoInput>
  connectOrCreate?: Prisma.ClienteCreateOrConnectWithoutLocaisAtendimentoInput
  upsert?: Prisma.ClienteUpsertWithoutLocaisAtendimentoInput
  connect?: Prisma.ClienteWhereUniqueInput
  update?: Prisma.XOR<Prisma.XOR<Prisma.ClienteUpdateToOneWithWhereWithoutLocaisAtendimentoInput, Prisma.ClienteUpdateWithoutLocaisAtendimentoInput>, Prisma.ClienteUncheckedUpdateWithoutLocaisAtendimentoInput>
}

export type ClienteCreateNestedOneWithoutPlantoesInput = {
  create?: Prisma.XOR<Prisma.ClienteCreateWithoutPlantoesInput, Prisma.ClienteUncheckedCreateWithoutPlantoesInput>
  connectOrCreate?: Prisma.ClienteCreateOrConnectWithoutPlantoesInput
  connect?: Prisma.ClienteWhereUniqueInput
}

export type ClienteUpdateOneRequiredWithoutPlantoesNestedInput = {
  create?: Prisma.XOR<Prisma.ClienteCreateWithoutPlantoesInput, Prisma.ClienteUncheckedCreateWithoutPlantoesInput>
  connectOrCreate?: Prisma.ClienteCreateOrConnectWithoutPlantoesInput
  upsert?: Prisma.ClienteUpsertWithoutPlantoesInput
  connect?: Prisma.ClienteWhereUniqueInput
  update?: Prisma.XOR<Prisma.XOR<Prisma.ClienteUpdateToOneWithWhereWithoutPlantoesInput, Prisma.ClienteUpdateWithoutPlantoesInput>, Prisma.ClienteUncheckedUpdateWithoutPlantoesInput>
}

export type ClienteCreateWithoutUsuariosInput = {
  uuid?: string
  nome: string
  cnpj: string
  email?: string | null
  telefone?: string | null
  taxaPadrao: number
  cep?: string | null
  logradouro?: string | null
  numero?: string | null
  complemento?: string | null
  bairro?: string | null
  cidade?: string | null
  uf?: string | null
  fusoHorario?: string
  ativo?: boolean
  createdAt?: Date | string
  updatedAt?: Date | string
  deletedAt?: Date | string | null
  locaisAtendimento?: Prisma.LocalAtendimentoCreateNestedManyWithoutClienteInput
  plantoes?: Prisma.PlantaoCreateNestedManyWithoutClienteInput
}

export type ClienteUncheckedCreateWithoutUsuariosInput = {
  id?: number
  uuid?: string
  nome: string
  cnpj: string
  email?: string | null
  telefone?: string | null
  taxaPadrao: number
  cep?: string | null
  logradouro?: string | null
  numero?: string | null
  complemento?: string | null
  bairro?: string | null
  cidade?: string | null
  uf?: string | null
  fusoHorario?: string
  ativo?: boolean
  createdAt?: Date | string
  updatedAt?: Date | string
  deletedAt?: Date | string | null
  locaisAtendimento?: Prisma.LocalAtendimentoUncheckedCreateNestedManyWithoutClienteInput
  plantoes?: Prisma.PlantaoUncheckedCreateNestedManyWithoutClienteInput
}

export type ClienteCreateOrConnectWithoutUsuariosInput = {
  where: Prisma.ClienteWhereUniqueInput
  create: Prisma.XOR<Prisma.ClienteCreateWithoutUsuariosInput, Prisma.ClienteUncheckedCreateWithoutUsuariosInput>
}

export type ClienteUpsertWithoutUsuariosInput = {
  update: Prisma.XOR<Prisma.ClienteUpdateWithoutUsuariosInput, Prisma.ClienteUncheckedUpdateWithoutUsuariosInput>
  create: Prisma.XOR<Prisma.ClienteCreateWithoutUsuariosInput, Prisma.ClienteUncheckedCreateWithoutUsuariosInput>
  where?: Prisma.ClienteWhereInput
}

export type ClienteUpdateToOneWithWhereWithoutUsuariosInput = {
  where?: Prisma.ClienteWhereInput
  data: Prisma.XOR<Prisma.ClienteUpdateWithoutUsuariosInput, Prisma.ClienteUncheckedUpdateWithoutUsuariosInput>
}

export type ClienteUpdateWithoutUsuariosInput = {
  uuid?: Prisma.StringFieldUpdateOperationsInput | string
  nome?: Prisma.StringFieldUpdateOperationsInput | string
  cnpj?: Prisma.StringFieldUpdateOperationsInput | string
  email?: Prisma.NullableStringFieldUpdateOperationsInput | string | null
  telefone?: Prisma.NullableStringFieldUpdateOperationsInput | string | null
  taxaPadrao?: Prisma.FloatFieldUpdateOperationsInput | number
  cep?: Prisma.NullableStringFieldUpdateOperationsInput | string | null
  logradouro?: Prisma.NullableStringFieldUpdateOperationsInput | string | null
  numero?: Prisma.NullableStringFieldUpdateOperationsInput | string | null
  complemento?: Prisma.NullableStringFieldUpdateOperationsInput | string | null
  bairro?: Prisma.NullableStringFieldUpdateOperationsInput | string | null
  cidade?: Prisma.NullableStringFieldUpdateOperationsInput | string | null
  uf?: Prisma.NullableStringFieldUpdateOperationsInput | string | null
  fusoHorario?: Prisma.StringFieldUpdateOperationsInput | string
  ativo?: Prisma.BoolFieldUpdateOperationsInput | boolean
  createdAt?: Prisma.DateTimeFieldUpdateOperationsInput | Date | string
  updatedAt?: Prisma.DateTimeFieldUpdateOperationsInput | Date | string
  deletedAt?: Prisma.NullableDateTimeFieldUpdateOperationsInput | Date | string | null
  locaisAtendimento?: Prisma.LocalAtendimentoUpdateManyWithoutClienteNestedInput
  plantoes?: Prisma.PlantaoUpdateManyWithoutClienteNestedInput
}

export type ClienteUncheckedUpdateWithoutUsuariosInput = {
  id?: Prisma.IntFieldUpdateOperationsInput | number
  uuid?: Prisma.StringFieldUpdateOperationsInput | string
  nome?: Prisma.StringFieldUpdateOperationsInput | string
  cnpj?: Prisma.StringFieldUpdateOperationsInput | string
  email?: Prisma.NullableStringFieldUpdateOperationsInput | string | null
  telefone?: Prisma.NullableStringFieldUpdateOperationsInput | string | null
  taxaPadrao?: Prisma.FloatFieldUpdateOperationsInput | number
  cep?: Prisma.NullableStringFieldUpdateOperationsInput | string | null
  logradouro?: Prisma.NullableStringFieldUpdateOperationsInput | string | null
  numero?: Prisma.NullableStringFieldUpdateOperationsInput | string | null
  complemento?: Prisma.NullableStringFieldUpdateOperationsInput | string | null
  bairro?: Prisma.NullableStringFieldUpdateOperationsInput | string | null
  cidade?: Prisma.NullableStringFieldUpdateOperationsInput | string | null
  uf?: Prisma.NullableStringFieldUpdateOperationsInput | string | null
  fusoHorario?: Prisma.StringFieldUpdateOperationsInput | string
  ativo?: Prisma.BoolFieldUpdateOperationsInput | boolean
  createdAt?: Prisma.DateTimeFieldUpdateOperationsInput | Date | string
  updatedAt?: Prisma.DateTimeFieldUpdateOperationsInput | Date | string
  deletedAt?: Prisma.NullableDateTimeFieldUpdateOperationsInput | Date | string | null
  locaisAtendimento?: Prisma.LocalAtendimentoUncheckedUpdateManyWithoutClienteNestedInput
  plantoes?: Prisma.PlantaoUncheckedUpdateManyWithoutClienteNestedInput
}

export type ClienteCreateWithoutLocaisAtendimentoInput = {
  uuid?: string
  nome: string
  cnpj: string
  email?: string | null
  telefone?: string | null
  taxaPadrao: number
  cep?: string | null
  logradouro?: string | null
  numero?: string | null
  complemento?: string | null
  bairro?: string | null
  cidade?: string | null
  uf?: string | null
  fusoHorario?: string
  ativo?: boolean
  createdAt?: Date | string
  updatedAt?: Date | string
  deletedAt?: Date | string | null
  plantoes?: Prisma.PlantaoCreateNestedManyWithoutClienteInput
  usuarios?: Prisma.UsuarioClienteCreateNestedManyWithoutClienteInput
}

export type ClienteUncheckedCreateWithoutLocaisAtendimentoInput = {
  id?: number
  uuid?: string
  nome: string
  cnpj: string
  email?: string | null
  telefone?: string | null
  taxaPadrao: number
  cep?: string | null
  logradouro?: string | null
  numero?: string | null
  complemento?: string | null
  bairro?: string | null
  cidade?: string | null
  uf?: string | null
  fusoHorario?: string
  ativo?: boolean
  createdAt?: Date | string
  updatedAt?: Date | string
  deletedAt?: Date | string | null
  plantoes?: Prisma.PlantaoUncheckedCreateNestedManyWithoutClienteInput
  usuarios?: Prisma.UsuarioClienteUncheckedCreateNestedManyWithoutClienteInput
}

export type ClienteCreateOrConnectWithoutLocaisAtendimentoInput = {
  where: Prisma.ClienteWhereUniqueInput
  create: Prisma.XOR<Prisma.ClienteCreateWithoutLocaisAtendimentoInput, Prisma.ClienteUncheckedCreateWithoutLocaisAtendimentoInput>
}

export type ClienteUpsertWithoutLocaisAtendimentoInput = {
  update: Prisma.XOR<Prisma.ClienteUpdateWithoutLocaisAtendimentoInput, Prisma.ClienteUncheckedUpdateWithoutLocaisAtendimentoInput>
  create: Prisma.XOR<Prisma.ClienteCreateWithoutLocaisAtendimentoInput, Prisma.ClienteUncheckedCreateWithoutLocaisAtendimentoInput>
  where?: Prisma.ClienteWhereInput
}

export type ClienteUpdateToOneWithWhereWithoutLocaisAtendimentoInput = {
  where?: Prisma.ClienteWhereInput
  data: Prisma.XOR<Prisma.ClienteUpdateWithoutLocaisAtendimentoInput, Prisma.ClienteUncheckedUpdateWithoutLocaisAtendimentoInput>
}

export type ClienteUpdateWithoutLocaisAtendimentoInput = {
  uuid?: Prisma.StringFieldUpdateOperationsInput | string
  nome?: Prisma.StringFieldUpdateOperationsInput | string
  cnpj?: Prisma.StringFieldUpdateOperationsInput | string
  email?: Prisma.NullableStringFieldUpdateOperationsInput | string | null
  telefone?: Prisma.NullableStringFieldUpdateOperationsInput | string | null
  taxaPadrao?: Prisma.FloatFieldUpdateOperationsInput | number
  cep?: Prisma.NullableStringFieldUpdateOperationsInput | string | null
  logradouro?: Prisma.NullableStringFieldUpdateOperationsInput | string | null
  numero?: Prisma.NullableStringFieldUpdateOperationsInput | string | null
  complemento?: Prisma.NullableStringFieldUpdateOperationsInput | string | null
  bairro?: Prisma.NullableStringFieldUpdateOperationsInput | string | null
  cidade?: Prisma.NullableStringFieldUpdateOperationsInput | string | null
  uf?: Prisma.NullableStringFieldUpdateOperationsInput | string | null
  fusoHorario?: Prisma.StringFieldUpdateOperationsInput | string
  ativo?: Prisma.BoolFieldUpdateOperationsInput | boolean
  createdAt?: Prisma.DateTimeFieldUpdateOperationsInput | Date | string
  updatedAt?: Prisma.DateTimeFieldUpdateOperationsInput | Date | string
  deletedAt?: Prisma.NullableDateTimeFieldUpdateOperationsInput | Date | string | null
  plantoes?: Prisma.PlantaoUpdateManyWithoutClienteNestedInput
  usuarios?: Prisma.UsuarioClienteUpdateManyWithoutClienteNestedInput
}

export type ClienteUncheckedUpdateWithoutLocaisAtendimentoInput = {
  id?: Prisma.IntFieldUpdateOperationsInput | number
  uuid?: Prisma.StringFieldUpdateOperationsInput | string
  nome?: Prisma.StringFieldUpdateOperationsInput | string
  cnpj?: Prisma.StringFieldUpdateOperationsInput | string
  email?: Prisma.NullableStringFieldUpdateOperationsInput | string | null
  telefone?: Prisma.NullableStringFieldUpdateOperationsInput | string | null
  taxaPadrao?: Prisma.FloatFieldUpdateOperationsInput | number
  cep?: Prisma.NullableStringFieldUpdateOperationsInput | string | null
  logradouro?: Prisma.NullableStringFieldUpdateOperationsInput | string | null
  numero?: Prisma.NullableStringFieldUpdateOperationsInput | string | null
  complemento?: Prisma.NullableStringFieldUpdateOperationsInput | string | null
  bairro?: Prisma.NullableStringFieldUpdateOperationsInput | string | null
  cidade?: Prisma.NullableStringFieldUpdateOperationsInput | string | null
  uf?: Prisma.NullableStringFieldUpdateOperationsInput | string | null
  fusoHorario?: Prisma.StringFieldUpdateOperationsInput | string
  ativo?: Prisma.BoolFieldUpdateOperationsInput | boolean
  createdAt?: Prisma.DateTimeFieldUpdateOperationsInput | Date | string
  updatedAt?: Prisma.DateTimeFieldUpdateOperationsInput | Date | string
  deletedAt?: Prisma.NullableDateTimeFieldUpdateOperationsInput | Date | string | null
  plantoes?: Prisma.PlantaoUncheckedUpdateManyWithoutClienteNestedInput
  usuarios?: Prisma.UsuarioClienteUncheckedUpdateManyWithoutClienteNestedInput
}

export type ClienteCreateWithoutPlantoesInput = {
  uuid?: string
  nome: string
  cnpj: string
  email?: string | null
  telefone?: string | null
  taxaPadrao: number
  cep?: string | null
  logradouro?: string | null
  numero?: string | null
  complemento?: string | null
  bairro?: string | null
  cidade?: string | null
  uf?: string | null
  fusoHorario?: string
  ativo?: boolean
  createdAt?: Date | string
  updatedAt?: Date | string
  deletedAt?: Date | string | null
  locaisAtendimento?: Prisma.LocalAtendimentoCreateNestedManyWithoutClienteInput
  usuarios?: Prisma.UsuarioClienteCreateNestedManyWithoutClienteInput
}

export type ClienteUncheckedCreateWithoutPlantoesInput = {
  id?: number
  uuid?: string
  nome: string
  cnpj: string
  email?: string | null
  telefone?: string | null
  taxaPadrao: number
  cep?: string | null
  logradouro?: string | null
  numero?: string | null
  complemento?: string | null
  bairro?: string | null
  cidade?: string | null
  uf?: string | null
  fusoHorario?: string
  ativo?: boolean
  createdAt?: Date | string
  updatedAt?: Date | string
  deletedAt?: Date | string | null
  locaisAtendimento?: Prisma.LocalAtendimentoUncheckedCreateNestedManyWithoutClienteInput
  usuarios?: Prisma.UsuarioClienteUncheckedCreateNestedManyWithoutClienteInput
}

export type ClienteCreateOrConnectWithoutPlantoesInput = {
  where: Prisma.ClienteWhereUniqueInput
  create: Prisma.XOR<Prisma.ClienteCreateWithoutPlantoesInput, Prisma.ClienteUncheckedCreateWithoutPlantoesInput>
}

export type ClienteUpsertWithoutPlantoesInput = {
  update: Prisma.XOR<Prisma.ClienteUpdateWithoutPlantoesInput, Prisma.ClienteUncheckedUpdateWithoutPlantoesInput>
  create: Prisma.XOR<Prisma.ClienteCreateWithoutPlantoesInput, Prisma.ClienteUncheckedCreateWithoutPlantoesInput>
  where?: Prisma.ClienteWhereInput
}

export type ClienteUpdateToOneWithWhereWithoutPlantoesInput = {
  where?: Prisma.ClienteWhereInput
  data: Prisma.XOR<Prisma.ClienteUpdateWithoutPlantoesInput, Prisma.ClienteUncheckedUpdateWithoutPlantoesInput>
}

export type ClienteUpdateWithoutPlantoesInput = {
  uuid?: Prisma.StringFieldUpdateOperationsInput | string
  nome?: Prisma.StringFieldUpdateOperationsInput | string
  cnpj?: Prisma.StringFieldUpdateOperationsInput | string
  email?: Prisma.NullableStringFieldUpdateOperationsInput | string | null
  telefone?: Prisma.NullableStringFieldUpdateOperationsInput | string | null
  taxaPadrao?: Prisma.FloatFieldUpdateOperationsInput | number
  cep?: Prisma.NullableStringFieldUpdateOperationsInput | string | null
  logradouro?: Prisma.NullableStringFieldUpdateOperationsInput | string | null
  numero?: Prisma.NullableStringFieldUpdateOperationsInput | string | null
  complemento?: Prisma.NullableStringFieldUpdateOperationsInput | string | null
  bairro?: Prisma.NullableStringFieldUpdateOperationsInput | string | null
  cidade?: Prisma.NullableStringFieldUpdateOperationsInput | string | null
  uf?: Prisma.NullableStringFieldUpdateOperationsInput | string | null
  fusoHorario?: Prisma.StringFieldUpdateOperationsInput | string
  ativo?: Prisma.BoolFieldUpdateOperationsInput | boolean
  createdAt?: Prisma.DateTimeFieldUpdateOperationsInput | Date | string
  updatedAt?: Prisma.DateTimeFieldUpdateOperationsInput | Date | string
  deletedAt?: Prisma.NullableDateTimeFieldUpdateOperationsInput | Date | string | null
  locaisAtendimento?: Prisma.LocalAtendimentoUpdateManyWithoutClienteNestedInput
  usuarios?: Prisma.UsuarioClienteUpdateManyWithoutClienteNestedInput
}

export type ClienteUncheckedUpdateWithoutPlantoesInput = {
  id?: Prisma.IntFieldUpdateOperationsInput | number
  uuid?: Prisma.StringFieldUpdateOperationsInput | string
  nome?: Prisma.StringFieldUpdateOperationsInput | string
  cnpj?: Prisma.StringFieldUpdateOperationsInput | string
  email?: Prisma.NullableStringFieldUpdateOperationsInput | string | null
  telefone?: Prisma.NullableStringFieldUpdateOperationsInput | string | null
  taxaPadrao?: Prisma.FloatFieldUpdateOperationsInput | number
  cep?: Prisma.NullableStringFieldUpdateOperationsInput | string | null
  logradouro?: Prisma.NullableStringFieldUpdateOperationsInput | string | null
  numero?: Prisma.NullableStringFieldUpdateOperationsInput | string | null
  complemento?: Prisma.NullableStringFieldUpdateOperationsInput | string | null
  bairro?: Prisma.NullableStringFieldUpdateOperationsInput | string | null
  cidade?: Prisma.NullableStringFieldUpdateOperationsInput | string | null
  uf?: Prisma.NullableStringFieldUpdateOperationsInput | string | null
  fusoHorario?: Prisma.StringFieldUpdateOperationsInput | string
  ativo?: Prisma.BoolFieldUpdateOperationsInput | boolean
  createdAt?: Prisma.DateTimeFieldUpdateOperationsInput | Date | string
  updatedAt?: Prisma.DateTimeFieldUpdateOperationsInput | Date | string
  deletedAt?: Prisma.NullableDateTimeFieldUpdateOperationsInput | Date | string | null
  locaisAtendimento?: Prisma.LocalAtendimentoUncheckedUpdateManyWithoutClienteNestedInput
  usuarios?: Prisma.UsuarioClienteUncheckedUpdateManyWithoutClienteNestedInput
}


/**
 * Count Type ClienteCountOutputType
 */

export type ClienteCountOutputType = {
  locaisAtendimento: number
  plantoes: number
  usuarios: number
}

export type ClienteCountOutputTypeSelect<ExtArgs extends runtime.Types.Extensions.InternalArgs = runtime.Types.Extensions.DefaultArgs> = {
  locaisAtendimento?: boolean | ClienteCountOutputTypeCountLocaisAtendimentoArgs
  plantoes?: boolean | ClienteCountOutputTypeCountPlantoesArgs
  usuarios?: boolean | ClienteCountOutputTypeCountUsuariosArgs
}

/**
 * ClienteCountOutputType without action
 */
export type ClienteCountOutputTypeDefaultArgs<ExtArgs extends runtime.Types.Extensions.InternalArgs = runtime.Types.Extensions.DefaultArgs> = {
  /**
   * Select specific fields to fetch from the ClienteCountOutputType
   */
  select?: Prisma.ClienteCountOutputTypeSelect<ExtArgs> | null
}

/**
 * ClienteCountOutputType without action
 */
export type ClienteCountOutputTypeCountLocaisAtendimentoArgs<ExtArgs extends runtime.Types.Extensions.InternalArgs = runtime.Types.Extensions.DefaultArgs> = {
  where?: Prisma.LocalAtendimentoWhereInput
}

/**
 * ClienteCountOutputType without action
 */
export type ClienteCountOutputTypeCountPlantoesArgs<ExtArgs extends runtime.Types.Extensions.InternalArgs = runtime.Types.Extensions.DefaultArgs> = {
  where?: Prisma.PlantaoWhereInput
}

/**
 * ClienteCountOutputType without action
 */
export type ClienteCountOutputTypeCountUsuariosArgs<ExtArgs extends runtime.Types.Extensions.InternalArgs = runtime.Types.Extensions.DefaultArgs> = {
  where?: Prisma.UsuarioClienteWhereInput
}


export type ClienteSelect<ExtArgs extends runtime.Types.Extensions.InternalArgs = runtime.Types.Extensions.DefaultArgs> = runtime.Types.Extensions.GetSelect<{
  id?: boolean
  uuid?: boolean
  nome?: boolean
  cnpj?: boolean
  email?: boolean
  telefone?: boolean
  taxaPadrao?: boolean
  cep?: boolean
  logradouro?: boolean
  numero?: boolean
  complemento?: boolean
  bairro?: boolean
  cidade?: boolean
  uf?: boolean
  fusoHorario?: boolean
  ativo?: boolean
  createdAt?: boolean
  updatedAt?: boolean
  deletedAt?: boolean
  locaisAtendimento?: boolean | Prisma.Cliente$locaisAtendimentoArgs<ExtArgs>
  plantoes?: boolean | Prisma.Cliente$plantoesArgs<ExtArgs>
  usuarios?: boolean | Prisma.Cliente$usuariosArgs<ExtArgs>
  _count?: boolean | Prisma.ClienteCountOutputTypeDefaultArgs<ExtArgs>
}, ExtArgs["result"]["cliente"]>



export type ClienteSelectScalar = {
  id?: boolean
  uuid?: boolean
  nome?: boolean
  cnpj?: boolean
  email?: boolean
  telefone?: boolean
  taxaPadrao?: boolean
  cep?: boolean
  logradouro?: boolean
  numero?: boolean
  complemento?: boolean
  bairro?: boolean
  cidade?: boolean
  uf?: boolean
  fusoHorario?: boolean
  ativo?: boolean
  createdAt?: boolean
  updatedAt?: boolean
  deletedAt?: boolean
}

export type ClienteOmit<ExtArgs extends runtime.Types.Extensions.InternalArgs = runtime.Types.Extensions.DefaultArgs> = runtime.Types.Extensions.GetOmit<"id" | "uuid" | "nome" | "cnpj" | "email" | "telefone" | "taxaPadrao" | "cep" | "logradouro" | "numero" | "complemento" | "bairro" | "cidade" | "uf" | "fusoHorario" | "ativo" | "createdAt" | "updatedAt" | "deletedAt", ExtArgs["result"]["cliente"]>
export type ClienteInclude<ExtArgs extends runtime.Types.Extensions.InternalArgs = runtime.Types.Extensions.DefaultArgs> = {
  locaisAtendimento?: boolean | Prisma.Cliente$locaisAtendimentoArgs<ExtArgs>
  plantoes?: boolean | Prisma.Cliente$plantoesArgs<ExtArgs>
  usuarios?: boolean | Prisma.Cliente$usuariosArgs<ExtArgs>
  _count?: boolean | Prisma.ClienteCountOutputTypeDefaultArgs<ExtArgs>
}

export type $ClientePayload<ExtArgs extends runtime.Types.Extensions.InternalArgs = runtime.Types.Extensions.DefaultArgs> = {
  name: "Cliente"
  objects: {
    locaisAtendimento: Prisma.$LocalAtendimentoPayload<ExtArgs>[]
    plantoes: Prisma.$PlantaoPayload<ExtArgs>[]
    usuarios: Prisma.$UsuarioClientePayload<ExtArgs>[]
  }
  scalars: runtime.Types.Extensions.GetPayloadResult<{
    id: number
    uuid: string
    nome: string
    cnpj: string
    email: string | null
    telefone: string | null
    taxaPadrao: number
    cep: string | null
    logradouro: string | null
    numero: string | null
    complemento: string | null
    bairro: string | null
    cidade: string | null
    uf: string | null
    fusoHorario: string
    ativo: boolean
    createdAt: Date
    updatedAt: Date
    deletedAt: Date | null
  }, ExtArgs["result"]["cliente"]>
  composites: {}
}

export type ClienteGetPayload<S extends boolean | null | undefined | ClienteDefaultArgs> = runtime.Types.Result.GetResult<Prisma.$ClientePayload, S>

export type ClienteCountArgs<ExtArgs extends runtime.Types.Extensions.InternalArgs = runtime.Types.Extensions.DefaultArgs> =
  Omit<ClienteFindManyArgs, 'select' | 'include' | 'distinct' | 'omit'> & {
    select?: ClienteCountAggregateInputType | true
  }

export interface ClienteDelegate<ExtArgs extends runtime.Types.Extensions.InternalArgs = runtime.Types.Extensions.DefaultArgs, GlobalOmitOptions = {}> {
  [K: symbol]: { types: Prisma.TypeMap<ExtArgs>['model']['Cliente'], meta: { name: 'Cliente' } }
  /**
   * Find zero or one Cliente that matches the filter.
   * @param {ClienteFindUniqueArgs} args - Arguments to find a Cliente
   * @example
   * // Get one Cliente
   * const cliente = await prisma.cliente.findUnique({
   *   where: {
   *     // ... provide filter here
   *   }
   * })
   */
  findUnique<T extends ClienteFindUniqueArgs>(args: Prisma.SelectSubset<T, ClienteFindUniqueArgs<ExtArgs>>): Prisma.Prisma__ClienteClient<runtime.Types.Result.GetResult<Prisma.$ClientePayload<ExtArgs>, T, "findUnique", GlobalOmitOptions> | null, null, ExtArgs, GlobalOmitOptions>

  /**
   * Find one Cliente that matches the filter or throw an error with `error.code='P2025'`
   * if no matches were found.
   * @param {ClienteFindUniqueOrThrowArgs} args - Arguments to find a Cliente
   * @example
   * // Get one Cliente
   * const cliente = await prisma.cliente.findUniqueOrThrow({
   *   where: {
   *     // ... provide filter here
   *   }
   * })
   */
  findUniqueOrThrow<T extends ClienteFindUniqueOrThrowArgs>(args: Prisma.SelectSubset<T, ClienteFindUniqueOrThrowArgs<ExtArgs>>): Prisma.Prisma__ClienteClient<runtime.Types.Result.GetResult<Prisma.$ClientePayload<ExtArgs>, T, "findUniqueOrThrow", GlobalOmitOptions>, never, ExtArgs, GlobalOmitOptions>

  /**
   * Find the first Cliente that matches the filter.
   * Note, that providing `undefined` is treated as the value not being there.
   * Read more here: https://pris.ly/d/null-undefined
   * @param {ClienteFindFirstArgs} args - Arguments to find a Cliente
   * @example
   * // Get one Cliente
   * const cliente = await prisma.cliente.findFirst({
   *   where: {
   *     // ... provide filter here
   *   }
   * })
   */
  findFirst<T extends ClienteFindFirstArgs>(args?: Prisma.SelectSubset<T, ClienteFindFirstArgs<ExtArgs>>): Prisma.Prisma__ClienteClient<runtime.Types.Result.GetResult<Prisma.$ClientePayload<ExtArgs>, T, "findFirst", GlobalOmitOptions> | null, null, ExtArgs, GlobalOmitOptions>

  /**
   * Find the first Cliente that matches the filter or
   * throw `PrismaKnownClientError` with `P2025` code if no matches were found.
   * Note, that providing `undefined` is treated as the value not being there.
   * Read more here: https://pris.ly/d/null-undefined
   * @param {ClienteFindFirstOrThrowArgs} args - Arguments to find a Cliente
   * @example
   * // Get one Cliente
   * const cliente = await prisma.cliente.findFirstOrThrow({
   *   where: {
   *     // ... provide filter here
   *   }
   * })
   */
  findFirstOrThrow<T extends ClienteFindFirstOrThrowArgs>(args?: Prisma.SelectSubset<T, ClienteFindFirstOrThrowArgs<ExtArgs>>): Prisma.Prisma__ClienteClient<runtime.Types.Result.GetResult<Prisma.$ClientePayload<ExtArgs>, T, "findFirstOrThrow", GlobalOmitOptions>, never, ExtArgs, GlobalOmitOptions>

  /**
   * Find zero or more Clientes that matches the filter.
   * Note, that providing `undefined` is treated as the value not being there.
   * Read more here: https://pris.ly/d/null-undefined
   * @param {ClienteFindManyArgs} args - Arguments to filter and select certain fields only.
   * @example
   * // Get all Clientes
   * const clientes = await prisma.cliente.findMany()
   * 
   * // Get first 10 Clientes
   * const clientes = await prisma.cliente.findMany({ take: 10 })
   * 
   * // Only select the `id`
   * const clienteWithIdOnly = await prisma.cliente.findMany({ select: { id: true } })
   * 
   */
  findMany<T extends ClienteFindManyArgs>(args?: Prisma.SelectSubset<T, ClienteFindManyArgs<ExtArgs>>): Prisma.PrismaPromise<runtime.Types.Result.GetResult<Prisma.$ClientePayload<ExtArgs>, T, "findMany", GlobalOmitOptions>>

  /**
   * Create a Cliente.
   * @param {ClienteCreateArgs} args - Arguments to create a Cliente.
   * @example
   * // Create one Cliente
   * const Cliente = await prisma.cliente.create({
   *   data: {
   *     // ... data to create a Cliente
   *   }
   * })
   * 
   */
  create<T extends ClienteCreateArgs>(args: Prisma.SelectSubset<T, ClienteCreateArgs<ExtArgs>>): Prisma.Prisma__ClienteClient<runtime.Types.Result.GetResult<Prisma.$ClientePayload<ExtArgs>, T, "create", GlobalOmitOptions>, never, ExtArgs, GlobalOmitOptions>

  /**
   * Create many Clientes.
   * @param {ClienteCreateManyArgs} args - Arguments to create many Clientes.
   * @example
   * // Create many Clientes
   * const cliente = await prisma.cliente.createMany({
   *   data: [
   *     // ... provide data here
   *   ]
   * })
   *     
   */
  createMany<T extends ClienteCreateManyArgs>(args?: Prisma.SelectSubset<T, ClienteCreateManyArgs<ExtArgs>>): Prisma.PrismaPromise<Prisma.BatchPayload>

  /**
   * Delete a Cliente.
   * @param {ClienteDeleteArgs} args - Arguments to delete one Cliente.
   * @example
   * // Delete one Cliente
   * const Cliente = await prisma.cliente.delete({
   *   where: {
   *     // ... filter to delete one Cliente
   *   }
   * })
   * 
   */
  delete<T extends ClienteDeleteArgs>(args: Prisma.SelectSubset<T, ClienteDeleteArgs<ExtArgs>>): Prisma.Prisma__ClienteClient<runtime.Types.Result.GetResult<Prisma.$ClientePayload<ExtArgs>, T, "delete", GlobalOmitOptions>, never, ExtArgs, GlobalOmitOptions>

  /**
   * Update one Cliente.
   * @param {ClienteUpdateArgs} args - Arguments to update one Cliente.
   * @example
   * // Update one Cliente
   * const cliente = await prisma.cliente.update({
   *   where: {
   *     // ... provide filter here
   *   },
   *   data: {
   *     // ... provide data here
   *   }
   * })
   * 
   */
  update<T extends ClienteUpdateArgs>(args: Prisma.SelectSubset<T, ClienteUpdateArgs<ExtArgs>>): Prisma.Prisma__ClienteClient<runtime.Types.Result.GetResult<Prisma.$ClientePayload<ExtArgs>, T, "update", GlobalOmitOptions>, never, ExtArgs, GlobalOmitOptions>

  /**
   * Delete zero or more Clientes.
   * @param {ClienteDeleteManyArgs} args - Arguments to filter Clientes to delete.
   * @example
   * // Delete a few Clientes
   * const { count } = await prisma.cliente.deleteMany({
   *   where: {
   *     // ... provide filter here
   *   }
   * })
   * 
   */
  deleteMany<T extends ClienteDeleteManyArgs>(args?: Prisma.SelectSubset<T, ClienteDeleteManyArgs<ExtArgs>>): Prisma.PrismaPromise<Prisma.BatchPayload>

  /**
   * Update zero or more Clientes.
   * Note, that providing `undefined` is treated as the value not being there.
   * Read more here: https://pris.ly/d/null-undefined
   * @param {ClienteUpdateManyArgs} args - Arguments to update one or more rows.
   * @example
   * // Update many Clientes
   * const cliente = await prisma.cliente.updateMany({
   *   where: {
   *     // ... provide filter here
   *   },
   *   data: {
   *     // ... provide data here
   *   }
   * })
   * 
   */
  updateMany<T extends ClienteUpdateManyArgs>(args: Prisma.SelectSubset<T, ClienteUpdateManyArgs<ExtArgs>>): Prisma.PrismaPromise<Prisma.BatchPayload>

  /**
   * Create or update one Cliente.
   * @param {ClienteUpsertArgs} args - Arguments to update or create a Cliente.
   * @example
   * // Update or create a Cliente
   * const cliente = await prisma.cliente.upsert({
   *   create: {
   *     // ... data to create a Cliente
   *   },
   *   update: {
   *     // ... in case it already exists, update
   *   },
   *   where: {
   *     // ... the filter for the Cliente we want to update
   *   }
   * })
   */
  upsert<T extends ClienteUpsertArgs>(args: Prisma.SelectSubset<T, ClienteUpsertArgs<ExtArgs>>): Prisma.Prisma__ClienteClient<runtime.Types.Result.GetResult<Prisma.$ClientePayload<ExtArgs>, T, "upsert", GlobalOmitOptions>, never, ExtArgs, GlobalOmitOptions>


  /**
   * Count the number of Clientes.
   * Note, that providing `undefined` is treated as the value not being there.
   * Read more here: https://pris.ly/d/null-undefined
   * @param {ClienteCountArgs} args - Arguments to filter Clientes to count.
   * @example
   * // Count the number of Clientes
   * const count = await prisma.cliente.count({
   *   where: {
   *     // ... the filter for the Clientes we want to count
   *   }
   * })
  **/
  count<T extends ClienteCountArgs>(
    args?: Prisma.Subset<T, ClienteCountArgs>,
  ): Prisma.PrismaPromise<
    T extends runtime.Types.Utils.Record<'select', any>
      ? T['select'] extends true
        ? number
        : Prisma.GetScalarType<T['select'], ClienteCountAggregateOutputType>
      : number
  >

  /**
   * Allows you to perform aggregations operations on a Cliente.
   * Note, that providing `undefined` is treated as the value not being there.
   * Read more here: https://pris.ly/d/null-undefined
   * @param {ClienteAggregateArgs} args - Select which aggregations you would like to apply and on what fields.
   * @example
   * // Ordered by age ascending
   * // Where email contains prisma.io
   * // Limited to the 10 users
   * const aggregations = await prisma.user.aggregate({
   *   _avg: {
   *     age: true,
   *   },
   *   where: {
   *     email: {
   *       contains: "prisma.io",
   *     },
   *   },
   *   orderBy: {
   *     age: "asc",
   *   },
   *   take: 10,
   * })
  **/
  aggregate<T extends ClienteAggregateArgs>(args: Prisma.Subset<T, ClienteAggregateArgs>): Prisma.PrismaPromise<GetClienteAggregateType<T>>

  /**
   * Group by Cliente.
   * Note, that providing `undefined` is treated as the value not being there.
   * Read more here: https://pris.ly/d/null-undefined
   * @param {ClienteGroupByArgs} args - Group by arguments.
   * @example
   * // Group by city, order by createdAt, get count
   * const result = await prisma.user.groupBy({
   *   by: ['city', 'createdAt'],
   *   orderBy: {
   *     createdAt: true
   *   },
   *   _count: {
   *     _all: true
   *   },
   * })
   * 
  **/
  groupBy<
    T extends ClienteGroupByArgs,
    HasSelectOrTake extends Prisma.Or<
      Prisma.Extends<'skip', Prisma.Keys<T>>,
      Prisma.Extends<'take', Prisma.Keys<T>>
    >,
    OrderByArg extends Prisma.True extends HasSelectOrTake
      ? { orderBy: ClienteGroupByArgs['orderBy'] }
      : { orderBy?: ClienteGroupByArgs['orderBy'] },
    OrderFields extends Prisma.ExcludeUnderscoreKeys<Prisma.Keys<Prisma.MaybeTupleToUnion<T['orderBy']>>>,
    ByFields extends Prisma.MaybeTupleToUnion<T['by']>,
    ByValid extends Prisma.Has<ByFields, OrderFields>,
    HavingFields extends Prisma.GetHavingFields<T['having']>,
    HavingValid extends Prisma.Has<ByFields, HavingFields>,
    ByEmpty extends T['by'] extends never[] ? Prisma.True : Prisma.False,
    InputErrors extends ByEmpty extends Prisma.True
    ? `Error: "by" must not be empty.`
    : HavingValid extends Prisma.False
    ? {
        [P in HavingFields]: P extends ByFields
          ? never
          : P extends string
          ? `Error: Field "${P}" used in "having" needs to be provided in "by".`
          : [
              Error,
              'Field ',
              P,
              ` in "having" needs to be provided in "by"`,
            ]
      }[HavingFields]
    : 'take' extends Prisma.Keys<T>
    ? 'orderBy' extends Prisma.Keys<T>
      ? ByValid extends Prisma.True
        ? {}
        : {
            [P in OrderFields]: P extends ByFields
              ? never
              : `Error: Field "${P}" in "orderBy" needs to be provided in "by"`
          }[OrderFields]
      : 'Error: If you provide "take", you also need to provide "orderBy"'
    : 'skip' extends Prisma.Keys<T>
    ? 'orderBy' extends Prisma.Keys<T>
      ? ByValid extends Prisma.True
        ? {}
        : {
            [P in OrderFields]: P extends ByFields
              ? never
              : `Error: Field "${P}" in "orderBy" needs to be provided in "by"`
          }[OrderFields]
      : 'Error: If you provide "skip", you also need to provide "orderBy"'
    : ByValid extends Prisma.True
    ? {}
    : {
        [P in OrderFields]: P extends ByFields
          ? never
          : `Error: Field "${P}" in "orderBy" needs to be provided in "by"`
      }[OrderFields]
  >(args: Prisma.SubsetIntersection<T, ClienteGroupByArgs, OrderByArg> & InputErrors): {} extends InputErrors ? GetClienteGroupByPayload<T> : Prisma.PrismaPromise<InputErrors>
/**
 * Fields of the Cliente model
 */
readonly fields: ClienteFieldRefs;
}

/**
 * The delegate class that acts as a "Promise-like" for Cliente.
 * Why is this prefixed with `Prisma__`?
 * Because we want to prevent naming conflicts as mentioned in
 * https://github.com/prisma/prisma-client-js/issues/707
 */
export interface Prisma__ClienteClient<T, Null = never, ExtArgs extends runtime.Types.Extensions.InternalArgs = runtime.Types.Extensions.DefaultArgs, GlobalOmitOptions = {}> extends Prisma.PrismaPromise<T> {
  readonly [Symbol.toStringTag]: "PrismaPromise"
  locaisAtendimento<T extends Prisma.Cliente$locaisAtendimentoArgs<ExtArgs> = {}>(args?: Prisma.Subset<T, Prisma.Cliente$locaisAtendimentoArgs<ExtArgs>>): Prisma.PrismaPromise<runtime.Types.Result.GetResult<Prisma.$LocalAtendimentoPayload<ExtArgs>, T, "findMany", GlobalOmitOptions> | Null>
  plantoes<T extends Prisma.Cliente$plantoesArgs<ExtArgs> = {}>(args?: Prisma.Subset<T, Prisma.Cliente$plantoesArgs<ExtArgs>>): Prisma.PrismaPromise<runtime.Types.Result.GetResult<Prisma.$PlantaoPayload<ExtArgs>, T, "findMany", GlobalOmitOptions> | Null>
  usuarios<T extends Prisma.Cliente$usuariosArgs<ExtArgs> = {}>(args?: Prisma.Subset<T, Prisma.Cliente$usuariosArgs<ExtArgs>>): Prisma.PrismaPromise<runtime.Types.Result.GetResult<Prisma.$UsuarioClientePayload<ExtArgs>, T, "findMany", GlobalOmitOptions> | Null>
  /**
   * Attaches callbacks for the resolution and/or rejection of the Promise.
   * @param onfulfilled The callback to execute when the Promise is resolved.
   * @param onrejected The callback to execute when the Promise is rejected.
   * @returns A Promise for the completion of which ever callback is executed.
   */
  then<TResult1 = T, TResult2 = never>(onfulfilled?: ((value: T) => TResult1 | PromiseLike<TResult1>) | undefined | null, onrejected?: ((reason: any) => TResult2 | PromiseLike<TResult2>) | undefined | null): runtime.Types.Utils.JsPromise<TResult1 | TResult2>
  /**
   * Attaches a callback for only the rejection of the Promise.
   * @param onrejected The callback to execute when the Promise is rejected.
   * @returns A Promise for the completion of the callback.
   */
  catch<TResult = never>(onrejected?: ((reason: any) => TResult | PromiseLike<TResult>) | undefined | null): runtime.Types.Utils.JsPromise<T | TResult>
  /**
   * Attaches a callback that is invoked when the Promise is settled (fulfilled or rejected). The
   * resolved value cannot be modified from the callback.
   * @param onfinally The callback to execute when the Promise is settled (fulfilled or rejected).
   * @returns A Promise for the completion of the callback.
   */
  finally(onfinally?: (() => void) | undefined | null): runtime.Types.Utils.JsPromise<T>
}




/**
 * Fields of the Cliente model
 */
export interface ClienteFieldRefs {
  readonly id: Prisma.FieldRef<"Cliente", 'Int'>
  readonly uuid: Prisma.FieldRef<"Cliente", 'String'>
  readonly nome: Prisma.FieldRef<"Cliente", 'String'>
  readonly cnpj: Prisma.FieldRef<"Cliente", 'String'>
  readonly email: Prisma.FieldRef<"Cliente", 'String'>
  readonly telefone: Prisma.FieldRef<"Cliente", 'String'>
  readonly taxaPadrao: Prisma.FieldRef<"Cliente", 'Float'>
  readonly cep: Prisma.FieldRef<"Cliente", 'String'>
  readonly logradouro: Prisma.FieldRef<"Cliente", 'String'>
  readonly numero: Prisma.FieldRef<"Cliente", 'String'>
  readonly complemento: Prisma.FieldRef<"Cliente", 'String'>
  readonly bairro: Prisma.FieldRef<"Cliente", 'String'>
  readonly cidade: Prisma.FieldRef<"Cliente", 'String'>
  readonly uf: Prisma.FieldRef<"Cliente", 'String'>
  readonly fusoHorario: Prisma.FieldRef<"Cliente", 'String'>
  readonly ativo: Prisma.FieldRef<"Cliente", 'Boolean'>
  readonly createdAt: Prisma.FieldRef<"Cliente", 'DateTime'>
  readonly updatedAt: Prisma.FieldRef<"Cliente", 'DateTime'>
  readonly deletedAt: Prisma.FieldRef<"Cliente", 'DateTime'>
}
    

// Custom InputTypes
/**
 * Cliente findUnique
 */
export type ClienteFindUniqueArgs<ExtArgs extends runtime.Types.Extensions.InternalArgs = runtime.Types.Extensions.DefaultArgs> = {
  /**
   * Select specific fields to fetch from the Cliente
   */
  select?: Prisma.ClienteSelect<ExtArgs> | null
  /**
   * Omit specific fields from the Cliente
   */
  omit?: Prisma.ClienteOmit<ExtArgs> | null
  /**
   * Choose, which related nodes to fetch as well
   */
  include?: Prisma.ClienteInclude<ExtArgs> | null
  /**
   * Filter, which Cliente to fetch.
   */
  where: Prisma.ClienteWhereUniqueInput
}

/**
 * Cliente findUniqueOrThrow
 */
export type ClienteFindUniqueOrThrowArgs<ExtArgs extends runtime.Types.Extensions.InternalArgs = runtime.Types.Extensions.DefaultArgs> = {
  /**
   * Select specific fields to fetch from the Cliente
   */
  select?: Prisma.ClienteSelect<ExtArgs> | null
  /**
   * Omit specific fields from the Cliente
   */
  omit?: Prisma.ClienteOmit<ExtArgs> | null
  /**
   * Choose, which related nodes to fetch as well
   */
  include?: Prisma.ClienteInclude<ExtArgs> | null
  /**
   * Filter, which Cliente to fetch.
   */
  where: Prisma.ClienteWhereUniqueInput
}

/**
 * Cliente findFirst
 */
export type ClienteFindFirstArgs<ExtArgs extends runtime.Types.Extensions.InternalArgs = runtime.Types.Extensions.DefaultArgs> = {
  /**
   * Select specific fields to fetch from the Cliente
   */
  select?: Prisma.ClienteSelect<ExtArgs> | null
  /**
   * Omit specific fields from the Cliente
   */
  omit?: Prisma.ClienteOmit<ExtArgs> | null
  /**
   * Choose, which related nodes to fetch as well
   */
  include?: Prisma.ClienteInclude<ExtArgs> | null
  /**
   * Filter, which Cliente to fetch.
   */
  where?: Prisma.ClienteWhereInput
  /**
   * {@link https://www.prisma.io/docs/concepts/components/prisma-client/sorting Sorting Docs}
   * 
   * Determine the order of Clientes to fetch.
   */
  orderBy?: Prisma.ClienteOrderByWithRelationInput | Prisma.ClienteOrderByWithRelationInput[]
  /**
   * {@link https://www.prisma.io/docs/concepts/components/prisma-client/pagination#cursor-based-pagination Cursor Docs}
   * 
   * Sets the position for searching for Clientes.
   */
  cursor?: Prisma.ClienteWhereUniqueInput
  /**
   * {@link https://www.prisma.io/docs/concepts/components/prisma-client/pagination Pagination Docs}
   * 
   * Take `±n` Clientes from the position of the cursor.
   */
  take?: number
  /**
   * {@link https://www.prisma.io/docs/concepts/components/prisma-client/pagination Pagination Docs}
   * 
   * Skip the first `n` Clientes.
   */
  skip?: number
  /**
   * {@link https://www.prisma.io/docs/concepts/components/prisma-client/distinct Distinct Docs}
   * 
   * Filter by unique combinations of Clientes.
   */
  distinct?: Prisma.ClienteScalarFieldEnum | Prisma.ClienteScalarFieldEnum[]
}

/**
 * Cliente findFirstOrThrow
 */
export type ClienteFindFirstOrThrowArgs<ExtArgs extends runtime.Types.Extensions.InternalArgs = runtime.Types.Extensions.DefaultArgs> = {
  /**
   * Select specific fields to fetch from the Cliente
   */
  select?: Prisma.ClienteSelect<ExtArgs> | null
  /**
   * Omit specific fields from the Cliente
   */
  omit?: Prisma.ClienteOmit<ExtArgs> | null
  /**
   * Choose, which related nodes to fetch as well
   */
  include?: Prisma.ClienteInclude<ExtArgs> | null
  /**
   * Filter, which Cliente to fetch.
   */
  where?: Prisma.ClienteWhereInput
  /**
   * {@link https://www.prisma.io/docs/concepts/components/prisma-client/sorting Sorting Docs}
   * 
   * Determine the order of Clientes to fetch.
   */
  orderBy?: Prisma.ClienteOrderByWithRelationInput | Prisma.ClienteOrderByWithRelationInput[]
  /**
   * {@link https://www.prisma.io/docs/concepts/components/prisma-client/pagination#cursor-based-pagination Cursor Docs}
   * 
   * Sets the position for searching for Clientes.
   */
  cursor?: Prisma.ClienteWhereUniqueInput
  /**
   * {@link https://www.prisma.io/docs/concepts/components/prisma-client/pagination Pagination Docs}
   * 
   * Take `±n` Clientes from the position of the cursor.
   */
  take?: number
  /**
   * {@link https://www.prisma.io/docs/concepts/components/prisma-client/pagination Pagination Docs}
   * 
   * Skip the first `n` Clientes.
   */
  skip?: number
  /**
   * {@link https://www.prisma.io/docs/concepts/components/prisma-client/distinct Distinct Docs}
   * 
   * Filter by unique combinations of Clientes.
   */
  distinct?: Prisma.ClienteScalarFieldEnum | Prisma.ClienteScalarFieldEnum[]
}

/**
 * Cliente findMany
 */
export type ClienteFindManyArgs<ExtArgs extends runtime.Types.Extensions.InternalArgs = runtime.Types.Extensions.DefaultArgs> = {
  /**
   * Select specific fields to fetch from the Cliente
   */
  select?: Prisma.ClienteSelect<ExtArgs> | null
  /**
   * Omit specific fields from the Cliente
   */
  omit?: Prisma.ClienteOmit<ExtArgs> | null
  /**
   * Choose, which related nodes to fetch as well
   */
  include?: Prisma.ClienteInclude<ExtArgs> | null
  /**
   * Filter, which Clientes to fetch.
   */
  where?: Prisma.ClienteWhereInput
  /**
   * {@link https://www.prisma.io/docs/concepts/components/prisma-client/sorting Sorting Docs}
   * 
   * Determine the order of Clientes to fetch.
   */
  orderBy?: Prisma.ClienteOrderByWithRelationInput | Prisma.ClienteOrderByWithRelationInput[]
  /**
   * {@link https://www.prisma.io/docs/concepts/components/prisma-client/pagination#cursor-based-pagination Cursor Docs}
   * 
   * Sets the position for listing Clientes.
   */
  cursor?: Prisma.ClienteWhereUniqueInput
  /**
   * {@link https://www.prisma.io/docs/concepts/components/prisma-client/pagination Pagination Docs}
   * 
   * Take `±n` Clientes from the position of the cursor.
   */
  take?: number
  /**
   * {@link https://www.prisma.io/docs/concepts/components/prisma-client/pagination Pagination Docs}
   * 
   * Skip the first `n` Clientes.
   */
  skip?: number
  distinct?: Prisma.ClienteScalarFieldEnum | Prisma.ClienteScalarFieldEnum[]
}

/**
 * Cliente create
 */
export type ClienteCreateArgs<ExtArgs extends runtime.Types.Extensions.InternalArgs = runtime.Types.Extensions.DefaultArgs> = {
  /**
   * Select specific fields to fetch from the Cliente
   */
  select?: Prisma.ClienteSelect<ExtArgs> | null
  /**
   * Omit specific fields from the Cliente
   */
  omit?: Prisma.ClienteOmit<ExtArgs> | null
  /**
   * Choose, which related nodes to fetch as well
   */
  include?: Prisma.ClienteInclude<ExtArgs> | null
  /**
   * The data needed to create a Cliente.
   */
  data: Prisma.XOR<Prisma.ClienteCreateInput, Prisma.ClienteUncheckedCreateInput>
}

/**
 * Cliente createMany
 */
export type ClienteCreateManyArgs<ExtArgs extends runtime.Types.Extensions.InternalArgs = runtime.Types.Extensions.DefaultArgs> = {
  /**
   * The data used to create many Clientes.
   */
  data: Prisma.ClienteCreateManyInput | Prisma.ClienteCreateManyInput[]
  skipDuplicates?: boolean
}

/**
 * Cliente update
 */
export type ClienteUpdateArgs<ExtArgs extends runtime.Types.Extensions.InternalArgs = runtime.Types.Extensions.DefaultArgs> = {
  /**
   * Select specific fields to fetch from the Cliente
   */
  select?: Prisma.ClienteSelect<ExtArgs> | null
  /**
   * Omit specific fields from the Cliente
   */
  omit?: Prisma.ClienteOmit<ExtArgs> | null
  /**
   * Choose, which related nodes to fetch as well
   */
  include?: Prisma.ClienteInclude<ExtArgs> | null
  /**
   * The data needed to update a Cliente.
   */
  data: Prisma.XOR<Prisma.ClienteUpdateInput, Prisma.ClienteUncheckedUpdateInput>
  /**
   * Choose, which Cliente to update.
   */
  where: Prisma.ClienteWhereUniqueInput
}

/**
 * Cliente updateMany
 */
export type ClienteUpdateManyArgs<ExtArgs extends runtime.Types.Extensions.InternalArgs = runtime.Types.Extensions.DefaultArgs> = {
  /**
   * The data used to update Clientes.
   */
  data: Prisma.XOR<Prisma.ClienteUpdateManyMutationInput, Prisma.ClienteUncheckedUpdateManyInput>
  /**
   * Filter which Clientes to update
   */
  where?: Prisma.ClienteWhereInput
  /**
   * Limit how many Clientes to update.
   */
  limit?: number
}

/**
 * Cliente upsert
 */
export type ClienteUpsertArgs<ExtArgs extends runtime.Types.Extensions.InternalArgs = runtime.Types.Extensions.DefaultArgs> = {
  /**
   * Select specific fields to fetch from the Cliente
   */
  select?: Prisma.ClienteSelect<ExtArgs> | null
  /**
   * Omit specific fields from the Cliente
   */
  omit?: Prisma.ClienteOmit<ExtArgs> | null
  /**
   * Choose, which related nodes to fetch as well
   */
  include?: Prisma.ClienteInclude<ExtArgs> | null
  /**
   * The filter to search for the Cliente to update in case it exists.
   */
  where: Prisma.ClienteWhereUniqueInput
  /**
   * In case the Cliente found by the `where` argument doesn't exist, create a new Cliente with this data.
   */
  create: Prisma.XOR<Prisma.ClienteCreateInput, Prisma.ClienteUncheckedCreateInput>
  /**
   * In case the Cliente was found with the provided `where` argument, update it with this data.
   */
  update: Prisma.XOR<Prisma.ClienteUpdateInput, Prisma.ClienteUncheckedUpdateInput>
}

/**
 * Cliente delete
 */
export type ClienteDeleteArgs<ExtArgs extends runtime.Types.Extensions.InternalArgs = runtime.Types.Extensions.DefaultArgs> = {
  /**
   * Select specific fields to fetch from the Cliente
   */
  select?: Prisma.ClienteSelect<ExtArgs> | null
  /**
   * Omit specific fields from the Cliente
   */
  omit?: Prisma.ClienteOmit<ExtArgs> | null
  /**
   * Choose, which related nodes to fetch as well
   */
  include?: Prisma.ClienteInclude<ExtArgs> | null
  /**
   * Filter which Cliente to delete.
   */
  where: Prisma.ClienteWhereUniqueInput
}

/**
 * Cliente deleteMany
 */
export type ClienteDeleteManyArgs<ExtArgs extends runtime.Types.Extensions.InternalArgs = runtime.Types.Extensions.DefaultArgs> = {
  /**
   * Filter which Clientes to delete
   */
  where?: Prisma.ClienteWhereInput
  /**
   * Limit how many Clientes to delete.
   */
  limit?: number
}

/**
 * Cliente.locaisAtendimento
 */
export type Cliente$locaisAtendimentoArgs<ExtArgs extends runtime.Types.Extensions.InternalArgs = runtime.Types.Extensions.DefaultArgs> = {
  /**
   * Select specific fields to fetch from the LocalAtendimento
   */
  select?: Prisma.LocalAtendimentoSelect<ExtArgs> | null
  /**
   * Omit specific fields from the LocalAtendimento
   */
  omit?: Prisma.LocalAtendimentoOmit<ExtArgs> | null
  /**
   * Choose, which related nodes to fetch as well
   */
  include?: Prisma.LocalAtendimentoInclude<ExtArgs> | null
  where?: Prisma.LocalAtendimentoWhereInput
  orderBy?: Prisma.LocalAtendimentoOrderByWithRelationInput | Prisma.LocalAtendimentoOrderByWithRelationInput[]
  cursor?: Prisma.LocalAtendimentoWhereUniqueInput
  take?: number
  skip?: number
  distinct?: Prisma.LocalAtendimentoScalarFieldEnum | Prisma.LocalAtendimentoScalarFieldEnum[]
}

/**
 * Cliente.plantoes
 */
export type Cliente$plantoesArgs<ExtArgs extends runtime.Types.Extensions.InternalArgs = runtime.Types.Extensions.DefaultArgs> = {
  /**
   * Select specific fields to fetch from the Plantao
   */
  select?: Prisma.PlantaoSelect<ExtArgs> | null
  /**
   * Omit specific fields from the Plantao
   */
  omit?: Prisma.PlantaoOmit<ExtArgs> | null
  /**
   * Choose, which related nodes to fetch as well
   */
  include?: Prisma.PlantaoInclude<ExtArgs> | null
  where?: Prisma.PlantaoWhereInput
  orderBy?: Prisma.PlantaoOrderByWithRelationInput | Prisma.PlantaoOrderByWithRelationInput[]
  cursor?: Prisma.PlantaoWhereUniqueInput
  take?: number
  skip?: number
  distinct?: Prisma.PlantaoScalarFieldEnum | Prisma.PlantaoScalarFieldEnum[]
}

/**
 * Cliente.usuarios
 */
export type Cliente$usuariosArgs<ExtArgs extends runtime.Types.Extensions.InternalArgs = runtime.Types.Extensions.DefaultArgs> = {
  /**
   * Select specific fields to fetch from the UsuarioCliente
   */
  select?: Prisma.UsuarioClienteSelect<ExtArgs> | null
  /**
   * Omit specific fields from the UsuarioCliente
   */
  omit?: Prisma.UsuarioClienteOmit<ExtArgs> | null
  /**
   * Choose, which related nodes to fetch as well
   */
  include?: Prisma.UsuarioClienteInclude<ExtArgs> | null
  where?: Prisma.UsuarioClienteWhereInput
  orderBy?: Prisma.UsuarioClienteOrderByWithRelationInput | Prisma.UsuarioClienteOrderByWithRelationInput[]
  cursor?: Prisma.UsuarioClienteWhereUniqueInput
  take?: number
  skip?: number
  distinct?: Prisma.UsuarioClienteScalarFieldEnum | Prisma.UsuarioClienteScalarFieldEnum[]
}

/**
 * Cliente without action
 */
export type ClienteDefaultArgs<ExtArgs extends runtime.Types.Extensions.InternalArgs = runtime.Types.Extensions.DefaultArgs> = {
  /**
   * Select specific fields to fetch from the Cliente
   */
  select?: Prisma.ClienteSelect<ExtArgs> | null
  /**
   * Omit specific fields from the Cliente
   */
  omit?: Prisma.ClienteOmit<ExtArgs> | null
  /**
   * Choose, which related nodes to fetch as well
   */
  include?: Prisma.ClienteInclude<ExtArgs> | null
}
