import { useEffect, useState } from "react";
import { useNavigate, useParams } from "@tanstack/react-router";
import { useQuery, useMutation, useQueryClient } from "@tanstack/react-query";
import { useForm } from "@tanstack/react-form";
import { z } from "zod";
import { Save, ArrowLeft, Loader2, Edit2 } from "lucide-react";
import { Link } from "@tanstack/react-router";
import { Button } from "@/components/ui/button";
import { Input } from "@/components/ui/input";
import { Label } from "@/components/ui/label";
import { Switch } from "@/components/ui/switch";
import { Select, SelectContent, SelectItem, SelectTrigger, SelectValue } from "@/components/ui/select";
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from "@/components/ui/card";
import { Tabs, TabsContent, Ta<PERSON>List, TabsTrigger } from "@/components/ui/tabs";
import { MultiSelect } from "@/components/ui/multi-select";
import { EspecialidadeSelect } from "@/components/especialidade-select";
import { toast } from "sonner";
import { api, type Profissional } from "@/lib/api";
import { formatCEP } from "@/lib/formatters";
import { CpfInput } from "@/components/ui/cpf-input";
import { PhoneInput } from "@/components/ui/phone-input";
import { buscarCEP } from "@/lib/address-service";
import { useClientes } from "@/hooks/use-clientes";
import { ESTADOS_BRASILEIROS, CONSELHOS_CLASSE, BANCOS } from "@/lib/constants";
import { formatDateForInput as formatDateForInputUtil } from "@shared/date";
import { formatCNPJ } from "@shared/utils";

const profissionalSchema = z.object({
  nome: z.string().min(1, "Nome é obrigatório"),
  cpf: z
    .string()
    .min(1, "CPF é obrigatório")
    .refine((val) => {
      const numbers = val.replace(/\D/g, "");
      return numbers.length === 11;
    }, "CPF deve ter 11 dígitos"),
  email: z.string().min(1, "Email é obrigatório").email("Email inválido"),
  telefone: z.string().optional(),

  // Dados pessoais
  dataNascimento: z.string().optional(),
  genero: z.string().optional(),
  estadoCivil: z.string().optional(),
  nacionalidade: z.string().optional(),

  // Endereço
  cep: z.string().optional(),
  logradouro: z.string().optional(),
  numero: z.string().optional(),
  complemento: z.string().optional(),
  bairro: z.string().optional(),
  cidade: z.string().optional(),
  uf: z.string().optional(),

  // Dados profissionais
  rg: z.string().optional(),
  orgaoEmissor: z.string().optional(),
  cnes: z.string().optional(),
  conselhoClasse: z.string().optional(),
  numeroRegistro: z.string().optional(),
  ufConselho: z.string().optional(),

  // Dados bancários
  banco: z.string().optional(),
  agencia: z.string().optional(),
  digitoAgencia: z.string().optional(),
  conta: z.string().optional(),
  digitoConta: z.string().optional(),
  tipoConta: z.enum(["corrente", "poupanca"]).optional(),
  chavePix: z.string().optional(),
  tipoPix: z.string().optional(),
  tipoAssinatura: z.string().optional(),

  especialidadeIds: z.array(z.number()).optional(),
  clienteIds: z.array(z.number()).optional(),
  ativo: z.boolean(),
  onboardingPendente: z.boolean(),
});

type ProfissionalFormData = z.infer<typeof profissionalSchema>;

interface ProfissionalFormProps {
  viewMode?: boolean;
}

export function ProfissionalForm({ viewMode = false }: ProfissionalFormProps) {
  const navigate = useNavigate();
  const queryClient = useQueryClient();
  const [isViewMode, setIsViewMode] = useState(viewMode);

  // Tenta pegar o uuid de qualquer rota que tenha $uuid
  let uuid: string | undefined;

  // Primeiro tenta pegar da rota de visualização
  try {
    const params = useParams({ from: "/cadastros/profissionais/$uuid" });
    uuid = params.uuid;
  } catch {
    // Se não conseguir, não tem UUID (modo criação)
    uuid = undefined;
  }

  const isEditing = !!uuid;
  const [activeTab, setActiveTab] = useState("pessoal");
  const [loadingCep, setLoadingCep] = useState(false);

  const { data: clientesData } = useClientes();
  const clientes = clientesData?.data || [];

  const handleCepSearch = async (cep: string) => {
    const cleanCep = cep.replace(/\D/g, "");
    if (cleanCep.length !== 8) return;

    setLoadingCep(true);
    try {
      const address = await buscarCEP(cleanCep);
      if (address) {
        form.setFieldValue("logradouro", address.logradouro);
        form.setFieldValue("bairro", address.bairro);
        form.setFieldValue("cidade", address.localidade);
        form.setFieldValue("uf", address.uf);
        toast.success("Endereço encontrado!");
      } else {
        toast.error("CEP não encontrado");
      }
    } catch (error) {
      toast.error("Erro ao buscar CEP");
    } finally {
      setLoadingCep(false);
    }
  };

  // Buscar profissional se estiver editando
  const {
    data: profissional,
    isLoading,
    error,
  } = useQuery({
    queryKey: ["profissional", uuid],
    queryFn: async () => {
      if (!uuid) return null;
      console.log("Buscando profissional com UUID:", uuid);
      const result = await api.get<Profissional>(`/profissionais/${uuid}`);
      console.log("Profissional encontrado:", result);
      return result;
    },
    enabled: isEditing && !!uuid,
  });

  // Mutation para criar/atualizar
  const saveMutation = useMutation({
    mutationFn: async (data: ProfissionalFormData) => {
      if (isEditing) {
        return await api.put(`/profissionais/${uuid}`, data);
      } else {
        return await api.post("/profissionais", data);
      }
    },
    onSuccess: async () => {
      await queryClient.invalidateQueries({ queryKey: ["profissionais"] });
      if (isEditing && uuid) {
        await queryClient.invalidateQueries({ queryKey: ["profissional", uuid] });
      }
      toast.success(`Profissional ${isEditing ? "atualizado" : "cadastrado"} com sucesso!`);
      navigate({ to: "/cadastros/profissionais" });
    },
    onError: (error: any) => {
      toast.error(error.message || `Erro ao ${isEditing ? "atualizar" : "cadastrar"} profissional`);
    },
  });

  // Formulário
  const form = useForm({
    defaultValues: {
      nome: "",
      cpf: "",
      email: "",
      telefone: "",

      // Dados pessoais
      dataNascimento: "",
      genero: "",
      estadoCivil: "",
      nacionalidade: "Brasileira",

      // Endereço
      cep: "",
      logradouro: "",
      numero: "",
      complemento: "",
      bairro: "",
      cidade: "",
      uf: "",

      // Dados profissionais
      rg: "",
      orgaoEmissor: "",
      cnes: "",
      conselhoClasse: "",
      numeroRegistro: "",
      ufConselho: "",

      // Dados bancários
      banco: "",
      agencia: "",
      digitoAgencia: "",
      conta: "",
      digitoConta: "",
      tipoConta: "corrente" as "corrente" | "poupanca",
      chavePix: "",
      tipoPix: "",
      tipoAssinatura: "Digital",

      especialidadeIds: [] as number[],
      clienteIds: [] as number[],
      ativo: true,
      onboardingPendente: false,
    } as ProfissionalFormData,
    onSubmit: async ({ value }) => {
      saveMutation.mutate(value);
    },
  });

  // Preencher formulário quando carregar profissional
  useEffect(() => {
    if (profissional) {
      form.setFieldValue("nome", profissional.usuario?.nome || "");
      form.setFieldValue("cpf", profissional.usuario?.cpf?.replace(/\D/g, "") || "");
      form.setFieldValue("email", profissional.usuario?.email || "");
      form.setFieldValue("telefone", profissional.usuario?.telefone?.replace(/\D/g, "") || "");

      // Dados pessoais - Corrigindo formatação da data
      form.setFieldValue(
        "dataNascimento",
        profissional.usuario?.dataNascimento ? formatDateForInputUtil(profissional.usuario.dataNascimento) : ""
      );
      form.setFieldValue("genero", profissional.usuario?.genero || "");
      form.setFieldValue("estadoCivil", profissional.usuario?.estadoCivil || "");
      form.setFieldValue("nacionalidade", profissional.usuario?.nacionalidade || "Brasileira");

      // Endereço
      form.setFieldValue("cep", profissional.usuario?.cep?.replace(/\D/g, "") || "");
      form.setFieldValue("logradouro", profissional.usuario?.logradouro || "");
      form.setFieldValue("numero", profissional.usuario?.numero || "");
      form.setFieldValue("complemento", profissional.usuario?.complemento || "");
      form.setFieldValue("bairro", profissional.usuario?.bairro || "");
      form.setFieldValue("cidade", profissional.usuario?.cidade || "");
      form.setFieldValue("uf", profissional.usuario?.uf || "");

      // Dados profissionais
      form.setFieldValue("rg", profissional.rg || "");
      form.setFieldValue("orgaoEmissor", profissional.orgaoEmissor || "");
      form.setFieldValue("cnes", profissional.cnes || "");
      form.setFieldValue("conselhoClasse", profissional.conselhoClasse || "");
      form.setFieldValue("numeroRegistro", profissional.numeroRegistro || "");
      form.setFieldValue("ufConselho", profissional.ufConselho || "");

      // Dados bancários
      form.setFieldValue("banco", profissional.banco || "");
      form.setFieldValue("agencia", profissional.agencia || "");
      form.setFieldValue("digitoAgencia", profissional.digitoAgencia || "");
      form.setFieldValue("conta", profissional.conta || "");
      form.setFieldValue("digitoConta", profissional.digitoConta || "");
      form.setFieldValue("tipoConta", (profissional.tipoConta || "corrente") as "corrente" | "poupanca");
      form.setFieldValue("chavePix", profissional.chavePix || "");
      form.setFieldValue("tipoPix", profissional.tipoPix || "");
      form.setFieldValue("tipoAssinatura", profissional.tipoAssinatura || "Digital");

      // Relacionamentos
      form.setFieldValue(
        "especialidadeIds",
        profissional.especialidades?.map((e) => e.especialidadeId).filter((id): id is number => id !== undefined) || []
      );
      form.setFieldValue(
        "clienteIds",
        profissional.usuario?.clientes?.map((c) => c.cliente?.id).filter((id): id is number => id !== undefined) || []
      );
      form.setFieldValue("ativo", profissional.usuario?.ativo !== false);
      form.setFieldValue("onboardingPendente", (profissional.usuario?.metaData as any)?.onboardingPendente || false);
    }
  }, [profissional, form]);

  if (isLoading) {
    return (
      <div className="flex items-center justify-center min-h-screen">
        <Loader2 className="h-8 w-8 animate-spin" />
      </div>
    );
  }

  if (error) {
    return (
      <div className="flex items-center justify-center min-h-screen">
        <div className="text-center">
          <p className="text-red-500 mb-4">Erro ao carregar profissional</p>
          <p className="text-sm text-muted-foreground mb-4">
            {(error as any)?.message || "Profissional não encontrado"}
          </p>
          <Button onClick={() => navigate({ to: "/cadastros/profissionais" })}>Voltar para lista</Button>
        </div>
      </div>
    );
  }

  if (isEditing && !profissional && !isLoading) {
    return (
      <div className="flex items-center justify-center min-h-screen">
        <div className="text-center">
          <p className="text-yellow-500 mb-4">Profissional não encontrado</p>
          <Button onClick={() => navigate({ to: "/cadastros/profissionais" })}>Voltar para lista</Button>
        </div>
      </div>
    );
  }

  return (
    <div className="space-y-6">
      <div className="flex items-center justify-between">
        <div className="flex items-center gap-4">
          <Link to="/cadastros/profissionais">
            <Button variant="ghost" size="icon">
              <ArrowLeft className="h-4 w-4" />
            </Button>
          </Link>
          <div>
            <h1 className="text-3xl font-bold">
              {!isEditing
                ? "Novo Profissional"
                : isViewMode
                  ? profissional?.usuario?.nome || "Carregando..."
                  : "Editar Profissional"}
            </h1>
            <p className="text-muted-foreground">
              {!isEditing
                ? "Cadastre um novo profissional de saúde"
                : isViewMode
                  ? "Visualizando dados do profissional"
                  : "Atualize os dados do profissional"}
            </p>
          </div>
        </div>
        {isEditing && (
          <div className="flex gap-2">
            {isViewMode ? (
              <Button onClick={() => setIsViewMode(false)}>
                <Edit2 className="h-4 w-4 mr-2" />
                Editar
              </Button>
            ) : (
              <>
                <Button variant="outline" onClick={() => setIsViewMode(true)}>
                  Cancelar
                </Button>
                <form.Subscribe selector={(state) => [state.canSubmit, state.isSubmitting]}>
                  {([canSubmit, isSubmitting]) => (
                    <Button type="button" onClick={() => form.handleSubmit()} disabled={!canSubmit || isSubmitting}>
                      {isSubmitting ? (
                        <>
                          <Loader2 className="mr-2 h-4 w-4 animate-spin" />
                          Salvando...
                        </>
                      ) : (
                        <>
                          <Save className="mr-2 h-4 w-4" />
                          Salvar
                        </>
                      )}
                    </Button>
                  )}
                </form.Subscribe>
              </>
            )}
          </div>
        )}
      </div>

      <form
        onSubmit={(e) => {
          e.preventDefault();
          e.stopPropagation();
          form.handleSubmit();
        }}
      >
        <Tabs value={activeTab} onValueChange={setActiveTab} className="space-y-6">
          <TabsList className="grid w-full grid-cols-5 max-w-3xl">
            <TabsTrigger value="pessoal">Dados Pessoais</TabsTrigger>
            <TabsTrigger value="endereco">Endereço</TabsTrigger>
            <TabsTrigger value="profissional">Dados Profissionais</TabsTrigger>
            <TabsTrigger value="bancario">Dados Bancários</TabsTrigger>
            <TabsTrigger value="vinculos">Clientes</TabsTrigger>
          </TabsList>

          <TabsContent value="pessoal">
            <Card>
              <CardHeader>
                <CardTitle>DADOS PESSOAIS</CardTitle>
                <CardDescription>Informações pessoais e de contato do profissional</CardDescription>
              </CardHeader>
              <CardContent className="space-y-6">
                <form.Field name="onboardingPendente">
                  {(field) => (
                    <div className="bg-blue-50 p-4 rounded-lg mb-4">
                      <div className="flex items-start space-x-3">
                        <input
                          type="checkbox"
                          id="onboardingPendente"
                          checked={field.state.value}
                          onChange={(e) => field.handleChange(e.target.checked)}
                          className="mt-1 h-4 w-4 text-blue-600 focus:ring-blue-500 border-gray-300 rounded"
                        />
                        <label htmlFor="onboardingPendente" className="text-sm text-blue-800 cursor-pointer">
                          <span className="font-semibold">Onboarding Pendente</span>
                          <br />
                          Ao marcar esta opção, o profissional ao fazer login verá apenas os termos de uso para aceitar
                          e depois uma mensagem informando que será notificado em breve. Use esta opção quando o
                          profissional ainda não está totalmente configurado no sistema.
                        </label>
                      </div>
                    </div>
                  )}
                </form.Field>

                <div className="grid gap-4 md:grid-cols-2">
                  <form.Field
                    name="nome"
                    validators={{
                      onChange: ({ value }) => (!value ? "Nome é obrigatório" : undefined),
                    }}
                  >
                    {(field) => (
                      <div className="space-y-2">
                        <Label htmlFor={field.name}>Nome *</Label>
                        <Input
                          id={field.name}
                          value={field.state.value}
                          onBlur={field.handleBlur}
                          onChange={(e) => field.handleChange(e.target.value)}
                          placeholder="Nome completo do profissional"
                          disabled={isViewMode}
                        />
                        {field.state.meta.errors && (
                          <p className="text-sm text-destructive">{field.state.meta.errors.join(", ")}</p>
                        )}
                      </div>
                    )}
                  </form.Field>

                  <form.Field
                    name="cpf"
                    validators={{
                      onChange: ({ value }) => {
                        if (!value) return "CPF é obrigatório";
                        const numbers = value.replace(/\D/g, "");
                        if (numbers.length !== 11) {
                          return "CPF deve ter 11 dígitos";
                        }
                        return undefined;
                      },
                    }}
                  >
                    {(field) => (
                      <CpfInput
                        id={field.name}
                        label="CPF"
                        required
                        value={field.state.value || ""}
                        onBlur={field.handleBlur}
                        onChange={field.handleChange}
                        error={field.state.meta.errors?.join(", ")}
                        disabled={isViewMode}
                      />
                    )}
                  </form.Field>

                  <form.Field name="dataNascimento">
                    {(field) => (
                      <div className="space-y-2">
                        <Label htmlFor={field.name}>Data de Nascimento</Label>
                        <Input
                          id={field.name}
                          type="date"
                          value={field.state.value || ""}
                          onBlur={field.handleBlur}
                          onChange={(e) => field.handleChange(e.target.value)}
                        />
                      </div>
                    )}
                  </form.Field>

                  <form.Field name="telefone">
                    {(field) => (
                      <PhoneInput
                        id={field.name}
                        label="Telefone"
                        value={field.state.value || ""}
                        onBlur={field.handleBlur}
                        onChange={field.handleChange}
                      />
                    )}
                  </form.Field>

                  <form.Field
                    name="email"
                    validators={{
                      onChange: ({ value }) => {
                        if (!value) return "Email é obrigatório";
                        if (!/^\S+@\S+\.\S+$/.test(value)) {
                          return "Email inválido";
                        }
                        return undefined;
                      },
                    }}
                  >
                    {(field) => (
                      <div className="space-y-2">
                        <Label htmlFor={field.name}>E-mail *</Label>
                        <Input
                          id={field.name}
                          type="email"
                          value={field.state.value}
                          onBlur={field.handleBlur}
                          onChange={(e) => field.handleChange(e.target.value)}
                          placeholder="<EMAIL>"
                        />
                        {field.state.meta.errors && (
                          <p className="text-sm text-destructive">{field.state.meta.errors.join(", ")}</p>
                        )}
                      </div>
                    )}
                  </form.Field>

                  <form.Field name="estadoCivil">
                    {(field) => (
                      <div className="space-y-2">
                        <Label htmlFor={field.name}>Estado Civil</Label>
                        <Select
                          value={field.state.value || ""}
                          onValueChange={field.handleChange}
                          disabled={isViewMode}
                        >
                          <SelectTrigger>
                            <SelectValue placeholder="Selecione o estado civil" />
                          </SelectTrigger>
                          <SelectContent>
                            <SelectItem value="Solteiro">Solteiro(a)</SelectItem>
                            <SelectItem value="Casado">Casado(a)</SelectItem>
                            <SelectItem value="Divorciado">Divorciado(a)</SelectItem>
                            <SelectItem value="Viúvo">Viúvo(a)</SelectItem>
                            <SelectItem value="União Estável">União Estável</SelectItem>
                          </SelectContent>
                        </Select>
                      </div>
                    )}
                  </form.Field>

                  <form.Field name="genero">
                    {(field) => (
                      <div className="space-y-2">
                        <Label htmlFor={field.name}>Gênero</Label>
                        <Select value={field.state.value || ""} onValueChange={field.handleChange}>
                          <SelectTrigger>
                            <SelectValue placeholder="Selecione o gênero" />
                          </SelectTrigger>
                          <SelectContent>
                            <SelectItem value="Masculino">Masculino</SelectItem>
                            <SelectItem value="Feminino">Feminino</SelectItem>
                            <SelectItem value="Outro">Outro</SelectItem>
                          </SelectContent>
                        </Select>
                      </div>
                    )}
                  </form.Field>

                  <form.Field name="nacionalidade">
                    {(field) => (
                      <div className="space-y-2">
                        <Label htmlFor={field.name}>Nacionalidade</Label>
                        <Input
                          id={field.name}
                          value={field.state.value || ""}
                          onBlur={field.handleBlur}
                          onChange={(e) => field.handleChange(e.target.value)}
                          placeholder="Brasileira"
                        />
                      </div>
                    )}
                  </form.Field>
                </div>

                {isEditing && (
                  <div className="flex items-center space-x-3 pt-4 border-t">
                    <form.Field name="ativo">
                      {(field) => (
                        <>
                          <Switch id="ativo" checked={field.state.value} onCheckedChange={field.handleChange} />
                          <Label htmlFor="ativo" className="cursor-pointer">
                            Profissional ativo
                          </Label>
                        </>
                      )}
                    </form.Field>
                  </div>
                )}

                {!isViewMode && (
                  <div className="flex justify-end gap-4 mt-6">
                    <Link to="/cadastros/profissionais">
                      <Button type="button" variant="outline">
                        Cancelar
                      </Button>
                    </Link>
                    <Button type="button" onClick={() => setActiveTab("endereco")}>
                      Próximo
                    </Button>
                  </div>
                )}
              </CardContent>
            </Card>
          </TabsContent>

          <TabsContent value="endereco">
            <Card>
              <CardHeader>
                <CardTitle>ENDEREÇO</CardTitle>
                <CardDescription>Endereço residencial do profissional</CardDescription>
              </CardHeader>
              <CardContent className="space-y-6">
                <div className="grid gap-4 md:grid-cols-2">
                  <form.Field name="cep">
                    {(field) => (
                      <div className="space-y-2">
                        <Label htmlFor={field.name}>CEP</Label>
                        <div className="relative">
                          <Input
                            id={field.name}
                            value={formatCEP(field.state.value || "")}
                            onBlur={(e) => {
                              field.handleBlur();
                              handleCepSearch(e.target.value);
                            }}
                            onChange={(e) => field.handleChange(e.target.value.replace(/\D/g, ""))}
                            placeholder="00000-000"
                            disabled={loadingCep}
                          />
                          {loadingCep && (
                            <Loader2 className="absolute right-2 top-2.5 h-4 w-4 animate-spin text-muted-foreground" />
                          )}
                        </div>
                      </div>
                    )}
                  </form.Field>

                  <form.Field name="uf">
                    {(field) => (
                      <div className="space-y-2">
                        <Label htmlFor={field.name}>UF</Label>
                        <Select value={field.state.value || ""} onValueChange={field.handleChange}>
                          <SelectTrigger>
                            <SelectValue placeholder="Selecione o estado" />
                          </SelectTrigger>
                          <SelectContent>
                            {ESTADOS_BRASILEIROS.map((estado) => (
                              <SelectItem key={estado.value} value={estado.value}>
                                {estado.label}
                              </SelectItem>
                            ))}
                          </SelectContent>
                        </Select>
                      </div>
                    )}
                  </form.Field>

                  <form.Field name="cidade">
                    {(field) => (
                      <div className="space-y-2">
                        <Label htmlFor={field.name}>Cidade</Label>
                        <Input
                          id={field.name}
                          value={field.state.value || ""}
                          onBlur={field.handleBlur}
                          onChange={(e) => field.handleChange(e.target.value)}
                          placeholder="Nome da cidade"
                        />
                      </div>
                    )}
                  </form.Field>

                  <form.Field name="bairro">
                    {(field) => (
                      <div className="space-y-2">
                        <Label htmlFor={field.name}>Bairro</Label>
                        <Input
                          id={field.name}
                          value={field.state.value || ""}
                          onBlur={field.handleBlur}
                          onChange={(e) => field.handleChange(e.target.value)}
                          placeholder="Nome do bairro"
                        />
                      </div>
                    )}
                  </form.Field>

                  <form.Field name="logradouro">
                    {(field) => (
                      <div className="space-y-2 md:col-span-1">
                        <Label htmlFor={field.name}>Logradouro</Label>
                        <Input
                          id={field.name}
                          value={field.state.value || ""}
                          onBlur={field.handleBlur}
                          onChange={(e) => field.handleChange(e.target.value)}
                          placeholder="Rua, Avenida, etc."
                        />
                      </div>
                    )}
                  </form.Field>

                  <div className="grid grid-cols-2 gap-4">
                    <form.Field name="numero">
                      {(field) => (
                        <div className="space-y-2">
                          <Label htmlFor={field.name}>Número</Label>
                          <Input
                            id={field.name}
                            value={field.state.value || ""}
                            onBlur={field.handleBlur}
                            onChange={(e) => field.handleChange(e.target.value)}
                            placeholder="123"
                          />
                        </div>
                      )}
                    </form.Field>

                    <form.Field name="complemento">
                      {(field) => (
                        <div className="space-y-2">
                          <Label htmlFor={field.name}>Complemento</Label>
                          <Input
                            id={field.name}
                            value={field.state.value || ""}
                            onBlur={field.handleBlur}
                            onChange={(e) => field.handleChange(e.target.value)}
                            placeholder="Apto, Sala, etc."
                          />
                        </div>
                      )}
                    </form.Field>
                  </div>
                </div>

                {!isViewMode && (
                  <div className="flex justify-between gap-4 mt-6">
                    <Button type="button" variant="outline" onClick={() => setActiveTab("pessoal")}>
                      Anterior
                    </Button>
                    <div className="flex gap-4">
                      <Link to="/cadastros/profissionais">
                        <Button type="button" variant="outline">
                          Cancelar
                        </Button>
                      </Link>
                      <Button type="button" onClick={() => setActiveTab("profissional")}>
                        Próximo
                      </Button>
                    </div>
                  </div>
                )}
              </CardContent>
            </Card>
          </TabsContent>

          <TabsContent value="profissional">
            <Card>
              <CardHeader>
                <CardTitle>DADOS PROFISSIONAIS</CardTitle>
                <CardDescription>Informações profissionais e documentação</CardDescription>
              </CardHeader>
              <CardContent className="space-y-6">
                <div className="grid gap-4 md:grid-cols-2">
                  <form.Field name="rg">
                    {(field) => (
                      <div className="space-y-2">
                        <Label htmlFor={field.name}>RG</Label>
                        <Input
                          id={field.name}
                          value={field.state.value || ""}
                          onBlur={field.handleBlur}
                          onChange={(e) => field.handleChange(e.target.value)}
                          placeholder="Número do RG"
                        />
                      </div>
                    )}
                  </form.Field>

                  <form.Field name="orgaoEmissor">
                    {(field) => (
                      <div className="space-y-2">
                        <Label htmlFor={field.name}>Órgão Emissor</Label>
                        <Input
                          id={field.name}
                          value={field.state.value || ""}
                          onBlur={field.handleBlur}
                          onChange={(e) => field.handleChange(e.target.value)}
                          placeholder="SSP/UF"
                        />
                      </div>
                    )}
                  </form.Field>

                  <form.Field name="cnes">
                    {(field) => (
                      <div className="space-y-2">
                        <Label htmlFor={field.name}>CNES</Label>
                        <Input
                          id={field.name}
                          value={field.state.value || ""}
                          onBlur={field.handleBlur}
                          onChange={(e) => field.handleChange(e.target.value)}
                          placeholder="Cadastro Nacional de Estabelecimentos de Saúde"
                        />
                      </div>
                    )}
                  </form.Field>

                  <form.Field name="especialidadeIds">
                    {(field) => (
                      <div className="space-y-2">
                        <Label htmlFor={field.name}>Especialidade</Label>
                        <EspecialidadeSelect
                          value={field.state.value || []}
                          onChange={(value) => field.handleChange(value)}
                        />
                      </div>
                    )}
                  </form.Field>

                  <form.Field name="conselhoClasse">
                    {(field) => (
                      <div className="space-y-2">
                        <Label htmlFor={field.name}>Conselho de Classe</Label>
                        <Select value={field.state.value || ""} onValueChange={field.handleChange}>
                          <SelectTrigger>
                            <SelectValue placeholder="Selecione o conselho" />
                          </SelectTrigger>
                          <SelectContent>
                            {CONSELHOS_CLASSE.map((conselho) => (
                              <SelectItem key={conselho.value} value={conselho.value}>
                                {conselho.label}
                              </SelectItem>
                            ))}
                          </SelectContent>
                        </Select>
                      </div>
                    )}
                  </form.Field>

                  <form.Field name="numeroRegistro">
                    {(field) => (
                      <div className="space-y-2">
                        <Label htmlFor={field.name}>Número de Registro de Classe</Label>
                        <Input
                          id={field.name}
                          value={field.state.value || ""}
                          onBlur={field.handleBlur}
                          onChange={(e) => field.handleChange(e.target.value)}
                          placeholder="Número do registro no conselho"
                        />
                      </div>
                    )}
                  </form.Field>

                  <form.Field name="ufConselho">
                    {(field) => (
                      <div className="space-y-2">
                        <Label htmlFor={field.name}>UF Conselho de Classe</Label>
                        <Select value={field.state.value || ""} onValueChange={field.handleChange}>
                          <SelectTrigger>
                            <SelectValue placeholder="Selecione o estado" />
                          </SelectTrigger>
                          <SelectContent>
                            {ESTADOS_BRASILEIROS.map((estado) => (
                              <SelectItem key={estado.value} value={estado.value}>
                                {estado.value}
                              </SelectItem>
                            ))}
                          </SelectContent>
                        </Select>
                      </div>
                    )}
                  </form.Field>
                </div>

                {!isViewMode && (
                  <div className="flex justify-between gap-4 mt-6">
                    <Button type="button" variant="outline" onClick={() => setActiveTab("endereco")}>
                      Anterior
                    </Button>
                    <div className="flex gap-4">
                      <Link to="/cadastros/profissionais">
                        <Button type="button" variant="outline">
                          Cancelar
                        </Button>
                      </Link>
                      <Button type="button" onClick={() => setActiveTab("bancario")}>
                        Próximo
                      </Button>
                    </div>
                  </div>
                )}
              </CardContent>
            </Card>
          </TabsContent>

          <TabsContent value="bancario">
            <Card>
              <CardHeader>
                <CardTitle>DADOS BANCÁRIOS</CardTitle>
                <CardDescription>Informações bancárias para pagamento</CardDescription>
              </CardHeader>
              <CardContent className="space-y-6">
                <div className="grid gap-4 md:grid-cols-2">
                  <form.Field name="banco">
                    {(field) => (
                      <div className="space-y-2">
                        <Label htmlFor={field.name}>Banco</Label>
                        <Select value={field.state.value || ""} onValueChange={field.handleChange}>
                          <SelectTrigger>
                            <SelectValue placeholder="Selecione o banco" />
                          </SelectTrigger>
                          <SelectContent>
                            {BANCOS.map((banco) => (
                              <SelectItem key={banco.value} value={banco.value}>
                                {banco.label}
                              </SelectItem>
                            ))}
                          </SelectContent>
                        </Select>
                      </div>
                    )}
                  </form.Field>

                  <div className="grid grid-cols-3 gap-2">
                    <form.Field name="agencia">
                      {(field) => (
                        <div className="space-y-2 col-span-2">
                          <Label htmlFor={field.name}>Agência</Label>
                          <Input
                            id={field.name}
                            value={field.state.value || ""}
                            onBlur={field.handleBlur}
                            onChange={(e) => field.handleChange(e.target.value)}
                            placeholder="0000"
                          />
                        </div>
                      )}
                    </form.Field>

                    <form.Field name="digitoAgencia">
                      {(field) => (
                        <div className="space-y-2">
                          <Label htmlFor={field.name}>Dígito</Label>
                          <Input
                            id={field.name}
                            value={field.state.value || ""}
                            onBlur={field.handleBlur}
                            onChange={(e) => field.handleChange(e.target.value)}
                            placeholder="0"
                            maxLength={1}
                          />
                        </div>
                      )}
                    </form.Field>
                  </div>

                  <div className="grid grid-cols-3 gap-2">
                    <form.Field name="conta">
                      {(field) => (
                        <div className="space-y-2 col-span-2">
                          <Label htmlFor={field.name}>Conta Corrente</Label>
                          <Input
                            id={field.name}
                            value={field.state.value || ""}
                            onBlur={field.handleBlur}
                            onChange={(e) => field.handleChange(e.target.value)}
                            placeholder="00000"
                          />
                        </div>
                      )}
                    </form.Field>

                    <form.Field name="digitoConta">
                      {(field) => (
                        <div className="space-y-2">
                          <Label htmlFor={field.name}>Dígito</Label>
                          <Input
                            id={field.name}
                            value={field.state.value || ""}
                            onBlur={field.handleBlur}
                            onChange={(e) => field.handleChange(e.target.value)}
                            placeholder="0"
                            maxLength={2}
                          />
                        </div>
                      )}
                    </form.Field>
                  </div>

                  <form.Field name="tipoConta">
                    {(field) => (
                      <div className="space-y-2">
                        <Label htmlFor={field.name}>Tipo de Conta</Label>
                        <Select
                          value={field.state.value || "corrente"}
                          onValueChange={(value) => field.handleChange(value as "corrente" | "poupanca")}
                        >
                          <SelectTrigger>
                            <SelectValue placeholder="Selecione o tipo" />
                          </SelectTrigger>
                          <SelectContent>
                            <SelectItem value="corrente">Conta Corrente</SelectItem>
                            <SelectItem value="poupanca">Conta Poupança</SelectItem>
                          </SelectContent>
                        </Select>
                      </div>
                    )}
                  </form.Field>

                  <form.Field name="tipoPix">
                    {(field) => (
                      <div className="space-y-2">
                        <Label htmlFor={field.name}>Tipo PIX</Label>
                        <Select value={field.state.value || ""} onValueChange={field.handleChange}>
                          <SelectTrigger>
                            <SelectValue placeholder="Selecione o tipo de chave" />
                          </SelectTrigger>
                          <SelectContent>
                            <SelectItem value="CPF">CPF</SelectItem>
                            <SelectItem value="CNPJ">CNPJ</SelectItem>
                            <SelectItem value="Email">E-mail</SelectItem>
                            <SelectItem value="Telefone">Telefone</SelectItem>
                            <SelectItem value="Aleatória">Chave Aleatória</SelectItem>
                          </SelectContent>
                        </Select>
                      </div>
                    )}
                  </form.Field>

                  <form.Field name="chavePix">
                    {(field) => (
                      <div className="space-y-2">
                        <Label htmlFor={field.name}>Chave PIX</Label>
                        <Input
                          id={field.name}
                          value={field.state.value || ""}
                          onBlur={field.handleBlur}
                          onChange={(e) => field.handleChange(e.target.value)}
                          placeholder="Digite a chave PIX"
                        />
                      </div>
                    )}
                  </form.Field>

                  <form.Field name="tipoAssinatura">
                    {(field) => (
                      <div className="space-y-2">
                        <Label htmlFor={field.name}>Tipo de Assinatura</Label>
                        <Select value={field.state.value || "Digital"} onValueChange={field.handleChange}>
                          <SelectTrigger>
                            <SelectValue placeholder="Selecione o tipo" />
                          </SelectTrigger>
                          <SelectContent>
                            <SelectItem value="Digital">Digital</SelectItem>
                            <SelectItem value="Física">Física</SelectItem>
                          </SelectContent>
                        </Select>
                      </div>
                    )}
                  </form.Field>
                </div>

                {!isViewMode && (
                  <div className="flex justify-between gap-4 mt-6">
                    <Button type="button" variant="outline" onClick={() => setActiveTab("profissional")}>
                      Anterior
                    </Button>
                    <div className="flex gap-4">
                      <Link to="/cadastros/profissionais">
                        <Button type="button" variant="outline">
                          Cancelar
                        </Button>
                      </Link>
                      <Button type="button" onClick={() => setActiveTab("vinculos")}>
                        Próximo
                      </Button>
                    </div>
                  </div>
                )}
              </CardContent>
            </Card>
          </TabsContent>

          <TabsContent value="vinculos">
            <Card>
              <CardHeader>
                <CardTitle>VÍNCULOS COM CLIENTES</CardTitle>
                <CardDescription>Selecione os clientes aos quais este profissional estará vinculado</CardDescription>
              </CardHeader>
              <CardContent className="space-y-6">
                <form.Field name="clienteIds">
                  {(field) => (
                    <div className="space-y-2">
                      <Label htmlFor={field.name}>Clientes Vinculados</Label>
                      <MultiSelect
                        options={clientes.map((c) => ({
                          value: c.id,
                          label: `${c.nome} (${formatCNPJ(c.cnpj)})`,
                        }))}
                        value={field.state.value || []}
                        onChange={(value) => field.handleChange(value as number[])}
                        placeholder="Selecione os clientes"
                        searchPlaceholder="Buscar cliente..."
                        emptyText="Nenhum cliente encontrado"
                      />
                      <p className="text-sm text-muted-foreground">
                        O profissional poderá trabalhar apenas nos plantões dos clientes selecionados.
                      </p>
                    </div>
                  )}
                </form.Field>

                {!isViewMode && (
                  <div className="flex justify-between gap-4 mt-6">
                    <Button type="button" variant="outline" onClick={() => setActiveTab("bancario")}>
                      Anterior
                    </Button>
                    <div className="flex gap-4">
                      <Link to="/cadastros/profissionais">
                        <Button type="button" variant="outline">
                          Cancelar
                        </Button>
                      </Link>
                      <form.Subscribe selector={(state) => [state.canSubmit, state.isSubmitting]}>
                        {([canSubmit, isSubmitting]) => (
                          <Button type="submit" disabled={!canSubmit || isSubmitting}>
                            {isSubmitting ? (
                              <>
                                <Loader2 className="mr-2 h-4 w-4 animate-spin" />
                                Salvando...
                              </>
                            ) : (
                              <>
                                <Save className="mr-2 h-4 w-4" />
                                {isEditing ? "Atualizar" : "Cadastrar"} Profissional
                              </>
                            )}
                          </Button>
                        )}
                      </form.Subscribe>
                    </div>
                  </div>
                )}
              </CardContent>
            </Card>
          </TabsContent>
        </Tabs>
      </form>
    </div>
  );
}
