import { UsuariosList } from "@/pages/usuarios/usuarios-list";
import { createFileRoute } from "@tanstack/react-router";
import { requireMasterRole } from "@/lib/route-guards";

function UsuariosIndex() {
  return <UsuariosList />;
}

export const Route = createFileRoute("/cadastros/usuarios/")({
  component: UsuariosIndex,
  beforeLoad: async () => {
    // Apenas usuários MASTER podem gerenciar outros usuários
    await requireMasterRole();
  },
});
