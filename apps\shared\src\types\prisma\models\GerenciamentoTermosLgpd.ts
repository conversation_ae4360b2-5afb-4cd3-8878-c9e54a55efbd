
/* !!! This is code generated by Prisma. Do not edit directly. !!! */
/* eslint-disable */
// @ts-nocheck 
/*
 * This file exports the `GerenciamentoTermosLgpd` model and its related types.
 *
 * 🟢 You can import this file directly.
 */
import * as runtime from "@prisma/client/runtime/library"
import type * as $Enums from "../enums.ts"
import type * as Prisma from "../internal/prismaNamespace.ts"

/**
 * Model GerenciamentoTermosLgpd
 * 
 */
export type GerenciamentoTermosLgpdModel = runtime.Types.Result.DefaultSelection<Prisma.$GerenciamentoTermosLgpdPayload>

export type AggregateGerenciamentoTermosLgpd = {
  _count: GerenciamentoTermosLgpdCountAggregateOutputType | null
  _avg: GerenciamentoTermosLgpdAvgAggregateOutputType | null
  _sum: GerenciamentoTermosLgpdSumAggregateOutputType | null
  _min: GerenciamentoTermosLgpdMinAggregateOutputType | null
  _max: GerenciamentoTermosLgpdMaxAggregateOutputType | null
}

export type GerenciamentoTermosLgpdAvgAggregateOutputType = {
  id: number | null
  criadoPor: number | null
}

export type GerenciamentoTermosLgpdSumAggregateOutputType = {
  id: number | null
  criadoPor: number | null
}

export type GerenciamentoTermosLgpdMinAggregateOutputType = {
  id: number | null
  versao: string | null
  titulo: string | null
  conteudo: string | null
  hashConteudo: string | null
  ativo: boolean | null
  criadoPor: number | null
  criadoEm: Date | null
  atualizadoEm: Date | null
  validoDe: Date | null
  validoAte: Date | null
  tipoDocumento: string | null
  deletedAt: Date | null
}

export type GerenciamentoTermosLgpdMaxAggregateOutputType = {
  id: number | null
  versao: string | null
  titulo: string | null
  conteudo: string | null
  hashConteudo: string | null
  ativo: boolean | null
  criadoPor: number | null
  criadoEm: Date | null
  atualizadoEm: Date | null
  validoDe: Date | null
  validoAte: Date | null
  tipoDocumento: string | null
  deletedAt: Date | null
}

export type GerenciamentoTermosLgpdCountAggregateOutputType = {
  id: number
  versao: number
  titulo: number
  conteudo: number
  hashConteudo: number
  ativo: number
  criadoPor: number
  criadoEm: number
  atualizadoEm: number
  validoDe: number
  validoAte: number
  tipoDocumento: number
  deletedAt: number
  _all: number
}


export type GerenciamentoTermosLgpdAvgAggregateInputType = {
  id?: true
  criadoPor?: true
}

export type GerenciamentoTermosLgpdSumAggregateInputType = {
  id?: true
  criadoPor?: true
}

export type GerenciamentoTermosLgpdMinAggregateInputType = {
  id?: true
  versao?: true
  titulo?: true
  conteudo?: true
  hashConteudo?: true
  ativo?: true
  criadoPor?: true
  criadoEm?: true
  atualizadoEm?: true
  validoDe?: true
  validoAte?: true
  tipoDocumento?: true
  deletedAt?: true
}

export type GerenciamentoTermosLgpdMaxAggregateInputType = {
  id?: true
  versao?: true
  titulo?: true
  conteudo?: true
  hashConteudo?: true
  ativo?: true
  criadoPor?: true
  criadoEm?: true
  atualizadoEm?: true
  validoDe?: true
  validoAte?: true
  tipoDocumento?: true
  deletedAt?: true
}

export type GerenciamentoTermosLgpdCountAggregateInputType = {
  id?: true
  versao?: true
  titulo?: true
  conteudo?: true
  hashConteudo?: true
  ativo?: true
  criadoPor?: true
  criadoEm?: true
  atualizadoEm?: true
  validoDe?: true
  validoAte?: true
  tipoDocumento?: true
  deletedAt?: true
  _all?: true
}

export type GerenciamentoTermosLgpdAggregateArgs<ExtArgs extends runtime.Types.Extensions.InternalArgs = runtime.Types.Extensions.DefaultArgs> = {
  /**
   * Filter which GerenciamentoTermosLgpd to aggregate.
   */
  where?: Prisma.GerenciamentoTermosLgpdWhereInput
  /**
   * {@link https://www.prisma.io/docs/concepts/components/prisma-client/sorting Sorting Docs}
   * 
   * Determine the order of GerenciamentoTermosLgpds to fetch.
   */
  orderBy?: Prisma.GerenciamentoTermosLgpdOrderByWithRelationInput | Prisma.GerenciamentoTermosLgpdOrderByWithRelationInput[]
  /**
   * {@link https://www.prisma.io/docs/concepts/components/prisma-client/pagination#cursor-based-pagination Cursor Docs}
   * 
   * Sets the start position
   */
  cursor?: Prisma.GerenciamentoTermosLgpdWhereUniqueInput
  /**
   * {@link https://www.prisma.io/docs/concepts/components/prisma-client/pagination Pagination Docs}
   * 
   * Take `±n` GerenciamentoTermosLgpds from the position of the cursor.
   */
  take?: number
  /**
   * {@link https://www.prisma.io/docs/concepts/components/prisma-client/pagination Pagination Docs}
   * 
   * Skip the first `n` GerenciamentoTermosLgpds.
   */
  skip?: number
  /**
   * {@link https://www.prisma.io/docs/concepts/components/prisma-client/aggregations Aggregation Docs}
   * 
   * Count returned GerenciamentoTermosLgpds
  **/
  _count?: true | GerenciamentoTermosLgpdCountAggregateInputType
  /**
   * {@link https://www.prisma.io/docs/concepts/components/prisma-client/aggregations Aggregation Docs}
   * 
   * Select which fields to average
  **/
  _avg?: GerenciamentoTermosLgpdAvgAggregateInputType
  /**
   * {@link https://www.prisma.io/docs/concepts/components/prisma-client/aggregations Aggregation Docs}
   * 
   * Select which fields to sum
  **/
  _sum?: GerenciamentoTermosLgpdSumAggregateInputType
  /**
   * {@link https://www.prisma.io/docs/concepts/components/prisma-client/aggregations Aggregation Docs}
   * 
   * Select which fields to find the minimum value
  **/
  _min?: GerenciamentoTermosLgpdMinAggregateInputType
  /**
   * {@link https://www.prisma.io/docs/concepts/components/prisma-client/aggregations Aggregation Docs}
   * 
   * Select which fields to find the maximum value
  **/
  _max?: GerenciamentoTermosLgpdMaxAggregateInputType
}

export type GetGerenciamentoTermosLgpdAggregateType<T extends GerenciamentoTermosLgpdAggregateArgs> = {
      [P in keyof T & keyof AggregateGerenciamentoTermosLgpd]: P extends '_count' | 'count'
    ? T[P] extends true
      ? number
      : Prisma.GetScalarType<T[P], AggregateGerenciamentoTermosLgpd[P]>
    : Prisma.GetScalarType<T[P], AggregateGerenciamentoTermosLgpd[P]>
}




export type GerenciamentoTermosLgpdGroupByArgs<ExtArgs extends runtime.Types.Extensions.InternalArgs = runtime.Types.Extensions.DefaultArgs> = {
  where?: Prisma.GerenciamentoTermosLgpdWhereInput
  orderBy?: Prisma.GerenciamentoTermosLgpdOrderByWithAggregationInput | Prisma.GerenciamentoTermosLgpdOrderByWithAggregationInput[]
  by: Prisma.GerenciamentoTermosLgpdScalarFieldEnum[] | Prisma.GerenciamentoTermosLgpdScalarFieldEnum
  having?: Prisma.GerenciamentoTermosLgpdScalarWhereWithAggregatesInput
  take?: number
  skip?: number
  _count?: GerenciamentoTermosLgpdCountAggregateInputType | true
  _avg?: GerenciamentoTermosLgpdAvgAggregateInputType
  _sum?: GerenciamentoTermosLgpdSumAggregateInputType
  _min?: GerenciamentoTermosLgpdMinAggregateInputType
  _max?: GerenciamentoTermosLgpdMaxAggregateInputType
}

export type GerenciamentoTermosLgpdGroupByOutputType = {
  id: number
  versao: string
  titulo: string
  conteudo: string
  hashConteudo: string
  ativo: boolean
  criadoPor: number | null
  criadoEm: Date
  atualizadoEm: Date
  validoDe: Date
  validoAte: Date | null
  tipoDocumento: string
  deletedAt: Date | null
  _count: GerenciamentoTermosLgpdCountAggregateOutputType | null
  _avg: GerenciamentoTermosLgpdAvgAggregateOutputType | null
  _sum: GerenciamentoTermosLgpdSumAggregateOutputType | null
  _min: GerenciamentoTermosLgpdMinAggregateOutputType | null
  _max: GerenciamentoTermosLgpdMaxAggregateOutputType | null
}

type GetGerenciamentoTermosLgpdGroupByPayload<T extends GerenciamentoTermosLgpdGroupByArgs> = Prisma.PrismaPromise<
  Array<
    Prisma.PickEnumerable<GerenciamentoTermosLgpdGroupByOutputType, T['by']> &
      {
        [P in ((keyof T) & (keyof GerenciamentoTermosLgpdGroupByOutputType))]: P extends '_count'
          ? T[P] extends boolean
            ? number
            : Prisma.GetScalarType<T[P], GerenciamentoTermosLgpdGroupByOutputType[P]>
          : Prisma.GetScalarType<T[P], GerenciamentoTermosLgpdGroupByOutputType[P]>
      }
    >
  >



export type GerenciamentoTermosLgpdWhereInput = {
  AND?: Prisma.GerenciamentoTermosLgpdWhereInput | Prisma.GerenciamentoTermosLgpdWhereInput[]
  OR?: Prisma.GerenciamentoTermosLgpdWhereInput[]
  NOT?: Prisma.GerenciamentoTermosLgpdWhereInput | Prisma.GerenciamentoTermosLgpdWhereInput[]
  id?: Prisma.IntFilter<"GerenciamentoTermosLgpd"> | number
  versao?: Prisma.StringFilter<"GerenciamentoTermosLgpd"> | string
  titulo?: Prisma.StringFilter<"GerenciamentoTermosLgpd"> | string
  conteudo?: Prisma.StringFilter<"GerenciamentoTermosLgpd"> | string
  hashConteudo?: Prisma.StringFilter<"GerenciamentoTermosLgpd"> | string
  ativo?: Prisma.BoolFilter<"GerenciamentoTermosLgpd"> | boolean
  criadoPor?: Prisma.IntNullableFilter<"GerenciamentoTermosLgpd"> | number | null
  criadoEm?: Prisma.DateTimeFilter<"GerenciamentoTermosLgpd"> | Date | string
  atualizadoEm?: Prisma.DateTimeFilter<"GerenciamentoTermosLgpd"> | Date | string
  validoDe?: Prisma.DateTimeFilter<"GerenciamentoTermosLgpd"> | Date | string
  validoAte?: Prisma.DateTimeNullableFilter<"GerenciamentoTermosLgpd"> | Date | string | null
  tipoDocumento?: Prisma.StringFilter<"GerenciamentoTermosLgpd"> | string
  deletedAt?: Prisma.DateTimeNullableFilter<"GerenciamentoTermosLgpd"> | Date | string | null
  aceiteTermosLgpd?: Prisma.AceiteTermosLgpdListRelationFilter
}

export type GerenciamentoTermosLgpdOrderByWithRelationInput = {
  id?: Prisma.SortOrder
  versao?: Prisma.SortOrder
  titulo?: Prisma.SortOrder
  conteudo?: Prisma.SortOrder
  hashConteudo?: Prisma.SortOrder
  ativo?: Prisma.SortOrder
  criadoPor?: Prisma.SortOrderInput | Prisma.SortOrder
  criadoEm?: Prisma.SortOrder
  atualizadoEm?: Prisma.SortOrder
  validoDe?: Prisma.SortOrder
  validoAte?: Prisma.SortOrderInput | Prisma.SortOrder
  tipoDocumento?: Prisma.SortOrder
  deletedAt?: Prisma.SortOrderInput | Prisma.SortOrder
  aceiteTermosLgpd?: Prisma.AceiteTermosLgpdOrderByRelationAggregateInput
  _relevance?: Prisma.GerenciamentoTermosLgpdOrderByRelevanceInput
}

export type GerenciamentoTermosLgpdWhereUniqueInput = Prisma.AtLeast<{
  id?: number
  AND?: Prisma.GerenciamentoTermosLgpdWhereInput | Prisma.GerenciamentoTermosLgpdWhereInput[]
  OR?: Prisma.GerenciamentoTermosLgpdWhereInput[]
  NOT?: Prisma.GerenciamentoTermosLgpdWhereInput | Prisma.GerenciamentoTermosLgpdWhereInput[]
  versao?: Prisma.StringFilter<"GerenciamentoTermosLgpd"> | string
  titulo?: Prisma.StringFilter<"GerenciamentoTermosLgpd"> | string
  conteudo?: Prisma.StringFilter<"GerenciamentoTermosLgpd"> | string
  hashConteudo?: Prisma.StringFilter<"GerenciamentoTermosLgpd"> | string
  ativo?: Prisma.BoolFilter<"GerenciamentoTermosLgpd"> | boolean
  criadoPor?: Prisma.IntNullableFilter<"GerenciamentoTermosLgpd"> | number | null
  criadoEm?: Prisma.DateTimeFilter<"GerenciamentoTermosLgpd"> | Date | string
  atualizadoEm?: Prisma.DateTimeFilter<"GerenciamentoTermosLgpd"> | Date | string
  validoDe?: Prisma.DateTimeFilter<"GerenciamentoTermosLgpd"> | Date | string
  validoAte?: Prisma.DateTimeNullableFilter<"GerenciamentoTermosLgpd"> | Date | string | null
  tipoDocumento?: Prisma.StringFilter<"GerenciamentoTermosLgpd"> | string
  deletedAt?: Prisma.DateTimeNullableFilter<"GerenciamentoTermosLgpd"> | Date | string | null
  aceiteTermosLgpd?: Prisma.AceiteTermosLgpdListRelationFilter
}, "id">

export type GerenciamentoTermosLgpdOrderByWithAggregationInput = {
  id?: Prisma.SortOrder
  versao?: Prisma.SortOrder
  titulo?: Prisma.SortOrder
  conteudo?: Prisma.SortOrder
  hashConteudo?: Prisma.SortOrder
  ativo?: Prisma.SortOrder
  criadoPor?: Prisma.SortOrderInput | Prisma.SortOrder
  criadoEm?: Prisma.SortOrder
  atualizadoEm?: Prisma.SortOrder
  validoDe?: Prisma.SortOrder
  validoAte?: Prisma.SortOrderInput | Prisma.SortOrder
  tipoDocumento?: Prisma.SortOrder
  deletedAt?: Prisma.SortOrderInput | Prisma.SortOrder
  _count?: Prisma.GerenciamentoTermosLgpdCountOrderByAggregateInput
  _avg?: Prisma.GerenciamentoTermosLgpdAvgOrderByAggregateInput
  _max?: Prisma.GerenciamentoTermosLgpdMaxOrderByAggregateInput
  _min?: Prisma.GerenciamentoTermosLgpdMinOrderByAggregateInput
  _sum?: Prisma.GerenciamentoTermosLgpdSumOrderByAggregateInput
}

export type GerenciamentoTermosLgpdScalarWhereWithAggregatesInput = {
  AND?: Prisma.GerenciamentoTermosLgpdScalarWhereWithAggregatesInput | Prisma.GerenciamentoTermosLgpdScalarWhereWithAggregatesInput[]
  OR?: Prisma.GerenciamentoTermosLgpdScalarWhereWithAggregatesInput[]
  NOT?: Prisma.GerenciamentoTermosLgpdScalarWhereWithAggregatesInput | Prisma.GerenciamentoTermosLgpdScalarWhereWithAggregatesInput[]
  id?: Prisma.IntWithAggregatesFilter<"GerenciamentoTermosLgpd"> | number
  versao?: Prisma.StringWithAggregatesFilter<"GerenciamentoTermosLgpd"> | string
  titulo?: Prisma.StringWithAggregatesFilter<"GerenciamentoTermosLgpd"> | string
  conteudo?: Prisma.StringWithAggregatesFilter<"GerenciamentoTermosLgpd"> | string
  hashConteudo?: Prisma.StringWithAggregatesFilter<"GerenciamentoTermosLgpd"> | string
  ativo?: Prisma.BoolWithAggregatesFilter<"GerenciamentoTermosLgpd"> | boolean
  criadoPor?: Prisma.IntNullableWithAggregatesFilter<"GerenciamentoTermosLgpd"> | number | null
  criadoEm?: Prisma.DateTimeWithAggregatesFilter<"GerenciamentoTermosLgpd"> | Date | string
  atualizadoEm?: Prisma.DateTimeWithAggregatesFilter<"GerenciamentoTermosLgpd"> | Date | string
  validoDe?: Prisma.DateTimeWithAggregatesFilter<"GerenciamentoTermosLgpd"> | Date | string
  validoAte?: Prisma.DateTimeNullableWithAggregatesFilter<"GerenciamentoTermosLgpd"> | Date | string | null
  tipoDocumento?: Prisma.StringWithAggregatesFilter<"GerenciamentoTermosLgpd"> | string
  deletedAt?: Prisma.DateTimeNullableWithAggregatesFilter<"GerenciamentoTermosLgpd"> | Date | string | null
}

export type GerenciamentoTermosLgpdCreateInput = {
  versao: string
  titulo: string
  conteudo: string
  hashConteudo: string
  ativo?: boolean
  criadoPor?: number | null
  criadoEm?: Date | string
  atualizadoEm?: Date | string
  validoDe?: Date | string
  validoAte?: Date | string | null
  tipoDocumento?: string
  deletedAt?: Date | string | null
  aceiteTermosLgpd?: Prisma.AceiteTermosLgpdCreateNestedManyWithoutTermoInput
}

export type GerenciamentoTermosLgpdUncheckedCreateInput = {
  id?: number
  versao: string
  titulo: string
  conteudo: string
  hashConteudo: string
  ativo?: boolean
  criadoPor?: number | null
  criadoEm?: Date | string
  atualizadoEm?: Date | string
  validoDe?: Date | string
  validoAte?: Date | string | null
  tipoDocumento?: string
  deletedAt?: Date | string | null
  aceiteTermosLgpd?: Prisma.AceiteTermosLgpdUncheckedCreateNestedManyWithoutTermoInput
}

export type GerenciamentoTermosLgpdUpdateInput = {
  versao?: Prisma.StringFieldUpdateOperationsInput | string
  titulo?: Prisma.StringFieldUpdateOperationsInput | string
  conteudo?: Prisma.StringFieldUpdateOperationsInput | string
  hashConteudo?: Prisma.StringFieldUpdateOperationsInput | string
  ativo?: Prisma.BoolFieldUpdateOperationsInput | boolean
  criadoPor?: Prisma.NullableIntFieldUpdateOperationsInput | number | null
  criadoEm?: Prisma.DateTimeFieldUpdateOperationsInput | Date | string
  atualizadoEm?: Prisma.DateTimeFieldUpdateOperationsInput | Date | string
  validoDe?: Prisma.DateTimeFieldUpdateOperationsInput | Date | string
  validoAte?: Prisma.NullableDateTimeFieldUpdateOperationsInput | Date | string | null
  tipoDocumento?: Prisma.StringFieldUpdateOperationsInput | string
  deletedAt?: Prisma.NullableDateTimeFieldUpdateOperationsInput | Date | string | null
  aceiteTermosLgpd?: Prisma.AceiteTermosLgpdUpdateManyWithoutTermoNestedInput
}

export type GerenciamentoTermosLgpdUncheckedUpdateInput = {
  id?: Prisma.IntFieldUpdateOperationsInput | number
  versao?: Prisma.StringFieldUpdateOperationsInput | string
  titulo?: Prisma.StringFieldUpdateOperationsInput | string
  conteudo?: Prisma.StringFieldUpdateOperationsInput | string
  hashConteudo?: Prisma.StringFieldUpdateOperationsInput | string
  ativo?: Prisma.BoolFieldUpdateOperationsInput | boolean
  criadoPor?: Prisma.NullableIntFieldUpdateOperationsInput | number | null
  criadoEm?: Prisma.DateTimeFieldUpdateOperationsInput | Date | string
  atualizadoEm?: Prisma.DateTimeFieldUpdateOperationsInput | Date | string
  validoDe?: Prisma.DateTimeFieldUpdateOperationsInput | Date | string
  validoAte?: Prisma.NullableDateTimeFieldUpdateOperationsInput | Date | string | null
  tipoDocumento?: Prisma.StringFieldUpdateOperationsInput | string
  deletedAt?: Prisma.NullableDateTimeFieldUpdateOperationsInput | Date | string | null
  aceiteTermosLgpd?: Prisma.AceiteTermosLgpdUncheckedUpdateManyWithoutTermoNestedInput
}

export type GerenciamentoTermosLgpdCreateManyInput = {
  id?: number
  versao: string
  titulo: string
  conteudo: string
  hashConteudo: string
  ativo?: boolean
  criadoPor?: number | null
  criadoEm?: Date | string
  atualizadoEm?: Date | string
  validoDe?: Date | string
  validoAte?: Date | string | null
  tipoDocumento?: string
  deletedAt?: Date | string | null
}

export type GerenciamentoTermosLgpdUpdateManyMutationInput = {
  versao?: Prisma.StringFieldUpdateOperationsInput | string
  titulo?: Prisma.StringFieldUpdateOperationsInput | string
  conteudo?: Prisma.StringFieldUpdateOperationsInput | string
  hashConteudo?: Prisma.StringFieldUpdateOperationsInput | string
  ativo?: Prisma.BoolFieldUpdateOperationsInput | boolean
  criadoPor?: Prisma.NullableIntFieldUpdateOperationsInput | number | null
  criadoEm?: Prisma.DateTimeFieldUpdateOperationsInput | Date | string
  atualizadoEm?: Prisma.DateTimeFieldUpdateOperationsInput | Date | string
  validoDe?: Prisma.DateTimeFieldUpdateOperationsInput | Date | string
  validoAte?: Prisma.NullableDateTimeFieldUpdateOperationsInput | Date | string | null
  tipoDocumento?: Prisma.StringFieldUpdateOperationsInput | string
  deletedAt?: Prisma.NullableDateTimeFieldUpdateOperationsInput | Date | string | null
}

export type GerenciamentoTermosLgpdUncheckedUpdateManyInput = {
  id?: Prisma.IntFieldUpdateOperationsInput | number
  versao?: Prisma.StringFieldUpdateOperationsInput | string
  titulo?: Prisma.StringFieldUpdateOperationsInput | string
  conteudo?: Prisma.StringFieldUpdateOperationsInput | string
  hashConteudo?: Prisma.StringFieldUpdateOperationsInput | string
  ativo?: Prisma.BoolFieldUpdateOperationsInput | boolean
  criadoPor?: Prisma.NullableIntFieldUpdateOperationsInput | number | null
  criadoEm?: Prisma.DateTimeFieldUpdateOperationsInput | Date | string
  atualizadoEm?: Prisma.DateTimeFieldUpdateOperationsInput | Date | string
  validoDe?: Prisma.DateTimeFieldUpdateOperationsInput | Date | string
  validoAte?: Prisma.NullableDateTimeFieldUpdateOperationsInput | Date | string | null
  tipoDocumento?: Prisma.StringFieldUpdateOperationsInput | string
  deletedAt?: Prisma.NullableDateTimeFieldUpdateOperationsInput | Date | string | null
}

export type GerenciamentoTermosLgpdOrderByRelevanceInput = {
  fields: Prisma.GerenciamentoTermosLgpdOrderByRelevanceFieldEnum | Prisma.GerenciamentoTermosLgpdOrderByRelevanceFieldEnum[]
  sort: Prisma.SortOrder
  search: string
}

export type GerenciamentoTermosLgpdCountOrderByAggregateInput = {
  id?: Prisma.SortOrder
  versao?: Prisma.SortOrder
  titulo?: Prisma.SortOrder
  conteudo?: Prisma.SortOrder
  hashConteudo?: Prisma.SortOrder
  ativo?: Prisma.SortOrder
  criadoPor?: Prisma.SortOrder
  criadoEm?: Prisma.SortOrder
  atualizadoEm?: Prisma.SortOrder
  validoDe?: Prisma.SortOrder
  validoAte?: Prisma.SortOrder
  tipoDocumento?: Prisma.SortOrder
  deletedAt?: Prisma.SortOrder
}

export type GerenciamentoTermosLgpdAvgOrderByAggregateInput = {
  id?: Prisma.SortOrder
  criadoPor?: Prisma.SortOrder
}

export type GerenciamentoTermosLgpdMaxOrderByAggregateInput = {
  id?: Prisma.SortOrder
  versao?: Prisma.SortOrder
  titulo?: Prisma.SortOrder
  conteudo?: Prisma.SortOrder
  hashConteudo?: Prisma.SortOrder
  ativo?: Prisma.SortOrder
  criadoPor?: Prisma.SortOrder
  criadoEm?: Prisma.SortOrder
  atualizadoEm?: Prisma.SortOrder
  validoDe?: Prisma.SortOrder
  validoAte?: Prisma.SortOrder
  tipoDocumento?: Prisma.SortOrder
  deletedAt?: Prisma.SortOrder
}

export type GerenciamentoTermosLgpdMinOrderByAggregateInput = {
  id?: Prisma.SortOrder
  versao?: Prisma.SortOrder
  titulo?: Prisma.SortOrder
  conteudo?: Prisma.SortOrder
  hashConteudo?: Prisma.SortOrder
  ativo?: Prisma.SortOrder
  criadoPor?: Prisma.SortOrder
  criadoEm?: Prisma.SortOrder
  atualizadoEm?: Prisma.SortOrder
  validoDe?: Prisma.SortOrder
  validoAte?: Prisma.SortOrder
  tipoDocumento?: Prisma.SortOrder
  deletedAt?: Prisma.SortOrder
}

export type GerenciamentoTermosLgpdSumOrderByAggregateInput = {
  id?: Prisma.SortOrder
  criadoPor?: Prisma.SortOrder
}

export type GerenciamentoTermosLgpdScalarRelationFilter = {
  is?: Prisma.GerenciamentoTermosLgpdWhereInput
  isNot?: Prisma.GerenciamentoTermosLgpdWhereInput
}

export type GerenciamentoTermosLgpdCreateNestedOneWithoutAceiteTermosLgpdInput = {
  create?: Prisma.XOR<Prisma.GerenciamentoTermosLgpdCreateWithoutAceiteTermosLgpdInput, Prisma.GerenciamentoTermosLgpdUncheckedCreateWithoutAceiteTermosLgpdInput>
  connectOrCreate?: Prisma.GerenciamentoTermosLgpdCreateOrConnectWithoutAceiteTermosLgpdInput
  connect?: Prisma.GerenciamentoTermosLgpdWhereUniqueInput
}

export type GerenciamentoTermosLgpdUpdateOneRequiredWithoutAceiteTermosLgpdNestedInput = {
  create?: Prisma.XOR<Prisma.GerenciamentoTermosLgpdCreateWithoutAceiteTermosLgpdInput, Prisma.GerenciamentoTermosLgpdUncheckedCreateWithoutAceiteTermosLgpdInput>
  connectOrCreate?: Prisma.GerenciamentoTermosLgpdCreateOrConnectWithoutAceiteTermosLgpdInput
  upsert?: Prisma.GerenciamentoTermosLgpdUpsertWithoutAceiteTermosLgpdInput
  connect?: Prisma.GerenciamentoTermosLgpdWhereUniqueInput
  update?: Prisma.XOR<Prisma.XOR<Prisma.GerenciamentoTermosLgpdUpdateToOneWithWhereWithoutAceiteTermosLgpdInput, Prisma.GerenciamentoTermosLgpdUpdateWithoutAceiteTermosLgpdInput>, Prisma.GerenciamentoTermosLgpdUncheckedUpdateWithoutAceiteTermosLgpdInput>
}

export type GerenciamentoTermosLgpdCreateWithoutAceiteTermosLgpdInput = {
  versao: string
  titulo: string
  conteudo: string
  hashConteudo: string
  ativo?: boolean
  criadoPor?: number | null
  criadoEm?: Date | string
  atualizadoEm?: Date | string
  validoDe?: Date | string
  validoAte?: Date | string | null
  tipoDocumento?: string
  deletedAt?: Date | string | null
}

export type GerenciamentoTermosLgpdUncheckedCreateWithoutAceiteTermosLgpdInput = {
  id?: number
  versao: string
  titulo: string
  conteudo: string
  hashConteudo: string
  ativo?: boolean
  criadoPor?: number | null
  criadoEm?: Date | string
  atualizadoEm?: Date | string
  validoDe?: Date | string
  validoAte?: Date | string | null
  tipoDocumento?: string
  deletedAt?: Date | string | null
}

export type GerenciamentoTermosLgpdCreateOrConnectWithoutAceiteTermosLgpdInput = {
  where: Prisma.GerenciamentoTermosLgpdWhereUniqueInput
  create: Prisma.XOR<Prisma.GerenciamentoTermosLgpdCreateWithoutAceiteTermosLgpdInput, Prisma.GerenciamentoTermosLgpdUncheckedCreateWithoutAceiteTermosLgpdInput>
}

export type GerenciamentoTermosLgpdUpsertWithoutAceiteTermosLgpdInput = {
  update: Prisma.XOR<Prisma.GerenciamentoTermosLgpdUpdateWithoutAceiteTermosLgpdInput, Prisma.GerenciamentoTermosLgpdUncheckedUpdateWithoutAceiteTermosLgpdInput>
  create: Prisma.XOR<Prisma.GerenciamentoTermosLgpdCreateWithoutAceiteTermosLgpdInput, Prisma.GerenciamentoTermosLgpdUncheckedCreateWithoutAceiteTermosLgpdInput>
  where?: Prisma.GerenciamentoTermosLgpdWhereInput
}

export type GerenciamentoTermosLgpdUpdateToOneWithWhereWithoutAceiteTermosLgpdInput = {
  where?: Prisma.GerenciamentoTermosLgpdWhereInput
  data: Prisma.XOR<Prisma.GerenciamentoTermosLgpdUpdateWithoutAceiteTermosLgpdInput, Prisma.GerenciamentoTermosLgpdUncheckedUpdateWithoutAceiteTermosLgpdInput>
}

export type GerenciamentoTermosLgpdUpdateWithoutAceiteTermosLgpdInput = {
  versao?: Prisma.StringFieldUpdateOperationsInput | string
  titulo?: Prisma.StringFieldUpdateOperationsInput | string
  conteudo?: Prisma.StringFieldUpdateOperationsInput | string
  hashConteudo?: Prisma.StringFieldUpdateOperationsInput | string
  ativo?: Prisma.BoolFieldUpdateOperationsInput | boolean
  criadoPor?: Prisma.NullableIntFieldUpdateOperationsInput | number | null
  criadoEm?: Prisma.DateTimeFieldUpdateOperationsInput | Date | string
  atualizadoEm?: Prisma.DateTimeFieldUpdateOperationsInput | Date | string
  validoDe?: Prisma.DateTimeFieldUpdateOperationsInput | Date | string
  validoAte?: Prisma.NullableDateTimeFieldUpdateOperationsInput | Date | string | null
  tipoDocumento?: Prisma.StringFieldUpdateOperationsInput | string
  deletedAt?: Prisma.NullableDateTimeFieldUpdateOperationsInput | Date | string | null
}

export type GerenciamentoTermosLgpdUncheckedUpdateWithoutAceiteTermosLgpdInput = {
  id?: Prisma.IntFieldUpdateOperationsInput | number
  versao?: Prisma.StringFieldUpdateOperationsInput | string
  titulo?: Prisma.StringFieldUpdateOperationsInput | string
  conteudo?: Prisma.StringFieldUpdateOperationsInput | string
  hashConteudo?: Prisma.StringFieldUpdateOperationsInput | string
  ativo?: Prisma.BoolFieldUpdateOperationsInput | boolean
  criadoPor?: Prisma.NullableIntFieldUpdateOperationsInput | number | null
  criadoEm?: Prisma.DateTimeFieldUpdateOperationsInput | Date | string
  atualizadoEm?: Prisma.DateTimeFieldUpdateOperationsInput | Date | string
  validoDe?: Prisma.DateTimeFieldUpdateOperationsInput | Date | string
  validoAte?: Prisma.NullableDateTimeFieldUpdateOperationsInput | Date | string | null
  tipoDocumento?: Prisma.StringFieldUpdateOperationsInput | string
  deletedAt?: Prisma.NullableDateTimeFieldUpdateOperationsInput | Date | string | null
}


/**
 * Count Type GerenciamentoTermosLgpdCountOutputType
 */

export type GerenciamentoTermosLgpdCountOutputType = {
  aceiteTermosLgpd: number
}

export type GerenciamentoTermosLgpdCountOutputTypeSelect<ExtArgs extends runtime.Types.Extensions.InternalArgs = runtime.Types.Extensions.DefaultArgs> = {
  aceiteTermosLgpd?: boolean | GerenciamentoTermosLgpdCountOutputTypeCountAceiteTermosLgpdArgs
}

/**
 * GerenciamentoTermosLgpdCountOutputType without action
 */
export type GerenciamentoTermosLgpdCountOutputTypeDefaultArgs<ExtArgs extends runtime.Types.Extensions.InternalArgs = runtime.Types.Extensions.DefaultArgs> = {
  /**
   * Select specific fields to fetch from the GerenciamentoTermosLgpdCountOutputType
   */
  select?: Prisma.GerenciamentoTermosLgpdCountOutputTypeSelect<ExtArgs> | null
}

/**
 * GerenciamentoTermosLgpdCountOutputType without action
 */
export type GerenciamentoTermosLgpdCountOutputTypeCountAceiteTermosLgpdArgs<ExtArgs extends runtime.Types.Extensions.InternalArgs = runtime.Types.Extensions.DefaultArgs> = {
  where?: Prisma.AceiteTermosLgpdWhereInput
}


export type GerenciamentoTermosLgpdSelect<ExtArgs extends runtime.Types.Extensions.InternalArgs = runtime.Types.Extensions.DefaultArgs> = runtime.Types.Extensions.GetSelect<{
  id?: boolean
  versao?: boolean
  titulo?: boolean
  conteudo?: boolean
  hashConteudo?: boolean
  ativo?: boolean
  criadoPor?: boolean
  criadoEm?: boolean
  atualizadoEm?: boolean
  validoDe?: boolean
  validoAte?: boolean
  tipoDocumento?: boolean
  deletedAt?: boolean
  aceiteTermosLgpd?: boolean | Prisma.GerenciamentoTermosLgpd$aceiteTermosLgpdArgs<ExtArgs>
  _count?: boolean | Prisma.GerenciamentoTermosLgpdCountOutputTypeDefaultArgs<ExtArgs>
}, ExtArgs["result"]["gerenciamentoTermosLgpd"]>



export type GerenciamentoTermosLgpdSelectScalar = {
  id?: boolean
  versao?: boolean
  titulo?: boolean
  conteudo?: boolean
  hashConteudo?: boolean
  ativo?: boolean
  criadoPor?: boolean
  criadoEm?: boolean
  atualizadoEm?: boolean
  validoDe?: boolean
  validoAte?: boolean
  tipoDocumento?: boolean
  deletedAt?: boolean
}

export type GerenciamentoTermosLgpdOmit<ExtArgs extends runtime.Types.Extensions.InternalArgs = runtime.Types.Extensions.DefaultArgs> = runtime.Types.Extensions.GetOmit<"id" | "versao" | "titulo" | "conteudo" | "hashConteudo" | "ativo" | "criadoPor" | "criadoEm" | "atualizadoEm" | "validoDe" | "validoAte" | "tipoDocumento" | "deletedAt", ExtArgs["result"]["gerenciamentoTermosLgpd"]>
export type GerenciamentoTermosLgpdInclude<ExtArgs extends runtime.Types.Extensions.InternalArgs = runtime.Types.Extensions.DefaultArgs> = {
  aceiteTermosLgpd?: boolean | Prisma.GerenciamentoTermosLgpd$aceiteTermosLgpdArgs<ExtArgs>
  _count?: boolean | Prisma.GerenciamentoTermosLgpdCountOutputTypeDefaultArgs<ExtArgs>
}

export type $GerenciamentoTermosLgpdPayload<ExtArgs extends runtime.Types.Extensions.InternalArgs = runtime.Types.Extensions.DefaultArgs> = {
  name: "GerenciamentoTermosLgpd"
  objects: {
    aceiteTermosLgpd: Prisma.$AceiteTermosLgpdPayload<ExtArgs>[]
  }
  scalars: runtime.Types.Extensions.GetPayloadResult<{
    id: number
    versao: string
    titulo: string
    conteudo: string
    hashConteudo: string
    ativo: boolean
    criadoPor: number | null
    criadoEm: Date
    atualizadoEm: Date
    validoDe: Date
    validoAte: Date | null
    tipoDocumento: string
    deletedAt: Date | null
  }, ExtArgs["result"]["gerenciamentoTermosLgpd"]>
  composites: {}
}

export type GerenciamentoTermosLgpdGetPayload<S extends boolean | null | undefined | GerenciamentoTermosLgpdDefaultArgs> = runtime.Types.Result.GetResult<Prisma.$GerenciamentoTermosLgpdPayload, S>

export type GerenciamentoTermosLgpdCountArgs<ExtArgs extends runtime.Types.Extensions.InternalArgs = runtime.Types.Extensions.DefaultArgs> =
  Omit<GerenciamentoTermosLgpdFindManyArgs, 'select' | 'include' | 'distinct' | 'omit'> & {
    select?: GerenciamentoTermosLgpdCountAggregateInputType | true
  }

export interface GerenciamentoTermosLgpdDelegate<ExtArgs extends runtime.Types.Extensions.InternalArgs = runtime.Types.Extensions.DefaultArgs, GlobalOmitOptions = {}> {
  [K: symbol]: { types: Prisma.TypeMap<ExtArgs>['model']['GerenciamentoTermosLgpd'], meta: { name: 'GerenciamentoTermosLgpd' } }
  /**
   * Find zero or one GerenciamentoTermosLgpd that matches the filter.
   * @param {GerenciamentoTermosLgpdFindUniqueArgs} args - Arguments to find a GerenciamentoTermosLgpd
   * @example
   * // Get one GerenciamentoTermosLgpd
   * const gerenciamentoTermosLgpd = await prisma.gerenciamentoTermosLgpd.findUnique({
   *   where: {
   *     // ... provide filter here
   *   }
   * })
   */
  findUnique<T extends GerenciamentoTermosLgpdFindUniqueArgs>(args: Prisma.SelectSubset<T, GerenciamentoTermosLgpdFindUniqueArgs<ExtArgs>>): Prisma.Prisma__GerenciamentoTermosLgpdClient<runtime.Types.Result.GetResult<Prisma.$GerenciamentoTermosLgpdPayload<ExtArgs>, T, "findUnique", GlobalOmitOptions> | null, null, ExtArgs, GlobalOmitOptions>

  /**
   * Find one GerenciamentoTermosLgpd that matches the filter or throw an error with `error.code='P2025'`
   * if no matches were found.
   * @param {GerenciamentoTermosLgpdFindUniqueOrThrowArgs} args - Arguments to find a GerenciamentoTermosLgpd
   * @example
   * // Get one GerenciamentoTermosLgpd
   * const gerenciamentoTermosLgpd = await prisma.gerenciamentoTermosLgpd.findUniqueOrThrow({
   *   where: {
   *     // ... provide filter here
   *   }
   * })
   */
  findUniqueOrThrow<T extends GerenciamentoTermosLgpdFindUniqueOrThrowArgs>(args: Prisma.SelectSubset<T, GerenciamentoTermosLgpdFindUniqueOrThrowArgs<ExtArgs>>): Prisma.Prisma__GerenciamentoTermosLgpdClient<runtime.Types.Result.GetResult<Prisma.$GerenciamentoTermosLgpdPayload<ExtArgs>, T, "findUniqueOrThrow", GlobalOmitOptions>, never, ExtArgs, GlobalOmitOptions>

  /**
   * Find the first GerenciamentoTermosLgpd that matches the filter.
   * Note, that providing `undefined` is treated as the value not being there.
   * Read more here: https://pris.ly/d/null-undefined
   * @param {GerenciamentoTermosLgpdFindFirstArgs} args - Arguments to find a GerenciamentoTermosLgpd
   * @example
   * // Get one GerenciamentoTermosLgpd
   * const gerenciamentoTermosLgpd = await prisma.gerenciamentoTermosLgpd.findFirst({
   *   where: {
   *     // ... provide filter here
   *   }
   * })
   */
  findFirst<T extends GerenciamentoTermosLgpdFindFirstArgs>(args?: Prisma.SelectSubset<T, GerenciamentoTermosLgpdFindFirstArgs<ExtArgs>>): Prisma.Prisma__GerenciamentoTermosLgpdClient<runtime.Types.Result.GetResult<Prisma.$GerenciamentoTermosLgpdPayload<ExtArgs>, T, "findFirst", GlobalOmitOptions> | null, null, ExtArgs, GlobalOmitOptions>

  /**
   * Find the first GerenciamentoTermosLgpd that matches the filter or
   * throw `PrismaKnownClientError` with `P2025` code if no matches were found.
   * Note, that providing `undefined` is treated as the value not being there.
   * Read more here: https://pris.ly/d/null-undefined
   * @param {GerenciamentoTermosLgpdFindFirstOrThrowArgs} args - Arguments to find a GerenciamentoTermosLgpd
   * @example
   * // Get one GerenciamentoTermosLgpd
   * const gerenciamentoTermosLgpd = await prisma.gerenciamentoTermosLgpd.findFirstOrThrow({
   *   where: {
   *     // ... provide filter here
   *   }
   * })
   */
  findFirstOrThrow<T extends GerenciamentoTermosLgpdFindFirstOrThrowArgs>(args?: Prisma.SelectSubset<T, GerenciamentoTermosLgpdFindFirstOrThrowArgs<ExtArgs>>): Prisma.Prisma__GerenciamentoTermosLgpdClient<runtime.Types.Result.GetResult<Prisma.$GerenciamentoTermosLgpdPayload<ExtArgs>, T, "findFirstOrThrow", GlobalOmitOptions>, never, ExtArgs, GlobalOmitOptions>

  /**
   * Find zero or more GerenciamentoTermosLgpds that matches the filter.
   * Note, that providing `undefined` is treated as the value not being there.
   * Read more here: https://pris.ly/d/null-undefined
   * @param {GerenciamentoTermosLgpdFindManyArgs} args - Arguments to filter and select certain fields only.
   * @example
   * // Get all GerenciamentoTermosLgpds
   * const gerenciamentoTermosLgpds = await prisma.gerenciamentoTermosLgpd.findMany()
   * 
   * // Get first 10 GerenciamentoTermosLgpds
   * const gerenciamentoTermosLgpds = await prisma.gerenciamentoTermosLgpd.findMany({ take: 10 })
   * 
   * // Only select the `id`
   * const gerenciamentoTermosLgpdWithIdOnly = await prisma.gerenciamentoTermosLgpd.findMany({ select: { id: true } })
   * 
   */
  findMany<T extends GerenciamentoTermosLgpdFindManyArgs>(args?: Prisma.SelectSubset<T, GerenciamentoTermosLgpdFindManyArgs<ExtArgs>>): Prisma.PrismaPromise<runtime.Types.Result.GetResult<Prisma.$GerenciamentoTermosLgpdPayload<ExtArgs>, T, "findMany", GlobalOmitOptions>>

  /**
   * Create a GerenciamentoTermosLgpd.
   * @param {GerenciamentoTermosLgpdCreateArgs} args - Arguments to create a GerenciamentoTermosLgpd.
   * @example
   * // Create one GerenciamentoTermosLgpd
   * const GerenciamentoTermosLgpd = await prisma.gerenciamentoTermosLgpd.create({
   *   data: {
   *     // ... data to create a GerenciamentoTermosLgpd
   *   }
   * })
   * 
   */
  create<T extends GerenciamentoTermosLgpdCreateArgs>(args: Prisma.SelectSubset<T, GerenciamentoTermosLgpdCreateArgs<ExtArgs>>): Prisma.Prisma__GerenciamentoTermosLgpdClient<runtime.Types.Result.GetResult<Prisma.$GerenciamentoTermosLgpdPayload<ExtArgs>, T, "create", GlobalOmitOptions>, never, ExtArgs, GlobalOmitOptions>

  /**
   * Create many GerenciamentoTermosLgpds.
   * @param {GerenciamentoTermosLgpdCreateManyArgs} args - Arguments to create many GerenciamentoTermosLgpds.
   * @example
   * // Create many GerenciamentoTermosLgpds
   * const gerenciamentoTermosLgpd = await prisma.gerenciamentoTermosLgpd.createMany({
   *   data: [
   *     // ... provide data here
   *   ]
   * })
   *     
   */
  createMany<T extends GerenciamentoTermosLgpdCreateManyArgs>(args?: Prisma.SelectSubset<T, GerenciamentoTermosLgpdCreateManyArgs<ExtArgs>>): Prisma.PrismaPromise<Prisma.BatchPayload>

  /**
   * Delete a GerenciamentoTermosLgpd.
   * @param {GerenciamentoTermosLgpdDeleteArgs} args - Arguments to delete one GerenciamentoTermosLgpd.
   * @example
   * // Delete one GerenciamentoTermosLgpd
   * const GerenciamentoTermosLgpd = await prisma.gerenciamentoTermosLgpd.delete({
   *   where: {
   *     // ... filter to delete one GerenciamentoTermosLgpd
   *   }
   * })
   * 
   */
  delete<T extends GerenciamentoTermosLgpdDeleteArgs>(args: Prisma.SelectSubset<T, GerenciamentoTermosLgpdDeleteArgs<ExtArgs>>): Prisma.Prisma__GerenciamentoTermosLgpdClient<runtime.Types.Result.GetResult<Prisma.$GerenciamentoTermosLgpdPayload<ExtArgs>, T, "delete", GlobalOmitOptions>, never, ExtArgs, GlobalOmitOptions>

  /**
   * Update one GerenciamentoTermosLgpd.
   * @param {GerenciamentoTermosLgpdUpdateArgs} args - Arguments to update one GerenciamentoTermosLgpd.
   * @example
   * // Update one GerenciamentoTermosLgpd
   * const gerenciamentoTermosLgpd = await prisma.gerenciamentoTermosLgpd.update({
   *   where: {
   *     // ... provide filter here
   *   },
   *   data: {
   *     // ... provide data here
   *   }
   * })
   * 
   */
  update<T extends GerenciamentoTermosLgpdUpdateArgs>(args: Prisma.SelectSubset<T, GerenciamentoTermosLgpdUpdateArgs<ExtArgs>>): Prisma.Prisma__GerenciamentoTermosLgpdClient<runtime.Types.Result.GetResult<Prisma.$GerenciamentoTermosLgpdPayload<ExtArgs>, T, "update", GlobalOmitOptions>, never, ExtArgs, GlobalOmitOptions>

  /**
   * Delete zero or more GerenciamentoTermosLgpds.
   * @param {GerenciamentoTermosLgpdDeleteManyArgs} args - Arguments to filter GerenciamentoTermosLgpds to delete.
   * @example
   * // Delete a few GerenciamentoTermosLgpds
   * const { count } = await prisma.gerenciamentoTermosLgpd.deleteMany({
   *   where: {
   *     // ... provide filter here
   *   }
   * })
   * 
   */
  deleteMany<T extends GerenciamentoTermosLgpdDeleteManyArgs>(args?: Prisma.SelectSubset<T, GerenciamentoTermosLgpdDeleteManyArgs<ExtArgs>>): Prisma.PrismaPromise<Prisma.BatchPayload>

  /**
   * Update zero or more GerenciamentoTermosLgpds.
   * Note, that providing `undefined` is treated as the value not being there.
   * Read more here: https://pris.ly/d/null-undefined
   * @param {GerenciamentoTermosLgpdUpdateManyArgs} args - Arguments to update one or more rows.
   * @example
   * // Update many GerenciamentoTermosLgpds
   * const gerenciamentoTermosLgpd = await prisma.gerenciamentoTermosLgpd.updateMany({
   *   where: {
   *     // ... provide filter here
   *   },
   *   data: {
   *     // ... provide data here
   *   }
   * })
   * 
   */
  updateMany<T extends GerenciamentoTermosLgpdUpdateManyArgs>(args: Prisma.SelectSubset<T, GerenciamentoTermosLgpdUpdateManyArgs<ExtArgs>>): Prisma.PrismaPromise<Prisma.BatchPayload>

  /**
   * Create or update one GerenciamentoTermosLgpd.
   * @param {GerenciamentoTermosLgpdUpsertArgs} args - Arguments to update or create a GerenciamentoTermosLgpd.
   * @example
   * // Update or create a GerenciamentoTermosLgpd
   * const gerenciamentoTermosLgpd = await prisma.gerenciamentoTermosLgpd.upsert({
   *   create: {
   *     // ... data to create a GerenciamentoTermosLgpd
   *   },
   *   update: {
   *     // ... in case it already exists, update
   *   },
   *   where: {
   *     // ... the filter for the GerenciamentoTermosLgpd we want to update
   *   }
   * })
   */
  upsert<T extends GerenciamentoTermosLgpdUpsertArgs>(args: Prisma.SelectSubset<T, GerenciamentoTermosLgpdUpsertArgs<ExtArgs>>): Prisma.Prisma__GerenciamentoTermosLgpdClient<runtime.Types.Result.GetResult<Prisma.$GerenciamentoTermosLgpdPayload<ExtArgs>, T, "upsert", GlobalOmitOptions>, never, ExtArgs, GlobalOmitOptions>


  /**
   * Count the number of GerenciamentoTermosLgpds.
   * Note, that providing `undefined` is treated as the value not being there.
   * Read more here: https://pris.ly/d/null-undefined
   * @param {GerenciamentoTermosLgpdCountArgs} args - Arguments to filter GerenciamentoTermosLgpds to count.
   * @example
   * // Count the number of GerenciamentoTermosLgpds
   * const count = await prisma.gerenciamentoTermosLgpd.count({
   *   where: {
   *     // ... the filter for the GerenciamentoTermosLgpds we want to count
   *   }
   * })
  **/
  count<T extends GerenciamentoTermosLgpdCountArgs>(
    args?: Prisma.Subset<T, GerenciamentoTermosLgpdCountArgs>,
  ): Prisma.PrismaPromise<
    T extends runtime.Types.Utils.Record<'select', any>
      ? T['select'] extends true
        ? number
        : Prisma.GetScalarType<T['select'], GerenciamentoTermosLgpdCountAggregateOutputType>
      : number
  >

  /**
   * Allows you to perform aggregations operations on a GerenciamentoTermosLgpd.
   * Note, that providing `undefined` is treated as the value not being there.
   * Read more here: https://pris.ly/d/null-undefined
   * @param {GerenciamentoTermosLgpdAggregateArgs} args - Select which aggregations you would like to apply and on what fields.
   * @example
   * // Ordered by age ascending
   * // Where email contains prisma.io
   * // Limited to the 10 users
   * const aggregations = await prisma.user.aggregate({
   *   _avg: {
   *     age: true,
   *   },
   *   where: {
   *     email: {
   *       contains: "prisma.io",
   *     },
   *   },
   *   orderBy: {
   *     age: "asc",
   *   },
   *   take: 10,
   * })
  **/
  aggregate<T extends GerenciamentoTermosLgpdAggregateArgs>(args: Prisma.Subset<T, GerenciamentoTermosLgpdAggregateArgs>): Prisma.PrismaPromise<GetGerenciamentoTermosLgpdAggregateType<T>>

  /**
   * Group by GerenciamentoTermosLgpd.
   * Note, that providing `undefined` is treated as the value not being there.
   * Read more here: https://pris.ly/d/null-undefined
   * @param {GerenciamentoTermosLgpdGroupByArgs} args - Group by arguments.
   * @example
   * // Group by city, order by createdAt, get count
   * const result = await prisma.user.groupBy({
   *   by: ['city', 'createdAt'],
   *   orderBy: {
   *     createdAt: true
   *   },
   *   _count: {
   *     _all: true
   *   },
   * })
   * 
  **/
  groupBy<
    T extends GerenciamentoTermosLgpdGroupByArgs,
    HasSelectOrTake extends Prisma.Or<
      Prisma.Extends<'skip', Prisma.Keys<T>>,
      Prisma.Extends<'take', Prisma.Keys<T>>
    >,
    OrderByArg extends Prisma.True extends HasSelectOrTake
      ? { orderBy: GerenciamentoTermosLgpdGroupByArgs['orderBy'] }
      : { orderBy?: GerenciamentoTermosLgpdGroupByArgs['orderBy'] },
    OrderFields extends Prisma.ExcludeUnderscoreKeys<Prisma.Keys<Prisma.MaybeTupleToUnion<T['orderBy']>>>,
    ByFields extends Prisma.MaybeTupleToUnion<T['by']>,
    ByValid extends Prisma.Has<ByFields, OrderFields>,
    HavingFields extends Prisma.GetHavingFields<T['having']>,
    HavingValid extends Prisma.Has<ByFields, HavingFields>,
    ByEmpty extends T['by'] extends never[] ? Prisma.True : Prisma.False,
    InputErrors extends ByEmpty extends Prisma.True
    ? `Error: "by" must not be empty.`
    : HavingValid extends Prisma.False
    ? {
        [P in HavingFields]: P extends ByFields
          ? never
          : P extends string
          ? `Error: Field "${P}" used in "having" needs to be provided in "by".`
          : [
              Error,
              'Field ',
              P,
              ` in "having" needs to be provided in "by"`,
            ]
      }[HavingFields]
    : 'take' extends Prisma.Keys<T>
    ? 'orderBy' extends Prisma.Keys<T>
      ? ByValid extends Prisma.True
        ? {}
        : {
            [P in OrderFields]: P extends ByFields
              ? never
              : `Error: Field "${P}" in "orderBy" needs to be provided in "by"`
          }[OrderFields]
      : 'Error: If you provide "take", you also need to provide "orderBy"'
    : 'skip' extends Prisma.Keys<T>
    ? 'orderBy' extends Prisma.Keys<T>
      ? ByValid extends Prisma.True
        ? {}
        : {
            [P in OrderFields]: P extends ByFields
              ? never
              : `Error: Field "${P}" in "orderBy" needs to be provided in "by"`
          }[OrderFields]
      : 'Error: If you provide "skip", you also need to provide "orderBy"'
    : ByValid extends Prisma.True
    ? {}
    : {
        [P in OrderFields]: P extends ByFields
          ? never
          : `Error: Field "${P}" in "orderBy" needs to be provided in "by"`
      }[OrderFields]
  >(args: Prisma.SubsetIntersection<T, GerenciamentoTermosLgpdGroupByArgs, OrderByArg> & InputErrors): {} extends InputErrors ? GetGerenciamentoTermosLgpdGroupByPayload<T> : Prisma.PrismaPromise<InputErrors>
/**
 * Fields of the GerenciamentoTermosLgpd model
 */
readonly fields: GerenciamentoTermosLgpdFieldRefs;
}

/**
 * The delegate class that acts as a "Promise-like" for GerenciamentoTermosLgpd.
 * Why is this prefixed with `Prisma__`?
 * Because we want to prevent naming conflicts as mentioned in
 * https://github.com/prisma/prisma-client-js/issues/707
 */
export interface Prisma__GerenciamentoTermosLgpdClient<T, Null = never, ExtArgs extends runtime.Types.Extensions.InternalArgs = runtime.Types.Extensions.DefaultArgs, GlobalOmitOptions = {}> extends Prisma.PrismaPromise<T> {
  readonly [Symbol.toStringTag]: "PrismaPromise"
  aceiteTermosLgpd<T extends Prisma.GerenciamentoTermosLgpd$aceiteTermosLgpdArgs<ExtArgs> = {}>(args?: Prisma.Subset<T, Prisma.GerenciamentoTermosLgpd$aceiteTermosLgpdArgs<ExtArgs>>): Prisma.PrismaPromise<runtime.Types.Result.GetResult<Prisma.$AceiteTermosLgpdPayload<ExtArgs>, T, "findMany", GlobalOmitOptions> | Null>
  /**
   * Attaches callbacks for the resolution and/or rejection of the Promise.
   * @param onfulfilled The callback to execute when the Promise is resolved.
   * @param onrejected The callback to execute when the Promise is rejected.
   * @returns A Promise for the completion of which ever callback is executed.
   */
  then<TResult1 = T, TResult2 = never>(onfulfilled?: ((value: T) => TResult1 | PromiseLike<TResult1>) | undefined | null, onrejected?: ((reason: any) => TResult2 | PromiseLike<TResult2>) | undefined | null): runtime.Types.Utils.JsPromise<TResult1 | TResult2>
  /**
   * Attaches a callback for only the rejection of the Promise.
   * @param onrejected The callback to execute when the Promise is rejected.
   * @returns A Promise for the completion of the callback.
   */
  catch<TResult = never>(onrejected?: ((reason: any) => TResult | PromiseLike<TResult>) | undefined | null): runtime.Types.Utils.JsPromise<T | TResult>
  /**
   * Attaches a callback that is invoked when the Promise is settled (fulfilled or rejected). The
   * resolved value cannot be modified from the callback.
   * @param onfinally The callback to execute when the Promise is settled (fulfilled or rejected).
   * @returns A Promise for the completion of the callback.
   */
  finally(onfinally?: (() => void) | undefined | null): runtime.Types.Utils.JsPromise<T>
}




/**
 * Fields of the GerenciamentoTermosLgpd model
 */
export interface GerenciamentoTermosLgpdFieldRefs {
  readonly id: Prisma.FieldRef<"GerenciamentoTermosLgpd", 'Int'>
  readonly versao: Prisma.FieldRef<"GerenciamentoTermosLgpd", 'String'>
  readonly titulo: Prisma.FieldRef<"GerenciamentoTermosLgpd", 'String'>
  readonly conteudo: Prisma.FieldRef<"GerenciamentoTermosLgpd", 'String'>
  readonly hashConteudo: Prisma.FieldRef<"GerenciamentoTermosLgpd", 'String'>
  readonly ativo: Prisma.FieldRef<"GerenciamentoTermosLgpd", 'Boolean'>
  readonly criadoPor: Prisma.FieldRef<"GerenciamentoTermosLgpd", 'Int'>
  readonly criadoEm: Prisma.FieldRef<"GerenciamentoTermosLgpd", 'DateTime'>
  readonly atualizadoEm: Prisma.FieldRef<"GerenciamentoTermosLgpd", 'DateTime'>
  readonly validoDe: Prisma.FieldRef<"GerenciamentoTermosLgpd", 'DateTime'>
  readonly validoAte: Prisma.FieldRef<"GerenciamentoTermosLgpd", 'DateTime'>
  readonly tipoDocumento: Prisma.FieldRef<"GerenciamentoTermosLgpd", 'String'>
  readonly deletedAt: Prisma.FieldRef<"GerenciamentoTermosLgpd", 'DateTime'>
}
    

// Custom InputTypes
/**
 * GerenciamentoTermosLgpd findUnique
 */
export type GerenciamentoTermosLgpdFindUniqueArgs<ExtArgs extends runtime.Types.Extensions.InternalArgs = runtime.Types.Extensions.DefaultArgs> = {
  /**
   * Select specific fields to fetch from the GerenciamentoTermosLgpd
   */
  select?: Prisma.GerenciamentoTermosLgpdSelect<ExtArgs> | null
  /**
   * Omit specific fields from the GerenciamentoTermosLgpd
   */
  omit?: Prisma.GerenciamentoTermosLgpdOmit<ExtArgs> | null
  /**
   * Choose, which related nodes to fetch as well
   */
  include?: Prisma.GerenciamentoTermosLgpdInclude<ExtArgs> | null
  /**
   * Filter, which GerenciamentoTermosLgpd to fetch.
   */
  where: Prisma.GerenciamentoTermosLgpdWhereUniqueInput
}

/**
 * GerenciamentoTermosLgpd findUniqueOrThrow
 */
export type GerenciamentoTermosLgpdFindUniqueOrThrowArgs<ExtArgs extends runtime.Types.Extensions.InternalArgs = runtime.Types.Extensions.DefaultArgs> = {
  /**
   * Select specific fields to fetch from the GerenciamentoTermosLgpd
   */
  select?: Prisma.GerenciamentoTermosLgpdSelect<ExtArgs> | null
  /**
   * Omit specific fields from the GerenciamentoTermosLgpd
   */
  omit?: Prisma.GerenciamentoTermosLgpdOmit<ExtArgs> | null
  /**
   * Choose, which related nodes to fetch as well
   */
  include?: Prisma.GerenciamentoTermosLgpdInclude<ExtArgs> | null
  /**
   * Filter, which GerenciamentoTermosLgpd to fetch.
   */
  where: Prisma.GerenciamentoTermosLgpdWhereUniqueInput
}

/**
 * GerenciamentoTermosLgpd findFirst
 */
export type GerenciamentoTermosLgpdFindFirstArgs<ExtArgs extends runtime.Types.Extensions.InternalArgs = runtime.Types.Extensions.DefaultArgs> = {
  /**
   * Select specific fields to fetch from the GerenciamentoTermosLgpd
   */
  select?: Prisma.GerenciamentoTermosLgpdSelect<ExtArgs> | null
  /**
   * Omit specific fields from the GerenciamentoTermosLgpd
   */
  omit?: Prisma.GerenciamentoTermosLgpdOmit<ExtArgs> | null
  /**
   * Choose, which related nodes to fetch as well
   */
  include?: Prisma.GerenciamentoTermosLgpdInclude<ExtArgs> | null
  /**
   * Filter, which GerenciamentoTermosLgpd to fetch.
   */
  where?: Prisma.GerenciamentoTermosLgpdWhereInput
  /**
   * {@link https://www.prisma.io/docs/concepts/components/prisma-client/sorting Sorting Docs}
   * 
   * Determine the order of GerenciamentoTermosLgpds to fetch.
   */
  orderBy?: Prisma.GerenciamentoTermosLgpdOrderByWithRelationInput | Prisma.GerenciamentoTermosLgpdOrderByWithRelationInput[]
  /**
   * {@link https://www.prisma.io/docs/concepts/components/prisma-client/pagination#cursor-based-pagination Cursor Docs}
   * 
   * Sets the position for searching for GerenciamentoTermosLgpds.
   */
  cursor?: Prisma.GerenciamentoTermosLgpdWhereUniqueInput
  /**
   * {@link https://www.prisma.io/docs/concepts/components/prisma-client/pagination Pagination Docs}
   * 
   * Take `±n` GerenciamentoTermosLgpds from the position of the cursor.
   */
  take?: number
  /**
   * {@link https://www.prisma.io/docs/concepts/components/prisma-client/pagination Pagination Docs}
   * 
   * Skip the first `n` GerenciamentoTermosLgpds.
   */
  skip?: number
  /**
   * {@link https://www.prisma.io/docs/concepts/components/prisma-client/distinct Distinct Docs}
   * 
   * Filter by unique combinations of GerenciamentoTermosLgpds.
   */
  distinct?: Prisma.GerenciamentoTermosLgpdScalarFieldEnum | Prisma.GerenciamentoTermosLgpdScalarFieldEnum[]
}

/**
 * GerenciamentoTermosLgpd findFirstOrThrow
 */
export type GerenciamentoTermosLgpdFindFirstOrThrowArgs<ExtArgs extends runtime.Types.Extensions.InternalArgs = runtime.Types.Extensions.DefaultArgs> = {
  /**
   * Select specific fields to fetch from the GerenciamentoTermosLgpd
   */
  select?: Prisma.GerenciamentoTermosLgpdSelect<ExtArgs> | null
  /**
   * Omit specific fields from the GerenciamentoTermosLgpd
   */
  omit?: Prisma.GerenciamentoTermosLgpdOmit<ExtArgs> | null
  /**
   * Choose, which related nodes to fetch as well
   */
  include?: Prisma.GerenciamentoTermosLgpdInclude<ExtArgs> | null
  /**
   * Filter, which GerenciamentoTermosLgpd to fetch.
   */
  where?: Prisma.GerenciamentoTermosLgpdWhereInput
  /**
   * {@link https://www.prisma.io/docs/concepts/components/prisma-client/sorting Sorting Docs}
   * 
   * Determine the order of GerenciamentoTermosLgpds to fetch.
   */
  orderBy?: Prisma.GerenciamentoTermosLgpdOrderByWithRelationInput | Prisma.GerenciamentoTermosLgpdOrderByWithRelationInput[]
  /**
   * {@link https://www.prisma.io/docs/concepts/components/prisma-client/pagination#cursor-based-pagination Cursor Docs}
   * 
   * Sets the position for searching for GerenciamentoTermosLgpds.
   */
  cursor?: Prisma.GerenciamentoTermosLgpdWhereUniqueInput
  /**
   * {@link https://www.prisma.io/docs/concepts/components/prisma-client/pagination Pagination Docs}
   * 
   * Take `±n` GerenciamentoTermosLgpds from the position of the cursor.
   */
  take?: number
  /**
   * {@link https://www.prisma.io/docs/concepts/components/prisma-client/pagination Pagination Docs}
   * 
   * Skip the first `n` GerenciamentoTermosLgpds.
   */
  skip?: number
  /**
   * {@link https://www.prisma.io/docs/concepts/components/prisma-client/distinct Distinct Docs}
   * 
   * Filter by unique combinations of GerenciamentoTermosLgpds.
   */
  distinct?: Prisma.GerenciamentoTermosLgpdScalarFieldEnum | Prisma.GerenciamentoTermosLgpdScalarFieldEnum[]
}

/**
 * GerenciamentoTermosLgpd findMany
 */
export type GerenciamentoTermosLgpdFindManyArgs<ExtArgs extends runtime.Types.Extensions.InternalArgs = runtime.Types.Extensions.DefaultArgs> = {
  /**
   * Select specific fields to fetch from the GerenciamentoTermosLgpd
   */
  select?: Prisma.GerenciamentoTermosLgpdSelect<ExtArgs> | null
  /**
   * Omit specific fields from the GerenciamentoTermosLgpd
   */
  omit?: Prisma.GerenciamentoTermosLgpdOmit<ExtArgs> | null
  /**
   * Choose, which related nodes to fetch as well
   */
  include?: Prisma.GerenciamentoTermosLgpdInclude<ExtArgs> | null
  /**
   * Filter, which GerenciamentoTermosLgpds to fetch.
   */
  where?: Prisma.GerenciamentoTermosLgpdWhereInput
  /**
   * {@link https://www.prisma.io/docs/concepts/components/prisma-client/sorting Sorting Docs}
   * 
   * Determine the order of GerenciamentoTermosLgpds to fetch.
   */
  orderBy?: Prisma.GerenciamentoTermosLgpdOrderByWithRelationInput | Prisma.GerenciamentoTermosLgpdOrderByWithRelationInput[]
  /**
   * {@link https://www.prisma.io/docs/concepts/components/prisma-client/pagination#cursor-based-pagination Cursor Docs}
   * 
   * Sets the position for listing GerenciamentoTermosLgpds.
   */
  cursor?: Prisma.GerenciamentoTermosLgpdWhereUniqueInput
  /**
   * {@link https://www.prisma.io/docs/concepts/components/prisma-client/pagination Pagination Docs}
   * 
   * Take `±n` GerenciamentoTermosLgpds from the position of the cursor.
   */
  take?: number
  /**
   * {@link https://www.prisma.io/docs/concepts/components/prisma-client/pagination Pagination Docs}
   * 
   * Skip the first `n` GerenciamentoTermosLgpds.
   */
  skip?: number
  distinct?: Prisma.GerenciamentoTermosLgpdScalarFieldEnum | Prisma.GerenciamentoTermosLgpdScalarFieldEnum[]
}

/**
 * GerenciamentoTermosLgpd create
 */
export type GerenciamentoTermosLgpdCreateArgs<ExtArgs extends runtime.Types.Extensions.InternalArgs = runtime.Types.Extensions.DefaultArgs> = {
  /**
   * Select specific fields to fetch from the GerenciamentoTermosLgpd
   */
  select?: Prisma.GerenciamentoTermosLgpdSelect<ExtArgs> | null
  /**
   * Omit specific fields from the GerenciamentoTermosLgpd
   */
  omit?: Prisma.GerenciamentoTermosLgpdOmit<ExtArgs> | null
  /**
   * Choose, which related nodes to fetch as well
   */
  include?: Prisma.GerenciamentoTermosLgpdInclude<ExtArgs> | null
  /**
   * The data needed to create a GerenciamentoTermosLgpd.
   */
  data: Prisma.XOR<Prisma.GerenciamentoTermosLgpdCreateInput, Prisma.GerenciamentoTermosLgpdUncheckedCreateInput>
}

/**
 * GerenciamentoTermosLgpd createMany
 */
export type GerenciamentoTermosLgpdCreateManyArgs<ExtArgs extends runtime.Types.Extensions.InternalArgs = runtime.Types.Extensions.DefaultArgs> = {
  /**
   * The data used to create many GerenciamentoTermosLgpds.
   */
  data: Prisma.GerenciamentoTermosLgpdCreateManyInput | Prisma.GerenciamentoTermosLgpdCreateManyInput[]
  skipDuplicates?: boolean
}

/**
 * GerenciamentoTermosLgpd update
 */
export type GerenciamentoTermosLgpdUpdateArgs<ExtArgs extends runtime.Types.Extensions.InternalArgs = runtime.Types.Extensions.DefaultArgs> = {
  /**
   * Select specific fields to fetch from the GerenciamentoTermosLgpd
   */
  select?: Prisma.GerenciamentoTermosLgpdSelect<ExtArgs> | null
  /**
   * Omit specific fields from the GerenciamentoTermosLgpd
   */
  omit?: Prisma.GerenciamentoTermosLgpdOmit<ExtArgs> | null
  /**
   * Choose, which related nodes to fetch as well
   */
  include?: Prisma.GerenciamentoTermosLgpdInclude<ExtArgs> | null
  /**
   * The data needed to update a GerenciamentoTermosLgpd.
   */
  data: Prisma.XOR<Prisma.GerenciamentoTermosLgpdUpdateInput, Prisma.GerenciamentoTermosLgpdUncheckedUpdateInput>
  /**
   * Choose, which GerenciamentoTermosLgpd to update.
   */
  where: Prisma.GerenciamentoTermosLgpdWhereUniqueInput
}

/**
 * GerenciamentoTermosLgpd updateMany
 */
export type GerenciamentoTermosLgpdUpdateManyArgs<ExtArgs extends runtime.Types.Extensions.InternalArgs = runtime.Types.Extensions.DefaultArgs> = {
  /**
   * The data used to update GerenciamentoTermosLgpds.
   */
  data: Prisma.XOR<Prisma.GerenciamentoTermosLgpdUpdateManyMutationInput, Prisma.GerenciamentoTermosLgpdUncheckedUpdateManyInput>
  /**
   * Filter which GerenciamentoTermosLgpds to update
   */
  where?: Prisma.GerenciamentoTermosLgpdWhereInput
  /**
   * Limit how many GerenciamentoTermosLgpds to update.
   */
  limit?: number
}

/**
 * GerenciamentoTermosLgpd upsert
 */
export type GerenciamentoTermosLgpdUpsertArgs<ExtArgs extends runtime.Types.Extensions.InternalArgs = runtime.Types.Extensions.DefaultArgs> = {
  /**
   * Select specific fields to fetch from the GerenciamentoTermosLgpd
   */
  select?: Prisma.GerenciamentoTermosLgpdSelect<ExtArgs> | null
  /**
   * Omit specific fields from the GerenciamentoTermosLgpd
   */
  omit?: Prisma.GerenciamentoTermosLgpdOmit<ExtArgs> | null
  /**
   * Choose, which related nodes to fetch as well
   */
  include?: Prisma.GerenciamentoTermosLgpdInclude<ExtArgs> | null
  /**
   * The filter to search for the GerenciamentoTermosLgpd to update in case it exists.
   */
  where: Prisma.GerenciamentoTermosLgpdWhereUniqueInput
  /**
   * In case the GerenciamentoTermosLgpd found by the `where` argument doesn't exist, create a new GerenciamentoTermosLgpd with this data.
   */
  create: Prisma.XOR<Prisma.GerenciamentoTermosLgpdCreateInput, Prisma.GerenciamentoTermosLgpdUncheckedCreateInput>
  /**
   * In case the GerenciamentoTermosLgpd was found with the provided `where` argument, update it with this data.
   */
  update: Prisma.XOR<Prisma.GerenciamentoTermosLgpdUpdateInput, Prisma.GerenciamentoTermosLgpdUncheckedUpdateInput>
}

/**
 * GerenciamentoTermosLgpd delete
 */
export type GerenciamentoTermosLgpdDeleteArgs<ExtArgs extends runtime.Types.Extensions.InternalArgs = runtime.Types.Extensions.DefaultArgs> = {
  /**
   * Select specific fields to fetch from the GerenciamentoTermosLgpd
   */
  select?: Prisma.GerenciamentoTermosLgpdSelect<ExtArgs> | null
  /**
   * Omit specific fields from the GerenciamentoTermosLgpd
   */
  omit?: Prisma.GerenciamentoTermosLgpdOmit<ExtArgs> | null
  /**
   * Choose, which related nodes to fetch as well
   */
  include?: Prisma.GerenciamentoTermosLgpdInclude<ExtArgs> | null
  /**
   * Filter which GerenciamentoTermosLgpd to delete.
   */
  where: Prisma.GerenciamentoTermosLgpdWhereUniqueInput
}

/**
 * GerenciamentoTermosLgpd deleteMany
 */
export type GerenciamentoTermosLgpdDeleteManyArgs<ExtArgs extends runtime.Types.Extensions.InternalArgs = runtime.Types.Extensions.DefaultArgs> = {
  /**
   * Filter which GerenciamentoTermosLgpds to delete
   */
  where?: Prisma.GerenciamentoTermosLgpdWhereInput
  /**
   * Limit how many GerenciamentoTermosLgpds to delete.
   */
  limit?: number
}

/**
 * GerenciamentoTermosLgpd.aceiteTermosLgpd
 */
export type GerenciamentoTermosLgpd$aceiteTermosLgpdArgs<ExtArgs extends runtime.Types.Extensions.InternalArgs = runtime.Types.Extensions.DefaultArgs> = {
  /**
   * Select specific fields to fetch from the AceiteTermosLgpd
   */
  select?: Prisma.AceiteTermosLgpdSelect<ExtArgs> | null
  /**
   * Omit specific fields from the AceiteTermosLgpd
   */
  omit?: Prisma.AceiteTermosLgpdOmit<ExtArgs> | null
  /**
   * Choose, which related nodes to fetch as well
   */
  include?: Prisma.AceiteTermosLgpdInclude<ExtArgs> | null
  where?: Prisma.AceiteTermosLgpdWhereInput
  orderBy?: Prisma.AceiteTermosLgpdOrderByWithRelationInput | Prisma.AceiteTermosLgpdOrderByWithRelationInput[]
  cursor?: Prisma.AceiteTermosLgpdWhereUniqueInput
  take?: number
  skip?: number
  distinct?: Prisma.AceiteTermosLgpdScalarFieldEnum | Prisma.AceiteTermosLgpdScalarFieldEnum[]
}

/**
 * GerenciamentoTermosLgpd without action
 */
export type GerenciamentoTermosLgpdDefaultArgs<ExtArgs extends runtime.Types.Extensions.InternalArgs = runtime.Types.Extensions.DefaultArgs> = {
  /**
   * Select specific fields to fetch from the GerenciamentoTermosLgpd
   */
  select?: Prisma.GerenciamentoTermosLgpdSelect<ExtArgs> | null
  /**
   * Omit specific fields from the GerenciamentoTermosLgpd
   */
  omit?: Prisma.GerenciamentoTermosLgpdOmit<ExtArgs> | null
  /**
   * Choose, which related nodes to fetch as well
   */
  include?: Prisma.GerenciamentoTermosLgpdInclude<ExtArgs> | null
}
