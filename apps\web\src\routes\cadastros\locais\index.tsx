import { useNavigate } from "@tanstack/react-router";
import { createFileRoute } from "@tanstack/react-router";
import { useState } from "react";
import { requireAdminRole } from "@/lib/route-guards";
import { type LocalAtendimento } from "@/lib/api";
import { useLocaisAtendimento, useDeleteLocalAtendimento } from "@/hooks/use-locais-atendimento";
import { Button } from "@/components/ui/button";
import { Input } from "@/components/ui/input";
import { Table, TableBody, TableCell, TableHead, TableHeader, TableRow } from "@/components/ui/table";
import { Badge } from "@/components/ui/badge";
import { Card, CardContent, CardHeader, CardTitle } from "@/components/ui/card";
import {
  DropdownMenu,
  DropdownMenuContent,
  DropdownMenuItem,
  DropdownMenuTrigger,
} from "@/components/ui/dropdown-menu";
import { Skeleton } from "@/components/ui/skeleton";
import { MapPin, Search, Plus, MoreHorizontal, Pencil, Trash2, Eye, Building2 } from "lucide-react";
import { toast } from "sonner";
import { DeleteConfirmDialog } from "@/components/ui/delete-confirm-dialog";

function LocaisIndex() {
  const navigate = useNavigate();
  const [page, setPage] = useState(1);
  const [search, setSearch] = useState("");
  const [deleteDialogOpen, setDeleteDialogOpen] = useState(false);
  const [localToDelete, setLocalToDelete] = useState<string | null>(null);
  const limit = 10;

  const { data, isLoading } = useLocaisAtendimento({ page, limit, search });
  const deleteLocalAtendimento = useDeleteLocalAtendimento();

  const handleDelete = (id: string) => {
    setLocalToDelete(id);
    setDeleteDialogOpen(true);
  };

  const confirmDelete = () => {
    if (localToDelete) {
      deleteLocalAtendimento.mutate(localToDelete);
      setDeleteDialogOpen(false);
      setLocalToDelete(null);
    }
  };

  const formatCEP = (cep: string) => {
    if (!cep) return "-";
    return cep.replace(/(\d{5})(\d{3})/, "$1-$2");
  };

  return (
    <div className="space-y-6">
      <div className="flex justify-between items-center">
        <div>
          <h1 className="text-3xl font-bold">Locais de Atendimento</h1>
          <p className="text-muted-foreground">Gerencie os locais de atendimento dos clientes</p>
        </div>
        {/* Botão desabilitado temporariamente - criar local deve ser feito via cliente */}
        <Button disabled>
          <Plus className="mr-2 h-4 w-4" />
          Novo Local
        </Button>
      </div>

      <Card>
        <CardHeader>
          <CardTitle>Lista de Locais</CardTitle>
          <div className="flex gap-4 mt-4">
            <div className="relative flex-1 max-w-sm">
              <Search className="absolute left-3 top-1/2 transform -translate-y-1/2 text-muted-foreground h-4 w-4" />
              <Input
                placeholder="Buscar por nome ou endereço..."
                value={search}
                onChange={(e) => setSearch(e.target.value)}
                className="pl-9"
              />
            </div>
          </div>
        </CardHeader>
        <CardContent>
          <div className="rounded-md border">
            <Table>
              <TableHeader>
                <TableRow>
                  <TableHead>Nome</TableHead>
                  <TableHead>Cliente</TableHead>
                  <TableHead>Endereço</TableHead>
                  <TableHead>Cidade</TableHead>
                  <TableHead>Estado</TableHead>
                  <TableHead>CEP</TableHead>
                  <TableHead>Escalas</TableHead>
                  <TableHead>Status</TableHead>
                  <TableHead className="w-[50px]"></TableHead>
                </TableRow>
              </TableHeader>
              <TableBody>
                {isLoading ? (
                  Array.from({ length: 5 }).map((_, i) => (
                    <TableRow key={i}>
                      <TableCell>
                        <Skeleton className="h-4 w-32" />
                      </TableCell>
                      <TableCell>
                        <Skeleton className="h-4 w-28" />
                      </TableCell>
                      <TableCell>
                        <Skeleton className="h-4 w-36" />
                      </TableCell>
                      <TableCell>
                        <Skeleton className="h-4 w-24" />
                      </TableCell>
                      <TableCell>
                        <Skeleton className="h-4 w-8" />
                      </TableCell>
                      <TableCell>
                        <Skeleton className="h-4 w-20" />
                      </TableCell>
                      <TableCell>
                        <Skeleton className="h-4 w-8" />
                      </TableCell>
                      <TableCell>
                        <Skeleton className="h-4 w-16" />
                      </TableCell>
                      <TableCell>
                        <Skeleton className="h-4 w-4" />
                      </TableCell>
                    </TableRow>
                  ))
                ) : data?.data.length === 0 ? (
                  <TableRow>
                    <TableCell colSpan={9} className="text-center py-8">
                      <MapPin className="mx-auto h-12 w-12 text-muted-foreground/20 mb-2" />
                      <p className="text-muted-foreground">Nenhum local encontrado</p>
                    </TableCell>
                  </TableRow>
                ) : (
                  data?.data.map((local) => (
                    <TableRow key={local.id}>
                      <TableCell className="font-medium">{local.nome}</TableCell>
                      <TableCell>
                        {local.cliente ? (
                          <div className="flex items-center gap-2">
                            <Building2 className="h-3 w-3 text-muted-foreground" />
                            <span className="text-sm">{local.cliente.nome}</span>
                          </div>
                        ) : (
                          "-"
                        )}
                      </TableCell>
                      <TableCell className="max-w-[200px] truncate" title={local.endereco}>
                        {local.endereco}
                      </TableCell>
                      <TableCell>{local.cidade || "-"}</TableCell>
                      <TableCell>{local.estado || "-"}</TableCell>
                      <TableCell className="font-mono text-sm">{formatCEP(local.cep || "")}</TableCell>
                      <TableCell>{local._count?.plantoes || 0}</TableCell>
                      <TableCell>
                        <Badge variant={local.ativo ? "default" : "secondary"}>
                          {local.ativo ? "Ativo" : "Inativo"}
                        </Badge>
                      </TableCell>
                      <TableCell>
                        <DropdownMenu>
                          <DropdownMenuTrigger asChild>
                            <Button variant="ghost" size="icon">
                              <MoreHorizontal className="h-4 w-4" />
                            </Button>
                          </DropdownMenuTrigger>
                          <DropdownMenuContent align="end">
                            <DropdownMenuItem onClick={() => navigate({ to: `/cadastros/locais/${local.uuid}` })}>
                              <Eye className="mr-2 h-4 w-4" />
                              Visualizar
                            </DropdownMenuItem>
                            <DropdownMenuItem
                              onClick={() =>
                                navigate({ to: `/cadastros/locais/${local.uuid}`, search: { action: "edit" } })
                              }
                            >
                              <Pencil className="mr-2 h-4 w-4" />
                              Editar
                            </DropdownMenuItem>
                            <DropdownMenuItem className="text-destructive" onClick={() => handleDelete(local.uuid)}>
                              <Trash2 className="mr-2 h-4 w-4" />
                              Excluir
                            </DropdownMenuItem>
                          </DropdownMenuContent>
                        </DropdownMenu>
                      </TableCell>
                    </TableRow>
                  ))
                )}
              </TableBody>
            </Table>
          </div>

          {data && data.meta?.totalPages && data.meta.totalPages > 1 && (
            <div className="flex items-center justify-between mt-4">
              <p className="text-sm text-muted-foreground">
                Mostrando {(page - 1) * limit + 1} a {Math.min(page * limit, data.meta?.total || 0)} de{" "}
                {data.meta?.total || 0} locais
              </p>
              <div className="flex gap-2">
                <Button variant="outline" size="sm" onClick={() => setPage(page - 1)} disabled={page === 1}>
                  Anterior
                </Button>
                <Button
                  variant="outline"
                  size="sm"
                  onClick={() => setPage(page + 1)}
                  disabled={page === data.meta?.totalPages}
                >
                  Próxima
                </Button>
              </div>
            </div>
          )}
        </CardContent>
      </Card>

      <DeleteConfirmDialog
        open={deleteDialogOpen}
        onOpenChange={setDeleteDialogOpen}
        onConfirm={confirmDelete}
        title="Confirmar Exclusão"
        description="Tem certeza que deseja excluir este local? Esta ação não pode ser desfeita."
      />
    </div>
  );
}

export const Route = createFileRoute("/cadastros/locais/")({
  component: LocaisIndex,
  beforeLoad: async () => {
    await requireAdminRole();
  },
});
