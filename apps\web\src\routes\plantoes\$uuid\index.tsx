import { use<PERSON><PERSON><PERSON>, useN<PERSON><PERSON>, <PERSON>, useSearch } from "@tanstack/react-router";
import { createFileRoute } from "@tanstack/react-router";
import { useQuery, useMutation, useQueryClient } from "@tanstack/react-query";
import { requireAdminRole } from "@/lib/route-guards";
import { useState, useEffect } from "react";
import { api } from "@/lib/api";
import { toast } from "sonner";
import { Button } from "@/components/ui/button";
import { Card, CardContent, CardHeader, CardTitle } from "@/components/ui/card";
import { Badge } from "@/components/ui/badge";
import { Label } from "@/components/ui/label";
import { Tabs, TabsContent, TabsList, TabsTrigger } from "@/components/ui/tabs";
import { Table, TableBody, TableCell, TableHead, TableHeader, TableRow } from "@/components/ui/table";
import { Alert, AlertDescription } from "@/components/ui/alert";
import {
  ArrowLeft,
  Edit2,
  Calendar,
  User,
  Building2,
  MapPin,
  DollarSign,
  Clock,
  CheckCircle2,
  CircleDashed,
  CheckCheck,
} from "lucide-react";
import { formatCurrency } from "@/lib/utils";
import {
  formatDate,
  createLocalDate,
  getCurrentISOString,
  getDaysInMonthForDate,
  parseISO,
  getMonth,
  getYear,
} from "@shared/date";
import type { Plantao } from "@/lib/api";
import { TabPresencas } from "@/components/tab-presencas";
import { CalendarioPlantao, type DiaPlantao } from "@/components/calendario-plantao";

const meses = [
  "Janeiro",
  "Fevereiro",
  "Março",
  "Abril",
  "Maio",
  "Junho",
  "Julho",
  "Agosto",
  "Setembro",
  "Outubro",
  "Novembro",
  "Dezembro",
];

function PlantaoDetails() {
  const { uuid } = useParams({ from: "/plantoes/$uuid/" });
  const navigate = useNavigate();

  const queryClient = useQueryClient();
  const searchParams = useSearch({ from: "/plantoes/$uuid/" });
  const [activeTab, setActiveTab] = useState("info");
  const [calendarioMes, setCalendarioMes] = useState<number>(0);
  const [calendarioAno, setCalendarioAno] = useState<number>(0);

  // Verifica se deve abrir em uma aba específica
  useEffect(() => {
    const tab = searchParams.tab;
    if (tab) {
      setActiveTab(tab);
    }
  }, [searchParams]);

  // Atualiza a URL quando a tab muda
  const handleTabChange = (newTab: string) => {
    setActiveTab(newTab);
    const newSearch = { ...searchParams };
    if (newTab !== "info") {
      newSearch.tab = newTab;
    } else {
      delete newSearch.tab;
    }
    navigate({ to: "/plantoes/$uuid", params: { uuid }, search: newSearch, replace: true });
  };

  const { data: plantao, isLoading } = useQuery({
    queryKey: ["plantao", uuid],
    queryFn: () => api.get<Plantao>(`/plantoes/${uuid}`),
  });

  const markAsCompletedMutation = useMutation({
    mutationFn: async (completed: boolean) => {
      return api.patch(`/plantoes/${uuid}`, {
        concluidoEm: completed ? getCurrentISOString() : null,
      });
    },
    onSuccess: (_, completed) => {
      queryClient.invalidateQueries({ queryKey: ["plantao", uuid] });
      queryClient.invalidateQueries({ queryKey: ["plantoes"] });
      toast.success(completed ? "Plantão marcado como concluído" : "Plantão reaberto");
    },
    onError: () => {
      toast.error("Erro ao atualizar status do plantão");
    },
  });

  // Inicializar mês e ano do calendário quando carregar o plantão
  useEffect(() => {
    if (plantao && calendarioMes === 0) {
      // Usar o primeiro mês do período (dataInicial) ao invés de plantao.mes/ano
      const dataInicial = plantao.dataInicial;
      setCalendarioMes(getMonth(dataInicial) + 1);
      setCalendarioAno(getYear(dataInicial));
    }
  }, [plantao]); // Removido calendarioMes das dependências para evitar loop infinito

  // Função para navegar entre meses no calendário
  const navigateCalendarioMonth = (direction: "prev" | "next") => {
    if (!plantao) return;

    const dataInicial = plantao.dataInicial;
    const dataFinal = plantao.dataFinal || createLocalDate(2099, 12, 31);

    if (direction === "prev") {
      let newDate = createLocalDate(calendarioAno, calendarioMes - 1, 1);
      if (newDate >= createLocalDate(getYear(dataInicial), getMonth(dataInicial) + 1, 1)) {
        setCalendarioMes(newDate.getMonth() + 1);
        setCalendarioAno(newDate.getFullYear());
      }
    } else {
      let newDate = createLocalDate(calendarioAno, calendarioMes + 1, 1);
      if (newDate <= createLocalDate(getYear(dataFinal), getMonth(dataFinal) + 1, 1)) {
        setCalendarioMes(newDate.getMonth() + 1);
        setCalendarioAno(newDate.getFullYear());
      }
    }
  };

  // Converter diasPlantao do formato da API para o formato do CalendarioPlantao
  const getDiasPlantaoFormatted = (): DiaPlantao[] => {
    if (!plantao || calendarioMes === 0) return [];

    const daysInMonth = getDaysInMonthForDate(calendarioAno, calendarioMes);
    const dias: DiaPlantao[] = [];

    for (let dia = 1; dia <= daysInMonth; dia++) {
      // Buscar o dia do plantão correspondente pelo campo 'data'
      const dataStr = `${calendarioAno}-${calendarioMes.toString().padStart(2, "0")}-${dia.toString().padStart(2, "0")}`;
      let diaPlantao = null;
      if (plantao.diasPlantao) {
        diaPlantao = plantao.diasPlantao.find((d) => {
          // Converter d.data para formato YYYY-MM-DD para comparação
          const dDataStr = formatDate(d.data, "yyyy-MM-dd");
          return dDataStr === dataStr;
        });
      }

      // Determinar se o dia está selecionado
      // Um dia está selecionado se existe um registro para este dia
      const isSelected = diaPlantao !== null;

      // Determinar o horário a ser usado
      let horario = undefined;
      if (diaPlantao) {
        // Se tem um registro do dia, usar os horários dele
        if (diaPlantao.horaEntrada && diaPlantao.horaSaida) {
          horario = {
            inicio: diaPlantao.horaEntrada,
            fim: diaPlantao.horaSaida,
            intervalo: diaPlantao.intervalo || "01:00",
          };
        }
        // Se o dia não tem horários mas o plantão tem horários padrão, usar eles
        else if (plantao.horaInicio && plantao.horaFim) {
          horario = {
            inicio: plantao.horaInicio,
            fim: plantao.horaFim,
            intervalo: plantao.intervalo || "01:00",
          };
        }
      }

      dias.push({
        data: dataStr,
        selecionado: isSelected,
        horario: horario,
      });
    }

    return dias;
  };

  if (isLoading) {
    return (
      <div className="flex items-center justify-center min-h-[400px]">
        <div className="text-muted-foreground">Carregando...</div>
      </div>
    );
  }

  if (!plantao) {
    return (
      <Alert>
        <AlertDescription>Plantão não encontrado</AlertDescription>
      </Alert>
    );
  }

  return (
    <div className="space-y-6">
      <div className="flex items-center justify-between">
        <div className="flex items-center gap-4">
          <Button variant="ghost" size="icon" onClick={() => navigate({ to: "/plantoes" })}>
            <ArrowLeft className="h-4 w-4" />
          </Button>
          <div>
            <h1 className="text-2xl font-bold">
              Plantão - {plantao.dataInicial ? meses[new Date(plantao.dataInicial).getMonth()] : ""}{" "}
              {plantao.dataInicial ? new Date(plantao.dataInicial).getFullYear() : ""}
            </h1>
            <p className="text-muted-foreground">{plantao.profissional?.usuario?.nome}</p>
          </div>
        </div>
        <div className="flex gap-2">
          {plantao.concluidoEm ? (
            <Button
              variant="outline"
              onClick={() => markAsCompletedMutation.mutate(false)}
              disabled={markAsCompletedMutation.isPending}
            >
              <CircleDashed className="h-4 w-4 mr-2" />
              Reabrir Plantão
            </Button>
          ) : (
            <Button
              variant="default"
              onClick={() => markAsCompletedMutation.mutate(true)}
              disabled={markAsCompletedMutation.isPending}
            >
              <CheckCheck className="h-4 w-4 mr-2" />
              Marcar como Concluído
            </Button>
          )}
          <Link to="/plantoes/$uuid/editar" params={{ uuid: plantao.uuid }}>
            <Button>
              <Edit2 className="h-4 w-4 mr-2" />
              Editar
            </Button>
          </Link>
        </div>
      </div>

      <Tabs value={activeTab} onValueChange={handleTabChange}>
        <TabsList>
          <TabsTrigger value="info">Informações</TabsTrigger>
          <TabsTrigger value="calendario">Calendário</TabsTrigger>
          <TabsTrigger value="presencas">Presença</TabsTrigger>
          <TabsTrigger value="fechamentos">Fechamentos</TabsTrigger>
        </TabsList>

        <TabsContent value="info" className="space-y-4">
          <div className="grid gap-4 md:grid-cols-2">
            <Card>
              <CardHeader>
                <CardTitle className="flex items-center gap-2">
                  <User className="h-5 w-5" />
                  Profissional
                </CardTitle>
              </CardHeader>
              <CardContent className="space-y-2">
                <div>
                  <Label>Nome</Label>
                  <p className="text-sm">{plantao.profissional?.usuario?.nome || "-"}</p>
                </div>
                <div>
                  <Label>CPF</Label>
                  <p className="text-sm">{plantao.profissional?.usuario?.cpf || "-"}</p>
                </div>
                <div>
                  <Label>Especialidade</Label>
                  <p className="text-sm">
                    {plantao.profissional?.especialidades
                      ?.map((e) => {
                        return e.especialidade.nome;
                      })
                      ?.join(", ") || "-"}
                  </p>
                </div>
              </CardContent>
            </Card>

            <Card>
              <CardHeader>
                <CardTitle className="flex items-center gap-2">
                  <Building2 className="h-5 w-5" />
                  Local de Atendimento
                </CardTitle>
              </CardHeader>
              <CardContent className="space-y-2">
                <div>
                  <Label>Cliente</Label>
                  <p className="text-sm">{plantao.cliente?.nome || "-"}</p>
                </div>
                <div>
                  <Label>Local</Label>
                  <p className="text-sm">{plantao.localAtendimento?.nome || "-"}</p>
                </div>
                <div>
                  <Label>Endereço</Label>
                  <p className="text-sm">{plantao.localAtendimento?.endereco || "-"}</p>
                </div>
                <div>
                  <Label>Cidade</Label>
                  <p className="text-sm">{plantao.localAtendimento?.cidade || "-"}</p>
                </div>
              </CardContent>
            </Card>
          </div>

          <Card>
            <CardHeader>
              <CardTitle className="flex items-center gap-2">
                <DollarSign className="h-5 w-5" />
                Informações Financeiras e Período
              </CardTitle>
            </CardHeader>
            <CardContent>
              <div className="grid grid-cols-2 md:grid-cols-4 gap-4">
                <div>
                  <Label>Modalidade de Trabalho</Label>
                  <div className="mt-1">
                    <Badge variant="outline">{plantao.modalidadeTrabalho}</Badge>
                  </div>
                </div>
                <div>
                  <Label>Tipo de Fechamento</Label>
                  <div className="mt-1">
                    <Badge variant="outline">{plantao.tipoFechamento}</Badge>
                  </div>
                </div>
                <div>
                  <Label>Data Inicial</Label>
                  <p className="text-sm">{formatDate(plantao.dataInicial)}</p>
                </div>
                <div>
                  <Label>Data Final</Label>
                  <p className="text-sm">{plantao.dataFinal ? formatDate(plantao.dataFinal) : "Em aberto"}</p>
                </div>
                <div>
                  <Label>Pagamento ({plantao.tipoValor})</Label>
                  <p className="text-sm font-medium">{formatCurrency(plantao.valorBase || 0)}</p>
                </div>
                {plantao.horaInicio && plantao.horaFim && (
                  <>
                    <div>
                      <Label>Hora Início</Label>
                      <p className="text-sm">{plantao.horaInicio}</p>
                    </div>
                    <div>
                      <Label>Hora Fim</Label>
                      <p className="text-sm">{plantao.horaFim}</p>
                    </div>
                  </>
                )}
                <div>
                  <Label>Status</Label>
                  <div className="mt-1 flex gap-2">
                    {plantao.concluidoEm ? (
                      <Badge variant="default" className="gap-1">
                        <CheckCircle2 className="h-3 w-3" />
                        Concluído
                      </Badge>
                    ) : (
                      <Badge variant="secondary" className="gap-1">
                        <CircleDashed className="h-3 w-3" />
                        Em andamento
                      </Badge>
                    )}
                  </div>
                </div>
                <div>
                  <Label>Dias Escalados</Label>
                  <p className="text-sm font-medium">{plantao.diasPlantao?.length || 0}</p>
                </div>
                {plantao.concluidoEm && (
                  <div>
                    <Label>Concluído em</Label>
                    <p className="text-sm">{formatDate(plantao.concluidoEm)}</p>
                  </div>
                )}
              </div>
            </CardContent>
          </Card>

          {plantao.observacoes && (
            <Card>
              <CardHeader>
                <CardTitle>Observações</CardTitle>
              </CardHeader>
              <CardContent>
                <p className="text-sm whitespace-pre-wrap">{plantao.observacoes}</p>
              </CardContent>
            </Card>
          )}
        </TabsContent>

        <TabsContent value="calendario">
          <Card>
            <CardHeader>
              <CardTitle className="flex items-center gap-2">
                <Calendar className="h-5 w-5" />
                Calendário do Plantão
              </CardTitle>
            </CardHeader>
            <CardContent>
              <CalendarioPlantao
                mes={calendarioMes}
                ano={calendarioAno}
                onMesChange={setCalendarioMes}
                onAnoChange={setCalendarioAno}
                diasPlantao={getDiasPlantaoFormatted()}
                tipoTurno={(plantao.tipoTurno as any) || "diurno"}
                dataInicial={plantao.dataInicial}
                dataFinal={plantao.dataFinal}
                navigateMonth={navigateCalendarioMonth}
                readOnly={true}
              />
              <div className="mt-4 p-4 bg-muted rounded-lg">
                <div className="grid grid-cols-2 md:grid-cols-4 gap-4 text-sm">
                  <div>
                    <span className="text-muted-foreground">Total de dias:</span>
                    <p className="font-semibold">{plantao.diasPlantao?.length || 0}</p>
                  </div>
                  <div>
                    <span className="text-muted-foreground">Tipo de Turno:</span>
                    <p className="font-semibold capitalize">{plantao.tipoTurno || "Não definido"}</p>
                  </div>
                  <div>
                    <span className="text-muted-foreground">Horário padrão:</span>
                    <p className="font-semibold">
                      {plantao.horaInicio && plantao.horaFim
                        ? `${plantao.horaInicio} - ${plantao.horaFim}`
                        : "Não definido"}
                    </p>
                  </div>
                  <div>
                    <span className="text-muted-foreground">Intervalo:</span>
                    <p className="font-semibold">{plantao.intervalo || "Não definido"}</p>
                  </div>
                </div>
              </div>
            </CardContent>
          </Card>
        </TabsContent>

        <TabsContent value="presencas">
          <TabPresencas plantao={plantao} calendarioMes={calendarioMes} calendarioAno={calendarioAno} />
        </TabsContent>

        <TabsContent value="fechamentos">
          <Card>
            <CardHeader className="flex flex-row items-center justify-between">
              <CardTitle>Fechamentos</CardTitle>
              <Button size="sm" asChild>
                <Link to="/fechamentos/novo">Novo Fechamento</Link>
              </Button>
            </CardHeader>
            <CardContent>
              {plantao.fechamentos && plantao.fechamentos.length > 0 ? (
                <Table>
                  <TableHeader>
                    <TableRow>
                      <TableHead>Mês/Ano</TableHead>
                      <TableHead>Dias Trabalhados</TableHead>
                      <TableHead>Valor Total</TableHead>
                      <TableHead>Status</TableHead>
                      <TableHead>Data Criação</TableHead>
                    </TableRow>
                  </TableHeader>
                  <TableBody>
                    {plantao.fechamentos.map((fechamento: any) => (
                      <TableRow key={fechamento.id}>
                        <TableCell className="font-medium">
                          {fechamento.mes ? String(fechamento.mes).padStart(2, "0") : ""}
                          {fechamento.mes && fechamento.ano ? "/" : ""}
                          {fechamento.ano || ""}
                        </TableCell>
                        <TableCell>{fechamento.diasTrabalhados}</TableCell>
                        <TableCell>{formatCurrency(fechamento.totalValor)}</TableCell>
                        <TableCell>
                          <Badge
                            variant={
                              fechamento.status === "APROVADO"
                                ? "default"
                                : fechamento.status === "PENDENTE"
                                  ? "secondary"
                                  : "destructive"
                            }
                          >
                            {fechamento.status}
                          </Badge>
                        </TableCell>
                        <TableCell>{formatDate(fechamento.createdAt)}</TableCell>
                      </TableRow>
                    ))}
                  </TableBody>
                </Table>
              ) : (
                <p className="text-muted-foreground text-center py-4">Nenhum fechamento encontrado</p>
              )}
            </CardContent>
          </Card>
        </TabsContent>
      </Tabs>
    </div>
  );
}

export const Route = createFileRoute("/plantoes/$uuid/")({
  component: PlantaoDetails,
  beforeLoad: async () => {
    await requireAdminRole();
  },
  validateSearch: (search: Record<string, unknown>): { tab?: string } => {
    return {
      tab: typeof search.tab === "string" ? search.tab : undefined,
    };
  },
});
