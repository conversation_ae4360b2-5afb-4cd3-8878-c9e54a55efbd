import type { FastifyTypedInstance } from "@/types";
import { criar<PERSON>lantaoRouter } from "./criar";
import { disponiveisAntecipacaoRouters } from "./disponiveis-antecipacao";
import { listarPlantaoRouter } from "./listar";
import { buscarPlantaoRouter } from "./buscar";
import { buscarPresencasRouter } from "./buscar-presencas";
import { atualizarPlantaoRouter } from "./atualizar";
import { deletarPlantaoRouter } from "./deletar";
import { estatisticasPlantaoRouter } from "./estatisticas";
import { replicarProxMesRouters } from "./replicar-prox-mes";

export async function plantaoRouter(fastify: FastifyTypedInstance) {
  listarPlantaoRouter(fastify);
  buscarPlantaoRouter(fastify);
  buscarPresencasRouter(fastify);
  criarPlantaoRouter(fastify);
  atualizarPlantaoRouter(fastify);
  deletarPlantaoRouter(fastify);
  estatisticasPlantaoRouter(fastify);
  replicarProxMesRouters(fastify);
  disponiveisAntecipacaoRouters(fastify);
}
