import { useEffect, useState } from "react";
import { useN<PERSON><PERSON>, usePara<PERSON>, useSearch } from "@tanstack/react-router";
import { useQuery, useMutation, useQueryClient } from "@tanstack/react-query";
import { useForm } from "@tanstack/react-form";
import { z } from "zod";
import { Save, ArrowLeft, Loader2, Edit2, Plus } from "lucide-react";
import { Link } from "@tanstack/react-router";
import { But<PERSON> } from "@/components/ui/button";
import { Input } from "@/components/ui/input";
import { Label } from "@/components/ui/label";
import { Switch } from "@/components/ui/switch";
import { Select, SelectContent, SelectItem, SelectTrigger, SelectValue } from "@/components/ui/select";
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from "@/components/ui/card";
import { Ta<PERSON>, <PERSON><PERSON><PERSON>ontent, <PERSON><PERSON><PERSON>ist, TabsTrigger } from "@/components/ui/tabs";
import { Table, TableBody, TableCell, TableHead, TableHeader, TableRow } from "@/components/ui/table";
import { toast } from "sonner";
import { api, type Cliente } from "@/lib/api";
import { formatCEP, formatCNPJ } from "@/lib/formatters";
import { CnpjInput } from "@/components/ui/cnpj-input";
import { PhoneInput } from "@/components/ui/phone-input";
import { PercentageInput } from "@/components/ui/percentage-input";
import { buscarCEP } from "@/lib/address-service";
import { ESTADOS_BRASILEIROS } from "@/lib/constants";

const clienteSchema = z.object({
  nome: z.string().min(1, "Nome é obrigatório"),
  cnpj: z
    .string()
    .min(1, "CNPJ é obrigatório")
    .refine((val) => {
      const numbers = val.replace(/\D/g, "");
      return numbers.length === 14;
    }, "CNPJ deve ter 14 dígitos"),
  email: z.string().email("Email inválido").optional().or(z.literal("")),
  telefone: z.string().optional(),
  taxaPadrao: z.number().min(0, "Taxa deve ser positiva").max(100, "Taxa deve ser menor que 100%"),

  // Endereço
  cep: z.string().optional(),
  logradouro: z.string().optional(),
  numero: z.string().optional(),
  complemento: z.string().optional(),
  bairro: z.string().optional(),
  cidade: z.string().optional(),
  uf: z.string().optional(),

  ativo: z.boolean(),
});

type ClienteFormData = z.infer<typeof clienteSchema>;

interface ClienteFormProps {
  viewMode?: boolean;
}

export function ClienteForm({ viewMode = false }: ClienteFormProps) {
  const navigate = useNavigate();
  const queryClient = useQueryClient();
  const [isViewMode, setIsViewMode] = useState(viewMode);

  // Always call hooks at the top level - no conditional calls
  const params = useParams({ strict: false });
  const searchParams = useSearch({ strict: false });

  // Extract clienteId safely
  const clienteId = params.clienteId;
  const isEditing = !!clienteId;
  const [activeTab, setActiveTab] = useState("dados");
  const [loadingCep, setLoadingCep] = useState(false);

  // Verifica se deve abrir em uma aba específica
  useEffect(() => {
    const tab = searchParams.tab;
    if (tab && ["dados", "endereco", "locais"].includes(tab)) {
      setActiveTab(tab);
    }
  }, [searchParams]);

  const handleCepSearch = async (cep: string) => {
    const cleanCep = cep.replace(/\D/g, "");
    if (cleanCep.length !== 8) return;

    setLoadingCep(true);
    try {
      const address = await buscarCEP(cleanCep);
      if (address) {
        form.setFieldValue("logradouro", address.logradouro);
        form.setFieldValue("bairro", address.bairro);
        form.setFieldValue("cidade", address.localidade);
        form.setFieldValue("uf", address.uf);
        toast.success("Endereço encontrado!");
      } else {
        toast.error("CEP não encontrado");
      }
    } catch (error) {
      toast.error("Erro ao buscar CEP");
    } finally {
      setLoadingCep(false);
    }
  };

  // Buscar cliente se estiver editando
  const {
    data: cliente,
    isLoading,
    error,
  } = useQuery({
    queryKey: ["cliente", clienteId],
    queryFn: async () => {
      if (!clienteId) return null;
      const result = await api.get<Cliente>(`/clientes/${clienteId}`);
      return result;
    },
    enabled: isEditing && !!clienteId,
  });

  // Mutation para criar/atualizar
  const saveMutation = useMutation({
    mutationFn: async (data: ClienteFormData) => {
      if (isEditing) {
        return await api.put(`/clientes/${clienteId}`, data);
      } else {
        return await api.post("/clientes", data);
      }
    },
    onSuccess: async () => {
      await queryClient.invalidateQueries({ queryKey: ["clientes"] });
      if (isEditing && clienteId) {
        await queryClient.invalidateQueries({ queryKey: ["cliente", clienteId] });
      }
      toast.success(`Cliente ${isEditing ? "atualizado" : "cadastrado"} com sucesso!`);
      navigate({ to: "/cadastros/clientes" });
    },
    onError: (error: any) => {
      toast.error(error.message || `Erro ao ${isEditing ? "atualizar" : "cadastrar"} cliente`);
    },
  });

  // Formulário
  const form = useForm({
    defaultValues: {
      nome: "",
      cnpj: "",
      email: "",
      telefone: "",
      taxaPadrao: 0,

      // Endereço
      cep: "",
      logradouro: "",
      numero: "",
      complemento: "",
      bairro: "",
      cidade: "",
      uf: "",

      ativo: true,
    } as ClienteFormData,
    onSubmit: async ({ value }) => {
      saveMutation.mutate(value);
    },
  });

  // Preencher formulário quando carregar cliente
  useEffect(() => {
    if (cliente) {
      form.setFieldValue("nome", cliente.nome || "");
      form.setFieldValue("cnpj", cliente.cnpj?.replace(/\D/g, "") || "");
      form.setFieldValue("email", cliente.email || "");
      form.setFieldValue("telefone", cliente.telefone?.replace(/\D/g, "") || "");
      form.setFieldValue("taxaPadrao", cliente.taxaPadrao || 0);

      // Endereço
      form.setFieldValue("cep", cliente.cep?.replace(/\D/g, "") || "");
      form.setFieldValue("logradouro", cliente.logradouro || "");
      form.setFieldValue("numero", cliente.numero || "");
      form.setFieldValue("complemento", cliente.complemento || "");
      form.setFieldValue("bairro", cliente.bairro || "");
      form.setFieldValue("cidade", cliente.cidade || "");
      form.setFieldValue("uf", cliente.uf || "");

      form.setFieldValue("ativo", cliente.ativo !== false);
    }
  }, [cliente, form]);

  if (isLoading) {
    return (
      <div className="flex items-center justify-center min-h-screen">
        <Loader2 className="h-8 w-8 animate-spin" />
      </div>
    );
  }

  if (error) {
    return (
      <div className="flex items-center justify-center min-h-screen">
        <div className="text-center">
          <p className="text-red-500 mb-4">Erro ao carregar cliente</p>
          <p className="text-sm text-muted-foreground mb-4">{(error as any)?.message || "Cliente não encontrado"}</p>
          <Button onClick={() => navigate({ to: "/cadastros/clientes" })}>Voltar para lista</Button>
        </div>
      </div>
    );
  }

  if (isEditing && !cliente && !isLoading) {
    return (
      <div className="flex items-center justify-center min-h-screen">
        <div className="text-center">
          <p className="text-yellow-500 mb-4">Cliente não encontrado</p>
          <Button onClick={() => navigate({ to: "/cadastros/clientes" })}>Voltar para lista</Button>
        </div>
      </div>
    );
  }

  return (
    <div className="space-y-6">
      <div className="flex items-center justify-between">
        <div className="flex items-center gap-4">
          <Link to="/cadastros/clientes">
            <Button variant="ghost" size="icon">
              <ArrowLeft className="h-4 w-4" />
            </Button>
          </Link>
          <div>
            <h1 className="text-3xl font-bold">
              {!isEditing ? "Novo Cliente" : isViewMode ? cliente?.nome || "Carregando..." : "Editar Cliente"}
            </h1>
            <p className="text-muted-foreground">
              {!isEditing
                ? "Cadastre um novo cliente ou instituição"
                : isViewMode
                  ? "Visualizando dados do cliente"
                  : "Atualize os dados do cliente"}
            </p>
          </div>
        </div>
        {isEditing && (
          <div className="flex gap-2">
            {isViewMode ? (
              <Button onClick={() => setIsViewMode(false)}>
                <Edit2 className="h-4 w-4 mr-2" />
                Editar
              </Button>
            ) : (
              <>
                <Button variant="outline" onClick={() => setIsViewMode(true)}>
                  Cancelar
                </Button>
                <form.Subscribe selector={(state) => [state.canSubmit, state.isSubmitting]}>
                  {([canSubmit, isSubmitting]) => (
                    <Button type="button" onClick={() => form.handleSubmit()} disabled={!canSubmit || isSubmitting}>
                      {isSubmitting ? (
                        <>
                          <Loader2 className="mr-2 h-4 w-4 animate-spin" />
                          Salvando...
                        </>
                      ) : (
                        <>
                          <Save className="mr-2 h-4 w-4" />
                          Salvar
                        </>
                      )}
                    </Button>
                  )}
                </form.Subscribe>
              </>
            )}
          </div>
        )}
      </div>

      <form
        onSubmit={(e) => {
          e.preventDefault();
          e.stopPropagation();
          form.handleSubmit();
        }}
      >
        <Tabs value={activeTab} onValueChange={setActiveTab} className="space-y-6">
          <TabsList className="grid w-full grid-cols-3 max-w-2xl">
            <TabsTrigger value="dados">Dados Cadastrais</TabsTrigger>
            <TabsTrigger value="endereco">Endereço</TabsTrigger>
            <TabsTrigger value="locais" disabled={!isEditing}>
              Locais de Atendimento
            </TabsTrigger>
          </TabsList>

          <TabsContent value="dados">
            <Card>
              <CardHeader>
                <CardTitle>DADOS CADASTRAIS</CardTitle>
                <CardDescription>Informações básicas do cliente</CardDescription>
              </CardHeader>
              <CardContent className="space-y-6">
                <div className="grid gap-4 md:grid-cols-2">
                  <form.Field
                    name="nome"
                    validators={{
                      onChange: ({ value }) => (!value ? "Nome é obrigatório" : undefined),
                    }}
                  >
                    {(field) => (
                      <div className="space-y-2">
                        <Label htmlFor={field.name}>Nome *</Label>
                        <Input
                          id={field.name}
                          value={field.state.value}
                          onBlur={field.handleBlur}
                          onChange={(e) => field.handleChange(e.target.value)}
                          placeholder="Nome da empresa ou instituição"
                          disabled={isViewMode}
                        />
                        {field.state.meta.errors && (
                          <p className="text-sm text-destructive">{field.state.meta.errors.join(", ")}</p>
                        )}
                      </div>
                    )}
                  </form.Field>

                  <form.Field
                    name="cnpj"
                    validators={{
                      onChange: ({ value }) => {
                        if (!value) return "CNPJ é obrigatório";
                        const numbers = value.replace(/\D/g, "");
                        if (numbers.length !== 14) {
                          return "CNPJ deve ter 14 dígitos";
                        }
                        return undefined;
                      },
                    }}
                  >
                    {(field) => (
                      <CnpjInput
                        id={field.name}
                        label="CNPJ"
                        required
                        value={field.state.value || ""}
                        onBlur={field.handleBlur}
                        onChange={field.handleChange}
                        error={field.state.meta.errors?.join(", ")}
                        disabled={isViewMode}
                      />
                    )}
                  </form.Field>

                  <form.Field name="email">
                    {(field) => (
                      <div className="space-y-2">
                        <Label htmlFor={field.name}>Email</Label>
                        <Input
                          id={field.name}
                          type="email"
                          value={field.state.value || ""}
                          onBlur={field.handleBlur}
                          onChange={(e) => field.handleChange(e.target.value)}
                          placeholder="<EMAIL>"
                          disabled={isViewMode}
                        />
                        {field.state.meta.errors && (
                          <p className="text-sm text-destructive">{field.state.meta.errors.join(", ")}</p>
                        )}
                      </div>
                    )}
                  </form.Field>

                  <form.Field name="telefone">
                    {(field) => (
                      <PhoneInput
                        id={field.name}
                        label="Telefone"
                        value={field.state.value || ""}
                        onBlur={field.handleBlur}
                        onChange={field.handleChange}
                        disabled={isViewMode}
                      />
                    )}
                  </form.Field>

                  <form.Field name="taxaPadrao">
                    {(field) => (
                      <PercentageInput
                        value={field.state.value}
                        onChange={(value) => field.handleChange(value ?? 0)}
                        label="Taxa Padrão"
                        disabled={isViewMode}
                      />
                    )}
                  </form.Field>

                  {isEditing && (
                    <form.Field name="ativo">
                      {(field) => (
                        <div className="flex items-center space-x-2">
                          <Switch
                            id="ativo"
                            checked={field.state.value}
                            onCheckedChange={field.handleChange}
                            disabled={isViewMode}
                          />
                          <Label htmlFor="ativo" className="cursor-pointer">
                            Cliente ativo
                          </Label>
                        </div>
                      )}
                    </form.Field>
                  )}
                </div>

                {!isViewMode && (
                  <div className="flex justify-end gap-4 mt-6">
                    <Link to="/cadastros/clientes">
                      <Button type="button" variant="outline">
                        Cancelar
                      </Button>
                    </Link>
                    {!isEditing ? (
                      <form.Subscribe selector={(state) => [state.canSubmit, state.isSubmitting]}>
                        {([canSubmit, isSubmitting]) => (
                          <Button type="submit" disabled={!canSubmit || isSubmitting}>
                            {isSubmitting ? (
                              <>
                                <Loader2 className="mr-2 h-4 w-4 animate-spin" />
                                Cadastrando...
                              </>
                            ) : (
                              <>
                                <Save className="mr-2 h-4 w-4" />
                                Cadastrar Cliente
                              </>
                            )}
                          </Button>
                        )}
                      </form.Subscribe>
                    ) : (
                      <Button type="button" onClick={() => setActiveTab("endereco")}>
                        Próximo
                      </Button>
                    )}
                  </div>
                )}
              </CardContent>
            </Card>
          </TabsContent>

          <TabsContent value="endereco">
            <Card>
              <CardHeader>
                <CardTitle>ENDEREÇO</CardTitle>
                <CardDescription>Endereço da sede ou matriz do cliente</CardDescription>
              </CardHeader>
              <CardContent className="space-y-6">
                <div className="grid gap-4 md:grid-cols-2">
                  <form.Field name="cep">
                    {(field) => (
                      <div className="space-y-2">
                        <Label htmlFor={field.name}>CEP</Label>
                        <div className="relative">
                          <Input
                            id={field.name}
                            value={formatCEP(field.state.value || "")}
                            onBlur={(e) => {
                              field.handleBlur();
                              handleCepSearch(e.target.value);
                            }}
                            onChange={(e) => field.handleChange(e.target.value.replace(/\D/g, ""))}
                            placeholder="00000-000"
                            disabled={loadingCep || isViewMode}
                          />
                          {loadingCep && (
                            <Loader2 className="absolute right-2 top-2.5 h-4 w-4 animate-spin text-muted-foreground" />
                          )}
                        </div>
                      </div>
                    )}
                  </form.Field>

                  <form.Field name="uf">
                    {(field) => (
                      <div className="space-y-2">
                        <Label htmlFor={field.name}>UF</Label>
                        <Select
                          value={field.state.value || ""}
                          onValueChange={field.handleChange}
                          disabled={isViewMode}
                        >
                          <SelectTrigger>
                            <SelectValue placeholder="Selecione o estado" />
                          </SelectTrigger>
                          <SelectContent>
                            {ESTADOS_BRASILEIROS.map((estado) => (
                              <SelectItem key={estado.value} value={estado.value}>
                                {estado.label}
                              </SelectItem>
                            ))}
                          </SelectContent>
                        </Select>
                      </div>
                    )}
                  </form.Field>

                  <form.Field name="cidade">
                    {(field) => (
                      <div className="space-y-2">
                        <Label htmlFor={field.name}>Cidade</Label>
                        <Input
                          id={field.name}
                          value={field.state.value || ""}
                          onBlur={field.handleBlur}
                          onChange={(e) => field.handleChange(e.target.value)}
                          placeholder="Nome da cidade"
                          disabled={isViewMode}
                        />
                      </div>
                    )}
                  </form.Field>

                  <form.Field name="bairro">
                    {(field) => (
                      <div className="space-y-2">
                        <Label htmlFor={field.name}>Bairro</Label>
                        <Input
                          id={field.name}
                          value={field.state.value || ""}
                          onBlur={field.handleBlur}
                          onChange={(e) => field.handleChange(e.target.value)}
                          placeholder="Nome do bairro"
                          disabled={isViewMode}
                        />
                      </div>
                    )}
                  </form.Field>

                  <form.Field name="logradouro">
                    {(field) => (
                      <div className="space-y-2 md:col-span-1">
                        <Label htmlFor={field.name}>Logradouro</Label>
                        <Input
                          id={field.name}
                          value={field.state.value || ""}
                          onBlur={field.handleBlur}
                          onChange={(e) => field.handleChange(e.target.value)}
                          placeholder="Rua, Avenida, etc."
                          disabled={isViewMode}
                        />
                      </div>
                    )}
                  </form.Field>

                  <div className="grid grid-cols-2 gap-4">
                    <form.Field name="numero">
                      {(field) => (
                        <div className="space-y-2">
                          <Label htmlFor={field.name}>Número</Label>
                          <Input
                            id={field.name}
                            value={field.state.value || ""}
                            onBlur={field.handleBlur}
                            onChange={(e) => field.handleChange(e.target.value)}
                            placeholder="123"
                            disabled={isViewMode}
                          />
                        </div>
                      )}
                    </form.Field>

                    <form.Field name="complemento">
                      {(field) => (
                        <div className="space-y-2">
                          <Label htmlFor={field.name}>Complemento</Label>
                          <Input
                            id={field.name}
                            value={field.state.value || ""}
                            onBlur={field.handleBlur}
                            onChange={(e) => field.handleChange(e.target.value)}
                            placeholder="Sala, Andar, etc."
                            disabled={isViewMode}
                          />
                        </div>
                      )}
                    </form.Field>
                  </div>
                </div>

                {!isViewMode && (
                  <div className="flex justify-between gap-4 mt-6">
                    <Button type="button" variant="outline" onClick={() => setActiveTab("dados")}>
                      Anterior
                    </Button>
                    <div className="flex gap-4">
                      <Link to="/cadastros/clientes">
                        <Button type="button" variant="outline">
                          Cancelar
                        </Button>
                      </Link>
                      {isEditing ? (
                        <Button type="button" onClick={() => setActiveTab("locais")}>
                          Próximo
                        </Button>
                      ) : (
                        <form.Subscribe selector={(state) => [state.canSubmit, state.isSubmitting]}>
                          {([canSubmit, isSubmitting]) => (
                            <Button type="submit" disabled={!canSubmit || isSubmitting}>
                              {isSubmitting ? (
                                <>
                                  <Loader2 className="mr-2 h-4 w-4 animate-spin" />
                                  Cadastrando...
                                </>
                              ) : (
                                <>
                                  <Save className="mr-2 h-4 w-4" />
                                  Cadastrar Cliente
                                </>
                              )}
                            </Button>
                          )}
                        </form.Subscribe>
                      )}
                    </div>
                  </div>
                )}
              </CardContent>
            </Card>
          </TabsContent>

          <TabsContent value="locais">
            <Card>
              <CardHeader className="flex flex-row items-center justify-between">
                <div>
                  <CardTitle>LOCAIS DE ATENDIMENTO</CardTitle>
                  <CardDescription>Gerencie os locais de atendimento deste cliente</CardDescription>
                </div>
                {!isViewMode && (
                  <Button size="sm" asChild disabled={isViewMode}>
                    <Link to={"/cadastros/clientes/$clienteId/locais/novo"} params={{ clienteId: clienteId! }}>
                      <Plus className="h-4 w-4 mr-2" />
                      Novo Local
                    </Link>
                  </Button>
                )}
              </CardHeader>
              <CardContent>
                {cliente?.locaisAtendimento && cliente.locaisAtendimento.length > 0 ? (
                  <Table>
                    <TableHeader>
                      <TableRow>
                        <TableHead>Nome</TableHead>
                        <TableHead>Endereço</TableHead>
                        <TableHead>Cidade</TableHead>
                        {!isViewMode && <TableHead className="text-right">Ações</TableHead>}
                      </TableRow>
                    </TableHeader>
                    <TableBody>
                      {cliente.locaisAtendimento.map((local) => (
                        <TableRow key={local.id}>
                          <TableCell className="font-medium">{local.nome}</TableCell>
                          <TableCell>{local.endereco}</TableCell>
                          <TableCell>{local.cidade}</TableCell>
                          {!isViewMode && (
                            <TableCell className="text-right">
                              <Button variant="ghost" size="sm" asChild disabled={isViewMode}>
                                <Link
                                  to={"/cadastros/clientes/$clienteId/locais/$localId/editar"}
                                  params={{ clienteId: clienteId!, localId: local.uuid }}
                                >
                                  <Edit2 className="h-4 w-4" />
                                </Link>
                              </Button>
                            </TableCell>
                          )}
                        </TableRow>
                      ))}
                    </TableBody>
                  </Table>
                ) : (
                  <p className="text-muted-foreground text-center py-8">Nenhum local de atendimento cadastrado</p>
                )}

                {!isViewMode && (
                  <div className="flex justify-between gap-4 mt-6">
                    <Button type="button" variant="outline" onClick={() => setActiveTab("endereco")}>
                      Anterior
                    </Button>
                    <div className="flex gap-4">
                      <Link to="/cadastros/clientes">
                        <Button type="button" variant="outline">
                          Cancelar
                        </Button>
                      </Link>
                      <form.Subscribe selector={(state) => [state.canSubmit, state.isSubmitting]}>
                        {([canSubmit, isSubmitting]) => (
                          <Button type="submit" disabled={!canSubmit || isSubmitting}>
                            {isSubmitting ? (
                              <>
                                <Loader2 className="mr-2 h-4 w-4 animate-spin" />
                                Atualizando...
                              </>
                            ) : (
                              <>
                                <Save className="mr-2 h-4 w-4" />
                                Atualizar Cliente
                              </>
                            )}
                          </Button>
                        )}
                      </form.Subscribe>
                    </div>
                  </div>
                )}
              </CardContent>
            </Card>
          </TabsContent>
        </Tabs>
      </form>
    </div>
  );
}
