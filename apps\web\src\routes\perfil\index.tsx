import { useState, useEffect } from "react";
import { useForm } from "@tanstack/react-form";
import { useMutation, useQuery } from "@tanstack/react-query";
import { toast } from "sonner";
import { Loader2, User, Mail, Phone, Calendar, MapPin, Key, Save, Eye, EyeOff } from "lucide-react";
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from "@/components/ui/card";
import { Button } from "@/components/ui/button";
import { Input } from "@/components/ui/input";
import { CpfInput } from "@/components/ui/cpf-input";
import { PhoneInput } from "@/components/ui/phone-input";
import { Label } from "@/components/ui/label";
import { Tabs, TabsContent, TabsList, TabsTrigger } from "@/components/ui/tabs";
import { Select, SelectContent, SelectItem, SelectTrigger, SelectValue } from "@/components/ui/select";
import { Separator } from "@/components/ui/separator";
import { Al<PERSON>, AlertDescription } from "@/components/ui/alert";
import { api, type Usuario } from "@/lib/api";
import { useAuthStore } from "@/stores/use-auth.store";
import { formatDateForInput } from "@/lib/utils";
import { createFileRoute } from "@tanstack/react-router";

type PerfilFormData = {
  nome: string;
  cpf: string;
  telefone: string;
  dataNascimento?: string;
  genero?: string;
  estadoCivil?: string;
  nacionalidade?: string;
  cep: string;
  logradouro: string;
  numero: string;
  complemento?: string;
  bairro: string;
  cidade: string;
  uf: string;
};

type SenhaFormData = {
  senhaAtual: string;
  novaSenha: string;
  confirmarSenha: string;
};

function PerfilPage() {
  const user = useAuthStore((state) => state.user);
  const updateUser = useAuthStore((state) => state.updateUser);
  const [showSenhaAtual, setShowSenhaAtual] = useState(false);
  const [showNovaSenha, setShowNovaSenha] = useState(false);
  const [showConfirmarSenha, setShowConfirmarSenha] = useState(false);

  const { data: userData, isLoading } = useQuery({
    queryKey: ["perfil"],
    queryFn: async () => {
      const response = await api.get<Usuario>("/usuarios/me");
      return response;
    },
  });

  const updatePerfilMutation = useMutation({
    mutationFn: async (data: PerfilFormData) => {
      const response = await api.put<Usuario>("/usuarios/me", data);
      return response;
    },
    onSuccess: (data) => {
      toast.success("Perfil atualizado com sucesso!");
      // Convert Usuario to match User interface from auth store
      updateUser({
        ...user!,
        nome: data.nome,
        cpf: data.cpf,
        telefone: data.telefone || "",
        dataNascimento: data.dataNascimento ? new Date(data.dataNascimento) : null,
        genero: data.genero || "",
        estadoCivil: data.estadoCivil || "",
        nacionalidade: data.nacionalidade || "",
        cep: data.cep || "",
        logradouro: data.logradouro || "",
        numero: data.numero || "",
        complemento: data.complemento || "",
        bairro: data.bairro || "",
        cidade: data.cidade || "",
        uf: data.uf || "",
      });
    },
    onError: (error: any) => {
      toast.error(error.response?.data?.error || "Erro ao atualizar perfil");
    },
  });

  const updateSenhaMutation = useMutation({
    mutationFn: async (data: { senhaAtual: string; novaSenha: string }) => {
      await api.put("/usuarios/me/senha", data);
    },
    onSuccess: () => {
      toast.success("Senha alterada com sucesso!");
      senhaForm.reset();
    },
    onError: (error: any) => {
      toast.error(error.response?.data?.error || "Erro ao alterar senha");
    },
  });

  const perfilForm = useForm({
    defaultValues: {
      nome: "",
      cpf: "",
      telefone: "",
      dataNascimento: "",
      genero: "",
      estadoCivil: "",
      nacionalidade: "",
      cep: "",
      logradouro: "",
      numero: "",
      complemento: "",
      bairro: "",
      cidade: "",
      uf: "",
    } as PerfilFormData,
    onSubmit: async ({ value }) => {
      updatePerfilMutation.mutate(value);
    },
  });

  const senhaForm = useForm({
    defaultValues: {
      senhaAtual: "",
      novaSenha: "",
      confirmarSenha: "",
    } as SenhaFormData,
    onSubmit: async ({ value }) => {
      console.log({
        value,
      });
      if (value.novaSenha !== value.confirmarSenha) {
        toast.error("As senhas não coincidem");
        return;
      }
      if (value.novaSenha.length < 6) {
        toast.error("A nova senha deve ter pelo menos 6 caracteres");
        return;
      }
      if (value.senhaAtual === value.novaSenha) {
        toast.error("A nova senha deve ser diferente da senha atual");
        return;
      }
      updateSenhaMutation.mutate({
        senhaAtual: value.senhaAtual,
        novaSenha: value.novaSenha,
      });
    },
  });

  useEffect(() => {
    if (userData && userData.cpf && !isLoading) {
      setTimeout(() => {
        perfilForm.setFieldValue("nome", userData.nome || "");
        perfilForm.setFieldValue("cpf", userData.cpf || "");
        perfilForm.setFieldValue("telefone", userData.telefone || "");
        perfilForm.setFieldValue(
          "dataNascimento",
          userData.dataNascimento ? formatDateForInput(new Date(userData.dataNascimento)) : ""
        );

        perfilForm.setFieldValue("genero", userData.genero || "");
        perfilForm.setFieldValue("estadoCivil", userData.estadoCivil || "");
        perfilForm.setFieldValue("nacionalidade", userData.nacionalidade || "");
        perfilForm.setFieldValue("cep", userData.cep || "");
        perfilForm.setFieldValue("logradouro", userData.logradouro || "");
        perfilForm.setFieldValue("numero", userData.numero || "");
        perfilForm.setFieldValue("complemento", userData.complemento || "");
        perfilForm.setFieldValue("bairro", userData.bairro || "");
        perfilForm.setFieldValue("cidade", userData.cidade || "");
        perfilForm.setFieldValue("uf", userData.uf || "");
      }, 250);
    }
  }, [userData]);

  const buscarCep = async (cep: string) => {
    const cepLimpo = cep.replace(/\D/g, "");
    if (cepLimpo.length !== 8) return;

    try {
      const response = await fetch(`https://viacep.com.br/ws/${cepLimpo}/json/`);
      const data = await response.json();

      if (!data.erro) {
        perfilForm.setFieldValue("logradouro", data.logradouro || "");
        perfilForm.setFieldValue("bairro", data.bairro || "");
        perfilForm.setFieldValue("cidade", data.localidade || "");
        perfilForm.setFieldValue("uf", data.uf || "");
      }
    } catch (error) {
      console.error("Erro ao buscar CEP:", error);
    }
  };

  if (isLoading) {
    return (
      <div className="flex items-center justify-center h-full">
        <Loader2 className="h-8 w-8 animate-spin" />
      </div>
    );
  }

  return (
    <div className="space-y-6">
      <div>
        <h1 className="text-3xl font-bold tracking-tight">Meu Perfil</h1>
        <p className="text-muted-foreground">Gerencie suas informações pessoais e configurações de conta</p>
      </div>

      <Tabs defaultValue="dados-pessoais" className="space-y-4">
        <TabsList>
          <TabsTrigger value="dados-pessoais">Dados Pessoais</TabsTrigger>
          <TabsTrigger value="seguranca">Segurança</TabsTrigger>
        </TabsList>

        <TabsContent value="dados-pessoais">
          <Card>
            <CardHeader>
              <CardTitle className="flex items-center gap-2">
                <User className="h-5 w-5" />
                Informações Pessoais
              </CardTitle>
              <CardDescription>Atualize suas informações pessoais e de contato</CardDescription>
            </CardHeader>
            <CardContent>
              <form
                onSubmit={(e) => {
                  e.preventDefault();
                  e.stopPropagation();
                  perfilForm.handleSubmit();
                }}
                className="space-y-6"
              >
                <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
                  <perfilForm.Field name="nome">
                    {(field) => (
                      <div className="space-y-2">
                        <Label htmlFor="nome">Nome Completo</Label>
                        <Input
                          id="nome"
                          value={field.state.value}
                          onChange={(e) => field.handleChange(e.target.value)}
                        />
                        {field.state.meta.errors && (
                          <p className="text-sm text-destructive">{field.state.meta.errors.join(", ")}</p>
                        )}
                      </div>
                    )}
                  </perfilForm.Field>

                  <perfilForm.Field name="cpf">
                    {(field) => (
                      <CpfInput id="cpf" label="CPF" value={field.state.value || ""} onChange={field.handleChange} />
                    )}
                  </perfilForm.Field>

                  <perfilForm.Field name="telefone">
                    {(field) => (
                      <PhoneInput
                        id="telefone"
                        label="Telefone"
                        value={field.state.value || ""}
                        onChange={field.handleChange}
                      />
                    )}
                  </perfilForm.Field>

                  <perfilForm.Field name="dataNascimento">
                    {(field) => (
                      <div className="space-y-2">
                        <Label htmlFor="dataNascimento">Data de Nascimento</Label>
                        <Input
                          id="dataNascimento"
                          type="date"
                          value={field.state.value}
                          onChange={(e) => field.handleChange(e.target.value)}
                        />
                      </div>
                    )}
                  </perfilForm.Field>

                  <perfilForm.Field name="genero">
                    {(field) => (
                      <div className="space-y-2">
                        <Label htmlFor="genero">Gênero</Label>
                        <Select value={field.state.value} onValueChange={(value) => field.handleChange(value)}>
                          <SelectTrigger>
                            <SelectValue placeholder="Selecione o gênero" />
                          </SelectTrigger>
                          <SelectContent>
                            <SelectItem value="MASCULINO">Masculino</SelectItem>
                            <SelectItem value="FEMININO">Feminino</SelectItem>
                            <SelectItem value="OUTRO">Outro</SelectItem>
                          </SelectContent>
                        </Select>
                      </div>
                    )}
                  </perfilForm.Field>

                  <perfilForm.Field name="estadoCivil">
                    {(field) => (
                      <div className="space-y-2">
                        <Label htmlFor="estadoCivil">Estado Civil</Label>
                        <Select value={field.state.value} onValueChange={(value) => field.handleChange(value)}>
                          <SelectTrigger>
                            <SelectValue placeholder="Selecione o estado civil" />
                          </SelectTrigger>
                          <SelectContent>
                            <SelectItem value="SOLTEIRO">Solteiro(a)</SelectItem>
                            <SelectItem value="CASADO">Casado(a)</SelectItem>
                            <SelectItem value="DIVORCIADO">Divorciado(a)</SelectItem>
                            <SelectItem value="VIUVO">Viúvo(a)</SelectItem>
                          </SelectContent>
                        </Select>
                      </div>
                    )}
                  </perfilForm.Field>
                </div>

                <Separator />

                <div>
                  <h3 className="text-lg font-semibold mb-4 flex items-center gap-2">
                    <MapPin className="h-4 w-4" />
                    Endereço
                  </h3>
                  <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
                    <perfilForm.Field name="cep">
                      {(field) => (
                        <div className="space-y-2">
                          <Label htmlFor="cep">CEP</Label>
                          <Input
                            id="cep"
                            value={field.state.value}
                            onChange={(e) => field.handleChange(e.target.value)}
                            onBlur={(e) => buscarCep(e.target.value)}
                            maxLength={9}
                          />
                        </div>
                      )}
                    </perfilForm.Field>

                    <perfilForm.Field name="logradouro">
                      {(field) => (
                        <div className="space-y-2">
                          <Label htmlFor="logradouro">Logradouro</Label>
                          <Input
                            id="logradouro"
                            value={field.state.value}
                            onChange={(e) => field.handleChange(e.target.value)}
                          />
                        </div>
                      )}
                    </perfilForm.Field>

                    <perfilForm.Field name="numero">
                      {(field) => (
                        <div className="space-y-2">
                          <Label htmlFor="numero">Número</Label>
                          <Input
                            id="numero"
                            value={field.state.value}
                            onChange={(e) => field.handleChange(e.target.value)}
                          />
                        </div>
                      )}
                    </perfilForm.Field>

                    <perfilForm.Field name="complemento">
                      {(field) => (
                        <div className="space-y-2">
                          <Label htmlFor="complemento">Complemento</Label>
                          <Input
                            id="complemento"
                            value={field.state.value || ""}
                            onChange={(e) => field.handleChange(e.target.value)}
                          />
                        </div>
                      )}
                    </perfilForm.Field>

                    <perfilForm.Field name="bairro">
                      {(field) => (
                        <div className="space-y-2">
                          <Label htmlFor="bairro">Bairro</Label>
                          <Input
                            id="bairro"
                            value={field.state.value}
                            onChange={(e) => field.handleChange(e.target.value)}
                          />
                        </div>
                      )}
                    </perfilForm.Field>

                    <perfilForm.Field name="cidade">
                      {(field) => (
                        <div className="space-y-2">
                          <Label htmlFor="cidade">Cidade</Label>
                          <Input
                            id="cidade"
                            value={field.state.value}
                            onChange={(e) => field.handleChange(e.target.value)}
                          />
                        </div>
                      )}
                    </perfilForm.Field>

                    <perfilForm.Field name="uf">
                      {(field) => (
                        <div className="space-y-2">
                          <Label htmlFor="uf">UF</Label>
                          <Input
                            id="uf"
                            value={field.state.value}
                            onChange={(e) => field.handleChange(e.target.value)}
                            maxLength={2}
                          />
                        </div>
                      )}
                    </perfilForm.Field>
                  </div>
                </div>

                <div className="flex justify-end">
                  <Button type="submit" disabled={updatePerfilMutation.isPending}>
                    {updatePerfilMutation.isPending ? (
                      <>
                        <Loader2 className="mr-2 h-4 w-4 animate-spin" />
                        Salvando...
                      </>
                    ) : (
                      <>
                        <Save className="mr-2 h-4 w-4" />
                        Salvar Alterações
                      </>
                    )}
                  </Button>
                </div>
              </form>
            </CardContent>
          </Card>
        </TabsContent>

        <TabsContent value="seguranca">
          <Card>
            <CardHeader>
              <CardTitle className="flex items-center gap-2">
                <Key className="h-5 w-5" />
                Alterar Senha
              </CardTitle>
              <CardDescription>Mantenha sua conta segura alterando sua senha periodicamente</CardDescription>
            </CardHeader>
            <CardContent>
              <form
                onSubmit={(e) => {
                  e.preventDefault();
                  e.stopPropagation();
                  senhaForm.handleSubmit();
                }}
                className="space-y-4"
              >
                <senhaForm.Field name="senhaAtual">
                  {(field) => (
                    <div className="space-y-2">
                      <Label htmlFor="senhaAtual">Senha Atual</Label>
                      <div className="relative">
                        <Input
                          id="senhaAtual"
                          type={showSenhaAtual ? "text" : "password"}
                          value={field.state.value}
                          onChange={(e) => field.handleChange(e.target.value)}
                          className="pr-10"
                        />
                        <button
                          type="button"
                          onClick={() => setShowSenhaAtual(!showSenhaAtual)}
                          className="absolute right-2 top-1/2 -translate-y-1/2 text-muted-foreground hover:text-foreground"
                          tabIndex={-1}
                        >
                          {showSenhaAtual ? <EyeOff className="h-4 w-4" /> : <Eye className="h-4 w-4" />}
                        </button>
                      </div>
                    </div>
                  )}
                </senhaForm.Field>

                <senhaForm.Field name="novaSenha">
                  {(field) => (
                    <div className="space-y-2">
                      <Label htmlFor="novaSenha">Nova Senha</Label>
                      <div className="relative">
                        <Input
                          id="novaSenha"
                          type={showNovaSenha ? "text" : "password"}
                          value={field.state.value}
                          onChange={(e) => field.handleChange(e.target.value)}
                          className="pr-10"
                        />
                        <button
                          type="button"
                          onClick={() => setShowNovaSenha(!showNovaSenha)}
                          className="absolute right-2 top-1/2 -translate-y-1/2 text-muted-foreground hover:text-foreground"
                          tabIndex={-1}
                        >
                          {showNovaSenha ? <EyeOff className="h-4 w-4" /> : <Eye className="h-4 w-4" />}
                        </button>
                      </div>
                      <p className="text-xs text-muted-foreground">Mínimo de 6 caracteres</p>
                    </div>
                  )}
                </senhaForm.Field>

                <senhaForm.Field name="confirmarSenha">
                  {(field) => (
                    <div className="space-y-2">
                      <Label htmlFor="confirmarSenha">Confirmar Nova Senha</Label>
                      <div className="relative">
                        <Input
                          id="confirmarSenha"
                          type={showConfirmarSenha ? "text" : "password"}
                          value={field.state.value}
                          onChange={(e) => field.handleChange(e.target.value)}
                          className="pr-10"
                        />
                        <button
                          type="button"
                          onClick={() => setShowConfirmarSenha(!showConfirmarSenha)}
                          className="absolute right-2 top-1/2 -translate-y-1/2 text-muted-foreground hover:text-foreground"
                          tabIndex={-1}
                        >
                          {showConfirmarSenha ? <EyeOff className="h-4 w-4" /> : <Eye className="h-4 w-4" />}
                        </button>
                      </div>
                    </div>
                  )}
                </senhaForm.Field>

                <div className="flex justify-end">
                  <Button type="submit" disabled={updateSenhaMutation.isPending}>
                    {updateSenhaMutation.isPending ? (
                      <>
                        <Loader2 className="mr-2 h-4 w-4 animate-spin" />
                        Alterando...
                      </>
                    ) : (
                      <>
                        <Key className="mr-2 h-4 w-4" />
                        Alterar Senha
                      </>
                    )}
                  </Button>
                </div>
              </form>
            </CardContent>
          </Card>
        </TabsContent>
      </Tabs>
    </div>
  );
}

export const Route = createFileRoute("/perfil/")({
  component: PerfilPage,
});
