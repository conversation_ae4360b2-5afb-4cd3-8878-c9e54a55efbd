
/* !!! This is code generated by Prisma. Do not edit directly. !!! */
/* eslint-disable */
// @ts-nocheck 
/*
 * This file exports the `UsuarioCliente` model and its related types.
 *
 * 🟢 You can import this file directly.
 */
import * as runtime from "@prisma/client/runtime/library"
import type * as $Enums from "../enums.ts"
import type * as Prisma from "../internal/prismaNamespace.ts"

/**
 * Model UsuarioCliente
 * 
 */
export type UsuarioClienteModel = runtime.Types.Result.DefaultSelection<Prisma.$UsuarioClientePayload>

export type AggregateUsuarioCliente = {
  _count: UsuarioClienteCountAggregateOutputType | null
  _avg: UsuarioClienteAvgAggregateOutputType | null
  _sum: UsuarioClienteSumAggregateOutputType | null
  _min: UsuarioClienteMinAggregateOutputType | null
  _max: UsuarioClienteMaxAggregateOutputType | null
}

export type UsuarioClienteAvgAggregateOutputType = {
  id: number | null
  usuarioId: number | null
  clienteId: number | null
}

export type UsuarioClienteSumAggregateOutputType = {
  id: number | null
  usuarioId: number | null
  clienteId: number | null
}

export type UsuarioClienteMinAggregateOutputType = {
  id: number | null
  usuarioId: number | null
  clienteId: number | null
  ativo: boolean | null
  createdAt: Date | null
  updatedAt: Date | null
}

export type UsuarioClienteMaxAggregateOutputType = {
  id: number | null
  usuarioId: number | null
  clienteId: number | null
  ativo: boolean | null
  createdAt: Date | null
  updatedAt: Date | null
}

export type UsuarioClienteCountAggregateOutputType = {
  id: number
  usuarioId: number
  clienteId: number
  ativo: number
  createdAt: number
  updatedAt: number
  _all: number
}


export type UsuarioClienteAvgAggregateInputType = {
  id?: true
  usuarioId?: true
  clienteId?: true
}

export type UsuarioClienteSumAggregateInputType = {
  id?: true
  usuarioId?: true
  clienteId?: true
}

export type UsuarioClienteMinAggregateInputType = {
  id?: true
  usuarioId?: true
  clienteId?: true
  ativo?: true
  createdAt?: true
  updatedAt?: true
}

export type UsuarioClienteMaxAggregateInputType = {
  id?: true
  usuarioId?: true
  clienteId?: true
  ativo?: true
  createdAt?: true
  updatedAt?: true
}

export type UsuarioClienteCountAggregateInputType = {
  id?: true
  usuarioId?: true
  clienteId?: true
  ativo?: true
  createdAt?: true
  updatedAt?: true
  _all?: true
}

export type UsuarioClienteAggregateArgs<ExtArgs extends runtime.Types.Extensions.InternalArgs = runtime.Types.Extensions.DefaultArgs> = {
  /**
   * Filter which UsuarioCliente to aggregate.
   */
  where?: Prisma.UsuarioClienteWhereInput
  /**
   * {@link https://www.prisma.io/docs/concepts/components/prisma-client/sorting Sorting Docs}
   * 
   * Determine the order of UsuarioClientes to fetch.
   */
  orderBy?: Prisma.UsuarioClienteOrderByWithRelationInput | Prisma.UsuarioClienteOrderByWithRelationInput[]
  /**
   * {@link https://www.prisma.io/docs/concepts/components/prisma-client/pagination#cursor-based-pagination Cursor Docs}
   * 
   * Sets the start position
   */
  cursor?: Prisma.UsuarioClienteWhereUniqueInput
  /**
   * {@link https://www.prisma.io/docs/concepts/components/prisma-client/pagination Pagination Docs}
   * 
   * Take `±n` UsuarioClientes from the position of the cursor.
   */
  take?: number
  /**
   * {@link https://www.prisma.io/docs/concepts/components/prisma-client/pagination Pagination Docs}
   * 
   * Skip the first `n` UsuarioClientes.
   */
  skip?: number
  /**
   * {@link https://www.prisma.io/docs/concepts/components/prisma-client/aggregations Aggregation Docs}
   * 
   * Count returned UsuarioClientes
  **/
  _count?: true | UsuarioClienteCountAggregateInputType
  /**
   * {@link https://www.prisma.io/docs/concepts/components/prisma-client/aggregations Aggregation Docs}
   * 
   * Select which fields to average
  **/
  _avg?: UsuarioClienteAvgAggregateInputType
  /**
   * {@link https://www.prisma.io/docs/concepts/components/prisma-client/aggregations Aggregation Docs}
   * 
   * Select which fields to sum
  **/
  _sum?: UsuarioClienteSumAggregateInputType
  /**
   * {@link https://www.prisma.io/docs/concepts/components/prisma-client/aggregations Aggregation Docs}
   * 
   * Select which fields to find the minimum value
  **/
  _min?: UsuarioClienteMinAggregateInputType
  /**
   * {@link https://www.prisma.io/docs/concepts/components/prisma-client/aggregations Aggregation Docs}
   * 
   * Select which fields to find the maximum value
  **/
  _max?: UsuarioClienteMaxAggregateInputType
}

export type GetUsuarioClienteAggregateType<T extends UsuarioClienteAggregateArgs> = {
      [P in keyof T & keyof AggregateUsuarioCliente]: P extends '_count' | 'count'
    ? T[P] extends true
      ? number
      : Prisma.GetScalarType<T[P], AggregateUsuarioCliente[P]>
    : Prisma.GetScalarType<T[P], AggregateUsuarioCliente[P]>
}




export type UsuarioClienteGroupByArgs<ExtArgs extends runtime.Types.Extensions.InternalArgs = runtime.Types.Extensions.DefaultArgs> = {
  where?: Prisma.UsuarioClienteWhereInput
  orderBy?: Prisma.UsuarioClienteOrderByWithAggregationInput | Prisma.UsuarioClienteOrderByWithAggregationInput[]
  by: Prisma.UsuarioClienteScalarFieldEnum[] | Prisma.UsuarioClienteScalarFieldEnum
  having?: Prisma.UsuarioClienteScalarWhereWithAggregatesInput
  take?: number
  skip?: number
  _count?: UsuarioClienteCountAggregateInputType | true
  _avg?: UsuarioClienteAvgAggregateInputType
  _sum?: UsuarioClienteSumAggregateInputType
  _min?: UsuarioClienteMinAggregateInputType
  _max?: UsuarioClienteMaxAggregateInputType
}

export type UsuarioClienteGroupByOutputType = {
  id: number
  usuarioId: number
  clienteId: number
  ativo: boolean
  createdAt: Date
  updatedAt: Date
  _count: UsuarioClienteCountAggregateOutputType | null
  _avg: UsuarioClienteAvgAggregateOutputType | null
  _sum: UsuarioClienteSumAggregateOutputType | null
  _min: UsuarioClienteMinAggregateOutputType | null
  _max: UsuarioClienteMaxAggregateOutputType | null
}

type GetUsuarioClienteGroupByPayload<T extends UsuarioClienteGroupByArgs> = Prisma.PrismaPromise<
  Array<
    Prisma.PickEnumerable<UsuarioClienteGroupByOutputType, T['by']> &
      {
        [P in ((keyof T) & (keyof UsuarioClienteGroupByOutputType))]: P extends '_count'
          ? T[P] extends boolean
            ? number
            : Prisma.GetScalarType<T[P], UsuarioClienteGroupByOutputType[P]>
          : Prisma.GetScalarType<T[P], UsuarioClienteGroupByOutputType[P]>
      }
    >
  >



export type UsuarioClienteWhereInput = {
  AND?: Prisma.UsuarioClienteWhereInput | Prisma.UsuarioClienteWhereInput[]
  OR?: Prisma.UsuarioClienteWhereInput[]
  NOT?: Prisma.UsuarioClienteWhereInput | Prisma.UsuarioClienteWhereInput[]
  id?: Prisma.IntFilter<"UsuarioCliente"> | number
  usuarioId?: Prisma.IntFilter<"UsuarioCliente"> | number
  clienteId?: Prisma.IntFilter<"UsuarioCliente"> | number
  ativo?: Prisma.BoolFilter<"UsuarioCliente"> | boolean
  createdAt?: Prisma.DateTimeFilter<"UsuarioCliente"> | Date | string
  updatedAt?: Prisma.DateTimeFilter<"UsuarioCliente"> | Date | string
  usuario?: Prisma.XOR<Prisma.UsuarioScalarRelationFilter, Prisma.UsuarioWhereInput>
  cliente?: Prisma.XOR<Prisma.ClienteScalarRelationFilter, Prisma.ClienteWhereInput>
}

export type UsuarioClienteOrderByWithRelationInput = {
  id?: Prisma.SortOrder
  usuarioId?: Prisma.SortOrder
  clienteId?: Prisma.SortOrder
  ativo?: Prisma.SortOrder
  createdAt?: Prisma.SortOrder
  updatedAt?: Prisma.SortOrder
  usuario?: Prisma.UsuarioOrderByWithRelationInput
  cliente?: Prisma.ClienteOrderByWithRelationInput
}

export type UsuarioClienteWhereUniqueInput = Prisma.AtLeast<{
  id?: number
  usuarioId_clienteId?: Prisma.UsuarioClienteUsuarioIdClienteIdCompoundUniqueInput
  AND?: Prisma.UsuarioClienteWhereInput | Prisma.UsuarioClienteWhereInput[]
  OR?: Prisma.UsuarioClienteWhereInput[]
  NOT?: Prisma.UsuarioClienteWhereInput | Prisma.UsuarioClienteWhereInput[]
  usuarioId?: Prisma.IntFilter<"UsuarioCliente"> | number
  clienteId?: Prisma.IntFilter<"UsuarioCliente"> | number
  ativo?: Prisma.BoolFilter<"UsuarioCliente"> | boolean
  createdAt?: Prisma.DateTimeFilter<"UsuarioCliente"> | Date | string
  updatedAt?: Prisma.DateTimeFilter<"UsuarioCliente"> | Date | string
  usuario?: Prisma.XOR<Prisma.UsuarioScalarRelationFilter, Prisma.UsuarioWhereInput>
  cliente?: Prisma.XOR<Prisma.ClienteScalarRelationFilter, Prisma.ClienteWhereInput>
}, "id" | "usuarioId_clienteId">

export type UsuarioClienteOrderByWithAggregationInput = {
  id?: Prisma.SortOrder
  usuarioId?: Prisma.SortOrder
  clienteId?: Prisma.SortOrder
  ativo?: Prisma.SortOrder
  createdAt?: Prisma.SortOrder
  updatedAt?: Prisma.SortOrder
  _count?: Prisma.UsuarioClienteCountOrderByAggregateInput
  _avg?: Prisma.UsuarioClienteAvgOrderByAggregateInput
  _max?: Prisma.UsuarioClienteMaxOrderByAggregateInput
  _min?: Prisma.UsuarioClienteMinOrderByAggregateInput
  _sum?: Prisma.UsuarioClienteSumOrderByAggregateInput
}

export type UsuarioClienteScalarWhereWithAggregatesInput = {
  AND?: Prisma.UsuarioClienteScalarWhereWithAggregatesInput | Prisma.UsuarioClienteScalarWhereWithAggregatesInput[]
  OR?: Prisma.UsuarioClienteScalarWhereWithAggregatesInput[]
  NOT?: Prisma.UsuarioClienteScalarWhereWithAggregatesInput | Prisma.UsuarioClienteScalarWhereWithAggregatesInput[]
  id?: Prisma.IntWithAggregatesFilter<"UsuarioCliente"> | number
  usuarioId?: Prisma.IntWithAggregatesFilter<"UsuarioCliente"> | number
  clienteId?: Prisma.IntWithAggregatesFilter<"UsuarioCliente"> | number
  ativo?: Prisma.BoolWithAggregatesFilter<"UsuarioCliente"> | boolean
  createdAt?: Prisma.DateTimeWithAggregatesFilter<"UsuarioCliente"> | Date | string
  updatedAt?: Prisma.DateTimeWithAggregatesFilter<"UsuarioCliente"> | Date | string
}

export type UsuarioClienteCreateInput = {
  ativo?: boolean
  createdAt?: Date | string
  updatedAt?: Date | string
  usuario: Prisma.UsuarioCreateNestedOneWithoutClientesInput
  cliente: Prisma.ClienteCreateNestedOneWithoutUsuariosInput
}

export type UsuarioClienteUncheckedCreateInput = {
  id?: number
  usuarioId: number
  clienteId: number
  ativo?: boolean
  createdAt?: Date | string
  updatedAt?: Date | string
}

export type UsuarioClienteUpdateInput = {
  ativo?: Prisma.BoolFieldUpdateOperationsInput | boolean
  createdAt?: Prisma.DateTimeFieldUpdateOperationsInput | Date | string
  updatedAt?: Prisma.DateTimeFieldUpdateOperationsInput | Date | string
  usuario?: Prisma.UsuarioUpdateOneRequiredWithoutClientesNestedInput
  cliente?: Prisma.ClienteUpdateOneRequiredWithoutUsuariosNestedInput
}

export type UsuarioClienteUncheckedUpdateInput = {
  id?: Prisma.IntFieldUpdateOperationsInput | number
  usuarioId?: Prisma.IntFieldUpdateOperationsInput | number
  clienteId?: Prisma.IntFieldUpdateOperationsInput | number
  ativo?: Prisma.BoolFieldUpdateOperationsInput | boolean
  createdAt?: Prisma.DateTimeFieldUpdateOperationsInput | Date | string
  updatedAt?: Prisma.DateTimeFieldUpdateOperationsInput | Date | string
}

export type UsuarioClienteCreateManyInput = {
  id?: number
  usuarioId: number
  clienteId: number
  ativo?: boolean
  createdAt?: Date | string
  updatedAt?: Date | string
}

export type UsuarioClienteUpdateManyMutationInput = {
  ativo?: Prisma.BoolFieldUpdateOperationsInput | boolean
  createdAt?: Prisma.DateTimeFieldUpdateOperationsInput | Date | string
  updatedAt?: Prisma.DateTimeFieldUpdateOperationsInput | Date | string
}

export type UsuarioClienteUncheckedUpdateManyInput = {
  id?: Prisma.IntFieldUpdateOperationsInput | number
  usuarioId?: Prisma.IntFieldUpdateOperationsInput | number
  clienteId?: Prisma.IntFieldUpdateOperationsInput | number
  ativo?: Prisma.BoolFieldUpdateOperationsInput | boolean
  createdAt?: Prisma.DateTimeFieldUpdateOperationsInput | Date | string
  updatedAt?: Prisma.DateTimeFieldUpdateOperationsInput | Date | string
}

export type UsuarioClienteListRelationFilter = {
  every?: Prisma.UsuarioClienteWhereInput
  some?: Prisma.UsuarioClienteWhereInput
  none?: Prisma.UsuarioClienteWhereInput
}

export type UsuarioClienteOrderByRelationAggregateInput = {
  _count?: Prisma.SortOrder
}

export type UsuarioClienteUsuarioIdClienteIdCompoundUniqueInput = {
  usuarioId: number
  clienteId: number
}

export type UsuarioClienteCountOrderByAggregateInput = {
  id?: Prisma.SortOrder
  usuarioId?: Prisma.SortOrder
  clienteId?: Prisma.SortOrder
  ativo?: Prisma.SortOrder
  createdAt?: Prisma.SortOrder
  updatedAt?: Prisma.SortOrder
}

export type UsuarioClienteAvgOrderByAggregateInput = {
  id?: Prisma.SortOrder
  usuarioId?: Prisma.SortOrder
  clienteId?: Prisma.SortOrder
}

export type UsuarioClienteMaxOrderByAggregateInput = {
  id?: Prisma.SortOrder
  usuarioId?: Prisma.SortOrder
  clienteId?: Prisma.SortOrder
  ativo?: Prisma.SortOrder
  createdAt?: Prisma.SortOrder
  updatedAt?: Prisma.SortOrder
}

export type UsuarioClienteMinOrderByAggregateInput = {
  id?: Prisma.SortOrder
  usuarioId?: Prisma.SortOrder
  clienteId?: Prisma.SortOrder
  ativo?: Prisma.SortOrder
  createdAt?: Prisma.SortOrder
  updatedAt?: Prisma.SortOrder
}

export type UsuarioClienteSumOrderByAggregateInput = {
  id?: Prisma.SortOrder
  usuarioId?: Prisma.SortOrder
  clienteId?: Prisma.SortOrder
}

export type UsuarioClienteCreateNestedManyWithoutUsuarioInput = {
  create?: Prisma.XOR<Prisma.UsuarioClienteCreateWithoutUsuarioInput, Prisma.UsuarioClienteUncheckedCreateWithoutUsuarioInput> | Prisma.UsuarioClienteCreateWithoutUsuarioInput[] | Prisma.UsuarioClienteUncheckedCreateWithoutUsuarioInput[]
  connectOrCreate?: Prisma.UsuarioClienteCreateOrConnectWithoutUsuarioInput | Prisma.UsuarioClienteCreateOrConnectWithoutUsuarioInput[]
  createMany?: Prisma.UsuarioClienteCreateManyUsuarioInputEnvelope
  connect?: Prisma.UsuarioClienteWhereUniqueInput | Prisma.UsuarioClienteWhereUniqueInput[]
}

export type UsuarioClienteUncheckedCreateNestedManyWithoutUsuarioInput = {
  create?: Prisma.XOR<Prisma.UsuarioClienteCreateWithoutUsuarioInput, Prisma.UsuarioClienteUncheckedCreateWithoutUsuarioInput> | Prisma.UsuarioClienteCreateWithoutUsuarioInput[] | Prisma.UsuarioClienteUncheckedCreateWithoutUsuarioInput[]
  connectOrCreate?: Prisma.UsuarioClienteCreateOrConnectWithoutUsuarioInput | Prisma.UsuarioClienteCreateOrConnectWithoutUsuarioInput[]
  createMany?: Prisma.UsuarioClienteCreateManyUsuarioInputEnvelope
  connect?: Prisma.UsuarioClienteWhereUniqueInput | Prisma.UsuarioClienteWhereUniqueInput[]
}

export type UsuarioClienteUpdateManyWithoutUsuarioNestedInput = {
  create?: Prisma.XOR<Prisma.UsuarioClienteCreateWithoutUsuarioInput, Prisma.UsuarioClienteUncheckedCreateWithoutUsuarioInput> | Prisma.UsuarioClienteCreateWithoutUsuarioInput[] | Prisma.UsuarioClienteUncheckedCreateWithoutUsuarioInput[]
  connectOrCreate?: Prisma.UsuarioClienteCreateOrConnectWithoutUsuarioInput | Prisma.UsuarioClienteCreateOrConnectWithoutUsuarioInput[]
  upsert?: Prisma.UsuarioClienteUpsertWithWhereUniqueWithoutUsuarioInput | Prisma.UsuarioClienteUpsertWithWhereUniqueWithoutUsuarioInput[]
  createMany?: Prisma.UsuarioClienteCreateManyUsuarioInputEnvelope
  set?: Prisma.UsuarioClienteWhereUniqueInput | Prisma.UsuarioClienteWhereUniqueInput[]
  disconnect?: Prisma.UsuarioClienteWhereUniqueInput | Prisma.UsuarioClienteWhereUniqueInput[]
  delete?: Prisma.UsuarioClienteWhereUniqueInput | Prisma.UsuarioClienteWhereUniqueInput[]
  connect?: Prisma.UsuarioClienteWhereUniqueInput | Prisma.UsuarioClienteWhereUniqueInput[]
  update?: Prisma.UsuarioClienteUpdateWithWhereUniqueWithoutUsuarioInput | Prisma.UsuarioClienteUpdateWithWhereUniqueWithoutUsuarioInput[]
  updateMany?: Prisma.UsuarioClienteUpdateManyWithWhereWithoutUsuarioInput | Prisma.UsuarioClienteUpdateManyWithWhereWithoutUsuarioInput[]
  deleteMany?: Prisma.UsuarioClienteScalarWhereInput | Prisma.UsuarioClienteScalarWhereInput[]
}

export type UsuarioClienteUncheckedUpdateManyWithoutUsuarioNestedInput = {
  create?: Prisma.XOR<Prisma.UsuarioClienteCreateWithoutUsuarioInput, Prisma.UsuarioClienteUncheckedCreateWithoutUsuarioInput> | Prisma.UsuarioClienteCreateWithoutUsuarioInput[] | Prisma.UsuarioClienteUncheckedCreateWithoutUsuarioInput[]
  connectOrCreate?: Prisma.UsuarioClienteCreateOrConnectWithoutUsuarioInput | Prisma.UsuarioClienteCreateOrConnectWithoutUsuarioInput[]
  upsert?: Prisma.UsuarioClienteUpsertWithWhereUniqueWithoutUsuarioInput | Prisma.UsuarioClienteUpsertWithWhereUniqueWithoutUsuarioInput[]
  createMany?: Prisma.UsuarioClienteCreateManyUsuarioInputEnvelope
  set?: Prisma.UsuarioClienteWhereUniqueInput | Prisma.UsuarioClienteWhereUniqueInput[]
  disconnect?: Prisma.UsuarioClienteWhereUniqueInput | Prisma.UsuarioClienteWhereUniqueInput[]
  delete?: Prisma.UsuarioClienteWhereUniqueInput | Prisma.UsuarioClienteWhereUniqueInput[]
  connect?: Prisma.UsuarioClienteWhereUniqueInput | Prisma.UsuarioClienteWhereUniqueInput[]
  update?: Prisma.UsuarioClienteUpdateWithWhereUniqueWithoutUsuarioInput | Prisma.UsuarioClienteUpdateWithWhereUniqueWithoutUsuarioInput[]
  updateMany?: Prisma.UsuarioClienteUpdateManyWithWhereWithoutUsuarioInput | Prisma.UsuarioClienteUpdateManyWithWhereWithoutUsuarioInput[]
  deleteMany?: Prisma.UsuarioClienteScalarWhereInput | Prisma.UsuarioClienteScalarWhereInput[]
}

export type UsuarioClienteCreateNestedManyWithoutClienteInput = {
  create?: Prisma.XOR<Prisma.UsuarioClienteCreateWithoutClienteInput, Prisma.UsuarioClienteUncheckedCreateWithoutClienteInput> | Prisma.UsuarioClienteCreateWithoutClienteInput[] | Prisma.UsuarioClienteUncheckedCreateWithoutClienteInput[]
  connectOrCreate?: Prisma.UsuarioClienteCreateOrConnectWithoutClienteInput | Prisma.UsuarioClienteCreateOrConnectWithoutClienteInput[]
  createMany?: Prisma.UsuarioClienteCreateManyClienteInputEnvelope
  connect?: Prisma.UsuarioClienteWhereUniqueInput | Prisma.UsuarioClienteWhereUniqueInput[]
}

export type UsuarioClienteUncheckedCreateNestedManyWithoutClienteInput = {
  create?: Prisma.XOR<Prisma.UsuarioClienteCreateWithoutClienteInput, Prisma.UsuarioClienteUncheckedCreateWithoutClienteInput> | Prisma.UsuarioClienteCreateWithoutClienteInput[] | Prisma.UsuarioClienteUncheckedCreateWithoutClienteInput[]
  connectOrCreate?: Prisma.UsuarioClienteCreateOrConnectWithoutClienteInput | Prisma.UsuarioClienteCreateOrConnectWithoutClienteInput[]
  createMany?: Prisma.UsuarioClienteCreateManyClienteInputEnvelope
  connect?: Prisma.UsuarioClienteWhereUniqueInput | Prisma.UsuarioClienteWhereUniqueInput[]
}

export type UsuarioClienteUpdateManyWithoutClienteNestedInput = {
  create?: Prisma.XOR<Prisma.UsuarioClienteCreateWithoutClienteInput, Prisma.UsuarioClienteUncheckedCreateWithoutClienteInput> | Prisma.UsuarioClienteCreateWithoutClienteInput[] | Prisma.UsuarioClienteUncheckedCreateWithoutClienteInput[]
  connectOrCreate?: Prisma.UsuarioClienteCreateOrConnectWithoutClienteInput | Prisma.UsuarioClienteCreateOrConnectWithoutClienteInput[]
  upsert?: Prisma.UsuarioClienteUpsertWithWhereUniqueWithoutClienteInput | Prisma.UsuarioClienteUpsertWithWhereUniqueWithoutClienteInput[]
  createMany?: Prisma.UsuarioClienteCreateManyClienteInputEnvelope
  set?: Prisma.UsuarioClienteWhereUniqueInput | Prisma.UsuarioClienteWhereUniqueInput[]
  disconnect?: Prisma.UsuarioClienteWhereUniqueInput | Prisma.UsuarioClienteWhereUniqueInput[]
  delete?: Prisma.UsuarioClienteWhereUniqueInput | Prisma.UsuarioClienteWhereUniqueInput[]
  connect?: Prisma.UsuarioClienteWhereUniqueInput | Prisma.UsuarioClienteWhereUniqueInput[]
  update?: Prisma.UsuarioClienteUpdateWithWhereUniqueWithoutClienteInput | Prisma.UsuarioClienteUpdateWithWhereUniqueWithoutClienteInput[]
  updateMany?: Prisma.UsuarioClienteUpdateManyWithWhereWithoutClienteInput | Prisma.UsuarioClienteUpdateManyWithWhereWithoutClienteInput[]
  deleteMany?: Prisma.UsuarioClienteScalarWhereInput | Prisma.UsuarioClienteScalarWhereInput[]
}

export type UsuarioClienteUncheckedUpdateManyWithoutClienteNestedInput = {
  create?: Prisma.XOR<Prisma.UsuarioClienteCreateWithoutClienteInput, Prisma.UsuarioClienteUncheckedCreateWithoutClienteInput> | Prisma.UsuarioClienteCreateWithoutClienteInput[] | Prisma.UsuarioClienteUncheckedCreateWithoutClienteInput[]
  connectOrCreate?: Prisma.UsuarioClienteCreateOrConnectWithoutClienteInput | Prisma.UsuarioClienteCreateOrConnectWithoutClienteInput[]
  upsert?: Prisma.UsuarioClienteUpsertWithWhereUniqueWithoutClienteInput | Prisma.UsuarioClienteUpsertWithWhereUniqueWithoutClienteInput[]
  createMany?: Prisma.UsuarioClienteCreateManyClienteInputEnvelope
  set?: Prisma.UsuarioClienteWhereUniqueInput | Prisma.UsuarioClienteWhereUniqueInput[]
  disconnect?: Prisma.UsuarioClienteWhereUniqueInput | Prisma.UsuarioClienteWhereUniqueInput[]
  delete?: Prisma.UsuarioClienteWhereUniqueInput | Prisma.UsuarioClienteWhereUniqueInput[]
  connect?: Prisma.UsuarioClienteWhereUniqueInput | Prisma.UsuarioClienteWhereUniqueInput[]
  update?: Prisma.UsuarioClienteUpdateWithWhereUniqueWithoutClienteInput | Prisma.UsuarioClienteUpdateWithWhereUniqueWithoutClienteInput[]
  updateMany?: Prisma.UsuarioClienteUpdateManyWithWhereWithoutClienteInput | Prisma.UsuarioClienteUpdateManyWithWhereWithoutClienteInput[]
  deleteMany?: Prisma.UsuarioClienteScalarWhereInput | Prisma.UsuarioClienteScalarWhereInput[]
}

export type UsuarioClienteCreateWithoutUsuarioInput = {
  ativo?: boolean
  createdAt?: Date | string
  updatedAt?: Date | string
  cliente: Prisma.ClienteCreateNestedOneWithoutUsuariosInput
}

export type UsuarioClienteUncheckedCreateWithoutUsuarioInput = {
  id?: number
  clienteId: number
  ativo?: boolean
  createdAt?: Date | string
  updatedAt?: Date | string
}

export type UsuarioClienteCreateOrConnectWithoutUsuarioInput = {
  where: Prisma.UsuarioClienteWhereUniqueInput
  create: Prisma.XOR<Prisma.UsuarioClienteCreateWithoutUsuarioInput, Prisma.UsuarioClienteUncheckedCreateWithoutUsuarioInput>
}

export type UsuarioClienteCreateManyUsuarioInputEnvelope = {
  data: Prisma.UsuarioClienteCreateManyUsuarioInput | Prisma.UsuarioClienteCreateManyUsuarioInput[]
  skipDuplicates?: boolean
}

export type UsuarioClienteUpsertWithWhereUniqueWithoutUsuarioInput = {
  where: Prisma.UsuarioClienteWhereUniqueInput
  update: Prisma.XOR<Prisma.UsuarioClienteUpdateWithoutUsuarioInput, Prisma.UsuarioClienteUncheckedUpdateWithoutUsuarioInput>
  create: Prisma.XOR<Prisma.UsuarioClienteCreateWithoutUsuarioInput, Prisma.UsuarioClienteUncheckedCreateWithoutUsuarioInput>
}

export type UsuarioClienteUpdateWithWhereUniqueWithoutUsuarioInput = {
  where: Prisma.UsuarioClienteWhereUniqueInput
  data: Prisma.XOR<Prisma.UsuarioClienteUpdateWithoutUsuarioInput, Prisma.UsuarioClienteUncheckedUpdateWithoutUsuarioInput>
}

export type UsuarioClienteUpdateManyWithWhereWithoutUsuarioInput = {
  where: Prisma.UsuarioClienteScalarWhereInput
  data: Prisma.XOR<Prisma.UsuarioClienteUpdateManyMutationInput, Prisma.UsuarioClienteUncheckedUpdateManyWithoutUsuarioInput>
}

export type UsuarioClienteScalarWhereInput = {
  AND?: Prisma.UsuarioClienteScalarWhereInput | Prisma.UsuarioClienteScalarWhereInput[]
  OR?: Prisma.UsuarioClienteScalarWhereInput[]
  NOT?: Prisma.UsuarioClienteScalarWhereInput | Prisma.UsuarioClienteScalarWhereInput[]
  id?: Prisma.IntFilter<"UsuarioCliente"> | number
  usuarioId?: Prisma.IntFilter<"UsuarioCliente"> | number
  clienteId?: Prisma.IntFilter<"UsuarioCliente"> | number
  ativo?: Prisma.BoolFilter<"UsuarioCliente"> | boolean
  createdAt?: Prisma.DateTimeFilter<"UsuarioCliente"> | Date | string
  updatedAt?: Prisma.DateTimeFilter<"UsuarioCliente"> | Date | string
}

export type UsuarioClienteCreateWithoutClienteInput = {
  ativo?: boolean
  createdAt?: Date | string
  updatedAt?: Date | string
  usuario: Prisma.UsuarioCreateNestedOneWithoutClientesInput
}

export type UsuarioClienteUncheckedCreateWithoutClienteInput = {
  id?: number
  usuarioId: number
  ativo?: boolean
  createdAt?: Date | string
  updatedAt?: Date | string
}

export type UsuarioClienteCreateOrConnectWithoutClienteInput = {
  where: Prisma.UsuarioClienteWhereUniqueInput
  create: Prisma.XOR<Prisma.UsuarioClienteCreateWithoutClienteInput, Prisma.UsuarioClienteUncheckedCreateWithoutClienteInput>
}

export type UsuarioClienteCreateManyClienteInputEnvelope = {
  data: Prisma.UsuarioClienteCreateManyClienteInput | Prisma.UsuarioClienteCreateManyClienteInput[]
  skipDuplicates?: boolean
}

export type UsuarioClienteUpsertWithWhereUniqueWithoutClienteInput = {
  where: Prisma.UsuarioClienteWhereUniqueInput
  update: Prisma.XOR<Prisma.UsuarioClienteUpdateWithoutClienteInput, Prisma.UsuarioClienteUncheckedUpdateWithoutClienteInput>
  create: Prisma.XOR<Prisma.UsuarioClienteCreateWithoutClienteInput, Prisma.UsuarioClienteUncheckedCreateWithoutClienteInput>
}

export type UsuarioClienteUpdateWithWhereUniqueWithoutClienteInput = {
  where: Prisma.UsuarioClienteWhereUniqueInput
  data: Prisma.XOR<Prisma.UsuarioClienteUpdateWithoutClienteInput, Prisma.UsuarioClienteUncheckedUpdateWithoutClienteInput>
}

export type UsuarioClienteUpdateManyWithWhereWithoutClienteInput = {
  where: Prisma.UsuarioClienteScalarWhereInput
  data: Prisma.XOR<Prisma.UsuarioClienteUpdateManyMutationInput, Prisma.UsuarioClienteUncheckedUpdateManyWithoutClienteInput>
}

export type UsuarioClienteCreateManyUsuarioInput = {
  id?: number
  clienteId: number
  ativo?: boolean
  createdAt?: Date | string
  updatedAt?: Date | string
}

export type UsuarioClienteUpdateWithoutUsuarioInput = {
  ativo?: Prisma.BoolFieldUpdateOperationsInput | boolean
  createdAt?: Prisma.DateTimeFieldUpdateOperationsInput | Date | string
  updatedAt?: Prisma.DateTimeFieldUpdateOperationsInput | Date | string
  cliente?: Prisma.ClienteUpdateOneRequiredWithoutUsuariosNestedInput
}

export type UsuarioClienteUncheckedUpdateWithoutUsuarioInput = {
  id?: Prisma.IntFieldUpdateOperationsInput | number
  clienteId?: Prisma.IntFieldUpdateOperationsInput | number
  ativo?: Prisma.BoolFieldUpdateOperationsInput | boolean
  createdAt?: Prisma.DateTimeFieldUpdateOperationsInput | Date | string
  updatedAt?: Prisma.DateTimeFieldUpdateOperationsInput | Date | string
}

export type UsuarioClienteUncheckedUpdateManyWithoutUsuarioInput = {
  id?: Prisma.IntFieldUpdateOperationsInput | number
  clienteId?: Prisma.IntFieldUpdateOperationsInput | number
  ativo?: Prisma.BoolFieldUpdateOperationsInput | boolean
  createdAt?: Prisma.DateTimeFieldUpdateOperationsInput | Date | string
  updatedAt?: Prisma.DateTimeFieldUpdateOperationsInput | Date | string
}

export type UsuarioClienteCreateManyClienteInput = {
  id?: number
  usuarioId: number
  ativo?: boolean
  createdAt?: Date | string
  updatedAt?: Date | string
}

export type UsuarioClienteUpdateWithoutClienteInput = {
  ativo?: Prisma.BoolFieldUpdateOperationsInput | boolean
  createdAt?: Prisma.DateTimeFieldUpdateOperationsInput | Date | string
  updatedAt?: Prisma.DateTimeFieldUpdateOperationsInput | Date | string
  usuario?: Prisma.UsuarioUpdateOneRequiredWithoutClientesNestedInput
}

export type UsuarioClienteUncheckedUpdateWithoutClienteInput = {
  id?: Prisma.IntFieldUpdateOperationsInput | number
  usuarioId?: Prisma.IntFieldUpdateOperationsInput | number
  ativo?: Prisma.BoolFieldUpdateOperationsInput | boolean
  createdAt?: Prisma.DateTimeFieldUpdateOperationsInput | Date | string
  updatedAt?: Prisma.DateTimeFieldUpdateOperationsInput | Date | string
}

export type UsuarioClienteUncheckedUpdateManyWithoutClienteInput = {
  id?: Prisma.IntFieldUpdateOperationsInput | number
  usuarioId?: Prisma.IntFieldUpdateOperationsInput | number
  ativo?: Prisma.BoolFieldUpdateOperationsInput | boolean
  createdAt?: Prisma.DateTimeFieldUpdateOperationsInput | Date | string
  updatedAt?: Prisma.DateTimeFieldUpdateOperationsInput | Date | string
}



export type UsuarioClienteSelect<ExtArgs extends runtime.Types.Extensions.InternalArgs = runtime.Types.Extensions.DefaultArgs> = runtime.Types.Extensions.GetSelect<{
  id?: boolean
  usuarioId?: boolean
  clienteId?: boolean
  ativo?: boolean
  createdAt?: boolean
  updatedAt?: boolean
  usuario?: boolean | Prisma.UsuarioDefaultArgs<ExtArgs>
  cliente?: boolean | Prisma.ClienteDefaultArgs<ExtArgs>
}, ExtArgs["result"]["usuarioCliente"]>



export type UsuarioClienteSelectScalar = {
  id?: boolean
  usuarioId?: boolean
  clienteId?: boolean
  ativo?: boolean
  createdAt?: boolean
  updatedAt?: boolean
}

export type UsuarioClienteOmit<ExtArgs extends runtime.Types.Extensions.InternalArgs = runtime.Types.Extensions.DefaultArgs> = runtime.Types.Extensions.GetOmit<"id" | "usuarioId" | "clienteId" | "ativo" | "createdAt" | "updatedAt", ExtArgs["result"]["usuarioCliente"]>
export type UsuarioClienteInclude<ExtArgs extends runtime.Types.Extensions.InternalArgs = runtime.Types.Extensions.DefaultArgs> = {
  usuario?: boolean | Prisma.UsuarioDefaultArgs<ExtArgs>
  cliente?: boolean | Prisma.ClienteDefaultArgs<ExtArgs>
}

export type $UsuarioClientePayload<ExtArgs extends runtime.Types.Extensions.InternalArgs = runtime.Types.Extensions.DefaultArgs> = {
  name: "UsuarioCliente"
  objects: {
    usuario: Prisma.$UsuarioPayload<ExtArgs>
    cliente: Prisma.$ClientePayload<ExtArgs>
  }
  scalars: runtime.Types.Extensions.GetPayloadResult<{
    id: number
    usuarioId: number
    clienteId: number
    ativo: boolean
    createdAt: Date
    updatedAt: Date
  }, ExtArgs["result"]["usuarioCliente"]>
  composites: {}
}

export type UsuarioClienteGetPayload<S extends boolean | null | undefined | UsuarioClienteDefaultArgs> = runtime.Types.Result.GetResult<Prisma.$UsuarioClientePayload, S>

export type UsuarioClienteCountArgs<ExtArgs extends runtime.Types.Extensions.InternalArgs = runtime.Types.Extensions.DefaultArgs> =
  Omit<UsuarioClienteFindManyArgs, 'select' | 'include' | 'distinct' | 'omit'> & {
    select?: UsuarioClienteCountAggregateInputType | true
  }

export interface UsuarioClienteDelegate<ExtArgs extends runtime.Types.Extensions.InternalArgs = runtime.Types.Extensions.DefaultArgs, GlobalOmitOptions = {}> {
  [K: symbol]: { types: Prisma.TypeMap<ExtArgs>['model']['UsuarioCliente'], meta: { name: 'UsuarioCliente' } }
  /**
   * Find zero or one UsuarioCliente that matches the filter.
   * @param {UsuarioClienteFindUniqueArgs} args - Arguments to find a UsuarioCliente
   * @example
   * // Get one UsuarioCliente
   * const usuarioCliente = await prisma.usuarioCliente.findUnique({
   *   where: {
   *     // ... provide filter here
   *   }
   * })
   */
  findUnique<T extends UsuarioClienteFindUniqueArgs>(args: Prisma.SelectSubset<T, UsuarioClienteFindUniqueArgs<ExtArgs>>): Prisma.Prisma__UsuarioClienteClient<runtime.Types.Result.GetResult<Prisma.$UsuarioClientePayload<ExtArgs>, T, "findUnique", GlobalOmitOptions> | null, null, ExtArgs, GlobalOmitOptions>

  /**
   * Find one UsuarioCliente that matches the filter or throw an error with `error.code='P2025'`
   * if no matches were found.
   * @param {UsuarioClienteFindUniqueOrThrowArgs} args - Arguments to find a UsuarioCliente
   * @example
   * // Get one UsuarioCliente
   * const usuarioCliente = await prisma.usuarioCliente.findUniqueOrThrow({
   *   where: {
   *     // ... provide filter here
   *   }
   * })
   */
  findUniqueOrThrow<T extends UsuarioClienteFindUniqueOrThrowArgs>(args: Prisma.SelectSubset<T, UsuarioClienteFindUniqueOrThrowArgs<ExtArgs>>): Prisma.Prisma__UsuarioClienteClient<runtime.Types.Result.GetResult<Prisma.$UsuarioClientePayload<ExtArgs>, T, "findUniqueOrThrow", GlobalOmitOptions>, never, ExtArgs, GlobalOmitOptions>

  /**
   * Find the first UsuarioCliente that matches the filter.
   * Note, that providing `undefined` is treated as the value not being there.
   * Read more here: https://pris.ly/d/null-undefined
   * @param {UsuarioClienteFindFirstArgs} args - Arguments to find a UsuarioCliente
   * @example
   * // Get one UsuarioCliente
   * const usuarioCliente = await prisma.usuarioCliente.findFirst({
   *   where: {
   *     // ... provide filter here
   *   }
   * })
   */
  findFirst<T extends UsuarioClienteFindFirstArgs>(args?: Prisma.SelectSubset<T, UsuarioClienteFindFirstArgs<ExtArgs>>): Prisma.Prisma__UsuarioClienteClient<runtime.Types.Result.GetResult<Prisma.$UsuarioClientePayload<ExtArgs>, T, "findFirst", GlobalOmitOptions> | null, null, ExtArgs, GlobalOmitOptions>

  /**
   * Find the first UsuarioCliente that matches the filter or
   * throw `PrismaKnownClientError` with `P2025` code if no matches were found.
   * Note, that providing `undefined` is treated as the value not being there.
   * Read more here: https://pris.ly/d/null-undefined
   * @param {UsuarioClienteFindFirstOrThrowArgs} args - Arguments to find a UsuarioCliente
   * @example
   * // Get one UsuarioCliente
   * const usuarioCliente = await prisma.usuarioCliente.findFirstOrThrow({
   *   where: {
   *     // ... provide filter here
   *   }
   * })
   */
  findFirstOrThrow<T extends UsuarioClienteFindFirstOrThrowArgs>(args?: Prisma.SelectSubset<T, UsuarioClienteFindFirstOrThrowArgs<ExtArgs>>): Prisma.Prisma__UsuarioClienteClient<runtime.Types.Result.GetResult<Prisma.$UsuarioClientePayload<ExtArgs>, T, "findFirstOrThrow", GlobalOmitOptions>, never, ExtArgs, GlobalOmitOptions>

  /**
   * Find zero or more UsuarioClientes that matches the filter.
   * Note, that providing `undefined` is treated as the value not being there.
   * Read more here: https://pris.ly/d/null-undefined
   * @param {UsuarioClienteFindManyArgs} args - Arguments to filter and select certain fields only.
   * @example
   * // Get all UsuarioClientes
   * const usuarioClientes = await prisma.usuarioCliente.findMany()
   * 
   * // Get first 10 UsuarioClientes
   * const usuarioClientes = await prisma.usuarioCliente.findMany({ take: 10 })
   * 
   * // Only select the `id`
   * const usuarioClienteWithIdOnly = await prisma.usuarioCliente.findMany({ select: { id: true } })
   * 
   */
  findMany<T extends UsuarioClienteFindManyArgs>(args?: Prisma.SelectSubset<T, UsuarioClienteFindManyArgs<ExtArgs>>): Prisma.PrismaPromise<runtime.Types.Result.GetResult<Prisma.$UsuarioClientePayload<ExtArgs>, T, "findMany", GlobalOmitOptions>>

  /**
   * Create a UsuarioCliente.
   * @param {UsuarioClienteCreateArgs} args - Arguments to create a UsuarioCliente.
   * @example
   * // Create one UsuarioCliente
   * const UsuarioCliente = await prisma.usuarioCliente.create({
   *   data: {
   *     // ... data to create a UsuarioCliente
   *   }
   * })
   * 
   */
  create<T extends UsuarioClienteCreateArgs>(args: Prisma.SelectSubset<T, UsuarioClienteCreateArgs<ExtArgs>>): Prisma.Prisma__UsuarioClienteClient<runtime.Types.Result.GetResult<Prisma.$UsuarioClientePayload<ExtArgs>, T, "create", GlobalOmitOptions>, never, ExtArgs, GlobalOmitOptions>

  /**
   * Create many UsuarioClientes.
   * @param {UsuarioClienteCreateManyArgs} args - Arguments to create many UsuarioClientes.
   * @example
   * // Create many UsuarioClientes
   * const usuarioCliente = await prisma.usuarioCliente.createMany({
   *   data: [
   *     // ... provide data here
   *   ]
   * })
   *     
   */
  createMany<T extends UsuarioClienteCreateManyArgs>(args?: Prisma.SelectSubset<T, UsuarioClienteCreateManyArgs<ExtArgs>>): Prisma.PrismaPromise<Prisma.BatchPayload>

  /**
   * Delete a UsuarioCliente.
   * @param {UsuarioClienteDeleteArgs} args - Arguments to delete one UsuarioCliente.
   * @example
   * // Delete one UsuarioCliente
   * const UsuarioCliente = await prisma.usuarioCliente.delete({
   *   where: {
   *     // ... filter to delete one UsuarioCliente
   *   }
   * })
   * 
   */
  delete<T extends UsuarioClienteDeleteArgs>(args: Prisma.SelectSubset<T, UsuarioClienteDeleteArgs<ExtArgs>>): Prisma.Prisma__UsuarioClienteClient<runtime.Types.Result.GetResult<Prisma.$UsuarioClientePayload<ExtArgs>, T, "delete", GlobalOmitOptions>, never, ExtArgs, GlobalOmitOptions>

  /**
   * Update one UsuarioCliente.
   * @param {UsuarioClienteUpdateArgs} args - Arguments to update one UsuarioCliente.
   * @example
   * // Update one UsuarioCliente
   * const usuarioCliente = await prisma.usuarioCliente.update({
   *   where: {
   *     // ... provide filter here
   *   },
   *   data: {
   *     // ... provide data here
   *   }
   * })
   * 
   */
  update<T extends UsuarioClienteUpdateArgs>(args: Prisma.SelectSubset<T, UsuarioClienteUpdateArgs<ExtArgs>>): Prisma.Prisma__UsuarioClienteClient<runtime.Types.Result.GetResult<Prisma.$UsuarioClientePayload<ExtArgs>, T, "update", GlobalOmitOptions>, never, ExtArgs, GlobalOmitOptions>

  /**
   * Delete zero or more UsuarioClientes.
   * @param {UsuarioClienteDeleteManyArgs} args - Arguments to filter UsuarioClientes to delete.
   * @example
   * // Delete a few UsuarioClientes
   * const { count } = await prisma.usuarioCliente.deleteMany({
   *   where: {
   *     // ... provide filter here
   *   }
   * })
   * 
   */
  deleteMany<T extends UsuarioClienteDeleteManyArgs>(args?: Prisma.SelectSubset<T, UsuarioClienteDeleteManyArgs<ExtArgs>>): Prisma.PrismaPromise<Prisma.BatchPayload>

  /**
   * Update zero or more UsuarioClientes.
   * Note, that providing `undefined` is treated as the value not being there.
   * Read more here: https://pris.ly/d/null-undefined
   * @param {UsuarioClienteUpdateManyArgs} args - Arguments to update one or more rows.
   * @example
   * // Update many UsuarioClientes
   * const usuarioCliente = await prisma.usuarioCliente.updateMany({
   *   where: {
   *     // ... provide filter here
   *   },
   *   data: {
   *     // ... provide data here
   *   }
   * })
   * 
   */
  updateMany<T extends UsuarioClienteUpdateManyArgs>(args: Prisma.SelectSubset<T, UsuarioClienteUpdateManyArgs<ExtArgs>>): Prisma.PrismaPromise<Prisma.BatchPayload>

  /**
   * Create or update one UsuarioCliente.
   * @param {UsuarioClienteUpsertArgs} args - Arguments to update or create a UsuarioCliente.
   * @example
   * // Update or create a UsuarioCliente
   * const usuarioCliente = await prisma.usuarioCliente.upsert({
   *   create: {
   *     // ... data to create a UsuarioCliente
   *   },
   *   update: {
   *     // ... in case it already exists, update
   *   },
   *   where: {
   *     // ... the filter for the UsuarioCliente we want to update
   *   }
   * })
   */
  upsert<T extends UsuarioClienteUpsertArgs>(args: Prisma.SelectSubset<T, UsuarioClienteUpsertArgs<ExtArgs>>): Prisma.Prisma__UsuarioClienteClient<runtime.Types.Result.GetResult<Prisma.$UsuarioClientePayload<ExtArgs>, T, "upsert", GlobalOmitOptions>, never, ExtArgs, GlobalOmitOptions>


  /**
   * Count the number of UsuarioClientes.
   * Note, that providing `undefined` is treated as the value not being there.
   * Read more here: https://pris.ly/d/null-undefined
   * @param {UsuarioClienteCountArgs} args - Arguments to filter UsuarioClientes to count.
   * @example
   * // Count the number of UsuarioClientes
   * const count = await prisma.usuarioCliente.count({
   *   where: {
   *     // ... the filter for the UsuarioClientes we want to count
   *   }
   * })
  **/
  count<T extends UsuarioClienteCountArgs>(
    args?: Prisma.Subset<T, UsuarioClienteCountArgs>,
  ): Prisma.PrismaPromise<
    T extends runtime.Types.Utils.Record<'select', any>
      ? T['select'] extends true
        ? number
        : Prisma.GetScalarType<T['select'], UsuarioClienteCountAggregateOutputType>
      : number
  >

  /**
   * Allows you to perform aggregations operations on a UsuarioCliente.
   * Note, that providing `undefined` is treated as the value not being there.
   * Read more here: https://pris.ly/d/null-undefined
   * @param {UsuarioClienteAggregateArgs} args - Select which aggregations you would like to apply and on what fields.
   * @example
   * // Ordered by age ascending
   * // Where email contains prisma.io
   * // Limited to the 10 users
   * const aggregations = await prisma.user.aggregate({
   *   _avg: {
   *     age: true,
   *   },
   *   where: {
   *     email: {
   *       contains: "prisma.io",
   *     },
   *   },
   *   orderBy: {
   *     age: "asc",
   *   },
   *   take: 10,
   * })
  **/
  aggregate<T extends UsuarioClienteAggregateArgs>(args: Prisma.Subset<T, UsuarioClienteAggregateArgs>): Prisma.PrismaPromise<GetUsuarioClienteAggregateType<T>>

  /**
   * Group by UsuarioCliente.
   * Note, that providing `undefined` is treated as the value not being there.
   * Read more here: https://pris.ly/d/null-undefined
   * @param {UsuarioClienteGroupByArgs} args - Group by arguments.
   * @example
   * // Group by city, order by createdAt, get count
   * const result = await prisma.user.groupBy({
   *   by: ['city', 'createdAt'],
   *   orderBy: {
   *     createdAt: true
   *   },
   *   _count: {
   *     _all: true
   *   },
   * })
   * 
  **/
  groupBy<
    T extends UsuarioClienteGroupByArgs,
    HasSelectOrTake extends Prisma.Or<
      Prisma.Extends<'skip', Prisma.Keys<T>>,
      Prisma.Extends<'take', Prisma.Keys<T>>
    >,
    OrderByArg extends Prisma.True extends HasSelectOrTake
      ? { orderBy: UsuarioClienteGroupByArgs['orderBy'] }
      : { orderBy?: UsuarioClienteGroupByArgs['orderBy'] },
    OrderFields extends Prisma.ExcludeUnderscoreKeys<Prisma.Keys<Prisma.MaybeTupleToUnion<T['orderBy']>>>,
    ByFields extends Prisma.MaybeTupleToUnion<T['by']>,
    ByValid extends Prisma.Has<ByFields, OrderFields>,
    HavingFields extends Prisma.GetHavingFields<T['having']>,
    HavingValid extends Prisma.Has<ByFields, HavingFields>,
    ByEmpty extends T['by'] extends never[] ? Prisma.True : Prisma.False,
    InputErrors extends ByEmpty extends Prisma.True
    ? `Error: "by" must not be empty.`
    : HavingValid extends Prisma.False
    ? {
        [P in HavingFields]: P extends ByFields
          ? never
          : P extends string
          ? `Error: Field "${P}" used in "having" needs to be provided in "by".`
          : [
              Error,
              'Field ',
              P,
              ` in "having" needs to be provided in "by"`,
            ]
      }[HavingFields]
    : 'take' extends Prisma.Keys<T>
    ? 'orderBy' extends Prisma.Keys<T>
      ? ByValid extends Prisma.True
        ? {}
        : {
            [P in OrderFields]: P extends ByFields
              ? never
              : `Error: Field "${P}" in "orderBy" needs to be provided in "by"`
          }[OrderFields]
      : 'Error: If you provide "take", you also need to provide "orderBy"'
    : 'skip' extends Prisma.Keys<T>
    ? 'orderBy' extends Prisma.Keys<T>
      ? ByValid extends Prisma.True
        ? {}
        : {
            [P in OrderFields]: P extends ByFields
              ? never
              : `Error: Field "${P}" in "orderBy" needs to be provided in "by"`
          }[OrderFields]
      : 'Error: If you provide "skip", you also need to provide "orderBy"'
    : ByValid extends Prisma.True
    ? {}
    : {
        [P in OrderFields]: P extends ByFields
          ? never
          : `Error: Field "${P}" in "orderBy" needs to be provided in "by"`
      }[OrderFields]
  >(args: Prisma.SubsetIntersection<T, UsuarioClienteGroupByArgs, OrderByArg> & InputErrors): {} extends InputErrors ? GetUsuarioClienteGroupByPayload<T> : Prisma.PrismaPromise<InputErrors>
/**
 * Fields of the UsuarioCliente model
 */
readonly fields: UsuarioClienteFieldRefs;
}

/**
 * The delegate class that acts as a "Promise-like" for UsuarioCliente.
 * Why is this prefixed with `Prisma__`?
 * Because we want to prevent naming conflicts as mentioned in
 * https://github.com/prisma/prisma-client-js/issues/707
 */
export interface Prisma__UsuarioClienteClient<T, Null = never, ExtArgs extends runtime.Types.Extensions.InternalArgs = runtime.Types.Extensions.DefaultArgs, GlobalOmitOptions = {}> extends Prisma.PrismaPromise<T> {
  readonly [Symbol.toStringTag]: "PrismaPromise"
  usuario<T extends Prisma.UsuarioDefaultArgs<ExtArgs> = {}>(args?: Prisma.Subset<T, Prisma.UsuarioDefaultArgs<ExtArgs>>): Prisma.Prisma__UsuarioClient<runtime.Types.Result.GetResult<Prisma.$UsuarioPayload<ExtArgs>, T, "findUniqueOrThrow", GlobalOmitOptions> | Null, Null, ExtArgs, GlobalOmitOptions>
  cliente<T extends Prisma.ClienteDefaultArgs<ExtArgs> = {}>(args?: Prisma.Subset<T, Prisma.ClienteDefaultArgs<ExtArgs>>): Prisma.Prisma__ClienteClient<runtime.Types.Result.GetResult<Prisma.$ClientePayload<ExtArgs>, T, "findUniqueOrThrow", GlobalOmitOptions> | Null, Null, ExtArgs, GlobalOmitOptions>
  /**
   * Attaches callbacks for the resolution and/or rejection of the Promise.
   * @param onfulfilled The callback to execute when the Promise is resolved.
   * @param onrejected The callback to execute when the Promise is rejected.
   * @returns A Promise for the completion of which ever callback is executed.
   */
  then<TResult1 = T, TResult2 = never>(onfulfilled?: ((value: T) => TResult1 | PromiseLike<TResult1>) | undefined | null, onrejected?: ((reason: any) => TResult2 | PromiseLike<TResult2>) | undefined | null): runtime.Types.Utils.JsPromise<TResult1 | TResult2>
  /**
   * Attaches a callback for only the rejection of the Promise.
   * @param onrejected The callback to execute when the Promise is rejected.
   * @returns A Promise for the completion of the callback.
   */
  catch<TResult = never>(onrejected?: ((reason: any) => TResult | PromiseLike<TResult>) | undefined | null): runtime.Types.Utils.JsPromise<T | TResult>
  /**
   * Attaches a callback that is invoked when the Promise is settled (fulfilled or rejected). The
   * resolved value cannot be modified from the callback.
   * @param onfinally The callback to execute when the Promise is settled (fulfilled or rejected).
   * @returns A Promise for the completion of the callback.
   */
  finally(onfinally?: (() => void) | undefined | null): runtime.Types.Utils.JsPromise<T>
}




/**
 * Fields of the UsuarioCliente model
 */
export interface UsuarioClienteFieldRefs {
  readonly id: Prisma.FieldRef<"UsuarioCliente", 'Int'>
  readonly usuarioId: Prisma.FieldRef<"UsuarioCliente", 'Int'>
  readonly clienteId: Prisma.FieldRef<"UsuarioCliente", 'Int'>
  readonly ativo: Prisma.FieldRef<"UsuarioCliente", 'Boolean'>
  readonly createdAt: Prisma.FieldRef<"UsuarioCliente", 'DateTime'>
  readonly updatedAt: Prisma.FieldRef<"UsuarioCliente", 'DateTime'>
}
    

// Custom InputTypes
/**
 * UsuarioCliente findUnique
 */
export type UsuarioClienteFindUniqueArgs<ExtArgs extends runtime.Types.Extensions.InternalArgs = runtime.Types.Extensions.DefaultArgs> = {
  /**
   * Select specific fields to fetch from the UsuarioCliente
   */
  select?: Prisma.UsuarioClienteSelect<ExtArgs> | null
  /**
   * Omit specific fields from the UsuarioCliente
   */
  omit?: Prisma.UsuarioClienteOmit<ExtArgs> | null
  /**
   * Choose, which related nodes to fetch as well
   */
  include?: Prisma.UsuarioClienteInclude<ExtArgs> | null
  /**
   * Filter, which UsuarioCliente to fetch.
   */
  where: Prisma.UsuarioClienteWhereUniqueInput
}

/**
 * UsuarioCliente findUniqueOrThrow
 */
export type UsuarioClienteFindUniqueOrThrowArgs<ExtArgs extends runtime.Types.Extensions.InternalArgs = runtime.Types.Extensions.DefaultArgs> = {
  /**
   * Select specific fields to fetch from the UsuarioCliente
   */
  select?: Prisma.UsuarioClienteSelect<ExtArgs> | null
  /**
   * Omit specific fields from the UsuarioCliente
   */
  omit?: Prisma.UsuarioClienteOmit<ExtArgs> | null
  /**
   * Choose, which related nodes to fetch as well
   */
  include?: Prisma.UsuarioClienteInclude<ExtArgs> | null
  /**
   * Filter, which UsuarioCliente to fetch.
   */
  where: Prisma.UsuarioClienteWhereUniqueInput
}

/**
 * UsuarioCliente findFirst
 */
export type UsuarioClienteFindFirstArgs<ExtArgs extends runtime.Types.Extensions.InternalArgs = runtime.Types.Extensions.DefaultArgs> = {
  /**
   * Select specific fields to fetch from the UsuarioCliente
   */
  select?: Prisma.UsuarioClienteSelect<ExtArgs> | null
  /**
   * Omit specific fields from the UsuarioCliente
   */
  omit?: Prisma.UsuarioClienteOmit<ExtArgs> | null
  /**
   * Choose, which related nodes to fetch as well
   */
  include?: Prisma.UsuarioClienteInclude<ExtArgs> | null
  /**
   * Filter, which UsuarioCliente to fetch.
   */
  where?: Prisma.UsuarioClienteWhereInput
  /**
   * {@link https://www.prisma.io/docs/concepts/components/prisma-client/sorting Sorting Docs}
   * 
   * Determine the order of UsuarioClientes to fetch.
   */
  orderBy?: Prisma.UsuarioClienteOrderByWithRelationInput | Prisma.UsuarioClienteOrderByWithRelationInput[]
  /**
   * {@link https://www.prisma.io/docs/concepts/components/prisma-client/pagination#cursor-based-pagination Cursor Docs}
   * 
   * Sets the position for searching for UsuarioClientes.
   */
  cursor?: Prisma.UsuarioClienteWhereUniqueInput
  /**
   * {@link https://www.prisma.io/docs/concepts/components/prisma-client/pagination Pagination Docs}
   * 
   * Take `±n` UsuarioClientes from the position of the cursor.
   */
  take?: number
  /**
   * {@link https://www.prisma.io/docs/concepts/components/prisma-client/pagination Pagination Docs}
   * 
   * Skip the first `n` UsuarioClientes.
   */
  skip?: number
  /**
   * {@link https://www.prisma.io/docs/concepts/components/prisma-client/distinct Distinct Docs}
   * 
   * Filter by unique combinations of UsuarioClientes.
   */
  distinct?: Prisma.UsuarioClienteScalarFieldEnum | Prisma.UsuarioClienteScalarFieldEnum[]
}

/**
 * UsuarioCliente findFirstOrThrow
 */
export type UsuarioClienteFindFirstOrThrowArgs<ExtArgs extends runtime.Types.Extensions.InternalArgs = runtime.Types.Extensions.DefaultArgs> = {
  /**
   * Select specific fields to fetch from the UsuarioCliente
   */
  select?: Prisma.UsuarioClienteSelect<ExtArgs> | null
  /**
   * Omit specific fields from the UsuarioCliente
   */
  omit?: Prisma.UsuarioClienteOmit<ExtArgs> | null
  /**
   * Choose, which related nodes to fetch as well
   */
  include?: Prisma.UsuarioClienteInclude<ExtArgs> | null
  /**
   * Filter, which UsuarioCliente to fetch.
   */
  where?: Prisma.UsuarioClienteWhereInput
  /**
   * {@link https://www.prisma.io/docs/concepts/components/prisma-client/sorting Sorting Docs}
   * 
   * Determine the order of UsuarioClientes to fetch.
   */
  orderBy?: Prisma.UsuarioClienteOrderByWithRelationInput | Prisma.UsuarioClienteOrderByWithRelationInput[]
  /**
   * {@link https://www.prisma.io/docs/concepts/components/prisma-client/pagination#cursor-based-pagination Cursor Docs}
   * 
   * Sets the position for searching for UsuarioClientes.
   */
  cursor?: Prisma.UsuarioClienteWhereUniqueInput
  /**
   * {@link https://www.prisma.io/docs/concepts/components/prisma-client/pagination Pagination Docs}
   * 
   * Take `±n` UsuarioClientes from the position of the cursor.
   */
  take?: number
  /**
   * {@link https://www.prisma.io/docs/concepts/components/prisma-client/pagination Pagination Docs}
   * 
   * Skip the first `n` UsuarioClientes.
   */
  skip?: number
  /**
   * {@link https://www.prisma.io/docs/concepts/components/prisma-client/distinct Distinct Docs}
   * 
   * Filter by unique combinations of UsuarioClientes.
   */
  distinct?: Prisma.UsuarioClienteScalarFieldEnum | Prisma.UsuarioClienteScalarFieldEnum[]
}

/**
 * UsuarioCliente findMany
 */
export type UsuarioClienteFindManyArgs<ExtArgs extends runtime.Types.Extensions.InternalArgs = runtime.Types.Extensions.DefaultArgs> = {
  /**
   * Select specific fields to fetch from the UsuarioCliente
   */
  select?: Prisma.UsuarioClienteSelect<ExtArgs> | null
  /**
   * Omit specific fields from the UsuarioCliente
   */
  omit?: Prisma.UsuarioClienteOmit<ExtArgs> | null
  /**
   * Choose, which related nodes to fetch as well
   */
  include?: Prisma.UsuarioClienteInclude<ExtArgs> | null
  /**
   * Filter, which UsuarioClientes to fetch.
   */
  where?: Prisma.UsuarioClienteWhereInput
  /**
   * {@link https://www.prisma.io/docs/concepts/components/prisma-client/sorting Sorting Docs}
   * 
   * Determine the order of UsuarioClientes to fetch.
   */
  orderBy?: Prisma.UsuarioClienteOrderByWithRelationInput | Prisma.UsuarioClienteOrderByWithRelationInput[]
  /**
   * {@link https://www.prisma.io/docs/concepts/components/prisma-client/pagination#cursor-based-pagination Cursor Docs}
   * 
   * Sets the position for listing UsuarioClientes.
   */
  cursor?: Prisma.UsuarioClienteWhereUniqueInput
  /**
   * {@link https://www.prisma.io/docs/concepts/components/prisma-client/pagination Pagination Docs}
   * 
   * Take `±n` UsuarioClientes from the position of the cursor.
   */
  take?: number
  /**
   * {@link https://www.prisma.io/docs/concepts/components/prisma-client/pagination Pagination Docs}
   * 
   * Skip the first `n` UsuarioClientes.
   */
  skip?: number
  distinct?: Prisma.UsuarioClienteScalarFieldEnum | Prisma.UsuarioClienteScalarFieldEnum[]
}

/**
 * UsuarioCliente create
 */
export type UsuarioClienteCreateArgs<ExtArgs extends runtime.Types.Extensions.InternalArgs = runtime.Types.Extensions.DefaultArgs> = {
  /**
   * Select specific fields to fetch from the UsuarioCliente
   */
  select?: Prisma.UsuarioClienteSelect<ExtArgs> | null
  /**
   * Omit specific fields from the UsuarioCliente
   */
  omit?: Prisma.UsuarioClienteOmit<ExtArgs> | null
  /**
   * Choose, which related nodes to fetch as well
   */
  include?: Prisma.UsuarioClienteInclude<ExtArgs> | null
  /**
   * The data needed to create a UsuarioCliente.
   */
  data: Prisma.XOR<Prisma.UsuarioClienteCreateInput, Prisma.UsuarioClienteUncheckedCreateInput>
}

/**
 * UsuarioCliente createMany
 */
export type UsuarioClienteCreateManyArgs<ExtArgs extends runtime.Types.Extensions.InternalArgs = runtime.Types.Extensions.DefaultArgs> = {
  /**
   * The data used to create many UsuarioClientes.
   */
  data: Prisma.UsuarioClienteCreateManyInput | Prisma.UsuarioClienteCreateManyInput[]
  skipDuplicates?: boolean
}

/**
 * UsuarioCliente update
 */
export type UsuarioClienteUpdateArgs<ExtArgs extends runtime.Types.Extensions.InternalArgs = runtime.Types.Extensions.DefaultArgs> = {
  /**
   * Select specific fields to fetch from the UsuarioCliente
   */
  select?: Prisma.UsuarioClienteSelect<ExtArgs> | null
  /**
   * Omit specific fields from the UsuarioCliente
   */
  omit?: Prisma.UsuarioClienteOmit<ExtArgs> | null
  /**
   * Choose, which related nodes to fetch as well
   */
  include?: Prisma.UsuarioClienteInclude<ExtArgs> | null
  /**
   * The data needed to update a UsuarioCliente.
   */
  data: Prisma.XOR<Prisma.UsuarioClienteUpdateInput, Prisma.UsuarioClienteUncheckedUpdateInput>
  /**
   * Choose, which UsuarioCliente to update.
   */
  where: Prisma.UsuarioClienteWhereUniqueInput
}

/**
 * UsuarioCliente updateMany
 */
export type UsuarioClienteUpdateManyArgs<ExtArgs extends runtime.Types.Extensions.InternalArgs = runtime.Types.Extensions.DefaultArgs> = {
  /**
   * The data used to update UsuarioClientes.
   */
  data: Prisma.XOR<Prisma.UsuarioClienteUpdateManyMutationInput, Prisma.UsuarioClienteUncheckedUpdateManyInput>
  /**
   * Filter which UsuarioClientes to update
   */
  where?: Prisma.UsuarioClienteWhereInput
  /**
   * Limit how many UsuarioClientes to update.
   */
  limit?: number
}

/**
 * UsuarioCliente upsert
 */
export type UsuarioClienteUpsertArgs<ExtArgs extends runtime.Types.Extensions.InternalArgs = runtime.Types.Extensions.DefaultArgs> = {
  /**
   * Select specific fields to fetch from the UsuarioCliente
   */
  select?: Prisma.UsuarioClienteSelect<ExtArgs> | null
  /**
   * Omit specific fields from the UsuarioCliente
   */
  omit?: Prisma.UsuarioClienteOmit<ExtArgs> | null
  /**
   * Choose, which related nodes to fetch as well
   */
  include?: Prisma.UsuarioClienteInclude<ExtArgs> | null
  /**
   * The filter to search for the UsuarioCliente to update in case it exists.
   */
  where: Prisma.UsuarioClienteWhereUniqueInput
  /**
   * In case the UsuarioCliente found by the `where` argument doesn't exist, create a new UsuarioCliente with this data.
   */
  create: Prisma.XOR<Prisma.UsuarioClienteCreateInput, Prisma.UsuarioClienteUncheckedCreateInput>
  /**
   * In case the UsuarioCliente was found with the provided `where` argument, update it with this data.
   */
  update: Prisma.XOR<Prisma.UsuarioClienteUpdateInput, Prisma.UsuarioClienteUncheckedUpdateInput>
}

/**
 * UsuarioCliente delete
 */
export type UsuarioClienteDeleteArgs<ExtArgs extends runtime.Types.Extensions.InternalArgs = runtime.Types.Extensions.DefaultArgs> = {
  /**
   * Select specific fields to fetch from the UsuarioCliente
   */
  select?: Prisma.UsuarioClienteSelect<ExtArgs> | null
  /**
   * Omit specific fields from the UsuarioCliente
   */
  omit?: Prisma.UsuarioClienteOmit<ExtArgs> | null
  /**
   * Choose, which related nodes to fetch as well
   */
  include?: Prisma.UsuarioClienteInclude<ExtArgs> | null
  /**
   * Filter which UsuarioCliente to delete.
   */
  where: Prisma.UsuarioClienteWhereUniqueInput
}

/**
 * UsuarioCliente deleteMany
 */
export type UsuarioClienteDeleteManyArgs<ExtArgs extends runtime.Types.Extensions.InternalArgs = runtime.Types.Extensions.DefaultArgs> = {
  /**
   * Filter which UsuarioClientes to delete
   */
  where?: Prisma.UsuarioClienteWhereInput
  /**
   * Limit how many UsuarioClientes to delete.
   */
  limit?: number
}

/**
 * UsuarioCliente without action
 */
export type UsuarioClienteDefaultArgs<ExtArgs extends runtime.Types.Extensions.InternalArgs = runtime.Types.Extensions.DefaultArgs> = {
  /**
   * Select specific fields to fetch from the UsuarioCliente
   */
  select?: Prisma.UsuarioClienteSelect<ExtArgs> | null
  /**
   * Omit specific fields from the UsuarioCliente
   */
  omit?: Prisma.UsuarioClienteOmit<ExtArgs> | null
  /**
   * Choose, which related nodes to fetch as well
   */
  include?: Prisma.UsuarioClienteInclude<ExtArgs> | null
}
