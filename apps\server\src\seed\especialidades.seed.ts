import { prisma } from "../lib/prisma";

const especialidades = [
  { nome: "Clínica Médica", descricao: "Medicina interna geral" },
  { nome: "Cirurgia Geral", descricao: "Procedimentos cirúrgicos gerais" },
  { nome: "Pediatria", descricao: "Cuidados médicos para crianças" },
  { nome: "Ginecologia e Obstetrícia", descricao: "Saúde da mulher" },
  { nome: "Cardiologia", descricao: "Doenças do coração" },
  { nome: "Ortopedia", descricao: "Sistema musculoesquelético" },
  { nome: "Neurologia", descricao: "Sistema nervoso" },
  { nome: "Psiquiatria", descricao: "Saúde mental" },
  { nome: "Anestesiologia", descricao: "Anestesia e controle da dor" },
  { nome: "Radiologia", descricao: "Diagnóstico por imagem" },
  { nome: "Dermatologia", descricao: "Doenças da pele" },
  { nome: "Oftalmologia", descricao: "Doenças dos olhos" },
  { nome: "Otorrinolaringologia", descricao: "Ouvido, nariz e garganta" },
  { nome: "Urologia", descricao: "Sistema urinário" },
  { nome: "Pneumologia", descricao: "Sistema respiratório" },
  { nome: "Gastroenterologia", descricao: "Sistema digestivo" },
  { nome: "Endocrinologia", descricao: "Sistema endócrino e hormônios" },
  { nome: "Nefrologia", descricao: "Doenças dos rins" },
  { nome: "Hematologia", descricao: "Doenças do sangue" },
  { nome: "Reumatologia", descricao: "Doenças reumáticas" },
  { nome: "Infectologia", descricao: "Doenças infecciosas" },
  { nome: "Medicina Intensiva", descricao: "Cuidados intensivos" },
  { nome: "Medicina de Emergência", descricao: "Atendimento de urgência" },
  { nome: "Medicina do Trabalho", descricao: "Saúde ocupacional" },
  { nome: "Medicina Esportiva", descricao: "Medicina do esporte" },
  { nome: "Geriatria", descricao: "Saúde do idoso" },
  { nome: "Oncologia", descricao: "Tratamento do câncer" },
  { nome: "Cirurgia Plástica", descricao: "Cirurgia reconstrutora e estética" },
  { nome: "Cirurgia Cardiovascular", descricao: "Cirurgia do coração" },
  { nome: "Neurocirurgia", descricao: "Cirurgia do sistema nervoso" },
  { nome: "Cirurgia Torácica", descricao: "Cirurgia do tórax" },
  { nome: "Cirurgia Vascular", descricao: "Cirurgia dos vasos sanguíneos" },
  { nome: "Cirurgia Pediátrica", descricao: "Cirurgia em crianças" },
  {
    nome: "Medicina Nuclear",
    descricao: "Diagnóstico e tratamento com radioisótopos",
  },
  { nome: "Patologia", descricao: "Diagnóstico de doenças" },
  { nome: "Medicina Legal", descricao: "Medicina forense" },
  { nome: "Fisioterapia", descricao: "Reabilitação física" },
  { nome: "Enfermagem", descricao: "Cuidados de enfermagem" },
  { nome: "Nutrição", descricao: "Orientação nutricional" },
  { nome: "Psicologia", descricao: "Saúde mental e comportamental" },
  { nome: "Fonoaudiologia", descricao: "Comunicação e audição" },
  { nome: "Odontologia", descricao: "Saúde bucal" },
  { nome: "Farmácia", descricao: "Assistência farmacêutica" },
];

export async function seedEspecialidades() {
  console.log("🌱 Seeding especialidades...");

  for (const especialidade of especialidades) {
    await prisma.especialidade.upsert({
      where: { nome: especialidade.nome },
      update: {},
      create: especialidade,
    });
  }

  console.log(`✅ ${especialidades.length} especialidades criadas/atualizadas`);
}

// Run if called directly
if (import.meta.url === `file://${process.argv[1]}`) {
  seedEspecialidades()
    .then(() => {
      console.log("✅ Seed concluído");
      process.exit(0);
    })
    .catch((error) => {
      console.error("❌ Erro no seed:", error);
      process.exit(1);
    })
    .finally(() => {
      prisma.$disconnect();
    });
}
