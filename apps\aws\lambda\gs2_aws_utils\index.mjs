import { handler as email<PERSON><PERSON><PERSON> } from "./mail.mjs";
import { checkAuth } from "./auth-middleware.mjs";

const actions = {
  email: emailHandler,
};

export const handler = async (event) => {
  // Check authorization first
  const authError = checkAuth(event);
  if (authError) {
    return authError;
  }
  
  // Parse body if it's a string (API Gateway)
  let body = event;
  if (event.body && typeof event.body === "string") {
    try {
      body = JSON.parse(event.body);
    } catch (error) {
      return {
        statusCode: 400,
        body: JSON.stringify({
          message: "Corpo da requisição inválido. Esperado JSON válido.",
          errorCode: "INVALID_JSON",
        }),
      };
    }
  }
  
  // Extract action from body
  const { action } = body;
  
  if (!action) {
    return {
      statusCode: 400,
      body: JSON.stringify({
        message: "Ação não especificada. Informe o campo 'action'.",
        errorCode: "MISSING_ACTION",
      }),
    };
  }
  
  // Check if action exists
  if (!actions[action]) {
    return {
      statusCode: 400,
      body: JSON.stringify({
        message: `Ação '${action}' não reconhecida. Ações disponíveis: ${Object.keys(actions).join(", ")}`,
        errorCode: "INVALID_ACTION",
      }),
    };
  }
  
  // Execute the action handler
  try {
    return await actions[action](body);
  } catch (error) {
    console.error(`Erro ao executar ação ${action}:`, error);
    return {
      statusCode: 500,
      body: JSON.stringify({
        message: "Erro interno ao processar requisição.",
        errorCode: "INTERNAL_ERROR",
      }),
    };
  }
};
