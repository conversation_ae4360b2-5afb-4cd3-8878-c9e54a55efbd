/**
 * Validates email address format
 * @param {string} email - Email address to validate
 * @returns {boolean} True if valid email format
 */
export const validateEmail = (email) => {
  if (!email || typeof email !== "string") return false;
  
  const emailRegex = /^[^\s@]+@[^\s@]+\.[^\s@]+$/;
  return emailRegex.test(email.trim());
};

/**
 * Sanitizes HTML content to prevent XSS attacks
 * Allows only safe HTML tags and attributes
 * @param {string} html - HTML content to sanitize
 * @returns {string} Sanitized HTML
 */
export const sanitizeHtml = (html) => {
  if (!html || typeof html !== "string") return "";
  
  // Basic HTML sanitization - in production, use a library like DOMPurify
  // This is a simple implementation that escapes potentially dangerous characters
  return html
    .replace(/&(?!(amp|lt|gt|quot|#39|#x2F);)/g, "&amp;")
    .replace(/</g, "&lt;")
    .replace(/>/g, "&gt;")
    .replace(/"/g, "&quot;")
    .replace(/'/g, "&#39;")
    .replace(/\//g, "&#x2F;");
};

/**
 * Validates subject line
 * @param {string} subject - Email subject
 * @returns {boolean} True if valid subject
 */
export const validateSubject = (subject) => {
  if (!subject || typeof subject !== "string") return false;
  
  const trimmed = subject.trim();
  return trimmed.length > 0 && trimmed.length <= 200;
};

/**
 * Validates email body content
 * @param {string} body - Email body
 * @returns {boolean} True if valid body
 */
export const validateBody = (body) => {
  if (!body || typeof body !== "string") return false;
  
  const trimmed = body.trim();
  return trimmed.length > 0 && trimmed.length <= 100000; // 100KB limit
};