import { createFileRoute, useNavigate } from "@tanstack/react-router";
import { useState } from "react";
import { useMutation } from "@tanstack/react-query";
import { api } from "@/lib/api";
import { Button } from "@/components/ui/button";
import { Card, CardContent, CardDescription, CardFooter, CardHeader, CardTitle } from "@/components/ui/card";
import { ScrollArea } from "@/components/ui/scroll-area";
import { Checkbox } from "@/components/ui/checkbox";
import { toast } from "sonner";
import { FileText, Shield, UserCheck } from "lucide-react";

function TermosUso() {
  const navigate = useNavigate();
  const [accepted, setAccepted] = useState(false);

  const acceptTermsMutation = useMutation({
    mutationFn: async () => {
      // Aqui você pode adicionar uma chamada à API para registrar que o usuário aceitou os termos
      // Por exemplo: await api.post("/usuarios/aceitar-termos");
      return { success: true };
    },
    onSuccess: () => {
      toast.success("Termos aceitos com sucesso!");
      // Redirecionar para a tela de onboarding pendente
      navigate({ to: "/onboarding-pendente" });
    },
    onError: () => {
      toast.error("Erro ao aceitar os termos. Tente novamente.");
    },
  });

  const handleAcceptTerms = () => {
    if (!accepted) {
      toast.error("Você precisa aceitar os termos para continuar.");
      return;
    }
    acceptTermsMutation.mutate();
  };

  return (
    <div className="min-h-screen flex items-center justify-center from-blue-50 to-indigo-100 p-4">
      <Card className="w-full max-w-3xl">
        <CardHeader>
          <div className="flex items-center gap-2 mb-2">
            <FileText className="h-6 w-6 text-blue-600" />
            <CardTitle className="text-2xl">Termos de Uso e Política de Privacidade</CardTitle>
          </div>
          <CardDescription>Por favor, leia com atenção os termos e condições de uso da plataforma</CardDescription>
        </CardHeader>

        <CardContent>
          <ScrollArea className="h-96 w-full rounded-md border p-4">
            <div className="space-y-6 text-sm">
              <section>
                <h3 className="font-semibold text-base mb-2 flex items-center gap-2">
                  <Shield className="h-4 w-4" />
                  1. Termos de Uso
                </h3>
                <p className="text-muted-foreground mb-2">
                  Ao utilizar esta plataforma, você concorda com os seguintes termos:
                </p>
                <ul className="list-disc pl-5 space-y-1 text-muted-foreground">
                  <li>Usar a plataforma apenas para fins profissionais relacionados à saúde</li>
                  <li>Manter a confidencialidade das informações dos pacientes</li>
                  <li>Não compartilhar suas credenciais de acesso com terceiros</li>
                  <li>Reportar imediatamente qualquer uso não autorizado de sua conta</li>
                  <li>Cumprir com todas as leis e regulamentos aplicáveis</li>
                </ul>
              </section>

              <section>
                <h3 className="font-semibold text-base mb-2 flex items-center gap-2">
                  <UserCheck className="h-4 w-4" />
                  2. Responsabilidades do Profissional
                </h3>
                <p className="text-muted-foreground mb-2">
                  Como profissional de saúde cadastrado, você se compromete a:
                </p>
                <ul className="list-disc pl-5 space-y-1 text-muted-foreground">
                  <li>Manter suas informações profissionais atualizadas</li>
                  <li>Registrar corretamente suas atividades e plantões</li>
                  <li>Respeitar os horários e compromissos assumidos</li>
                  <li>Seguir os protocolos estabelecidos pela instituição</li>
                  <li>Utilizar a plataforma de forma ética e responsável</li>
                </ul>
              </section>

              <section>
                <h3 className="font-semibold text-base mb-2">3. Política de Privacidade</h3>
                <p className="text-muted-foreground mb-2">Nós nos comprometemos a proteger sua privacidade:</p>
                <ul className="list-disc pl-5 space-y-1 text-muted-foreground">
                  <li>Seus dados pessoais são criptografados e armazenados com segurança</li>
                  <li>Não compartilhamos suas informações com terceiros sem seu consentimento</li>
                  <li>Você tem direito de acessar, corrigir ou excluir seus dados</li>
                  <li>Utilizamos cookies apenas para melhorar sua experiência na plataforma</li>
                  <li>Cumprimos com a Lei Geral de Proteção de Dados (LGPD)</li>
                </ul>
              </section>

              <section>
                <h3 className="font-semibold text-base mb-2">4. Uso de Dados</h3>
                <p className="text-muted-foreground mb-2">Seus dados serão utilizados para:</p>
                <ul className="list-disc pl-5 space-y-1 text-muted-foreground">
                  <li>Gerenciar seus plantões e escalas de trabalho</li>
                  <li>Processar pagamentos e antecipações</li>
                  <li>Comunicar informações importantes sobre seus serviços</li>
                  <li>Melhorar continuamente nossos serviços</li>
                  <li>Cumprir obrigações legais e regulatórias</li>
                </ul>
              </section>

              <section>
                <h3 className="font-semibold text-base mb-2">5. Segurança</h3>
                <p className="text-muted-foreground">
                  Implementamos medidas técnicas e organizacionais apropriadas para proteger seus dados contra acesso
                  não autorizado, alteração, divulgação ou destruição. Isso inclui criptografia de dados, controles de
                  acesso rigorosos e monitoramento contínuo de segurança.
                </p>
              </section>

              <section>
                <h3 className="font-semibold text-base mb-2">6. Alterações nos Termos</h3>
                <p className="text-muted-foreground">
                  Reservamo-nos o direito de modificar estes termos a qualquer momento. Você será notificado sobre
                  quaisquer alterações significativas e terá a oportunidade de revisar e aceitar os novos termos antes
                  de continuar usando a plataforma.
                </p>
              </section>

              <section>
                <h3 className="font-semibold text-base mb-2">7. Contato</h3>
                <p className="text-muted-foreground">
                  Se você tiver dúvidas sobre estes termos ou nossa política de privacidade, entre em contato conosco
                  através do suporte da plataforma ou pelo e-mail de atendimento.
                </p>
              </section>
            </div>
          </ScrollArea>

          <div className="flex items-center space-x-2 mt-4">
            <Checkbox
              id="accept-terms"
              checked={accepted}
              onCheckedChange={(checked) => setAccepted(checked as boolean)}
            />
            <label
              htmlFor="accept-terms"
              className="text-sm font-medium leading-none peer-disabled:cursor-not-allowed peer-disabled:opacity-70 cursor-pointer"
            >
              Li e aceito os termos de uso e política de privacidade
            </label>
          </div>
        </CardContent>

        <CardFooter>
          <Button onClick={handleAcceptTerms} className="w-full" disabled={!accepted || acceptTermsMutation.isPending}>
            {acceptTermsMutation.isPending ? "Processando..." : "Aceitar e Continuar"}
          </Button>
        </CardFooter>
      </Card>
    </div>
  );
}

export const Route = createFileRoute("/termos-uso")({
  component: TermosUso,
});
