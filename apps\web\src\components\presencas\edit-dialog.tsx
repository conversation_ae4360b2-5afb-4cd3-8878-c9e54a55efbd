import { useEffect, useState } from "react";
import {
  <PERSON><PERSON>,
  DialogContent,
  DialogDescription,
  DialogFooter,
  DialogHeader,
  DialogTitle,
} from "@/components/ui/dialog";
import { Button } from "@/components/ui/button";
import { Input } from "@/components/ui/input";
import { Label } from "@/components/ui/label";
import { Textarea } from "@/components/ui/textarea";
import { Select, SelectContent, SelectItem, SelectTrigger, SelectValue } from "@/components/ui/select";
import { getDaysInMonth } from "date-fns";
import { createLocalDate, parseISO } from "@/lib/utils";

import type { Plantao } from "@/lib/api";

interface RegistroParaEditar {
  dia: number;
  diaPlantaoId: number;
  registroId: number;
  entrada: {
    id: number;
    horario: string;
    observacao?: string;
  } | null;
  saida: {
    id: number;
    horario: string;
    observacao?: string;
  } | null;
  intervalo?: string;
  observacoes?: string;
  tempoGlosado?: number;
  justificativaGlosa?: string;
}

interface EditDialogProps {
  isOpen: boolean;
  onOpenChange: (open: boolean) => void;
  selectedPresenca: RegistroParaEditar | null;
  selectedDia: number | null;
  currentMonth: number;
  currentYear: number;
  plantao: Plantao;
  isLoading: boolean;
  onSubmit: (formData: FormData) => void;
  onClose: () => void;
}

const diasSemana = ["Dom", "Seg", "Ter", "Qua", "Qui", "Sex", "Sáb"];

export function EditDialog({
  isOpen,
  onOpenChange,
  selectedPresenca,
  selectedDia,
  currentMonth,
  currentYear,
  plantao,
  isLoading,
  onSubmit,
  onClose,
}: EditDialogProps) {
  const [formDia, setFormDia] = useState<string>("");

  useEffect(() => {
    if (isOpen) {
      if (selectedPresenca) {
        setFormDia(selectedPresenca.dia.toString());
      } else if (selectedDia) {
        setFormDia(selectedDia.toString());
      } else {
        setFormDia("");
      }
    }
  }, [isOpen, selectedPresenca, selectedDia]);

  const formatTime = (dateString: string | null | undefined) => {
    if (!dateString) return "";
    const date = parseISO(dateString);
    return date.toTimeString().slice(0, 5);
  };

  const getDiasDisponiveisParaSelecao = () => {
    const diasDoMes = getDaysInMonth(createLocalDate(currentYear, currentMonth, 1));
    const dias = [];

    for (let dia = 1; dia <= diasDoMes; dia++) {
      const date = createLocalDate(currentYear, currentMonth, dia);
      const diaSemana = date.getDay();
      dias.push({ dia, diaSemana });
    }

    return dias;
  };

  return (
    <Dialog open={isOpen} onOpenChange={onOpenChange}>
      <DialogContent>
        <DialogHeader>
          <DialogTitle>
            {selectedPresenca && selectedPresenca.registroId
              ? "Editar Registro de Ponto"
              : "Adicionar Novo Registro de Ponto"}
          </DialogTitle>
          <DialogDescription>
            {selectedDia
              ? `${selectedPresenca && selectedPresenca.registroId ? "Editando" : "Adicionando"} registro para o dia ${selectedDia}/${currentMonth.toString().padStart(2, "0")}/${currentYear}`
              : "Selecione o dia e horários do registro"}
          </DialogDescription>
        </DialogHeader>
        <form
          onSubmit={(e) => {
            e.preventDefault();
            const formData = new FormData(e.currentTarget);
            onSubmit(formData);
          }}
        >
          <div className="space-y-4">
            <div className="space-y-2">
              <Label htmlFor="dia">Dia</Label>
              <Select name="dia" value={formDia} onValueChange={setFormDia} disabled={!!selectedPresenca}>
                <SelectTrigger>
                  <SelectValue placeholder="Selecione o dia" />
                </SelectTrigger>
                <SelectContent>
                  {getDiasDisponiveisParaSelecao().map(({ dia, diaSemana }) => (
                    <SelectItem key={dia} value={dia.toString()}>
                      {dia.toString().padStart(2, "0")} - {diasSemana[diaSemana]}
                    </SelectItem>
                  ))}
                </SelectContent>
              </Select>
            </div>

            <div className="grid grid-cols-2 gap-4">
              <div className="space-y-2">
                <Label htmlFor="horaEntrada">Hora Entrada</Label>
                <Input
                  id="horaEntrada"
                  name="horaEntrada"
                  type="time"
                  defaultValue={
                    selectedPresenca?.entrada ? formatTime(selectedPresenca.entrada.horario) : plantao.horaInicio || ""
                  }
                />
              </div>

              <div className="space-y-2">
                <Label htmlFor="horaSaida">Hora Saída</Label>
                <Input
                  id="horaSaida"
                  name="horaSaida"
                  type="time"
                  defaultValue={
                    selectedPresenca?.saida ? formatTime(selectedPresenca.saida.horario) : plantao.horaFim || ""
                  }
                />
              </div>
            </div>

            <div className="space-y-2">
              <Label htmlFor="intervalo">Intervalo</Label>
              <Input
                id="intervalo"
                name="intervalo"
                type="time"
                defaultValue={selectedPresenca?.intervalo || plantao.intervalo || "01:00"}
              />
            </div>

            <div className="space-y-2">
              <Label htmlFor="observacao">Observação</Label>
              <Textarea
                id="observacao"
                name="observacao"
                placeholder="Observações adicionais..."
                defaultValue={
                  selectedPresenca?.entrada?.observacao ||
                  selectedPresenca?.saida?.observacao ||
                  selectedPresenca?.observacoes ||
                  ""
                }
                rows={3}
              />
            </div>
          </div>

          <DialogFooter className="mt-6">
            <Button type="button" variant="outline" onClick={onClose}>
              Cancelar
            </Button>
            <Button type="submit" disabled={isLoading}>
              {selectedPresenca ? "Salvar Alterações" : "Adicionar Presença"}
            </Button>
          </DialogFooter>
        </form>
      </DialogContent>
    </Dialog>
  );
}
