import { Link } from "@tanstack/react-router";
import { createFileRoute } from "@tanstack/react-router";
import { useQuery } from "@tanstack/react-query";
import { requireAdminRole } from "@/lib/route-guards";
import { useState } from "react";
import { api, type Plantao, type PaginatedResponse } from "@/lib/api";
import { Button } from "@/components/ui/button";
import { Card, CardContent, CardHeader, CardTitle } from "@/components/ui/card";
import { Badge } from "@/components/ui/badge";
import { Skeleton } from "@/components/ui/skeleton";
import { PlantaoStatistics } from "@/components/plantao-statistics";
import {
  Calendar,
  Plus,
  Users,
  Building2,
  MapPin,
  DollarSign,
  Clock,
  Copy,
  Eye,
  Pencil,
  CheckCircle2,
  CircleDashed,
  Filter,
  User,
} from "lucide-react";
import { toast } from "sonner";
import { Select, SelectContent, SelectItem, SelectTrigger, SelectValue } from "@/components/ui/select";
import { DateRangePicker } from "@/components/ui/date-picker";
import { createLocalDate, createCurrentLocalDate, getDaysInMonthForDate, parseISO } from "@/lib/utils";
import { format, startOfMonth, endOfMonth } from "date-fns";

const diasSemana = ["Dom", "Seg", "Ter", "Qua", "Qui", "Sex", "Sáb"];

function PlantoesIndex() {
  const hoje = createCurrentLocalDate();
  const [dateRange, setDateRange] = useState<{ from: Date; to?: Date }>(() => {
    const startOfCurrentMonth = startOfMonth(hoje);
    const endOfCurrentMonth = endOfMonth(hoje);
    return { from: startOfCurrentMonth, to: endOfCurrentMonth };
  });
  const [viewMode, setViewMode] = useState<"calendar" | "list">(() => {
    const saved = localStorage.getItem("plantoes-view-mode");
    return (saved as "calendar" | "list") || "calendar";
  });
  const [statusFilter, setStatusFilter] = useState<"all" | "completed" | "ongoing">("all");

  const {
    data: plantoes,
    isLoading,
    refetch,
  } = useQuery<PaginatedResponse<Plantao>>({
    queryKey: ["plantoes", dateRange, statusFilter],
    queryFn: () => {
      const params: any = { limit: 100 };
      if (dateRange.from) {
        params.dataInicial = format(dateRange.from, "yyyy-MM-dd");
      }
      if (dateRange.to) {
        params.dataFinal = format(dateRange.to, "yyyy-MM-dd");
      }
      if (statusFilter === "completed") {
        params.status = "concluido";
      } else if (statusFilter === "ongoing") {
        params.status = "em_andamento";
      }
      return api.get("/plantoes", params);
    },
  });

  const handleDateRangeChange = (newRange: { from: Date; to?: Date } | undefined) => {
    if (newRange) {
      setDateRange(newRange);
    }
  };

  const formatDateRangeTitle = () => {
    if (!dateRange.from) return "Período";
    if (!dateRange.to) return format(dateRange.from, "MMMM yyyy");
    if (format(dateRange.from, "MM/yyyy") === format(dateRange.to, "MM/yyyy")) {
      return format(dateRange.from, "MMMM yyyy");
    }
    return `${format(dateRange.from, "MM/yyyy")} - ${format(dateRange.to, "MM/yyyy")}`;
  };

  const handleReplicate = async (plantaoId: string) => {
    try {
      await api.post(`/plantoes/${plantaoId}/replicar`);
      toast.success("Plantão replicado para o próximo mês");
      refetch();
    } catch (error) {
      toast.error("Erro ao replicar plantão");
    }
  };

  const getDaysInMonthCount = (month: number, year: number) => {
    return getDaysInMonthForDate(year, month);
  };

  const getFirstDayOfMonth = (month: number, year: number) => {
    return createLocalDate(year, month, 1).getDay();
  };

  const formatCurrency = (value: number) => {
    return new Intl.NumberFormat("pt-BR", {
      style: "currency",
      currency: "BRL",
    }).format(value);
  };

  const renderCalendarView = () => {
    if (!dateRange.from) return null;

    const currentMonth = dateRange.from.getMonth() + 1;
    const currentYear = dateRange.from.getFullYear();
    const daysInMonth = getDaysInMonthCount(currentMonth, currentYear);
    const firstDay = getFirstDayOfMonth(currentMonth, currentYear);
    const days = [];

    // Empty cells for days before month starts
    for (let i = 0; i < firstDay; i++) {
      days.push(<div key={`empty-${i}`} className="p-2"></div>);
    }

    // Days of the month
    for (let day = 1; day <= daysInMonth; day++) {
      const dataStr = `${currentYear}-${currentMonth.toString().padStart(2, "0")}-${day.toString().padStart(2, "0")}`;
      const dayPlantoes =
        plantoes?.data.filter((plantao) => {
          return plantao.diasPlantao?.some((d) => {
            // Format the Date object to YYYY-MM-DD for comparison
            const formattedDate = d.data ? format(d.data, "yyyy-MM-dd") : null;
            return formattedDate === dataStr;
          });
        }) || [];

      days.push(
        <div key={day} className="min-h-[100px] border rounded-lg p-2 hover:bg-accent/50 transition-colors">
          <div className="font-semibold text-sm mb-1">{day}</div>
          <div className="space-y-1">
            {dayPlantoes.slice(0, 3).map((plantao) => (
              <Link key={plantao.id} to={"/plantoes/$uuid"} params={{ uuid: plantao.uuid }} className="block">
                <div className="text-xs p-1 bg-primary/10 rounded truncate hover:bg-primary/20">
                  <span className="font-medium">{plantao.profissional?.usuario?.nome}</span>
                </div>
              </Link>
            ))}
            {dayPlantoes.length > 3 && (
              <div className="text-xs text-muted-foreground">+{dayPlantoes.length - 3} mais</div>
            )}
          </div>
        </div>
      );
    }

    return (
      <div className="grid grid-cols-7 gap-2">
        {diasSemana.map((dia) => (
          <div key={dia} className="text-center font-semibold text-sm py-2">
            {dia}
          </div>
        ))}
        {days}
      </div>
    );
  };

  const renderListView = () => {
    return (
      <div className="space-y-4">
        {isLoading ? (
          Array.from({ length: 3 }).map((_, i) => (
            <Card key={i}>
              <CardContent className="p-4">
                <Skeleton className="h-20 w-full" />
              </CardContent>
            </Card>
          ))
        ) : plantoes?.data.length === 0 ? (
          <Card>
            <CardContent className="text-center py-12">
              <Calendar className="mx-auto h-12 w-12 text-muted-foreground/20 mb-2" />
              <p className="text-muted-foreground">Nenhum plantão para este período</p>
            </CardContent>
          </Card>
        ) : (
          plantoes?.data.map((plantao) => (
            <Card key={plantao.id} className="hover:shadow-lg transition-shadow">
              <CardContent className="p-4">
                <div className="flex justify-between items-start">
                  <div className="space-y-2 flex-1">
                    <div className="flex items-center gap-2">
                      <Users className="h-4 w-4 text-muted-foreground" />
                      <span className="font-semibold">{plantao.profissional?.usuario?.nome}</span>
                      <Badge variant="outline">{plantao.modalidadeTrabalho}</Badge>
                      {plantao.concluidoEm ? (
                        <Badge variant="default" className="gap-1">
                          <CheckCircle2 className="h-3 w-3" />
                          Concluído
                        </Badge>
                      ) : (
                        <Badge variant="secondary" className="gap-1">
                          <CircleDashed className="h-3 w-3" />
                          Em andamento
                        </Badge>
                      )}
                    </div>
                    <div className="flex items-center gap-4 text-sm text-muted-foreground">
                      <div className="flex items-center gap-1">
                        <Building2 className="h-3 w-3" />
                        {plantao.cliente?.nome}
                      </div>
                      <div className="flex items-center gap-1">
                        <MapPin className="h-3 w-3" />
                        {plantao.localAtendimento?.nome}
                      </div>
                    </div>
                    <div className="flex items-center gap-4 text-sm">
                      <div className="flex items-center gap-1">
                        <User className="h-3 w-3" />
                        <span className="font-medium">{plantao.profissional?.usuario?.nome}</span>
                      </div>
                      {plantao.horaInicio && plantao.horaFim && (
                        <div className="flex items-center gap-1">
                          <Clock className="h-3 w-3" />
                          {plantao.horaInicio} - {plantao.horaFim}
                        </div>
                      )}
                    </div>
                  </div>
                  <div className="flex gap-2">
                    <Link to={"/plantoes/$uuid"} params={{ uuid: plantao.uuid }}>
                      <Button variant="ghost" size="icon">
                        <Eye className="h-4 w-4" />
                      </Button>
                    </Link>
                    <Link to={"/plantoes/$uuid/editar"} params={{ uuid: plantao.uuid }}>
                      <Button variant="ghost" size="icon">
                        <Pencil className="h-4 w-4" />
                      </Button>
                    </Link>
                    <Button variant="ghost" size="icon" onClick={() => handleReplicate(plantao.uuid)}>
                      <Copy className="h-4 w-4" />
                    </Button>
                  </div>
                </div>
              </CardContent>
            </Card>
          ))
        )}
      </div>
    );
  };

  return (
    <div className="space-y-6">
      <div className="flex justify-between items-center">
        <div>
          <h1 className="text-3xl font-bold">Plantões</h1>
          <p className="text-muted-foreground">Gerencie os plantões de trabalho dos profissionais</p>
        </div>
        <Link to="/plantoes/novo">
          <Button>
            <Plus className="mr-2 h-4 w-4" />
            Novo Plantão
          </Button>
        </Link>
      </div>

      {/* Estatísticas de Plantões */}
      {/* {dateRange.from && <PlantaoStatistics mes={dateRange.from.getMonth() + 1} ano={dateRange.from.getFullYear()} />} */}

      <Card>
        <CardHeader>
          <div className="flex justify-between items-center">
            <CardTitle>Plantões - {formatDateRangeTitle()}</CardTitle>
            <div className="flex items-center gap-4">
              <Select value={statusFilter} onValueChange={(v: any) => setStatusFilter(v)}>
                <SelectTrigger className="w-[180px]">
                  <SelectValue />
                </SelectTrigger>
                <SelectContent>
                  <SelectItem value="all">
                    <div className="flex items-center gap-2">
                      <Filter className="h-4 w-4" />
                      Todos
                    </div>
                  </SelectItem>
                  <SelectItem value="ongoing">
                    <div className="flex items-center gap-2">
                      <CircleDashed className="h-4 w-4" />
                      Em andamento
                    </div>
                  </SelectItem>
                  <SelectItem value="completed">
                    <div className="flex items-center gap-2">
                      <CheckCircle2 className="h-4 w-4" />
                      Concluídos
                    </div>
                  </SelectItem>
                </SelectContent>
              </Select>
              <Select
                value={viewMode}
                onValueChange={(v: any) => {
                  setViewMode(v);
                  localStorage.setItem("plantoes-view-mode", v);
                }}
              >
                <SelectTrigger className="w-[140px]">
                  <SelectValue />
                </SelectTrigger>
                <SelectContent>
                  <SelectItem value="calendar">
                    <div className="flex items-center gap-2">
                      <Calendar className="h-4 w-4" />
                      Calendário
                    </div>
                  </SelectItem>
                  <SelectItem value="list">
                    <div className="flex items-center gap-2">
                      <Users className="h-4 w-4" />
                      Lista
                    </div>
                  </SelectItem>
                </SelectContent>
              </Select>
              <div className="flex items-center gap-2">
                <div className="w-[280px]">
                  <DateRangePicker
                    value={dateRange}
                    onChange={handleDateRangeChange}
                    placeholder="Selecione o período"
                  />
                </div>
              </div>
            </div>
          </div>
        </CardHeader>
        <CardContent>{viewMode === "calendar" ? renderCalendarView() : renderListView()}</CardContent>
      </Card>
    </div>
  );
}

export const Route = createFileRoute("/plantoes/")({
  component: PlantoesIndex,
  beforeLoad: async () => {
    await requireAdminRole();
  },
});
