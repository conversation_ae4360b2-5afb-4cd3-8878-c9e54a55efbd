import { useNavigate, useSearch } from "@tanstack/react-router";
import { createFileRoute } from "@tanstack/react-router";
import { useMutation, useQuery } from "@tanstack/react-query";
import { requireAdminRole } from "@/lib/route-guards";
import { useForm } from "@tanstack/react-form";
import { api, type <PERSON>ao } from "@/lib/api";
import { Button } from "@/components/ui/button";
import { Input } from "@/components/ui/input";
import { Label } from "@/components/ui/label";
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from "@/components/ui/card";
import { Switch } from "@/components/ui/switch";
import { ArrowLeft, Save, Loader2, Calendar, CheckCircle2 } from "lucide-react";
import { toast } from "sonner";
import { Link } from "@tanstack/react-router";
import { useState } from "react";
import { Select, SelectContent, SelectItem, SelectTrigger, SelectValue } from "@/components/ui/select";
import { Textarea } from "@/components/ui/textarea";
import { Badge } from "@/components/ui/badge";
import { Checkbox } from "@/components/ui/checkbox";
import { createLocalDate, createCurrentLocalDate, getDaysInMonthForDate } from "@/lib/utils";

interface FechamentoForm {
  escalaId: string;
  mes: number;
  ano: number;
  diasTrabalhados: number;
  valorTotal: number;
  observacoes?: string;
  diasConfirmados: number[];
}

const meses = [
  "Janeiro",
  "Fevereiro",
  "Março",
  "Abril",
  "Maio",
  "Junho",
  "Julho",
  "Agosto",
  "Setembro",
  "Outubro",
  "Novembro",
  "Dezembro",
];

const diasSemana = ["D", "S", "T", "Q", "Q", "S", "S"];

function FechamentoNovo() {
  const navigate = useNavigate();
  const hoje = createCurrentLocalDate();
  const [selectedEscala, setSelectedEscala] = useState<Plantao | null>(null);

  const { data: escalas } = useQuery({
    queryKey: ["plantoes-fechamento"],
    queryFn: () =>
      api.get<{ data: Plantao[] }>("/plantoes", {
        limit: 100,
        ativo: true,
        mes: hoje.getMonth() + 1,
        ano: hoje.getFullYear(),
      }),
  });

  const mutation = useMutation({
    mutationFn: async (data: FechamentoForm) => {
      // Since escalaId is now the UUID, we can use it directly
      const plantao = escalas?.data.find((e) => e.uuid === data.escalaId);
      if (!plantao) {
        throw new Error("Plantão não encontrado");
      }

      return api.post("/fechamentos", {
        plantaoUuid: data.escalaId, // This is now the UUID
        mes: data.mes,
        ano: data.ano,
        diasTrabalhados: data.diasTrabalhados,
        totalValor: data.valorTotal,
        diasPrevistos: plantao.diasPlantao?.length || 0,
        status: "PENDENTE",
        observacoes: data.observacoes,
      });
    },
    onSuccess: () => {
      toast.success("Fechamento criado com sucesso!");
      navigate({ to: "/fechamentos" });
    },
    onError: (error: any) => {
      toast.error(error.message || "Erro ao criar fechamento");
    },
  });

  const form = useForm({
    defaultValues: {
      escalaId: "",
      mes: hoje.getMonth() + 1,
      ano: hoje.getFullYear(),
      diasTrabalhados: 0,
      valorTotal: 0,
      observacoes: "",
      diasConfirmados: [],
    } as FechamentoForm,
    onSubmit: async ({ value }) => {
      if (value.diasConfirmados.length === 0) {
        toast.error("Confirme pelo menos um dia trabalhado");
        return;
      }
      mutation.mutate(value);
    },
  });

  const handleEscalaChange = (escalaUuid: string) => {
    form.setFieldValue("escalaId", escalaUuid);
    const plantao = escalas?.data.find((e) => e.uuid === escalaUuid);
    if (plantao) {
      setSelectedEscala(plantao);
      // Extrair mês e ano da dataInicial do plantão
      const dataInicial = new Date(plantao.dataInicial);
      form.setFieldValue("mes", dataInicial.getMonth() + 1);
      form.setFieldValue("ano", dataInicial.getFullYear());

      // Initialize with all scheduled days
      const diasEscalados = plantao.diasPlantao?.map((d) => new Date(String(d.data)).getDate()) || [];
      form.setFieldValue("diasConfirmados", diasEscalados);
      form.setFieldValue("diasTrabalhados", diasEscalados.length);

      // Calculate total value based on payment type
      let valorTotal = 0;
      if (plantao.tipoValor === "MENSAL" && plantao.valorBase) {
        valorTotal = plantao.valorBase;
      } else if ((plantao.tipoValor === "PLANTAO" || plantao.tipoValor === "DIARIA") && plantao.valorBase) {
        valorTotal = plantao.valorBase * diasEscalados.length;
      } else if (plantao.tipoValor === "HORA" && plantao.valorBase && plantao.horaInicio && plantao.horaFim) {
        const [horaIni, minIni] = plantao.horaInicio.split(":").map(Number);
        const [horaFim, minFim] = plantao.horaFim.split(":").map(Number);
        const horasTrabalhadas = horaFim + minFim / 60 - (horaIni + minIni / 60);
        valorTotal = plantao.valorBase * horasTrabalhadas * diasEscalados.length;
      }
      form.setFieldValue("valorTotal", valorTotal);
    }
  };

  const getDaysInMonthCount = (month: number, year: number) => {
    return getDaysInMonthForDate(year, month);
  };

  const getFirstDayOfMonth = (month: number, year: number) => {
    return createLocalDate(year, month, 1).getDay();
  };

  const toggleDay = (day: number) => {
    const dias = form.state.values.diasConfirmados;
    let newDias: number[];

    if (dias.includes(day)) {
      newDias = dias.filter((d) => d !== day);
    } else {
      newDias = [...dias, day].sort((a, b) => a - b);
    }

    form.setFieldValue("diasConfirmados", newDias);
    form.setFieldValue("diasTrabalhados", newDias.length);

    // Recalculate value based on confirmed days
    if (selectedEscala) {
      let valorTotal = 0;
      if (selectedEscala.tipoValor === "MENSAL" && selectedEscala.valorBase) {
        valorTotal = selectedEscala.valorBase;
      } else if (
        (selectedEscala.tipoValor === "PLANTAO" || selectedEscala.tipoValor === "DIARIA") &&
        selectedEscala.valorBase
      ) {
        valorTotal = selectedEscala.valorBase * newDias.length;
      } else if (
        selectedEscala.tipoValor === "HORA" &&
        selectedEscala.valorBase &&
        selectedEscala.horaInicio &&
        selectedEscala.horaFim
      ) {
        const [horaIni, minIni] = selectedEscala.horaInicio.split(":").map(Number);
        const [horaFim, minFim] = selectedEscala.horaFim.split(":").map(Number);
        const horasTrabalhadas = horaFim + minFim / 60 - (horaIni + minIni / 60);
        valorTotal = selectedEscala.valorBase * horasTrabalhadas * newDias.length;
      }
      form.setFieldValue("valorTotal", valorTotal);
    }
  };

  const renderCalendar = () => {
    if (!selectedEscala) return null;

    const daysInMonth = getDaysInMonthCount(form.state.values.mes, form.state.values.ano);
    const firstDay = getFirstDayOfMonth(form.state.values.mes, form.state.values.ano);
    const days = [];
    const diasEscalados = selectedEscala.diasPlantao?.map((d) => new Date(String(d.data)).getDate()) || [];

    // Days of week headers
    const headers = diasSemana.map((dia, index) => (
      <div key={`header-${index}`} className="text-center text-xs font-semibold p-1">
        {dia}
      </div>
    ));

    // Empty cells for days before month starts
    for (let i = 0; i < firstDay; i++) {
      days.push(<div key={`empty-${i}`} className="p-1"></div>);
    }

    // Days of the month
    for (let day = 1; day <= daysInMonth; day++) {
      const isScheduled = diasEscalados.includes(day);
      const isConfirmed = form.state.values.diasConfirmados.includes(day);
      const date = createLocalDate(form.state.values.ano, form.state.values.mes, day);
      const isWeekend = date.getDay() === 0 || date.getDay() === 6;

      days.push(
        <Button
          key={day}
          type="button"
          variant={isConfirmed ? "default" : isScheduled ? "secondary" : "ghost"}
          size="sm"
          className={`h-8 w-full ${isWeekend ? "text-muted-foreground" : ""} ${!isScheduled ? "opacity-50" : ""}`}
          onClick={() => isScheduled && toggleDay(day)}
          disabled={!isScheduled}
        >
          {day}
          {isConfirmed && <CheckCircle2 className="h-3 w-3 ml-1" />}
        </Button>
      );
    }

    return (
      <div>
        <div className="grid grid-cols-7 gap-1 mb-2">{headers}</div>
        <div className="grid grid-cols-7 gap-1">{days}</div>
      </div>
    );
  };

  const formatCurrency = (value: number) => {
    return new Intl.NumberFormat("pt-BR", {
      style: "currency",
      currency: "BRL",
    }).format(value);
  };

  return (
    <div className="space-y-6">
      <div className="flex items-center gap-4">
        <Link to="/fechamentos">
          <Button variant="ghost" size="icon">
            <ArrowLeft className="h-4 w-4" />
          </Button>
        </Link>
        <div>
          <h1 className="text-3xl font-bold">Novo Fechamento</h1>
          <p className="text-muted-foreground">Confirme os dias trabalhados e crie o fechamento</p>
        </div>
      </div>

      <form
        onSubmit={(e) => {
          e.preventDefault();
          e.stopPropagation();
          form.handleSubmit();
        }}
      >
        <div className="grid gap-6 lg:grid-cols-2">
          <div className="space-y-6">
            <Card>
              <CardHeader>
                <CardTitle>Informações do Fechamento</CardTitle>
                <CardDescription>Selecione o plantão e confirme os dias trabalhados</CardDescription>
              </CardHeader>
              <CardContent className="space-y-4">
                <form.Field
                  name="escalaId"
                  validators={{
                    onChange: ({ value }) => (!value ? "Plantão é obrigatório" : undefined),
                  }}
                >
                  {(field) => (
                    <div className="space-y-2">
                      <Label htmlFor={field.name}>Plantão *</Label>
                      <Select value={field.state.value} onValueChange={handleEscalaChange}>
                        <SelectTrigger>
                          <SelectValue placeholder="Selecione o plantão" />
                        </SelectTrigger>
                        <SelectContent>
                          {escalas?.data.map((plantao) => (
                            <SelectItem key={plantao.uuid} value={plantao.uuid}>
                              <div className="flex flex-col">
                                <span>{plantao.profissional?.usuario?.nome}</span>
                                <span className="text-xs text-muted-foreground">
                                  {plantao.cliente?.nome} - {plantao.localAtendimento?.nome}
                                </span>
                              </div>
                            </SelectItem>
                          ))}
                        </SelectContent>
                      </Select>
                      {field.state.meta.errors && (
                        <p className="text-sm text-destructive">{field.state.meta.errors.join(", ")}</p>
                      )}
                    </div>
                  )}
                </form.Field>

                {selectedEscala && (
                  <>
                    <div className="grid grid-cols-2 gap-4">
                      <div className="space-y-2">
                        <Label>Mês</Label>
                        <Input value={meses[form.state.values.mes - 1]} disabled />
                      </div>
                      <div className="space-y-2">
                        <Label>Ano</Label>
                        <Input value={form.state.values.ano} disabled />
                      </div>
                    </div>

                    <div className="grid grid-cols-2 gap-4">
                      <form.Field name="diasTrabalhados">
                        {(field) => (
                          <div className="space-y-2">
                            <Label htmlFor={field.name}>Dias Trabalhados</Label>
                            <Input id={field.name} type="number" value={field.state.value} disabled />
                          </div>
                        )}
                      </form.Field>

                      <form.Field name="valorTotal">
                        {(field) => (
                          <div className="space-y-2">
                            <Label htmlFor={field.name}>Valor Total</Label>
                            <Input id={field.name} value={formatCurrency(field.state.value)} disabled />
                          </div>
                        )}
                      </form.Field>
                    </div>

                    <div className="space-y-2">
                      <Label>Informações do Plantão</Label>
                      <div className="rounded-lg border p-4 space-y-2">
                        <div className="flex justify-between text-sm">
                          <span className="text-muted-foreground">Profissional:</span>
                          <span className="font-medium">{selectedEscala.profissional?.usuario?.nome}</span>
                        </div>
                        <div className="flex justify-between text-sm">
                          <span className="text-muted-foreground">Cliente:</span>
                          <span className="font-medium">{selectedEscala.cliente?.nome}</span>
                        </div>
                        <div className="flex justify-between text-sm">
                          <span className="text-muted-foreground">Local:</span>
                          <span className="font-medium">{selectedEscala.localAtendimento?.nome}</span>
                        </div>
                        <div className="flex justify-between text-sm">
                          <span className="text-muted-foreground">Tipo:</span>
                          <Badge variant="outline">{selectedEscala.modalidadeTrabalho}</Badge>
                        </div>
                        <div className="flex justify-between text-sm">
                          <span className="text-muted-foreground">Dias Escalados:</span>
                          <span className="font-medium">{selectedEscala.diasPlantao?.length || 0}</span>
                        </div>
                      </div>
                    </div>
                  </>
                )}

                <form.Field name="observacoes">
                  {(field) => (
                    <div className="space-y-2">
                      <Label htmlFor={field.name}>Observações</Label>
                      <Textarea
                        id={field.name}
                        value={field.state.value}
                        onBlur={field.handleBlur}
                        onChange={(e) => field.handleChange(e.target.value)}
                        placeholder="Observações sobre o fechamento..."
                        rows={3}
                      />
                    </div>
                  )}
                </form.Field>
              </CardContent>
            </Card>
          </div>

          {selectedEscala && (
            <Card className="h-fit">
              <CardHeader>
                <CardTitle>Confirmação de Dias Trabalhados</CardTitle>
                <CardDescription>Clique nos dias para confirmar ou desconfirmar o trabalho realizado</CardDescription>
              </CardHeader>
              <CardContent>
                {renderCalendar()}
                <div className="mt-4 space-y-2">
                  <div className="flex items-center justify-between text-sm">
                    <span className="text-muted-foreground">Dias confirmados:</span>
                    <span className="font-semibold">
                      {form.state.values.diasConfirmados.length} de {selectedEscala.diasPlantao?.length || 0}
                    </span>
                  </div>
                  <div className="flex items-center gap-4 text-xs">
                    <div className="flex items-center gap-1">
                      <div className="h-4 w-4 rounded bg-primary" />
                      <span>Confirmado</span>
                    </div>
                    <div className="flex items-center gap-1">
                      <div className="h-4 w-4 rounded bg-secondary" />
                      <span>Escalado</span>
                    </div>
                    <div className="flex items-center gap-1">
                      <div className="h-4 w-4 rounded border" />
                      <span>Não escalado</span>
                    </div>
                  </div>
                </div>
              </CardContent>
            </Card>
          )}
        </div>

        <div className="flex justify-end gap-4 mt-6">
          <Link to="/fechamentos">
            <Button type="button" variant="outline">
              Cancelar
            </Button>
          </Link>
          <form.Subscribe selector={(state) => [state.canSubmit, state.isSubmitting]}>
            {([canSubmit, isSubmitting]) => (
              <Button type="submit" disabled={!canSubmit || isSubmitting}>
                {isSubmitting ? (
                  <>
                    <Loader2 className="mr-2 h-4 w-4 animate-spin" />
                    Salvando...
                  </>
                ) : (
                  <>
                    <Save className="mr-2 h-4 w-4" />
                    Criar Fechamento
                  </>
                )}
              </Button>
            )}
          </form.Subscribe>
        </div>
      </form>
    </div>
  );
}

export const Route = createFileRoute("/fechamentos/novo")({
  component: FechamentoNovo,
  beforeLoad: async () => {
    await requireAdminRole();
  },
});
