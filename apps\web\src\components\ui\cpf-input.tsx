import * as React from "react";
import { cn } from "@/lib/utils";
import { formatCPF } from "@/lib/utils";
import { Label } from "./label";

export interface CpfInputProps extends Omit<React.ComponentProps<"input">, "onChange" | "value"> {
  value?: string;
  onChange?: (value: string) => void;
  onBlur?: () => void;
  label?: string;
  error?: string;
  required?: boolean;
}

const CpfInput = React.forwardRef<HTMLInputElement, CpfInputProps>(
  ({ className, value = "", onChange, onBlur, label, error, required, id, ...props }, ref) => {
    const inputId = id || "cpf-input";

    const handleChange = (e: React.ChangeEvent<HTMLInputElement>) => {
      const rawValue = e.target.value.replace(/\D/g, "");
      if (rawValue.length <= 11) {
        onChange?.(rawValue);
      }
    };

    return (
      <div className="space-y-2">
        {label && (
          <Label htmlFor={inputId}>
            {label}
            {required && <span className="text-red-500 ml-1">*</span>}
          </Label>
        )}
        <input
          ref={ref}
          id={inputId}
          type="text"
          value={formatCPF(value)}
          onChange={handleChange}
          onBlur={onBlur}
          placeholder="000.000.000-00"
          maxLength={14}
          data-slot="input"
          className={cn(
            "file:text-foreground placeholder:text-muted-foreground selection:bg-primary selection:text-primary-foreground dark:bg-input/30 border-input flex h-9 w-full min-w-0 rounded-md border bg-transparent px-3 py-1 text-base shadow-xs transition-[color,box-shadow] outline-none file:inline-flex file:h-7 file:border-0 file:bg-transparent file:text-sm file:font-medium disabled:pointer-events-none disabled:cursor-not-allowed disabled:opacity-50 md:text-sm",
            "focus-visible:border-ring focus-visible:ring-ring/50 focus-visible:ring-[3px]",
            "aria-invalid:ring-destructive/20 dark:aria-invalid:ring-destructive/40 aria-invalid:border-destructive",
            error && "border-destructive",
            className
          )}
          aria-invalid={!!error}
          {...props}
        />
        {error && <p className="text-sm text-destructive">{error}</p>}
      </div>
    );
  }
);

CpfInput.displayName = "CpfInput";

export { CpfInput };
