import * as React from "react";
import { Check, X, ChevronDown, ChevronRight, ChevronLeft } from "lucide-react";
import { cn, createLocalDate, getDaysInMonthForDate, parseUTCDate } from "@/lib/utils";
import { <PERSON><PERSON> } from "@/components/ui/button";
import { Card, CardContent, CardHeader } from "@/components/ui/card";
import { Label } from "@/components/ui/label";
import { Tabs, TabsList, TabsTrigger } from "@/components/ui/tabs";
import { Select, SelectContent, SelectItem, SelectTrigger, SelectValue } from "@/components/ui/select";
import {
  Dialog,
  DialogContent,
  DialogDescription,
  DialogFooter,
  DialogHeader,
  DialogTitle,
} from "@/components/ui/dialog";
import { Input } from "@/components/ui/input";
import { Checkbox } from "@/components/ui/checkbox";
import {
  DropdownMenu,
  DropdownMenuContent,
  DropdownMenuItem,
  DropdownMenuSeparator,
  DropdownMenuTrigger,
} from "@/components/ui/dropdown-menu";

export type TipoTurno = "diurno" | "comercial" | "noturno" | "customizado";

export interface HorarioTurno {
  inicio: string;
  fim: string;
  intervalo: string;
}

export interface DiaPlantao {
  data: string; // YYYY-MM-DD format
  selecionado: boolean;
  horario?: HorarioTurno;
}

interface CalendarioPlantaoProps {
  mes: number;
  ano: number;
  onMesChange?: (mes: number) => void;
  onAnoChange?: (ano: number) => void;
  diasPlantao: DiaPlantao[];
  onDiasChange?: (dias: DiaPlantao[]) => void;
  tipoTurno?: TipoTurno;
  onTipoTurnoChange?: (tipo: TipoTurno) => void;
  dataInicial?: string | Date | null;
  dataFinal?: string | Date | null;
  navigateMonth?: (direction: "next" | "prev") => void;
  setFieldValue?: (field: string, value: any) => void;
  onTodoPeriodoChange?: (diaSemana: number, ativar: boolean) => void;
  readOnly?: boolean; // Novo prop para modo de visualização
}

const meses = [
  "janeiro",
  "fevereiro",
  "março",
  "abril",
  "maio",
  "junho",
  "julho",
  "agosto",
  "setembro",
  "outubro",
  "novembro",
  "dezembro",
];

const diasSemana = ["Dom", "Seg", "Ter", "Qua", "Qui", "Sex", "Sáb"];

const horariosPreDefinidos: Record<TipoTurno, HorarioTurno> = {
  diurno: { inicio: "07:00", fim: "19:00", intervalo: "01:00" },
  comercial: { inicio: "09:00", fim: "18:00", intervalo: "01:00" },
  noturno: { inicio: "19:00", fim: "07:00", intervalo: "01:00" },
  customizado: { inicio: "", fim: "", intervalo: "" },
};

export function CalendarioPlantao({
  mes,
  ano,
  onMesChange,
  onAnoChange,
  diasPlantao,
  onDiasChange,
  tipoTurno,
  onTipoTurnoChange,
  dataInicial,
  dataFinal,
  navigateMonth,
  setFieldValue,
  onTodoPeriodoChange,
  readOnly,
}: CalendarioPlantaoProps) {
  /*
  
  {
    "mes": 9,
    "ano": 2025,
    "diasPlantao": [
        {
            "data": "2025-09-01",
            "selecionado": true
        },
        {
            "data": "2025-09-02",
            "selecionado": true
        },
        {
            "data": "2025-09-03",
            "selecionado": true
        },
        {
            "data": "2025-09-04",
            "selecionado": true
        },
        {
            "data": "2025-09-05",
            "selecionado": true
        },
        {
            "data": "2025-09-06",
            "selecionado": true
        },
        {
            "data": "2025-09-07",
            "selecionado": true,
            "horario": {
                "inicio": "07:00",
                "fim": "19:00",
                "intervalo": "01:00"
            }
        },
        {
            "data": "2025-09-08",
            "selecionado": true,
            "horario": {
                "inicio": "07:00",
                "fim": "19:00",
                "intervalo": "01:00"
            }
        },
        {
            "data": "2025-09-09",
            "selecionado": true,
            "horario": {
                "inicio": "07:00",
                "fim": "19:00",
                "intervalo": "01:00"
            }
        },
        {
            "data": "2025-09-10",
            "selecionado": true,
            "horario": {
                "inicio": "07:00",
                "fim": "19:00",
                "intervalo": "01:00"
            }
        },
        {
            "data": "2025-09-11",
            "selecionado": true,
            "horario": {
                "inicio": "07:00",
                "fim": "19:00",
                "intervalo": "01:00"
            }
        },
        {
            "data": "2025-09-12",
            "selecionado": true
        },
        {
            "data": "2025-09-13",
            "selecionado": true
        },
        {
            "data": "2025-09-14",
            "selecionado": true
        },
        {
            "data": "2025-09-15",
            "selecionado": true
        },
        {
            "data": "2025-09-16",
            "selecionado": true
        },
        {
            "data": "2025-09-17",
            "selecionado": true
        },
        {
            "data": "2025-09-18",
            "selecionado": true
        },
        {
            "data": "2025-09-19",
            "selecionado": true
        },
        {
            "data": "2025-09-20",
            "selecionado": true
        },
        {
            "data": "2025-09-21",
            "selecionado": true
        },
        {
            "data": "2025-09-22",
            "selecionado": true
        },
        {
            "data": "2025-09-23",
            "selecionado": true
        },
        {
            "data": "2025-09-24",
            "selecionado": true
        },
        {
            "data": "2025-09-25",
            "selecionado": true
        },
        {
            "data": "2025-09-26",
            "selecionado": true
        },
        {
            "data": "2025-09-27",
            "selecionado": true
        },
        {
            "data": "2025-09-28",
            "selecionado": true
        },
        {
            "data": "2025-09-29",
            "selecionado": true
        },
        {
            "data": "2025-09-30",
            "selecionado": true
        }
    ],
    "tipoTurno": "diurno",
    "dataInicial": "2025-09-08T00:00:00.000Z",
    "dataFinal": "2025-09-12T00:00:00.000Z",
    "readOnly": true
}*/

  // Funções para verificar se pode navegar
  const canNavigatePrev = React.useMemo(() => {
    if (!dataInicial) return false;

    const currentDate = createLocalDate(ano, mes, 1);
    const dataInicialDate = parseUTCDate(dataInicial);
    const minDate = createLocalDate(dataInicialDate.getFullYear(), dataInicialDate.getMonth() + 1, 1);

    return currentDate > minDate;
  }, [mes, ano, dataInicial]);

  const canNavigateNext = React.useMemo(() => {
    if (!dataInicial) return false;

    const currentDate = createLocalDate(ano, mes, 1);
    const dataFinalDate = dataFinal ? parseUTCDate(dataFinal) : null;
    const maxDate = dataFinalDate
      ? createLocalDate(dataFinalDate.getFullYear(), dataFinalDate.getMonth() + 1, 1)
      : createLocalDate(2099, 12, 1); // Data muito no futuro se não há fim

    return currentDate < maxDate;
  }, [mes, ano, dataInicial, dataFinal]);

  // Gerar lista de meses válidos para o período selecionado
  const mesesValidos = React.useMemo(() => {
    if (!dataInicial) return [];

    const dataInicialDate = parseUTCDate(dataInicial);
    const dataFinalDate = dataFinal ? parseUTCDate(dataFinal) : createLocalDate(2099, 12, 31);

    const mesesValidosList: { mes: number; ano: number; nome: string }[] = [];

    // Começar do primeiro mês válido
    let currentDate = createLocalDate(dataInicialDate.getFullYear(), dataInicialDate.getMonth() + 1, 1);
    const endDate = createLocalDate(dataFinalDate.getFullYear(), dataFinalDate.getMonth() + 1, 1);

    while (currentDate <= endDate) {
      mesesValidosList.push({
        mes: currentDate.getMonth() + 1,
        ano: currentDate.getFullYear(),
        nome: `${meses[currentDate.getMonth()]} ${currentDate.getFullYear()}`,
      });

      // Próximo mês
      currentDate.setMonth(currentDate.getMonth() + 1);
    }

    return mesesValidosList;
  }, [dataInicial, dataFinal]);
  const [customDialogOpen, setCustomDialogOpen] = React.useState(false);
  const [customHorario, setCustomHorario] = React.useState<HorarioTurno>({
    inicio: "",
    fim: "",
    intervalo: "",
  });
  const [menuDiasOpen, setMenuDiasOpen] = React.useState(false);
  const [initialized, setInitialized] = React.useState(false);

  // Aplicar horários do turno inicial quando o componente monta
  React.useEffect(() => {
    if (
      !initialized &&
      diasPlantao.length > 0 &&
      tipoTurno &&
      tipoTurno !== "customizado" &&
      !readOnly &&
      onDiasChange
    ) {
      const horario = horariosPreDefinidos[tipoTurno];
      const diasComHorario = diasPlantao.map((d) => ({
        ...d,
        horario: d.selecionado ? horario : undefined,
      }));
      onDiasChange(diasComHorario);
      setInitialized(true);
    }
  }, [diasPlantao, tipoTurno, initialized, onDiasChange, readOnly]);

  const getDaysInMonthCount = (month: number, year: number) => {
    return getDaysInMonthForDate(year, month);
  };

  const getFirstDayOfMonth = (month: number, year: number) => {
    return createLocalDate(year, month, 1).getDay();
  };

  const toggleDia = (data: string) => {
    if (readOnly || !onDiasChange) return; // Não fazer nada em modo readOnly
    const novosDias = diasPlantao.map((d) => {
      if (d.data === data) {
        const horario =
          tipoTurno && tipoTurno !== "customizado"
            ? horariosPreDefinidos[tipoTurno]
            : customHorario.inicio
              ? customHorario
              : undefined;

        return {
          ...d,
          selecionado: !d.selecionado,
          horario: !d.selecionado ? horario : undefined,
        };
      }
      return d;
    });
    onDiasChange(novosDias);
  };

  // Função removida - trabalhado não é mais usado

  const handleTipoTurnoChange = (tipo: TipoTurno) => {
    if (readOnly || !onTipoTurnoChange || !onDiasChange) return;
    if (tipo === "customizado") {
      setCustomDialogOpen(true);
    } else {
      onTipoTurnoChange(tipo);
      const horario = horariosPreDefinidos[tipo];
      const novosDias = diasPlantao.map((d) => ({
        ...d,
        horario: d.selecionado ? horario : undefined,
      }));
      onDiasChange(novosDias);
    }
  };

  const handleCustomConfirm = () => {
    if (!onTipoTurnoChange || !onDiasChange) return;
    onTipoTurnoChange("customizado");
    const novosDias = diasPlantao.map((d) => ({
      ...d,
      horario: d.selecionado ? customHorario : undefined,
    }));
    onDiasChange(novosDias);
    setCustomDialogOpen(false);
  };

  const ativarDiasSemana = (diaSemana: number) => {
    if (readOnly || !onDiasChange) return;
    const daysInMonth = getDaysInMonthCount(mes, ano);
    const horario =
      tipoTurno && tipoTurno !== "customizado"
        ? horariosPreDefinidos[tipoTurno]
        : customHorario.inicio
          ? customHorario
          : undefined;

    const novosDias = [...diasPlantao];

    for (let dia = 1; dia <= daysInMonth; dia++) {
      const date = createLocalDate(ano, mes, dia);
      if (date.getDay() === diaSemana) {
        const dataStr = `${ano}-${mes.toString().padStart(2, "0")}-${dia.toString().padStart(2, "0")}`;
        const index = novosDias.findIndex((d) => d.data === dataStr);
        if (index >= 0) {
          novosDias[index] = {
            ...novosDias[index],
            selecionado: true,
            horario,
          };
        }
      }
    }

    onDiasChange(novosDias);
  };

  const desativarDiasSemana = (diaSemana: number) => {
    if (readOnly || !onDiasChange) return;
    const daysInMonth = getDaysInMonthCount(mes, ano);
    const novosDias = [...diasPlantao];

    for (let dia = 1; dia <= daysInMonth; dia++) {
      const date = createLocalDate(ano, mes, dia);
      if (date.getDay() === diaSemana) {
        const dataStr = `${ano}-${mes.toString().padStart(2, "0")}-${dia.toString().padStart(2, "0")}`;
        const index = novosDias.findIndex((d) => d.data === dataStr);
        if (index >= 0) {
          novosDias[index] = {
            ...novosDias[index],
            selecionado: false,
            horario: undefined,
          };
        }
      }
    }

    onDiasChange(novosDias);
  };

  const ativarTodoPeriodo = () => {
    if (readOnly || !onDiasChange) return;
    const horario =
      tipoTurno && tipoTurno !== "customizado"
        ? horariosPreDefinidos[tipoTurno]
        : customHorario.inicio
          ? customHorario
          : undefined;

    const novosDias = diasPlantao.map((d) => ({
      ...d,
      selecionado: true,
      horario,
    }));
    onDiasChange(novosDias);
  };

  const desativarTodoPeriodo = () => {
    if (readOnly || !onDiasChange) return;
    const novosDias = diasPlantao.map((d) => ({
      ...d,
      selecionado: false,
      horario: undefined,
    }));
    onDiasChange(novosDias);
  };

  const toggleDiasSemana = (diaSemana: number) => {
    const daysInMonth = getDaysInMonthCount(mes, ano);
    const diasDaSemana = [];

    // Encontra todos os dias desta semana no mês
    for (let dia = 1; dia <= daysInMonth; dia++) {
      const date = createLocalDate(ano, mes, dia);
      if (date.getDay() === diaSemana) {
        const dataStr = `${ano}-${mes.toString().padStart(2, "0")}-${dia.toString().padStart(2, "0")}`;
        const diaPlantao = diasPlantao.find((d) => d.data === dataStr);
        if (diaPlantao) {
          diasDaSemana.push(diaPlantao);
        }
      }
    }

    // Verifica se pelo menos um dia desta semana já está selecionado
    const algumSelecionado = diasDaSemana.some((d) => d.selecionado);

    // Se algum está selecionado, desativa todos; se nenhum está, ativa todos
    if (algumSelecionado) {
      desativarDiasSemana(diaSemana);
    } else {
      ativarDiasSemana(diaSemana);
    }
  };

  const isColumnActive = (diaSemana: number) => {
    const daysInMonth = getDaysInMonthCount(mes, ano);
    const diasDaSemana = [];

    for (let dia = 1; dia <= daysInMonth; dia++) {
      const date = createLocalDate(ano, mes, dia);
      if (date.getDay() === diaSemana) {
        const dataStr = `${ano}-${mes.toString().padStart(2, "0")}-${dia.toString().padStart(2, "0")}`;
        const diaPlantao = diasPlantao.find((d) => d.data === dataStr);
        if (diaPlantao) {
          diasDaSemana.push(diaPlantao);
        }
      }
    }

    // Retorna true se todos os dias desta semana estão selecionados
    return diasDaSemana.length > 0 && diasDaSemana.every((d) => d.selecionado);
  };

  const ativarDiasSemanaTodoPeriodo = (diaSemana: number) => {
    if (!dataInicial || !dataFinal || readOnly || !onDiasChange) return;

    // Se temos uma callback para todo período, usa ela
    if (onTodoPeriodoChange) {
      onTodoPeriodoChange(diaSemana, true);
      return;
    }

    // Caso contrário, aplica apenas no mês atual
    // Extrair datas diretamente das strings ISO para evitar problemas de timezone
    const dataInicialStr = typeof dataInicial === "string" ? dataInicial : dataInicial.toISOString();
    const dataFinalStr = typeof dataFinal === "string" ? dataFinal : dataFinal.toISOString();

    const dataInicialMatch = dataInicialStr.match(/^(\d{4})-(\d{2})-(\d{2})/);
    const dataFinalMatch = dataFinalStr.match(/^(\d{4})-(\d{2})-(\d{2})/);

    if (!dataInicialMatch || !dataFinalMatch) return;

    const [, initialYear, initialMonth, initialDay] = dataInicialMatch;
    const [, finalYear, finalMonth, finalDay] = dataFinalMatch;

    const inicioOnly = createLocalDate(parseInt(initialYear), parseInt(initialMonth), parseInt(initialDay));
    const fimOnly = createLocalDate(parseInt(finalYear), parseInt(finalMonth), parseInt(finalDay));

    const horario =
      tipoTurno && tipoTurno !== "customizado"
        ? horariosPreDefinidos[tipoTurno]
        : customHorario.inicio
          ? customHorario
          : undefined;

    const novosDias = [...diasPlantao];

    for (let dia = 1; dia <= getDaysInMonthCount(mes, ano); dia++) {
      const currentDate = createLocalDate(ano, mes, dia);

      // Verifica se está dentro do período E é o dia da semana correto
      const currentDateOnly = createLocalDate(
        currentDate.getFullYear(),
        currentDate.getMonth() + 1,
        currentDate.getDate()
      );

      if (currentDateOnly >= inicioOnly && currentDateOnly <= fimOnly && currentDate.getDay() === diaSemana) {
        const dataStr = `${ano}-${mes.toString().padStart(2, "0")}-${dia.toString().padStart(2, "0")}`;
        const index = novosDias.findIndex((d) => d.data === dataStr);
        if (index >= 0) {
          novosDias[index] = {
            ...novosDias[index],
            selecionado: true,
            horario,
          };
        }
      }
    }

    onDiasChange(novosDias);
  };

  const desativarDiasSemanaTodoPeriodo = (diaSemana: number) => {
    if (!dataInicial || !dataFinal || readOnly || !onDiasChange) return;

    // Se temos uma callback para todo período, usa ela
    if (onTodoPeriodoChange) {
      onTodoPeriodoChange(diaSemana, false);
      return;
    }

    // Caso contrário, aplica apenas no mês atual
    // Extrair datas diretamente das strings ISO para evitar problemas de timezone
    const dataInicialStr = typeof dataInicial === "string" ? dataInicial : dataInicial.toISOString();
    const dataFinalStr = typeof dataFinal === "string" ? dataFinal : dataFinal.toISOString();

    const dataInicialMatch = dataInicialStr.match(/^(\d{4})-(\d{2})-(\d{2})/);
    const dataFinalMatch = dataFinalStr.match(/^(\d{4})-(\d{2})-(\d{2})/);

    if (!dataInicialMatch || !dataFinalMatch) return;

    const [, initialYear, initialMonth, initialDay] = dataInicialMatch;
    const [, finalYear, finalMonth, finalDay] = dataFinalMatch;

    const inicioOnly = createLocalDate(parseInt(initialYear), parseInt(initialMonth), parseInt(initialDay));
    const fimOnly = createLocalDate(parseInt(finalYear), parseInt(finalMonth), parseInt(finalDay));

    const novosDias = [...diasPlantao];

    for (let dia = 1; dia <= getDaysInMonthCount(mes, ano); dia++) {
      const currentDate = createLocalDate(ano, mes, dia);

      // Verifica se está dentro do período E é o dia da semana correto
      const currentDateOnly = createLocalDate(
        currentDate.getFullYear(),
        currentDate.getMonth() + 1,
        currentDate.getDate()
      );

      if (currentDateOnly >= inicioOnly && currentDateOnly <= fimOnly && currentDate.getDay() === diaSemana) {
        const dataStr = `${ano}-${mes.toString().padStart(2, "0")}-${dia.toString().padStart(2, "0")}`;
        const index = novosDias.findIndex((d) => d.data === dataStr);
        if (index >= 0) {
          novosDias[index] = {
            ...novosDias[index],
            selecionado: false,
            horario: undefined,
          };
        }
      }
    }

    onDiasChange(novosDias);
  };

  const formatHorario = (horario: HorarioTurno) => {
    return `Início:${horario.inicio} Fim:${horario.fim} Intervalo:${horario.intervalo}`;
  };

  const renderDia = (dia: DiaPlantao) => {
    // Extract date components directly from ISO string to avoid timezone issues
    const dateMatch = dia.data.match(/^(\d{4})-(\d{2})-(\d{2})/);
    let dayNumber, month, year;

    if (dateMatch) {
      year = parseInt(dateMatch[1]);
      month = parseInt(dateMatch[2]);
      dayNumber = parseInt(dateMatch[3]);
    } else {
      // Fallback to parseUTCDate if string format is unexpected
      const date = parseUTCDate(dia.data);
      dayNumber = date.getDate();
      month = date.getMonth() + 1;
      year = date.getFullYear();
    }

    // Create a date object just for determining the day of the week
    const dateForDayOfWeek = createLocalDate(year, month, dayNumber);
    const isWeekend = dateForDayOfWeek.getDay() === 0 || dateForDayOfWeek.getDay() === 6;

    // Verificar se o dia está dentro do período elegível
    let isElegivel = true;
    if (dataInicial && dataFinal) {
      // Para datas UTC, extrair diretamente os componentes sem conversão de timezone
      const dataInicialStr = typeof dataInicial === "string" ? dataInicial : dataInicial.toISOString();
      const dataFinalStr = typeof dataFinal === "string" ? dataFinal : dataFinal.toISOString();

      // Extrair ano, mês, dia diretamente da string ISO - REMOVER T DO FINAL
      const dataInicialMatch = dataInicialStr.match(/^(\d{4})-(\d{2})-(\d{2})/);
      const dataFinalMatch = dataFinalStr.match(/^(\d{4})-(\d{2})-(\d{2})/);

      if (dataInicialMatch && dataFinalMatch) {
        const [, initialYear, initialMonth, initialDay] = dataInicialMatch;
        const [, finalYear, finalMonth, finalDay] = dataFinalMatch;

        const currentDateOnly = createLocalDate(year, month, dayNumber);
        const dataInicialOnly = createLocalDate(parseInt(initialYear), parseInt(initialMonth), parseInt(initialDay));
        const dataFinalOnly = createLocalDate(parseInt(finalYear), parseInt(finalMonth), parseInt(finalDay));

        isElegivel = currentDateOnly >= dataInicialOnly && currentDateOnly <= dataFinalOnly;
      }
    }

    return (
      <div
        key={dia.data}
        className={cn(
          "relative rounded-lg p-2 min-h-[100px] border",
          dia.selecionado && isElegivel ? "bg-primary/10 border-primary" : "",
          isWeekend && "bg-muted/50"
        )}
      >
        <div className="flex items-start justify-between mb-1">
          <span className="font-semibold text-sm">{dayNumber}</span>
          <div className="flex gap-1">
            <Button
              type="button"
              variant="ghost"
              size="icon"
              className="h-6 w-6"
              onClick={() => toggleDia(dia.data)}
              disabled={readOnly || !isElegivel}
            >
              {dia.selecionado ? (
                <Check className="h-4 w-4 text-primary" />
              ) : (
                <X className="h-4 w-4 text-muted-foreground" />
              )}
            </Button>
          </div>
        </div>

        {dia.selecionado && dia.horario && (
          <div className="text-xs space-y-1">
            <div className="flex items-center gap-1">
              <span className="text-muted-foreground">Início:</span>
              <span className="font-medium">{dia.horario.inicio}</span>
            </div>
            <div className="flex items-center gap-1">
              <span className="text-muted-foreground">Fim:</span>
              <span className="font-medium">{dia.horario.fim}</span>
            </div>
            <div className="flex items-center gap-1">
              <span className="text-muted-foreground">Intervalo:</span>
              <span className="font-medium">{dia.horario.intervalo}</span>
            </div>
          </div>
        )}

        {dia.selecionado && !dia.horario && !readOnly && isElegivel && (
          <div className="text-xs text-muted-foreground">Horário não definido</div>
        )}

        <div className="absolute bottom-1 right-1 text-xs text-muted-foreground">(Dia {dayNumber})</div>
      </div>
    );
  };

  const renderCalendario = () => {
    const daysInMonth = getDaysInMonthCount(mes, ano);
    const firstDay = getFirstDayOfMonth(mes, ano);
    const weeks = [];
    let currentWeek = [];

    for (let i = 0; i < firstDay; i++) {
      currentWeek.push(<div key={`empty-${i}`} className="min-h-[100px]" />);
    }

    for (let day = 1; day <= daysInMonth; day++) {
      const dataStr = `${ano}-${mes.toString().padStart(2, "0")}-${day.toString().padStart(2, "0")}`;
      const diaPlantao = diasPlantao.find((d) => d.data === dataStr) || {
        data: dataStr,
        selecionado: false,
      };

      currentWeek.push(renderDia(diaPlantao));

      if (currentWeek.length === 7) {
        weeks.push(
          <div key={`week-${weeks.length}`} className="grid grid-cols-7 gap-2">
            {currentWeek}
          </div>
        );
        currentWeek = [];
      }
    }

    if (currentWeek.length > 0) {
      while (currentWeek.length < 7) {
        currentWeek.push(<div key={`empty-end-${currentWeek.length}`} className="min-h-[100px]" />);
      }
      weeks.push(
        <div key={`week-${weeks.length}`} className="grid grid-cols-7 gap-2">
          {currentWeek}
        </div>
      );
    }

    return weeks;
  };

  const diasSelecionados = diasPlantao.filter((d) => d.selecionado).length;

  return (
    <Card className="w-full">
      <CardHeader className="space-y-4">
        <div className="flex items-center justify-between">
          <div className="space-y-1">
            <h3 className="text-lg font-semibold">HORÁRIOS</h3>
          </div>
          <div className="flex items-center gap-2">
            <Button
              type="button"
              variant="outline"
              size="sm"
              onClick={() => navigateMonth && navigateMonth("prev")}
              disabled={!canNavigatePrev || !navigateMonth}
            >
              <ChevronLeft className="h-4 w-4" />
            </Button>

            {dataInicial && dataFinal ? (
              <Select
                // ref:HORARIOS
                value={`${mes}-${ano}`}
                onValueChange={(value) => {
                  const [mesStr, anoStr] = value.split("-");
                  if (onMesChange) onMesChange(parseInt(mesStr));
                  if (onAnoChange) onAnoChange(parseInt(anoStr));
                }}
              >
                <SelectTrigger className="w-48">
                  <SelectValue />
                </SelectTrigger>
                <SelectContent>
                  {mesesValidos.map((mesValido) => (
                    <SelectItem key={`${mesValido.mes}-${mesValido.ano}`} value={`${mesValido.mes}-${mesValido.ano}`}>
                      {mesValido.nome}
                    </SelectItem>
                  ))}
                </SelectContent>
              </Select>
            ) : (
              <span className="text-sm font-medium min-w-[120px] text-center">
                {meses[mes - 1]} {ano}
              </span>
            )}

            <Button
              type="button"
              variant="outline"
              size="sm"
              onClick={() => navigateMonth && navigateMonth("next")}
              disabled={!canNavigateNext || !navigateMonth}
            >
              <ChevronRight className="h-4 w-4" />
            </Button>
          </div>
        </div>

        <div className="flex items-center gap-2">
          <Tabs
            value={tipoTurno}
            defaultValue="diurno"
            onValueChange={(v) => handleTipoTurnoChange(v as TipoTurno)}
            className={readOnly ? "pointer-events-none opacity-60" : ""}
          >
            <TabsList>
              <TabsTrigger value="diurno">Diurno</TabsTrigger>
              <TabsTrigger value="comercial">Comercial</TabsTrigger>
              <TabsTrigger value="noturno">Noturno</TabsTrigger>
              <TabsTrigger value="customizado">Customizado</TabsTrigger>
            </TabsList>
          </Tabs>
        </div>

        <div className="grid grid-cols-7 gap-2">
          {diasSemana.map((dia, index) => {
            const isActive = isColumnActive(index);
            return (
              // ref:DROPDOWN:DIA_SEMANA
              <DropdownMenu key={dia}>
                <DropdownMenuTrigger asChild disabled={readOnly}>
                  <Button
                    type="button"
                    variant={isActive ? "default" : "outline"}
                    size="sm"
                    className={cn(
                      "text-center text-sm font-semibold py-2 h-auto flex items-center justify-center gap-1",
                      isActive && "bg-primary text-primary-foreground",
                      !isActive && "hover:bg-muted",
                      readOnly && "cursor-default"
                    )}
                    disabled={readOnly}
                  >
                    {dia}
                    {!readOnly && <ChevronDown className="h-3 w-3" />}
                  </Button>
                </DropdownMenuTrigger>
                <DropdownMenuContent align="center" className="w-40">
                  <DropdownMenuItem onClick={() => ativarDiasSemana(index)}>Habilitar</DropdownMenuItem>
                  <DropdownMenuItem onClick={() => desativarDiasSemana(index)}>Desabilitar</DropdownMenuItem>
                  {dataInicial && dataFinal && (
                    <>
                      <DropdownMenuSeparator />
                      <div className="px-2 py-1.5 text-xs font-semibold text-muted-foreground">Todo o período</div>
                      <DropdownMenuItem onClick={() => ativarDiasSemanaTodoPeriodo(index)}>Habilitar</DropdownMenuItem>
                      <DropdownMenuItem onClick={() => desativarDiasSemanaTodoPeriodo(index)}>
                        Desabilitar
                      </DropdownMenuItem>
                    </>
                  )}
                </DropdownMenuContent>
              </DropdownMenu>
            );
          })}
        </div>
      </CardHeader>

      <CardContent className="space-y-2">
        {renderCalendario()}

        <div className="flex justify-between items-center pt-4 border-t">
          <div className="text-sm text-muted-foreground">{diasSelecionados} dias selecionados</div>
        </div>
      </CardContent>

      <Dialog open={customDialogOpen} onOpenChange={setCustomDialogOpen}>
        <DialogContent>
          <DialogHeader>
            <DialogTitle>Definir Turno Customizado</DialogTitle>
            <DialogDescription>Configure os horários personalizados para o turno</DialogDescription>
          </DialogHeader>
          <div className="grid gap-4 py-4">
            <div className="grid grid-cols-2 gap-4">
              <div className="space-y-2">
                <Label htmlFor="inicio">Início</Label>
                <Input
                  id="inicio"
                  type="time"
                  value={customHorario.inicio}
                  onChange={(e) =>
                    setCustomHorario({
                      ...customHorario,
                      inicio: e.target.value,
                    })
                  }
                />
              </div>
              <div className="space-y-2">
                <Label htmlFor="fim">Fim</Label>
                <Input
                  id="fim"
                  type="time"
                  value={customHorario.fim}
                  onChange={(e) => setCustomHorario({ ...customHorario, fim: e.target.value })}
                />
              </div>
            </div>
            <div className="space-y-2">
              <Label htmlFor="intervalo">Intervalo</Label>
              <div className="flex items-center gap-2">
                <Input
                  id="intervalo"
                  type="time"
                  value={customHorario.intervalo}
                  onChange={(e) =>
                    setCustomHorario({
                      ...customHorario,
                      intervalo: e.target.value,
                    })
                  }
                />
                <span className="text-sm text-muted-foreground">Horas</span>
              </div>
            </div>
          </div>
          <DialogFooter>
            <Button variant="outline" onClick={() => setCustomDialogOpen(false)}>
              Cancelar
            </Button>
            <Button
              disabled={!customHorario.inicio || !customHorario.fim || !customHorario.intervalo}
              onClick={handleCustomConfirm}
            >
              Definir
            </Button>
          </DialogFooter>
        </DialogContent>
      </Dialog>
    </Card>
  );
}
