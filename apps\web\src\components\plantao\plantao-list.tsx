import { Link } from "@tanstack/react-router";
import { Badge } from "@/components/ui/badge";
import { Button } from "@/components/ui/button";
import { Card, CardContent } from "@/components/ui/card";
import { Skeleton } from "@/components/ui/skeleton";
import {
  Calendar,
  Users,
  Building2,
  MapPin,
  DollarSign,
  Clock,
  Copy,
  Eye,
  Pencil,
  CheckCircle2,
  CircleDashed,
} from "lucide-react";
import type { Plantao } from "@/lib/api";
import { formatDate } from "@shared/date";

interface PlantaoListProps {
  plantoes?: Plantao[];
  isLoading?: boolean;
  viewMode?: "calendar" | "list";
  onReplicate?: (plantaoId: string) => void;
  dateRange?: { from: Date; to?: Date };
}

const diasSemana = ["Dom", "Seg", "Ter", "Qua", "Qui", "Sex", "Sáb"];

export function PlantaoList({
  plantoes = [],
  isLoading = false,
  viewMode = "list",
  onReplicate,
  dateRange,
}: PlantaoListProps) {
  const formatCurrency = (value: number) => {
    return new Intl.NumberFormat("pt-BR", {
      style: "currency",
      currency: "BRL",
    }).format(value);
  };

  const getDaysInMonthCount = (month: number, year: number) => {
    return new Date(year, month, 0).getDate();
  };

  const getFirstDayOfMonth = (month: number, year: number) => {
    return new Date(year, month - 1, 1).getDay();
  };

  const renderCalendarView = () => {
    if (!dateRange?.from) return null;

    const currentMonth = dateRange.from.getMonth() + 1;
    const currentYear = dateRange.from.getFullYear();
    const daysInMonth = getDaysInMonthCount(currentMonth, currentYear);
    const firstDay = getFirstDayOfMonth(currentMonth, currentYear);
    const days = [];

    // Empty cells for days before month starts
    for (let i = 0; i < firstDay; i++) {
      days.push(<div key={`empty-${i}`} className="p-2"></div>);
    }

    // Days of the month
    for (let day = 1; day <= daysInMonth; day++) {
      const dataStr = `${currentYear}-${currentMonth.toString().padStart(2, "0")}-${day.toString().padStart(2, "0")}`;
      const dayPlantoes = plantoes.filter((plantao) => {
        return plantao.diasPlantao?.some((d) => formatDate(d.data, "yyyy-MM-dd") === dataStr);
      });

      days.push(
        <div key={day} className="min-h-[100px] border rounded-lg p-2 hover:bg-accent/50 transition-colors">
          <div className="font-semibold text-sm mb-1">{day}</div>
          <div className="space-y-1">
            {dayPlantoes.slice(0, 3).map((plantao) => (
              <Link key={plantao.id} to={"/plantoes/$uuid"} params={{ uuid: plantao.uuid }} className="block">
                <div className="text-xs p-1 bg-primary/10 rounded truncate hover:bg-primary/20">
                  <span className="font-medium">{plantao.profissional?.usuario?.nome}</span>
                </div>
              </Link>
            ))}
            {dayPlantoes.length > 3 && (
              <div className="text-xs text-muted-foreground">+{dayPlantoes.length - 3} mais</div>
            )}
          </div>
        </div>
      );
    }

    return (
      <div className="grid grid-cols-7 gap-2">
        {diasSemana.map((dia) => (
          <div key={dia} className="text-center font-semibold text-sm py-2">
            {dia}
          </div>
        ))}
        {days}
      </div>
    );
  };

  const renderListView = () => {
    return (
      <div className="space-y-4">
        {isLoading ? (
          Array.from({ length: 3 }).map((_, i) => (
            <Card key={i}>
              <CardContent className="p-4">
                <Skeleton className="h-20 w-full" />
              </CardContent>
            </Card>
          ))
        ) : plantoes.length === 0 ? (
          <Card>
            <CardContent className="text-center py-12">
              <Calendar className="mx-auto h-12 w-12 text-muted-foreground/20 mb-2" />
              <p className="text-muted-foreground">Nenhum plantão para este período</p>
            </CardContent>
          </Card>
        ) : (
          plantoes.map((plantao) => (
            <Card key={plantao.id} className="hover:shadow-lg transition-shadow">
              <CardContent className="p-4">
                <div className="flex justify-between items-start">
                  <div className="space-y-2 flex-1">
                    <div className="flex items-center gap-2">
                      <Users className="h-4 w-4 text-muted-foreground" />
                      <span className="font-semibold">{plantao.profissional?.usuario?.nome}</span>
                      <Badge variant="outline">{plantao.modalidadeTrabalho}</Badge>
                      {plantao.concluidoEm ? (
                        <Badge variant="default" className="gap-1">
                          <CheckCircle2 className="h-3 w-3" />
                          Concluído
                        </Badge>
                      ) : (
                        <Badge variant="secondary" className="gap-1">
                          <CircleDashed className="h-3 w-3" />
                          Em andamento
                        </Badge>
                      )}
                    </div>
                    <div className="flex items-center gap-4 text-sm text-muted-foreground">
                      <div className="flex items-center gap-1">
                        <Building2 className="h-3 w-3" />
                        {plantao.cliente?.nome}
                      </div>
                      <div className="flex items-center gap-1">
                        <MapPin className="h-3 w-3" />
                        {plantao.localAtendimento?.nome}
                      </div>
                    </div>
                    <div className="flex items-center gap-4 text-sm">
                      <div className="flex items-center gap-1">
                        <DollarSign className="h-3 w-3" />
                        <span className="font-medium">{formatCurrency(plantao.valorTotal || 0)}</span>
                      </div>
                      {plantao.horaInicio && plantao.horaFim && (
                        <div className="flex items-center gap-1">
                          <Clock className="h-3 w-3" />
                          {plantao.horaInicio} - {plantao.horaFim}
                        </div>
                      )}
                    </div>
                  </div>
                  <div className="flex gap-2">
                    <Link to={"/plantoes/$uuid"} params={{ uuid: plantao.uuid }}>
                      <Button variant="ghost" size="icon">
                        <Eye className="h-4 w-4" />
                      </Button>
                    </Link>
                    <Link to={"/plantoes/$uuid/editar"} params={{ uuid: plantao.uuid }}>
                      <Button variant="ghost" size="icon">
                        <Pencil className="h-4 w-4" />
                      </Button>
                    </Link>
                    {onReplicate && (
                      <Button variant="ghost" size="icon" onClick={() => onReplicate(plantao.uuid)}>
                        <Copy className="h-4 w-4" />
                      </Button>
                    )}
                  </div>
                </div>
              </CardContent>
            </Card>
          ))
        )}
      </div>
    );
  };

  return viewMode === "calendar" ? renderCalendarView() : renderListView();
}
