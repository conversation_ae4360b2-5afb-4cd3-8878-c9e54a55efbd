
/* !!! This is code generated by Prisma. Do not edit directly. !!! */
/* eslint-disable */
// @ts-nocheck 
/*
 * This file should be your main import to use Prisma. Through it you get access to all the models, enums, and input types.
 *
 * 🟢 You can import this file directly.
 */

import * as process from 'node:process'
import * as path from 'node:path'
import { fileURLToPath } from 'node:url'
globalThis['__dirname'] = path.dirname(fileURLToPath(import.meta.url))

import * as runtime from "@prisma/client/runtime/library"
import * as $Enums from "./enums.ts"
import * as $Class from "./internal/class.ts"
import * as Prisma from "./internal/prismaNamespace.ts"

export * as $Enums from './enums.ts'
/**
 * ## Prisma Client
 * 
 * Type-safe database client for TypeScript
 * @example
 * ```
 * const prisma = new PrismaClient()
 * // Fetch zero or more Usuarios
 * const usuarios = await prisma.usuario.findMany()
 * ```
 * 
 * Read more in our [docs](https://www.prisma.io/docs/reference/tools-and-interfaces/prisma-client).
 */
export const PrismaClient = $Class.getPrismaClientClass(__dirname)
export type PrismaClient<LogOpts extends Prisma.LogLevel = never, OmitOpts extends Prisma.PrismaClientOptions["omit"] = Prisma.PrismaClientOptions["omit"], ExtArgs extends runtime.Types.Extensions.InternalArgs = runtime.Types.Extensions.DefaultArgs> = $Class.PrismaClient<LogOpts, OmitOpts, ExtArgs>
export { Prisma }


// file annotations for bundling tools to include these files
path.join(__dirname, "query_engine-windows.dll.node")
path.join(process.cwd(), "../shared/src/types/prisma/query_engine-windows.dll.node")

/**
 * Model Usuario
 * 
 */
export type Usuario = Prisma.UsuarioModel
/**
 * Model Role
 * 
 */
export type Role = Prisma.RoleModel
/**
 * Model UsuarioRole
 * 
 */
export type UsuarioRole = Prisma.UsuarioRoleModel
/**
 * Model Cliente
 * 
 */
export type Cliente = Prisma.ClienteModel
/**
 * Model Especialidade
 * 
 */
export type Especialidade = Prisma.EspecialidadeModel
/**
 * Model Profissional
 * 
 */
export type Profissional = Prisma.ProfissionalModel
/**
 * Model ProfissionalEspecialidade
 * 
 */
export type ProfissionalEspecialidade = Prisma.ProfissionalEspecialidadeModel
/**
 * Model UsuarioCliente
 * 
 */
export type UsuarioCliente = Prisma.UsuarioClienteModel
/**
 * Model LocalAtendimento
 * 
 */
export type LocalAtendimento = Prisma.LocalAtendimentoModel
/**
 * Model LocalAtendimentoEspecialidades
 * 
 */
export type LocalAtendimentoEspecialidades = Prisma.LocalAtendimentoEspecialidadesModel
/**
 * Model Plantao
 * 
 */
export type Plantao = Prisma.PlantaoModel
/**
 * Model DiaPlantao
 * 
 */
export type DiaPlantao = Prisma.DiaPlantaoModel
/**
 * Model PresencaDiaPlantao
 * 
 */
export type PresencaDiaPlantao = Prisma.PresencaDiaPlantaoModel
/**
 * Model Fechamento
 * 
 */
export type Fechamento = Prisma.FechamentoModel
/**
 * Model Antecipacao
 * 
 */
export type Antecipacao = Prisma.AntecipacaoModel
/**
 * Model AntecipacaoHistorico
 * 
 */
export type AntecipacaoHistorico = Prisma.AntecipacaoHistoricoModel
/**
 * Model AuditLog
 * 
 */
export type AuditLog = Prisma.AuditLogModel
/**
 * Model GerenciamentoTermosLgpd
 * 
 */
export type GerenciamentoTermosLgpd = Prisma.GerenciamentoTermosLgpdModel
/**
 * Model AceiteTermosLgpd
 * 
 */
export type AceiteTermosLgpd = Prisma.AceiteTermosLgpdModel


