import { z } from "zod";
import { prisma, withAudit } from "@/lib/prisma";
import {
  createPlantaoSchema,
  plantaoQuerySchema,
  type CreatePlantaoInput,
  type PlantaoQuery,
} from "@/schemas/plantao.schema";
import type { FastifyTypedInstance, FastifyTypedRequest } from "@/types";
import { formatDateToDateTime, getCurrentYear, parseUTCDate, toISOString } from "@shared/date";
import { getEndOfMonthInTimezone, getStartOfMonthInTimezone } from "@shared/date";

export function buscarPlantaoRouter(fastify: FastifyTypedInstance) {
  // Buscar plantão por UUID
  fastify.get<{ Params: { uuid: string } }>(
    "/plantoes/:uuid",
    withAudit(async (request, reply) => {
      const { uuid } = request.params;
      const clienteId = request.clienteId;

      const where = {
        uuid,
        clienteId,
      };

      const plantao = await prisma.plantao.findFirst({
        where,
        include: {
          cliente: true,
          profissional: {
            select: {
              id: true,
              usuario: {
                select: {
                  id: true,
                  nome: true,
                  cpf: true,
                  email: true,
                },
              },
              especialidades: {
                include: {
                  especialidade: true,
                },
              },
            },
          },
          localAtendimento: true,
          diasPlantao: {
            orderBy: { data: "asc" },
            include: {
              presencaDiaPlantao: false,
            },
          },
          fechamentos: {
            include: {
              antecipacao: true,
            },
          },
        },
      });

      if (!plantao) {
        return reply.status(404).send({ error: "Plantão não encontrado" });
      }

      return reply.send(plantao);
    })
  );
}
