import { Outlet, useLocation } from "@tanstack/react-router";
import { Sidebar } from "./sidebar";
import { useUIStore } from "@/stores/use-ui.store";
import { cn } from "@/lib/utils";
import { Button } from "@/components/ui/button";
import { Menu } from "lucide-react";
import { useState, useEffect } from "react";
import { OnboardingGuard } from "./onboarding-guard";
import { ThemeToggle } from "./theme-toggle";

export function Layout() {
  const { sidebarCollapsed, setSidebarCollapsed } = useUIStore();
  const [isMobile, setIsMobile] = useState(false);
  const [mobileMenuOpen, setMobileMenuOpen] = useState(false);
  const location = useLocation();

  useEffect(() => {
    const checkMobile = () => {
      const mobile = window.innerWidth < 1024;
      setIsMobile(mobile);
      if (mobile) {
        setSidebarCollapsed(true);
      }
    };

    checkMobile();
    window.addEventListener("resize", checkMobile);
    return () => window.removeEventListener("resize", checkMobile);
  }, []);

  // Se está na página de login, renderiza apenas o Outlet
  if (location.pathname === "/login") {
    return <Outlet />;
  }

  return (
    <div className="min-h-screen">
      {/* Mobile overlay */}
      {isMobile && mobileMenuOpen && (
        <div className="fixed inset-0 z-40 bg-black/50 lg:hidden" onClick={() => setMobileMenuOpen(false)} />
      )}

      {/* Sidebar */}
      <Sidebar onMobileClose={() => setMobileMenuOpen(false)} />

      <main className={cn("transition-all duration-300", !isMobile && (sidebarCollapsed ? "lg:ml-16" : "lg:ml-64"))}>
        {/* Mobile header */}
        {isMobile && (
          <header className="flex items-center justify-between h-16 px-4 border-b bg-background lg:hidden">
            <div className="flex items-center">
              <Button variant="ghost" size="icon" onClick={() => setMobileMenuOpen(true)}>
                <Menu className="h-5 w-5" />
              </Button>
              <div className="flex items-center gap-2 ml-3">
                {/* <img src="/logo.png" alt="GS2" className="h-6 w-6" /> */}
                <img src="/logo-fundo-branco.png" alt="GS2" className="h-6 w-6" />
                <span className="font-bold text-lg">GS2</span>
              </div>
            </div>
            <ThemeToggle />
          </header>
        )}

        {/* Desktop header */}
        {!isMobile && (
          <header className="flex items-center justify-end h-16 px-4 border-b bg-background">
            <ThemeToggle />
          </header>
        )}

        {/* Main content area */}
        <div className="container mx-auto max-w-7xl p-4 md:p-6">
          <OnboardingGuard>
            <Outlet />
          </OnboardingGuard>
        </div>
      </main>
    </div>
  );
}
