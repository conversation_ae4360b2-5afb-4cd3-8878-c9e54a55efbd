
/* !!! This is code generated by Prisma. Do not edit directly. !!! */
/* eslint-disable */
// @ts-nocheck 
/*
 * This file exports the `Role` model and its related types.
 *
 * 🟢 You can import this file directly.
 */
import * as runtime from "@prisma/client/runtime/library"
import type * as $Enums from "../enums.ts"
import type * as Prisma from "../internal/prismaNamespace.ts"

/**
 * Model Role
 * 
 */
export type RoleModel = runtime.Types.Result.DefaultSelection<Prisma.$RolePayload>

export type AggregateRole = {
  _count: RoleCountAggregateOutputType | null
  _avg: RoleAvgAggregateOutputType | null
  _sum: RoleSumAggregateOutputType | null
  _min: RoleMinAggregateOutputType | null
  _max: RoleMaxAggregateOutputType | null
}

export type RoleAvgAggregateOutputType = {
  id: number | null
}

export type RoleSumAggregateOutputType = {
  id: number | null
}

export type RoleMinAggregateOutputType = {
  id: number | null
  nome: string | null
  descricao: string | null
  ativo: boolean | null
  createdAt: Date | null
  updatedAt: Date | null
}

export type RoleMaxAggregateOutputType = {
  id: number | null
  nome: string | null
  descricao: string | null
  ativo: boolean | null
  createdAt: Date | null
  updatedAt: Date | null
}

export type RoleCountAggregateOutputType = {
  id: number
  nome: number
  descricao: number
  ativo: number
  createdAt: number
  updatedAt: number
  _all: number
}


export type RoleAvgAggregateInputType = {
  id?: true
}

export type RoleSumAggregateInputType = {
  id?: true
}

export type RoleMinAggregateInputType = {
  id?: true
  nome?: true
  descricao?: true
  ativo?: true
  createdAt?: true
  updatedAt?: true
}

export type RoleMaxAggregateInputType = {
  id?: true
  nome?: true
  descricao?: true
  ativo?: true
  createdAt?: true
  updatedAt?: true
}

export type RoleCountAggregateInputType = {
  id?: true
  nome?: true
  descricao?: true
  ativo?: true
  createdAt?: true
  updatedAt?: true
  _all?: true
}

export type RoleAggregateArgs<ExtArgs extends runtime.Types.Extensions.InternalArgs = runtime.Types.Extensions.DefaultArgs> = {
  /**
   * Filter which Role to aggregate.
   */
  where?: Prisma.RoleWhereInput
  /**
   * {@link https://www.prisma.io/docs/concepts/components/prisma-client/sorting Sorting Docs}
   * 
   * Determine the order of Roles to fetch.
   */
  orderBy?: Prisma.RoleOrderByWithRelationInput | Prisma.RoleOrderByWithRelationInput[]
  /**
   * {@link https://www.prisma.io/docs/concepts/components/prisma-client/pagination#cursor-based-pagination Cursor Docs}
   * 
   * Sets the start position
   */
  cursor?: Prisma.RoleWhereUniqueInput
  /**
   * {@link https://www.prisma.io/docs/concepts/components/prisma-client/pagination Pagination Docs}
   * 
   * Take `±n` Roles from the position of the cursor.
   */
  take?: number
  /**
   * {@link https://www.prisma.io/docs/concepts/components/prisma-client/pagination Pagination Docs}
   * 
   * Skip the first `n` Roles.
   */
  skip?: number
  /**
   * {@link https://www.prisma.io/docs/concepts/components/prisma-client/aggregations Aggregation Docs}
   * 
   * Count returned Roles
  **/
  _count?: true | RoleCountAggregateInputType
  /**
   * {@link https://www.prisma.io/docs/concepts/components/prisma-client/aggregations Aggregation Docs}
   * 
   * Select which fields to average
  **/
  _avg?: RoleAvgAggregateInputType
  /**
   * {@link https://www.prisma.io/docs/concepts/components/prisma-client/aggregations Aggregation Docs}
   * 
   * Select which fields to sum
  **/
  _sum?: RoleSumAggregateInputType
  /**
   * {@link https://www.prisma.io/docs/concepts/components/prisma-client/aggregations Aggregation Docs}
   * 
   * Select which fields to find the minimum value
  **/
  _min?: RoleMinAggregateInputType
  /**
   * {@link https://www.prisma.io/docs/concepts/components/prisma-client/aggregations Aggregation Docs}
   * 
   * Select which fields to find the maximum value
  **/
  _max?: RoleMaxAggregateInputType
}

export type GetRoleAggregateType<T extends RoleAggregateArgs> = {
      [P in keyof T & keyof AggregateRole]: P extends '_count' | 'count'
    ? T[P] extends true
      ? number
      : Prisma.GetScalarType<T[P], AggregateRole[P]>
    : Prisma.GetScalarType<T[P], AggregateRole[P]>
}




export type RoleGroupByArgs<ExtArgs extends runtime.Types.Extensions.InternalArgs = runtime.Types.Extensions.DefaultArgs> = {
  where?: Prisma.RoleWhereInput
  orderBy?: Prisma.RoleOrderByWithAggregationInput | Prisma.RoleOrderByWithAggregationInput[]
  by: Prisma.RoleScalarFieldEnum[] | Prisma.RoleScalarFieldEnum
  having?: Prisma.RoleScalarWhereWithAggregatesInput
  take?: number
  skip?: number
  _count?: RoleCountAggregateInputType | true
  _avg?: RoleAvgAggregateInputType
  _sum?: RoleSumAggregateInputType
  _min?: RoleMinAggregateInputType
  _max?: RoleMaxAggregateInputType
}

export type RoleGroupByOutputType = {
  id: number
  nome: string
  descricao: string | null
  ativo: boolean
  createdAt: Date
  updatedAt: Date
  _count: RoleCountAggregateOutputType | null
  _avg: RoleAvgAggregateOutputType | null
  _sum: RoleSumAggregateOutputType | null
  _min: RoleMinAggregateOutputType | null
  _max: RoleMaxAggregateOutputType | null
}

type GetRoleGroupByPayload<T extends RoleGroupByArgs> = Prisma.PrismaPromise<
  Array<
    Prisma.PickEnumerable<RoleGroupByOutputType, T['by']> &
      {
        [P in ((keyof T) & (keyof RoleGroupByOutputType))]: P extends '_count'
          ? T[P] extends boolean
            ? number
            : Prisma.GetScalarType<T[P], RoleGroupByOutputType[P]>
          : Prisma.GetScalarType<T[P], RoleGroupByOutputType[P]>
      }
    >
  >



export type RoleWhereInput = {
  AND?: Prisma.RoleWhereInput | Prisma.RoleWhereInput[]
  OR?: Prisma.RoleWhereInput[]
  NOT?: Prisma.RoleWhereInput | Prisma.RoleWhereInput[]
  id?: Prisma.IntFilter<"Role"> | number
  nome?: Prisma.StringFilter<"Role"> | string
  descricao?: Prisma.StringNullableFilter<"Role"> | string | null
  ativo?: Prisma.BoolFilter<"Role"> | boolean
  createdAt?: Prisma.DateTimeFilter<"Role"> | Date | string
  updatedAt?: Prisma.DateTimeFilter<"Role"> | Date | string
  usuarios?: Prisma.UsuarioRoleListRelationFilter
}

export type RoleOrderByWithRelationInput = {
  id?: Prisma.SortOrder
  nome?: Prisma.SortOrder
  descricao?: Prisma.SortOrderInput | Prisma.SortOrder
  ativo?: Prisma.SortOrder
  createdAt?: Prisma.SortOrder
  updatedAt?: Prisma.SortOrder
  usuarios?: Prisma.UsuarioRoleOrderByRelationAggregateInput
  _relevance?: Prisma.RoleOrderByRelevanceInput
}

export type RoleWhereUniqueInput = Prisma.AtLeast<{
  id?: number
  nome?: string
  AND?: Prisma.RoleWhereInput | Prisma.RoleWhereInput[]
  OR?: Prisma.RoleWhereInput[]
  NOT?: Prisma.RoleWhereInput | Prisma.RoleWhereInput[]
  descricao?: Prisma.StringNullableFilter<"Role"> | string | null
  ativo?: Prisma.BoolFilter<"Role"> | boolean
  createdAt?: Prisma.DateTimeFilter<"Role"> | Date | string
  updatedAt?: Prisma.DateTimeFilter<"Role"> | Date | string
  usuarios?: Prisma.UsuarioRoleListRelationFilter
}, "id" | "nome">

export type RoleOrderByWithAggregationInput = {
  id?: Prisma.SortOrder
  nome?: Prisma.SortOrder
  descricao?: Prisma.SortOrderInput | Prisma.SortOrder
  ativo?: Prisma.SortOrder
  createdAt?: Prisma.SortOrder
  updatedAt?: Prisma.SortOrder
  _count?: Prisma.RoleCountOrderByAggregateInput
  _avg?: Prisma.RoleAvgOrderByAggregateInput
  _max?: Prisma.RoleMaxOrderByAggregateInput
  _min?: Prisma.RoleMinOrderByAggregateInput
  _sum?: Prisma.RoleSumOrderByAggregateInput
}

export type RoleScalarWhereWithAggregatesInput = {
  AND?: Prisma.RoleScalarWhereWithAggregatesInput | Prisma.RoleScalarWhereWithAggregatesInput[]
  OR?: Prisma.RoleScalarWhereWithAggregatesInput[]
  NOT?: Prisma.RoleScalarWhereWithAggregatesInput | Prisma.RoleScalarWhereWithAggregatesInput[]
  id?: Prisma.IntWithAggregatesFilter<"Role"> | number
  nome?: Prisma.StringWithAggregatesFilter<"Role"> | string
  descricao?: Prisma.StringNullableWithAggregatesFilter<"Role"> | string | null
  ativo?: Prisma.BoolWithAggregatesFilter<"Role"> | boolean
  createdAt?: Prisma.DateTimeWithAggregatesFilter<"Role"> | Date | string
  updatedAt?: Prisma.DateTimeWithAggregatesFilter<"Role"> | Date | string
}

export type RoleCreateInput = {
  nome: string
  descricao?: string | null
  ativo?: boolean
  createdAt?: Date | string
  updatedAt?: Date | string
  usuarios?: Prisma.UsuarioRoleCreateNestedManyWithoutRoleInput
}

export type RoleUncheckedCreateInput = {
  id?: number
  nome: string
  descricao?: string | null
  ativo?: boolean
  createdAt?: Date | string
  updatedAt?: Date | string
  usuarios?: Prisma.UsuarioRoleUncheckedCreateNestedManyWithoutRoleInput
}

export type RoleUpdateInput = {
  nome?: Prisma.StringFieldUpdateOperationsInput | string
  descricao?: Prisma.NullableStringFieldUpdateOperationsInput | string | null
  ativo?: Prisma.BoolFieldUpdateOperationsInput | boolean
  createdAt?: Prisma.DateTimeFieldUpdateOperationsInput | Date | string
  updatedAt?: Prisma.DateTimeFieldUpdateOperationsInput | Date | string
  usuarios?: Prisma.UsuarioRoleUpdateManyWithoutRoleNestedInput
}

export type RoleUncheckedUpdateInput = {
  id?: Prisma.IntFieldUpdateOperationsInput | number
  nome?: Prisma.StringFieldUpdateOperationsInput | string
  descricao?: Prisma.NullableStringFieldUpdateOperationsInput | string | null
  ativo?: Prisma.BoolFieldUpdateOperationsInput | boolean
  createdAt?: Prisma.DateTimeFieldUpdateOperationsInput | Date | string
  updatedAt?: Prisma.DateTimeFieldUpdateOperationsInput | Date | string
  usuarios?: Prisma.UsuarioRoleUncheckedUpdateManyWithoutRoleNestedInput
}

export type RoleCreateManyInput = {
  id?: number
  nome: string
  descricao?: string | null
  ativo?: boolean
  createdAt?: Date | string
  updatedAt?: Date | string
}

export type RoleUpdateManyMutationInput = {
  nome?: Prisma.StringFieldUpdateOperationsInput | string
  descricao?: Prisma.NullableStringFieldUpdateOperationsInput | string | null
  ativo?: Prisma.BoolFieldUpdateOperationsInput | boolean
  createdAt?: Prisma.DateTimeFieldUpdateOperationsInput | Date | string
  updatedAt?: Prisma.DateTimeFieldUpdateOperationsInput | Date | string
}

export type RoleUncheckedUpdateManyInput = {
  id?: Prisma.IntFieldUpdateOperationsInput | number
  nome?: Prisma.StringFieldUpdateOperationsInput | string
  descricao?: Prisma.NullableStringFieldUpdateOperationsInput | string | null
  ativo?: Prisma.BoolFieldUpdateOperationsInput | boolean
  createdAt?: Prisma.DateTimeFieldUpdateOperationsInput | Date | string
  updatedAt?: Prisma.DateTimeFieldUpdateOperationsInput | Date | string
}

export type RoleOrderByRelevanceInput = {
  fields: Prisma.RoleOrderByRelevanceFieldEnum | Prisma.RoleOrderByRelevanceFieldEnum[]
  sort: Prisma.SortOrder
  search: string
}

export type RoleCountOrderByAggregateInput = {
  id?: Prisma.SortOrder
  nome?: Prisma.SortOrder
  descricao?: Prisma.SortOrder
  ativo?: Prisma.SortOrder
  createdAt?: Prisma.SortOrder
  updatedAt?: Prisma.SortOrder
}

export type RoleAvgOrderByAggregateInput = {
  id?: Prisma.SortOrder
}

export type RoleMaxOrderByAggregateInput = {
  id?: Prisma.SortOrder
  nome?: Prisma.SortOrder
  descricao?: Prisma.SortOrder
  ativo?: Prisma.SortOrder
  createdAt?: Prisma.SortOrder
  updatedAt?: Prisma.SortOrder
}

export type RoleMinOrderByAggregateInput = {
  id?: Prisma.SortOrder
  nome?: Prisma.SortOrder
  descricao?: Prisma.SortOrder
  ativo?: Prisma.SortOrder
  createdAt?: Prisma.SortOrder
  updatedAt?: Prisma.SortOrder
}

export type RoleSumOrderByAggregateInput = {
  id?: Prisma.SortOrder
}

export type RoleScalarRelationFilter = {
  is?: Prisma.RoleWhereInput
  isNot?: Prisma.RoleWhereInput
}

export type RoleCreateNestedOneWithoutUsuariosInput = {
  create?: Prisma.XOR<Prisma.RoleCreateWithoutUsuariosInput, Prisma.RoleUncheckedCreateWithoutUsuariosInput>
  connectOrCreate?: Prisma.RoleCreateOrConnectWithoutUsuariosInput
  connect?: Prisma.RoleWhereUniqueInput
}

export type RoleUpdateOneRequiredWithoutUsuariosNestedInput = {
  create?: Prisma.XOR<Prisma.RoleCreateWithoutUsuariosInput, Prisma.RoleUncheckedCreateWithoutUsuariosInput>
  connectOrCreate?: Prisma.RoleCreateOrConnectWithoutUsuariosInput
  upsert?: Prisma.RoleUpsertWithoutUsuariosInput
  connect?: Prisma.RoleWhereUniqueInput
  update?: Prisma.XOR<Prisma.XOR<Prisma.RoleUpdateToOneWithWhereWithoutUsuariosInput, Prisma.RoleUpdateWithoutUsuariosInput>, Prisma.RoleUncheckedUpdateWithoutUsuariosInput>
}

export type RoleCreateWithoutUsuariosInput = {
  nome: string
  descricao?: string | null
  ativo?: boolean
  createdAt?: Date | string
  updatedAt?: Date | string
}

export type RoleUncheckedCreateWithoutUsuariosInput = {
  id?: number
  nome: string
  descricao?: string | null
  ativo?: boolean
  createdAt?: Date | string
  updatedAt?: Date | string
}

export type RoleCreateOrConnectWithoutUsuariosInput = {
  where: Prisma.RoleWhereUniqueInput
  create: Prisma.XOR<Prisma.RoleCreateWithoutUsuariosInput, Prisma.RoleUncheckedCreateWithoutUsuariosInput>
}

export type RoleUpsertWithoutUsuariosInput = {
  update: Prisma.XOR<Prisma.RoleUpdateWithoutUsuariosInput, Prisma.RoleUncheckedUpdateWithoutUsuariosInput>
  create: Prisma.XOR<Prisma.RoleCreateWithoutUsuariosInput, Prisma.RoleUncheckedCreateWithoutUsuariosInput>
  where?: Prisma.RoleWhereInput
}

export type RoleUpdateToOneWithWhereWithoutUsuariosInput = {
  where?: Prisma.RoleWhereInput
  data: Prisma.XOR<Prisma.RoleUpdateWithoutUsuariosInput, Prisma.RoleUncheckedUpdateWithoutUsuariosInput>
}

export type RoleUpdateWithoutUsuariosInput = {
  nome?: Prisma.StringFieldUpdateOperationsInput | string
  descricao?: Prisma.NullableStringFieldUpdateOperationsInput | string | null
  ativo?: Prisma.BoolFieldUpdateOperationsInput | boolean
  createdAt?: Prisma.DateTimeFieldUpdateOperationsInput | Date | string
  updatedAt?: Prisma.DateTimeFieldUpdateOperationsInput | Date | string
}

export type RoleUncheckedUpdateWithoutUsuariosInput = {
  id?: Prisma.IntFieldUpdateOperationsInput | number
  nome?: Prisma.StringFieldUpdateOperationsInput | string
  descricao?: Prisma.NullableStringFieldUpdateOperationsInput | string | null
  ativo?: Prisma.BoolFieldUpdateOperationsInput | boolean
  createdAt?: Prisma.DateTimeFieldUpdateOperationsInput | Date | string
  updatedAt?: Prisma.DateTimeFieldUpdateOperationsInput | Date | string
}


/**
 * Count Type RoleCountOutputType
 */

export type RoleCountOutputType = {
  usuarios: number
}

export type RoleCountOutputTypeSelect<ExtArgs extends runtime.Types.Extensions.InternalArgs = runtime.Types.Extensions.DefaultArgs> = {
  usuarios?: boolean | RoleCountOutputTypeCountUsuariosArgs
}

/**
 * RoleCountOutputType without action
 */
export type RoleCountOutputTypeDefaultArgs<ExtArgs extends runtime.Types.Extensions.InternalArgs = runtime.Types.Extensions.DefaultArgs> = {
  /**
   * Select specific fields to fetch from the RoleCountOutputType
   */
  select?: Prisma.RoleCountOutputTypeSelect<ExtArgs> | null
}

/**
 * RoleCountOutputType without action
 */
export type RoleCountOutputTypeCountUsuariosArgs<ExtArgs extends runtime.Types.Extensions.InternalArgs = runtime.Types.Extensions.DefaultArgs> = {
  where?: Prisma.UsuarioRoleWhereInput
}


export type RoleSelect<ExtArgs extends runtime.Types.Extensions.InternalArgs = runtime.Types.Extensions.DefaultArgs> = runtime.Types.Extensions.GetSelect<{
  id?: boolean
  nome?: boolean
  descricao?: boolean
  ativo?: boolean
  createdAt?: boolean
  updatedAt?: boolean
  usuarios?: boolean | Prisma.Role$usuariosArgs<ExtArgs>
  _count?: boolean | Prisma.RoleCountOutputTypeDefaultArgs<ExtArgs>
}, ExtArgs["result"]["role"]>



export type RoleSelectScalar = {
  id?: boolean
  nome?: boolean
  descricao?: boolean
  ativo?: boolean
  createdAt?: boolean
  updatedAt?: boolean
}

export type RoleOmit<ExtArgs extends runtime.Types.Extensions.InternalArgs = runtime.Types.Extensions.DefaultArgs> = runtime.Types.Extensions.GetOmit<"id" | "nome" | "descricao" | "ativo" | "createdAt" | "updatedAt", ExtArgs["result"]["role"]>
export type RoleInclude<ExtArgs extends runtime.Types.Extensions.InternalArgs = runtime.Types.Extensions.DefaultArgs> = {
  usuarios?: boolean | Prisma.Role$usuariosArgs<ExtArgs>
  _count?: boolean | Prisma.RoleCountOutputTypeDefaultArgs<ExtArgs>
}

export type $RolePayload<ExtArgs extends runtime.Types.Extensions.InternalArgs = runtime.Types.Extensions.DefaultArgs> = {
  name: "Role"
  objects: {
    usuarios: Prisma.$UsuarioRolePayload<ExtArgs>[]
  }
  scalars: runtime.Types.Extensions.GetPayloadResult<{
    id: number
    nome: string
    descricao: string | null
    ativo: boolean
    createdAt: Date
    updatedAt: Date
  }, ExtArgs["result"]["role"]>
  composites: {}
}

export type RoleGetPayload<S extends boolean | null | undefined | RoleDefaultArgs> = runtime.Types.Result.GetResult<Prisma.$RolePayload, S>

export type RoleCountArgs<ExtArgs extends runtime.Types.Extensions.InternalArgs = runtime.Types.Extensions.DefaultArgs> =
  Omit<RoleFindManyArgs, 'select' | 'include' | 'distinct' | 'omit'> & {
    select?: RoleCountAggregateInputType | true
  }

export interface RoleDelegate<ExtArgs extends runtime.Types.Extensions.InternalArgs = runtime.Types.Extensions.DefaultArgs, GlobalOmitOptions = {}> {
  [K: symbol]: { types: Prisma.TypeMap<ExtArgs>['model']['Role'], meta: { name: 'Role' } }
  /**
   * Find zero or one Role that matches the filter.
   * @param {RoleFindUniqueArgs} args - Arguments to find a Role
   * @example
   * // Get one Role
   * const role = await prisma.role.findUnique({
   *   where: {
   *     // ... provide filter here
   *   }
   * })
   */
  findUnique<T extends RoleFindUniqueArgs>(args: Prisma.SelectSubset<T, RoleFindUniqueArgs<ExtArgs>>): Prisma.Prisma__RoleClient<runtime.Types.Result.GetResult<Prisma.$RolePayload<ExtArgs>, T, "findUnique", GlobalOmitOptions> | null, null, ExtArgs, GlobalOmitOptions>

  /**
   * Find one Role that matches the filter or throw an error with `error.code='P2025'`
   * if no matches were found.
   * @param {RoleFindUniqueOrThrowArgs} args - Arguments to find a Role
   * @example
   * // Get one Role
   * const role = await prisma.role.findUniqueOrThrow({
   *   where: {
   *     // ... provide filter here
   *   }
   * })
   */
  findUniqueOrThrow<T extends RoleFindUniqueOrThrowArgs>(args: Prisma.SelectSubset<T, RoleFindUniqueOrThrowArgs<ExtArgs>>): Prisma.Prisma__RoleClient<runtime.Types.Result.GetResult<Prisma.$RolePayload<ExtArgs>, T, "findUniqueOrThrow", GlobalOmitOptions>, never, ExtArgs, GlobalOmitOptions>

  /**
   * Find the first Role that matches the filter.
   * Note, that providing `undefined` is treated as the value not being there.
   * Read more here: https://pris.ly/d/null-undefined
   * @param {RoleFindFirstArgs} args - Arguments to find a Role
   * @example
   * // Get one Role
   * const role = await prisma.role.findFirst({
   *   where: {
   *     // ... provide filter here
   *   }
   * })
   */
  findFirst<T extends RoleFindFirstArgs>(args?: Prisma.SelectSubset<T, RoleFindFirstArgs<ExtArgs>>): Prisma.Prisma__RoleClient<runtime.Types.Result.GetResult<Prisma.$RolePayload<ExtArgs>, T, "findFirst", GlobalOmitOptions> | null, null, ExtArgs, GlobalOmitOptions>

  /**
   * Find the first Role that matches the filter or
   * throw `PrismaKnownClientError` with `P2025` code if no matches were found.
   * Note, that providing `undefined` is treated as the value not being there.
   * Read more here: https://pris.ly/d/null-undefined
   * @param {RoleFindFirstOrThrowArgs} args - Arguments to find a Role
   * @example
   * // Get one Role
   * const role = await prisma.role.findFirstOrThrow({
   *   where: {
   *     // ... provide filter here
   *   }
   * })
   */
  findFirstOrThrow<T extends RoleFindFirstOrThrowArgs>(args?: Prisma.SelectSubset<T, RoleFindFirstOrThrowArgs<ExtArgs>>): Prisma.Prisma__RoleClient<runtime.Types.Result.GetResult<Prisma.$RolePayload<ExtArgs>, T, "findFirstOrThrow", GlobalOmitOptions>, never, ExtArgs, GlobalOmitOptions>

  /**
   * Find zero or more Roles that matches the filter.
   * Note, that providing `undefined` is treated as the value not being there.
   * Read more here: https://pris.ly/d/null-undefined
   * @param {RoleFindManyArgs} args - Arguments to filter and select certain fields only.
   * @example
   * // Get all Roles
   * const roles = await prisma.role.findMany()
   * 
   * // Get first 10 Roles
   * const roles = await prisma.role.findMany({ take: 10 })
   * 
   * // Only select the `id`
   * const roleWithIdOnly = await prisma.role.findMany({ select: { id: true } })
   * 
   */
  findMany<T extends RoleFindManyArgs>(args?: Prisma.SelectSubset<T, RoleFindManyArgs<ExtArgs>>): Prisma.PrismaPromise<runtime.Types.Result.GetResult<Prisma.$RolePayload<ExtArgs>, T, "findMany", GlobalOmitOptions>>

  /**
   * Create a Role.
   * @param {RoleCreateArgs} args - Arguments to create a Role.
   * @example
   * // Create one Role
   * const Role = await prisma.role.create({
   *   data: {
   *     // ... data to create a Role
   *   }
   * })
   * 
   */
  create<T extends RoleCreateArgs>(args: Prisma.SelectSubset<T, RoleCreateArgs<ExtArgs>>): Prisma.Prisma__RoleClient<runtime.Types.Result.GetResult<Prisma.$RolePayload<ExtArgs>, T, "create", GlobalOmitOptions>, never, ExtArgs, GlobalOmitOptions>

  /**
   * Create many Roles.
   * @param {RoleCreateManyArgs} args - Arguments to create many Roles.
   * @example
   * // Create many Roles
   * const role = await prisma.role.createMany({
   *   data: [
   *     // ... provide data here
   *   ]
   * })
   *     
   */
  createMany<T extends RoleCreateManyArgs>(args?: Prisma.SelectSubset<T, RoleCreateManyArgs<ExtArgs>>): Prisma.PrismaPromise<Prisma.BatchPayload>

  /**
   * Delete a Role.
   * @param {RoleDeleteArgs} args - Arguments to delete one Role.
   * @example
   * // Delete one Role
   * const Role = await prisma.role.delete({
   *   where: {
   *     // ... filter to delete one Role
   *   }
   * })
   * 
   */
  delete<T extends RoleDeleteArgs>(args: Prisma.SelectSubset<T, RoleDeleteArgs<ExtArgs>>): Prisma.Prisma__RoleClient<runtime.Types.Result.GetResult<Prisma.$RolePayload<ExtArgs>, T, "delete", GlobalOmitOptions>, never, ExtArgs, GlobalOmitOptions>

  /**
   * Update one Role.
   * @param {RoleUpdateArgs} args - Arguments to update one Role.
   * @example
   * // Update one Role
   * const role = await prisma.role.update({
   *   where: {
   *     // ... provide filter here
   *   },
   *   data: {
   *     // ... provide data here
   *   }
   * })
   * 
   */
  update<T extends RoleUpdateArgs>(args: Prisma.SelectSubset<T, RoleUpdateArgs<ExtArgs>>): Prisma.Prisma__RoleClient<runtime.Types.Result.GetResult<Prisma.$RolePayload<ExtArgs>, T, "update", GlobalOmitOptions>, never, ExtArgs, GlobalOmitOptions>

  /**
   * Delete zero or more Roles.
   * @param {RoleDeleteManyArgs} args - Arguments to filter Roles to delete.
   * @example
   * // Delete a few Roles
   * const { count } = await prisma.role.deleteMany({
   *   where: {
   *     // ... provide filter here
   *   }
   * })
   * 
   */
  deleteMany<T extends RoleDeleteManyArgs>(args?: Prisma.SelectSubset<T, RoleDeleteManyArgs<ExtArgs>>): Prisma.PrismaPromise<Prisma.BatchPayload>

  /**
   * Update zero or more Roles.
   * Note, that providing `undefined` is treated as the value not being there.
   * Read more here: https://pris.ly/d/null-undefined
   * @param {RoleUpdateManyArgs} args - Arguments to update one or more rows.
   * @example
   * // Update many Roles
   * const role = await prisma.role.updateMany({
   *   where: {
   *     // ... provide filter here
   *   },
   *   data: {
   *     // ... provide data here
   *   }
   * })
   * 
   */
  updateMany<T extends RoleUpdateManyArgs>(args: Prisma.SelectSubset<T, RoleUpdateManyArgs<ExtArgs>>): Prisma.PrismaPromise<Prisma.BatchPayload>

  /**
   * Create or update one Role.
   * @param {RoleUpsertArgs} args - Arguments to update or create a Role.
   * @example
   * // Update or create a Role
   * const role = await prisma.role.upsert({
   *   create: {
   *     // ... data to create a Role
   *   },
   *   update: {
   *     // ... in case it already exists, update
   *   },
   *   where: {
   *     // ... the filter for the Role we want to update
   *   }
   * })
   */
  upsert<T extends RoleUpsertArgs>(args: Prisma.SelectSubset<T, RoleUpsertArgs<ExtArgs>>): Prisma.Prisma__RoleClient<runtime.Types.Result.GetResult<Prisma.$RolePayload<ExtArgs>, T, "upsert", GlobalOmitOptions>, never, ExtArgs, GlobalOmitOptions>


  /**
   * Count the number of Roles.
   * Note, that providing `undefined` is treated as the value not being there.
   * Read more here: https://pris.ly/d/null-undefined
   * @param {RoleCountArgs} args - Arguments to filter Roles to count.
   * @example
   * // Count the number of Roles
   * const count = await prisma.role.count({
   *   where: {
   *     // ... the filter for the Roles we want to count
   *   }
   * })
  **/
  count<T extends RoleCountArgs>(
    args?: Prisma.Subset<T, RoleCountArgs>,
  ): Prisma.PrismaPromise<
    T extends runtime.Types.Utils.Record<'select', any>
      ? T['select'] extends true
        ? number
        : Prisma.GetScalarType<T['select'], RoleCountAggregateOutputType>
      : number
  >

  /**
   * Allows you to perform aggregations operations on a Role.
   * Note, that providing `undefined` is treated as the value not being there.
   * Read more here: https://pris.ly/d/null-undefined
   * @param {RoleAggregateArgs} args - Select which aggregations you would like to apply and on what fields.
   * @example
   * // Ordered by age ascending
   * // Where email contains prisma.io
   * // Limited to the 10 users
   * const aggregations = await prisma.user.aggregate({
   *   _avg: {
   *     age: true,
   *   },
   *   where: {
   *     email: {
   *       contains: "prisma.io",
   *     },
   *   },
   *   orderBy: {
   *     age: "asc",
   *   },
   *   take: 10,
   * })
  **/
  aggregate<T extends RoleAggregateArgs>(args: Prisma.Subset<T, RoleAggregateArgs>): Prisma.PrismaPromise<GetRoleAggregateType<T>>

  /**
   * Group by Role.
   * Note, that providing `undefined` is treated as the value not being there.
   * Read more here: https://pris.ly/d/null-undefined
   * @param {RoleGroupByArgs} args - Group by arguments.
   * @example
   * // Group by city, order by createdAt, get count
   * const result = await prisma.user.groupBy({
   *   by: ['city', 'createdAt'],
   *   orderBy: {
   *     createdAt: true
   *   },
   *   _count: {
   *     _all: true
   *   },
   * })
   * 
  **/
  groupBy<
    T extends RoleGroupByArgs,
    HasSelectOrTake extends Prisma.Or<
      Prisma.Extends<'skip', Prisma.Keys<T>>,
      Prisma.Extends<'take', Prisma.Keys<T>>
    >,
    OrderByArg extends Prisma.True extends HasSelectOrTake
      ? { orderBy: RoleGroupByArgs['orderBy'] }
      : { orderBy?: RoleGroupByArgs['orderBy'] },
    OrderFields extends Prisma.ExcludeUnderscoreKeys<Prisma.Keys<Prisma.MaybeTupleToUnion<T['orderBy']>>>,
    ByFields extends Prisma.MaybeTupleToUnion<T['by']>,
    ByValid extends Prisma.Has<ByFields, OrderFields>,
    HavingFields extends Prisma.GetHavingFields<T['having']>,
    HavingValid extends Prisma.Has<ByFields, HavingFields>,
    ByEmpty extends T['by'] extends never[] ? Prisma.True : Prisma.False,
    InputErrors extends ByEmpty extends Prisma.True
    ? `Error: "by" must not be empty.`
    : HavingValid extends Prisma.False
    ? {
        [P in HavingFields]: P extends ByFields
          ? never
          : P extends string
          ? `Error: Field "${P}" used in "having" needs to be provided in "by".`
          : [
              Error,
              'Field ',
              P,
              ` in "having" needs to be provided in "by"`,
            ]
      }[HavingFields]
    : 'take' extends Prisma.Keys<T>
    ? 'orderBy' extends Prisma.Keys<T>
      ? ByValid extends Prisma.True
        ? {}
        : {
            [P in OrderFields]: P extends ByFields
              ? never
              : `Error: Field "${P}" in "orderBy" needs to be provided in "by"`
          }[OrderFields]
      : 'Error: If you provide "take", you also need to provide "orderBy"'
    : 'skip' extends Prisma.Keys<T>
    ? 'orderBy' extends Prisma.Keys<T>
      ? ByValid extends Prisma.True
        ? {}
        : {
            [P in OrderFields]: P extends ByFields
              ? never
              : `Error: Field "${P}" in "orderBy" needs to be provided in "by"`
          }[OrderFields]
      : 'Error: If you provide "skip", you also need to provide "orderBy"'
    : ByValid extends Prisma.True
    ? {}
    : {
        [P in OrderFields]: P extends ByFields
          ? never
          : `Error: Field "${P}" in "orderBy" needs to be provided in "by"`
      }[OrderFields]
  >(args: Prisma.SubsetIntersection<T, RoleGroupByArgs, OrderByArg> & InputErrors): {} extends InputErrors ? GetRoleGroupByPayload<T> : Prisma.PrismaPromise<InputErrors>
/**
 * Fields of the Role model
 */
readonly fields: RoleFieldRefs;
}

/**
 * The delegate class that acts as a "Promise-like" for Role.
 * Why is this prefixed with `Prisma__`?
 * Because we want to prevent naming conflicts as mentioned in
 * https://github.com/prisma/prisma-client-js/issues/707
 */
export interface Prisma__RoleClient<T, Null = never, ExtArgs extends runtime.Types.Extensions.InternalArgs = runtime.Types.Extensions.DefaultArgs, GlobalOmitOptions = {}> extends Prisma.PrismaPromise<T> {
  readonly [Symbol.toStringTag]: "PrismaPromise"
  usuarios<T extends Prisma.Role$usuariosArgs<ExtArgs> = {}>(args?: Prisma.Subset<T, Prisma.Role$usuariosArgs<ExtArgs>>): Prisma.PrismaPromise<runtime.Types.Result.GetResult<Prisma.$UsuarioRolePayload<ExtArgs>, T, "findMany", GlobalOmitOptions> | Null>
  /**
   * Attaches callbacks for the resolution and/or rejection of the Promise.
   * @param onfulfilled The callback to execute when the Promise is resolved.
   * @param onrejected The callback to execute when the Promise is rejected.
   * @returns A Promise for the completion of which ever callback is executed.
   */
  then<TResult1 = T, TResult2 = never>(onfulfilled?: ((value: T) => TResult1 | PromiseLike<TResult1>) | undefined | null, onrejected?: ((reason: any) => TResult2 | PromiseLike<TResult2>) | undefined | null): runtime.Types.Utils.JsPromise<TResult1 | TResult2>
  /**
   * Attaches a callback for only the rejection of the Promise.
   * @param onrejected The callback to execute when the Promise is rejected.
   * @returns A Promise for the completion of the callback.
   */
  catch<TResult = never>(onrejected?: ((reason: any) => TResult | PromiseLike<TResult>) | undefined | null): runtime.Types.Utils.JsPromise<T | TResult>
  /**
   * Attaches a callback that is invoked when the Promise is settled (fulfilled or rejected). The
   * resolved value cannot be modified from the callback.
   * @param onfinally The callback to execute when the Promise is settled (fulfilled or rejected).
   * @returns A Promise for the completion of the callback.
   */
  finally(onfinally?: (() => void) | undefined | null): runtime.Types.Utils.JsPromise<T>
}




/**
 * Fields of the Role model
 */
export interface RoleFieldRefs {
  readonly id: Prisma.FieldRef<"Role", 'Int'>
  readonly nome: Prisma.FieldRef<"Role", 'String'>
  readonly descricao: Prisma.FieldRef<"Role", 'String'>
  readonly ativo: Prisma.FieldRef<"Role", 'Boolean'>
  readonly createdAt: Prisma.FieldRef<"Role", 'DateTime'>
  readonly updatedAt: Prisma.FieldRef<"Role", 'DateTime'>
}
    

// Custom InputTypes
/**
 * Role findUnique
 */
export type RoleFindUniqueArgs<ExtArgs extends runtime.Types.Extensions.InternalArgs = runtime.Types.Extensions.DefaultArgs> = {
  /**
   * Select specific fields to fetch from the Role
   */
  select?: Prisma.RoleSelect<ExtArgs> | null
  /**
   * Omit specific fields from the Role
   */
  omit?: Prisma.RoleOmit<ExtArgs> | null
  /**
   * Choose, which related nodes to fetch as well
   */
  include?: Prisma.RoleInclude<ExtArgs> | null
  /**
   * Filter, which Role to fetch.
   */
  where: Prisma.RoleWhereUniqueInput
}

/**
 * Role findUniqueOrThrow
 */
export type RoleFindUniqueOrThrowArgs<ExtArgs extends runtime.Types.Extensions.InternalArgs = runtime.Types.Extensions.DefaultArgs> = {
  /**
   * Select specific fields to fetch from the Role
   */
  select?: Prisma.RoleSelect<ExtArgs> | null
  /**
   * Omit specific fields from the Role
   */
  omit?: Prisma.RoleOmit<ExtArgs> | null
  /**
   * Choose, which related nodes to fetch as well
   */
  include?: Prisma.RoleInclude<ExtArgs> | null
  /**
   * Filter, which Role to fetch.
   */
  where: Prisma.RoleWhereUniqueInput
}

/**
 * Role findFirst
 */
export type RoleFindFirstArgs<ExtArgs extends runtime.Types.Extensions.InternalArgs = runtime.Types.Extensions.DefaultArgs> = {
  /**
   * Select specific fields to fetch from the Role
   */
  select?: Prisma.RoleSelect<ExtArgs> | null
  /**
   * Omit specific fields from the Role
   */
  omit?: Prisma.RoleOmit<ExtArgs> | null
  /**
   * Choose, which related nodes to fetch as well
   */
  include?: Prisma.RoleInclude<ExtArgs> | null
  /**
   * Filter, which Role to fetch.
   */
  where?: Prisma.RoleWhereInput
  /**
   * {@link https://www.prisma.io/docs/concepts/components/prisma-client/sorting Sorting Docs}
   * 
   * Determine the order of Roles to fetch.
   */
  orderBy?: Prisma.RoleOrderByWithRelationInput | Prisma.RoleOrderByWithRelationInput[]
  /**
   * {@link https://www.prisma.io/docs/concepts/components/prisma-client/pagination#cursor-based-pagination Cursor Docs}
   * 
   * Sets the position for searching for Roles.
   */
  cursor?: Prisma.RoleWhereUniqueInput
  /**
   * {@link https://www.prisma.io/docs/concepts/components/prisma-client/pagination Pagination Docs}
   * 
   * Take `±n` Roles from the position of the cursor.
   */
  take?: number
  /**
   * {@link https://www.prisma.io/docs/concepts/components/prisma-client/pagination Pagination Docs}
   * 
   * Skip the first `n` Roles.
   */
  skip?: number
  /**
   * {@link https://www.prisma.io/docs/concepts/components/prisma-client/distinct Distinct Docs}
   * 
   * Filter by unique combinations of Roles.
   */
  distinct?: Prisma.RoleScalarFieldEnum | Prisma.RoleScalarFieldEnum[]
}

/**
 * Role findFirstOrThrow
 */
export type RoleFindFirstOrThrowArgs<ExtArgs extends runtime.Types.Extensions.InternalArgs = runtime.Types.Extensions.DefaultArgs> = {
  /**
   * Select specific fields to fetch from the Role
   */
  select?: Prisma.RoleSelect<ExtArgs> | null
  /**
   * Omit specific fields from the Role
   */
  omit?: Prisma.RoleOmit<ExtArgs> | null
  /**
   * Choose, which related nodes to fetch as well
   */
  include?: Prisma.RoleInclude<ExtArgs> | null
  /**
   * Filter, which Role to fetch.
   */
  where?: Prisma.RoleWhereInput
  /**
   * {@link https://www.prisma.io/docs/concepts/components/prisma-client/sorting Sorting Docs}
   * 
   * Determine the order of Roles to fetch.
   */
  orderBy?: Prisma.RoleOrderByWithRelationInput | Prisma.RoleOrderByWithRelationInput[]
  /**
   * {@link https://www.prisma.io/docs/concepts/components/prisma-client/pagination#cursor-based-pagination Cursor Docs}
   * 
   * Sets the position for searching for Roles.
   */
  cursor?: Prisma.RoleWhereUniqueInput
  /**
   * {@link https://www.prisma.io/docs/concepts/components/prisma-client/pagination Pagination Docs}
   * 
   * Take `±n` Roles from the position of the cursor.
   */
  take?: number
  /**
   * {@link https://www.prisma.io/docs/concepts/components/prisma-client/pagination Pagination Docs}
   * 
   * Skip the first `n` Roles.
   */
  skip?: number
  /**
   * {@link https://www.prisma.io/docs/concepts/components/prisma-client/distinct Distinct Docs}
   * 
   * Filter by unique combinations of Roles.
   */
  distinct?: Prisma.RoleScalarFieldEnum | Prisma.RoleScalarFieldEnum[]
}

/**
 * Role findMany
 */
export type RoleFindManyArgs<ExtArgs extends runtime.Types.Extensions.InternalArgs = runtime.Types.Extensions.DefaultArgs> = {
  /**
   * Select specific fields to fetch from the Role
   */
  select?: Prisma.RoleSelect<ExtArgs> | null
  /**
   * Omit specific fields from the Role
   */
  omit?: Prisma.RoleOmit<ExtArgs> | null
  /**
   * Choose, which related nodes to fetch as well
   */
  include?: Prisma.RoleInclude<ExtArgs> | null
  /**
   * Filter, which Roles to fetch.
   */
  where?: Prisma.RoleWhereInput
  /**
   * {@link https://www.prisma.io/docs/concepts/components/prisma-client/sorting Sorting Docs}
   * 
   * Determine the order of Roles to fetch.
   */
  orderBy?: Prisma.RoleOrderByWithRelationInput | Prisma.RoleOrderByWithRelationInput[]
  /**
   * {@link https://www.prisma.io/docs/concepts/components/prisma-client/pagination#cursor-based-pagination Cursor Docs}
   * 
   * Sets the position for listing Roles.
   */
  cursor?: Prisma.RoleWhereUniqueInput
  /**
   * {@link https://www.prisma.io/docs/concepts/components/prisma-client/pagination Pagination Docs}
   * 
   * Take `±n` Roles from the position of the cursor.
   */
  take?: number
  /**
   * {@link https://www.prisma.io/docs/concepts/components/prisma-client/pagination Pagination Docs}
   * 
   * Skip the first `n` Roles.
   */
  skip?: number
  distinct?: Prisma.RoleScalarFieldEnum | Prisma.RoleScalarFieldEnum[]
}

/**
 * Role create
 */
export type RoleCreateArgs<ExtArgs extends runtime.Types.Extensions.InternalArgs = runtime.Types.Extensions.DefaultArgs> = {
  /**
   * Select specific fields to fetch from the Role
   */
  select?: Prisma.RoleSelect<ExtArgs> | null
  /**
   * Omit specific fields from the Role
   */
  omit?: Prisma.RoleOmit<ExtArgs> | null
  /**
   * Choose, which related nodes to fetch as well
   */
  include?: Prisma.RoleInclude<ExtArgs> | null
  /**
   * The data needed to create a Role.
   */
  data: Prisma.XOR<Prisma.RoleCreateInput, Prisma.RoleUncheckedCreateInput>
}

/**
 * Role createMany
 */
export type RoleCreateManyArgs<ExtArgs extends runtime.Types.Extensions.InternalArgs = runtime.Types.Extensions.DefaultArgs> = {
  /**
   * The data used to create many Roles.
   */
  data: Prisma.RoleCreateManyInput | Prisma.RoleCreateManyInput[]
  skipDuplicates?: boolean
}

/**
 * Role update
 */
export type RoleUpdateArgs<ExtArgs extends runtime.Types.Extensions.InternalArgs = runtime.Types.Extensions.DefaultArgs> = {
  /**
   * Select specific fields to fetch from the Role
   */
  select?: Prisma.RoleSelect<ExtArgs> | null
  /**
   * Omit specific fields from the Role
   */
  omit?: Prisma.RoleOmit<ExtArgs> | null
  /**
   * Choose, which related nodes to fetch as well
   */
  include?: Prisma.RoleInclude<ExtArgs> | null
  /**
   * The data needed to update a Role.
   */
  data: Prisma.XOR<Prisma.RoleUpdateInput, Prisma.RoleUncheckedUpdateInput>
  /**
   * Choose, which Role to update.
   */
  where: Prisma.RoleWhereUniqueInput
}

/**
 * Role updateMany
 */
export type RoleUpdateManyArgs<ExtArgs extends runtime.Types.Extensions.InternalArgs = runtime.Types.Extensions.DefaultArgs> = {
  /**
   * The data used to update Roles.
   */
  data: Prisma.XOR<Prisma.RoleUpdateManyMutationInput, Prisma.RoleUncheckedUpdateManyInput>
  /**
   * Filter which Roles to update
   */
  where?: Prisma.RoleWhereInput
  /**
   * Limit how many Roles to update.
   */
  limit?: number
}

/**
 * Role upsert
 */
export type RoleUpsertArgs<ExtArgs extends runtime.Types.Extensions.InternalArgs = runtime.Types.Extensions.DefaultArgs> = {
  /**
   * Select specific fields to fetch from the Role
   */
  select?: Prisma.RoleSelect<ExtArgs> | null
  /**
   * Omit specific fields from the Role
   */
  omit?: Prisma.RoleOmit<ExtArgs> | null
  /**
   * Choose, which related nodes to fetch as well
   */
  include?: Prisma.RoleInclude<ExtArgs> | null
  /**
   * The filter to search for the Role to update in case it exists.
   */
  where: Prisma.RoleWhereUniqueInput
  /**
   * In case the Role found by the `where` argument doesn't exist, create a new Role with this data.
   */
  create: Prisma.XOR<Prisma.RoleCreateInput, Prisma.RoleUncheckedCreateInput>
  /**
   * In case the Role was found with the provided `where` argument, update it with this data.
   */
  update: Prisma.XOR<Prisma.RoleUpdateInput, Prisma.RoleUncheckedUpdateInput>
}

/**
 * Role delete
 */
export type RoleDeleteArgs<ExtArgs extends runtime.Types.Extensions.InternalArgs = runtime.Types.Extensions.DefaultArgs> = {
  /**
   * Select specific fields to fetch from the Role
   */
  select?: Prisma.RoleSelect<ExtArgs> | null
  /**
   * Omit specific fields from the Role
   */
  omit?: Prisma.RoleOmit<ExtArgs> | null
  /**
   * Choose, which related nodes to fetch as well
   */
  include?: Prisma.RoleInclude<ExtArgs> | null
  /**
   * Filter which Role to delete.
   */
  where: Prisma.RoleWhereUniqueInput
}

/**
 * Role deleteMany
 */
export type RoleDeleteManyArgs<ExtArgs extends runtime.Types.Extensions.InternalArgs = runtime.Types.Extensions.DefaultArgs> = {
  /**
   * Filter which Roles to delete
   */
  where?: Prisma.RoleWhereInput
  /**
   * Limit how many Roles to delete.
   */
  limit?: number
}

/**
 * Role.usuarios
 */
export type Role$usuariosArgs<ExtArgs extends runtime.Types.Extensions.InternalArgs = runtime.Types.Extensions.DefaultArgs> = {
  /**
   * Select specific fields to fetch from the UsuarioRole
   */
  select?: Prisma.UsuarioRoleSelect<ExtArgs> | null
  /**
   * Omit specific fields from the UsuarioRole
   */
  omit?: Prisma.UsuarioRoleOmit<ExtArgs> | null
  /**
   * Choose, which related nodes to fetch as well
   */
  include?: Prisma.UsuarioRoleInclude<ExtArgs> | null
  where?: Prisma.UsuarioRoleWhereInput
  orderBy?: Prisma.UsuarioRoleOrderByWithRelationInput | Prisma.UsuarioRoleOrderByWithRelationInput[]
  cursor?: Prisma.UsuarioRoleWhereUniqueInput
  take?: number
  skip?: number
  distinct?: Prisma.UsuarioRoleScalarFieldEnum | Prisma.UsuarioRoleScalarFieldEnum[]
}

/**
 * Role without action
 */
export type RoleDefaultArgs<ExtArgs extends runtime.Types.Extensions.InternalArgs = runtime.Types.Extensions.DefaultArgs> = {
  /**
   * Select specific fields to fetch from the Role
   */
  select?: Prisma.RoleSelect<ExtArgs> | null
  /**
   * Omit specific fields from the Role
   */
  omit?: Prisma.RoleOmit<ExtArgs> | null
  /**
   * Choose, which related nodes to fetch as well
   */
  include?: Prisma.RoleInclude<ExtArgs> | null
}
