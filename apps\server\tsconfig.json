{"compilerOptions": {"target": "ESNext", "module": "ESNext", "moduleResolution": "bundler", "verbatimModuleSyntax": true, "strict": true, "skipLibCheck": true, "rootDir": "../..", "baseUrl": "./", "paths": {"@/*": ["./src/*"], "@shared/*": ["../shared/src/*"], "@prisma/*": ["../shared/src/types/prisma/*"], "@lib/*": ["./src/lib/*"], "prisma": ["node_modules/prisma"]}, "outDir": "../../server-dist", "types": ["node"], "composite": true}, "include": ["**/*", "**/.server/**/*", "../shared/**/*"], "exclude": ["node_modules", "dist"]}