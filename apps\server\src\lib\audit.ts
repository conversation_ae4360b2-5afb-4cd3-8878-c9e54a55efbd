// Tipo para contexto de auditoria
export interface AuditContext {
  userId?: number;
  userName?: string;
  userEmail?: string;
  userRole?: string;
  ipAddress?: string;
  userAgent?: string;
  endpoint?: string;
  method?: string;
}

// Função para calcular diferenças entre objetos
function calculateChanges(oldData: any, newData: any): any {
  const changes: any = {};

  if (!oldData || !newData) return null;

  // Encontrar campos que mudaram
  for (const key in newData) {
    if (oldData[key] !== newData[key]) {
      changes[key] = {
        old: oldData[key],
        new: newData[key],
      };
    }
  }

  // Encontrar campos removidos
  for (const key in oldData) {
    if (!(key in newData)) {
      changes[key] = {
        old: oldData[key],
        new: null,
      };
    }
  }

  return Object.keys(changes).length > 0 ? changes : null;
}

// Função para gerar descrição legível da operação
function generateDescription(operation: string, tableName: string, data: any, context?: AuditContext): string {
  const user = context?.userName || context?.userEmail || "Sistema";

  switch (operation) {
    case "CREATE":
      return `${user} criou novo registro em ${tableName}`;
    case "UPDATE":
      return `${user} atualizou registro em ${tableName}`;
    case "DELETE":
      return `${user} removeu registro de ${tableName}`;
    case "READ":
      return `${user} consultou registros de ${tableName}`;
    default:
      return `${user} realizou operação ${operation} em ${tableName}`;
  }
}

// Middleware de auditoria para Prisma
export function createAuditMiddleware(getContext: () => AuditContext | null) {
  return async (params: any, next: any) => {
    const startTime = Date.now();
    const context = getContext();

    // Executar a operação original
    const result = await next(params);

    // Tempo de execução
    const executionTime = Date.now() - startTime;

    // Determinar a operação
    let operation = "READ";
    if (params.action === "create" || params.action === "createMany") {
      operation = "CREATE";
    } else if (params.action === "update" || params.action === "updateMany" || params.action === "upsert") {
      operation = "UPDATE";
    } else if (params.action === "delete" || params.action === "deleteMany") {
      operation = "DELETE";
    }

    // Não auditar consultas à própria tabela de auditoria
    if (params.model === "AuditLog") {
      return result;
    }

    // Preparar dados de auditoria
    const auditData: any = {
      operation,
      tableName: params.model || "unknown",
      userId: context?.userId,
      userName: context?.userName,
      userEmail: context?.userEmail,
      userRole: context?.userRole,
      ipAddress: context?.ipAddress,
      userAgent: context?.userAgent,
      endpoint: context?.endpoint,
      method: context?.method,
      metadata: {
        action: params.action,
        executionTime,
        args: params.args,
      },
    };

    // Adicionar dados específicos baseado na operação
    if (operation === "CREATE" && result) {
      auditData.recordId = result.id;
      auditData.newData = result;
      auditData.description = generateDescription(operation, params.model!, result, context || undefined);
    } else if (operation === "UPDATE" && result) {
      auditData.recordId = result.id || params.args.where?.id;
      auditData.newData = result;
      // Para updates, idealmente precisamos buscar o registro anterior
      // Isso pode ser feito com um findFirst antes do update
      auditData.description = generateDescription(operation, params.model!, result, context || undefined);
    } else if (operation === "DELETE") {
      auditData.recordId = params.args.where?.id;
      auditData.oldData = result;
      auditData.description = generateDescription(operation, params.model!, result, context || undefined);
    }

    // Registrar no log de auditoria (async, não bloqueia)
    setImmediate(async () => {
      try {
        const { PrismaClient } = await import("../../../shared/src/types/prisma/client");
        const prismaAudit = new PrismaClient();

        await prismaAudit.auditLog.create({
          data: auditData,
        });

        await prismaAudit.$disconnect();
      } catch (error) {
        console.error("Erro ao registrar auditoria:", error);
      }
    });

    return result;
  };
}

// Função auxiliar para auditar operações manuais
export async function logAuditManual(
  operation: string,
  tableName: string,
  recordId: string | null,
  description: string,
  context?: AuditContext,
  oldData?: any,
  newData?: any
) {
  try {
    const { PrismaClient } = await import("../../../shared/src/types/prisma/client");
    const prisma = new PrismaClient();

    await prisma.auditLog.create({
      data: {
        operation,
        tableName,
        recordId,
        description,
        oldData: oldData ? oldData : undefined,
        newData: newData ? newData : undefined,
        changes: oldData && newData ? calculateChanges(oldData, newData) : undefined,
        userId: context?.userId,
        userName: context?.userName,
        userEmail: context?.userEmail,
        userRole: context?.userRole,
        ipAddress: context?.ipAddress,
        userAgent: context?.userAgent,
        endpoint: context?.endpoint,
        method: context?.method,
        metadata: {
          manual: true,
          timestamp: new Date().toISOString(),
        },
      },
    });

    await prisma.$disconnect();
  } catch (error) {
    console.error("Erro ao registrar auditoria manual:", error);
  }
}

// Exportar tipos e funções úteis
export { calculateChanges, generateDescription };
