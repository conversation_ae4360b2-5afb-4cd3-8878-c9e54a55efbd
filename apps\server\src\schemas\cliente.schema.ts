import { z } from "zod";

export const createClienteSchema = z.object({
  nome: z.string().min(1, "Nome é obrigatório"),
  cnpj: z.string().length(14, "CNPJ deve ter 14 dígitos"),

  taxaPadrao: z.number().min(0, "Taxa Padrão deve ser um número positivo"),

  email: z.email().optional().nullable().or(z.literal("")),

  telefone: z.string().optional().nullable(),

  // Campos de endereço detalhado
  cep: z.string().optional().nullable(),
  logradouro: z.string().optional().nullable(),
  numero: z.string().optional().nullable(),
  complemento: z.string().optional().nullable(),
  bairro: z.string().optional().nullable(),
  cidade: z.string().optional().nullable(),
  uf: z.string().optional().nullable(),

  ativo: z.boolean().default(true),
});

export const updateClienteSchema = createClienteSchema.partial();

export const clienteQuerySchema = z.object({
  page: z.coerce.number().min(1).optional().default(1),
  limit: z.coerce.number().min(1).max(100).optional().default(10),
  search: z.string().optional(),
  ativo: z.boolean().optional(),
});

export type CreateClienteInput = z.infer<typeof createClienteSchema>;
export type UpdateClienteInput = z.infer<typeof updateClienteSchema>;
export type ClienteQuery = z.infer<typeof clienteQuerySchema>;
