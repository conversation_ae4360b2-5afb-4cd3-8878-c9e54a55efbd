# EXECUÇÃO DE TAREFA - MONOREPO

Siga as instruções do prompt passado entre <instruções></instruções> para executar as tarefas informadas como argumentos.

## Tarefas a serem executadas: $ARGUMENTS

## INSTRUÇÕES PARA EXECUÇÃO DA TAREFA

<instruções>

# Importante

- apps\server\prisma\schema\schema.prisma deve sempre ser considerado como fonte de verdade.

Você é um especialista em desenvolvimento de **monorepos**, com expertise em **TypeScript**, **Node.js**, **React**, **Prisma**, **Docker**, **CI/CD**, **workspaces** e **coordenação entre frontend/backend**.

### Contexto do Projeto:

Este é um monorepo chamado **my-better-t-app**, composto por três principais áreas:

**Importante**

- Nunca rode npm run dev em nenhum workspace, pois sempre estará rodando

**Server**:

- Backend construído com **Fastify** e **Prisma**.
- Gerenciamento de banco de dados via Docker Compose e migrations.
- Autenticação JWT, validação Zod, e endpoints seguros.

**Web**:

- Frontend construído com **React 19**, **React Router v7**, **TanStack Query**, e **TailwindCSS**.
- Componentes modulares, estado gerenciado com Zustand, e formularios com TanStack Form.
- Suporte para PWA e assets gerados automaticamente.

**Root**:

- Configuração centralizada para scripts globais (`check-types`).
- Linting, formatting, e verificação de tipos em todos os workspaces.
- Scripts para banco de dados e pre-commit hooks.

### Sua missão:

- Coordenar alterações entre frontend e backend.
- Garantir que scripts sejam executados nos contextos corretos.
- Resolver problemas de integração entre pacotes.
- Manter consistência em práticas de desenvolvimento.

### Diretrizes de execução:

**Contexto correto**: Sempre especifique o workspace (`server`, `web`, `shared`, `root`) antes de executar qualquer script.

- Exemplo: `npm run dev --workspace server`.

**Scripts compartilhados**:

- Use scripts globais (`build`, `check-types`) quando aplicável.
- Certifique-se de que todos os workspaces estejam atualizados após mudanças significativas.

**Frontend/backend sincronização**:

- Ao adicionar novos endpoints ou APIs, atualize automaticamente o TypeScript typegen no frontend.
- Teste integração completa antes de considerar a tarefa concluída.

**Testes e linting**:

- Execute linting e typechecking em todos os workspaces após grandes mudanças:
  ```bash
  npm run check-types --workspaces
  npm run lint --workspaces
  ```

**Documentação**:

- Atualize READMEs ou documentação relevante sempre que necessário.

### Exemplos de ações esperadas:

- Adicionar um novo endpoint `/api/users/profile` no backend e atualizar o tipo correspondente no frontend.
- Criar um componente React reutilizável com suporte para dark mode e tipagem TypeScript.
- Refatorar o sistema de logs no backend para usar `pino-pretty`.
- Corrigir um bug de integração entre Prisma e Fastify.
- Manipulando datas: nunca use new Date diretamente. Sempre utilize as funções utilitárias fornecidas.

> ⚠️ **Atenção**: NUNCA faça alterações sem garantir que impactos colaterais foram avaliados em todos os workspaces.

</instruções>
