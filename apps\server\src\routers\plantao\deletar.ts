import { z } from "zod";
import { prisma, withAudit } from "@/lib/prisma";
import { createPlantaoSchema, type CreatePlantaoInput, type UpdatePlantaoInput } from "@/schemas/plantao.schema";
import type { FastifyTypedInstance } from "@/types";
import { formatDateToDateTime, parseUTCDate, toISOString } from "@shared/date";
import { authorize } from "@/middlewares/auth.middleware";

export function deletarPlantaoRouter(fastify: FastifyTypedInstance) {
  // Deletar plantão
  fastify.delete<{ Params: { uuid: string } }>(
    "/plantoes/:uuid",
    { preHandler: [authorize("master")] },
    withAudit(async (request, reply) => {
      const { uuid } = request.params;
      const clienteId = request.clienteId;

      // Verificar se plantão existe e pertence ao cliente
      const where = {
        uuid,
        clienteId,
      };

      const plantao = await prisma.plantao.findFirst({
        where,
        include: {
          _count: {
            select: {
              fechamentos: true,
            },
          },
        },
      });

      if (!plantao) {
        return reply.status(404).send({ error: "Plantão não encontrado" });
      }

      // Verificar se tem fechamentos associados
      if (plantao._count.fechamentos > 0) {
        return reply.status(400).send({
          error: "Não é possível excluir plantão com fechamentos associados",
        });
      }

      await prisma.plantao.delete({
        where: { uuid },
      });

      return reply.status(204).send();
    })
  );
}
