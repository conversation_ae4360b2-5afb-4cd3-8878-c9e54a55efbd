import { useState, useEffect } from "react";
import { useQuery, useQueryClient } from "@tanstack/react-query";
import { api, type Especialidade } from "@/lib/api";
import { MultiSelect } from "@/components/ui/multi-select";
import { EspecialidadeDialog } from "@/components/especialidade-dialog";

interface EspecialidadeSelectProps {
  value: number[];
  onChange: (value: number[]) => void;
  placeholder?: string;
  searchPlaceholder?: string;
  emptyText?: string;
  addNewText?: string;
  className?: string;
  disabled?: boolean;
}

export function EspecialidadeSelect({
  value,
  onChange,
  placeholder = "Selecione as especialidades",
  searchPlaceholder = "Buscar especialidade...",
  emptyText = "Nenhuma especialidade encontrada",
  addNewText = "Adicionar nova especialidade",
  className,
  disabled,
}: EspecialidadeSelectProps) {
  const [dialogOpen, setDialogOpen] = useState(false);
  const [searchText, setSearchText] = useState("");
  const [initialNome, setInitialNome] = useState("");
  const queryClient = useQueryClient();

  const { data: especialidadesData } = useQuery({
    queryKey: ["especialidades"],
    queryFn: () => api.get<{ data: Especialidade[] }>("/especialidades?limit=100"),
  });

  const especialidades = especialidadesData?.data || [];

  const handleAddNew = () => {
    // Captura o texto de busca atual para usar como nome inicial
    setInitialNome(searchText);
    setDialogOpen(true);
  };

  const handleSearch = (search: string) => {
    setSearchText(search);
  };

  const handleSuccess = (especialidade: Especialidade) => {
    // Adiciona a nova especialidade à lista selecionada
    onChange([...value, especialidade.id]);
    // Limpa o texto de busca
    setSearchText("");
    setInitialNome("");
  };

  // Limpa o nome inicial quando o dialog fecha
  useEffect(() => {
    if (!dialogOpen) {
      setInitialNome("");
    }
  }, [dialogOpen]);

  return (
    <>
      <MultiSelect
        options={especialidades.map((e) => ({
          value: e.id,
          label: e.nome,
        }))}
        value={value}
        onChange={(newValue) => onChange(newValue as number[])}
        placeholder={placeholder}
        searchPlaceholder={searchPlaceholder}
        emptyText={emptyText}
        onAddNew={handleAddNew}
        addNewText={addNewText}
        onSearch={handleSearch}
        className={className}
        disabled={disabled}
      />

      <EspecialidadeDialog
        open={dialogOpen}
        onOpenChange={setDialogOpen}
        onSuccess={handleSuccess}
        initialNome={initialNome}
      />
    </>
  );
}
