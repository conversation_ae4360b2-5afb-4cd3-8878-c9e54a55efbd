import { useState, useEffect, useRef } from "react";
import {
  <PERSON><PERSON>,
  DialogContent,
  DialogDescription,
  DialogHeader,
  DialogTitle,
  DialogFooter,
} from "@/components/ui/dialog";
import { Button } from "@/components/ui/button";
import { Textarea } from "@/components/ui/textarea";
import { Alert, AlertDescription } from "@/components/ui/alert";
import { Badge } from "@/components/ui/badge";
import { Loader2, MapPin, Camera, AlertCircle, CheckCircle, X } from "lucide-react";
import { cn } from "@/lib/utils";

interface CheckInOutModalProps {
  isOpen: boolean;
  onClose: () => void;
  onConfirm: (data: CheckInOutData) => void;
  type: "checkin" | "checkout";
  shiftInfo: {
    cliente: string;
    local: string;
    endereco: string;
    horario: {
      entrada: string;
      saida: string;
    };
  };
  isLoading?: boolean;
}

interface CheckInOutData {
  latitude?: number;
  longitude?: number;
  photo?: string;
  observacao?: string;
}

export function CheckInOutModal({
  isOpen,
  onClose,
  onConfirm,
  type,
  shiftInfo,
  isLoading = false,
}: CheckInOutModalProps) {
  const [location, setLocation] = useState<{ latitude: number; longitude: number } | null>(null);
  const [locationError, setLocationError] = useState<string | null>(null);
  const [isGettingLocation, setIsGettingLocation] = useState(false);
  const [photo, setPhoto] = useState<string | null>(null);
  const [observacao, setObservacao] = useState("");
  const videoRef = useRef<HTMLVideoElement>(null);
  const canvasRef = useRef<HTMLCanvasElement>(null);
  const streamRef = useRef<MediaStream | null>(null);
  const [isCameraActive, setIsCameraActive] = useState(false);

  // Obter localização ao abrir modal
  useEffect(() => {
    if (isOpen) {
      getLocation();
    }
    return () => {
      // Limpar câmera ao fechar
      if (streamRef.current) {
        streamRef.current.getTracks().forEach((track) => track.stop());
        streamRef.current = null;
        setIsCameraActive(false);
      }
    };
  }, [isOpen]);

  const getLocation = () => {
    setIsGettingLocation(true);
    setLocationError(null);

    if (!navigator.geolocation) {
      setLocationError("Geolocalização não é suportada pelo seu navegador");
      setIsGettingLocation(false);
      return;
    }

    navigator.geolocation.getCurrentPosition(
      (position) => {
        setLocation({
          latitude: position.coords.latitude,
          longitude: position.coords.longitude,
        });
        setIsGettingLocation(false);
      },
      (error) => {
        let errorMessage = "Erro ao obter localização";
        switch (error.code) {
          case error.PERMISSION_DENIED:
            errorMessage = "Permissão de localização negada";
            break;
          case error.POSITION_UNAVAILABLE:
            errorMessage = "Localização indisponível";
            break;
          case error.TIMEOUT:
            errorMessage = "Tempo esgotado ao obter localização";
            break;
        }
        setLocationError(errorMessage);
        setIsGettingLocation(false);
      },
      {
        enableHighAccuracy: true,
        timeout: 10000,
        maximumAge: 0,
      }
    );
  };

  const startCamera = async () => {
    try {
      const stream = await navigator.mediaDevices.getUserMedia({
        video: { facingMode: "user" },
      });
      if (videoRef.current) {
        videoRef.current.srcObject = stream;
        streamRef.current = stream;
        setIsCameraActive(true);
      }
    } catch (error) {
      console.error("Erro ao acessar câmera:", error);
      setLocationError("Não foi possível acessar a câmera");
    }
  };

  const stopCamera = () => {
    if (streamRef.current) {
      streamRef.current.getTracks().forEach((track) => track.stop());
      streamRef.current = null;
      setIsCameraActive(false);
    }
  };

  const takePhoto = () => {
    if (videoRef.current && canvasRef.current) {
      const video = videoRef.current;
      const canvas = canvasRef.current;
      const context = canvas.getContext("2d");

      if (context) {
        canvas.width = video.videoWidth;
        canvas.height = video.videoHeight;
        context.drawImage(video, 0, 0);
        const dataUrl = canvas.toDataURL("image/jpeg", 0.8);
        setPhoto(dataUrl);
        stopCamera();
      }
    }
  };

  const removePhoto = () => {
    setPhoto(null);
  };

  const handleConfirm = () => {
    const data: CheckInOutData = {
      observacao: observacao.trim() || undefined,
    };

    if (location) {
      data.latitude = location.latitude;
      data.longitude = location.longitude;
    }

    if (photo) {
      data.photo = photo;
    }

    onConfirm(data);
  };

  const handleClose = () => {
    setLocation(null);
    setLocationError(null);
    setPhoto(null);
    setObservacao("");
    stopCamera();
    onClose();
  };

  return (
    <Dialog open={isOpen} onOpenChange={handleClose}>
      <DialogContent className="sm:max-w-[500px]">
        <DialogHeader>
          <DialogTitle>{type === "checkin" ? "Confirmar Check-in" : "Confirmar Check-out"}</DialogTitle>
          <DialogDescription>
            {type === "checkin" ? "Registre sua entrada no plantão" : "Registre sua saída do plantão"}
          </DialogDescription>
        </DialogHeader>

        <div className="space-y-4">
          {/* Informações do plantão */}
          <div className="rounded-lg border p-3 space-y-2 bg-muted/50">
            <div className="font-semibold">{shiftInfo.cliente}</div>
            <div className="text-sm text-muted-foreground">{shiftInfo.local}</div>
            <div className="text-xs text-muted-foreground">{shiftInfo.endereco}</div>
            <div className="flex gap-2 text-sm">
              <Badge variant="outline">Entrada: {shiftInfo.horario.entrada}</Badge>
              <Badge variant="outline">Saída: {shiftInfo.horario.saida}</Badge>
            </div>
          </div>

          {/* Status da localização */}
          <div className="space-y-2">
            <div className="flex items-center justify-between">
              <span className="text-sm font-medium">Localização</span>
              {isGettingLocation && <Loader2 className="h-4 w-4 animate-spin" />}
            </div>
            {location ? (
              <Alert className="border-green-200 bg-green-50">
                <CheckCircle className="h-4 w-4 text-green-600" />
                <AlertDescription className="text-green-700">
                  Localização obtida com sucesso
                  <div className="text-xs mt-1 font-mono">
                    Lat: {location.latitude.toFixed(6)}, Lng: {location.longitude.toFixed(6)}
                  </div>
                </AlertDescription>
              </Alert>
            ) : locationError ? (
              <Alert className="border-yellow-200 bg-yellow-50">
                <AlertCircle className="h-4 w-4 text-yellow-600" />
                <AlertDescription className="text-yellow-700">
                  {locationError}
                  <Button variant="link" size="sm" onClick={getLocation} className="ml-2 h-auto p-0">
                    Tentar novamente
                  </Button>
                </AlertDescription>
              </Alert>
            ) : (
              <Alert>
                <MapPin className="h-4 w-4" />
                <AlertDescription>Obtendo localização...</AlertDescription>
              </Alert>
            )}
          </div>

          {/* Câmera/Foto */}
          <div className="space-y-2">
            <div className="flex items-center justify-between">
              <span className="text-sm font-medium">Foto (opcional)</span>
              {!photo && !isCameraActive && (
                <Button type="button" variant="outline" size="sm" onClick={startCamera}>
                  <Camera className="h-4 w-4 mr-2" />
                  Tirar Foto
                </Button>
              )}
            </div>

            {isCameraActive && (
              <div className="space-y-2">
                <div className="relative rounded-lg overflow-hidden bg-black">
                  <video ref={videoRef} autoPlay playsInline className="w-full" />
                </div>
                <div className="flex gap-2">
                  <Button type="button" onClick={takePhoto} className="flex-1">
                    Capturar
                  </Button>
                  <Button type="button" variant="outline" onClick={stopCamera} className="flex-1">
                    Cancelar
                  </Button>
                </div>
              </div>
            )}

            {photo && (
              <div className="relative rounded-lg overflow-hidden">
                <img src={photo} alt="Foto do check-in" className="w-full" />
                <Button
                  type="button"
                  variant="destructive"
                  size="icon"
                  className="absolute top-2 right-2"
                  onClick={removePhoto}
                >
                  <X className="h-4 w-4" />
                </Button>
              </div>
            )}

            <canvas ref={canvasRef} className="hidden" />
          </div>

          {/* Observações */}
          <div className="space-y-2">
            <label htmlFor="observacao" className="text-sm font-medium">
              Observações (opcional)
            </label>
            <Textarea
              id="observacao"
              placeholder="Adicione observações se necessário..."
              value={observacao}
              onChange={(e) => setObservacao(e.target.value)}
              rows={3}
            />
          </div>
        </div>

        <DialogFooter className="gap-2">
          <Button type="button" variant="outline" onClick={handleClose} disabled={isLoading}>
            Cancelar
          </Button>
          <Button
            onClick={handleConfirm}
            disabled={isLoading || isGettingLocation}
            className={cn(type === "checkin" ? "" : "bg-red-600 hover:bg-red-700")}
          >
            {isLoading ? (
              <>
                <Loader2 className="mr-2 h-4 w-4 animate-spin" />
                Processando...
              </>
            ) : (
              <>{type === "checkin" ? "Confirmar Check-in" : "Confirmar Check-out"}</>
            )}
          </Button>
        </DialogFooter>
      </DialogContent>
    </Dialog>
  );
}
