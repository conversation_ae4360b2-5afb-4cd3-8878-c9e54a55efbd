import { create } from "zustand";
import { persist } from "zustand/middleware";
import { AUTH_STORAGE_KEY, JWT_TOKEN_KEY } from "@/lib/consts";
import type { RoleType } from "@shared/types";

interface Perfil {
  id: string;
  nome: string;
  descricao?: string;
}

interface Cliente {
  id: string;
  nome: string;
  fusoHorario?: string;
}

interface User {
  id: string;
  uuid: string;
  nome: string;
  email: string;
  roles: RoleType[];
  tipo?: "ADMIN" | "PROFISSIONAL";
  termosAceitos?: boolean;
  deveResetarSenha?: boolean;

  cpf: string;
  telefone: string;
  dataNascimento: Date | null;
  genero: string;
  estadoCivil: string;
  nacionalidade: string;
  cep: string;
  logradouro: string;
  numero: string;
  complemento: string;
  bairro: string;
  cidade: string;
  uf: string;
  ativo: boolean;
  emailVerificado: boolean;
  emailVerificadoEm: Date;
  tokenRecuperacao: string | null;
  tokenRecuperacaoExpira: Date | null;
  ultimoAcesso: Date;
  createdAt: Date;
  updatedAt: Date;
  profissional: {
    id: string;
    uuid: string;
    usuarioId: number;
    rg: string;
    orgaoEmissor: string;
    cnes: string;
    conselhoClasse: string;
    numeroRegistro: string;
    ufConselho: string;
    banco: string;
    agencia: string;
    digitoAgencia: string;
    conta: string;
    digitoConta: string;
    tipoConta: string;
    chavePix: string;
    tipoPix: string;
    tipoAssinatura: string;
    createdAt: Date;
    updatedAt: Date;
  };
  metaData: {
    onboardingPendente: boolean;
  };
}

interface AuthState {
  user: User | null;
  token: string | null;
  isAuthenticated: boolean;
  hasHydrated: boolean;
  // Perfil e Cliente selecionados
  perfilSelecionado: Perfil | null;
  clienteSelecionado: Cliente | null;
  perfis: Perfil[];
  clientes: Cliente[];
  // Actions
  setAuth: (user: Partial<User>, token: string) => void;
  updateUser: (updates: Partial<User>) => void;
  setPerfis: (perfis: Perfil[]) => void;
  setClientes: (clientes: Cliente[]) => void;
  selecionarPerfil: (perfil: Perfil) => void;
  selecionarCliente: (cliente: Cliente) => void;
  logout: () => void;
}

export const useAuthStore = create<AuthState>()(
  persist(
    (set, get) => ({
      user: null,
      token: null,
      isAuthenticated: false,
      hasHydrated: false,
      perfilSelecionado: null,
      clienteSelecionado: null,
      perfis: [],
      clientes: [],
      setAuth: (user, token) => {
        set({ user: user as User, token, isAuthenticated: true });
      },
      updateUser: (updates) => {
        set((state) => ({
          user: state.user ? { ...state.user, ...updates } : null,
        }));
      },
      setPerfis: (perfis) => set({ perfis }),
      setClientes: (clientes) => set({ clientes }),
      selecionarPerfil: (perfil) => set({ perfilSelecionado: perfil }),
      selecionarCliente: (cliente) => set({ clienteSelecionado: cliente }),
      logout: () => {
        set({
          user: null,
          token: null,
          isAuthenticated: false,
          perfilSelecionado: null,
          clienteSelecionado: null,
          perfis: [],
          clientes: [],
        });
      },
    }),
    {
      name: AUTH_STORAGE_KEY,
      onRehydrateStorage: () => (state) => {
        if (state) {
          // Token agora está apenas no Zustand, não precisa verificar localStorage separado
          if (!state.token) {
            // Se não tem token, limpar a sessão
            state.user = null;
            state.isAuthenticated = false;
          }

          // Use setTimeout to avoid synchronous state updates during hydration
          setTimeout(() => {
            state.hasHydrated = true;
          }, 0);
        }
      },
    }
  )
);

// Hook to safely access hasHydrated
export const useHasHydrated = () => {
  return useAuthStore((state) => state.hasHydrated);
};
