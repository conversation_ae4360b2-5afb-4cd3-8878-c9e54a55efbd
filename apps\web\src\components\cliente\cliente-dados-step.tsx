import { Input } from "@/components/ui/input";
import { Label } from "@/components/ui/label";
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from "@/components/ui/card";
import { Switch } from "@/components/ui/switch";
import { CnpjInput } from "@/components/ui/cnpj-input";
import { PhoneInput } from "@/components/ui/phone-input";
import { useCheckCnpj } from "@/hooks/use-clientes";
import { useState, useEffect } from "react";
import { AlertCircle, CheckCircle } from "lucide-react";
import { PercentageInput } from "../ui/percentage-input";

interface ClienteDadosStepProps {
  form: any;
  isEditing?: boolean;
  existingCnpj?: string;
}

export function ClienteDadosStep({ form, isEditing = false, existingCnpj }: ClienteDadosStepProps) {
  const [cnpjValue, setCnpjValue] = useState("");
  const [shouldCheckCnpj, setShouldCheckCnpj] = useState(false);
  const { data: cnpjCheck, isLoading: isCheckingCnpj } = useCheckCnpj(
    cnpjValue.replace(/\D/g, ""),
    shouldCheckCnpj && !isEditing
  );

  useEffect(() => {
    if (cnpjCheck && cnpjCheck.exists) {
      const cnpjNumbers = cnpjValue.replace(/\D/g, "");
      if (!isEditing || (existingCnpj && cnpjNumbers !== existingCnpj.replace(/\D/g, ""))) {
        form.setFieldMeta("cnpj", { errors: [`CNPJ já cadastrado para: ${cnpjCheck.cliente?.nome}`] });
      }
    } else {
      form.setFieldMeta("cnpj", { errors: [] });
    }
  }, [cnpjCheck, cnpjValue, form, isEditing, existingCnpj]);

  return (
    <Card>
      <CardHeader>
        <CardTitle>Informações do Cliente</CardTitle>
        <CardDescription>Preencha os dados do cliente ou instituição</CardDescription>
      </CardHeader>
      <CardContent className="space-y-6">
        <div className="grid gap-6 md:grid-cols-2">
          <form.Field
            name="nome"
            validators={{
              onChange: ({ value }: { value: string }) => (!value ? "Nome é obrigatório" : undefined),
            }}
          >
            {(field: any) => (
              <div className="space-y-2">
                <Label htmlFor={field.name}>Nome *</Label>
                <Input
                  id={field.name}
                  value={field.state.value}
                  onBlur={field.handleBlur}
                  onChange={(e) => field.handleChange(e.target.value)}
                  placeholder="Nome do cliente ou instituição"
                />
                {field.state.meta.errors && (
                  <p className="text-sm text-destructive">{field.state.meta.errors.join(", ")}</p>
                )}
              </div>
            )}
          </form.Field>

          <form.Field
            name="cnpj"
            validators={{
              onChange: ({ value }: { value: string }) => {
                if (!value) return "CNPJ é obrigatório";
                const numbers = value.replace(/\D/g, "");
                if (numbers.length !== 14) {
                  return "CNPJ deve ter 14 dígitos";
                }
                return undefined;
              },
            }}
          >
            {(field: any) => (
              <div className="space-y-2">
                <div className="relative">
                  <CnpjInput
                    id={field.name}
                    label="CNPJ"
                    required
                    value={field.state.value}
                    onBlur={() => {
                      field.handleBlur();
                      const value = field.state.value;
                      if (value && value.length === 14) {
                        setCnpjValue(value);
                        setShouldCheckCnpj(true);
                      }
                    }}
                    onChange={(value) => {
                      field.handleChange(value);
                      setCnpjValue(value);
                      if (value.length < 14) {
                        setShouldCheckCnpj(false);
                      }
                    }}
                    className={cnpjCheck?.exists && !isEditing ? "border-destructive" : ""}
                    error={field.state.meta.errors?.join(", ")}
                  />
                  {isCheckingCnpj && (
                    <div className="absolute right-2 top-9 -translate-y-1/2">
                      <div className="h-4 w-4 animate-spin rounded-full border-2 border-primary border-t-transparent" />
                    </div>
                  )}
                  {!isCheckingCnpj && cnpjCheck && (
                    <div className="absolute right-2 top-1/2 -translate-y-1/2">
                      {cnpjCheck.exists && !isEditing ? (
                        <AlertCircle className="h-4 w-4 text-destructive" />
                      ) : (
                        cnpjValue.length === 14 && <CheckCircle className="h-4 w-4 text-green-600" />
                      )}
                    </div>
                  )}
                </div>
                {field.state.meta.errors && (
                  <p className="text-sm text-destructive">{field.state.meta.errors.join(", ")}</p>
                )}
                {cnpjCheck?.exists && !isEditing && (
                  <p className="text-sm text-destructive flex items-center gap-1">
                    <AlertCircle className="h-3 w-3" />
                    CNPJ já cadastrado para: {cnpjCheck.cliente?.nome}
                  </p>
                )}
              </div>
            )}
          </form.Field>

          <form.Field
            name="email"
            validators={{
              onChange: ({ value }: { value: string }) => {
                if (value && !/^[^\s@]+@[^\s@]+\.[^\s@]+$/.test(value)) {
                  return "Email inválido";
                }
                return undefined;
              },
            }}
          >
            {(field: any) => (
              <div className="space-y-2">
                <Label htmlFor={field.name}>Email</Label>
                <Input
                  id={field.name}
                  type="email"
                  value={field.state.value}
                  onBlur={field.handleBlur}
                  onChange={(e) => field.handleChange(e.target.value)}
                  placeholder="<EMAIL>"
                />
                {field.state.meta.errors && (
                  <p className="text-sm text-destructive">{field.state.meta.errors.join(", ")}</p>
                )}
              </div>
            )}
          </form.Field>

          <form.Field name="telefone">
            {(field: any) => (
              <PhoneInput
                id={field.name}
                label="Telefone"
                value={field.state.value || ""}
                onBlur={field.handleBlur}
                onChange={field.handleChange}
              />
            )}
          </form.Field>

          <form.Field name="taxaPadrao">
            {(field: any) => (
              <PercentageInput
                value={field.state.value ? field.state.value : null}
                onChange={field.handleChange}
                label="Taxa Padrão"
              />
            )}
          </form.Field>

          <form.Field name="prcentagemMaxima">
            {(field: any) => (
              <div className="space-y-2">
                <PercentageInput
                  value={field.state.value ? field.state.value : 100}
                  onChange={field.handleChange}
                  label="Percentual Máximo de Antecipação"
                  placeholder="100"
                />
                <p className="text-xs text-muted-foreground">Percentual máximo que pode ser antecipado (0-100%)</p>
              </div>
            )}
          </form.Field>

          <form.Field name="ativo">
            {(field: any) => (
              <div className="flex items-center space-x-2">
                <Switch id={field.name} checked={field.state.value} onCheckedChange={field.handleChange} />
                <Label htmlFor={field.name}>Cliente ativo</Label>
              </div>
            )}
          </form.Field>
        </div>

        {/* <div className="space-y-4 border-t pt-6">
          <h3 className="text-lg font-semibold">Endereço</h3>
          <div className="grid gap-4 md:grid-cols-2">
            <form.Field name="cep">
              {(field: any) => (
                <div className="space-y-2">
                  <Label htmlFor={field.name}>CEP</Label>
                  <Input
                    id={field.name}
                    value={formatCEP(field.state.value || "")}
                    onBlur={(e) => {
                      field.handleBlur();
                      handleCepSearch(e.target.value);
                    }}
                    onChange={(e) => field.handleChange(e.target.value.replace(/\D/g, ""))}
                    placeholder="00000-000"
                  />
                </div>
              )}
            </form.Field>

            <form.Field name="uf">
              {(field: any) => (
                <div className="space-y-2">
                  <Label htmlFor={field.name}>UF</Label>
                  <Select value={field.state.value || ""} onValueChange={field.handleChange}>
                    <SelectTrigger>
                      <SelectValue placeholder="Selecione o estado" />
                    </SelectTrigger>
                    <SelectContent>
                      {ESTADOS_BRASILEIROS.map((estado) => (
                        <SelectItem key={estado.value} value={estado.value}>
                          {estado.label}
                        </SelectItem>
                      ))}
                    </SelectContent>
                  </Select>
                </div>
              )}
            </form.Field>

            <form.Field name="cidade">
              {(field: any) => (
                <div className="space-y-2">
                  <Label htmlFor={field.name}>Cidade</Label>
                  <Input
                    id={field.name}
                    value={field.state.value || ""}
                    onBlur={field.handleBlur}
                    onChange={(e) => field.handleChange(e.target.value)}
                    placeholder="Nome da cidade"
                  />
                </div>
              )}
            </form.Field>

            <form.Field name="bairro">
              {(field: any) => (
                <div className="space-y-2">
                  <Label htmlFor={field.name}>Bairro</Label>
                  <Input
                    id={field.name}
                    value={field.state.value || ""}
                    onBlur={field.handleBlur}
                    onChange={(e) => field.handleChange(e.target.value)}
                    placeholder="Nome do bairro"
                  />
                </div>
              )}
            </form.Field>

            <form.Field name="logradouro">
              {(field: any) => (
                <div className="space-y-2 md:col-span-1">
                  <Label htmlFor={field.name}>Logradouro</Label>
                  <Input
                    id={field.name}
                    value={field.state.value || ""}
                    onBlur={field.handleBlur}
                    onChange={(e) => field.handleChange(e.target.value)}
                    placeholder="Rua, Avenida, etc."
                  />
                </div>
              )}
            </form.Field>

            <div className="grid grid-cols-2 gap-4">
              <form.Field name="numero">
                {(field: any) => (
                  <div className="space-y-2">
                    <Label htmlFor={field.name}>Número</Label>
                    <Input
                      id={field.name}
                      value={field.state.value || ""}
                      onBlur={field.handleBlur}
                      onChange={(e) => field.handleChange(e.target.value)}
                      placeholder="123"
                    />
                  </div>
                )}
              </form.Field>

              <form.Field name="complemento">
                {(field: any) => (
                  <div className="space-y-2">
                    <Label htmlFor={field.name}>Complemento</Label>
                    <Input
                      id={field.name}
                      value={field.state.value || ""}
                      onBlur={field.handleBlur}
                      onChange={(e) => field.handleChange(e.target.value)}
                      placeholder="Apto, Sala, etc."
                    />
                  </div>
                )}
              </form.Field>
            </div>
          </div>
        </div> */}
      </CardContent>
    </Card>
  );
}
