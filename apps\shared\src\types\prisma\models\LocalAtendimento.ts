
/* !!! This is code generated by Prisma. Do not edit directly. !!! */
/* eslint-disable */
// @ts-nocheck 
/*
 * This file exports the `LocalAtendimento` model and its related types.
 *
 * 🟢 You can import this file directly.
 */
import * as runtime from "@prisma/client/runtime/library"
import type * as $Enums from "../enums.ts"
import type * as Prisma from "../internal/prismaNamespace.ts"

/**
 * Model LocalAtendimento
 * 
 */
export type LocalAtendimentoModel = runtime.Types.Result.DefaultSelection<Prisma.$LocalAtendimentoPayload>

export type AggregateLocalAtendimento = {
  _count: LocalAtendimentoCountAggregateOutputType | null
  _avg: LocalAtendimentoAvgAggregateOutputType | null
  _sum: LocalAtendimentoSumAggregateOutputType | null
  _min: LocalAtendimentoMinAggregateOutputType | null
  _max: LocalAtendimentoMaxAggregateOutputType | null
}

export type LocalAtendimentoAvgAggregateOutputType = {
  id: number | null
  clienteId: number | null
  latitude: number | null
  longitude: number | null
}

export type LocalAtendimentoSumAggregateOutputType = {
  id: number | null
  clienteId: number | null
  latitude: number | null
  longitude: number | null
}

export type LocalAtendimentoMinAggregateOutputType = {
  id: number | null
  uuid: string | null
  clienteId: number | null
  nome: string | null
  endereco: string | null
  cidade: string | null
  estado: string | null
  cep: string | null
  telefone: string | null
  responsavel: string | null
  observacoes: string | null
  latitude: number | null
  longitude: number | null
  ativo: boolean | null
  fusoHorario: string | null
  createdAt: Date | null
  updatedAt: Date | null
  deletedAt: Date | null
}

export type LocalAtendimentoMaxAggregateOutputType = {
  id: number | null
  uuid: string | null
  clienteId: number | null
  nome: string | null
  endereco: string | null
  cidade: string | null
  estado: string | null
  cep: string | null
  telefone: string | null
  responsavel: string | null
  observacoes: string | null
  latitude: number | null
  longitude: number | null
  ativo: boolean | null
  fusoHorario: string | null
  createdAt: Date | null
  updatedAt: Date | null
  deletedAt: Date | null
}

export type LocalAtendimentoCountAggregateOutputType = {
  id: number
  uuid: number
  clienteId: number
  nome: number
  endereco: number
  cidade: number
  estado: number
  cep: number
  telefone: number
  responsavel: number
  observacoes: number
  latitude: number
  longitude: number
  ativo: number
  fusoHorario: number
  createdAt: number
  updatedAt: number
  deletedAt: number
  _all: number
}


export type LocalAtendimentoAvgAggregateInputType = {
  id?: true
  clienteId?: true
  latitude?: true
  longitude?: true
}

export type LocalAtendimentoSumAggregateInputType = {
  id?: true
  clienteId?: true
  latitude?: true
  longitude?: true
}

export type LocalAtendimentoMinAggregateInputType = {
  id?: true
  uuid?: true
  clienteId?: true
  nome?: true
  endereco?: true
  cidade?: true
  estado?: true
  cep?: true
  telefone?: true
  responsavel?: true
  observacoes?: true
  latitude?: true
  longitude?: true
  ativo?: true
  fusoHorario?: true
  createdAt?: true
  updatedAt?: true
  deletedAt?: true
}

export type LocalAtendimentoMaxAggregateInputType = {
  id?: true
  uuid?: true
  clienteId?: true
  nome?: true
  endereco?: true
  cidade?: true
  estado?: true
  cep?: true
  telefone?: true
  responsavel?: true
  observacoes?: true
  latitude?: true
  longitude?: true
  ativo?: true
  fusoHorario?: true
  createdAt?: true
  updatedAt?: true
  deletedAt?: true
}

export type LocalAtendimentoCountAggregateInputType = {
  id?: true
  uuid?: true
  clienteId?: true
  nome?: true
  endereco?: true
  cidade?: true
  estado?: true
  cep?: true
  telefone?: true
  responsavel?: true
  observacoes?: true
  latitude?: true
  longitude?: true
  ativo?: true
  fusoHorario?: true
  createdAt?: true
  updatedAt?: true
  deletedAt?: true
  _all?: true
}

export type LocalAtendimentoAggregateArgs<ExtArgs extends runtime.Types.Extensions.InternalArgs = runtime.Types.Extensions.DefaultArgs> = {
  /**
   * Filter which LocalAtendimento to aggregate.
   */
  where?: Prisma.LocalAtendimentoWhereInput
  /**
   * {@link https://www.prisma.io/docs/concepts/components/prisma-client/sorting Sorting Docs}
   * 
   * Determine the order of LocalAtendimentos to fetch.
   */
  orderBy?: Prisma.LocalAtendimentoOrderByWithRelationInput | Prisma.LocalAtendimentoOrderByWithRelationInput[]
  /**
   * {@link https://www.prisma.io/docs/concepts/components/prisma-client/pagination#cursor-based-pagination Cursor Docs}
   * 
   * Sets the start position
   */
  cursor?: Prisma.LocalAtendimentoWhereUniqueInput
  /**
   * {@link https://www.prisma.io/docs/concepts/components/prisma-client/pagination Pagination Docs}
   * 
   * Take `±n` LocalAtendimentos from the position of the cursor.
   */
  take?: number
  /**
   * {@link https://www.prisma.io/docs/concepts/components/prisma-client/pagination Pagination Docs}
   * 
   * Skip the first `n` LocalAtendimentos.
   */
  skip?: number
  /**
   * {@link https://www.prisma.io/docs/concepts/components/prisma-client/aggregations Aggregation Docs}
   * 
   * Count returned LocalAtendimentos
  **/
  _count?: true | LocalAtendimentoCountAggregateInputType
  /**
   * {@link https://www.prisma.io/docs/concepts/components/prisma-client/aggregations Aggregation Docs}
   * 
   * Select which fields to average
  **/
  _avg?: LocalAtendimentoAvgAggregateInputType
  /**
   * {@link https://www.prisma.io/docs/concepts/components/prisma-client/aggregations Aggregation Docs}
   * 
   * Select which fields to sum
  **/
  _sum?: LocalAtendimentoSumAggregateInputType
  /**
   * {@link https://www.prisma.io/docs/concepts/components/prisma-client/aggregations Aggregation Docs}
   * 
   * Select which fields to find the minimum value
  **/
  _min?: LocalAtendimentoMinAggregateInputType
  /**
   * {@link https://www.prisma.io/docs/concepts/components/prisma-client/aggregations Aggregation Docs}
   * 
   * Select which fields to find the maximum value
  **/
  _max?: LocalAtendimentoMaxAggregateInputType
}

export type GetLocalAtendimentoAggregateType<T extends LocalAtendimentoAggregateArgs> = {
      [P in keyof T & keyof AggregateLocalAtendimento]: P extends '_count' | 'count'
    ? T[P] extends true
      ? number
      : Prisma.GetScalarType<T[P], AggregateLocalAtendimento[P]>
    : Prisma.GetScalarType<T[P], AggregateLocalAtendimento[P]>
}




export type LocalAtendimentoGroupByArgs<ExtArgs extends runtime.Types.Extensions.InternalArgs = runtime.Types.Extensions.DefaultArgs> = {
  where?: Prisma.LocalAtendimentoWhereInput
  orderBy?: Prisma.LocalAtendimentoOrderByWithAggregationInput | Prisma.LocalAtendimentoOrderByWithAggregationInput[]
  by: Prisma.LocalAtendimentoScalarFieldEnum[] | Prisma.LocalAtendimentoScalarFieldEnum
  having?: Prisma.LocalAtendimentoScalarWhereWithAggregatesInput
  take?: number
  skip?: number
  _count?: LocalAtendimentoCountAggregateInputType | true
  _avg?: LocalAtendimentoAvgAggregateInputType
  _sum?: LocalAtendimentoSumAggregateInputType
  _min?: LocalAtendimentoMinAggregateInputType
  _max?: LocalAtendimentoMaxAggregateInputType
}

export type LocalAtendimentoGroupByOutputType = {
  id: number
  uuid: string
  clienteId: number
  nome: string
  endereco: string
  cidade: string
  estado: string
  cep: string
  telefone: string | null
  responsavel: string | null
  observacoes: string | null
  latitude: number | null
  longitude: number | null
  ativo: boolean
  fusoHorario: string
  createdAt: Date
  updatedAt: Date
  deletedAt: Date | null
  _count: LocalAtendimentoCountAggregateOutputType | null
  _avg: LocalAtendimentoAvgAggregateOutputType | null
  _sum: LocalAtendimentoSumAggregateOutputType | null
  _min: LocalAtendimentoMinAggregateOutputType | null
  _max: LocalAtendimentoMaxAggregateOutputType | null
}

type GetLocalAtendimentoGroupByPayload<T extends LocalAtendimentoGroupByArgs> = Prisma.PrismaPromise<
  Array<
    Prisma.PickEnumerable<LocalAtendimentoGroupByOutputType, T['by']> &
      {
        [P in ((keyof T) & (keyof LocalAtendimentoGroupByOutputType))]: P extends '_count'
          ? T[P] extends boolean
            ? number
            : Prisma.GetScalarType<T[P], LocalAtendimentoGroupByOutputType[P]>
          : Prisma.GetScalarType<T[P], LocalAtendimentoGroupByOutputType[P]>
      }
    >
  >



export type LocalAtendimentoWhereInput = {
  AND?: Prisma.LocalAtendimentoWhereInput | Prisma.LocalAtendimentoWhereInput[]
  OR?: Prisma.LocalAtendimentoWhereInput[]
  NOT?: Prisma.LocalAtendimentoWhereInput | Prisma.LocalAtendimentoWhereInput[]
  id?: Prisma.IntFilter<"LocalAtendimento"> | number
  uuid?: Prisma.StringFilter<"LocalAtendimento"> | string
  clienteId?: Prisma.IntFilter<"LocalAtendimento"> | number
  nome?: Prisma.StringFilter<"LocalAtendimento"> | string
  endereco?: Prisma.StringFilter<"LocalAtendimento"> | string
  cidade?: Prisma.StringFilter<"LocalAtendimento"> | string
  estado?: Prisma.StringFilter<"LocalAtendimento"> | string
  cep?: Prisma.StringFilter<"LocalAtendimento"> | string
  telefone?: Prisma.StringNullableFilter<"LocalAtendimento"> | string | null
  responsavel?: Prisma.StringNullableFilter<"LocalAtendimento"> | string | null
  observacoes?: Prisma.StringNullableFilter<"LocalAtendimento"> | string | null
  latitude?: Prisma.FloatNullableFilter<"LocalAtendimento"> | number | null
  longitude?: Prisma.FloatNullableFilter<"LocalAtendimento"> | number | null
  ativo?: Prisma.BoolFilter<"LocalAtendimento"> | boolean
  fusoHorario?: Prisma.StringFilter<"LocalAtendimento"> | string
  createdAt?: Prisma.DateTimeFilter<"LocalAtendimento"> | Date | string
  updatedAt?: Prisma.DateTimeFilter<"LocalAtendimento"> | Date | string
  deletedAt?: Prisma.DateTimeNullableFilter<"LocalAtendimento"> | Date | string | null
  cliente?: Prisma.XOR<Prisma.ClienteScalarRelationFilter, Prisma.ClienteWhereInput>
  plantoes?: Prisma.PlantaoListRelationFilter
  especialidades?: Prisma.LocalAtendimentoEspecialidadesListRelationFilter
}

export type LocalAtendimentoOrderByWithRelationInput = {
  id?: Prisma.SortOrder
  uuid?: Prisma.SortOrder
  clienteId?: Prisma.SortOrder
  nome?: Prisma.SortOrder
  endereco?: Prisma.SortOrder
  cidade?: Prisma.SortOrder
  estado?: Prisma.SortOrder
  cep?: Prisma.SortOrder
  telefone?: Prisma.SortOrderInput | Prisma.SortOrder
  responsavel?: Prisma.SortOrderInput | Prisma.SortOrder
  observacoes?: Prisma.SortOrderInput | Prisma.SortOrder
  latitude?: Prisma.SortOrderInput | Prisma.SortOrder
  longitude?: Prisma.SortOrderInput | Prisma.SortOrder
  ativo?: Prisma.SortOrder
  fusoHorario?: Prisma.SortOrder
  createdAt?: Prisma.SortOrder
  updatedAt?: Prisma.SortOrder
  deletedAt?: Prisma.SortOrderInput | Prisma.SortOrder
  cliente?: Prisma.ClienteOrderByWithRelationInput
  plantoes?: Prisma.PlantaoOrderByRelationAggregateInput
  especialidades?: Prisma.LocalAtendimentoEspecialidadesOrderByRelationAggregateInput
  _relevance?: Prisma.LocalAtendimentoOrderByRelevanceInput
}

export type LocalAtendimentoWhereUniqueInput = Prisma.AtLeast<{
  id?: number
  uuid?: string
  clienteId_nome_endereco_cidade_estado_cep?: Prisma.LocalAtendimentoClienteIdNomeEnderecoCidadeEstadoCepCompoundUniqueInput
  AND?: Prisma.LocalAtendimentoWhereInput | Prisma.LocalAtendimentoWhereInput[]
  OR?: Prisma.LocalAtendimentoWhereInput[]
  NOT?: Prisma.LocalAtendimentoWhereInput | Prisma.LocalAtendimentoWhereInput[]
  clienteId?: Prisma.IntFilter<"LocalAtendimento"> | number
  nome?: Prisma.StringFilter<"LocalAtendimento"> | string
  endereco?: Prisma.StringFilter<"LocalAtendimento"> | string
  cidade?: Prisma.StringFilter<"LocalAtendimento"> | string
  estado?: Prisma.StringFilter<"LocalAtendimento"> | string
  cep?: Prisma.StringFilter<"LocalAtendimento"> | string
  telefone?: Prisma.StringNullableFilter<"LocalAtendimento"> | string | null
  responsavel?: Prisma.StringNullableFilter<"LocalAtendimento"> | string | null
  observacoes?: Prisma.StringNullableFilter<"LocalAtendimento"> | string | null
  latitude?: Prisma.FloatNullableFilter<"LocalAtendimento"> | number | null
  longitude?: Prisma.FloatNullableFilter<"LocalAtendimento"> | number | null
  ativo?: Prisma.BoolFilter<"LocalAtendimento"> | boolean
  fusoHorario?: Prisma.StringFilter<"LocalAtendimento"> | string
  createdAt?: Prisma.DateTimeFilter<"LocalAtendimento"> | Date | string
  updatedAt?: Prisma.DateTimeFilter<"LocalAtendimento"> | Date | string
  deletedAt?: Prisma.DateTimeNullableFilter<"LocalAtendimento"> | Date | string | null
  cliente?: Prisma.XOR<Prisma.ClienteScalarRelationFilter, Prisma.ClienteWhereInput>
  plantoes?: Prisma.PlantaoListRelationFilter
  especialidades?: Prisma.LocalAtendimentoEspecialidadesListRelationFilter
}, "id" | "uuid" | "clienteId_nome_endereco_cidade_estado_cep">

export type LocalAtendimentoOrderByWithAggregationInput = {
  id?: Prisma.SortOrder
  uuid?: Prisma.SortOrder
  clienteId?: Prisma.SortOrder
  nome?: Prisma.SortOrder
  endereco?: Prisma.SortOrder
  cidade?: Prisma.SortOrder
  estado?: Prisma.SortOrder
  cep?: Prisma.SortOrder
  telefone?: Prisma.SortOrderInput | Prisma.SortOrder
  responsavel?: Prisma.SortOrderInput | Prisma.SortOrder
  observacoes?: Prisma.SortOrderInput | Prisma.SortOrder
  latitude?: Prisma.SortOrderInput | Prisma.SortOrder
  longitude?: Prisma.SortOrderInput | Prisma.SortOrder
  ativo?: Prisma.SortOrder
  fusoHorario?: Prisma.SortOrder
  createdAt?: Prisma.SortOrder
  updatedAt?: Prisma.SortOrder
  deletedAt?: Prisma.SortOrderInput | Prisma.SortOrder
  _count?: Prisma.LocalAtendimentoCountOrderByAggregateInput
  _avg?: Prisma.LocalAtendimentoAvgOrderByAggregateInput
  _max?: Prisma.LocalAtendimentoMaxOrderByAggregateInput
  _min?: Prisma.LocalAtendimentoMinOrderByAggregateInput
  _sum?: Prisma.LocalAtendimentoSumOrderByAggregateInput
}

export type LocalAtendimentoScalarWhereWithAggregatesInput = {
  AND?: Prisma.LocalAtendimentoScalarWhereWithAggregatesInput | Prisma.LocalAtendimentoScalarWhereWithAggregatesInput[]
  OR?: Prisma.LocalAtendimentoScalarWhereWithAggregatesInput[]
  NOT?: Prisma.LocalAtendimentoScalarWhereWithAggregatesInput | Prisma.LocalAtendimentoScalarWhereWithAggregatesInput[]
  id?: Prisma.IntWithAggregatesFilter<"LocalAtendimento"> | number
  uuid?: Prisma.StringWithAggregatesFilter<"LocalAtendimento"> | string
  clienteId?: Prisma.IntWithAggregatesFilter<"LocalAtendimento"> | number
  nome?: Prisma.StringWithAggregatesFilter<"LocalAtendimento"> | string
  endereco?: Prisma.StringWithAggregatesFilter<"LocalAtendimento"> | string
  cidade?: Prisma.StringWithAggregatesFilter<"LocalAtendimento"> | string
  estado?: Prisma.StringWithAggregatesFilter<"LocalAtendimento"> | string
  cep?: Prisma.StringWithAggregatesFilter<"LocalAtendimento"> | string
  telefone?: Prisma.StringNullableWithAggregatesFilter<"LocalAtendimento"> | string | null
  responsavel?: Prisma.StringNullableWithAggregatesFilter<"LocalAtendimento"> | string | null
  observacoes?: Prisma.StringNullableWithAggregatesFilter<"LocalAtendimento"> | string | null
  latitude?: Prisma.FloatNullableWithAggregatesFilter<"LocalAtendimento"> | number | null
  longitude?: Prisma.FloatNullableWithAggregatesFilter<"LocalAtendimento"> | number | null
  ativo?: Prisma.BoolWithAggregatesFilter<"LocalAtendimento"> | boolean
  fusoHorario?: Prisma.StringWithAggregatesFilter<"LocalAtendimento"> | string
  createdAt?: Prisma.DateTimeWithAggregatesFilter<"LocalAtendimento"> | Date | string
  updatedAt?: Prisma.DateTimeWithAggregatesFilter<"LocalAtendimento"> | Date | string
  deletedAt?: Prisma.DateTimeNullableWithAggregatesFilter<"LocalAtendimento"> | Date | string | null
}

export type LocalAtendimentoCreateInput = {
  uuid?: string
  nome: string
  endereco: string
  cidade: string
  estado: string
  cep: string
  telefone?: string | null
  responsavel?: string | null
  observacoes?: string | null
  latitude?: number | null
  longitude?: number | null
  ativo?: boolean
  fusoHorario?: string
  createdAt?: Date | string
  updatedAt?: Date | string
  deletedAt?: Date | string | null
  cliente: Prisma.ClienteCreateNestedOneWithoutLocaisAtendimentoInput
  plantoes?: Prisma.PlantaoCreateNestedManyWithoutLocalAtendimentoInput
  especialidades?: Prisma.LocalAtendimentoEspecialidadesCreateNestedManyWithoutLocalAtendimentoInput
}

export type LocalAtendimentoUncheckedCreateInput = {
  id?: number
  uuid?: string
  clienteId: number
  nome: string
  endereco: string
  cidade: string
  estado: string
  cep: string
  telefone?: string | null
  responsavel?: string | null
  observacoes?: string | null
  latitude?: number | null
  longitude?: number | null
  ativo?: boolean
  fusoHorario?: string
  createdAt?: Date | string
  updatedAt?: Date | string
  deletedAt?: Date | string | null
  plantoes?: Prisma.PlantaoUncheckedCreateNestedManyWithoutLocalAtendimentoInput
  especialidades?: Prisma.LocalAtendimentoEspecialidadesUncheckedCreateNestedManyWithoutLocalAtendimentoInput
}

export type LocalAtendimentoUpdateInput = {
  uuid?: Prisma.StringFieldUpdateOperationsInput | string
  nome?: Prisma.StringFieldUpdateOperationsInput | string
  endereco?: Prisma.StringFieldUpdateOperationsInput | string
  cidade?: Prisma.StringFieldUpdateOperationsInput | string
  estado?: Prisma.StringFieldUpdateOperationsInput | string
  cep?: Prisma.StringFieldUpdateOperationsInput | string
  telefone?: Prisma.NullableStringFieldUpdateOperationsInput | string | null
  responsavel?: Prisma.NullableStringFieldUpdateOperationsInput | string | null
  observacoes?: Prisma.NullableStringFieldUpdateOperationsInput | string | null
  latitude?: Prisma.NullableFloatFieldUpdateOperationsInput | number | null
  longitude?: Prisma.NullableFloatFieldUpdateOperationsInput | number | null
  ativo?: Prisma.BoolFieldUpdateOperationsInput | boolean
  fusoHorario?: Prisma.StringFieldUpdateOperationsInput | string
  createdAt?: Prisma.DateTimeFieldUpdateOperationsInput | Date | string
  updatedAt?: Prisma.DateTimeFieldUpdateOperationsInput | Date | string
  deletedAt?: Prisma.NullableDateTimeFieldUpdateOperationsInput | Date | string | null
  cliente?: Prisma.ClienteUpdateOneRequiredWithoutLocaisAtendimentoNestedInput
  plantoes?: Prisma.PlantaoUpdateManyWithoutLocalAtendimentoNestedInput
  especialidades?: Prisma.LocalAtendimentoEspecialidadesUpdateManyWithoutLocalAtendimentoNestedInput
}

export type LocalAtendimentoUncheckedUpdateInput = {
  id?: Prisma.IntFieldUpdateOperationsInput | number
  uuid?: Prisma.StringFieldUpdateOperationsInput | string
  clienteId?: Prisma.IntFieldUpdateOperationsInput | number
  nome?: Prisma.StringFieldUpdateOperationsInput | string
  endereco?: Prisma.StringFieldUpdateOperationsInput | string
  cidade?: Prisma.StringFieldUpdateOperationsInput | string
  estado?: Prisma.StringFieldUpdateOperationsInput | string
  cep?: Prisma.StringFieldUpdateOperationsInput | string
  telefone?: Prisma.NullableStringFieldUpdateOperationsInput | string | null
  responsavel?: Prisma.NullableStringFieldUpdateOperationsInput | string | null
  observacoes?: Prisma.NullableStringFieldUpdateOperationsInput | string | null
  latitude?: Prisma.NullableFloatFieldUpdateOperationsInput | number | null
  longitude?: Prisma.NullableFloatFieldUpdateOperationsInput | number | null
  ativo?: Prisma.BoolFieldUpdateOperationsInput | boolean
  fusoHorario?: Prisma.StringFieldUpdateOperationsInput | string
  createdAt?: Prisma.DateTimeFieldUpdateOperationsInput | Date | string
  updatedAt?: Prisma.DateTimeFieldUpdateOperationsInput | Date | string
  deletedAt?: Prisma.NullableDateTimeFieldUpdateOperationsInput | Date | string | null
  plantoes?: Prisma.PlantaoUncheckedUpdateManyWithoutLocalAtendimentoNestedInput
  especialidades?: Prisma.LocalAtendimentoEspecialidadesUncheckedUpdateManyWithoutLocalAtendimentoNestedInput
}

export type LocalAtendimentoCreateManyInput = {
  id?: number
  uuid?: string
  clienteId: number
  nome: string
  endereco: string
  cidade: string
  estado: string
  cep: string
  telefone?: string | null
  responsavel?: string | null
  observacoes?: string | null
  latitude?: number | null
  longitude?: number | null
  ativo?: boolean
  fusoHorario?: string
  createdAt?: Date | string
  updatedAt?: Date | string
  deletedAt?: Date | string | null
}

export type LocalAtendimentoUpdateManyMutationInput = {
  uuid?: Prisma.StringFieldUpdateOperationsInput | string
  nome?: Prisma.StringFieldUpdateOperationsInput | string
  endereco?: Prisma.StringFieldUpdateOperationsInput | string
  cidade?: Prisma.StringFieldUpdateOperationsInput | string
  estado?: Prisma.StringFieldUpdateOperationsInput | string
  cep?: Prisma.StringFieldUpdateOperationsInput | string
  telefone?: Prisma.NullableStringFieldUpdateOperationsInput | string | null
  responsavel?: Prisma.NullableStringFieldUpdateOperationsInput | string | null
  observacoes?: Prisma.NullableStringFieldUpdateOperationsInput | string | null
  latitude?: Prisma.NullableFloatFieldUpdateOperationsInput | number | null
  longitude?: Prisma.NullableFloatFieldUpdateOperationsInput | number | null
  ativo?: Prisma.BoolFieldUpdateOperationsInput | boolean
  fusoHorario?: Prisma.StringFieldUpdateOperationsInput | string
  createdAt?: Prisma.DateTimeFieldUpdateOperationsInput | Date | string
  updatedAt?: Prisma.DateTimeFieldUpdateOperationsInput | Date | string
  deletedAt?: Prisma.NullableDateTimeFieldUpdateOperationsInput | Date | string | null
}

export type LocalAtendimentoUncheckedUpdateManyInput = {
  id?: Prisma.IntFieldUpdateOperationsInput | number
  uuid?: Prisma.StringFieldUpdateOperationsInput | string
  clienteId?: Prisma.IntFieldUpdateOperationsInput | number
  nome?: Prisma.StringFieldUpdateOperationsInput | string
  endereco?: Prisma.StringFieldUpdateOperationsInput | string
  cidade?: Prisma.StringFieldUpdateOperationsInput | string
  estado?: Prisma.StringFieldUpdateOperationsInput | string
  cep?: Prisma.StringFieldUpdateOperationsInput | string
  telefone?: Prisma.NullableStringFieldUpdateOperationsInput | string | null
  responsavel?: Prisma.NullableStringFieldUpdateOperationsInput | string | null
  observacoes?: Prisma.NullableStringFieldUpdateOperationsInput | string | null
  latitude?: Prisma.NullableFloatFieldUpdateOperationsInput | number | null
  longitude?: Prisma.NullableFloatFieldUpdateOperationsInput | number | null
  ativo?: Prisma.BoolFieldUpdateOperationsInput | boolean
  fusoHorario?: Prisma.StringFieldUpdateOperationsInput | string
  createdAt?: Prisma.DateTimeFieldUpdateOperationsInput | Date | string
  updatedAt?: Prisma.DateTimeFieldUpdateOperationsInput | Date | string
  deletedAt?: Prisma.NullableDateTimeFieldUpdateOperationsInput | Date | string | null
}

export type LocalAtendimentoListRelationFilter = {
  every?: Prisma.LocalAtendimentoWhereInput
  some?: Prisma.LocalAtendimentoWhereInput
  none?: Prisma.LocalAtendimentoWhereInput
}

export type LocalAtendimentoOrderByRelationAggregateInput = {
  _count?: Prisma.SortOrder
}

export type LocalAtendimentoOrderByRelevanceInput = {
  fields: Prisma.LocalAtendimentoOrderByRelevanceFieldEnum | Prisma.LocalAtendimentoOrderByRelevanceFieldEnum[]
  sort: Prisma.SortOrder
  search: string
}

export type LocalAtendimentoClienteIdNomeEnderecoCidadeEstadoCepCompoundUniqueInput = {
  clienteId: number
  nome: string
  endereco: string
  cidade: string
  estado: string
  cep: string
}

export type LocalAtendimentoCountOrderByAggregateInput = {
  id?: Prisma.SortOrder
  uuid?: Prisma.SortOrder
  clienteId?: Prisma.SortOrder
  nome?: Prisma.SortOrder
  endereco?: Prisma.SortOrder
  cidade?: Prisma.SortOrder
  estado?: Prisma.SortOrder
  cep?: Prisma.SortOrder
  telefone?: Prisma.SortOrder
  responsavel?: Prisma.SortOrder
  observacoes?: Prisma.SortOrder
  latitude?: Prisma.SortOrder
  longitude?: Prisma.SortOrder
  ativo?: Prisma.SortOrder
  fusoHorario?: Prisma.SortOrder
  createdAt?: Prisma.SortOrder
  updatedAt?: Prisma.SortOrder
  deletedAt?: Prisma.SortOrder
}

export type LocalAtendimentoAvgOrderByAggregateInput = {
  id?: Prisma.SortOrder
  clienteId?: Prisma.SortOrder
  latitude?: Prisma.SortOrder
  longitude?: Prisma.SortOrder
}

export type LocalAtendimentoMaxOrderByAggregateInput = {
  id?: Prisma.SortOrder
  uuid?: Prisma.SortOrder
  clienteId?: Prisma.SortOrder
  nome?: Prisma.SortOrder
  endereco?: Prisma.SortOrder
  cidade?: Prisma.SortOrder
  estado?: Prisma.SortOrder
  cep?: Prisma.SortOrder
  telefone?: Prisma.SortOrder
  responsavel?: Prisma.SortOrder
  observacoes?: Prisma.SortOrder
  latitude?: Prisma.SortOrder
  longitude?: Prisma.SortOrder
  ativo?: Prisma.SortOrder
  fusoHorario?: Prisma.SortOrder
  createdAt?: Prisma.SortOrder
  updatedAt?: Prisma.SortOrder
  deletedAt?: Prisma.SortOrder
}

export type LocalAtendimentoMinOrderByAggregateInput = {
  id?: Prisma.SortOrder
  uuid?: Prisma.SortOrder
  clienteId?: Prisma.SortOrder
  nome?: Prisma.SortOrder
  endereco?: Prisma.SortOrder
  cidade?: Prisma.SortOrder
  estado?: Prisma.SortOrder
  cep?: Prisma.SortOrder
  telefone?: Prisma.SortOrder
  responsavel?: Prisma.SortOrder
  observacoes?: Prisma.SortOrder
  latitude?: Prisma.SortOrder
  longitude?: Prisma.SortOrder
  ativo?: Prisma.SortOrder
  fusoHorario?: Prisma.SortOrder
  createdAt?: Prisma.SortOrder
  updatedAt?: Prisma.SortOrder
  deletedAt?: Prisma.SortOrder
}

export type LocalAtendimentoSumOrderByAggregateInput = {
  id?: Prisma.SortOrder
  clienteId?: Prisma.SortOrder
  latitude?: Prisma.SortOrder
  longitude?: Prisma.SortOrder
}

export type LocalAtendimentoScalarRelationFilter = {
  is?: Prisma.LocalAtendimentoWhereInput
  isNot?: Prisma.LocalAtendimentoWhereInput
}

export type LocalAtendimentoCreateNestedManyWithoutClienteInput = {
  create?: Prisma.XOR<Prisma.LocalAtendimentoCreateWithoutClienteInput, Prisma.LocalAtendimentoUncheckedCreateWithoutClienteInput> | Prisma.LocalAtendimentoCreateWithoutClienteInput[] | Prisma.LocalAtendimentoUncheckedCreateWithoutClienteInput[]
  connectOrCreate?: Prisma.LocalAtendimentoCreateOrConnectWithoutClienteInput | Prisma.LocalAtendimentoCreateOrConnectWithoutClienteInput[]
  createMany?: Prisma.LocalAtendimentoCreateManyClienteInputEnvelope
  connect?: Prisma.LocalAtendimentoWhereUniqueInput | Prisma.LocalAtendimentoWhereUniqueInput[]
}

export type LocalAtendimentoUncheckedCreateNestedManyWithoutClienteInput = {
  create?: Prisma.XOR<Prisma.LocalAtendimentoCreateWithoutClienteInput, Prisma.LocalAtendimentoUncheckedCreateWithoutClienteInput> | Prisma.LocalAtendimentoCreateWithoutClienteInput[] | Prisma.LocalAtendimentoUncheckedCreateWithoutClienteInput[]
  connectOrCreate?: Prisma.LocalAtendimentoCreateOrConnectWithoutClienteInput | Prisma.LocalAtendimentoCreateOrConnectWithoutClienteInput[]
  createMany?: Prisma.LocalAtendimentoCreateManyClienteInputEnvelope
  connect?: Prisma.LocalAtendimentoWhereUniqueInput | Prisma.LocalAtendimentoWhereUniqueInput[]
}

export type LocalAtendimentoUpdateManyWithoutClienteNestedInput = {
  create?: Prisma.XOR<Prisma.LocalAtendimentoCreateWithoutClienteInput, Prisma.LocalAtendimentoUncheckedCreateWithoutClienteInput> | Prisma.LocalAtendimentoCreateWithoutClienteInput[] | Prisma.LocalAtendimentoUncheckedCreateWithoutClienteInput[]
  connectOrCreate?: Prisma.LocalAtendimentoCreateOrConnectWithoutClienteInput | Prisma.LocalAtendimentoCreateOrConnectWithoutClienteInput[]
  upsert?: Prisma.LocalAtendimentoUpsertWithWhereUniqueWithoutClienteInput | Prisma.LocalAtendimentoUpsertWithWhereUniqueWithoutClienteInput[]
  createMany?: Prisma.LocalAtendimentoCreateManyClienteInputEnvelope
  set?: Prisma.LocalAtendimentoWhereUniqueInput | Prisma.LocalAtendimentoWhereUniqueInput[]
  disconnect?: Prisma.LocalAtendimentoWhereUniqueInput | Prisma.LocalAtendimentoWhereUniqueInput[]
  delete?: Prisma.LocalAtendimentoWhereUniqueInput | Prisma.LocalAtendimentoWhereUniqueInput[]
  connect?: Prisma.LocalAtendimentoWhereUniqueInput | Prisma.LocalAtendimentoWhereUniqueInput[]
  update?: Prisma.LocalAtendimentoUpdateWithWhereUniqueWithoutClienteInput | Prisma.LocalAtendimentoUpdateWithWhereUniqueWithoutClienteInput[]
  updateMany?: Prisma.LocalAtendimentoUpdateManyWithWhereWithoutClienteInput | Prisma.LocalAtendimentoUpdateManyWithWhereWithoutClienteInput[]
  deleteMany?: Prisma.LocalAtendimentoScalarWhereInput | Prisma.LocalAtendimentoScalarWhereInput[]
}

export type LocalAtendimentoUncheckedUpdateManyWithoutClienteNestedInput = {
  create?: Prisma.XOR<Prisma.LocalAtendimentoCreateWithoutClienteInput, Prisma.LocalAtendimentoUncheckedCreateWithoutClienteInput> | Prisma.LocalAtendimentoCreateWithoutClienteInput[] | Prisma.LocalAtendimentoUncheckedCreateWithoutClienteInput[]
  connectOrCreate?: Prisma.LocalAtendimentoCreateOrConnectWithoutClienteInput | Prisma.LocalAtendimentoCreateOrConnectWithoutClienteInput[]
  upsert?: Prisma.LocalAtendimentoUpsertWithWhereUniqueWithoutClienteInput | Prisma.LocalAtendimentoUpsertWithWhereUniqueWithoutClienteInput[]
  createMany?: Prisma.LocalAtendimentoCreateManyClienteInputEnvelope
  set?: Prisma.LocalAtendimentoWhereUniqueInput | Prisma.LocalAtendimentoWhereUniqueInput[]
  disconnect?: Prisma.LocalAtendimentoWhereUniqueInput | Prisma.LocalAtendimentoWhereUniqueInput[]
  delete?: Prisma.LocalAtendimentoWhereUniqueInput | Prisma.LocalAtendimentoWhereUniqueInput[]
  connect?: Prisma.LocalAtendimentoWhereUniqueInput | Prisma.LocalAtendimentoWhereUniqueInput[]
  update?: Prisma.LocalAtendimentoUpdateWithWhereUniqueWithoutClienteInput | Prisma.LocalAtendimentoUpdateWithWhereUniqueWithoutClienteInput[]
  updateMany?: Prisma.LocalAtendimentoUpdateManyWithWhereWithoutClienteInput | Prisma.LocalAtendimentoUpdateManyWithWhereWithoutClienteInput[]
  deleteMany?: Prisma.LocalAtendimentoScalarWhereInput | Prisma.LocalAtendimentoScalarWhereInput[]
}

export type NullableFloatFieldUpdateOperationsInput = {
  set?: number | null
  increment?: number
  decrement?: number
  multiply?: number
  divide?: number
}

export type LocalAtendimentoCreateNestedOneWithoutEspecialidadesInput = {
  create?: Prisma.XOR<Prisma.LocalAtendimentoCreateWithoutEspecialidadesInput, Prisma.LocalAtendimentoUncheckedCreateWithoutEspecialidadesInput>
  connectOrCreate?: Prisma.LocalAtendimentoCreateOrConnectWithoutEspecialidadesInput
  connect?: Prisma.LocalAtendimentoWhereUniqueInput
}

export type LocalAtendimentoUpdateOneRequiredWithoutEspecialidadesNestedInput = {
  create?: Prisma.XOR<Prisma.LocalAtendimentoCreateWithoutEspecialidadesInput, Prisma.LocalAtendimentoUncheckedCreateWithoutEspecialidadesInput>
  connectOrCreate?: Prisma.LocalAtendimentoCreateOrConnectWithoutEspecialidadesInput
  upsert?: Prisma.LocalAtendimentoUpsertWithoutEspecialidadesInput
  connect?: Prisma.LocalAtendimentoWhereUniqueInput
  update?: Prisma.XOR<Prisma.XOR<Prisma.LocalAtendimentoUpdateToOneWithWhereWithoutEspecialidadesInput, Prisma.LocalAtendimentoUpdateWithoutEspecialidadesInput>, Prisma.LocalAtendimentoUncheckedUpdateWithoutEspecialidadesInput>
}

export type LocalAtendimentoCreateNestedOneWithoutPlantoesInput = {
  create?: Prisma.XOR<Prisma.LocalAtendimentoCreateWithoutPlantoesInput, Prisma.LocalAtendimentoUncheckedCreateWithoutPlantoesInput>
  connectOrCreate?: Prisma.LocalAtendimentoCreateOrConnectWithoutPlantoesInput
  connect?: Prisma.LocalAtendimentoWhereUniqueInput
}

export type LocalAtendimentoUpdateOneRequiredWithoutPlantoesNestedInput = {
  create?: Prisma.XOR<Prisma.LocalAtendimentoCreateWithoutPlantoesInput, Prisma.LocalAtendimentoUncheckedCreateWithoutPlantoesInput>
  connectOrCreate?: Prisma.LocalAtendimentoCreateOrConnectWithoutPlantoesInput
  upsert?: Prisma.LocalAtendimentoUpsertWithoutPlantoesInput
  connect?: Prisma.LocalAtendimentoWhereUniqueInput
  update?: Prisma.XOR<Prisma.XOR<Prisma.LocalAtendimentoUpdateToOneWithWhereWithoutPlantoesInput, Prisma.LocalAtendimentoUpdateWithoutPlantoesInput>, Prisma.LocalAtendimentoUncheckedUpdateWithoutPlantoesInput>
}

export type LocalAtendimentoCreateWithoutClienteInput = {
  uuid?: string
  nome: string
  endereco: string
  cidade: string
  estado: string
  cep: string
  telefone?: string | null
  responsavel?: string | null
  observacoes?: string | null
  latitude?: number | null
  longitude?: number | null
  ativo?: boolean
  fusoHorario?: string
  createdAt?: Date | string
  updatedAt?: Date | string
  deletedAt?: Date | string | null
  plantoes?: Prisma.PlantaoCreateNestedManyWithoutLocalAtendimentoInput
  especialidades?: Prisma.LocalAtendimentoEspecialidadesCreateNestedManyWithoutLocalAtendimentoInput
}

export type LocalAtendimentoUncheckedCreateWithoutClienteInput = {
  id?: number
  uuid?: string
  nome: string
  endereco: string
  cidade: string
  estado: string
  cep: string
  telefone?: string | null
  responsavel?: string | null
  observacoes?: string | null
  latitude?: number | null
  longitude?: number | null
  ativo?: boolean
  fusoHorario?: string
  createdAt?: Date | string
  updatedAt?: Date | string
  deletedAt?: Date | string | null
  plantoes?: Prisma.PlantaoUncheckedCreateNestedManyWithoutLocalAtendimentoInput
  especialidades?: Prisma.LocalAtendimentoEspecialidadesUncheckedCreateNestedManyWithoutLocalAtendimentoInput
}

export type LocalAtendimentoCreateOrConnectWithoutClienteInput = {
  where: Prisma.LocalAtendimentoWhereUniqueInput
  create: Prisma.XOR<Prisma.LocalAtendimentoCreateWithoutClienteInput, Prisma.LocalAtendimentoUncheckedCreateWithoutClienteInput>
}

export type LocalAtendimentoCreateManyClienteInputEnvelope = {
  data: Prisma.LocalAtendimentoCreateManyClienteInput | Prisma.LocalAtendimentoCreateManyClienteInput[]
  skipDuplicates?: boolean
}

export type LocalAtendimentoUpsertWithWhereUniqueWithoutClienteInput = {
  where: Prisma.LocalAtendimentoWhereUniqueInput
  update: Prisma.XOR<Prisma.LocalAtendimentoUpdateWithoutClienteInput, Prisma.LocalAtendimentoUncheckedUpdateWithoutClienteInput>
  create: Prisma.XOR<Prisma.LocalAtendimentoCreateWithoutClienteInput, Prisma.LocalAtendimentoUncheckedCreateWithoutClienteInput>
}

export type LocalAtendimentoUpdateWithWhereUniqueWithoutClienteInput = {
  where: Prisma.LocalAtendimentoWhereUniqueInput
  data: Prisma.XOR<Prisma.LocalAtendimentoUpdateWithoutClienteInput, Prisma.LocalAtendimentoUncheckedUpdateWithoutClienteInput>
}

export type LocalAtendimentoUpdateManyWithWhereWithoutClienteInput = {
  where: Prisma.LocalAtendimentoScalarWhereInput
  data: Prisma.XOR<Prisma.LocalAtendimentoUpdateManyMutationInput, Prisma.LocalAtendimentoUncheckedUpdateManyWithoutClienteInput>
}

export type LocalAtendimentoScalarWhereInput = {
  AND?: Prisma.LocalAtendimentoScalarWhereInput | Prisma.LocalAtendimentoScalarWhereInput[]
  OR?: Prisma.LocalAtendimentoScalarWhereInput[]
  NOT?: Prisma.LocalAtendimentoScalarWhereInput | Prisma.LocalAtendimentoScalarWhereInput[]
  id?: Prisma.IntFilter<"LocalAtendimento"> | number
  uuid?: Prisma.StringFilter<"LocalAtendimento"> | string
  clienteId?: Prisma.IntFilter<"LocalAtendimento"> | number
  nome?: Prisma.StringFilter<"LocalAtendimento"> | string
  endereco?: Prisma.StringFilter<"LocalAtendimento"> | string
  cidade?: Prisma.StringFilter<"LocalAtendimento"> | string
  estado?: Prisma.StringFilter<"LocalAtendimento"> | string
  cep?: Prisma.StringFilter<"LocalAtendimento"> | string
  telefone?: Prisma.StringNullableFilter<"LocalAtendimento"> | string | null
  responsavel?: Prisma.StringNullableFilter<"LocalAtendimento"> | string | null
  observacoes?: Prisma.StringNullableFilter<"LocalAtendimento"> | string | null
  latitude?: Prisma.FloatNullableFilter<"LocalAtendimento"> | number | null
  longitude?: Prisma.FloatNullableFilter<"LocalAtendimento"> | number | null
  ativo?: Prisma.BoolFilter<"LocalAtendimento"> | boolean
  fusoHorario?: Prisma.StringFilter<"LocalAtendimento"> | string
  createdAt?: Prisma.DateTimeFilter<"LocalAtendimento"> | Date | string
  updatedAt?: Prisma.DateTimeFilter<"LocalAtendimento"> | Date | string
  deletedAt?: Prisma.DateTimeNullableFilter<"LocalAtendimento"> | Date | string | null
}

export type LocalAtendimentoCreateWithoutEspecialidadesInput = {
  uuid?: string
  nome: string
  endereco: string
  cidade: string
  estado: string
  cep: string
  telefone?: string | null
  responsavel?: string | null
  observacoes?: string | null
  latitude?: number | null
  longitude?: number | null
  ativo?: boolean
  fusoHorario?: string
  createdAt?: Date | string
  updatedAt?: Date | string
  deletedAt?: Date | string | null
  cliente: Prisma.ClienteCreateNestedOneWithoutLocaisAtendimentoInput
  plantoes?: Prisma.PlantaoCreateNestedManyWithoutLocalAtendimentoInput
}

export type LocalAtendimentoUncheckedCreateWithoutEspecialidadesInput = {
  id?: number
  uuid?: string
  clienteId: number
  nome: string
  endereco: string
  cidade: string
  estado: string
  cep: string
  telefone?: string | null
  responsavel?: string | null
  observacoes?: string | null
  latitude?: number | null
  longitude?: number | null
  ativo?: boolean
  fusoHorario?: string
  createdAt?: Date | string
  updatedAt?: Date | string
  deletedAt?: Date | string | null
  plantoes?: Prisma.PlantaoUncheckedCreateNestedManyWithoutLocalAtendimentoInput
}

export type LocalAtendimentoCreateOrConnectWithoutEspecialidadesInput = {
  where: Prisma.LocalAtendimentoWhereUniqueInput
  create: Prisma.XOR<Prisma.LocalAtendimentoCreateWithoutEspecialidadesInput, Prisma.LocalAtendimentoUncheckedCreateWithoutEspecialidadesInput>
}

export type LocalAtendimentoUpsertWithoutEspecialidadesInput = {
  update: Prisma.XOR<Prisma.LocalAtendimentoUpdateWithoutEspecialidadesInput, Prisma.LocalAtendimentoUncheckedUpdateWithoutEspecialidadesInput>
  create: Prisma.XOR<Prisma.LocalAtendimentoCreateWithoutEspecialidadesInput, Prisma.LocalAtendimentoUncheckedCreateWithoutEspecialidadesInput>
  where?: Prisma.LocalAtendimentoWhereInput
}

export type LocalAtendimentoUpdateToOneWithWhereWithoutEspecialidadesInput = {
  where?: Prisma.LocalAtendimentoWhereInput
  data: Prisma.XOR<Prisma.LocalAtendimentoUpdateWithoutEspecialidadesInput, Prisma.LocalAtendimentoUncheckedUpdateWithoutEspecialidadesInput>
}

export type LocalAtendimentoUpdateWithoutEspecialidadesInput = {
  uuid?: Prisma.StringFieldUpdateOperationsInput | string
  nome?: Prisma.StringFieldUpdateOperationsInput | string
  endereco?: Prisma.StringFieldUpdateOperationsInput | string
  cidade?: Prisma.StringFieldUpdateOperationsInput | string
  estado?: Prisma.StringFieldUpdateOperationsInput | string
  cep?: Prisma.StringFieldUpdateOperationsInput | string
  telefone?: Prisma.NullableStringFieldUpdateOperationsInput | string | null
  responsavel?: Prisma.NullableStringFieldUpdateOperationsInput | string | null
  observacoes?: Prisma.NullableStringFieldUpdateOperationsInput | string | null
  latitude?: Prisma.NullableFloatFieldUpdateOperationsInput | number | null
  longitude?: Prisma.NullableFloatFieldUpdateOperationsInput | number | null
  ativo?: Prisma.BoolFieldUpdateOperationsInput | boolean
  fusoHorario?: Prisma.StringFieldUpdateOperationsInput | string
  createdAt?: Prisma.DateTimeFieldUpdateOperationsInput | Date | string
  updatedAt?: Prisma.DateTimeFieldUpdateOperationsInput | Date | string
  deletedAt?: Prisma.NullableDateTimeFieldUpdateOperationsInput | Date | string | null
  cliente?: Prisma.ClienteUpdateOneRequiredWithoutLocaisAtendimentoNestedInput
  plantoes?: Prisma.PlantaoUpdateManyWithoutLocalAtendimentoNestedInput
}

export type LocalAtendimentoUncheckedUpdateWithoutEspecialidadesInput = {
  id?: Prisma.IntFieldUpdateOperationsInput | number
  uuid?: Prisma.StringFieldUpdateOperationsInput | string
  clienteId?: Prisma.IntFieldUpdateOperationsInput | number
  nome?: Prisma.StringFieldUpdateOperationsInput | string
  endereco?: Prisma.StringFieldUpdateOperationsInput | string
  cidade?: Prisma.StringFieldUpdateOperationsInput | string
  estado?: Prisma.StringFieldUpdateOperationsInput | string
  cep?: Prisma.StringFieldUpdateOperationsInput | string
  telefone?: Prisma.NullableStringFieldUpdateOperationsInput | string | null
  responsavel?: Prisma.NullableStringFieldUpdateOperationsInput | string | null
  observacoes?: Prisma.NullableStringFieldUpdateOperationsInput | string | null
  latitude?: Prisma.NullableFloatFieldUpdateOperationsInput | number | null
  longitude?: Prisma.NullableFloatFieldUpdateOperationsInput | number | null
  ativo?: Prisma.BoolFieldUpdateOperationsInput | boolean
  fusoHorario?: Prisma.StringFieldUpdateOperationsInput | string
  createdAt?: Prisma.DateTimeFieldUpdateOperationsInput | Date | string
  updatedAt?: Prisma.DateTimeFieldUpdateOperationsInput | Date | string
  deletedAt?: Prisma.NullableDateTimeFieldUpdateOperationsInput | Date | string | null
  plantoes?: Prisma.PlantaoUncheckedUpdateManyWithoutLocalAtendimentoNestedInput
}

export type LocalAtendimentoCreateWithoutPlantoesInput = {
  uuid?: string
  nome: string
  endereco: string
  cidade: string
  estado: string
  cep: string
  telefone?: string | null
  responsavel?: string | null
  observacoes?: string | null
  latitude?: number | null
  longitude?: number | null
  ativo?: boolean
  fusoHorario?: string
  createdAt?: Date | string
  updatedAt?: Date | string
  deletedAt?: Date | string | null
  cliente: Prisma.ClienteCreateNestedOneWithoutLocaisAtendimentoInput
  especialidades?: Prisma.LocalAtendimentoEspecialidadesCreateNestedManyWithoutLocalAtendimentoInput
}

export type LocalAtendimentoUncheckedCreateWithoutPlantoesInput = {
  id?: number
  uuid?: string
  clienteId: number
  nome: string
  endereco: string
  cidade: string
  estado: string
  cep: string
  telefone?: string | null
  responsavel?: string | null
  observacoes?: string | null
  latitude?: number | null
  longitude?: number | null
  ativo?: boolean
  fusoHorario?: string
  createdAt?: Date | string
  updatedAt?: Date | string
  deletedAt?: Date | string | null
  especialidades?: Prisma.LocalAtendimentoEspecialidadesUncheckedCreateNestedManyWithoutLocalAtendimentoInput
}

export type LocalAtendimentoCreateOrConnectWithoutPlantoesInput = {
  where: Prisma.LocalAtendimentoWhereUniqueInput
  create: Prisma.XOR<Prisma.LocalAtendimentoCreateWithoutPlantoesInput, Prisma.LocalAtendimentoUncheckedCreateWithoutPlantoesInput>
}

export type LocalAtendimentoUpsertWithoutPlantoesInput = {
  update: Prisma.XOR<Prisma.LocalAtendimentoUpdateWithoutPlantoesInput, Prisma.LocalAtendimentoUncheckedUpdateWithoutPlantoesInput>
  create: Prisma.XOR<Prisma.LocalAtendimentoCreateWithoutPlantoesInput, Prisma.LocalAtendimentoUncheckedCreateWithoutPlantoesInput>
  where?: Prisma.LocalAtendimentoWhereInput
}

export type LocalAtendimentoUpdateToOneWithWhereWithoutPlantoesInput = {
  where?: Prisma.LocalAtendimentoWhereInput
  data: Prisma.XOR<Prisma.LocalAtendimentoUpdateWithoutPlantoesInput, Prisma.LocalAtendimentoUncheckedUpdateWithoutPlantoesInput>
}

export type LocalAtendimentoUpdateWithoutPlantoesInput = {
  uuid?: Prisma.StringFieldUpdateOperationsInput | string
  nome?: Prisma.StringFieldUpdateOperationsInput | string
  endereco?: Prisma.StringFieldUpdateOperationsInput | string
  cidade?: Prisma.StringFieldUpdateOperationsInput | string
  estado?: Prisma.StringFieldUpdateOperationsInput | string
  cep?: Prisma.StringFieldUpdateOperationsInput | string
  telefone?: Prisma.NullableStringFieldUpdateOperationsInput | string | null
  responsavel?: Prisma.NullableStringFieldUpdateOperationsInput | string | null
  observacoes?: Prisma.NullableStringFieldUpdateOperationsInput | string | null
  latitude?: Prisma.NullableFloatFieldUpdateOperationsInput | number | null
  longitude?: Prisma.NullableFloatFieldUpdateOperationsInput | number | null
  ativo?: Prisma.BoolFieldUpdateOperationsInput | boolean
  fusoHorario?: Prisma.StringFieldUpdateOperationsInput | string
  createdAt?: Prisma.DateTimeFieldUpdateOperationsInput | Date | string
  updatedAt?: Prisma.DateTimeFieldUpdateOperationsInput | Date | string
  deletedAt?: Prisma.NullableDateTimeFieldUpdateOperationsInput | Date | string | null
  cliente?: Prisma.ClienteUpdateOneRequiredWithoutLocaisAtendimentoNestedInput
  especialidades?: Prisma.LocalAtendimentoEspecialidadesUpdateManyWithoutLocalAtendimentoNestedInput
}

export type LocalAtendimentoUncheckedUpdateWithoutPlantoesInput = {
  id?: Prisma.IntFieldUpdateOperationsInput | number
  uuid?: Prisma.StringFieldUpdateOperationsInput | string
  clienteId?: Prisma.IntFieldUpdateOperationsInput | number
  nome?: Prisma.StringFieldUpdateOperationsInput | string
  endereco?: Prisma.StringFieldUpdateOperationsInput | string
  cidade?: Prisma.StringFieldUpdateOperationsInput | string
  estado?: Prisma.StringFieldUpdateOperationsInput | string
  cep?: Prisma.StringFieldUpdateOperationsInput | string
  telefone?: Prisma.NullableStringFieldUpdateOperationsInput | string | null
  responsavel?: Prisma.NullableStringFieldUpdateOperationsInput | string | null
  observacoes?: Prisma.NullableStringFieldUpdateOperationsInput | string | null
  latitude?: Prisma.NullableFloatFieldUpdateOperationsInput | number | null
  longitude?: Prisma.NullableFloatFieldUpdateOperationsInput | number | null
  ativo?: Prisma.BoolFieldUpdateOperationsInput | boolean
  fusoHorario?: Prisma.StringFieldUpdateOperationsInput | string
  createdAt?: Prisma.DateTimeFieldUpdateOperationsInput | Date | string
  updatedAt?: Prisma.DateTimeFieldUpdateOperationsInput | Date | string
  deletedAt?: Prisma.NullableDateTimeFieldUpdateOperationsInput | Date | string | null
  especialidades?: Prisma.LocalAtendimentoEspecialidadesUncheckedUpdateManyWithoutLocalAtendimentoNestedInput
}

export type LocalAtendimentoCreateManyClienteInput = {
  id?: number
  uuid?: string
  nome: string
  endereco: string
  cidade: string
  estado: string
  cep: string
  telefone?: string | null
  responsavel?: string | null
  observacoes?: string | null
  latitude?: number | null
  longitude?: number | null
  ativo?: boolean
  fusoHorario?: string
  createdAt?: Date | string
  updatedAt?: Date | string
  deletedAt?: Date | string | null
}

export type LocalAtendimentoUpdateWithoutClienteInput = {
  uuid?: Prisma.StringFieldUpdateOperationsInput | string
  nome?: Prisma.StringFieldUpdateOperationsInput | string
  endereco?: Prisma.StringFieldUpdateOperationsInput | string
  cidade?: Prisma.StringFieldUpdateOperationsInput | string
  estado?: Prisma.StringFieldUpdateOperationsInput | string
  cep?: Prisma.StringFieldUpdateOperationsInput | string
  telefone?: Prisma.NullableStringFieldUpdateOperationsInput | string | null
  responsavel?: Prisma.NullableStringFieldUpdateOperationsInput | string | null
  observacoes?: Prisma.NullableStringFieldUpdateOperationsInput | string | null
  latitude?: Prisma.NullableFloatFieldUpdateOperationsInput | number | null
  longitude?: Prisma.NullableFloatFieldUpdateOperationsInput | number | null
  ativo?: Prisma.BoolFieldUpdateOperationsInput | boolean
  fusoHorario?: Prisma.StringFieldUpdateOperationsInput | string
  createdAt?: Prisma.DateTimeFieldUpdateOperationsInput | Date | string
  updatedAt?: Prisma.DateTimeFieldUpdateOperationsInput | Date | string
  deletedAt?: Prisma.NullableDateTimeFieldUpdateOperationsInput | Date | string | null
  plantoes?: Prisma.PlantaoUpdateManyWithoutLocalAtendimentoNestedInput
  especialidades?: Prisma.LocalAtendimentoEspecialidadesUpdateManyWithoutLocalAtendimentoNestedInput
}

export type LocalAtendimentoUncheckedUpdateWithoutClienteInput = {
  id?: Prisma.IntFieldUpdateOperationsInput | number
  uuid?: Prisma.StringFieldUpdateOperationsInput | string
  nome?: Prisma.StringFieldUpdateOperationsInput | string
  endereco?: Prisma.StringFieldUpdateOperationsInput | string
  cidade?: Prisma.StringFieldUpdateOperationsInput | string
  estado?: Prisma.StringFieldUpdateOperationsInput | string
  cep?: Prisma.StringFieldUpdateOperationsInput | string
  telefone?: Prisma.NullableStringFieldUpdateOperationsInput | string | null
  responsavel?: Prisma.NullableStringFieldUpdateOperationsInput | string | null
  observacoes?: Prisma.NullableStringFieldUpdateOperationsInput | string | null
  latitude?: Prisma.NullableFloatFieldUpdateOperationsInput | number | null
  longitude?: Prisma.NullableFloatFieldUpdateOperationsInput | number | null
  ativo?: Prisma.BoolFieldUpdateOperationsInput | boolean
  fusoHorario?: Prisma.StringFieldUpdateOperationsInput | string
  createdAt?: Prisma.DateTimeFieldUpdateOperationsInput | Date | string
  updatedAt?: Prisma.DateTimeFieldUpdateOperationsInput | Date | string
  deletedAt?: Prisma.NullableDateTimeFieldUpdateOperationsInput | Date | string | null
  plantoes?: Prisma.PlantaoUncheckedUpdateManyWithoutLocalAtendimentoNestedInput
  especialidades?: Prisma.LocalAtendimentoEspecialidadesUncheckedUpdateManyWithoutLocalAtendimentoNestedInput
}

export type LocalAtendimentoUncheckedUpdateManyWithoutClienteInput = {
  id?: Prisma.IntFieldUpdateOperationsInput | number
  uuid?: Prisma.StringFieldUpdateOperationsInput | string
  nome?: Prisma.StringFieldUpdateOperationsInput | string
  endereco?: Prisma.StringFieldUpdateOperationsInput | string
  cidade?: Prisma.StringFieldUpdateOperationsInput | string
  estado?: Prisma.StringFieldUpdateOperationsInput | string
  cep?: Prisma.StringFieldUpdateOperationsInput | string
  telefone?: Prisma.NullableStringFieldUpdateOperationsInput | string | null
  responsavel?: Prisma.NullableStringFieldUpdateOperationsInput | string | null
  observacoes?: Prisma.NullableStringFieldUpdateOperationsInput | string | null
  latitude?: Prisma.NullableFloatFieldUpdateOperationsInput | number | null
  longitude?: Prisma.NullableFloatFieldUpdateOperationsInput | number | null
  ativo?: Prisma.BoolFieldUpdateOperationsInput | boolean
  fusoHorario?: Prisma.StringFieldUpdateOperationsInput | string
  createdAt?: Prisma.DateTimeFieldUpdateOperationsInput | Date | string
  updatedAt?: Prisma.DateTimeFieldUpdateOperationsInput | Date | string
  deletedAt?: Prisma.NullableDateTimeFieldUpdateOperationsInput | Date | string | null
}


/**
 * Count Type LocalAtendimentoCountOutputType
 */

export type LocalAtendimentoCountOutputType = {
  plantoes: number
  especialidades: number
}

export type LocalAtendimentoCountOutputTypeSelect<ExtArgs extends runtime.Types.Extensions.InternalArgs = runtime.Types.Extensions.DefaultArgs> = {
  plantoes?: boolean | LocalAtendimentoCountOutputTypeCountPlantoesArgs
  especialidades?: boolean | LocalAtendimentoCountOutputTypeCountEspecialidadesArgs
}

/**
 * LocalAtendimentoCountOutputType without action
 */
export type LocalAtendimentoCountOutputTypeDefaultArgs<ExtArgs extends runtime.Types.Extensions.InternalArgs = runtime.Types.Extensions.DefaultArgs> = {
  /**
   * Select specific fields to fetch from the LocalAtendimentoCountOutputType
   */
  select?: Prisma.LocalAtendimentoCountOutputTypeSelect<ExtArgs> | null
}

/**
 * LocalAtendimentoCountOutputType without action
 */
export type LocalAtendimentoCountOutputTypeCountPlantoesArgs<ExtArgs extends runtime.Types.Extensions.InternalArgs = runtime.Types.Extensions.DefaultArgs> = {
  where?: Prisma.PlantaoWhereInput
}

/**
 * LocalAtendimentoCountOutputType without action
 */
export type LocalAtendimentoCountOutputTypeCountEspecialidadesArgs<ExtArgs extends runtime.Types.Extensions.InternalArgs = runtime.Types.Extensions.DefaultArgs> = {
  where?: Prisma.LocalAtendimentoEspecialidadesWhereInput
}


export type LocalAtendimentoSelect<ExtArgs extends runtime.Types.Extensions.InternalArgs = runtime.Types.Extensions.DefaultArgs> = runtime.Types.Extensions.GetSelect<{
  id?: boolean
  uuid?: boolean
  clienteId?: boolean
  nome?: boolean
  endereco?: boolean
  cidade?: boolean
  estado?: boolean
  cep?: boolean
  telefone?: boolean
  responsavel?: boolean
  observacoes?: boolean
  latitude?: boolean
  longitude?: boolean
  ativo?: boolean
  fusoHorario?: boolean
  createdAt?: boolean
  updatedAt?: boolean
  deletedAt?: boolean
  cliente?: boolean | Prisma.ClienteDefaultArgs<ExtArgs>
  plantoes?: boolean | Prisma.LocalAtendimento$plantoesArgs<ExtArgs>
  especialidades?: boolean | Prisma.LocalAtendimento$especialidadesArgs<ExtArgs>
  _count?: boolean | Prisma.LocalAtendimentoCountOutputTypeDefaultArgs<ExtArgs>
}, ExtArgs["result"]["localAtendimento"]>



export type LocalAtendimentoSelectScalar = {
  id?: boolean
  uuid?: boolean
  clienteId?: boolean
  nome?: boolean
  endereco?: boolean
  cidade?: boolean
  estado?: boolean
  cep?: boolean
  telefone?: boolean
  responsavel?: boolean
  observacoes?: boolean
  latitude?: boolean
  longitude?: boolean
  ativo?: boolean
  fusoHorario?: boolean
  createdAt?: boolean
  updatedAt?: boolean
  deletedAt?: boolean
}

export type LocalAtendimentoOmit<ExtArgs extends runtime.Types.Extensions.InternalArgs = runtime.Types.Extensions.DefaultArgs> = runtime.Types.Extensions.GetOmit<"id" | "uuid" | "clienteId" | "nome" | "endereco" | "cidade" | "estado" | "cep" | "telefone" | "responsavel" | "observacoes" | "latitude" | "longitude" | "ativo" | "fusoHorario" | "createdAt" | "updatedAt" | "deletedAt", ExtArgs["result"]["localAtendimento"]>
export type LocalAtendimentoInclude<ExtArgs extends runtime.Types.Extensions.InternalArgs = runtime.Types.Extensions.DefaultArgs> = {
  cliente?: boolean | Prisma.ClienteDefaultArgs<ExtArgs>
  plantoes?: boolean | Prisma.LocalAtendimento$plantoesArgs<ExtArgs>
  especialidades?: boolean | Prisma.LocalAtendimento$especialidadesArgs<ExtArgs>
  _count?: boolean | Prisma.LocalAtendimentoCountOutputTypeDefaultArgs<ExtArgs>
}

export type $LocalAtendimentoPayload<ExtArgs extends runtime.Types.Extensions.InternalArgs = runtime.Types.Extensions.DefaultArgs> = {
  name: "LocalAtendimento"
  objects: {
    cliente: Prisma.$ClientePayload<ExtArgs>
    plantoes: Prisma.$PlantaoPayload<ExtArgs>[]
    especialidades: Prisma.$LocalAtendimentoEspecialidadesPayload<ExtArgs>[]
  }
  scalars: runtime.Types.Extensions.GetPayloadResult<{
    id: number
    uuid: string
    clienteId: number
    nome: string
    endereco: string
    cidade: string
    estado: string
    cep: string
    telefone: string | null
    responsavel: string | null
    observacoes: string | null
    latitude: number | null
    longitude: number | null
    ativo: boolean
    fusoHorario: string
    createdAt: Date
    updatedAt: Date
    deletedAt: Date | null
  }, ExtArgs["result"]["localAtendimento"]>
  composites: {}
}

export type LocalAtendimentoGetPayload<S extends boolean | null | undefined | LocalAtendimentoDefaultArgs> = runtime.Types.Result.GetResult<Prisma.$LocalAtendimentoPayload, S>

export type LocalAtendimentoCountArgs<ExtArgs extends runtime.Types.Extensions.InternalArgs = runtime.Types.Extensions.DefaultArgs> =
  Omit<LocalAtendimentoFindManyArgs, 'select' | 'include' | 'distinct' | 'omit'> & {
    select?: LocalAtendimentoCountAggregateInputType | true
  }

export interface LocalAtendimentoDelegate<ExtArgs extends runtime.Types.Extensions.InternalArgs = runtime.Types.Extensions.DefaultArgs, GlobalOmitOptions = {}> {
  [K: symbol]: { types: Prisma.TypeMap<ExtArgs>['model']['LocalAtendimento'], meta: { name: 'LocalAtendimento' } }
  /**
   * Find zero or one LocalAtendimento that matches the filter.
   * @param {LocalAtendimentoFindUniqueArgs} args - Arguments to find a LocalAtendimento
   * @example
   * // Get one LocalAtendimento
   * const localAtendimento = await prisma.localAtendimento.findUnique({
   *   where: {
   *     // ... provide filter here
   *   }
   * })
   */
  findUnique<T extends LocalAtendimentoFindUniqueArgs>(args: Prisma.SelectSubset<T, LocalAtendimentoFindUniqueArgs<ExtArgs>>): Prisma.Prisma__LocalAtendimentoClient<runtime.Types.Result.GetResult<Prisma.$LocalAtendimentoPayload<ExtArgs>, T, "findUnique", GlobalOmitOptions> | null, null, ExtArgs, GlobalOmitOptions>

  /**
   * Find one LocalAtendimento that matches the filter or throw an error with `error.code='P2025'`
   * if no matches were found.
   * @param {LocalAtendimentoFindUniqueOrThrowArgs} args - Arguments to find a LocalAtendimento
   * @example
   * // Get one LocalAtendimento
   * const localAtendimento = await prisma.localAtendimento.findUniqueOrThrow({
   *   where: {
   *     // ... provide filter here
   *   }
   * })
   */
  findUniqueOrThrow<T extends LocalAtendimentoFindUniqueOrThrowArgs>(args: Prisma.SelectSubset<T, LocalAtendimentoFindUniqueOrThrowArgs<ExtArgs>>): Prisma.Prisma__LocalAtendimentoClient<runtime.Types.Result.GetResult<Prisma.$LocalAtendimentoPayload<ExtArgs>, T, "findUniqueOrThrow", GlobalOmitOptions>, never, ExtArgs, GlobalOmitOptions>

  /**
   * Find the first LocalAtendimento that matches the filter.
   * Note, that providing `undefined` is treated as the value not being there.
   * Read more here: https://pris.ly/d/null-undefined
   * @param {LocalAtendimentoFindFirstArgs} args - Arguments to find a LocalAtendimento
   * @example
   * // Get one LocalAtendimento
   * const localAtendimento = await prisma.localAtendimento.findFirst({
   *   where: {
   *     // ... provide filter here
   *   }
   * })
   */
  findFirst<T extends LocalAtendimentoFindFirstArgs>(args?: Prisma.SelectSubset<T, LocalAtendimentoFindFirstArgs<ExtArgs>>): Prisma.Prisma__LocalAtendimentoClient<runtime.Types.Result.GetResult<Prisma.$LocalAtendimentoPayload<ExtArgs>, T, "findFirst", GlobalOmitOptions> | null, null, ExtArgs, GlobalOmitOptions>

  /**
   * Find the first LocalAtendimento that matches the filter or
   * throw `PrismaKnownClientError` with `P2025` code if no matches were found.
   * Note, that providing `undefined` is treated as the value not being there.
   * Read more here: https://pris.ly/d/null-undefined
   * @param {LocalAtendimentoFindFirstOrThrowArgs} args - Arguments to find a LocalAtendimento
   * @example
   * // Get one LocalAtendimento
   * const localAtendimento = await prisma.localAtendimento.findFirstOrThrow({
   *   where: {
   *     // ... provide filter here
   *   }
   * })
   */
  findFirstOrThrow<T extends LocalAtendimentoFindFirstOrThrowArgs>(args?: Prisma.SelectSubset<T, LocalAtendimentoFindFirstOrThrowArgs<ExtArgs>>): Prisma.Prisma__LocalAtendimentoClient<runtime.Types.Result.GetResult<Prisma.$LocalAtendimentoPayload<ExtArgs>, T, "findFirstOrThrow", GlobalOmitOptions>, never, ExtArgs, GlobalOmitOptions>

  /**
   * Find zero or more LocalAtendimentos that matches the filter.
   * Note, that providing `undefined` is treated as the value not being there.
   * Read more here: https://pris.ly/d/null-undefined
   * @param {LocalAtendimentoFindManyArgs} args - Arguments to filter and select certain fields only.
   * @example
   * // Get all LocalAtendimentos
   * const localAtendimentos = await prisma.localAtendimento.findMany()
   * 
   * // Get first 10 LocalAtendimentos
   * const localAtendimentos = await prisma.localAtendimento.findMany({ take: 10 })
   * 
   * // Only select the `id`
   * const localAtendimentoWithIdOnly = await prisma.localAtendimento.findMany({ select: { id: true } })
   * 
   */
  findMany<T extends LocalAtendimentoFindManyArgs>(args?: Prisma.SelectSubset<T, LocalAtendimentoFindManyArgs<ExtArgs>>): Prisma.PrismaPromise<runtime.Types.Result.GetResult<Prisma.$LocalAtendimentoPayload<ExtArgs>, T, "findMany", GlobalOmitOptions>>

  /**
   * Create a LocalAtendimento.
   * @param {LocalAtendimentoCreateArgs} args - Arguments to create a LocalAtendimento.
   * @example
   * // Create one LocalAtendimento
   * const LocalAtendimento = await prisma.localAtendimento.create({
   *   data: {
   *     // ... data to create a LocalAtendimento
   *   }
   * })
   * 
   */
  create<T extends LocalAtendimentoCreateArgs>(args: Prisma.SelectSubset<T, LocalAtendimentoCreateArgs<ExtArgs>>): Prisma.Prisma__LocalAtendimentoClient<runtime.Types.Result.GetResult<Prisma.$LocalAtendimentoPayload<ExtArgs>, T, "create", GlobalOmitOptions>, never, ExtArgs, GlobalOmitOptions>

  /**
   * Create many LocalAtendimentos.
   * @param {LocalAtendimentoCreateManyArgs} args - Arguments to create many LocalAtendimentos.
   * @example
   * // Create many LocalAtendimentos
   * const localAtendimento = await prisma.localAtendimento.createMany({
   *   data: [
   *     // ... provide data here
   *   ]
   * })
   *     
   */
  createMany<T extends LocalAtendimentoCreateManyArgs>(args?: Prisma.SelectSubset<T, LocalAtendimentoCreateManyArgs<ExtArgs>>): Prisma.PrismaPromise<Prisma.BatchPayload>

  /**
   * Delete a LocalAtendimento.
   * @param {LocalAtendimentoDeleteArgs} args - Arguments to delete one LocalAtendimento.
   * @example
   * // Delete one LocalAtendimento
   * const LocalAtendimento = await prisma.localAtendimento.delete({
   *   where: {
   *     // ... filter to delete one LocalAtendimento
   *   }
   * })
   * 
   */
  delete<T extends LocalAtendimentoDeleteArgs>(args: Prisma.SelectSubset<T, LocalAtendimentoDeleteArgs<ExtArgs>>): Prisma.Prisma__LocalAtendimentoClient<runtime.Types.Result.GetResult<Prisma.$LocalAtendimentoPayload<ExtArgs>, T, "delete", GlobalOmitOptions>, never, ExtArgs, GlobalOmitOptions>

  /**
   * Update one LocalAtendimento.
   * @param {LocalAtendimentoUpdateArgs} args - Arguments to update one LocalAtendimento.
   * @example
   * // Update one LocalAtendimento
   * const localAtendimento = await prisma.localAtendimento.update({
   *   where: {
   *     // ... provide filter here
   *   },
   *   data: {
   *     // ... provide data here
   *   }
   * })
   * 
   */
  update<T extends LocalAtendimentoUpdateArgs>(args: Prisma.SelectSubset<T, LocalAtendimentoUpdateArgs<ExtArgs>>): Prisma.Prisma__LocalAtendimentoClient<runtime.Types.Result.GetResult<Prisma.$LocalAtendimentoPayload<ExtArgs>, T, "update", GlobalOmitOptions>, never, ExtArgs, GlobalOmitOptions>

  /**
   * Delete zero or more LocalAtendimentos.
   * @param {LocalAtendimentoDeleteManyArgs} args - Arguments to filter LocalAtendimentos to delete.
   * @example
   * // Delete a few LocalAtendimentos
   * const { count } = await prisma.localAtendimento.deleteMany({
   *   where: {
   *     // ... provide filter here
   *   }
   * })
   * 
   */
  deleteMany<T extends LocalAtendimentoDeleteManyArgs>(args?: Prisma.SelectSubset<T, LocalAtendimentoDeleteManyArgs<ExtArgs>>): Prisma.PrismaPromise<Prisma.BatchPayload>

  /**
   * Update zero or more LocalAtendimentos.
   * Note, that providing `undefined` is treated as the value not being there.
   * Read more here: https://pris.ly/d/null-undefined
   * @param {LocalAtendimentoUpdateManyArgs} args - Arguments to update one or more rows.
   * @example
   * // Update many LocalAtendimentos
   * const localAtendimento = await prisma.localAtendimento.updateMany({
   *   where: {
   *     // ... provide filter here
   *   },
   *   data: {
   *     // ... provide data here
   *   }
   * })
   * 
   */
  updateMany<T extends LocalAtendimentoUpdateManyArgs>(args: Prisma.SelectSubset<T, LocalAtendimentoUpdateManyArgs<ExtArgs>>): Prisma.PrismaPromise<Prisma.BatchPayload>

  /**
   * Create or update one LocalAtendimento.
   * @param {LocalAtendimentoUpsertArgs} args - Arguments to update or create a LocalAtendimento.
   * @example
   * // Update or create a LocalAtendimento
   * const localAtendimento = await prisma.localAtendimento.upsert({
   *   create: {
   *     // ... data to create a LocalAtendimento
   *   },
   *   update: {
   *     // ... in case it already exists, update
   *   },
   *   where: {
   *     // ... the filter for the LocalAtendimento we want to update
   *   }
   * })
   */
  upsert<T extends LocalAtendimentoUpsertArgs>(args: Prisma.SelectSubset<T, LocalAtendimentoUpsertArgs<ExtArgs>>): Prisma.Prisma__LocalAtendimentoClient<runtime.Types.Result.GetResult<Prisma.$LocalAtendimentoPayload<ExtArgs>, T, "upsert", GlobalOmitOptions>, never, ExtArgs, GlobalOmitOptions>


  /**
   * Count the number of LocalAtendimentos.
   * Note, that providing `undefined` is treated as the value not being there.
   * Read more here: https://pris.ly/d/null-undefined
   * @param {LocalAtendimentoCountArgs} args - Arguments to filter LocalAtendimentos to count.
   * @example
   * // Count the number of LocalAtendimentos
   * const count = await prisma.localAtendimento.count({
   *   where: {
   *     // ... the filter for the LocalAtendimentos we want to count
   *   }
   * })
  **/
  count<T extends LocalAtendimentoCountArgs>(
    args?: Prisma.Subset<T, LocalAtendimentoCountArgs>,
  ): Prisma.PrismaPromise<
    T extends runtime.Types.Utils.Record<'select', any>
      ? T['select'] extends true
        ? number
        : Prisma.GetScalarType<T['select'], LocalAtendimentoCountAggregateOutputType>
      : number
  >

  /**
   * Allows you to perform aggregations operations on a LocalAtendimento.
   * Note, that providing `undefined` is treated as the value not being there.
   * Read more here: https://pris.ly/d/null-undefined
   * @param {LocalAtendimentoAggregateArgs} args - Select which aggregations you would like to apply and on what fields.
   * @example
   * // Ordered by age ascending
   * // Where email contains prisma.io
   * // Limited to the 10 users
   * const aggregations = await prisma.user.aggregate({
   *   _avg: {
   *     age: true,
   *   },
   *   where: {
   *     email: {
   *       contains: "prisma.io",
   *     },
   *   },
   *   orderBy: {
   *     age: "asc",
   *   },
   *   take: 10,
   * })
  **/
  aggregate<T extends LocalAtendimentoAggregateArgs>(args: Prisma.Subset<T, LocalAtendimentoAggregateArgs>): Prisma.PrismaPromise<GetLocalAtendimentoAggregateType<T>>

  /**
   * Group by LocalAtendimento.
   * Note, that providing `undefined` is treated as the value not being there.
   * Read more here: https://pris.ly/d/null-undefined
   * @param {LocalAtendimentoGroupByArgs} args - Group by arguments.
   * @example
   * // Group by city, order by createdAt, get count
   * const result = await prisma.user.groupBy({
   *   by: ['city', 'createdAt'],
   *   orderBy: {
   *     createdAt: true
   *   },
   *   _count: {
   *     _all: true
   *   },
   * })
   * 
  **/
  groupBy<
    T extends LocalAtendimentoGroupByArgs,
    HasSelectOrTake extends Prisma.Or<
      Prisma.Extends<'skip', Prisma.Keys<T>>,
      Prisma.Extends<'take', Prisma.Keys<T>>
    >,
    OrderByArg extends Prisma.True extends HasSelectOrTake
      ? { orderBy: LocalAtendimentoGroupByArgs['orderBy'] }
      : { orderBy?: LocalAtendimentoGroupByArgs['orderBy'] },
    OrderFields extends Prisma.ExcludeUnderscoreKeys<Prisma.Keys<Prisma.MaybeTupleToUnion<T['orderBy']>>>,
    ByFields extends Prisma.MaybeTupleToUnion<T['by']>,
    ByValid extends Prisma.Has<ByFields, OrderFields>,
    HavingFields extends Prisma.GetHavingFields<T['having']>,
    HavingValid extends Prisma.Has<ByFields, HavingFields>,
    ByEmpty extends T['by'] extends never[] ? Prisma.True : Prisma.False,
    InputErrors extends ByEmpty extends Prisma.True
    ? `Error: "by" must not be empty.`
    : HavingValid extends Prisma.False
    ? {
        [P in HavingFields]: P extends ByFields
          ? never
          : P extends string
          ? `Error: Field "${P}" used in "having" needs to be provided in "by".`
          : [
              Error,
              'Field ',
              P,
              ` in "having" needs to be provided in "by"`,
            ]
      }[HavingFields]
    : 'take' extends Prisma.Keys<T>
    ? 'orderBy' extends Prisma.Keys<T>
      ? ByValid extends Prisma.True
        ? {}
        : {
            [P in OrderFields]: P extends ByFields
              ? never
              : `Error: Field "${P}" in "orderBy" needs to be provided in "by"`
          }[OrderFields]
      : 'Error: If you provide "take", you also need to provide "orderBy"'
    : 'skip' extends Prisma.Keys<T>
    ? 'orderBy' extends Prisma.Keys<T>
      ? ByValid extends Prisma.True
        ? {}
        : {
            [P in OrderFields]: P extends ByFields
              ? never
              : `Error: Field "${P}" in "orderBy" needs to be provided in "by"`
          }[OrderFields]
      : 'Error: If you provide "skip", you also need to provide "orderBy"'
    : ByValid extends Prisma.True
    ? {}
    : {
        [P in OrderFields]: P extends ByFields
          ? never
          : `Error: Field "${P}" in "orderBy" needs to be provided in "by"`
      }[OrderFields]
  >(args: Prisma.SubsetIntersection<T, LocalAtendimentoGroupByArgs, OrderByArg> & InputErrors): {} extends InputErrors ? GetLocalAtendimentoGroupByPayload<T> : Prisma.PrismaPromise<InputErrors>
/**
 * Fields of the LocalAtendimento model
 */
readonly fields: LocalAtendimentoFieldRefs;
}

/**
 * The delegate class that acts as a "Promise-like" for LocalAtendimento.
 * Why is this prefixed with `Prisma__`?
 * Because we want to prevent naming conflicts as mentioned in
 * https://github.com/prisma/prisma-client-js/issues/707
 */
export interface Prisma__LocalAtendimentoClient<T, Null = never, ExtArgs extends runtime.Types.Extensions.InternalArgs = runtime.Types.Extensions.DefaultArgs, GlobalOmitOptions = {}> extends Prisma.PrismaPromise<T> {
  readonly [Symbol.toStringTag]: "PrismaPromise"
  cliente<T extends Prisma.ClienteDefaultArgs<ExtArgs> = {}>(args?: Prisma.Subset<T, Prisma.ClienteDefaultArgs<ExtArgs>>): Prisma.Prisma__ClienteClient<runtime.Types.Result.GetResult<Prisma.$ClientePayload<ExtArgs>, T, "findUniqueOrThrow", GlobalOmitOptions> | Null, Null, ExtArgs, GlobalOmitOptions>
  plantoes<T extends Prisma.LocalAtendimento$plantoesArgs<ExtArgs> = {}>(args?: Prisma.Subset<T, Prisma.LocalAtendimento$plantoesArgs<ExtArgs>>): Prisma.PrismaPromise<runtime.Types.Result.GetResult<Prisma.$PlantaoPayload<ExtArgs>, T, "findMany", GlobalOmitOptions> | Null>
  especialidades<T extends Prisma.LocalAtendimento$especialidadesArgs<ExtArgs> = {}>(args?: Prisma.Subset<T, Prisma.LocalAtendimento$especialidadesArgs<ExtArgs>>): Prisma.PrismaPromise<runtime.Types.Result.GetResult<Prisma.$LocalAtendimentoEspecialidadesPayload<ExtArgs>, T, "findMany", GlobalOmitOptions> | Null>
  /**
   * Attaches callbacks for the resolution and/or rejection of the Promise.
   * @param onfulfilled The callback to execute when the Promise is resolved.
   * @param onrejected The callback to execute when the Promise is rejected.
   * @returns A Promise for the completion of which ever callback is executed.
   */
  then<TResult1 = T, TResult2 = never>(onfulfilled?: ((value: T) => TResult1 | PromiseLike<TResult1>) | undefined | null, onrejected?: ((reason: any) => TResult2 | PromiseLike<TResult2>) | undefined | null): runtime.Types.Utils.JsPromise<TResult1 | TResult2>
  /**
   * Attaches a callback for only the rejection of the Promise.
   * @param onrejected The callback to execute when the Promise is rejected.
   * @returns A Promise for the completion of the callback.
   */
  catch<TResult = never>(onrejected?: ((reason: any) => TResult | PromiseLike<TResult>) | undefined | null): runtime.Types.Utils.JsPromise<T | TResult>
  /**
   * Attaches a callback that is invoked when the Promise is settled (fulfilled or rejected). The
   * resolved value cannot be modified from the callback.
   * @param onfinally The callback to execute when the Promise is settled (fulfilled or rejected).
   * @returns A Promise for the completion of the callback.
   */
  finally(onfinally?: (() => void) | undefined | null): runtime.Types.Utils.JsPromise<T>
}




/**
 * Fields of the LocalAtendimento model
 */
export interface LocalAtendimentoFieldRefs {
  readonly id: Prisma.FieldRef<"LocalAtendimento", 'Int'>
  readonly uuid: Prisma.FieldRef<"LocalAtendimento", 'String'>
  readonly clienteId: Prisma.FieldRef<"LocalAtendimento", 'Int'>
  readonly nome: Prisma.FieldRef<"LocalAtendimento", 'String'>
  readonly endereco: Prisma.FieldRef<"LocalAtendimento", 'String'>
  readonly cidade: Prisma.FieldRef<"LocalAtendimento", 'String'>
  readonly estado: Prisma.FieldRef<"LocalAtendimento", 'String'>
  readonly cep: Prisma.FieldRef<"LocalAtendimento", 'String'>
  readonly telefone: Prisma.FieldRef<"LocalAtendimento", 'String'>
  readonly responsavel: Prisma.FieldRef<"LocalAtendimento", 'String'>
  readonly observacoes: Prisma.FieldRef<"LocalAtendimento", 'String'>
  readonly latitude: Prisma.FieldRef<"LocalAtendimento", 'Float'>
  readonly longitude: Prisma.FieldRef<"LocalAtendimento", 'Float'>
  readonly ativo: Prisma.FieldRef<"LocalAtendimento", 'Boolean'>
  readonly fusoHorario: Prisma.FieldRef<"LocalAtendimento", 'String'>
  readonly createdAt: Prisma.FieldRef<"LocalAtendimento", 'DateTime'>
  readonly updatedAt: Prisma.FieldRef<"LocalAtendimento", 'DateTime'>
  readonly deletedAt: Prisma.FieldRef<"LocalAtendimento", 'DateTime'>
}
    

// Custom InputTypes
/**
 * LocalAtendimento findUnique
 */
export type LocalAtendimentoFindUniqueArgs<ExtArgs extends runtime.Types.Extensions.InternalArgs = runtime.Types.Extensions.DefaultArgs> = {
  /**
   * Select specific fields to fetch from the LocalAtendimento
   */
  select?: Prisma.LocalAtendimentoSelect<ExtArgs> | null
  /**
   * Omit specific fields from the LocalAtendimento
   */
  omit?: Prisma.LocalAtendimentoOmit<ExtArgs> | null
  /**
   * Choose, which related nodes to fetch as well
   */
  include?: Prisma.LocalAtendimentoInclude<ExtArgs> | null
  /**
   * Filter, which LocalAtendimento to fetch.
   */
  where: Prisma.LocalAtendimentoWhereUniqueInput
}

/**
 * LocalAtendimento findUniqueOrThrow
 */
export type LocalAtendimentoFindUniqueOrThrowArgs<ExtArgs extends runtime.Types.Extensions.InternalArgs = runtime.Types.Extensions.DefaultArgs> = {
  /**
   * Select specific fields to fetch from the LocalAtendimento
   */
  select?: Prisma.LocalAtendimentoSelect<ExtArgs> | null
  /**
   * Omit specific fields from the LocalAtendimento
   */
  omit?: Prisma.LocalAtendimentoOmit<ExtArgs> | null
  /**
   * Choose, which related nodes to fetch as well
   */
  include?: Prisma.LocalAtendimentoInclude<ExtArgs> | null
  /**
   * Filter, which LocalAtendimento to fetch.
   */
  where: Prisma.LocalAtendimentoWhereUniqueInput
}

/**
 * LocalAtendimento findFirst
 */
export type LocalAtendimentoFindFirstArgs<ExtArgs extends runtime.Types.Extensions.InternalArgs = runtime.Types.Extensions.DefaultArgs> = {
  /**
   * Select specific fields to fetch from the LocalAtendimento
   */
  select?: Prisma.LocalAtendimentoSelect<ExtArgs> | null
  /**
   * Omit specific fields from the LocalAtendimento
   */
  omit?: Prisma.LocalAtendimentoOmit<ExtArgs> | null
  /**
   * Choose, which related nodes to fetch as well
   */
  include?: Prisma.LocalAtendimentoInclude<ExtArgs> | null
  /**
   * Filter, which LocalAtendimento to fetch.
   */
  where?: Prisma.LocalAtendimentoWhereInput
  /**
   * {@link https://www.prisma.io/docs/concepts/components/prisma-client/sorting Sorting Docs}
   * 
   * Determine the order of LocalAtendimentos to fetch.
   */
  orderBy?: Prisma.LocalAtendimentoOrderByWithRelationInput | Prisma.LocalAtendimentoOrderByWithRelationInput[]
  /**
   * {@link https://www.prisma.io/docs/concepts/components/prisma-client/pagination#cursor-based-pagination Cursor Docs}
   * 
   * Sets the position for searching for LocalAtendimentos.
   */
  cursor?: Prisma.LocalAtendimentoWhereUniqueInput
  /**
   * {@link https://www.prisma.io/docs/concepts/components/prisma-client/pagination Pagination Docs}
   * 
   * Take `±n` LocalAtendimentos from the position of the cursor.
   */
  take?: number
  /**
   * {@link https://www.prisma.io/docs/concepts/components/prisma-client/pagination Pagination Docs}
   * 
   * Skip the first `n` LocalAtendimentos.
   */
  skip?: number
  /**
   * {@link https://www.prisma.io/docs/concepts/components/prisma-client/distinct Distinct Docs}
   * 
   * Filter by unique combinations of LocalAtendimentos.
   */
  distinct?: Prisma.LocalAtendimentoScalarFieldEnum | Prisma.LocalAtendimentoScalarFieldEnum[]
}

/**
 * LocalAtendimento findFirstOrThrow
 */
export type LocalAtendimentoFindFirstOrThrowArgs<ExtArgs extends runtime.Types.Extensions.InternalArgs = runtime.Types.Extensions.DefaultArgs> = {
  /**
   * Select specific fields to fetch from the LocalAtendimento
   */
  select?: Prisma.LocalAtendimentoSelect<ExtArgs> | null
  /**
   * Omit specific fields from the LocalAtendimento
   */
  omit?: Prisma.LocalAtendimentoOmit<ExtArgs> | null
  /**
   * Choose, which related nodes to fetch as well
   */
  include?: Prisma.LocalAtendimentoInclude<ExtArgs> | null
  /**
   * Filter, which LocalAtendimento to fetch.
   */
  where?: Prisma.LocalAtendimentoWhereInput
  /**
   * {@link https://www.prisma.io/docs/concepts/components/prisma-client/sorting Sorting Docs}
   * 
   * Determine the order of LocalAtendimentos to fetch.
   */
  orderBy?: Prisma.LocalAtendimentoOrderByWithRelationInput | Prisma.LocalAtendimentoOrderByWithRelationInput[]
  /**
   * {@link https://www.prisma.io/docs/concepts/components/prisma-client/pagination#cursor-based-pagination Cursor Docs}
   * 
   * Sets the position for searching for LocalAtendimentos.
   */
  cursor?: Prisma.LocalAtendimentoWhereUniqueInput
  /**
   * {@link https://www.prisma.io/docs/concepts/components/prisma-client/pagination Pagination Docs}
   * 
   * Take `±n` LocalAtendimentos from the position of the cursor.
   */
  take?: number
  /**
   * {@link https://www.prisma.io/docs/concepts/components/prisma-client/pagination Pagination Docs}
   * 
   * Skip the first `n` LocalAtendimentos.
   */
  skip?: number
  /**
   * {@link https://www.prisma.io/docs/concepts/components/prisma-client/distinct Distinct Docs}
   * 
   * Filter by unique combinations of LocalAtendimentos.
   */
  distinct?: Prisma.LocalAtendimentoScalarFieldEnum | Prisma.LocalAtendimentoScalarFieldEnum[]
}

/**
 * LocalAtendimento findMany
 */
export type LocalAtendimentoFindManyArgs<ExtArgs extends runtime.Types.Extensions.InternalArgs = runtime.Types.Extensions.DefaultArgs> = {
  /**
   * Select specific fields to fetch from the LocalAtendimento
   */
  select?: Prisma.LocalAtendimentoSelect<ExtArgs> | null
  /**
   * Omit specific fields from the LocalAtendimento
   */
  omit?: Prisma.LocalAtendimentoOmit<ExtArgs> | null
  /**
   * Choose, which related nodes to fetch as well
   */
  include?: Prisma.LocalAtendimentoInclude<ExtArgs> | null
  /**
   * Filter, which LocalAtendimentos to fetch.
   */
  where?: Prisma.LocalAtendimentoWhereInput
  /**
   * {@link https://www.prisma.io/docs/concepts/components/prisma-client/sorting Sorting Docs}
   * 
   * Determine the order of LocalAtendimentos to fetch.
   */
  orderBy?: Prisma.LocalAtendimentoOrderByWithRelationInput | Prisma.LocalAtendimentoOrderByWithRelationInput[]
  /**
   * {@link https://www.prisma.io/docs/concepts/components/prisma-client/pagination#cursor-based-pagination Cursor Docs}
   * 
   * Sets the position for listing LocalAtendimentos.
   */
  cursor?: Prisma.LocalAtendimentoWhereUniqueInput
  /**
   * {@link https://www.prisma.io/docs/concepts/components/prisma-client/pagination Pagination Docs}
   * 
   * Take `±n` LocalAtendimentos from the position of the cursor.
   */
  take?: number
  /**
   * {@link https://www.prisma.io/docs/concepts/components/prisma-client/pagination Pagination Docs}
   * 
   * Skip the first `n` LocalAtendimentos.
   */
  skip?: number
  distinct?: Prisma.LocalAtendimentoScalarFieldEnum | Prisma.LocalAtendimentoScalarFieldEnum[]
}

/**
 * LocalAtendimento create
 */
export type LocalAtendimentoCreateArgs<ExtArgs extends runtime.Types.Extensions.InternalArgs = runtime.Types.Extensions.DefaultArgs> = {
  /**
   * Select specific fields to fetch from the LocalAtendimento
   */
  select?: Prisma.LocalAtendimentoSelect<ExtArgs> | null
  /**
   * Omit specific fields from the LocalAtendimento
   */
  omit?: Prisma.LocalAtendimentoOmit<ExtArgs> | null
  /**
   * Choose, which related nodes to fetch as well
   */
  include?: Prisma.LocalAtendimentoInclude<ExtArgs> | null
  /**
   * The data needed to create a LocalAtendimento.
   */
  data: Prisma.XOR<Prisma.LocalAtendimentoCreateInput, Prisma.LocalAtendimentoUncheckedCreateInput>
}

/**
 * LocalAtendimento createMany
 */
export type LocalAtendimentoCreateManyArgs<ExtArgs extends runtime.Types.Extensions.InternalArgs = runtime.Types.Extensions.DefaultArgs> = {
  /**
   * The data used to create many LocalAtendimentos.
   */
  data: Prisma.LocalAtendimentoCreateManyInput | Prisma.LocalAtendimentoCreateManyInput[]
  skipDuplicates?: boolean
}

/**
 * LocalAtendimento update
 */
export type LocalAtendimentoUpdateArgs<ExtArgs extends runtime.Types.Extensions.InternalArgs = runtime.Types.Extensions.DefaultArgs> = {
  /**
   * Select specific fields to fetch from the LocalAtendimento
   */
  select?: Prisma.LocalAtendimentoSelect<ExtArgs> | null
  /**
   * Omit specific fields from the LocalAtendimento
   */
  omit?: Prisma.LocalAtendimentoOmit<ExtArgs> | null
  /**
   * Choose, which related nodes to fetch as well
   */
  include?: Prisma.LocalAtendimentoInclude<ExtArgs> | null
  /**
   * The data needed to update a LocalAtendimento.
   */
  data: Prisma.XOR<Prisma.LocalAtendimentoUpdateInput, Prisma.LocalAtendimentoUncheckedUpdateInput>
  /**
   * Choose, which LocalAtendimento to update.
   */
  where: Prisma.LocalAtendimentoWhereUniqueInput
}

/**
 * LocalAtendimento updateMany
 */
export type LocalAtendimentoUpdateManyArgs<ExtArgs extends runtime.Types.Extensions.InternalArgs = runtime.Types.Extensions.DefaultArgs> = {
  /**
   * The data used to update LocalAtendimentos.
   */
  data: Prisma.XOR<Prisma.LocalAtendimentoUpdateManyMutationInput, Prisma.LocalAtendimentoUncheckedUpdateManyInput>
  /**
   * Filter which LocalAtendimentos to update
   */
  where?: Prisma.LocalAtendimentoWhereInput
  /**
   * Limit how many LocalAtendimentos to update.
   */
  limit?: number
}

/**
 * LocalAtendimento upsert
 */
export type LocalAtendimentoUpsertArgs<ExtArgs extends runtime.Types.Extensions.InternalArgs = runtime.Types.Extensions.DefaultArgs> = {
  /**
   * Select specific fields to fetch from the LocalAtendimento
   */
  select?: Prisma.LocalAtendimentoSelect<ExtArgs> | null
  /**
   * Omit specific fields from the LocalAtendimento
   */
  omit?: Prisma.LocalAtendimentoOmit<ExtArgs> | null
  /**
   * Choose, which related nodes to fetch as well
   */
  include?: Prisma.LocalAtendimentoInclude<ExtArgs> | null
  /**
   * The filter to search for the LocalAtendimento to update in case it exists.
   */
  where: Prisma.LocalAtendimentoWhereUniqueInput
  /**
   * In case the LocalAtendimento found by the `where` argument doesn't exist, create a new LocalAtendimento with this data.
   */
  create: Prisma.XOR<Prisma.LocalAtendimentoCreateInput, Prisma.LocalAtendimentoUncheckedCreateInput>
  /**
   * In case the LocalAtendimento was found with the provided `where` argument, update it with this data.
   */
  update: Prisma.XOR<Prisma.LocalAtendimentoUpdateInput, Prisma.LocalAtendimentoUncheckedUpdateInput>
}

/**
 * LocalAtendimento delete
 */
export type LocalAtendimentoDeleteArgs<ExtArgs extends runtime.Types.Extensions.InternalArgs = runtime.Types.Extensions.DefaultArgs> = {
  /**
   * Select specific fields to fetch from the LocalAtendimento
   */
  select?: Prisma.LocalAtendimentoSelect<ExtArgs> | null
  /**
   * Omit specific fields from the LocalAtendimento
   */
  omit?: Prisma.LocalAtendimentoOmit<ExtArgs> | null
  /**
   * Choose, which related nodes to fetch as well
   */
  include?: Prisma.LocalAtendimentoInclude<ExtArgs> | null
  /**
   * Filter which LocalAtendimento to delete.
   */
  where: Prisma.LocalAtendimentoWhereUniqueInput
}

/**
 * LocalAtendimento deleteMany
 */
export type LocalAtendimentoDeleteManyArgs<ExtArgs extends runtime.Types.Extensions.InternalArgs = runtime.Types.Extensions.DefaultArgs> = {
  /**
   * Filter which LocalAtendimentos to delete
   */
  where?: Prisma.LocalAtendimentoWhereInput
  /**
   * Limit how many LocalAtendimentos to delete.
   */
  limit?: number
}

/**
 * LocalAtendimento.plantoes
 */
export type LocalAtendimento$plantoesArgs<ExtArgs extends runtime.Types.Extensions.InternalArgs = runtime.Types.Extensions.DefaultArgs> = {
  /**
   * Select specific fields to fetch from the Plantao
   */
  select?: Prisma.PlantaoSelect<ExtArgs> | null
  /**
   * Omit specific fields from the Plantao
   */
  omit?: Prisma.PlantaoOmit<ExtArgs> | null
  /**
   * Choose, which related nodes to fetch as well
   */
  include?: Prisma.PlantaoInclude<ExtArgs> | null
  where?: Prisma.PlantaoWhereInput
  orderBy?: Prisma.PlantaoOrderByWithRelationInput | Prisma.PlantaoOrderByWithRelationInput[]
  cursor?: Prisma.PlantaoWhereUniqueInput
  take?: number
  skip?: number
  distinct?: Prisma.PlantaoScalarFieldEnum | Prisma.PlantaoScalarFieldEnum[]
}

/**
 * LocalAtendimento.especialidades
 */
export type LocalAtendimento$especialidadesArgs<ExtArgs extends runtime.Types.Extensions.InternalArgs = runtime.Types.Extensions.DefaultArgs> = {
  /**
   * Select specific fields to fetch from the LocalAtendimentoEspecialidades
   */
  select?: Prisma.LocalAtendimentoEspecialidadesSelect<ExtArgs> | null
  /**
   * Omit specific fields from the LocalAtendimentoEspecialidades
   */
  omit?: Prisma.LocalAtendimentoEspecialidadesOmit<ExtArgs> | null
  /**
   * Choose, which related nodes to fetch as well
   */
  include?: Prisma.LocalAtendimentoEspecialidadesInclude<ExtArgs> | null
  where?: Prisma.LocalAtendimentoEspecialidadesWhereInput
  orderBy?: Prisma.LocalAtendimentoEspecialidadesOrderByWithRelationInput | Prisma.LocalAtendimentoEspecialidadesOrderByWithRelationInput[]
  cursor?: Prisma.LocalAtendimentoEspecialidadesWhereUniqueInput
  take?: number
  skip?: number
  distinct?: Prisma.LocalAtendimentoEspecialidadesScalarFieldEnum | Prisma.LocalAtendimentoEspecialidadesScalarFieldEnum[]
}

/**
 * LocalAtendimento without action
 */
export type LocalAtendimentoDefaultArgs<ExtArgs extends runtime.Types.Extensions.InternalArgs = runtime.Types.Extensions.DefaultArgs> = {
  /**
   * Select specific fields to fetch from the LocalAtendimento
   */
  select?: Prisma.LocalAtendimentoSelect<ExtArgs> | null
  /**
   * Omit specific fields from the LocalAtendimento
   */
  omit?: Prisma.LocalAtendimentoOmit<ExtArgs> | null
  /**
   * Choose, which related nodes to fetch as well
   */
  include?: Prisma.LocalAtendimentoInclude<ExtArgs> | null
}
