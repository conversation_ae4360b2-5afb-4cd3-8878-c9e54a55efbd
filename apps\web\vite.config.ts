import { ViteP<PERSON> } from "vite-plugin-pwa";
import tailwindcss from "@tailwindcss/vite";
import { defineConfig } from "vite";
import tsconfigPaths from "vite-tsconfig-paths";
import react from "@vitejs/plugin-react";
import { tanstackRouter } from "@tanstack/router-plugin/vite";

export default defineConfig({
  build: {
    outDir: "../../web-dist",
  },
  server: {
    port: 5173,
    host: false,
    open: false,
    // hmr: { overlay: false },
    // watch: {
    //   usePolling: true,
    // },
  },
  plugins: [
    tanstackRouter({
      target: "react",
      autoCodeSplitting: true,
    }),
    react(),
    tailwindcss(),
    tsconfigPaths(),
    VitePWA({
      registerType: "autoUpdate",
      manifest: {
        name: "GS2",
        short_name: "GS2",
        description: "GS2",
        theme_color: "#165487",
      },
      pwaAssets: { disabled: false, config: true },
      devOptions: { enabled: false },
    }),
  ],
});
