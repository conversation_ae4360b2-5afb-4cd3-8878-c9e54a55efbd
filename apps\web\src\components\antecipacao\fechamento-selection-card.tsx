import { Card, CardContent, CardDescription, CardHeader, CardTitle } from "@/components/ui/card";
import { <PERSON><PERSON> } from "@/components/ui/button";
import { Badge } from "@/components/ui/badge";
import { Table, TableBody, TableCell, TableHead, TableHeader, TableRow } from "@/components/ui/table";
import { Building2, MapPin, User } from "lucide-react";
import { formatCurrency } from "@/lib/utils";
import type { Fechamento, Plantao } from "@/lib/api";

interface FechamentoSelectionCardProps {
  selectedPlantao: Plantao;
  fechamentos: { data: Fechamento[] } | undefined;
  selectedFechamentos: string[];
  totalSelectedValue: number;
  onBackToStep1: () => void;
  onFechamentoToggle: (fechamentoId: string) => void;
  onSelectAll: () => void;
  onClearSelection: () => void;
}

export function FechamentoSelectionCard({
  selectedPlantao,
  fechamentos,
  selectedFechamentos,
  totalSelectedValue,
  onBackToStep1,
  onFechamentoToggle,
  onSelectAll,
  onClearSelection,
}: FechamentoSelectionCardProps) {
  return (
    <Card>
      <CardHeader>
        <div className="flex items-center justify-between">
          <div>
            <CardTitle>Plantão Selecionado</CardTitle>
            <CardDescription>Fechamentos aprovados para antecipação</CardDescription>
          </div>
        </div>
      </CardHeader>
      <CardContent className="space-y-4">
        <div className="grid gap-2">
          <div className="flex items-center gap-2">
            <User className="h-4 w-4 text-muted-foreground" />
            <span className="font-semibold">{selectedPlantao.profissional?.usuario?.nome}</span>
          </div>
          <div className="flex items-center gap-2">
            <Building2 className="h-4 w-4 text-muted-foreground" />
            <span>{selectedPlantao.cliente?.nome}</span>
          </div>
          <div className="flex items-center gap-2">
            <MapPin className="h-4 w-4 text-muted-foreground" />
            <span>{selectedPlantao.localAtendimento?.nome}</span>
          </div>
        </div>

        {fechamentos?.data && fechamentos.data.length > 0 && (
          <div className="space-y-4">
            <div className="flex items-center justify-between">
              <h4 className="font-medium">Fechamentos Disponíveis</h4>
              <div className="flex gap-2">
                <Button type="button" variant="outline" size="sm" onClick={onSelectAll}>
                  Selecionar Todos
                </Button>
                <Button type="button" variant="outline" size="sm" onClick={onClearSelection}>
                  Limpar Seleção
                </Button>
              </div>
            </div>
            <div className="border rounded-lg">
              <Table>
                <TableHeader>
                  <TableRow>
                    <TableHead className="w-12"></TableHead>
                    <TableHead>Ref. Fechamento</TableHead>
                    <TableHead>Dias Trabalhados</TableHead>
                    <TableHead>Valor Total</TableHead>
                  </TableRow>
                </TableHeader>
                <TableBody>
                  {fechamentos.data.map((fechamento) => (
                    <TableRow key={fechamento.uuid} className="hover:bg-muted/50">
                      <TableCell>
                        <input
                          type="checkbox"
                          checked={selectedFechamentos.includes(fechamento.uuid)}
                          onChange={() => onFechamentoToggle(fechamento.uuid)}
                          className="rounded border-gray-300 text-blue-600 focus:ring-blue-500"
                        />
                      </TableCell>
                      <TableCell className="font-mono">#{fechamento.id.toString().padStart(6, "0")}</TableCell>
                      <TableCell>{fechamento.diasTrabalhados}</TableCell>
                      <TableCell className="font-medium">{formatCurrency(fechamento.totalValor)}</TableCell>
                    </TableRow>
                  ))}
                </TableBody>
              </Table>
            </div>
          </div>
        )}

        <div className="bg-muted/50 rounded-lg p-4">
          <div className="flex justify-between text-sm">
            <span>Total de Fechamentos:</span>
            <span>{fechamentos?.data?.length || 0}</span>
          </div>
          <div className="flex justify-between text-sm font-semibold border-t pt-2">
            <span>Valor Total:</span>
            <span className="text-green-600">{formatCurrency(totalSelectedValue)}</span>
          </div>
          {/* <div className="flex justify-between text-sm">
            <span className="text-muted-foreground">Taxa Padrão:</span>
            <Badge variant="outline">{selectedPlantao.cliente.taxaPadrao}%</Badge>
          </div> */}
        </div>
      </CardContent>
    </Card>
  );
}
