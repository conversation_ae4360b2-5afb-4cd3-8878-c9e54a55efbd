import { createFile<PERSON>out<PERSON>, useNavigate } from "@tanstack/react-router";
import { useMutation, useQuery } from "@tanstack/react-query";
import { api } from "@/lib/api";
import { Button } from "@/components/ui/button";
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from "@/components/ui/card";
import { Checkbox } from "@/components/ui/checkbox";
import { Alert, AlertDescription } from "@/components/ui/alert";
import { Badge } from "@/components/ui/badge";
import { Separator } from "@/components/ui/separator";
import {
  FileText,
  Calendar,
  Building2,
  User,
  MapPin,
  DollarSign,
  Percent,
  Calculator,
  AlertCircle,
  CheckCircle,
  FileSignature,
  Loader2,
  ArrowLeft,
} from "lucide-react";
import { formatCurrency, formatDate } from "@/lib/utils";
import { useState } from "react";
import { toast } from "sonner";

interface TermoData {
  numeroTermo: string;
  dataEmissao: string;
  hospital: string;
  hospitalCNPJ: string;
  profissional: string;
  profissionalCPF: string;
  localAtendimento: string;
  periodo: string;
  valorTotal: number;
  percentual: number;
  taxaAntecipacao: number;
  valorLiquido: number;
  dataPagamentoPrevista: string;
  fechamentos: Array<{
    uuid: string;
    valor: number;
  }>;
}

interface TermoResponse {
  antecipacao: TermoData;
  termoAssinado: boolean;
  profissionalId: number;
  isAuthenticated: boolean;
  isProfissional: boolean;
  isAdmin: boolean;
}

function TermoAntecipacao() {
  const { uuid } = Route.useParams();
  const navigate = useNavigate();
  const [aceite, setAceite] = useState(false);

  // Buscar dados do termo
  const { data, isLoading, error } = useQuery<TermoResponse>({
    queryKey: ["termo-antecipacao", uuid],
    queryFn: () => api.get(`/antecipacoes/${uuid}/termo`),
    retry: false,
  });

  // Mutation para assinar o termo
  const assinaturaMutation = useMutation({
    mutationFn: () => api.post(`/antecipacoes/${uuid}/assinar`, { aceite: true }),
    onSuccess: () => {
      toast.success("Termo assinado com sucesso!");
      navigate({ to: "/profissional/dashboard" });
    },
    onError: (error: any) => {
      toast.error(error.response?.data?.error || "Erro ao assinar o termo");
    },
  });

  const handleAssinar = () => {
    if (!aceite) {
      toast.error("É necessário aceitar os termos para prosseguir");
      return;
    }
    assinaturaMutation.mutate();
  };

  if (isLoading) {
    return (
      <div className="container mx-auto py-8">
        <div className="flex items-center justify-center min-h-[50vh]">
          <div className="flex items-center gap-2">
            <Loader2 className="h-6 w-6 animate-spin" />
            <span>Carregando termo de antecipação...</span>
          </div>
        </div>
      </div>
    );
  }

  if (error) {
    return (
      <div className="container mx-auto py-8">
        <div className="max-w-2xl mx-auto">
          <Alert variant="destructive">
            <AlertCircle className="h-4 w-4" />
            <AlertDescription>{(error as any)?.response?.data?.error || "Erro ao carregar o termo"}</AlertDescription>
          </Alert>
          <div className="mt-4">
            <Button variant="outline" onClick={() => navigate({ to: "/profissional/dashboard" })}>
              <ArrowLeft className="mr-2 h-4 w-4" />
              Voltar ao Dashboard
            </Button>
          </div>
        </div>
      </div>
    );
  }

  if (!data) {
    return null;
  }

  const { antecipacao } = data;

  return (
    <div className="container mx-auto py-8">
      <div className="max-w-4xl mx-auto space-y-6">
        {/* Header */}
        <div className="flex items-center justify-between">
          <div>
            <h1 className="text-3xl font-bold flex items-center gap-2">
              <FileSignature className="h-8 w-8" />
              Termo de Cessão de Direitos de Crédito
            </h1>
            <p className="text-muted-foreground mt-1">Termo #{antecipacao.numeroTermo.slice(0, 8)}</p>
          </div>
          <Button variant="outline" onClick={() => navigate({ to: "/profissional/dashboard" })}>
            <ArrowLeft className="mr-2 h-4 w-4" />
            Voltar
          </Button>
        </div>

        {/* Informações da Antecipação */}
        <Card>
          <CardHeader>
            <CardTitle className="flex items-center gap-2">
              <FileText className="h-5 w-5" />
              Informações da Antecipação
            </CardTitle>
          </CardHeader>
          <CardContent className="space-y-4">
            <div className="grid md:grid-cols-2 gap-4">
              <div className="space-y-3">
                <div className="flex items-start gap-2">
                  <Building2 className="h-4 w-4 text-muted-foreground mt-0.5" />
                  <div>
                    <p className="text-sm text-muted-foreground">Hospital/Cliente</p>
                    <p className="font-medium">{antecipacao.hospital}</p>
                    <p className="text-xs text-muted-foreground">CNPJ: {antecipacao.hospitalCNPJ}</p>
                  </div>
                </div>

                <div className="flex items-start gap-2">
                  <User className="h-4 w-4 text-muted-foreground mt-0.5" />
                  <div>
                    <p className="text-sm text-muted-foreground">Profissional</p>
                    <p className="font-medium">{antecipacao.profissional}</p>
                    <p className="text-xs text-muted-foreground">CPF: {antecipacao.profissionalCPF}</p>
                  </div>
                </div>

                <div className="flex items-start gap-2">
                  <MapPin className="h-4 w-4 text-muted-foreground mt-0.5" />
                  <div>
                    <p className="text-sm text-muted-foreground">Local de Atendimento</p>
                    <p className="font-medium">{antecipacao.localAtendimento}</p>
                  </div>
                </div>
              </div>

              <div className="space-y-3">
                <div className="flex items-start gap-2">
                  <Calendar className="h-4 w-4 text-muted-foreground mt-0.5" />
                  <div>
                    <p className="text-sm text-muted-foreground">Período</p>
                    <p className="font-medium">{antecipacao.periodo}</p>
                  </div>
                </div>

                <div className="flex items-start gap-2">
                  <FileText className="h-4 w-4 text-muted-foreground mt-0.5" />
                  <div>
                    <p className="text-sm text-muted-foreground">Data de Emissão</p>
                    <p className="font-medium">{formatDate(antecipacao.dataEmissao)}</p>
                  </div>
                </div>

                <div className="flex items-start gap-2">
                  <CheckCircle className="h-4 w-4 text-muted-foreground mt-0.5" />
                  <div>
                    <p className="text-sm text-muted-foreground">Fechamentos</p>
                    <p className="font-medium">{antecipacao.fechamentos.length} fechamento(s)</p>
                  </div>
                </div>
              </div>
            </div>
          </CardContent>
        </Card>

        {/* Valores */}
        <Card>
          <CardHeader>
            <CardTitle className="flex items-center gap-2">
              <Calculator className="h-5 w-5" />
              Valores da Antecipação
            </CardTitle>
          </CardHeader>
          <CardContent>
            <div className="grid md:grid-cols-2 lg:grid-cols-4 gap-4">
              <div className="text-center p-4 bg-blue-50 rounded-lg">
                <DollarSign className="h-6 w-6 text-blue-600 mx-auto mb-2" />
                <p className="text-sm text-blue-600">Valor Total</p>
                <p className="text-xl font-bold text-blue-800">{formatCurrency(antecipacao.valorTotal)}</p>
              </div>

              <div className="text-center p-4 bg-purple-50 rounded-lg">
                <Percent className="h-6 w-6 text-purple-600 mx-auto mb-2" />
                <p className="text-sm text-purple-600">Percentual</p>
                <p className="text-xl font-bold text-purple-800">{antecipacao.percentual}%</p>
              </div>

              <div className="text-center p-4 bg-orange-50 rounded-lg">
                <Calculator className="h-6 w-6 text-orange-600 mx-auto mb-2" />
                <p className="text-sm text-orange-600">Taxa</p>
                <p className="text-xl font-bold text-orange-800">{antecipacao.taxaAntecipacao}%</p>
              </div>

              <div className="text-center p-4 bg-green-50 rounded-lg">
                <CheckCircle className="h-6 w-6 text-green-600 mx-auto mb-2" />
                <p className="text-sm text-green-600">Valor Líquido</p>
                <p className="text-xl font-bold text-green-800">{formatCurrency(antecipacao.valorLiquido)}</p>
              </div>
            </div>

            {antecipacao.dataPagamentoPrevista && (
              <div className="mt-4 p-3 bg-amber-50 rounded-lg">
                <p className="text-sm text-amber-700">
                  <Calendar className="h-4 w-4 inline mr-1" />
                  <strong>Previsão de Pagamento:</strong> {formatDate(antecipacao.dataPagamentoPrevista)}
                </p>
              </div>
            )}
          </CardContent>
        </Card>

        {/* Termo Legal */}
        <Card>
          <CardHeader>
            <CardTitle className="flex items-center gap-2">
              <FileText className="h-5 w-5" />
              Termo de Cessão de Direitos de Crédito
            </CardTitle>
          </CardHeader>
          <CardContent className="space-y-4">
            <div className="prose prose-sm max-w-none">
              <p>
                Pelo presente instrumento, <strong>{antecipacao.profissional}</strong>, inscrito no CPF sob nº{" "}
                {antecipacao.profissionalCPF}, doravante denominado <strong>"CEDENTE"</strong>, cede e transfere à{" "}
                <strong>GS2 GROUP LTDA</strong>, inscrita no CNPJ sob nº XX.XXX.XXX/0001-XX, doravante denominada{" "}
                <strong>"CESSIONÁRIA"</strong>, todos os direitos creditórios decorrentes dos serviços prestados.
              </p>

              <h4 className="font-semibold mt-4">CLÁUSULA PRIMEIRA - DO OBJETO</h4>
              <p>
                O presente contrato tem por objeto a cessão de direitos creditórios no valor de{" "}
                <strong>{formatCurrency(antecipacao.valorTotal)}</strong>, referente aos serviços prestados no período
                de <strong>{antecipacao.periodo}</strong> para <strong>{antecipacao.hospital}</strong>.
              </p>

              <h4 className="font-semibold mt-4">CLÁUSULA SEGUNDA - DO VALOR E PAGAMENTO</h4>
              <p>
                O CEDENTE receberá antecipadamente o valor líquido de{" "}
                <strong>{formatCurrency(antecipacao.valorLiquido)}</strong>, já descontada a taxa de antecipação de{" "}
                <strong>{antecipacao.taxaAntecipacao}%</strong>.
              </p>

              <h4 className="font-semibold mt-4">CLÁUSULA TERCEIRA - DAS OBRIGAÇÕES</h4>
              <p>
                O CEDENTE declara e garante que os créditos ora cedidos são líquidos, certos e exigíveis, livres de
                quaisquer ônus, gravames ou restrições, responsabilizando-se pela veracidade das informações prestadas.
              </p>

              <h4 className="font-semibold mt-4">CLÁUSULA QUARTA - DA VIGÊNCIA</h4>
              <p>
                Este termo entra em vigor na data de sua assinatura digital e permanece válido até a completa liquidação
                das obrigações aqui estabelecidas.
              </p>
            </div>
          </CardContent>
        </Card>

        {/* Aceite */}
        <Card className="border-2 border-primary/20">
          <CardContent className="pt-6">
            <div className="flex items-start space-x-3">
              <Checkbox
                id="aceite"
                checked={aceite}
                onCheckedChange={(checked) => setAceite(!!checked)}
                className="mt-1"
              />
              <div className="space-y-1">
                <label
                  htmlFor="aceite"
                  className="text-sm font-medium leading-none peer-disabled:cursor-not-allowed peer-disabled:opacity-70 cursor-pointer"
                >
                  Li e aceito os termos acima
                </label>
                <p className="text-xs text-muted-foreground">
                  Declaro que li, compreendi e aceito todos os termos e condições deste Termo de Cessão de Direitos de
                  Crédito.
                </p>
              </div>
            </div>

            <Separator className="my-6" />

            <div className="flex gap-3">
              <Button
                variant="outline"
                onClick={() => navigate({ to: "/profissional/dashboard" })}
                disabled={assinaturaMutation.isPending}
              >
                Cancelar
              </Button>
              <Button onClick={handleAssinar} disabled={!aceite || assinaturaMutation.isPending} className="flex-1">
                {assinaturaMutation.isPending ? (
                  <>
                    <Loader2 className="mr-2 h-4 w-4 animate-spin" />
                    Assinando...
                  </>
                ) : (
                  <>
                    <FileSignature className="mr-2 h-4 w-4" />
                    Assinar Digitalmente
                  </>
                )}
              </Button>
            </div>
          </CardContent>
        </Card>
      </div>
    </div>
  );
}

export const Route = createFileRoute("/antecipacoes/$uuid/termo")({
  component: TermoAntecipacao,
});
