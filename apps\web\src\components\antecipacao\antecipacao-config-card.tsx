import { Card, CardContent, CardDescription, CardHeader, CardTitle } from "@/components/ui/card";
import { Input } from "@/components/ui/input";
import { Label } from "@/components/ui/label";
import { Textarea } from "@/components/ui/textarea";
import { Alert, AlertDescription, AlertTitle } from "@/components/ui/alert";
import { DollarSign, Calculator, Percent, Info, Calendar } from "lucide-react";
import { formatCurrency } from "@/lib/utils";

interface AntecipacaoForm {
  plantaoId: string;
  fechamentoIds: string[];
  valorSolicitado: number;
  valorAprovado?: number;
  percentual: number;
  taxaPadrao: number;
  taxaAntecipacao: number;
  valorDesconto: number;
  valorLiquido: number;
  dataPagamentoPrevista: string;
  observacoes?: string;
}

interface AntecipacaoConfigCardProps {
  valorSolicitadoField: any;
  dataPagamentoPrevistaField: any;
  observacoesField: any;
  percentualField: any;
  taxaPadraoField: any;
  taxaAntecipacaoField: any;
  formValues: AntecipacaoForm;
  totalSelectedValue: number;
  onValorChange: (valor: number) => void;
  onTaxaChange: (taxa: number) => void;
  onTaxaPadraoChange: (taxa: number) => void;
  onPercentualChange: (percentual: number) => void;
  diasAteVencimento: number;
  taxaCalculada: number;
  percentualMaximo: number;
}

export function AntecipacaoConfigCard({
  valorSolicitadoField,
  dataPagamentoPrevistaField,
  observacoesField,
  percentualField,
  taxaPadraoField,
  taxaAntecipacaoField,
  formValues,
  totalSelectedValue,
  onValorChange,
  onTaxaChange,
  onTaxaPadraoChange,
  onPercentualChange,
  diasAteVencimento,
  taxaCalculada,
  percentualMaximo,
}: AntecipacaoConfigCardProps) {
  return (
    <Card>
      <CardHeader>
        <CardTitle className="flex items-center gap-2">
          <Calculator className="h-5 w-5" />
          Dados da Antecipação
        </CardTitle>
        <CardDescription>Configure os valores e condições</CardDescription>
      </CardHeader>
      <CardContent className="space-y-4">
        <div className="grid gap-4 md:grid-cols-2">
          <div className="space-y-2">
            <Label htmlFor="valorSolicitado">Valor Solicitado *</Label>
            <div className="relative">
              <DollarSign className="absolute left-3 top-3 h-4 w-4 text-muted-foreground" />
              <Input
                id="valorSolicitado"
                type="number"
                step="0.01"
                min="0"
                max={totalSelectedValue}
                value={valorSolicitadoField.state.value}
                onBlur={valorSolicitadoField.handleBlur}
                onChange={(e) => onValorChange(parseFloat(e.target.value) || 0)}
                placeholder="0.00"
                className="pl-10"
              />
            </div>
            {valorSolicitadoField.state.value > totalSelectedValue && (
              <Alert variant="destructive">
                <AlertDescription>Valor acima do limite</AlertDescription>
              </Alert>
            )}
          </div>

          <div className="space-y-2">
            <Label htmlFor="percentual">Percentual de Antecipação *</Label>
            <div className="relative">
              <Percent className="absolute left-3 top-3 h-4 w-4 text-muted-foreground" />
              <Input
                id="percentual"
                type="number"
                step="1"
                min="1"
                max={percentualMaximo}
                value={percentualField.state.value}
                onBlur={percentualField.handleBlur}
                onChange={(e) => {
                  const value = Math.min(percentualMaximo, Math.max(1, parseFloat(e.target.value) || 1));
                  onPercentualChange(value);
                }}
                placeholder="70"
                className="pl-10"
              />
            </div>
            {percentualField.state.value > percentualMaximo && (
              <Alert variant="destructive">
                <AlertDescription>Máximo {percentualMaximo}%</AlertDescription>
              </Alert>
            )}
          </div>
        </div>

        <div className="space-y-2">
          <div className="flex items-center gap-2">
            <Label htmlFor="taxaPadrao">Taxa Padrão Mensal *</Label>
            <Info className="h-4 w-4 text-muted-foreground" />
          </div>
          <div className="relative">
            <Percent className="absolute left-3 top-3 h-4 w-4 text-muted-foreground" />
            <Input
              id="taxaPadrao"
              type="number"
              step="0.01"
              min="0"
              max="50"
              value={taxaPadraoField.state.value}
              onBlur={taxaPadraoField.handleBlur}
              onChange={(e) => onTaxaPadraoChange(parseFloat(e.target.value) || 0)}
              placeholder="5.00"
              className="pl-10"
            />
          </div>
          <p className="text-sm text-muted-foreground">Taxa base mensal para cálculo de juros compostos</p>
        </div>

        <div className="space-y-2">
          <div className="flex items-center gap-2">
            <Label htmlFor="taxaAntecipacao">Taxa de Antecipação *</Label>
            <Info className="h-4 w-4 text-muted-foreground" />
          </div>
          {/* <Alert>
            <Info className="h-4 w-4" />
            <AlertTitle>Taxa Calculada Automaticamente</AlertTitle>
            <AlertDescription>
              Taxa calculada com base em {diasAteVencimento} dias até o vencimento.
              {diasAteVencimento <= 30
                ? " Taxa mensal aplicada diretamente."
                : ` Fórmula: (1 + taxa)^(${diasAteVencimento}/30) - 1`}
            </AlertDescription>
          </Alert> */}
          <div className="relative">
            <Percent className="absolute left-3 top-3 h-4 w-4 text-muted-foreground" />
            <Input
              id="taxaAntecipacao"
              type="number"
              step="0.01"
              min="0"
              max={percentualMaximo}
              value={taxaAntecipacaoField.state.value}
              onBlur={taxaAntecipacaoField.handleBlur}
              onChange={(e) => onTaxaChange(parseFloat(e.target.value) || 0)}
              placeholder={taxaCalculada.toFixed(2)}
              className="pl-10"
            />
          </div>
          <p className="text-sm text-muted-foreground">
            Valor padrão: {taxaCalculada.toFixed(2)}% | Desconto: {formatCurrency(formValues.valorDesconto)}
          </p>
        </div>

        <div className="space-y-2">
          <div className="flex items-center gap-2">
            <Label htmlFor="dataPagamentoPrevista">Data de Pagamento Prevista</Label>
            <Calendar className="h-4 w-4 text-muted-foreground" />
          </div>
          <Alert>
            <Info className="h-4 w-4" />
            <AlertDescription>
              Data calculada automaticamente com base no prazo de pagamento do plantão.
            </AlertDescription>
          </Alert>
          <Input
            id="dataPagamentoPrevista"
            type="date"
            value={dataPagamentoPrevistaField.state.value}
            disabled
            className="bg-muted"
          />
        </div>

        <div className="space-y-2">
          <Label htmlFor="observacoes">Observações</Label>
          <Textarea
            id="observacoes"
            value={observacoesField.state.value}
            onBlur={observacoesField.handleBlur}
            onChange={(e) => observacoesField.handleChange(e.target.value)}
            placeholder="Informações adicionais sobre a antecipação..."
            rows={3}
          />
        </div>
      </CardContent>
    </Card>
  );
}
