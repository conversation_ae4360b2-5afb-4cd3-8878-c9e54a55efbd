
/* !!! This is code generated by Prisma. Do not edit directly. !!! */
/* eslint-disable */
// @ts-nocheck 
/*
 * This file exports the `UsuarioRole` model and its related types.
 *
 * 🟢 You can import this file directly.
 */
import * as runtime from "@prisma/client/runtime/library"
import type * as $Enums from "../enums.ts"
import type * as Prisma from "../internal/prismaNamespace.ts"

/**
 * Model UsuarioRole
 * 
 */
export type UsuarioRoleModel = runtime.Types.Result.DefaultSelection<Prisma.$UsuarioRolePayload>

export type AggregateUsuarioRole = {
  _count: UsuarioRoleCountAggregateOutputType | null
  _avg: UsuarioRoleAvgAggregateOutputType | null
  _sum: UsuarioRoleSumAggregateOutputType | null
  _min: UsuarioRoleMinAggregateOutputType | null
  _max: UsuarioRoleMaxAggregateOutputType | null
}

export type UsuarioRoleAvgAggregateOutputType = {
  id: number | null
  usuarioId: number | null
  roleId: number | null
}

export type UsuarioRoleSumAggregateOutputType = {
  id: number | null
  usuarioId: number | null
  roleId: number | null
}

export type UsuarioRoleMinAggregateOutputType = {
  id: number | null
  usuarioId: number | null
  roleId: number | null
  createdAt: Date | null
}

export type UsuarioRoleMaxAggregateOutputType = {
  id: number | null
  usuarioId: number | null
  roleId: number | null
  createdAt: Date | null
}

export type UsuarioRoleCountAggregateOutputType = {
  id: number
  usuarioId: number
  roleId: number
  createdAt: number
  _all: number
}


export type UsuarioRoleAvgAggregateInputType = {
  id?: true
  usuarioId?: true
  roleId?: true
}

export type UsuarioRoleSumAggregateInputType = {
  id?: true
  usuarioId?: true
  roleId?: true
}

export type UsuarioRoleMinAggregateInputType = {
  id?: true
  usuarioId?: true
  roleId?: true
  createdAt?: true
}

export type UsuarioRoleMaxAggregateInputType = {
  id?: true
  usuarioId?: true
  roleId?: true
  createdAt?: true
}

export type UsuarioRoleCountAggregateInputType = {
  id?: true
  usuarioId?: true
  roleId?: true
  createdAt?: true
  _all?: true
}

export type UsuarioRoleAggregateArgs<ExtArgs extends runtime.Types.Extensions.InternalArgs = runtime.Types.Extensions.DefaultArgs> = {
  /**
   * Filter which UsuarioRole to aggregate.
   */
  where?: Prisma.UsuarioRoleWhereInput
  /**
   * {@link https://www.prisma.io/docs/concepts/components/prisma-client/sorting Sorting Docs}
   * 
   * Determine the order of UsuarioRoles to fetch.
   */
  orderBy?: Prisma.UsuarioRoleOrderByWithRelationInput | Prisma.UsuarioRoleOrderByWithRelationInput[]
  /**
   * {@link https://www.prisma.io/docs/concepts/components/prisma-client/pagination#cursor-based-pagination Cursor Docs}
   * 
   * Sets the start position
   */
  cursor?: Prisma.UsuarioRoleWhereUniqueInput
  /**
   * {@link https://www.prisma.io/docs/concepts/components/prisma-client/pagination Pagination Docs}
   * 
   * Take `±n` UsuarioRoles from the position of the cursor.
   */
  take?: number
  /**
   * {@link https://www.prisma.io/docs/concepts/components/prisma-client/pagination Pagination Docs}
   * 
   * Skip the first `n` UsuarioRoles.
   */
  skip?: number
  /**
   * {@link https://www.prisma.io/docs/concepts/components/prisma-client/aggregations Aggregation Docs}
   * 
   * Count returned UsuarioRoles
  **/
  _count?: true | UsuarioRoleCountAggregateInputType
  /**
   * {@link https://www.prisma.io/docs/concepts/components/prisma-client/aggregations Aggregation Docs}
   * 
   * Select which fields to average
  **/
  _avg?: UsuarioRoleAvgAggregateInputType
  /**
   * {@link https://www.prisma.io/docs/concepts/components/prisma-client/aggregations Aggregation Docs}
   * 
   * Select which fields to sum
  **/
  _sum?: UsuarioRoleSumAggregateInputType
  /**
   * {@link https://www.prisma.io/docs/concepts/components/prisma-client/aggregations Aggregation Docs}
   * 
   * Select which fields to find the minimum value
  **/
  _min?: UsuarioRoleMinAggregateInputType
  /**
   * {@link https://www.prisma.io/docs/concepts/components/prisma-client/aggregations Aggregation Docs}
   * 
   * Select which fields to find the maximum value
  **/
  _max?: UsuarioRoleMaxAggregateInputType
}

export type GetUsuarioRoleAggregateType<T extends UsuarioRoleAggregateArgs> = {
      [P in keyof T & keyof AggregateUsuarioRole]: P extends '_count' | 'count'
    ? T[P] extends true
      ? number
      : Prisma.GetScalarType<T[P], AggregateUsuarioRole[P]>
    : Prisma.GetScalarType<T[P], AggregateUsuarioRole[P]>
}




export type UsuarioRoleGroupByArgs<ExtArgs extends runtime.Types.Extensions.InternalArgs = runtime.Types.Extensions.DefaultArgs> = {
  where?: Prisma.UsuarioRoleWhereInput
  orderBy?: Prisma.UsuarioRoleOrderByWithAggregationInput | Prisma.UsuarioRoleOrderByWithAggregationInput[]
  by: Prisma.UsuarioRoleScalarFieldEnum[] | Prisma.UsuarioRoleScalarFieldEnum
  having?: Prisma.UsuarioRoleScalarWhereWithAggregatesInput
  take?: number
  skip?: number
  _count?: UsuarioRoleCountAggregateInputType | true
  _avg?: UsuarioRoleAvgAggregateInputType
  _sum?: UsuarioRoleSumAggregateInputType
  _min?: UsuarioRoleMinAggregateInputType
  _max?: UsuarioRoleMaxAggregateInputType
}

export type UsuarioRoleGroupByOutputType = {
  id: number
  usuarioId: number
  roleId: number
  createdAt: Date
  _count: UsuarioRoleCountAggregateOutputType | null
  _avg: UsuarioRoleAvgAggregateOutputType | null
  _sum: UsuarioRoleSumAggregateOutputType | null
  _min: UsuarioRoleMinAggregateOutputType | null
  _max: UsuarioRoleMaxAggregateOutputType | null
}

type GetUsuarioRoleGroupByPayload<T extends UsuarioRoleGroupByArgs> = Prisma.PrismaPromise<
  Array<
    Prisma.PickEnumerable<UsuarioRoleGroupByOutputType, T['by']> &
      {
        [P in ((keyof T) & (keyof UsuarioRoleGroupByOutputType))]: P extends '_count'
          ? T[P] extends boolean
            ? number
            : Prisma.GetScalarType<T[P], UsuarioRoleGroupByOutputType[P]>
          : Prisma.GetScalarType<T[P], UsuarioRoleGroupByOutputType[P]>
      }
    >
  >



export type UsuarioRoleWhereInput = {
  AND?: Prisma.UsuarioRoleWhereInput | Prisma.UsuarioRoleWhereInput[]
  OR?: Prisma.UsuarioRoleWhereInput[]
  NOT?: Prisma.UsuarioRoleWhereInput | Prisma.UsuarioRoleWhereInput[]
  id?: Prisma.IntFilter<"UsuarioRole"> | number
  usuarioId?: Prisma.IntFilter<"UsuarioRole"> | number
  roleId?: Prisma.IntFilter<"UsuarioRole"> | number
  createdAt?: Prisma.DateTimeFilter<"UsuarioRole"> | Date | string
  usuario?: Prisma.XOR<Prisma.UsuarioScalarRelationFilter, Prisma.UsuarioWhereInput>
  role?: Prisma.XOR<Prisma.RoleScalarRelationFilter, Prisma.RoleWhereInput>
}

export type UsuarioRoleOrderByWithRelationInput = {
  id?: Prisma.SortOrder
  usuarioId?: Prisma.SortOrder
  roleId?: Prisma.SortOrder
  createdAt?: Prisma.SortOrder
  usuario?: Prisma.UsuarioOrderByWithRelationInput
  role?: Prisma.RoleOrderByWithRelationInput
}

export type UsuarioRoleWhereUniqueInput = Prisma.AtLeast<{
  id?: number
  usuarioId_roleId?: Prisma.UsuarioRoleUsuarioIdRoleIdCompoundUniqueInput
  AND?: Prisma.UsuarioRoleWhereInput | Prisma.UsuarioRoleWhereInput[]
  OR?: Prisma.UsuarioRoleWhereInput[]
  NOT?: Prisma.UsuarioRoleWhereInput | Prisma.UsuarioRoleWhereInput[]
  usuarioId?: Prisma.IntFilter<"UsuarioRole"> | number
  roleId?: Prisma.IntFilter<"UsuarioRole"> | number
  createdAt?: Prisma.DateTimeFilter<"UsuarioRole"> | Date | string
  usuario?: Prisma.XOR<Prisma.UsuarioScalarRelationFilter, Prisma.UsuarioWhereInput>
  role?: Prisma.XOR<Prisma.RoleScalarRelationFilter, Prisma.RoleWhereInput>
}, "id" | "usuarioId_roleId">

export type UsuarioRoleOrderByWithAggregationInput = {
  id?: Prisma.SortOrder
  usuarioId?: Prisma.SortOrder
  roleId?: Prisma.SortOrder
  createdAt?: Prisma.SortOrder
  _count?: Prisma.UsuarioRoleCountOrderByAggregateInput
  _avg?: Prisma.UsuarioRoleAvgOrderByAggregateInput
  _max?: Prisma.UsuarioRoleMaxOrderByAggregateInput
  _min?: Prisma.UsuarioRoleMinOrderByAggregateInput
  _sum?: Prisma.UsuarioRoleSumOrderByAggregateInput
}

export type UsuarioRoleScalarWhereWithAggregatesInput = {
  AND?: Prisma.UsuarioRoleScalarWhereWithAggregatesInput | Prisma.UsuarioRoleScalarWhereWithAggregatesInput[]
  OR?: Prisma.UsuarioRoleScalarWhereWithAggregatesInput[]
  NOT?: Prisma.UsuarioRoleScalarWhereWithAggregatesInput | Prisma.UsuarioRoleScalarWhereWithAggregatesInput[]
  id?: Prisma.IntWithAggregatesFilter<"UsuarioRole"> | number
  usuarioId?: Prisma.IntWithAggregatesFilter<"UsuarioRole"> | number
  roleId?: Prisma.IntWithAggregatesFilter<"UsuarioRole"> | number
  createdAt?: Prisma.DateTimeWithAggregatesFilter<"UsuarioRole"> | Date | string
}

export type UsuarioRoleCreateInput = {
  createdAt?: Date | string
  usuario: Prisma.UsuarioCreateNestedOneWithoutRolesInput
  role: Prisma.RoleCreateNestedOneWithoutUsuariosInput
}

export type UsuarioRoleUncheckedCreateInput = {
  id?: number
  usuarioId: number
  roleId: number
  createdAt?: Date | string
}

export type UsuarioRoleUpdateInput = {
  createdAt?: Prisma.DateTimeFieldUpdateOperationsInput | Date | string
  usuario?: Prisma.UsuarioUpdateOneRequiredWithoutRolesNestedInput
  role?: Prisma.RoleUpdateOneRequiredWithoutUsuariosNestedInput
}

export type UsuarioRoleUncheckedUpdateInput = {
  id?: Prisma.IntFieldUpdateOperationsInput | number
  usuarioId?: Prisma.IntFieldUpdateOperationsInput | number
  roleId?: Prisma.IntFieldUpdateOperationsInput | number
  createdAt?: Prisma.DateTimeFieldUpdateOperationsInput | Date | string
}

export type UsuarioRoleCreateManyInput = {
  id?: number
  usuarioId: number
  roleId: number
  createdAt?: Date | string
}

export type UsuarioRoleUpdateManyMutationInput = {
  createdAt?: Prisma.DateTimeFieldUpdateOperationsInput | Date | string
}

export type UsuarioRoleUncheckedUpdateManyInput = {
  id?: Prisma.IntFieldUpdateOperationsInput | number
  usuarioId?: Prisma.IntFieldUpdateOperationsInput | number
  roleId?: Prisma.IntFieldUpdateOperationsInput | number
  createdAt?: Prisma.DateTimeFieldUpdateOperationsInput | Date | string
}

export type UsuarioRoleListRelationFilter = {
  every?: Prisma.UsuarioRoleWhereInput
  some?: Prisma.UsuarioRoleWhereInput
  none?: Prisma.UsuarioRoleWhereInput
}

export type UsuarioRoleOrderByRelationAggregateInput = {
  _count?: Prisma.SortOrder
}

export type UsuarioRoleUsuarioIdRoleIdCompoundUniqueInput = {
  usuarioId: number
  roleId: number
}

export type UsuarioRoleCountOrderByAggregateInput = {
  id?: Prisma.SortOrder
  usuarioId?: Prisma.SortOrder
  roleId?: Prisma.SortOrder
  createdAt?: Prisma.SortOrder
}

export type UsuarioRoleAvgOrderByAggregateInput = {
  id?: Prisma.SortOrder
  usuarioId?: Prisma.SortOrder
  roleId?: Prisma.SortOrder
}

export type UsuarioRoleMaxOrderByAggregateInput = {
  id?: Prisma.SortOrder
  usuarioId?: Prisma.SortOrder
  roleId?: Prisma.SortOrder
  createdAt?: Prisma.SortOrder
}

export type UsuarioRoleMinOrderByAggregateInput = {
  id?: Prisma.SortOrder
  usuarioId?: Prisma.SortOrder
  roleId?: Prisma.SortOrder
  createdAt?: Prisma.SortOrder
}

export type UsuarioRoleSumOrderByAggregateInput = {
  id?: Prisma.SortOrder
  usuarioId?: Prisma.SortOrder
  roleId?: Prisma.SortOrder
}

export type UsuarioRoleCreateNestedManyWithoutUsuarioInput = {
  create?: Prisma.XOR<Prisma.UsuarioRoleCreateWithoutUsuarioInput, Prisma.UsuarioRoleUncheckedCreateWithoutUsuarioInput> | Prisma.UsuarioRoleCreateWithoutUsuarioInput[] | Prisma.UsuarioRoleUncheckedCreateWithoutUsuarioInput[]
  connectOrCreate?: Prisma.UsuarioRoleCreateOrConnectWithoutUsuarioInput | Prisma.UsuarioRoleCreateOrConnectWithoutUsuarioInput[]
  createMany?: Prisma.UsuarioRoleCreateManyUsuarioInputEnvelope
  connect?: Prisma.UsuarioRoleWhereUniqueInput | Prisma.UsuarioRoleWhereUniqueInput[]
}

export type UsuarioRoleUncheckedCreateNestedManyWithoutUsuarioInput = {
  create?: Prisma.XOR<Prisma.UsuarioRoleCreateWithoutUsuarioInput, Prisma.UsuarioRoleUncheckedCreateWithoutUsuarioInput> | Prisma.UsuarioRoleCreateWithoutUsuarioInput[] | Prisma.UsuarioRoleUncheckedCreateWithoutUsuarioInput[]
  connectOrCreate?: Prisma.UsuarioRoleCreateOrConnectWithoutUsuarioInput | Prisma.UsuarioRoleCreateOrConnectWithoutUsuarioInput[]
  createMany?: Prisma.UsuarioRoleCreateManyUsuarioInputEnvelope
  connect?: Prisma.UsuarioRoleWhereUniqueInput | Prisma.UsuarioRoleWhereUniqueInput[]
}

export type UsuarioRoleUpdateManyWithoutUsuarioNestedInput = {
  create?: Prisma.XOR<Prisma.UsuarioRoleCreateWithoutUsuarioInput, Prisma.UsuarioRoleUncheckedCreateWithoutUsuarioInput> | Prisma.UsuarioRoleCreateWithoutUsuarioInput[] | Prisma.UsuarioRoleUncheckedCreateWithoutUsuarioInput[]
  connectOrCreate?: Prisma.UsuarioRoleCreateOrConnectWithoutUsuarioInput | Prisma.UsuarioRoleCreateOrConnectWithoutUsuarioInput[]
  upsert?: Prisma.UsuarioRoleUpsertWithWhereUniqueWithoutUsuarioInput | Prisma.UsuarioRoleUpsertWithWhereUniqueWithoutUsuarioInput[]
  createMany?: Prisma.UsuarioRoleCreateManyUsuarioInputEnvelope
  set?: Prisma.UsuarioRoleWhereUniqueInput | Prisma.UsuarioRoleWhereUniqueInput[]
  disconnect?: Prisma.UsuarioRoleWhereUniqueInput | Prisma.UsuarioRoleWhereUniqueInput[]
  delete?: Prisma.UsuarioRoleWhereUniqueInput | Prisma.UsuarioRoleWhereUniqueInput[]
  connect?: Prisma.UsuarioRoleWhereUniqueInput | Prisma.UsuarioRoleWhereUniqueInput[]
  update?: Prisma.UsuarioRoleUpdateWithWhereUniqueWithoutUsuarioInput | Prisma.UsuarioRoleUpdateWithWhereUniqueWithoutUsuarioInput[]
  updateMany?: Prisma.UsuarioRoleUpdateManyWithWhereWithoutUsuarioInput | Prisma.UsuarioRoleUpdateManyWithWhereWithoutUsuarioInput[]
  deleteMany?: Prisma.UsuarioRoleScalarWhereInput | Prisma.UsuarioRoleScalarWhereInput[]
}

export type UsuarioRoleUncheckedUpdateManyWithoutUsuarioNestedInput = {
  create?: Prisma.XOR<Prisma.UsuarioRoleCreateWithoutUsuarioInput, Prisma.UsuarioRoleUncheckedCreateWithoutUsuarioInput> | Prisma.UsuarioRoleCreateWithoutUsuarioInput[] | Prisma.UsuarioRoleUncheckedCreateWithoutUsuarioInput[]
  connectOrCreate?: Prisma.UsuarioRoleCreateOrConnectWithoutUsuarioInput | Prisma.UsuarioRoleCreateOrConnectWithoutUsuarioInput[]
  upsert?: Prisma.UsuarioRoleUpsertWithWhereUniqueWithoutUsuarioInput | Prisma.UsuarioRoleUpsertWithWhereUniqueWithoutUsuarioInput[]
  createMany?: Prisma.UsuarioRoleCreateManyUsuarioInputEnvelope
  set?: Prisma.UsuarioRoleWhereUniqueInput | Prisma.UsuarioRoleWhereUniqueInput[]
  disconnect?: Prisma.UsuarioRoleWhereUniqueInput | Prisma.UsuarioRoleWhereUniqueInput[]
  delete?: Prisma.UsuarioRoleWhereUniqueInput | Prisma.UsuarioRoleWhereUniqueInput[]
  connect?: Prisma.UsuarioRoleWhereUniqueInput | Prisma.UsuarioRoleWhereUniqueInput[]
  update?: Prisma.UsuarioRoleUpdateWithWhereUniqueWithoutUsuarioInput | Prisma.UsuarioRoleUpdateWithWhereUniqueWithoutUsuarioInput[]
  updateMany?: Prisma.UsuarioRoleUpdateManyWithWhereWithoutUsuarioInput | Prisma.UsuarioRoleUpdateManyWithWhereWithoutUsuarioInput[]
  deleteMany?: Prisma.UsuarioRoleScalarWhereInput | Prisma.UsuarioRoleScalarWhereInput[]
}

export type UsuarioRoleCreateNestedManyWithoutRoleInput = {
  create?: Prisma.XOR<Prisma.UsuarioRoleCreateWithoutRoleInput, Prisma.UsuarioRoleUncheckedCreateWithoutRoleInput> | Prisma.UsuarioRoleCreateWithoutRoleInput[] | Prisma.UsuarioRoleUncheckedCreateWithoutRoleInput[]
  connectOrCreate?: Prisma.UsuarioRoleCreateOrConnectWithoutRoleInput | Prisma.UsuarioRoleCreateOrConnectWithoutRoleInput[]
  createMany?: Prisma.UsuarioRoleCreateManyRoleInputEnvelope
  connect?: Prisma.UsuarioRoleWhereUniqueInput | Prisma.UsuarioRoleWhereUniqueInput[]
}

export type UsuarioRoleUncheckedCreateNestedManyWithoutRoleInput = {
  create?: Prisma.XOR<Prisma.UsuarioRoleCreateWithoutRoleInput, Prisma.UsuarioRoleUncheckedCreateWithoutRoleInput> | Prisma.UsuarioRoleCreateWithoutRoleInput[] | Prisma.UsuarioRoleUncheckedCreateWithoutRoleInput[]
  connectOrCreate?: Prisma.UsuarioRoleCreateOrConnectWithoutRoleInput | Prisma.UsuarioRoleCreateOrConnectWithoutRoleInput[]
  createMany?: Prisma.UsuarioRoleCreateManyRoleInputEnvelope
  connect?: Prisma.UsuarioRoleWhereUniqueInput | Prisma.UsuarioRoleWhereUniqueInput[]
}

export type UsuarioRoleUpdateManyWithoutRoleNestedInput = {
  create?: Prisma.XOR<Prisma.UsuarioRoleCreateWithoutRoleInput, Prisma.UsuarioRoleUncheckedCreateWithoutRoleInput> | Prisma.UsuarioRoleCreateWithoutRoleInput[] | Prisma.UsuarioRoleUncheckedCreateWithoutRoleInput[]
  connectOrCreate?: Prisma.UsuarioRoleCreateOrConnectWithoutRoleInput | Prisma.UsuarioRoleCreateOrConnectWithoutRoleInput[]
  upsert?: Prisma.UsuarioRoleUpsertWithWhereUniqueWithoutRoleInput | Prisma.UsuarioRoleUpsertWithWhereUniqueWithoutRoleInput[]
  createMany?: Prisma.UsuarioRoleCreateManyRoleInputEnvelope
  set?: Prisma.UsuarioRoleWhereUniqueInput | Prisma.UsuarioRoleWhereUniqueInput[]
  disconnect?: Prisma.UsuarioRoleWhereUniqueInput | Prisma.UsuarioRoleWhereUniqueInput[]
  delete?: Prisma.UsuarioRoleWhereUniqueInput | Prisma.UsuarioRoleWhereUniqueInput[]
  connect?: Prisma.UsuarioRoleWhereUniqueInput | Prisma.UsuarioRoleWhereUniqueInput[]
  update?: Prisma.UsuarioRoleUpdateWithWhereUniqueWithoutRoleInput | Prisma.UsuarioRoleUpdateWithWhereUniqueWithoutRoleInput[]
  updateMany?: Prisma.UsuarioRoleUpdateManyWithWhereWithoutRoleInput | Prisma.UsuarioRoleUpdateManyWithWhereWithoutRoleInput[]
  deleteMany?: Prisma.UsuarioRoleScalarWhereInput | Prisma.UsuarioRoleScalarWhereInput[]
}

export type UsuarioRoleUncheckedUpdateManyWithoutRoleNestedInput = {
  create?: Prisma.XOR<Prisma.UsuarioRoleCreateWithoutRoleInput, Prisma.UsuarioRoleUncheckedCreateWithoutRoleInput> | Prisma.UsuarioRoleCreateWithoutRoleInput[] | Prisma.UsuarioRoleUncheckedCreateWithoutRoleInput[]
  connectOrCreate?: Prisma.UsuarioRoleCreateOrConnectWithoutRoleInput | Prisma.UsuarioRoleCreateOrConnectWithoutRoleInput[]
  upsert?: Prisma.UsuarioRoleUpsertWithWhereUniqueWithoutRoleInput | Prisma.UsuarioRoleUpsertWithWhereUniqueWithoutRoleInput[]
  createMany?: Prisma.UsuarioRoleCreateManyRoleInputEnvelope
  set?: Prisma.UsuarioRoleWhereUniqueInput | Prisma.UsuarioRoleWhereUniqueInput[]
  disconnect?: Prisma.UsuarioRoleWhereUniqueInput | Prisma.UsuarioRoleWhereUniqueInput[]
  delete?: Prisma.UsuarioRoleWhereUniqueInput | Prisma.UsuarioRoleWhereUniqueInput[]
  connect?: Prisma.UsuarioRoleWhereUniqueInput | Prisma.UsuarioRoleWhereUniqueInput[]
  update?: Prisma.UsuarioRoleUpdateWithWhereUniqueWithoutRoleInput | Prisma.UsuarioRoleUpdateWithWhereUniqueWithoutRoleInput[]
  updateMany?: Prisma.UsuarioRoleUpdateManyWithWhereWithoutRoleInput | Prisma.UsuarioRoleUpdateManyWithWhereWithoutRoleInput[]
  deleteMany?: Prisma.UsuarioRoleScalarWhereInput | Prisma.UsuarioRoleScalarWhereInput[]
}

export type UsuarioRoleCreateWithoutUsuarioInput = {
  createdAt?: Date | string
  role: Prisma.RoleCreateNestedOneWithoutUsuariosInput
}

export type UsuarioRoleUncheckedCreateWithoutUsuarioInput = {
  id?: number
  roleId: number
  createdAt?: Date | string
}

export type UsuarioRoleCreateOrConnectWithoutUsuarioInput = {
  where: Prisma.UsuarioRoleWhereUniqueInput
  create: Prisma.XOR<Prisma.UsuarioRoleCreateWithoutUsuarioInput, Prisma.UsuarioRoleUncheckedCreateWithoutUsuarioInput>
}

export type UsuarioRoleCreateManyUsuarioInputEnvelope = {
  data: Prisma.UsuarioRoleCreateManyUsuarioInput | Prisma.UsuarioRoleCreateManyUsuarioInput[]
  skipDuplicates?: boolean
}

export type UsuarioRoleUpsertWithWhereUniqueWithoutUsuarioInput = {
  where: Prisma.UsuarioRoleWhereUniqueInput
  update: Prisma.XOR<Prisma.UsuarioRoleUpdateWithoutUsuarioInput, Prisma.UsuarioRoleUncheckedUpdateWithoutUsuarioInput>
  create: Prisma.XOR<Prisma.UsuarioRoleCreateWithoutUsuarioInput, Prisma.UsuarioRoleUncheckedCreateWithoutUsuarioInput>
}

export type UsuarioRoleUpdateWithWhereUniqueWithoutUsuarioInput = {
  where: Prisma.UsuarioRoleWhereUniqueInput
  data: Prisma.XOR<Prisma.UsuarioRoleUpdateWithoutUsuarioInput, Prisma.UsuarioRoleUncheckedUpdateWithoutUsuarioInput>
}

export type UsuarioRoleUpdateManyWithWhereWithoutUsuarioInput = {
  where: Prisma.UsuarioRoleScalarWhereInput
  data: Prisma.XOR<Prisma.UsuarioRoleUpdateManyMutationInput, Prisma.UsuarioRoleUncheckedUpdateManyWithoutUsuarioInput>
}

export type UsuarioRoleScalarWhereInput = {
  AND?: Prisma.UsuarioRoleScalarWhereInput | Prisma.UsuarioRoleScalarWhereInput[]
  OR?: Prisma.UsuarioRoleScalarWhereInput[]
  NOT?: Prisma.UsuarioRoleScalarWhereInput | Prisma.UsuarioRoleScalarWhereInput[]
  id?: Prisma.IntFilter<"UsuarioRole"> | number
  usuarioId?: Prisma.IntFilter<"UsuarioRole"> | number
  roleId?: Prisma.IntFilter<"UsuarioRole"> | number
  createdAt?: Prisma.DateTimeFilter<"UsuarioRole"> | Date | string
}

export type UsuarioRoleCreateWithoutRoleInput = {
  createdAt?: Date | string
  usuario: Prisma.UsuarioCreateNestedOneWithoutRolesInput
}

export type UsuarioRoleUncheckedCreateWithoutRoleInput = {
  id?: number
  usuarioId: number
  createdAt?: Date | string
}

export type UsuarioRoleCreateOrConnectWithoutRoleInput = {
  where: Prisma.UsuarioRoleWhereUniqueInput
  create: Prisma.XOR<Prisma.UsuarioRoleCreateWithoutRoleInput, Prisma.UsuarioRoleUncheckedCreateWithoutRoleInput>
}

export type UsuarioRoleCreateManyRoleInputEnvelope = {
  data: Prisma.UsuarioRoleCreateManyRoleInput | Prisma.UsuarioRoleCreateManyRoleInput[]
  skipDuplicates?: boolean
}

export type UsuarioRoleUpsertWithWhereUniqueWithoutRoleInput = {
  where: Prisma.UsuarioRoleWhereUniqueInput
  update: Prisma.XOR<Prisma.UsuarioRoleUpdateWithoutRoleInput, Prisma.UsuarioRoleUncheckedUpdateWithoutRoleInput>
  create: Prisma.XOR<Prisma.UsuarioRoleCreateWithoutRoleInput, Prisma.UsuarioRoleUncheckedCreateWithoutRoleInput>
}

export type UsuarioRoleUpdateWithWhereUniqueWithoutRoleInput = {
  where: Prisma.UsuarioRoleWhereUniqueInput
  data: Prisma.XOR<Prisma.UsuarioRoleUpdateWithoutRoleInput, Prisma.UsuarioRoleUncheckedUpdateWithoutRoleInput>
}

export type UsuarioRoleUpdateManyWithWhereWithoutRoleInput = {
  where: Prisma.UsuarioRoleScalarWhereInput
  data: Prisma.XOR<Prisma.UsuarioRoleUpdateManyMutationInput, Prisma.UsuarioRoleUncheckedUpdateManyWithoutRoleInput>
}

export type UsuarioRoleCreateManyUsuarioInput = {
  id?: number
  roleId: number
  createdAt?: Date | string
}

export type UsuarioRoleUpdateWithoutUsuarioInput = {
  createdAt?: Prisma.DateTimeFieldUpdateOperationsInput | Date | string
  role?: Prisma.RoleUpdateOneRequiredWithoutUsuariosNestedInput
}

export type UsuarioRoleUncheckedUpdateWithoutUsuarioInput = {
  id?: Prisma.IntFieldUpdateOperationsInput | number
  roleId?: Prisma.IntFieldUpdateOperationsInput | number
  createdAt?: Prisma.DateTimeFieldUpdateOperationsInput | Date | string
}

export type UsuarioRoleUncheckedUpdateManyWithoutUsuarioInput = {
  id?: Prisma.IntFieldUpdateOperationsInput | number
  roleId?: Prisma.IntFieldUpdateOperationsInput | number
  createdAt?: Prisma.DateTimeFieldUpdateOperationsInput | Date | string
}

export type UsuarioRoleCreateManyRoleInput = {
  id?: number
  usuarioId: number
  createdAt?: Date | string
}

export type UsuarioRoleUpdateWithoutRoleInput = {
  createdAt?: Prisma.DateTimeFieldUpdateOperationsInput | Date | string
  usuario?: Prisma.UsuarioUpdateOneRequiredWithoutRolesNestedInput
}

export type UsuarioRoleUncheckedUpdateWithoutRoleInput = {
  id?: Prisma.IntFieldUpdateOperationsInput | number
  usuarioId?: Prisma.IntFieldUpdateOperationsInput | number
  createdAt?: Prisma.DateTimeFieldUpdateOperationsInput | Date | string
}

export type UsuarioRoleUncheckedUpdateManyWithoutRoleInput = {
  id?: Prisma.IntFieldUpdateOperationsInput | number
  usuarioId?: Prisma.IntFieldUpdateOperationsInput | number
  createdAt?: Prisma.DateTimeFieldUpdateOperationsInput | Date | string
}



export type UsuarioRoleSelect<ExtArgs extends runtime.Types.Extensions.InternalArgs = runtime.Types.Extensions.DefaultArgs> = runtime.Types.Extensions.GetSelect<{
  id?: boolean
  usuarioId?: boolean
  roleId?: boolean
  createdAt?: boolean
  usuario?: boolean | Prisma.UsuarioDefaultArgs<ExtArgs>
  role?: boolean | Prisma.RoleDefaultArgs<ExtArgs>
}, ExtArgs["result"]["usuarioRole"]>



export type UsuarioRoleSelectScalar = {
  id?: boolean
  usuarioId?: boolean
  roleId?: boolean
  createdAt?: boolean
}

export type UsuarioRoleOmit<ExtArgs extends runtime.Types.Extensions.InternalArgs = runtime.Types.Extensions.DefaultArgs> = runtime.Types.Extensions.GetOmit<"id" | "usuarioId" | "roleId" | "createdAt", ExtArgs["result"]["usuarioRole"]>
export type UsuarioRoleInclude<ExtArgs extends runtime.Types.Extensions.InternalArgs = runtime.Types.Extensions.DefaultArgs> = {
  usuario?: boolean | Prisma.UsuarioDefaultArgs<ExtArgs>
  role?: boolean | Prisma.RoleDefaultArgs<ExtArgs>
}

export type $UsuarioRolePayload<ExtArgs extends runtime.Types.Extensions.InternalArgs = runtime.Types.Extensions.DefaultArgs> = {
  name: "UsuarioRole"
  objects: {
    usuario: Prisma.$UsuarioPayload<ExtArgs>
    role: Prisma.$RolePayload<ExtArgs>
  }
  scalars: runtime.Types.Extensions.GetPayloadResult<{
    id: number
    usuarioId: number
    roleId: number
    createdAt: Date
  }, ExtArgs["result"]["usuarioRole"]>
  composites: {}
}

export type UsuarioRoleGetPayload<S extends boolean | null | undefined | UsuarioRoleDefaultArgs> = runtime.Types.Result.GetResult<Prisma.$UsuarioRolePayload, S>

export type UsuarioRoleCountArgs<ExtArgs extends runtime.Types.Extensions.InternalArgs = runtime.Types.Extensions.DefaultArgs> =
  Omit<UsuarioRoleFindManyArgs, 'select' | 'include' | 'distinct' | 'omit'> & {
    select?: UsuarioRoleCountAggregateInputType | true
  }

export interface UsuarioRoleDelegate<ExtArgs extends runtime.Types.Extensions.InternalArgs = runtime.Types.Extensions.DefaultArgs, GlobalOmitOptions = {}> {
  [K: symbol]: { types: Prisma.TypeMap<ExtArgs>['model']['UsuarioRole'], meta: { name: 'UsuarioRole' } }
  /**
   * Find zero or one UsuarioRole that matches the filter.
   * @param {UsuarioRoleFindUniqueArgs} args - Arguments to find a UsuarioRole
   * @example
   * // Get one UsuarioRole
   * const usuarioRole = await prisma.usuarioRole.findUnique({
   *   where: {
   *     // ... provide filter here
   *   }
   * })
   */
  findUnique<T extends UsuarioRoleFindUniqueArgs>(args: Prisma.SelectSubset<T, UsuarioRoleFindUniqueArgs<ExtArgs>>): Prisma.Prisma__UsuarioRoleClient<runtime.Types.Result.GetResult<Prisma.$UsuarioRolePayload<ExtArgs>, T, "findUnique", GlobalOmitOptions> | null, null, ExtArgs, GlobalOmitOptions>

  /**
   * Find one UsuarioRole that matches the filter or throw an error with `error.code='P2025'`
   * if no matches were found.
   * @param {UsuarioRoleFindUniqueOrThrowArgs} args - Arguments to find a UsuarioRole
   * @example
   * // Get one UsuarioRole
   * const usuarioRole = await prisma.usuarioRole.findUniqueOrThrow({
   *   where: {
   *     // ... provide filter here
   *   }
   * })
   */
  findUniqueOrThrow<T extends UsuarioRoleFindUniqueOrThrowArgs>(args: Prisma.SelectSubset<T, UsuarioRoleFindUniqueOrThrowArgs<ExtArgs>>): Prisma.Prisma__UsuarioRoleClient<runtime.Types.Result.GetResult<Prisma.$UsuarioRolePayload<ExtArgs>, T, "findUniqueOrThrow", GlobalOmitOptions>, never, ExtArgs, GlobalOmitOptions>

  /**
   * Find the first UsuarioRole that matches the filter.
   * Note, that providing `undefined` is treated as the value not being there.
   * Read more here: https://pris.ly/d/null-undefined
   * @param {UsuarioRoleFindFirstArgs} args - Arguments to find a UsuarioRole
   * @example
   * // Get one UsuarioRole
   * const usuarioRole = await prisma.usuarioRole.findFirst({
   *   where: {
   *     // ... provide filter here
   *   }
   * })
   */
  findFirst<T extends UsuarioRoleFindFirstArgs>(args?: Prisma.SelectSubset<T, UsuarioRoleFindFirstArgs<ExtArgs>>): Prisma.Prisma__UsuarioRoleClient<runtime.Types.Result.GetResult<Prisma.$UsuarioRolePayload<ExtArgs>, T, "findFirst", GlobalOmitOptions> | null, null, ExtArgs, GlobalOmitOptions>

  /**
   * Find the first UsuarioRole that matches the filter or
   * throw `PrismaKnownClientError` with `P2025` code if no matches were found.
   * Note, that providing `undefined` is treated as the value not being there.
   * Read more here: https://pris.ly/d/null-undefined
   * @param {UsuarioRoleFindFirstOrThrowArgs} args - Arguments to find a UsuarioRole
   * @example
   * // Get one UsuarioRole
   * const usuarioRole = await prisma.usuarioRole.findFirstOrThrow({
   *   where: {
   *     // ... provide filter here
   *   }
   * })
   */
  findFirstOrThrow<T extends UsuarioRoleFindFirstOrThrowArgs>(args?: Prisma.SelectSubset<T, UsuarioRoleFindFirstOrThrowArgs<ExtArgs>>): Prisma.Prisma__UsuarioRoleClient<runtime.Types.Result.GetResult<Prisma.$UsuarioRolePayload<ExtArgs>, T, "findFirstOrThrow", GlobalOmitOptions>, never, ExtArgs, GlobalOmitOptions>

  /**
   * Find zero or more UsuarioRoles that matches the filter.
   * Note, that providing `undefined` is treated as the value not being there.
   * Read more here: https://pris.ly/d/null-undefined
   * @param {UsuarioRoleFindManyArgs} args - Arguments to filter and select certain fields only.
   * @example
   * // Get all UsuarioRoles
   * const usuarioRoles = await prisma.usuarioRole.findMany()
   * 
   * // Get first 10 UsuarioRoles
   * const usuarioRoles = await prisma.usuarioRole.findMany({ take: 10 })
   * 
   * // Only select the `id`
   * const usuarioRoleWithIdOnly = await prisma.usuarioRole.findMany({ select: { id: true } })
   * 
   */
  findMany<T extends UsuarioRoleFindManyArgs>(args?: Prisma.SelectSubset<T, UsuarioRoleFindManyArgs<ExtArgs>>): Prisma.PrismaPromise<runtime.Types.Result.GetResult<Prisma.$UsuarioRolePayload<ExtArgs>, T, "findMany", GlobalOmitOptions>>

  /**
   * Create a UsuarioRole.
   * @param {UsuarioRoleCreateArgs} args - Arguments to create a UsuarioRole.
   * @example
   * // Create one UsuarioRole
   * const UsuarioRole = await prisma.usuarioRole.create({
   *   data: {
   *     // ... data to create a UsuarioRole
   *   }
   * })
   * 
   */
  create<T extends UsuarioRoleCreateArgs>(args: Prisma.SelectSubset<T, UsuarioRoleCreateArgs<ExtArgs>>): Prisma.Prisma__UsuarioRoleClient<runtime.Types.Result.GetResult<Prisma.$UsuarioRolePayload<ExtArgs>, T, "create", GlobalOmitOptions>, never, ExtArgs, GlobalOmitOptions>

  /**
   * Create many UsuarioRoles.
   * @param {UsuarioRoleCreateManyArgs} args - Arguments to create many UsuarioRoles.
   * @example
   * // Create many UsuarioRoles
   * const usuarioRole = await prisma.usuarioRole.createMany({
   *   data: [
   *     // ... provide data here
   *   ]
   * })
   *     
   */
  createMany<T extends UsuarioRoleCreateManyArgs>(args?: Prisma.SelectSubset<T, UsuarioRoleCreateManyArgs<ExtArgs>>): Prisma.PrismaPromise<Prisma.BatchPayload>

  /**
   * Delete a UsuarioRole.
   * @param {UsuarioRoleDeleteArgs} args - Arguments to delete one UsuarioRole.
   * @example
   * // Delete one UsuarioRole
   * const UsuarioRole = await prisma.usuarioRole.delete({
   *   where: {
   *     // ... filter to delete one UsuarioRole
   *   }
   * })
   * 
   */
  delete<T extends UsuarioRoleDeleteArgs>(args: Prisma.SelectSubset<T, UsuarioRoleDeleteArgs<ExtArgs>>): Prisma.Prisma__UsuarioRoleClient<runtime.Types.Result.GetResult<Prisma.$UsuarioRolePayload<ExtArgs>, T, "delete", GlobalOmitOptions>, never, ExtArgs, GlobalOmitOptions>

  /**
   * Update one UsuarioRole.
   * @param {UsuarioRoleUpdateArgs} args - Arguments to update one UsuarioRole.
   * @example
   * // Update one UsuarioRole
   * const usuarioRole = await prisma.usuarioRole.update({
   *   where: {
   *     // ... provide filter here
   *   },
   *   data: {
   *     // ... provide data here
   *   }
   * })
   * 
   */
  update<T extends UsuarioRoleUpdateArgs>(args: Prisma.SelectSubset<T, UsuarioRoleUpdateArgs<ExtArgs>>): Prisma.Prisma__UsuarioRoleClient<runtime.Types.Result.GetResult<Prisma.$UsuarioRolePayload<ExtArgs>, T, "update", GlobalOmitOptions>, never, ExtArgs, GlobalOmitOptions>

  /**
   * Delete zero or more UsuarioRoles.
   * @param {UsuarioRoleDeleteManyArgs} args - Arguments to filter UsuarioRoles to delete.
   * @example
   * // Delete a few UsuarioRoles
   * const { count } = await prisma.usuarioRole.deleteMany({
   *   where: {
   *     // ... provide filter here
   *   }
   * })
   * 
   */
  deleteMany<T extends UsuarioRoleDeleteManyArgs>(args?: Prisma.SelectSubset<T, UsuarioRoleDeleteManyArgs<ExtArgs>>): Prisma.PrismaPromise<Prisma.BatchPayload>

  /**
   * Update zero or more UsuarioRoles.
   * Note, that providing `undefined` is treated as the value not being there.
   * Read more here: https://pris.ly/d/null-undefined
   * @param {UsuarioRoleUpdateManyArgs} args - Arguments to update one or more rows.
   * @example
   * // Update many UsuarioRoles
   * const usuarioRole = await prisma.usuarioRole.updateMany({
   *   where: {
   *     // ... provide filter here
   *   },
   *   data: {
   *     // ... provide data here
   *   }
   * })
   * 
   */
  updateMany<T extends UsuarioRoleUpdateManyArgs>(args: Prisma.SelectSubset<T, UsuarioRoleUpdateManyArgs<ExtArgs>>): Prisma.PrismaPromise<Prisma.BatchPayload>

  /**
   * Create or update one UsuarioRole.
   * @param {UsuarioRoleUpsertArgs} args - Arguments to update or create a UsuarioRole.
   * @example
   * // Update or create a UsuarioRole
   * const usuarioRole = await prisma.usuarioRole.upsert({
   *   create: {
   *     // ... data to create a UsuarioRole
   *   },
   *   update: {
   *     // ... in case it already exists, update
   *   },
   *   where: {
   *     // ... the filter for the UsuarioRole we want to update
   *   }
   * })
   */
  upsert<T extends UsuarioRoleUpsertArgs>(args: Prisma.SelectSubset<T, UsuarioRoleUpsertArgs<ExtArgs>>): Prisma.Prisma__UsuarioRoleClient<runtime.Types.Result.GetResult<Prisma.$UsuarioRolePayload<ExtArgs>, T, "upsert", GlobalOmitOptions>, never, ExtArgs, GlobalOmitOptions>


  /**
   * Count the number of UsuarioRoles.
   * Note, that providing `undefined` is treated as the value not being there.
   * Read more here: https://pris.ly/d/null-undefined
   * @param {UsuarioRoleCountArgs} args - Arguments to filter UsuarioRoles to count.
   * @example
   * // Count the number of UsuarioRoles
   * const count = await prisma.usuarioRole.count({
   *   where: {
   *     // ... the filter for the UsuarioRoles we want to count
   *   }
   * })
  **/
  count<T extends UsuarioRoleCountArgs>(
    args?: Prisma.Subset<T, UsuarioRoleCountArgs>,
  ): Prisma.PrismaPromise<
    T extends runtime.Types.Utils.Record<'select', any>
      ? T['select'] extends true
        ? number
        : Prisma.GetScalarType<T['select'], UsuarioRoleCountAggregateOutputType>
      : number
  >

  /**
   * Allows you to perform aggregations operations on a UsuarioRole.
   * Note, that providing `undefined` is treated as the value not being there.
   * Read more here: https://pris.ly/d/null-undefined
   * @param {UsuarioRoleAggregateArgs} args - Select which aggregations you would like to apply and on what fields.
   * @example
   * // Ordered by age ascending
   * // Where email contains prisma.io
   * // Limited to the 10 users
   * const aggregations = await prisma.user.aggregate({
   *   _avg: {
   *     age: true,
   *   },
   *   where: {
   *     email: {
   *       contains: "prisma.io",
   *     },
   *   },
   *   orderBy: {
   *     age: "asc",
   *   },
   *   take: 10,
   * })
  **/
  aggregate<T extends UsuarioRoleAggregateArgs>(args: Prisma.Subset<T, UsuarioRoleAggregateArgs>): Prisma.PrismaPromise<GetUsuarioRoleAggregateType<T>>

  /**
   * Group by UsuarioRole.
   * Note, that providing `undefined` is treated as the value not being there.
   * Read more here: https://pris.ly/d/null-undefined
   * @param {UsuarioRoleGroupByArgs} args - Group by arguments.
   * @example
   * // Group by city, order by createdAt, get count
   * const result = await prisma.user.groupBy({
   *   by: ['city', 'createdAt'],
   *   orderBy: {
   *     createdAt: true
   *   },
   *   _count: {
   *     _all: true
   *   },
   * })
   * 
  **/
  groupBy<
    T extends UsuarioRoleGroupByArgs,
    HasSelectOrTake extends Prisma.Or<
      Prisma.Extends<'skip', Prisma.Keys<T>>,
      Prisma.Extends<'take', Prisma.Keys<T>>
    >,
    OrderByArg extends Prisma.True extends HasSelectOrTake
      ? { orderBy: UsuarioRoleGroupByArgs['orderBy'] }
      : { orderBy?: UsuarioRoleGroupByArgs['orderBy'] },
    OrderFields extends Prisma.ExcludeUnderscoreKeys<Prisma.Keys<Prisma.MaybeTupleToUnion<T['orderBy']>>>,
    ByFields extends Prisma.MaybeTupleToUnion<T['by']>,
    ByValid extends Prisma.Has<ByFields, OrderFields>,
    HavingFields extends Prisma.GetHavingFields<T['having']>,
    HavingValid extends Prisma.Has<ByFields, HavingFields>,
    ByEmpty extends T['by'] extends never[] ? Prisma.True : Prisma.False,
    InputErrors extends ByEmpty extends Prisma.True
    ? `Error: "by" must not be empty.`
    : HavingValid extends Prisma.False
    ? {
        [P in HavingFields]: P extends ByFields
          ? never
          : P extends string
          ? `Error: Field "${P}" used in "having" needs to be provided in "by".`
          : [
              Error,
              'Field ',
              P,
              ` in "having" needs to be provided in "by"`,
            ]
      }[HavingFields]
    : 'take' extends Prisma.Keys<T>
    ? 'orderBy' extends Prisma.Keys<T>
      ? ByValid extends Prisma.True
        ? {}
        : {
            [P in OrderFields]: P extends ByFields
              ? never
              : `Error: Field "${P}" in "orderBy" needs to be provided in "by"`
          }[OrderFields]
      : 'Error: If you provide "take", you also need to provide "orderBy"'
    : 'skip' extends Prisma.Keys<T>
    ? 'orderBy' extends Prisma.Keys<T>
      ? ByValid extends Prisma.True
        ? {}
        : {
            [P in OrderFields]: P extends ByFields
              ? never
              : `Error: Field "${P}" in "orderBy" needs to be provided in "by"`
          }[OrderFields]
      : 'Error: If you provide "skip", you also need to provide "orderBy"'
    : ByValid extends Prisma.True
    ? {}
    : {
        [P in OrderFields]: P extends ByFields
          ? never
          : `Error: Field "${P}" in "orderBy" needs to be provided in "by"`
      }[OrderFields]
  >(args: Prisma.SubsetIntersection<T, UsuarioRoleGroupByArgs, OrderByArg> & InputErrors): {} extends InputErrors ? GetUsuarioRoleGroupByPayload<T> : Prisma.PrismaPromise<InputErrors>
/**
 * Fields of the UsuarioRole model
 */
readonly fields: UsuarioRoleFieldRefs;
}

/**
 * The delegate class that acts as a "Promise-like" for UsuarioRole.
 * Why is this prefixed with `Prisma__`?
 * Because we want to prevent naming conflicts as mentioned in
 * https://github.com/prisma/prisma-client-js/issues/707
 */
export interface Prisma__UsuarioRoleClient<T, Null = never, ExtArgs extends runtime.Types.Extensions.InternalArgs = runtime.Types.Extensions.DefaultArgs, GlobalOmitOptions = {}> extends Prisma.PrismaPromise<T> {
  readonly [Symbol.toStringTag]: "PrismaPromise"
  usuario<T extends Prisma.UsuarioDefaultArgs<ExtArgs> = {}>(args?: Prisma.Subset<T, Prisma.UsuarioDefaultArgs<ExtArgs>>): Prisma.Prisma__UsuarioClient<runtime.Types.Result.GetResult<Prisma.$UsuarioPayload<ExtArgs>, T, "findUniqueOrThrow", GlobalOmitOptions> | Null, Null, ExtArgs, GlobalOmitOptions>
  role<T extends Prisma.RoleDefaultArgs<ExtArgs> = {}>(args?: Prisma.Subset<T, Prisma.RoleDefaultArgs<ExtArgs>>): Prisma.Prisma__RoleClient<runtime.Types.Result.GetResult<Prisma.$RolePayload<ExtArgs>, T, "findUniqueOrThrow", GlobalOmitOptions> | Null, Null, ExtArgs, GlobalOmitOptions>
  /**
   * Attaches callbacks for the resolution and/or rejection of the Promise.
   * @param onfulfilled The callback to execute when the Promise is resolved.
   * @param onrejected The callback to execute when the Promise is rejected.
   * @returns A Promise for the completion of which ever callback is executed.
   */
  then<TResult1 = T, TResult2 = never>(onfulfilled?: ((value: T) => TResult1 | PromiseLike<TResult1>) | undefined | null, onrejected?: ((reason: any) => TResult2 | PromiseLike<TResult2>) | undefined | null): runtime.Types.Utils.JsPromise<TResult1 | TResult2>
  /**
   * Attaches a callback for only the rejection of the Promise.
   * @param onrejected The callback to execute when the Promise is rejected.
   * @returns A Promise for the completion of the callback.
   */
  catch<TResult = never>(onrejected?: ((reason: any) => TResult | PromiseLike<TResult>) | undefined | null): runtime.Types.Utils.JsPromise<T | TResult>
  /**
   * Attaches a callback that is invoked when the Promise is settled (fulfilled or rejected). The
   * resolved value cannot be modified from the callback.
   * @param onfinally The callback to execute when the Promise is settled (fulfilled or rejected).
   * @returns A Promise for the completion of the callback.
   */
  finally(onfinally?: (() => void) | undefined | null): runtime.Types.Utils.JsPromise<T>
}




/**
 * Fields of the UsuarioRole model
 */
export interface UsuarioRoleFieldRefs {
  readonly id: Prisma.FieldRef<"UsuarioRole", 'Int'>
  readonly usuarioId: Prisma.FieldRef<"UsuarioRole", 'Int'>
  readonly roleId: Prisma.FieldRef<"UsuarioRole", 'Int'>
  readonly createdAt: Prisma.FieldRef<"UsuarioRole", 'DateTime'>
}
    

// Custom InputTypes
/**
 * UsuarioRole findUnique
 */
export type UsuarioRoleFindUniqueArgs<ExtArgs extends runtime.Types.Extensions.InternalArgs = runtime.Types.Extensions.DefaultArgs> = {
  /**
   * Select specific fields to fetch from the UsuarioRole
   */
  select?: Prisma.UsuarioRoleSelect<ExtArgs> | null
  /**
   * Omit specific fields from the UsuarioRole
   */
  omit?: Prisma.UsuarioRoleOmit<ExtArgs> | null
  /**
   * Choose, which related nodes to fetch as well
   */
  include?: Prisma.UsuarioRoleInclude<ExtArgs> | null
  /**
   * Filter, which UsuarioRole to fetch.
   */
  where: Prisma.UsuarioRoleWhereUniqueInput
}

/**
 * UsuarioRole findUniqueOrThrow
 */
export type UsuarioRoleFindUniqueOrThrowArgs<ExtArgs extends runtime.Types.Extensions.InternalArgs = runtime.Types.Extensions.DefaultArgs> = {
  /**
   * Select specific fields to fetch from the UsuarioRole
   */
  select?: Prisma.UsuarioRoleSelect<ExtArgs> | null
  /**
   * Omit specific fields from the UsuarioRole
   */
  omit?: Prisma.UsuarioRoleOmit<ExtArgs> | null
  /**
   * Choose, which related nodes to fetch as well
   */
  include?: Prisma.UsuarioRoleInclude<ExtArgs> | null
  /**
   * Filter, which UsuarioRole to fetch.
   */
  where: Prisma.UsuarioRoleWhereUniqueInput
}

/**
 * UsuarioRole findFirst
 */
export type UsuarioRoleFindFirstArgs<ExtArgs extends runtime.Types.Extensions.InternalArgs = runtime.Types.Extensions.DefaultArgs> = {
  /**
   * Select specific fields to fetch from the UsuarioRole
   */
  select?: Prisma.UsuarioRoleSelect<ExtArgs> | null
  /**
   * Omit specific fields from the UsuarioRole
   */
  omit?: Prisma.UsuarioRoleOmit<ExtArgs> | null
  /**
   * Choose, which related nodes to fetch as well
   */
  include?: Prisma.UsuarioRoleInclude<ExtArgs> | null
  /**
   * Filter, which UsuarioRole to fetch.
   */
  where?: Prisma.UsuarioRoleWhereInput
  /**
   * {@link https://www.prisma.io/docs/concepts/components/prisma-client/sorting Sorting Docs}
   * 
   * Determine the order of UsuarioRoles to fetch.
   */
  orderBy?: Prisma.UsuarioRoleOrderByWithRelationInput | Prisma.UsuarioRoleOrderByWithRelationInput[]
  /**
   * {@link https://www.prisma.io/docs/concepts/components/prisma-client/pagination#cursor-based-pagination Cursor Docs}
   * 
   * Sets the position for searching for UsuarioRoles.
   */
  cursor?: Prisma.UsuarioRoleWhereUniqueInput
  /**
   * {@link https://www.prisma.io/docs/concepts/components/prisma-client/pagination Pagination Docs}
   * 
   * Take `±n` UsuarioRoles from the position of the cursor.
   */
  take?: number
  /**
   * {@link https://www.prisma.io/docs/concepts/components/prisma-client/pagination Pagination Docs}
   * 
   * Skip the first `n` UsuarioRoles.
   */
  skip?: number
  /**
   * {@link https://www.prisma.io/docs/concepts/components/prisma-client/distinct Distinct Docs}
   * 
   * Filter by unique combinations of UsuarioRoles.
   */
  distinct?: Prisma.UsuarioRoleScalarFieldEnum | Prisma.UsuarioRoleScalarFieldEnum[]
}

/**
 * UsuarioRole findFirstOrThrow
 */
export type UsuarioRoleFindFirstOrThrowArgs<ExtArgs extends runtime.Types.Extensions.InternalArgs = runtime.Types.Extensions.DefaultArgs> = {
  /**
   * Select specific fields to fetch from the UsuarioRole
   */
  select?: Prisma.UsuarioRoleSelect<ExtArgs> | null
  /**
   * Omit specific fields from the UsuarioRole
   */
  omit?: Prisma.UsuarioRoleOmit<ExtArgs> | null
  /**
   * Choose, which related nodes to fetch as well
   */
  include?: Prisma.UsuarioRoleInclude<ExtArgs> | null
  /**
   * Filter, which UsuarioRole to fetch.
   */
  where?: Prisma.UsuarioRoleWhereInput
  /**
   * {@link https://www.prisma.io/docs/concepts/components/prisma-client/sorting Sorting Docs}
   * 
   * Determine the order of UsuarioRoles to fetch.
   */
  orderBy?: Prisma.UsuarioRoleOrderByWithRelationInput | Prisma.UsuarioRoleOrderByWithRelationInput[]
  /**
   * {@link https://www.prisma.io/docs/concepts/components/prisma-client/pagination#cursor-based-pagination Cursor Docs}
   * 
   * Sets the position for searching for UsuarioRoles.
   */
  cursor?: Prisma.UsuarioRoleWhereUniqueInput
  /**
   * {@link https://www.prisma.io/docs/concepts/components/prisma-client/pagination Pagination Docs}
   * 
   * Take `±n` UsuarioRoles from the position of the cursor.
   */
  take?: number
  /**
   * {@link https://www.prisma.io/docs/concepts/components/prisma-client/pagination Pagination Docs}
   * 
   * Skip the first `n` UsuarioRoles.
   */
  skip?: number
  /**
   * {@link https://www.prisma.io/docs/concepts/components/prisma-client/distinct Distinct Docs}
   * 
   * Filter by unique combinations of UsuarioRoles.
   */
  distinct?: Prisma.UsuarioRoleScalarFieldEnum | Prisma.UsuarioRoleScalarFieldEnum[]
}

/**
 * UsuarioRole findMany
 */
export type UsuarioRoleFindManyArgs<ExtArgs extends runtime.Types.Extensions.InternalArgs = runtime.Types.Extensions.DefaultArgs> = {
  /**
   * Select specific fields to fetch from the UsuarioRole
   */
  select?: Prisma.UsuarioRoleSelect<ExtArgs> | null
  /**
   * Omit specific fields from the UsuarioRole
   */
  omit?: Prisma.UsuarioRoleOmit<ExtArgs> | null
  /**
   * Choose, which related nodes to fetch as well
   */
  include?: Prisma.UsuarioRoleInclude<ExtArgs> | null
  /**
   * Filter, which UsuarioRoles to fetch.
   */
  where?: Prisma.UsuarioRoleWhereInput
  /**
   * {@link https://www.prisma.io/docs/concepts/components/prisma-client/sorting Sorting Docs}
   * 
   * Determine the order of UsuarioRoles to fetch.
   */
  orderBy?: Prisma.UsuarioRoleOrderByWithRelationInput | Prisma.UsuarioRoleOrderByWithRelationInput[]
  /**
   * {@link https://www.prisma.io/docs/concepts/components/prisma-client/pagination#cursor-based-pagination Cursor Docs}
   * 
   * Sets the position for listing UsuarioRoles.
   */
  cursor?: Prisma.UsuarioRoleWhereUniqueInput
  /**
   * {@link https://www.prisma.io/docs/concepts/components/prisma-client/pagination Pagination Docs}
   * 
   * Take `±n` UsuarioRoles from the position of the cursor.
   */
  take?: number
  /**
   * {@link https://www.prisma.io/docs/concepts/components/prisma-client/pagination Pagination Docs}
   * 
   * Skip the first `n` UsuarioRoles.
   */
  skip?: number
  distinct?: Prisma.UsuarioRoleScalarFieldEnum | Prisma.UsuarioRoleScalarFieldEnum[]
}

/**
 * UsuarioRole create
 */
export type UsuarioRoleCreateArgs<ExtArgs extends runtime.Types.Extensions.InternalArgs = runtime.Types.Extensions.DefaultArgs> = {
  /**
   * Select specific fields to fetch from the UsuarioRole
   */
  select?: Prisma.UsuarioRoleSelect<ExtArgs> | null
  /**
   * Omit specific fields from the UsuarioRole
   */
  omit?: Prisma.UsuarioRoleOmit<ExtArgs> | null
  /**
   * Choose, which related nodes to fetch as well
   */
  include?: Prisma.UsuarioRoleInclude<ExtArgs> | null
  /**
   * The data needed to create a UsuarioRole.
   */
  data: Prisma.XOR<Prisma.UsuarioRoleCreateInput, Prisma.UsuarioRoleUncheckedCreateInput>
}

/**
 * UsuarioRole createMany
 */
export type UsuarioRoleCreateManyArgs<ExtArgs extends runtime.Types.Extensions.InternalArgs = runtime.Types.Extensions.DefaultArgs> = {
  /**
   * The data used to create many UsuarioRoles.
   */
  data: Prisma.UsuarioRoleCreateManyInput | Prisma.UsuarioRoleCreateManyInput[]
  skipDuplicates?: boolean
}

/**
 * UsuarioRole update
 */
export type UsuarioRoleUpdateArgs<ExtArgs extends runtime.Types.Extensions.InternalArgs = runtime.Types.Extensions.DefaultArgs> = {
  /**
   * Select specific fields to fetch from the UsuarioRole
   */
  select?: Prisma.UsuarioRoleSelect<ExtArgs> | null
  /**
   * Omit specific fields from the UsuarioRole
   */
  omit?: Prisma.UsuarioRoleOmit<ExtArgs> | null
  /**
   * Choose, which related nodes to fetch as well
   */
  include?: Prisma.UsuarioRoleInclude<ExtArgs> | null
  /**
   * The data needed to update a UsuarioRole.
   */
  data: Prisma.XOR<Prisma.UsuarioRoleUpdateInput, Prisma.UsuarioRoleUncheckedUpdateInput>
  /**
   * Choose, which UsuarioRole to update.
   */
  where: Prisma.UsuarioRoleWhereUniqueInput
}

/**
 * UsuarioRole updateMany
 */
export type UsuarioRoleUpdateManyArgs<ExtArgs extends runtime.Types.Extensions.InternalArgs = runtime.Types.Extensions.DefaultArgs> = {
  /**
   * The data used to update UsuarioRoles.
   */
  data: Prisma.XOR<Prisma.UsuarioRoleUpdateManyMutationInput, Prisma.UsuarioRoleUncheckedUpdateManyInput>
  /**
   * Filter which UsuarioRoles to update
   */
  where?: Prisma.UsuarioRoleWhereInput
  /**
   * Limit how many UsuarioRoles to update.
   */
  limit?: number
}

/**
 * UsuarioRole upsert
 */
export type UsuarioRoleUpsertArgs<ExtArgs extends runtime.Types.Extensions.InternalArgs = runtime.Types.Extensions.DefaultArgs> = {
  /**
   * Select specific fields to fetch from the UsuarioRole
   */
  select?: Prisma.UsuarioRoleSelect<ExtArgs> | null
  /**
   * Omit specific fields from the UsuarioRole
   */
  omit?: Prisma.UsuarioRoleOmit<ExtArgs> | null
  /**
   * Choose, which related nodes to fetch as well
   */
  include?: Prisma.UsuarioRoleInclude<ExtArgs> | null
  /**
   * The filter to search for the UsuarioRole to update in case it exists.
   */
  where: Prisma.UsuarioRoleWhereUniqueInput
  /**
   * In case the UsuarioRole found by the `where` argument doesn't exist, create a new UsuarioRole with this data.
   */
  create: Prisma.XOR<Prisma.UsuarioRoleCreateInput, Prisma.UsuarioRoleUncheckedCreateInput>
  /**
   * In case the UsuarioRole was found with the provided `where` argument, update it with this data.
   */
  update: Prisma.XOR<Prisma.UsuarioRoleUpdateInput, Prisma.UsuarioRoleUncheckedUpdateInput>
}

/**
 * UsuarioRole delete
 */
export type UsuarioRoleDeleteArgs<ExtArgs extends runtime.Types.Extensions.InternalArgs = runtime.Types.Extensions.DefaultArgs> = {
  /**
   * Select specific fields to fetch from the UsuarioRole
   */
  select?: Prisma.UsuarioRoleSelect<ExtArgs> | null
  /**
   * Omit specific fields from the UsuarioRole
   */
  omit?: Prisma.UsuarioRoleOmit<ExtArgs> | null
  /**
   * Choose, which related nodes to fetch as well
   */
  include?: Prisma.UsuarioRoleInclude<ExtArgs> | null
  /**
   * Filter which UsuarioRole to delete.
   */
  where: Prisma.UsuarioRoleWhereUniqueInput
}

/**
 * UsuarioRole deleteMany
 */
export type UsuarioRoleDeleteManyArgs<ExtArgs extends runtime.Types.Extensions.InternalArgs = runtime.Types.Extensions.DefaultArgs> = {
  /**
   * Filter which UsuarioRoles to delete
   */
  where?: Prisma.UsuarioRoleWhereInput
  /**
   * Limit how many UsuarioRoles to delete.
   */
  limit?: number
}

/**
 * UsuarioRole without action
 */
export type UsuarioRoleDefaultArgs<ExtArgs extends runtime.Types.Extensions.InternalArgs = runtime.Types.Extensions.DefaultArgs> = {
  /**
   * Select specific fields to fetch from the UsuarioRole
   */
  select?: Prisma.UsuarioRoleSelect<ExtArgs> | null
  /**
   * Omit specific fields from the UsuarioRole
   */
  omit?: Prisma.UsuarioRoleOmit<ExtArgs> | null
  /**
   * Choose, which related nodes to fetch as well
   */
  include?: Prisma.UsuarioRoleInclude<ExtArgs> | null
}
