import { z } from "zod";

export const createAntecipacaoSchema = z.object({
  plantaoId: z.string().uuid(),
  fechamentoIds: z.array(z.string().uuid()).min(1, "Selecione pelo menos um fechamento"),
  valorSolicitado: z.number().positive("Valor deve ser maior que zero"),
  valorAprovado: z.number().positive().optional(),
  percentual: z.number().min(1, "Percentual mínimo é 1%").max(100, "Percentual máximo de antecipação é 100%"),
  taxaAntecipacao: z.number().min(0).optional(), // Taxa calculada (pode ser editada)
  dataPagamentoPrevista: z.string().optional(), // Calculada automaticamente baseada no prazo do plantão
  status: z.enum(["PENDENTE", "APROVADA", "REJEITADA", "PAGA", "CANCELADA"]).optional().default("PENDENTE"),
  numeroCCB: z.string().optional(),
  dataCCB: z.string().optional(),
  metaData: z.record(z.string(), z.any()).optional(), // Dados adicionais
  observacoes: z.string().optional(),
});

export const updateAntecipacaoSchema = createAntecipacaoSchema.partial();

export const antecipacaoQuerySchema = z.object({
  page: z.string().optional(),
  limit: z.string().optional(),
  search: z.string().optional(),
  status: z.enum(["PENDENTE", "APROVADA", "REJEITADA", "PAGA", "CANCELADA"]).optional(),
  plantaoId: z.string().optional(),
  profissionalId: z.string().optional(),
  clienteId: z.string().optional(),
});

export type CreateAntecipacaoInput = z.infer<typeof createAntecipacaoSchema>;
export type UpdateAntecipacaoInput = z.infer<typeof updateAntecipacaoSchema>;
export type AntecipacaoQuery = z.infer<typeof antecipacaoQuerySchema>;
