export const ModalidadeTrabalho = {
  PLANTONISTA: "PLANTONIS<PERSON>",
  MENSALISTA: "MENSALISTA",
  COORDENADOR: "COORDENADOR",
  SUPERVISOR: "SUPERVISOR",
  DIRETOR: "DIRETOR",
} as const;

export type ModalidadeTrabalho = (typeof ModalidadeTrabalho)[keyof typeof ModalidadeTrabalho];

export const TipoFechamento = {
  DIARIO: "DIARIO",
  SEMANAL: "SEMANAL",
  QUINZENAL: "QUINZENAL",
  MENSAL: "MENSAL",
} as const;

export type TipoFechamento = (typeof TipoFechamento)[keyof typeof TipoFechamento];

export const StatusFechamento = {
  PENDENTE: "PENDENTE",
  APROVADO: "APROVADO",
  REJEITADO: "REJEITADO",
} as const;

export type StatusFechamento = (typeof StatusFechamento)[keyof typeof StatusFechamento];

export const StatusAntecipacao = {
  PENDENTE: "PENDENTE",
  APROVADA: "APROVADA",
  REJEITADA: "REJEITADA",
  PAGA: "PAGA",
  CANCELADA: "CANCELADA",
} as const;

export type StatusAntecipacao = (typeof StatusAntecipacao)[keyof typeof StatusAntecipacao];

export const TipoNegocio = {
  TERCEIROS: "TERCEIROS",
  AURA: "AURA",
} as const;

export type TipoNegocio = (typeof TipoNegocio)[keyof typeof TipoNegocio];

export const TipoValor = {
  HORA: "HORA",
  DIARIA: "DIARIA",
  PLANTAO: "PLANTAO",
  MENSAL: "MENSAL",
} as const;

export type TipoValor = (typeof TipoValor)[keyof typeof TipoValor];

export const StatusPresencaDiaPlantao = {
  PENDENTE: "PENDENTE",
  APROVADO: "APROVADO",
  REJEITADO: "REJEITADO",
  EM_REVISAO: "EM_REVISAO",
} as const;

export type StatusPresencaDiaPlantao = (typeof StatusPresencaDiaPlantao)[keyof typeof StatusPresencaDiaPlantao];
