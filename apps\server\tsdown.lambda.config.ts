import { defineConfig } from "tsdown";
import fs from "fs";
import { execSync } from "child_process";

// const outDirName = 'lambda';
const outDirName = "build/gs2api_v2";

export default defineConfig({
  entry: ["./src/lambda"],
  minify: true,
  outDir: `./${outDirName}`,
  external: ["@prisma/client"],
  hooks: {
    "build:done": () => {
      fs.copyFileSync("./package.json", `./${outDirName}/package.json`);

      // Copy Prisma schema
      if (!fs.existsSync(`./${outDirName}/prisma`)) {
        fs.mkdirSync(`./${outDirName}/prisma`, { recursive: true });
        fs.mkdirSync(`./${outDirName}/prisma/schema`, { recursive: true });
      }
      execSync(`cp ./prisma/schema/schema.prisma ./${outDirName}/prisma/schema/schema.prisma`, { stdio: "inherit" });

      // Fix Prisma schema for production
      const schemaPath = `./${outDirName}/prisma/schema/schema.prisma`;
      let schema = fs.readFileSync(schemaPath, "utf-8");
      schema = schema.replace(
        /generator client \{[\s\S]*?\}/,
        `generator client {
          provider = "prisma-client-js"
        }`
      );
      fs.writeFileSync(schemaPath, schema);

      execSync("npm install --omit=dev", {
        cwd: `./${outDirName}`,
        stdio: "inherit",
      });

      // Generate Prisma client
      execSync("npx prisma generate --schema=./prisma/schema/schema.prisma", {
        cwd: `./${outDirName}`,
        stdio: "inherit",
      });
    },
  },
});
