generator client {
  provider     = "prisma-client"
  output       = "../../../shared/src/types/prisma"
  moduleFormat = "esm"
}

datasource db {
  provider = "mysql"
  url      = env("DATABASE_URL")
}

// Models
model Usuario {
  id       Int     @id @default(autoincrement())
  uuid     String  @unique @default(uuid())
  email    String  @unique
  senha    String // Hash da senha
  nome     String
  cpf      String  @unique @db.VarChar(20)
  telefone String?

  // Dados pessoais
  dataNascimento DateTime?
  genero         String? // Masculino, Feminino, Outro
  estadoCivil    String? // Solteiro, Casado, Divorciado, Viúvo
  nacionalidade  String?

  // Endereço
  cep         String? @db.VarChar(15)
  logradouro  String? @db.VarChar(150)
  numero      String? @db.VarChar(10)
  complemento String? @db.VarChar(250)
  bairro      String? @db.VarChar(150)
  cidade      String? @db.VarChar(150)
  uf          String? @db.VarChar(2)

  // Controle de acesso
  ativo             Boolean   @default(true)
  emailVerificado   Boolean   @default(false)
  emailVerificadoEm DateTime?
  deveResetarSenha  Boolean   @default(true)

  // Tokens de recuperação
  tokenRecuperacao       String?   @db.VarChar(255)
  tokenRecuperacaoExpira DateTime?

  // Último acesso
  ultimoAcesso DateTime?

  metaData Json?

  createdAt DateTime @default(now())
  updatedAt DateTime @default(now()) @updatedAt

  deletedAt DateTime?

  // Relações
  profissional     Profissional?
  roles            UsuarioRole[]
  clientes         UsuarioCliente[]
  aceiteTermosLgpd AceiteTermosLgpd[]
  logs             AuditLog[]

  @@map("usuarios")
}

model Role {
  id        Int     @id @default(autoincrement())
  nome      String  @unique @db.VarChar(25) // master, admin, profissional, coordenador
  descricao String? @db.VarChar(100)
  ativo     Boolean @default(true)

  createdAt DateTime @default(now())
  updatedAt DateTime @default(now()) @updatedAt

  usuarios UsuarioRole[]

  @@map("roles")
}

model UsuarioRole {
  id        Int @id @default(autoincrement())
  usuarioId Int
  roleId    Int

  usuario Usuario @relation(fields: [usuarioId], references: [id], onDelete: Cascade)
  role    Role    @relation(fields: [roleId], references: [id], onDelete: Cascade)

  createdAt DateTime @default(now())

  @@unique([usuarioId, roleId])
  @@index([usuarioId])
  @@index([roleId])
  @@map("usuario_roles")
}

model Cliente {
  id       Int     @id @default(autoincrement())
  uuid     String  @unique @default(uuid())
  nome     String  @db.VarChar(150)
  cnpj     String  @unique @db.VarChar(30)
  email    String? @db.VarChar(100)
  telefone String? @db.VarChar(20)

  taxaPadrao Float

  cep         String? @db.VarChar(20)
  logradouro  String? @db.VarChar(150)
  numero      String? @db.VarChar(10)
  complemento String? @db.VarChar(250)
  bairro      String? @db.VarChar(150)
  cidade      String? @db.VarChar(150)
  uf          String? @db.VarChar(2)

  fusoHorario String @default("America/Sao_Paulo") // ✅ IANA timezone

  ativo Boolean @default(true)

  createdAt DateTime @default(now())
  updatedAt DateTime @default(now()) @updatedAt

  deletedAt DateTime?

  locaisAtendimento LocalAtendimento[]
  plantoes          Plantao[]
  usuarios          UsuarioCliente[]

  @@map("clientes")
}

model Especialidade {
  id        Int     @id @default(autoincrement())
  nome      String  @unique @db.VarChar(150)
  descricao String? @db.VarChar(255)

  createdAt DateTime @default(now())
  updatedAt DateTime @default(now()) @updatedAt

  profissionais ProfissionalEspecialidade[]
  locais        LocalAtendimentoEspecialidades[]

  @@map("especialidades")
}

model Profissional {
  id        Int    @id @default(autoincrement())
  uuid      String @unique @default(uuid())
  usuarioId Int    @unique

  // Dados profissionais
  rg             String? @db.VarChar(20)
  orgaoEmissor   String? @db.VarChar(100)
  cnes           String? @db.VarChar(50) // Cadastro Nacional de Estabelecimentos de Saúde
  conselhoClasse String? @db.VarChar(50) // CRM, CRO, COREN, etc
  numeroRegistro String? @db.VarChar(50) // Número do registro no conselho
  ufConselho     String? @db.VarChar(2) // UF do conselho de classe

  // Dados bancários
  banco          String? @db.VarChar(100)
  agencia        String? @db.VarChar(10)
  digitoAgencia  String? @db.VarChar(2)
  conta          String? @db.VarChar(20)
  digitoConta    String? @db.VarChar(2)
  tipoConta      String? @db.VarChar(20) // Corrente, Poupança
  chavePix       String? @db.VarChar(100)
  tipoPix        String? @db.VarChar(20) // CPF, CNPJ, Email, Telefone, Aleatória
  tipoAssinatura String? @db.VarChar(20) // Digital, Física

  metaData Json?

  createdAt DateTime @default(now())
  updatedAt DateTime @default(now()) @updatedAt

  deletedAt DateTime?

  usuario        Usuario                     @relation(fields: [usuarioId], references: [id], onDelete: Cascade)
  especialidades ProfissionalEspecialidade[]
  plantoes       Plantao[]
  fechamentos    Fechamento[]
  antecipacoes   Antecipacao[]

  @@index([usuarioId])
  @@map("profissionais")
}

model ProfissionalEspecialidade {
  id              Int @id @default(autoincrement())
  profissionalId  Int
  especialidadeId Int

  profissional  Profissional  @relation(fields: [profissionalId], references: [id], onDelete: Cascade)
  especialidade Especialidade @relation(fields: [especialidadeId], references: [id], onDelete: Cascade)

  createdAt DateTime @default(now())

  @@unique([profissionalId, especialidadeId])
  @@index([profissionalId])
  @@index([especialidadeId])
  @@map("profissional_especialidades")
}

model UsuarioCliente {
  id        Int     @id @default(autoincrement())
  usuarioId Int
  clienteId Int
  ativo     Boolean @default(true)

  createdAt DateTime @default(now())
  updatedAt DateTime @default(now()) @updatedAt

  usuario Usuario @relation(fields: [usuarioId], references: [id], onDelete: Cascade)
  cliente Cliente @relation(fields: [clienteId], references: [id], onDelete: Cascade)

  @@unique([usuarioId, clienteId])
  @@index([usuarioId])
  @@index([clienteId])
  @@map("usuario_clientes")
}

model LocalAtendimento {
  id          Int     @id @default(autoincrement())
  uuid        String  @unique @default(uuid())
  clienteId   Int
  nome        String  @db.VarChar(150)
  endereco    String  @db.VarChar(255)
  cidade      String  @db.VarChar(100)
  estado      String  @db.VarChar(100)
  cep         String  @db.VarChar(10)
  telefone    String? @db.VarChar(15)
  responsavel String? @db.VarChar(100)
  observacoes String? @db.Text
  latitude    Float?
  longitude   Float?
  ativo       Boolean @default(true)

  fusoHorario String @default("America/Sao_Paulo") // ✅ IANA timezone

  createdAt DateTime @default(now())
  updatedAt DateTime @default(now()) @updatedAt

  deletedAt DateTime?

  cliente        Cliente                          @relation(fields: [clienteId], references: [id], onDelete: Cascade)
  plantoes       Plantao[]
  especialidades LocalAtendimentoEspecialidades[]

  @@unique([clienteId, nome, endereco, cidade, estado, cep])
  @@index([clienteId])
  @@map("locais_atendimento")
}

model LocalAtendimentoEspecialidades {
  id                 Int @id @default(autoincrement())
  localAtendimentoId Int
  especialidadeId    Int

  localAtendimento LocalAtendimento @relation(fields: [localAtendimentoId], references: [id], onDelete: Cascade)
  especialidade    Especialidade    @relation(fields: [especialidadeId], references: [id], onDelete: Cascade)

  createdAt DateTime @default(now())

  @@unique([localAtendimentoId, especialidadeId])
  @@index([localAtendimentoId])
  @@index([especialidadeId])
  @@map("locais_atendimento_especialidades")
}

model Plantao {
  id                 Int    @id @default(autoincrement())
  uuid               String @unique @default(uuid())
  clienteId          Int
  localAtendimentoId Int
  profissionalId     Int

  dataInicial DateTime
  dataFinal   DateTime?

  // Prazo de pagamento específico do plantão (em dias)
  // Define quando o pagamento será realizado após o fechamento
  prazoPagamentoDias Int? // Ex: 30, 45, 60 dias

  modalidadeTrabalho String @db.VarChar(20) // PLANTONISTA | MENSALISTA | COORDENADOR | SUPERVISOR | DIRETOR

  // tipoFechamento reflete a frequência de fechamento do plantão
  tipoFechamento String @db.VarChar(20) //DIARIO | SEMANAL | QUINZENAL | MENSAL

  // valorHora    Float?
  // valorPlantao Float?
  // valorMensal  Float?
  // valorTotal   Float
  tipoValor String @default("HORA") @db.VarChar(20) // HORA | DIARIA | PLANTAO | MENSAL
  valorBase Float // Valor base dependendo do tipo (hora, plantão ou mensal)
  // valorTotal Float // Total calculado (pode ser derivado em código)

  // Configuração de horários
  horaInicio String @db.VarChar(5) // "08:00"
  horaFim    String @db.VarChar(5) // "18:00"
  intervalo  String @db.VarChar(5) // "01:00" - tempo de intervalo
  tipoTurno  String @db.VarChar(20) // "diurno", "comercial", "noturno", "customizado"

  observacoes String? @db.Text
  // ativo       Boolean @default(true)

  fusoHorario String   @default("America/Sao_Paulo") // ✅ IANA timezone
  createdAt   DateTime @default(now())
  updatedAt   DateTime @default(now()) @updatedAt

  // Campo para rastreamento de conclusão
  concluidoEm DateTime? // Data/hora de conclusão do plantão

  deletedAt DateTime?

  cliente          Cliente          @relation(fields: [clienteId], references: [id])
  localAtendimento LocalAtendimento @relation(fields: [localAtendimentoId], references: [id])
  profissional     Profissional     @relation(fields: [profissionalId], references: [id])
  diasPlantao      DiaPlantao[]
  fechamentos      Fechamento[]
  antecipacoes     Antecipacao[]

  // Nova constraint baseada em dataInicial
  @@unique([profissionalId, dataInicial, clienteId])
  @@index([clienteId])
  @@index([localAtendimentoId])
  @@index([profissionalId])
  @@index([dataInicial, dataFinal])
  @@index([concluidoEm])
  @@map("plantoes")
}

model DiaPlantao {
  id        Int      @id @default(autoincrement())
  plantaoId Int
  data      DateTime // Data completa do dia (YYYY-MM-DD)  

  // referencia para entrada e saida do dia
  horaEntrada String? @db.VarChar(5) // "08:00"
  horaSaida   String? @db.VarChar(5) // "18:00"
  intervalo   String? @db.VarChar(5) // "01:00" - tempo de intervalo do dia

  observacoes String? @db.Text

  createdAt DateTime @default(now())
  updatedAt DateTime @default(now()) @updatedAt

  plantao            Plantao              @relation(fields: [plantaoId], references: [id], onDelete: Cascade)
  presencaDiaPlantao PresencaDiaPlantao[]

  @@unique([plantaoId, data], name: "plantao_data_unique")
  @@index([plantaoId])
  @@index([data])
  @@map("dias_plantao")
}

model PresencaDiaPlantao {
  id           Int  @id @default(autoincrement())
  diaPlantaoId Int
  fechamentoId Int? // Referência ao fechamento quando o registro for processado

  // Horários registrados
  horaEntrada DateTime?
  horaSaida   DateTime?
  intervalo   String?   @db.VarChar(5) // "01:00" - tempo de intervalo

  // Campos calculados/derivados
  horasTrabalhadas Float? // Total de horas trabalhadas (já descontado o intervalo)
  valorEstimado    Float? // Valor estimado do dia baseado no total de horas trabalhadas

  // Campos de aprovação
  status String @default("PENDENTE") @db.VarChar(20) // Status do registro (PENDENTE, APROVADO, REJEITADO, EM_REVISAO)

  historicoAprovacoes Json? // Lista de aprovações/rejeições: [{usuarioId, nome, acao, data, observacao}]

  // Campos de glosa
  tempoGlosado       Float? // Tempo em horas que foi glosado
  justificativaGlosa String? @db.Text // Justificativa para o tempo glosado

  observacao String? @db.Text
  metadata   Json? // Metadados adicionais (localização, fotos, etc)

  createdAt DateTime @default(now())
  updatedAt DateTime @default(now()) @updatedAt

  diaPlantao DiaPlantao  @relation(fields: [diaPlantaoId], references: [id], onDelete: Cascade)
  fechamento Fechamento? @relation(fields: [fechamentoId], references: [id], onDelete: SetNull)

  // Constraint única para evitar duplicatas de horários no mesmo dia
  @@unique([diaPlantaoId, horaEntrada, horaSaida])
  @@index([diaPlantaoId])
  @@index([fechamentoId])
  @@index([status])
  @@map("presenca_dia_plantao")
}

model Fechamento {
  id             Int    @id @default(autoincrement())
  uuid           String @unique @default(uuid())
  plantaoId      Int
  profissionalId Int
  antecipacaoId  Int? // Referência para a antecipação

  status String @default("PENDENTE") @db.VarChar(20) // Status do fechamento PENDENTE|APROVADO|REJEITADO

  // Totalizadores
  totalHoras      Float?
  totalValor      Float
  diasTrabalhados Int
  diasPrevistos   Int

  // Dados de aprovação
  aprovadoPor    String?   @db.VarChar(100)
  aprovadoEm     DateTime?
  rejeitadoPor   String?   @db.VarChar(100)
  rejeitadoEm    DateTime?
  motivoRejeicao String?   @db.Text

  observacoes String? @db.Text

  createdAt DateTime @default(now())
  updatedAt DateTime @default(now()) @updatedAt

  deletedAt DateTime?

  plantao            Plantao              @relation(fields: [plantaoId], references: [id])
  profissional       Profissional         @relation(fields: [profissionalId], references: [id])
  antecipacao        Antecipacao?         @relation(fields: [antecipacaoId], references: [id], onDelete: SetNull)
  presencaDiaPlantao PresencaDiaPlantao[]

  @@index([plantaoId])
  @@index([profissionalId])
  @@index([antecipacaoId])
  @@index([status])
  @@map("fechamentos")
}

model Antecipacao {
  id   Int    @id @default(autoincrement())
  uuid String @unique @default(uuid())

  plantaoId      Int
  profissionalId Int

  valorSolicitado Float
  valorAprovado   Float?
  percentual      Float // Percentual de antecipação (máximo 100%)

  // Taxa de antecipação calculada (pode ser editada)
  taxaAntecipacao Float? // Taxa aplicada baseada na fórmula do cliente

  // Prazo de pagamento prevista (calculada automaticamente)
  // Baseada em plantao.prazoPagamentoDias
  dataPagamentoPrevista DateTime

  status String @default("PENDENTE") @db.VarChar(20) // Status da antecipação PENDENTE|APROVADA|REJEITADA|PAGA|CANCELADA

  // Dados da CCB
  numeroCCB  String?   @db.VarChar(250)
  dataCCB    DateTime?
  arquivoCCB String?   @db.VarChar(250) // Path do arquivo PDF

  // Campos para assinatura digital do termo
  termoAssinado       Boolean   @default(false) // Indica se o termo foi assinado
  dataAssinaturaTermo DateTime? // Data e hora da assinatura
  hashAssinatura      String?   @db.VarChar(250) // Hash SHA-256 da assinatura
  ipAssinatura        String?   @db.VarChar(50) // IP do profissional no momento da assinatura

  observacoes String? @db.Text

  metaData Json?

  createdAt DateTime @default(now())
  updatedAt DateTime @default(now()) @updatedAt

  deletedAt DateTime?

  plantao      Plantao                @relation(fields: [plantaoId], references: [id])
  profissional Profissional           @relation(fields: [profissionalId], references: [id])
  fechamentos  Fechamento[] // 1:N relationship - uma antecipação pode ter vários fechamentos
  historico    AntecipacaoHistorico[]

  @@index([plantaoId])
  @@index([profissionalId])
  @@index([status])
  @@map("antecipacoes")
}

model AntecipacaoHistorico {
  id            Int    @id @default(autoincrement())
  antecipacaoId Int
  status        String @db.VarChar(20) // PENDENTE|APROVADA|REJEITADA|PAGA|CANCELADA

  metaData  Json? // Dados adicionais sobre a mudança
  createdAt DateTime @default(now()) // Timestamp do evento

  antecipacao Antecipacao @relation(fields: [antecipacaoId], references: [id], onDelete: Cascade)

  @@index([antecipacaoId])
  @@map("antecipacoes_historico")
}

// Modelo de Auditoria/Log
model AuditLog {
  id Int @id @default(autoincrement())

  // Informações da operação
  operation String  @db.VarChar(20) // CREATE, UPDATE, DELETE, READ
  tableName String  @db.VarChar(100) // Nome da tabela afetada
  recordId  String? @db.VarChar(100) // ID do registro afetado

  // Dados da mudança
  oldData Json? // Dados anteriores (para UPDATE e DELETE)
  newData Json? // Novos dados (para CREATE e UPDATE)
  changes Json? // Diferença entre old e new (para UPDATE)

  // Contexto da operação
  userId    Int? // ID do usuário que realizou a ação
  userName  String? @db.VarChar(100) // Nome do usuário para facilitar leitura
  userEmail String? @db.VarChar(100) // Email do usuário
  userRole  String? @db.VarChar(100) // Papel/função do usuário

  // Informações de requisição
  ipAddress String? @db.VarChar(45) // IP de origem
  userAgent String? @db.Text // Browser/client info
  endpoint  String? @db.VarChar(100) // Rota API utilizada
  method    String? @db.VarChar(10) // GET, POST, PUT, DELETE

  // Informações adicionais
  description String? @db.Text // Descrição legível da ação
  metadata    Json? // Dados adicionais contextuais

  // Timestamps
  createdAt DateTime @default(now())

  user Usuario? @relation(fields: [userId], references: [id])

  @@index([tableName, createdAt])
  @@index([recordId])
  @@index([userId])
  @@index([operation])
  @@index([createdAt])
  @@map("audit_logs")
}

model GerenciamentoTermosLgpd {
  id            Int       @id @default(autoincrement())
  versao        String    @db.VarChar(20)
  titulo        String    @db.VarChar(255)
  conteudo      String    @db.LongText
  hashConteudo  String
  ativo         Boolean   @default(false)
  criadoPor     Int?
  criadoEm      DateTime  @default(now())
  atualizadoEm  DateTime  @default(now()) @updatedAt
  validoDe      DateTime  @default(now())
  validoAte     DateTime?
  tipoDocumento String    @default("termos_lgpd") @db.VarChar(100)

  deletedAt DateTime?

  aceiteTermosLgpd AceiteTermosLgpd[]

  @@map("gerenciamento_termos_lgpd")
}

model AceiteTermosLgpd {
  id                Int       @id @default(autoincrement())
  usuarioId         Int
  termoVersaoId     Int
  consentimentoLgpd Boolean   @default(true)
  aceitoEm          DateTime? @default(now())
  enderecoIp        String    @db.VarChar(45)
  latitude          Decimal   @db.Decimal(10, 8)
  longitude         Decimal   @db.Decimal(11, 8)
  userAgent         String?   @db.Text
  dadosAdicionais   Json?     @db.Json
  termoVersao       String    @db.VarChar(20)

  termo   GerenciamentoTermosLgpd @relation(fields: [termoVersaoId], references: [id])
  usuario Usuario                 @relation(fields: [usuarioId], references: [id])

  @@index([usuarioId])
  @@index([termoVersaoId])
  @@map("aceite_termos_lgpd")
}
