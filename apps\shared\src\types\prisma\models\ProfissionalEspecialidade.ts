
/* !!! This is code generated by Prisma. Do not edit directly. !!! */
/* eslint-disable */
// @ts-nocheck 
/*
 * This file exports the `ProfissionalEspecialidade` model and its related types.
 *
 * 🟢 You can import this file directly.
 */
import * as runtime from "@prisma/client/runtime/library"
import type * as $Enums from "../enums.ts"
import type * as Prisma from "../internal/prismaNamespace.ts"

/**
 * Model ProfissionalEspecialidade
 * 
 */
export type ProfissionalEspecialidadeModel = runtime.Types.Result.DefaultSelection<Prisma.$ProfissionalEspecialidadePayload>

export type AggregateProfissionalEspecialidade = {
  _count: ProfissionalEspecialidadeCountAggregateOutputType | null
  _avg: ProfissionalEspecialidadeAvgAggregateOutputType | null
  _sum: ProfissionalEspecialidadeSumAggregateOutputType | null
  _min: ProfissionalEspecialidadeMinAggregateOutputType | null
  _max: ProfissionalEspecialidadeMaxAggregateOutputType | null
}

export type ProfissionalEspecialidadeAvgAggregateOutputType = {
  id: number | null
  profissionalId: number | null
  especialidadeId: number | null
}

export type ProfissionalEspecialidadeSumAggregateOutputType = {
  id: number | null
  profissionalId: number | null
  especialidadeId: number | null
}

export type ProfissionalEspecialidadeMinAggregateOutputType = {
  id: number | null
  profissionalId: number | null
  especialidadeId: number | null
  createdAt: Date | null
}

export type ProfissionalEspecialidadeMaxAggregateOutputType = {
  id: number | null
  profissionalId: number | null
  especialidadeId: number | null
  createdAt: Date | null
}

export type ProfissionalEspecialidadeCountAggregateOutputType = {
  id: number
  profissionalId: number
  especialidadeId: number
  createdAt: number
  _all: number
}


export type ProfissionalEspecialidadeAvgAggregateInputType = {
  id?: true
  profissionalId?: true
  especialidadeId?: true
}

export type ProfissionalEspecialidadeSumAggregateInputType = {
  id?: true
  profissionalId?: true
  especialidadeId?: true
}

export type ProfissionalEspecialidadeMinAggregateInputType = {
  id?: true
  profissionalId?: true
  especialidadeId?: true
  createdAt?: true
}

export type ProfissionalEspecialidadeMaxAggregateInputType = {
  id?: true
  profissionalId?: true
  especialidadeId?: true
  createdAt?: true
}

export type ProfissionalEspecialidadeCountAggregateInputType = {
  id?: true
  profissionalId?: true
  especialidadeId?: true
  createdAt?: true
  _all?: true
}

export type ProfissionalEspecialidadeAggregateArgs<ExtArgs extends runtime.Types.Extensions.InternalArgs = runtime.Types.Extensions.DefaultArgs> = {
  /**
   * Filter which ProfissionalEspecialidade to aggregate.
   */
  where?: Prisma.ProfissionalEspecialidadeWhereInput
  /**
   * {@link https://www.prisma.io/docs/concepts/components/prisma-client/sorting Sorting Docs}
   * 
   * Determine the order of ProfissionalEspecialidades to fetch.
   */
  orderBy?: Prisma.ProfissionalEspecialidadeOrderByWithRelationInput | Prisma.ProfissionalEspecialidadeOrderByWithRelationInput[]
  /**
   * {@link https://www.prisma.io/docs/concepts/components/prisma-client/pagination#cursor-based-pagination Cursor Docs}
   * 
   * Sets the start position
   */
  cursor?: Prisma.ProfissionalEspecialidadeWhereUniqueInput
  /**
   * {@link https://www.prisma.io/docs/concepts/components/prisma-client/pagination Pagination Docs}
   * 
   * Take `±n` ProfissionalEspecialidades from the position of the cursor.
   */
  take?: number
  /**
   * {@link https://www.prisma.io/docs/concepts/components/prisma-client/pagination Pagination Docs}
   * 
   * Skip the first `n` ProfissionalEspecialidades.
   */
  skip?: number
  /**
   * {@link https://www.prisma.io/docs/concepts/components/prisma-client/aggregations Aggregation Docs}
   * 
   * Count returned ProfissionalEspecialidades
  **/
  _count?: true | ProfissionalEspecialidadeCountAggregateInputType
  /**
   * {@link https://www.prisma.io/docs/concepts/components/prisma-client/aggregations Aggregation Docs}
   * 
   * Select which fields to average
  **/
  _avg?: ProfissionalEspecialidadeAvgAggregateInputType
  /**
   * {@link https://www.prisma.io/docs/concepts/components/prisma-client/aggregations Aggregation Docs}
   * 
   * Select which fields to sum
  **/
  _sum?: ProfissionalEspecialidadeSumAggregateInputType
  /**
   * {@link https://www.prisma.io/docs/concepts/components/prisma-client/aggregations Aggregation Docs}
   * 
   * Select which fields to find the minimum value
  **/
  _min?: ProfissionalEspecialidadeMinAggregateInputType
  /**
   * {@link https://www.prisma.io/docs/concepts/components/prisma-client/aggregations Aggregation Docs}
   * 
   * Select which fields to find the maximum value
  **/
  _max?: ProfissionalEspecialidadeMaxAggregateInputType
}

export type GetProfissionalEspecialidadeAggregateType<T extends ProfissionalEspecialidadeAggregateArgs> = {
      [P in keyof T & keyof AggregateProfissionalEspecialidade]: P extends '_count' | 'count'
    ? T[P] extends true
      ? number
      : Prisma.GetScalarType<T[P], AggregateProfissionalEspecialidade[P]>
    : Prisma.GetScalarType<T[P], AggregateProfissionalEspecialidade[P]>
}




export type ProfissionalEspecialidadeGroupByArgs<ExtArgs extends runtime.Types.Extensions.InternalArgs = runtime.Types.Extensions.DefaultArgs> = {
  where?: Prisma.ProfissionalEspecialidadeWhereInput
  orderBy?: Prisma.ProfissionalEspecialidadeOrderByWithAggregationInput | Prisma.ProfissionalEspecialidadeOrderByWithAggregationInput[]
  by: Prisma.ProfissionalEspecialidadeScalarFieldEnum[] | Prisma.ProfissionalEspecialidadeScalarFieldEnum
  having?: Prisma.ProfissionalEspecialidadeScalarWhereWithAggregatesInput
  take?: number
  skip?: number
  _count?: ProfissionalEspecialidadeCountAggregateInputType | true
  _avg?: ProfissionalEspecialidadeAvgAggregateInputType
  _sum?: ProfissionalEspecialidadeSumAggregateInputType
  _min?: ProfissionalEspecialidadeMinAggregateInputType
  _max?: ProfissionalEspecialidadeMaxAggregateInputType
}

export type ProfissionalEspecialidadeGroupByOutputType = {
  id: number
  profissionalId: number
  especialidadeId: number
  createdAt: Date
  _count: ProfissionalEspecialidadeCountAggregateOutputType | null
  _avg: ProfissionalEspecialidadeAvgAggregateOutputType | null
  _sum: ProfissionalEspecialidadeSumAggregateOutputType | null
  _min: ProfissionalEspecialidadeMinAggregateOutputType | null
  _max: ProfissionalEspecialidadeMaxAggregateOutputType | null
}

type GetProfissionalEspecialidadeGroupByPayload<T extends ProfissionalEspecialidadeGroupByArgs> = Prisma.PrismaPromise<
  Array<
    Prisma.PickEnumerable<ProfissionalEspecialidadeGroupByOutputType, T['by']> &
      {
        [P in ((keyof T) & (keyof ProfissionalEspecialidadeGroupByOutputType))]: P extends '_count'
          ? T[P] extends boolean
            ? number
            : Prisma.GetScalarType<T[P], ProfissionalEspecialidadeGroupByOutputType[P]>
          : Prisma.GetScalarType<T[P], ProfissionalEspecialidadeGroupByOutputType[P]>
      }
    >
  >



export type ProfissionalEspecialidadeWhereInput = {
  AND?: Prisma.ProfissionalEspecialidadeWhereInput | Prisma.ProfissionalEspecialidadeWhereInput[]
  OR?: Prisma.ProfissionalEspecialidadeWhereInput[]
  NOT?: Prisma.ProfissionalEspecialidadeWhereInput | Prisma.ProfissionalEspecialidadeWhereInput[]
  id?: Prisma.IntFilter<"ProfissionalEspecialidade"> | number
  profissionalId?: Prisma.IntFilter<"ProfissionalEspecialidade"> | number
  especialidadeId?: Prisma.IntFilter<"ProfissionalEspecialidade"> | number
  createdAt?: Prisma.DateTimeFilter<"ProfissionalEspecialidade"> | Date | string
  profissional?: Prisma.XOR<Prisma.ProfissionalScalarRelationFilter, Prisma.ProfissionalWhereInput>
  especialidade?: Prisma.XOR<Prisma.EspecialidadeScalarRelationFilter, Prisma.EspecialidadeWhereInput>
}

export type ProfissionalEspecialidadeOrderByWithRelationInput = {
  id?: Prisma.SortOrder
  profissionalId?: Prisma.SortOrder
  especialidadeId?: Prisma.SortOrder
  createdAt?: Prisma.SortOrder
  profissional?: Prisma.ProfissionalOrderByWithRelationInput
  especialidade?: Prisma.EspecialidadeOrderByWithRelationInput
}

export type ProfissionalEspecialidadeWhereUniqueInput = Prisma.AtLeast<{
  id?: number
  profissionalId_especialidadeId?: Prisma.ProfissionalEspecialidadeProfissionalIdEspecialidadeIdCompoundUniqueInput
  AND?: Prisma.ProfissionalEspecialidadeWhereInput | Prisma.ProfissionalEspecialidadeWhereInput[]
  OR?: Prisma.ProfissionalEspecialidadeWhereInput[]
  NOT?: Prisma.ProfissionalEspecialidadeWhereInput | Prisma.ProfissionalEspecialidadeWhereInput[]
  profissionalId?: Prisma.IntFilter<"ProfissionalEspecialidade"> | number
  especialidadeId?: Prisma.IntFilter<"ProfissionalEspecialidade"> | number
  createdAt?: Prisma.DateTimeFilter<"ProfissionalEspecialidade"> | Date | string
  profissional?: Prisma.XOR<Prisma.ProfissionalScalarRelationFilter, Prisma.ProfissionalWhereInput>
  especialidade?: Prisma.XOR<Prisma.EspecialidadeScalarRelationFilter, Prisma.EspecialidadeWhereInput>
}, "id" | "profissionalId_especialidadeId">

export type ProfissionalEspecialidadeOrderByWithAggregationInput = {
  id?: Prisma.SortOrder
  profissionalId?: Prisma.SortOrder
  especialidadeId?: Prisma.SortOrder
  createdAt?: Prisma.SortOrder
  _count?: Prisma.ProfissionalEspecialidadeCountOrderByAggregateInput
  _avg?: Prisma.ProfissionalEspecialidadeAvgOrderByAggregateInput
  _max?: Prisma.ProfissionalEspecialidadeMaxOrderByAggregateInput
  _min?: Prisma.ProfissionalEspecialidadeMinOrderByAggregateInput
  _sum?: Prisma.ProfissionalEspecialidadeSumOrderByAggregateInput
}

export type ProfissionalEspecialidadeScalarWhereWithAggregatesInput = {
  AND?: Prisma.ProfissionalEspecialidadeScalarWhereWithAggregatesInput | Prisma.ProfissionalEspecialidadeScalarWhereWithAggregatesInput[]
  OR?: Prisma.ProfissionalEspecialidadeScalarWhereWithAggregatesInput[]
  NOT?: Prisma.ProfissionalEspecialidadeScalarWhereWithAggregatesInput | Prisma.ProfissionalEspecialidadeScalarWhereWithAggregatesInput[]
  id?: Prisma.IntWithAggregatesFilter<"ProfissionalEspecialidade"> | number
  profissionalId?: Prisma.IntWithAggregatesFilter<"ProfissionalEspecialidade"> | number
  especialidadeId?: Prisma.IntWithAggregatesFilter<"ProfissionalEspecialidade"> | number
  createdAt?: Prisma.DateTimeWithAggregatesFilter<"ProfissionalEspecialidade"> | Date | string
}

export type ProfissionalEspecialidadeCreateInput = {
  createdAt?: Date | string
  profissional: Prisma.ProfissionalCreateNestedOneWithoutEspecialidadesInput
  especialidade: Prisma.EspecialidadeCreateNestedOneWithoutProfissionaisInput
}

export type ProfissionalEspecialidadeUncheckedCreateInput = {
  id?: number
  profissionalId: number
  especialidadeId: number
  createdAt?: Date | string
}

export type ProfissionalEspecialidadeUpdateInput = {
  createdAt?: Prisma.DateTimeFieldUpdateOperationsInput | Date | string
  profissional?: Prisma.ProfissionalUpdateOneRequiredWithoutEspecialidadesNestedInput
  especialidade?: Prisma.EspecialidadeUpdateOneRequiredWithoutProfissionaisNestedInput
}

export type ProfissionalEspecialidadeUncheckedUpdateInput = {
  id?: Prisma.IntFieldUpdateOperationsInput | number
  profissionalId?: Prisma.IntFieldUpdateOperationsInput | number
  especialidadeId?: Prisma.IntFieldUpdateOperationsInput | number
  createdAt?: Prisma.DateTimeFieldUpdateOperationsInput | Date | string
}

export type ProfissionalEspecialidadeCreateManyInput = {
  id?: number
  profissionalId: number
  especialidadeId: number
  createdAt?: Date | string
}

export type ProfissionalEspecialidadeUpdateManyMutationInput = {
  createdAt?: Prisma.DateTimeFieldUpdateOperationsInput | Date | string
}

export type ProfissionalEspecialidadeUncheckedUpdateManyInput = {
  id?: Prisma.IntFieldUpdateOperationsInput | number
  profissionalId?: Prisma.IntFieldUpdateOperationsInput | number
  especialidadeId?: Prisma.IntFieldUpdateOperationsInput | number
  createdAt?: Prisma.DateTimeFieldUpdateOperationsInput | Date | string
}

export type ProfissionalEspecialidadeListRelationFilter = {
  every?: Prisma.ProfissionalEspecialidadeWhereInput
  some?: Prisma.ProfissionalEspecialidadeWhereInput
  none?: Prisma.ProfissionalEspecialidadeWhereInput
}

export type ProfissionalEspecialidadeOrderByRelationAggregateInput = {
  _count?: Prisma.SortOrder
}

export type ProfissionalEspecialidadeProfissionalIdEspecialidadeIdCompoundUniqueInput = {
  profissionalId: number
  especialidadeId: number
}

export type ProfissionalEspecialidadeCountOrderByAggregateInput = {
  id?: Prisma.SortOrder
  profissionalId?: Prisma.SortOrder
  especialidadeId?: Prisma.SortOrder
  createdAt?: Prisma.SortOrder
}

export type ProfissionalEspecialidadeAvgOrderByAggregateInput = {
  id?: Prisma.SortOrder
  profissionalId?: Prisma.SortOrder
  especialidadeId?: Prisma.SortOrder
}

export type ProfissionalEspecialidadeMaxOrderByAggregateInput = {
  id?: Prisma.SortOrder
  profissionalId?: Prisma.SortOrder
  especialidadeId?: Prisma.SortOrder
  createdAt?: Prisma.SortOrder
}

export type ProfissionalEspecialidadeMinOrderByAggregateInput = {
  id?: Prisma.SortOrder
  profissionalId?: Prisma.SortOrder
  especialidadeId?: Prisma.SortOrder
  createdAt?: Prisma.SortOrder
}

export type ProfissionalEspecialidadeSumOrderByAggregateInput = {
  id?: Prisma.SortOrder
  profissionalId?: Prisma.SortOrder
  especialidadeId?: Prisma.SortOrder
}

export type ProfissionalEspecialidadeCreateNestedManyWithoutEspecialidadeInput = {
  create?: Prisma.XOR<Prisma.ProfissionalEspecialidadeCreateWithoutEspecialidadeInput, Prisma.ProfissionalEspecialidadeUncheckedCreateWithoutEspecialidadeInput> | Prisma.ProfissionalEspecialidadeCreateWithoutEspecialidadeInput[] | Prisma.ProfissionalEspecialidadeUncheckedCreateWithoutEspecialidadeInput[]
  connectOrCreate?: Prisma.ProfissionalEspecialidadeCreateOrConnectWithoutEspecialidadeInput | Prisma.ProfissionalEspecialidadeCreateOrConnectWithoutEspecialidadeInput[]
  createMany?: Prisma.ProfissionalEspecialidadeCreateManyEspecialidadeInputEnvelope
  connect?: Prisma.ProfissionalEspecialidadeWhereUniqueInput | Prisma.ProfissionalEspecialidadeWhereUniqueInput[]
}

export type ProfissionalEspecialidadeUncheckedCreateNestedManyWithoutEspecialidadeInput = {
  create?: Prisma.XOR<Prisma.ProfissionalEspecialidadeCreateWithoutEspecialidadeInput, Prisma.ProfissionalEspecialidadeUncheckedCreateWithoutEspecialidadeInput> | Prisma.ProfissionalEspecialidadeCreateWithoutEspecialidadeInput[] | Prisma.ProfissionalEspecialidadeUncheckedCreateWithoutEspecialidadeInput[]
  connectOrCreate?: Prisma.ProfissionalEspecialidadeCreateOrConnectWithoutEspecialidadeInput | Prisma.ProfissionalEspecialidadeCreateOrConnectWithoutEspecialidadeInput[]
  createMany?: Prisma.ProfissionalEspecialidadeCreateManyEspecialidadeInputEnvelope
  connect?: Prisma.ProfissionalEspecialidadeWhereUniqueInput | Prisma.ProfissionalEspecialidadeWhereUniqueInput[]
}

export type ProfissionalEspecialidadeUpdateManyWithoutEspecialidadeNestedInput = {
  create?: Prisma.XOR<Prisma.ProfissionalEspecialidadeCreateWithoutEspecialidadeInput, Prisma.ProfissionalEspecialidadeUncheckedCreateWithoutEspecialidadeInput> | Prisma.ProfissionalEspecialidadeCreateWithoutEspecialidadeInput[] | Prisma.ProfissionalEspecialidadeUncheckedCreateWithoutEspecialidadeInput[]
  connectOrCreate?: Prisma.ProfissionalEspecialidadeCreateOrConnectWithoutEspecialidadeInput | Prisma.ProfissionalEspecialidadeCreateOrConnectWithoutEspecialidadeInput[]
  upsert?: Prisma.ProfissionalEspecialidadeUpsertWithWhereUniqueWithoutEspecialidadeInput | Prisma.ProfissionalEspecialidadeUpsertWithWhereUniqueWithoutEspecialidadeInput[]
  createMany?: Prisma.ProfissionalEspecialidadeCreateManyEspecialidadeInputEnvelope
  set?: Prisma.ProfissionalEspecialidadeWhereUniqueInput | Prisma.ProfissionalEspecialidadeWhereUniqueInput[]
  disconnect?: Prisma.ProfissionalEspecialidadeWhereUniqueInput | Prisma.ProfissionalEspecialidadeWhereUniqueInput[]
  delete?: Prisma.ProfissionalEspecialidadeWhereUniqueInput | Prisma.ProfissionalEspecialidadeWhereUniqueInput[]
  connect?: Prisma.ProfissionalEspecialidadeWhereUniqueInput | Prisma.ProfissionalEspecialidadeWhereUniqueInput[]
  update?: Prisma.ProfissionalEspecialidadeUpdateWithWhereUniqueWithoutEspecialidadeInput | Prisma.ProfissionalEspecialidadeUpdateWithWhereUniqueWithoutEspecialidadeInput[]
  updateMany?: Prisma.ProfissionalEspecialidadeUpdateManyWithWhereWithoutEspecialidadeInput | Prisma.ProfissionalEspecialidadeUpdateManyWithWhereWithoutEspecialidadeInput[]
  deleteMany?: Prisma.ProfissionalEspecialidadeScalarWhereInput | Prisma.ProfissionalEspecialidadeScalarWhereInput[]
}

export type ProfissionalEspecialidadeUncheckedUpdateManyWithoutEspecialidadeNestedInput = {
  create?: Prisma.XOR<Prisma.ProfissionalEspecialidadeCreateWithoutEspecialidadeInput, Prisma.ProfissionalEspecialidadeUncheckedCreateWithoutEspecialidadeInput> | Prisma.ProfissionalEspecialidadeCreateWithoutEspecialidadeInput[] | Prisma.ProfissionalEspecialidadeUncheckedCreateWithoutEspecialidadeInput[]
  connectOrCreate?: Prisma.ProfissionalEspecialidadeCreateOrConnectWithoutEspecialidadeInput | Prisma.ProfissionalEspecialidadeCreateOrConnectWithoutEspecialidadeInput[]
  upsert?: Prisma.ProfissionalEspecialidadeUpsertWithWhereUniqueWithoutEspecialidadeInput | Prisma.ProfissionalEspecialidadeUpsertWithWhereUniqueWithoutEspecialidadeInput[]
  createMany?: Prisma.ProfissionalEspecialidadeCreateManyEspecialidadeInputEnvelope
  set?: Prisma.ProfissionalEspecialidadeWhereUniqueInput | Prisma.ProfissionalEspecialidadeWhereUniqueInput[]
  disconnect?: Prisma.ProfissionalEspecialidadeWhereUniqueInput | Prisma.ProfissionalEspecialidadeWhereUniqueInput[]
  delete?: Prisma.ProfissionalEspecialidadeWhereUniqueInput | Prisma.ProfissionalEspecialidadeWhereUniqueInput[]
  connect?: Prisma.ProfissionalEspecialidadeWhereUniqueInput | Prisma.ProfissionalEspecialidadeWhereUniqueInput[]
  update?: Prisma.ProfissionalEspecialidadeUpdateWithWhereUniqueWithoutEspecialidadeInput | Prisma.ProfissionalEspecialidadeUpdateWithWhereUniqueWithoutEspecialidadeInput[]
  updateMany?: Prisma.ProfissionalEspecialidadeUpdateManyWithWhereWithoutEspecialidadeInput | Prisma.ProfissionalEspecialidadeUpdateManyWithWhereWithoutEspecialidadeInput[]
  deleteMany?: Prisma.ProfissionalEspecialidadeScalarWhereInput | Prisma.ProfissionalEspecialidadeScalarWhereInput[]
}

export type ProfissionalEspecialidadeCreateNestedManyWithoutProfissionalInput = {
  create?: Prisma.XOR<Prisma.ProfissionalEspecialidadeCreateWithoutProfissionalInput, Prisma.ProfissionalEspecialidadeUncheckedCreateWithoutProfissionalInput> | Prisma.ProfissionalEspecialidadeCreateWithoutProfissionalInput[] | Prisma.ProfissionalEspecialidadeUncheckedCreateWithoutProfissionalInput[]
  connectOrCreate?: Prisma.ProfissionalEspecialidadeCreateOrConnectWithoutProfissionalInput | Prisma.ProfissionalEspecialidadeCreateOrConnectWithoutProfissionalInput[]
  createMany?: Prisma.ProfissionalEspecialidadeCreateManyProfissionalInputEnvelope
  connect?: Prisma.ProfissionalEspecialidadeWhereUniqueInput | Prisma.ProfissionalEspecialidadeWhereUniqueInput[]
}

export type ProfissionalEspecialidadeUncheckedCreateNestedManyWithoutProfissionalInput = {
  create?: Prisma.XOR<Prisma.ProfissionalEspecialidadeCreateWithoutProfissionalInput, Prisma.ProfissionalEspecialidadeUncheckedCreateWithoutProfissionalInput> | Prisma.ProfissionalEspecialidadeCreateWithoutProfissionalInput[] | Prisma.ProfissionalEspecialidadeUncheckedCreateWithoutProfissionalInput[]
  connectOrCreate?: Prisma.ProfissionalEspecialidadeCreateOrConnectWithoutProfissionalInput | Prisma.ProfissionalEspecialidadeCreateOrConnectWithoutProfissionalInput[]
  createMany?: Prisma.ProfissionalEspecialidadeCreateManyProfissionalInputEnvelope
  connect?: Prisma.ProfissionalEspecialidadeWhereUniqueInput | Prisma.ProfissionalEspecialidadeWhereUniqueInput[]
}

export type ProfissionalEspecialidadeUpdateManyWithoutProfissionalNestedInput = {
  create?: Prisma.XOR<Prisma.ProfissionalEspecialidadeCreateWithoutProfissionalInput, Prisma.ProfissionalEspecialidadeUncheckedCreateWithoutProfissionalInput> | Prisma.ProfissionalEspecialidadeCreateWithoutProfissionalInput[] | Prisma.ProfissionalEspecialidadeUncheckedCreateWithoutProfissionalInput[]
  connectOrCreate?: Prisma.ProfissionalEspecialidadeCreateOrConnectWithoutProfissionalInput | Prisma.ProfissionalEspecialidadeCreateOrConnectWithoutProfissionalInput[]
  upsert?: Prisma.ProfissionalEspecialidadeUpsertWithWhereUniqueWithoutProfissionalInput | Prisma.ProfissionalEspecialidadeUpsertWithWhereUniqueWithoutProfissionalInput[]
  createMany?: Prisma.ProfissionalEspecialidadeCreateManyProfissionalInputEnvelope
  set?: Prisma.ProfissionalEspecialidadeWhereUniqueInput | Prisma.ProfissionalEspecialidadeWhereUniqueInput[]
  disconnect?: Prisma.ProfissionalEspecialidadeWhereUniqueInput | Prisma.ProfissionalEspecialidadeWhereUniqueInput[]
  delete?: Prisma.ProfissionalEspecialidadeWhereUniqueInput | Prisma.ProfissionalEspecialidadeWhereUniqueInput[]
  connect?: Prisma.ProfissionalEspecialidadeWhereUniqueInput | Prisma.ProfissionalEspecialidadeWhereUniqueInput[]
  update?: Prisma.ProfissionalEspecialidadeUpdateWithWhereUniqueWithoutProfissionalInput | Prisma.ProfissionalEspecialidadeUpdateWithWhereUniqueWithoutProfissionalInput[]
  updateMany?: Prisma.ProfissionalEspecialidadeUpdateManyWithWhereWithoutProfissionalInput | Prisma.ProfissionalEspecialidadeUpdateManyWithWhereWithoutProfissionalInput[]
  deleteMany?: Prisma.ProfissionalEspecialidadeScalarWhereInput | Prisma.ProfissionalEspecialidadeScalarWhereInput[]
}

export type ProfissionalEspecialidadeUncheckedUpdateManyWithoutProfissionalNestedInput = {
  create?: Prisma.XOR<Prisma.ProfissionalEspecialidadeCreateWithoutProfissionalInput, Prisma.ProfissionalEspecialidadeUncheckedCreateWithoutProfissionalInput> | Prisma.ProfissionalEspecialidadeCreateWithoutProfissionalInput[] | Prisma.ProfissionalEspecialidadeUncheckedCreateWithoutProfissionalInput[]
  connectOrCreate?: Prisma.ProfissionalEspecialidadeCreateOrConnectWithoutProfissionalInput | Prisma.ProfissionalEspecialidadeCreateOrConnectWithoutProfissionalInput[]
  upsert?: Prisma.ProfissionalEspecialidadeUpsertWithWhereUniqueWithoutProfissionalInput | Prisma.ProfissionalEspecialidadeUpsertWithWhereUniqueWithoutProfissionalInput[]
  createMany?: Prisma.ProfissionalEspecialidadeCreateManyProfissionalInputEnvelope
  set?: Prisma.ProfissionalEspecialidadeWhereUniqueInput | Prisma.ProfissionalEspecialidadeWhereUniqueInput[]
  disconnect?: Prisma.ProfissionalEspecialidadeWhereUniqueInput | Prisma.ProfissionalEspecialidadeWhereUniqueInput[]
  delete?: Prisma.ProfissionalEspecialidadeWhereUniqueInput | Prisma.ProfissionalEspecialidadeWhereUniqueInput[]
  connect?: Prisma.ProfissionalEspecialidadeWhereUniqueInput | Prisma.ProfissionalEspecialidadeWhereUniqueInput[]
  update?: Prisma.ProfissionalEspecialidadeUpdateWithWhereUniqueWithoutProfissionalInput | Prisma.ProfissionalEspecialidadeUpdateWithWhereUniqueWithoutProfissionalInput[]
  updateMany?: Prisma.ProfissionalEspecialidadeUpdateManyWithWhereWithoutProfissionalInput | Prisma.ProfissionalEspecialidadeUpdateManyWithWhereWithoutProfissionalInput[]
  deleteMany?: Prisma.ProfissionalEspecialidadeScalarWhereInput | Prisma.ProfissionalEspecialidadeScalarWhereInput[]
}

export type ProfissionalEspecialidadeCreateWithoutEspecialidadeInput = {
  createdAt?: Date | string
  profissional: Prisma.ProfissionalCreateNestedOneWithoutEspecialidadesInput
}

export type ProfissionalEspecialidadeUncheckedCreateWithoutEspecialidadeInput = {
  id?: number
  profissionalId: number
  createdAt?: Date | string
}

export type ProfissionalEspecialidadeCreateOrConnectWithoutEspecialidadeInput = {
  where: Prisma.ProfissionalEspecialidadeWhereUniqueInput
  create: Prisma.XOR<Prisma.ProfissionalEspecialidadeCreateWithoutEspecialidadeInput, Prisma.ProfissionalEspecialidadeUncheckedCreateWithoutEspecialidadeInput>
}

export type ProfissionalEspecialidadeCreateManyEspecialidadeInputEnvelope = {
  data: Prisma.ProfissionalEspecialidadeCreateManyEspecialidadeInput | Prisma.ProfissionalEspecialidadeCreateManyEspecialidadeInput[]
  skipDuplicates?: boolean
}

export type ProfissionalEspecialidadeUpsertWithWhereUniqueWithoutEspecialidadeInput = {
  where: Prisma.ProfissionalEspecialidadeWhereUniqueInput
  update: Prisma.XOR<Prisma.ProfissionalEspecialidadeUpdateWithoutEspecialidadeInput, Prisma.ProfissionalEspecialidadeUncheckedUpdateWithoutEspecialidadeInput>
  create: Prisma.XOR<Prisma.ProfissionalEspecialidadeCreateWithoutEspecialidadeInput, Prisma.ProfissionalEspecialidadeUncheckedCreateWithoutEspecialidadeInput>
}

export type ProfissionalEspecialidadeUpdateWithWhereUniqueWithoutEspecialidadeInput = {
  where: Prisma.ProfissionalEspecialidadeWhereUniqueInput
  data: Prisma.XOR<Prisma.ProfissionalEspecialidadeUpdateWithoutEspecialidadeInput, Prisma.ProfissionalEspecialidadeUncheckedUpdateWithoutEspecialidadeInput>
}

export type ProfissionalEspecialidadeUpdateManyWithWhereWithoutEspecialidadeInput = {
  where: Prisma.ProfissionalEspecialidadeScalarWhereInput
  data: Prisma.XOR<Prisma.ProfissionalEspecialidadeUpdateManyMutationInput, Prisma.ProfissionalEspecialidadeUncheckedUpdateManyWithoutEspecialidadeInput>
}

export type ProfissionalEspecialidadeScalarWhereInput = {
  AND?: Prisma.ProfissionalEspecialidadeScalarWhereInput | Prisma.ProfissionalEspecialidadeScalarWhereInput[]
  OR?: Prisma.ProfissionalEspecialidadeScalarWhereInput[]
  NOT?: Prisma.ProfissionalEspecialidadeScalarWhereInput | Prisma.ProfissionalEspecialidadeScalarWhereInput[]
  id?: Prisma.IntFilter<"ProfissionalEspecialidade"> | number
  profissionalId?: Prisma.IntFilter<"ProfissionalEspecialidade"> | number
  especialidadeId?: Prisma.IntFilter<"ProfissionalEspecialidade"> | number
  createdAt?: Prisma.DateTimeFilter<"ProfissionalEspecialidade"> | Date | string
}

export type ProfissionalEspecialidadeCreateWithoutProfissionalInput = {
  createdAt?: Date | string
  especialidade: Prisma.EspecialidadeCreateNestedOneWithoutProfissionaisInput
}

export type ProfissionalEspecialidadeUncheckedCreateWithoutProfissionalInput = {
  id?: number
  especialidadeId: number
  createdAt?: Date | string
}

export type ProfissionalEspecialidadeCreateOrConnectWithoutProfissionalInput = {
  where: Prisma.ProfissionalEspecialidadeWhereUniqueInput
  create: Prisma.XOR<Prisma.ProfissionalEspecialidadeCreateWithoutProfissionalInput, Prisma.ProfissionalEspecialidadeUncheckedCreateWithoutProfissionalInput>
}

export type ProfissionalEspecialidadeCreateManyProfissionalInputEnvelope = {
  data: Prisma.ProfissionalEspecialidadeCreateManyProfissionalInput | Prisma.ProfissionalEspecialidadeCreateManyProfissionalInput[]
  skipDuplicates?: boolean
}

export type ProfissionalEspecialidadeUpsertWithWhereUniqueWithoutProfissionalInput = {
  where: Prisma.ProfissionalEspecialidadeWhereUniqueInput
  update: Prisma.XOR<Prisma.ProfissionalEspecialidadeUpdateWithoutProfissionalInput, Prisma.ProfissionalEspecialidadeUncheckedUpdateWithoutProfissionalInput>
  create: Prisma.XOR<Prisma.ProfissionalEspecialidadeCreateWithoutProfissionalInput, Prisma.ProfissionalEspecialidadeUncheckedCreateWithoutProfissionalInput>
}

export type ProfissionalEspecialidadeUpdateWithWhereUniqueWithoutProfissionalInput = {
  where: Prisma.ProfissionalEspecialidadeWhereUniqueInput
  data: Prisma.XOR<Prisma.ProfissionalEspecialidadeUpdateWithoutProfissionalInput, Prisma.ProfissionalEspecialidadeUncheckedUpdateWithoutProfissionalInput>
}

export type ProfissionalEspecialidadeUpdateManyWithWhereWithoutProfissionalInput = {
  where: Prisma.ProfissionalEspecialidadeScalarWhereInput
  data: Prisma.XOR<Prisma.ProfissionalEspecialidadeUpdateManyMutationInput, Prisma.ProfissionalEspecialidadeUncheckedUpdateManyWithoutProfissionalInput>
}

export type ProfissionalEspecialidadeCreateManyEspecialidadeInput = {
  id?: number
  profissionalId: number
  createdAt?: Date | string
}

export type ProfissionalEspecialidadeUpdateWithoutEspecialidadeInput = {
  createdAt?: Prisma.DateTimeFieldUpdateOperationsInput | Date | string
  profissional?: Prisma.ProfissionalUpdateOneRequiredWithoutEspecialidadesNestedInput
}

export type ProfissionalEspecialidadeUncheckedUpdateWithoutEspecialidadeInput = {
  id?: Prisma.IntFieldUpdateOperationsInput | number
  profissionalId?: Prisma.IntFieldUpdateOperationsInput | number
  createdAt?: Prisma.DateTimeFieldUpdateOperationsInput | Date | string
}

export type ProfissionalEspecialidadeUncheckedUpdateManyWithoutEspecialidadeInput = {
  id?: Prisma.IntFieldUpdateOperationsInput | number
  profissionalId?: Prisma.IntFieldUpdateOperationsInput | number
  createdAt?: Prisma.DateTimeFieldUpdateOperationsInput | Date | string
}

export type ProfissionalEspecialidadeCreateManyProfissionalInput = {
  id?: number
  especialidadeId: number
  createdAt?: Date | string
}

export type ProfissionalEspecialidadeUpdateWithoutProfissionalInput = {
  createdAt?: Prisma.DateTimeFieldUpdateOperationsInput | Date | string
  especialidade?: Prisma.EspecialidadeUpdateOneRequiredWithoutProfissionaisNestedInput
}

export type ProfissionalEspecialidadeUncheckedUpdateWithoutProfissionalInput = {
  id?: Prisma.IntFieldUpdateOperationsInput | number
  especialidadeId?: Prisma.IntFieldUpdateOperationsInput | number
  createdAt?: Prisma.DateTimeFieldUpdateOperationsInput | Date | string
}

export type ProfissionalEspecialidadeUncheckedUpdateManyWithoutProfissionalInput = {
  id?: Prisma.IntFieldUpdateOperationsInput | number
  especialidadeId?: Prisma.IntFieldUpdateOperationsInput | number
  createdAt?: Prisma.DateTimeFieldUpdateOperationsInput | Date | string
}



export type ProfissionalEspecialidadeSelect<ExtArgs extends runtime.Types.Extensions.InternalArgs = runtime.Types.Extensions.DefaultArgs> = runtime.Types.Extensions.GetSelect<{
  id?: boolean
  profissionalId?: boolean
  especialidadeId?: boolean
  createdAt?: boolean
  profissional?: boolean | Prisma.ProfissionalDefaultArgs<ExtArgs>
  especialidade?: boolean | Prisma.EspecialidadeDefaultArgs<ExtArgs>
}, ExtArgs["result"]["profissionalEspecialidade"]>



export type ProfissionalEspecialidadeSelectScalar = {
  id?: boolean
  profissionalId?: boolean
  especialidadeId?: boolean
  createdAt?: boolean
}

export type ProfissionalEspecialidadeOmit<ExtArgs extends runtime.Types.Extensions.InternalArgs = runtime.Types.Extensions.DefaultArgs> = runtime.Types.Extensions.GetOmit<"id" | "profissionalId" | "especialidadeId" | "createdAt", ExtArgs["result"]["profissionalEspecialidade"]>
export type ProfissionalEspecialidadeInclude<ExtArgs extends runtime.Types.Extensions.InternalArgs = runtime.Types.Extensions.DefaultArgs> = {
  profissional?: boolean | Prisma.ProfissionalDefaultArgs<ExtArgs>
  especialidade?: boolean | Prisma.EspecialidadeDefaultArgs<ExtArgs>
}

export type $ProfissionalEspecialidadePayload<ExtArgs extends runtime.Types.Extensions.InternalArgs = runtime.Types.Extensions.DefaultArgs> = {
  name: "ProfissionalEspecialidade"
  objects: {
    profissional: Prisma.$ProfissionalPayload<ExtArgs>
    especialidade: Prisma.$EspecialidadePayload<ExtArgs>
  }
  scalars: runtime.Types.Extensions.GetPayloadResult<{
    id: number
    profissionalId: number
    especialidadeId: number
    createdAt: Date
  }, ExtArgs["result"]["profissionalEspecialidade"]>
  composites: {}
}

export type ProfissionalEspecialidadeGetPayload<S extends boolean | null | undefined | ProfissionalEspecialidadeDefaultArgs> = runtime.Types.Result.GetResult<Prisma.$ProfissionalEspecialidadePayload, S>

export type ProfissionalEspecialidadeCountArgs<ExtArgs extends runtime.Types.Extensions.InternalArgs = runtime.Types.Extensions.DefaultArgs> =
  Omit<ProfissionalEspecialidadeFindManyArgs, 'select' | 'include' | 'distinct' | 'omit'> & {
    select?: ProfissionalEspecialidadeCountAggregateInputType | true
  }

export interface ProfissionalEspecialidadeDelegate<ExtArgs extends runtime.Types.Extensions.InternalArgs = runtime.Types.Extensions.DefaultArgs, GlobalOmitOptions = {}> {
  [K: symbol]: { types: Prisma.TypeMap<ExtArgs>['model']['ProfissionalEspecialidade'], meta: { name: 'ProfissionalEspecialidade' } }
  /**
   * Find zero or one ProfissionalEspecialidade that matches the filter.
   * @param {ProfissionalEspecialidadeFindUniqueArgs} args - Arguments to find a ProfissionalEspecialidade
   * @example
   * // Get one ProfissionalEspecialidade
   * const profissionalEspecialidade = await prisma.profissionalEspecialidade.findUnique({
   *   where: {
   *     // ... provide filter here
   *   }
   * })
   */
  findUnique<T extends ProfissionalEspecialidadeFindUniqueArgs>(args: Prisma.SelectSubset<T, ProfissionalEspecialidadeFindUniqueArgs<ExtArgs>>): Prisma.Prisma__ProfissionalEspecialidadeClient<runtime.Types.Result.GetResult<Prisma.$ProfissionalEspecialidadePayload<ExtArgs>, T, "findUnique", GlobalOmitOptions> | null, null, ExtArgs, GlobalOmitOptions>

  /**
   * Find one ProfissionalEspecialidade that matches the filter or throw an error with `error.code='P2025'`
   * if no matches were found.
   * @param {ProfissionalEspecialidadeFindUniqueOrThrowArgs} args - Arguments to find a ProfissionalEspecialidade
   * @example
   * // Get one ProfissionalEspecialidade
   * const profissionalEspecialidade = await prisma.profissionalEspecialidade.findUniqueOrThrow({
   *   where: {
   *     // ... provide filter here
   *   }
   * })
   */
  findUniqueOrThrow<T extends ProfissionalEspecialidadeFindUniqueOrThrowArgs>(args: Prisma.SelectSubset<T, ProfissionalEspecialidadeFindUniqueOrThrowArgs<ExtArgs>>): Prisma.Prisma__ProfissionalEspecialidadeClient<runtime.Types.Result.GetResult<Prisma.$ProfissionalEspecialidadePayload<ExtArgs>, T, "findUniqueOrThrow", GlobalOmitOptions>, never, ExtArgs, GlobalOmitOptions>

  /**
   * Find the first ProfissionalEspecialidade that matches the filter.
   * Note, that providing `undefined` is treated as the value not being there.
   * Read more here: https://pris.ly/d/null-undefined
   * @param {ProfissionalEspecialidadeFindFirstArgs} args - Arguments to find a ProfissionalEspecialidade
   * @example
   * // Get one ProfissionalEspecialidade
   * const profissionalEspecialidade = await prisma.profissionalEspecialidade.findFirst({
   *   where: {
   *     // ... provide filter here
   *   }
   * })
   */
  findFirst<T extends ProfissionalEspecialidadeFindFirstArgs>(args?: Prisma.SelectSubset<T, ProfissionalEspecialidadeFindFirstArgs<ExtArgs>>): Prisma.Prisma__ProfissionalEspecialidadeClient<runtime.Types.Result.GetResult<Prisma.$ProfissionalEspecialidadePayload<ExtArgs>, T, "findFirst", GlobalOmitOptions> | null, null, ExtArgs, GlobalOmitOptions>

  /**
   * Find the first ProfissionalEspecialidade that matches the filter or
   * throw `PrismaKnownClientError` with `P2025` code if no matches were found.
   * Note, that providing `undefined` is treated as the value not being there.
   * Read more here: https://pris.ly/d/null-undefined
   * @param {ProfissionalEspecialidadeFindFirstOrThrowArgs} args - Arguments to find a ProfissionalEspecialidade
   * @example
   * // Get one ProfissionalEspecialidade
   * const profissionalEspecialidade = await prisma.profissionalEspecialidade.findFirstOrThrow({
   *   where: {
   *     // ... provide filter here
   *   }
   * })
   */
  findFirstOrThrow<T extends ProfissionalEspecialidadeFindFirstOrThrowArgs>(args?: Prisma.SelectSubset<T, ProfissionalEspecialidadeFindFirstOrThrowArgs<ExtArgs>>): Prisma.Prisma__ProfissionalEspecialidadeClient<runtime.Types.Result.GetResult<Prisma.$ProfissionalEspecialidadePayload<ExtArgs>, T, "findFirstOrThrow", GlobalOmitOptions>, never, ExtArgs, GlobalOmitOptions>

  /**
   * Find zero or more ProfissionalEspecialidades that matches the filter.
   * Note, that providing `undefined` is treated as the value not being there.
   * Read more here: https://pris.ly/d/null-undefined
   * @param {ProfissionalEspecialidadeFindManyArgs} args - Arguments to filter and select certain fields only.
   * @example
   * // Get all ProfissionalEspecialidades
   * const profissionalEspecialidades = await prisma.profissionalEspecialidade.findMany()
   * 
   * // Get first 10 ProfissionalEspecialidades
   * const profissionalEspecialidades = await prisma.profissionalEspecialidade.findMany({ take: 10 })
   * 
   * // Only select the `id`
   * const profissionalEspecialidadeWithIdOnly = await prisma.profissionalEspecialidade.findMany({ select: { id: true } })
   * 
   */
  findMany<T extends ProfissionalEspecialidadeFindManyArgs>(args?: Prisma.SelectSubset<T, ProfissionalEspecialidadeFindManyArgs<ExtArgs>>): Prisma.PrismaPromise<runtime.Types.Result.GetResult<Prisma.$ProfissionalEspecialidadePayload<ExtArgs>, T, "findMany", GlobalOmitOptions>>

  /**
   * Create a ProfissionalEspecialidade.
   * @param {ProfissionalEspecialidadeCreateArgs} args - Arguments to create a ProfissionalEspecialidade.
   * @example
   * // Create one ProfissionalEspecialidade
   * const ProfissionalEspecialidade = await prisma.profissionalEspecialidade.create({
   *   data: {
   *     // ... data to create a ProfissionalEspecialidade
   *   }
   * })
   * 
   */
  create<T extends ProfissionalEspecialidadeCreateArgs>(args: Prisma.SelectSubset<T, ProfissionalEspecialidadeCreateArgs<ExtArgs>>): Prisma.Prisma__ProfissionalEspecialidadeClient<runtime.Types.Result.GetResult<Prisma.$ProfissionalEspecialidadePayload<ExtArgs>, T, "create", GlobalOmitOptions>, never, ExtArgs, GlobalOmitOptions>

  /**
   * Create many ProfissionalEspecialidades.
   * @param {ProfissionalEspecialidadeCreateManyArgs} args - Arguments to create many ProfissionalEspecialidades.
   * @example
   * // Create many ProfissionalEspecialidades
   * const profissionalEspecialidade = await prisma.profissionalEspecialidade.createMany({
   *   data: [
   *     // ... provide data here
   *   ]
   * })
   *     
   */
  createMany<T extends ProfissionalEspecialidadeCreateManyArgs>(args?: Prisma.SelectSubset<T, ProfissionalEspecialidadeCreateManyArgs<ExtArgs>>): Prisma.PrismaPromise<Prisma.BatchPayload>

  /**
   * Delete a ProfissionalEspecialidade.
   * @param {ProfissionalEspecialidadeDeleteArgs} args - Arguments to delete one ProfissionalEspecialidade.
   * @example
   * // Delete one ProfissionalEspecialidade
   * const ProfissionalEspecialidade = await prisma.profissionalEspecialidade.delete({
   *   where: {
   *     // ... filter to delete one ProfissionalEspecialidade
   *   }
   * })
   * 
   */
  delete<T extends ProfissionalEspecialidadeDeleteArgs>(args: Prisma.SelectSubset<T, ProfissionalEspecialidadeDeleteArgs<ExtArgs>>): Prisma.Prisma__ProfissionalEspecialidadeClient<runtime.Types.Result.GetResult<Prisma.$ProfissionalEspecialidadePayload<ExtArgs>, T, "delete", GlobalOmitOptions>, never, ExtArgs, GlobalOmitOptions>

  /**
   * Update one ProfissionalEspecialidade.
   * @param {ProfissionalEspecialidadeUpdateArgs} args - Arguments to update one ProfissionalEspecialidade.
   * @example
   * // Update one ProfissionalEspecialidade
   * const profissionalEspecialidade = await prisma.profissionalEspecialidade.update({
   *   where: {
   *     // ... provide filter here
   *   },
   *   data: {
   *     // ... provide data here
   *   }
   * })
   * 
   */
  update<T extends ProfissionalEspecialidadeUpdateArgs>(args: Prisma.SelectSubset<T, ProfissionalEspecialidadeUpdateArgs<ExtArgs>>): Prisma.Prisma__ProfissionalEspecialidadeClient<runtime.Types.Result.GetResult<Prisma.$ProfissionalEspecialidadePayload<ExtArgs>, T, "update", GlobalOmitOptions>, never, ExtArgs, GlobalOmitOptions>

  /**
   * Delete zero or more ProfissionalEspecialidades.
   * @param {ProfissionalEspecialidadeDeleteManyArgs} args - Arguments to filter ProfissionalEspecialidades to delete.
   * @example
   * // Delete a few ProfissionalEspecialidades
   * const { count } = await prisma.profissionalEspecialidade.deleteMany({
   *   where: {
   *     // ... provide filter here
   *   }
   * })
   * 
   */
  deleteMany<T extends ProfissionalEspecialidadeDeleteManyArgs>(args?: Prisma.SelectSubset<T, ProfissionalEspecialidadeDeleteManyArgs<ExtArgs>>): Prisma.PrismaPromise<Prisma.BatchPayload>

  /**
   * Update zero or more ProfissionalEspecialidades.
   * Note, that providing `undefined` is treated as the value not being there.
   * Read more here: https://pris.ly/d/null-undefined
   * @param {ProfissionalEspecialidadeUpdateManyArgs} args - Arguments to update one or more rows.
   * @example
   * // Update many ProfissionalEspecialidades
   * const profissionalEspecialidade = await prisma.profissionalEspecialidade.updateMany({
   *   where: {
   *     // ... provide filter here
   *   },
   *   data: {
   *     // ... provide data here
   *   }
   * })
   * 
   */
  updateMany<T extends ProfissionalEspecialidadeUpdateManyArgs>(args: Prisma.SelectSubset<T, ProfissionalEspecialidadeUpdateManyArgs<ExtArgs>>): Prisma.PrismaPromise<Prisma.BatchPayload>

  /**
   * Create or update one ProfissionalEspecialidade.
   * @param {ProfissionalEspecialidadeUpsertArgs} args - Arguments to update or create a ProfissionalEspecialidade.
   * @example
   * // Update or create a ProfissionalEspecialidade
   * const profissionalEspecialidade = await prisma.profissionalEspecialidade.upsert({
   *   create: {
   *     // ... data to create a ProfissionalEspecialidade
   *   },
   *   update: {
   *     // ... in case it already exists, update
   *   },
   *   where: {
   *     // ... the filter for the ProfissionalEspecialidade we want to update
   *   }
   * })
   */
  upsert<T extends ProfissionalEspecialidadeUpsertArgs>(args: Prisma.SelectSubset<T, ProfissionalEspecialidadeUpsertArgs<ExtArgs>>): Prisma.Prisma__ProfissionalEspecialidadeClient<runtime.Types.Result.GetResult<Prisma.$ProfissionalEspecialidadePayload<ExtArgs>, T, "upsert", GlobalOmitOptions>, never, ExtArgs, GlobalOmitOptions>


  /**
   * Count the number of ProfissionalEspecialidades.
   * Note, that providing `undefined` is treated as the value not being there.
   * Read more here: https://pris.ly/d/null-undefined
   * @param {ProfissionalEspecialidadeCountArgs} args - Arguments to filter ProfissionalEspecialidades to count.
   * @example
   * // Count the number of ProfissionalEspecialidades
   * const count = await prisma.profissionalEspecialidade.count({
   *   where: {
   *     // ... the filter for the ProfissionalEspecialidades we want to count
   *   }
   * })
  **/
  count<T extends ProfissionalEspecialidadeCountArgs>(
    args?: Prisma.Subset<T, ProfissionalEspecialidadeCountArgs>,
  ): Prisma.PrismaPromise<
    T extends runtime.Types.Utils.Record<'select', any>
      ? T['select'] extends true
        ? number
        : Prisma.GetScalarType<T['select'], ProfissionalEspecialidadeCountAggregateOutputType>
      : number
  >

  /**
   * Allows you to perform aggregations operations on a ProfissionalEspecialidade.
   * Note, that providing `undefined` is treated as the value not being there.
   * Read more here: https://pris.ly/d/null-undefined
   * @param {ProfissionalEspecialidadeAggregateArgs} args - Select which aggregations you would like to apply and on what fields.
   * @example
   * // Ordered by age ascending
   * // Where email contains prisma.io
   * // Limited to the 10 users
   * const aggregations = await prisma.user.aggregate({
   *   _avg: {
   *     age: true,
   *   },
   *   where: {
   *     email: {
   *       contains: "prisma.io",
   *     },
   *   },
   *   orderBy: {
   *     age: "asc",
   *   },
   *   take: 10,
   * })
  **/
  aggregate<T extends ProfissionalEspecialidadeAggregateArgs>(args: Prisma.Subset<T, ProfissionalEspecialidadeAggregateArgs>): Prisma.PrismaPromise<GetProfissionalEspecialidadeAggregateType<T>>

  /**
   * Group by ProfissionalEspecialidade.
   * Note, that providing `undefined` is treated as the value not being there.
   * Read more here: https://pris.ly/d/null-undefined
   * @param {ProfissionalEspecialidadeGroupByArgs} args - Group by arguments.
   * @example
   * // Group by city, order by createdAt, get count
   * const result = await prisma.user.groupBy({
   *   by: ['city', 'createdAt'],
   *   orderBy: {
   *     createdAt: true
   *   },
   *   _count: {
   *     _all: true
   *   },
   * })
   * 
  **/
  groupBy<
    T extends ProfissionalEspecialidadeGroupByArgs,
    HasSelectOrTake extends Prisma.Or<
      Prisma.Extends<'skip', Prisma.Keys<T>>,
      Prisma.Extends<'take', Prisma.Keys<T>>
    >,
    OrderByArg extends Prisma.True extends HasSelectOrTake
      ? { orderBy: ProfissionalEspecialidadeGroupByArgs['orderBy'] }
      : { orderBy?: ProfissionalEspecialidadeGroupByArgs['orderBy'] },
    OrderFields extends Prisma.ExcludeUnderscoreKeys<Prisma.Keys<Prisma.MaybeTupleToUnion<T['orderBy']>>>,
    ByFields extends Prisma.MaybeTupleToUnion<T['by']>,
    ByValid extends Prisma.Has<ByFields, OrderFields>,
    HavingFields extends Prisma.GetHavingFields<T['having']>,
    HavingValid extends Prisma.Has<ByFields, HavingFields>,
    ByEmpty extends T['by'] extends never[] ? Prisma.True : Prisma.False,
    InputErrors extends ByEmpty extends Prisma.True
    ? `Error: "by" must not be empty.`
    : HavingValid extends Prisma.False
    ? {
        [P in HavingFields]: P extends ByFields
          ? never
          : P extends string
          ? `Error: Field "${P}" used in "having" needs to be provided in "by".`
          : [
              Error,
              'Field ',
              P,
              ` in "having" needs to be provided in "by"`,
            ]
      }[HavingFields]
    : 'take' extends Prisma.Keys<T>
    ? 'orderBy' extends Prisma.Keys<T>
      ? ByValid extends Prisma.True
        ? {}
        : {
            [P in OrderFields]: P extends ByFields
              ? never
              : `Error: Field "${P}" in "orderBy" needs to be provided in "by"`
          }[OrderFields]
      : 'Error: If you provide "take", you also need to provide "orderBy"'
    : 'skip' extends Prisma.Keys<T>
    ? 'orderBy' extends Prisma.Keys<T>
      ? ByValid extends Prisma.True
        ? {}
        : {
            [P in OrderFields]: P extends ByFields
              ? never
              : `Error: Field "${P}" in "orderBy" needs to be provided in "by"`
          }[OrderFields]
      : 'Error: If you provide "skip", you also need to provide "orderBy"'
    : ByValid extends Prisma.True
    ? {}
    : {
        [P in OrderFields]: P extends ByFields
          ? never
          : `Error: Field "${P}" in "orderBy" needs to be provided in "by"`
      }[OrderFields]
  >(args: Prisma.SubsetIntersection<T, ProfissionalEspecialidadeGroupByArgs, OrderByArg> & InputErrors): {} extends InputErrors ? GetProfissionalEspecialidadeGroupByPayload<T> : Prisma.PrismaPromise<InputErrors>
/**
 * Fields of the ProfissionalEspecialidade model
 */
readonly fields: ProfissionalEspecialidadeFieldRefs;
}

/**
 * The delegate class that acts as a "Promise-like" for ProfissionalEspecialidade.
 * Why is this prefixed with `Prisma__`?
 * Because we want to prevent naming conflicts as mentioned in
 * https://github.com/prisma/prisma-client-js/issues/707
 */
export interface Prisma__ProfissionalEspecialidadeClient<T, Null = never, ExtArgs extends runtime.Types.Extensions.InternalArgs = runtime.Types.Extensions.DefaultArgs, GlobalOmitOptions = {}> extends Prisma.PrismaPromise<T> {
  readonly [Symbol.toStringTag]: "PrismaPromise"
  profissional<T extends Prisma.ProfissionalDefaultArgs<ExtArgs> = {}>(args?: Prisma.Subset<T, Prisma.ProfissionalDefaultArgs<ExtArgs>>): Prisma.Prisma__ProfissionalClient<runtime.Types.Result.GetResult<Prisma.$ProfissionalPayload<ExtArgs>, T, "findUniqueOrThrow", GlobalOmitOptions> | Null, Null, ExtArgs, GlobalOmitOptions>
  especialidade<T extends Prisma.EspecialidadeDefaultArgs<ExtArgs> = {}>(args?: Prisma.Subset<T, Prisma.EspecialidadeDefaultArgs<ExtArgs>>): Prisma.Prisma__EspecialidadeClient<runtime.Types.Result.GetResult<Prisma.$EspecialidadePayload<ExtArgs>, T, "findUniqueOrThrow", GlobalOmitOptions> | Null, Null, ExtArgs, GlobalOmitOptions>
  /**
   * Attaches callbacks for the resolution and/or rejection of the Promise.
   * @param onfulfilled The callback to execute when the Promise is resolved.
   * @param onrejected The callback to execute when the Promise is rejected.
   * @returns A Promise for the completion of which ever callback is executed.
   */
  then<TResult1 = T, TResult2 = never>(onfulfilled?: ((value: T) => TResult1 | PromiseLike<TResult1>) | undefined | null, onrejected?: ((reason: any) => TResult2 | PromiseLike<TResult2>) | undefined | null): runtime.Types.Utils.JsPromise<TResult1 | TResult2>
  /**
   * Attaches a callback for only the rejection of the Promise.
   * @param onrejected The callback to execute when the Promise is rejected.
   * @returns A Promise for the completion of the callback.
   */
  catch<TResult = never>(onrejected?: ((reason: any) => TResult | PromiseLike<TResult>) | undefined | null): runtime.Types.Utils.JsPromise<T | TResult>
  /**
   * Attaches a callback that is invoked when the Promise is settled (fulfilled or rejected). The
   * resolved value cannot be modified from the callback.
   * @param onfinally The callback to execute when the Promise is settled (fulfilled or rejected).
   * @returns A Promise for the completion of the callback.
   */
  finally(onfinally?: (() => void) | undefined | null): runtime.Types.Utils.JsPromise<T>
}




/**
 * Fields of the ProfissionalEspecialidade model
 */
export interface ProfissionalEspecialidadeFieldRefs {
  readonly id: Prisma.FieldRef<"ProfissionalEspecialidade", 'Int'>
  readonly profissionalId: Prisma.FieldRef<"ProfissionalEspecialidade", 'Int'>
  readonly especialidadeId: Prisma.FieldRef<"ProfissionalEspecialidade", 'Int'>
  readonly createdAt: Prisma.FieldRef<"ProfissionalEspecialidade", 'DateTime'>
}
    

// Custom InputTypes
/**
 * ProfissionalEspecialidade findUnique
 */
export type ProfissionalEspecialidadeFindUniqueArgs<ExtArgs extends runtime.Types.Extensions.InternalArgs = runtime.Types.Extensions.DefaultArgs> = {
  /**
   * Select specific fields to fetch from the ProfissionalEspecialidade
   */
  select?: Prisma.ProfissionalEspecialidadeSelect<ExtArgs> | null
  /**
   * Omit specific fields from the ProfissionalEspecialidade
   */
  omit?: Prisma.ProfissionalEspecialidadeOmit<ExtArgs> | null
  /**
   * Choose, which related nodes to fetch as well
   */
  include?: Prisma.ProfissionalEspecialidadeInclude<ExtArgs> | null
  /**
   * Filter, which ProfissionalEspecialidade to fetch.
   */
  where: Prisma.ProfissionalEspecialidadeWhereUniqueInput
}

/**
 * ProfissionalEspecialidade findUniqueOrThrow
 */
export type ProfissionalEspecialidadeFindUniqueOrThrowArgs<ExtArgs extends runtime.Types.Extensions.InternalArgs = runtime.Types.Extensions.DefaultArgs> = {
  /**
   * Select specific fields to fetch from the ProfissionalEspecialidade
   */
  select?: Prisma.ProfissionalEspecialidadeSelect<ExtArgs> | null
  /**
   * Omit specific fields from the ProfissionalEspecialidade
   */
  omit?: Prisma.ProfissionalEspecialidadeOmit<ExtArgs> | null
  /**
   * Choose, which related nodes to fetch as well
   */
  include?: Prisma.ProfissionalEspecialidadeInclude<ExtArgs> | null
  /**
   * Filter, which ProfissionalEspecialidade to fetch.
   */
  where: Prisma.ProfissionalEspecialidadeWhereUniqueInput
}

/**
 * ProfissionalEspecialidade findFirst
 */
export type ProfissionalEspecialidadeFindFirstArgs<ExtArgs extends runtime.Types.Extensions.InternalArgs = runtime.Types.Extensions.DefaultArgs> = {
  /**
   * Select specific fields to fetch from the ProfissionalEspecialidade
   */
  select?: Prisma.ProfissionalEspecialidadeSelect<ExtArgs> | null
  /**
   * Omit specific fields from the ProfissionalEspecialidade
   */
  omit?: Prisma.ProfissionalEspecialidadeOmit<ExtArgs> | null
  /**
   * Choose, which related nodes to fetch as well
   */
  include?: Prisma.ProfissionalEspecialidadeInclude<ExtArgs> | null
  /**
   * Filter, which ProfissionalEspecialidade to fetch.
   */
  where?: Prisma.ProfissionalEspecialidadeWhereInput
  /**
   * {@link https://www.prisma.io/docs/concepts/components/prisma-client/sorting Sorting Docs}
   * 
   * Determine the order of ProfissionalEspecialidades to fetch.
   */
  orderBy?: Prisma.ProfissionalEspecialidadeOrderByWithRelationInput | Prisma.ProfissionalEspecialidadeOrderByWithRelationInput[]
  /**
   * {@link https://www.prisma.io/docs/concepts/components/prisma-client/pagination#cursor-based-pagination Cursor Docs}
   * 
   * Sets the position for searching for ProfissionalEspecialidades.
   */
  cursor?: Prisma.ProfissionalEspecialidadeWhereUniqueInput
  /**
   * {@link https://www.prisma.io/docs/concepts/components/prisma-client/pagination Pagination Docs}
   * 
   * Take `±n` ProfissionalEspecialidades from the position of the cursor.
   */
  take?: number
  /**
   * {@link https://www.prisma.io/docs/concepts/components/prisma-client/pagination Pagination Docs}
   * 
   * Skip the first `n` ProfissionalEspecialidades.
   */
  skip?: number
  /**
   * {@link https://www.prisma.io/docs/concepts/components/prisma-client/distinct Distinct Docs}
   * 
   * Filter by unique combinations of ProfissionalEspecialidades.
   */
  distinct?: Prisma.ProfissionalEspecialidadeScalarFieldEnum | Prisma.ProfissionalEspecialidadeScalarFieldEnum[]
}

/**
 * ProfissionalEspecialidade findFirstOrThrow
 */
export type ProfissionalEspecialidadeFindFirstOrThrowArgs<ExtArgs extends runtime.Types.Extensions.InternalArgs = runtime.Types.Extensions.DefaultArgs> = {
  /**
   * Select specific fields to fetch from the ProfissionalEspecialidade
   */
  select?: Prisma.ProfissionalEspecialidadeSelect<ExtArgs> | null
  /**
   * Omit specific fields from the ProfissionalEspecialidade
   */
  omit?: Prisma.ProfissionalEspecialidadeOmit<ExtArgs> | null
  /**
   * Choose, which related nodes to fetch as well
   */
  include?: Prisma.ProfissionalEspecialidadeInclude<ExtArgs> | null
  /**
   * Filter, which ProfissionalEspecialidade to fetch.
   */
  where?: Prisma.ProfissionalEspecialidadeWhereInput
  /**
   * {@link https://www.prisma.io/docs/concepts/components/prisma-client/sorting Sorting Docs}
   * 
   * Determine the order of ProfissionalEspecialidades to fetch.
   */
  orderBy?: Prisma.ProfissionalEspecialidadeOrderByWithRelationInput | Prisma.ProfissionalEspecialidadeOrderByWithRelationInput[]
  /**
   * {@link https://www.prisma.io/docs/concepts/components/prisma-client/pagination#cursor-based-pagination Cursor Docs}
   * 
   * Sets the position for searching for ProfissionalEspecialidades.
   */
  cursor?: Prisma.ProfissionalEspecialidadeWhereUniqueInput
  /**
   * {@link https://www.prisma.io/docs/concepts/components/prisma-client/pagination Pagination Docs}
   * 
   * Take `±n` ProfissionalEspecialidades from the position of the cursor.
   */
  take?: number
  /**
   * {@link https://www.prisma.io/docs/concepts/components/prisma-client/pagination Pagination Docs}
   * 
   * Skip the first `n` ProfissionalEspecialidades.
   */
  skip?: number
  /**
   * {@link https://www.prisma.io/docs/concepts/components/prisma-client/distinct Distinct Docs}
   * 
   * Filter by unique combinations of ProfissionalEspecialidades.
   */
  distinct?: Prisma.ProfissionalEspecialidadeScalarFieldEnum | Prisma.ProfissionalEspecialidadeScalarFieldEnum[]
}

/**
 * ProfissionalEspecialidade findMany
 */
export type ProfissionalEspecialidadeFindManyArgs<ExtArgs extends runtime.Types.Extensions.InternalArgs = runtime.Types.Extensions.DefaultArgs> = {
  /**
   * Select specific fields to fetch from the ProfissionalEspecialidade
   */
  select?: Prisma.ProfissionalEspecialidadeSelect<ExtArgs> | null
  /**
   * Omit specific fields from the ProfissionalEspecialidade
   */
  omit?: Prisma.ProfissionalEspecialidadeOmit<ExtArgs> | null
  /**
   * Choose, which related nodes to fetch as well
   */
  include?: Prisma.ProfissionalEspecialidadeInclude<ExtArgs> | null
  /**
   * Filter, which ProfissionalEspecialidades to fetch.
   */
  where?: Prisma.ProfissionalEspecialidadeWhereInput
  /**
   * {@link https://www.prisma.io/docs/concepts/components/prisma-client/sorting Sorting Docs}
   * 
   * Determine the order of ProfissionalEspecialidades to fetch.
   */
  orderBy?: Prisma.ProfissionalEspecialidadeOrderByWithRelationInput | Prisma.ProfissionalEspecialidadeOrderByWithRelationInput[]
  /**
   * {@link https://www.prisma.io/docs/concepts/components/prisma-client/pagination#cursor-based-pagination Cursor Docs}
   * 
   * Sets the position for listing ProfissionalEspecialidades.
   */
  cursor?: Prisma.ProfissionalEspecialidadeWhereUniqueInput
  /**
   * {@link https://www.prisma.io/docs/concepts/components/prisma-client/pagination Pagination Docs}
   * 
   * Take `±n` ProfissionalEspecialidades from the position of the cursor.
   */
  take?: number
  /**
   * {@link https://www.prisma.io/docs/concepts/components/prisma-client/pagination Pagination Docs}
   * 
   * Skip the first `n` ProfissionalEspecialidades.
   */
  skip?: number
  distinct?: Prisma.ProfissionalEspecialidadeScalarFieldEnum | Prisma.ProfissionalEspecialidadeScalarFieldEnum[]
}

/**
 * ProfissionalEspecialidade create
 */
export type ProfissionalEspecialidadeCreateArgs<ExtArgs extends runtime.Types.Extensions.InternalArgs = runtime.Types.Extensions.DefaultArgs> = {
  /**
   * Select specific fields to fetch from the ProfissionalEspecialidade
   */
  select?: Prisma.ProfissionalEspecialidadeSelect<ExtArgs> | null
  /**
   * Omit specific fields from the ProfissionalEspecialidade
   */
  omit?: Prisma.ProfissionalEspecialidadeOmit<ExtArgs> | null
  /**
   * Choose, which related nodes to fetch as well
   */
  include?: Prisma.ProfissionalEspecialidadeInclude<ExtArgs> | null
  /**
   * The data needed to create a ProfissionalEspecialidade.
   */
  data: Prisma.XOR<Prisma.ProfissionalEspecialidadeCreateInput, Prisma.ProfissionalEspecialidadeUncheckedCreateInput>
}

/**
 * ProfissionalEspecialidade createMany
 */
export type ProfissionalEspecialidadeCreateManyArgs<ExtArgs extends runtime.Types.Extensions.InternalArgs = runtime.Types.Extensions.DefaultArgs> = {
  /**
   * The data used to create many ProfissionalEspecialidades.
   */
  data: Prisma.ProfissionalEspecialidadeCreateManyInput | Prisma.ProfissionalEspecialidadeCreateManyInput[]
  skipDuplicates?: boolean
}

/**
 * ProfissionalEspecialidade update
 */
export type ProfissionalEspecialidadeUpdateArgs<ExtArgs extends runtime.Types.Extensions.InternalArgs = runtime.Types.Extensions.DefaultArgs> = {
  /**
   * Select specific fields to fetch from the ProfissionalEspecialidade
   */
  select?: Prisma.ProfissionalEspecialidadeSelect<ExtArgs> | null
  /**
   * Omit specific fields from the ProfissionalEspecialidade
   */
  omit?: Prisma.ProfissionalEspecialidadeOmit<ExtArgs> | null
  /**
   * Choose, which related nodes to fetch as well
   */
  include?: Prisma.ProfissionalEspecialidadeInclude<ExtArgs> | null
  /**
   * The data needed to update a ProfissionalEspecialidade.
   */
  data: Prisma.XOR<Prisma.ProfissionalEspecialidadeUpdateInput, Prisma.ProfissionalEspecialidadeUncheckedUpdateInput>
  /**
   * Choose, which ProfissionalEspecialidade to update.
   */
  where: Prisma.ProfissionalEspecialidadeWhereUniqueInput
}

/**
 * ProfissionalEspecialidade updateMany
 */
export type ProfissionalEspecialidadeUpdateManyArgs<ExtArgs extends runtime.Types.Extensions.InternalArgs = runtime.Types.Extensions.DefaultArgs> = {
  /**
   * The data used to update ProfissionalEspecialidades.
   */
  data: Prisma.XOR<Prisma.ProfissionalEspecialidadeUpdateManyMutationInput, Prisma.ProfissionalEspecialidadeUncheckedUpdateManyInput>
  /**
   * Filter which ProfissionalEspecialidades to update
   */
  where?: Prisma.ProfissionalEspecialidadeWhereInput
  /**
   * Limit how many ProfissionalEspecialidades to update.
   */
  limit?: number
}

/**
 * ProfissionalEspecialidade upsert
 */
export type ProfissionalEspecialidadeUpsertArgs<ExtArgs extends runtime.Types.Extensions.InternalArgs = runtime.Types.Extensions.DefaultArgs> = {
  /**
   * Select specific fields to fetch from the ProfissionalEspecialidade
   */
  select?: Prisma.ProfissionalEspecialidadeSelect<ExtArgs> | null
  /**
   * Omit specific fields from the ProfissionalEspecialidade
   */
  omit?: Prisma.ProfissionalEspecialidadeOmit<ExtArgs> | null
  /**
   * Choose, which related nodes to fetch as well
   */
  include?: Prisma.ProfissionalEspecialidadeInclude<ExtArgs> | null
  /**
   * The filter to search for the ProfissionalEspecialidade to update in case it exists.
   */
  where: Prisma.ProfissionalEspecialidadeWhereUniqueInput
  /**
   * In case the ProfissionalEspecialidade found by the `where` argument doesn't exist, create a new ProfissionalEspecialidade with this data.
   */
  create: Prisma.XOR<Prisma.ProfissionalEspecialidadeCreateInput, Prisma.ProfissionalEspecialidadeUncheckedCreateInput>
  /**
   * In case the ProfissionalEspecialidade was found with the provided `where` argument, update it with this data.
   */
  update: Prisma.XOR<Prisma.ProfissionalEspecialidadeUpdateInput, Prisma.ProfissionalEspecialidadeUncheckedUpdateInput>
}

/**
 * ProfissionalEspecialidade delete
 */
export type ProfissionalEspecialidadeDeleteArgs<ExtArgs extends runtime.Types.Extensions.InternalArgs = runtime.Types.Extensions.DefaultArgs> = {
  /**
   * Select specific fields to fetch from the ProfissionalEspecialidade
   */
  select?: Prisma.ProfissionalEspecialidadeSelect<ExtArgs> | null
  /**
   * Omit specific fields from the ProfissionalEspecialidade
   */
  omit?: Prisma.ProfissionalEspecialidadeOmit<ExtArgs> | null
  /**
   * Choose, which related nodes to fetch as well
   */
  include?: Prisma.ProfissionalEspecialidadeInclude<ExtArgs> | null
  /**
   * Filter which ProfissionalEspecialidade to delete.
   */
  where: Prisma.ProfissionalEspecialidadeWhereUniqueInput
}

/**
 * ProfissionalEspecialidade deleteMany
 */
export type ProfissionalEspecialidadeDeleteManyArgs<ExtArgs extends runtime.Types.Extensions.InternalArgs = runtime.Types.Extensions.DefaultArgs> = {
  /**
   * Filter which ProfissionalEspecialidades to delete
   */
  where?: Prisma.ProfissionalEspecialidadeWhereInput
  /**
   * Limit how many ProfissionalEspecialidades to delete.
   */
  limit?: number
}

/**
 * ProfissionalEspecialidade without action
 */
export type ProfissionalEspecialidadeDefaultArgs<ExtArgs extends runtime.Types.Extensions.InternalArgs = runtime.Types.Extensions.DefaultArgs> = {
  /**
   * Select specific fields to fetch from the ProfissionalEspecialidade
   */
  select?: Prisma.ProfissionalEspecialidadeSelect<ExtArgs> | null
  /**
   * Omit specific fields from the ProfissionalEspecialidade
   */
  omit?: Prisma.ProfissionalEspecialidadeOmit<ExtArgs> | null
  /**
   * Choose, which related nodes to fetch as well
   */
  include?: Prisma.ProfissionalEspecialidadeInclude<ExtArgs> | null
}
