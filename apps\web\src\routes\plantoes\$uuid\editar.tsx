import { useNavigate, useParams } from "@tanstack/react-router";
import { createFileRoute } from "@tanstack/react-router";
import { useMutation, useQuery, useQueryClient } from "@tanstack/react-query";
import { requireAdminRole } from "@/lib/route-guards";
import { api, type Cliente, type Profissional, type LocalAtendimento, type Plantao } from "@/lib/api";
import { Button } from "@/components/ui/button";
import { ArrowLeft } from "lucide-react";
import { toast } from "sonner";
import { Link } from "@tanstack/react-router";
import { useState, useEffect } from "react";
import type { DiaPlantao, TipoTurno } from "@/components/calendario-plantao";
import { usePlantaoStore } from "@/stores/plantao.store";
import { Skeleton } from "@/components/ui/skeleton";
import { createLocalDate, getDaysInMonthForDate, parseISO, formatDateForInput, getMonth, getYear } from "@shared/date";
import { PlantaoForm } from "@/components/plantao/plantao-form";

function PlantaoEditar() {
  const navigate = useNavigate({ from: "/plantoes/$uuid/editar" });
  const queryClient = useQueryClient();
  const { uuid } = useParams({ from: "/plantoes/$uuid/editar" });
  const [isInitialized, setIsInitialized] = useState(false);

  // Zustand store
  const {
    form,
    setDataInicial,
    setDataFinal,
    setClienteId,
    setLocalAtendimentoId,
    setProfissionalId,
    setMes,
    setAno,
    setModalidadeTrabalho,
    setTipoFechamento,
    setTipoValor,
    setValorBase,
    setPrazoPagamentoDias,
    setHoraInicio,
    setHoraFim,
    setObservacoes,
    setDiasPlantao,
    setTipoTurno,
    resetForm,
    saveDiasForMonth,
  } = usePlantaoStore();

  useEffect(() => {
    setIsInitialized(false);
    return () => {
      resetForm();
    };
  }, [uuid, resetForm]);

  // Fetch plantão data
  const { data: plantao, isLoading: isLoadingPlantao } = useQuery<Plantao>({
    queryKey: ["plantao", uuid],
    queryFn: () => api.get(`/plantoes/${uuid}`),
    enabled: !!uuid,
  });

  const { data: clientes } = useQuery({
    queryKey: ["clientes-select"],
    queryFn: () => api.get<{ data: Cliente[] }>("/clientes", { limit: 100, ativo: true }),
  });

  const { data: profissionais } = useQuery({
    queryKey: ["profissionais-select"],
    queryFn: () =>
      api.get<{ data: Profissional[] }>("/profissionais", {
        limit: 100,
        ativo: true,
      }),
  });

  const { data: locais } = useQuery({
    queryKey: ["locais-select", form.clienteId],
    queryFn: () =>
      api.get<{ data: LocalAtendimento[] }>("/locais-atendimento", {
        limit: 100,
        ativo: true,
        ...(form.clienteId && { clienteUuid: form.clienteId }),
      }),
    enabled: true, // Sempre buscar, mas filtrar no backend quando tem cliente selecionado
  });

  // Initialize form with plantão data
  useEffect(() => {
    // Only initialize when we have plantao data, haven't initialized yet,
    // AND the dropdown data is loaded
    if (plantao && !isInitialized && clientes?.data && profissionais?.data && locais?.data) {
      console.log("Initializing form with plantao data v3 - first load fix");
      console.log("Available clientes:", clientes.data.length);
      console.log("Available profissionais:", profissionais.data.length);
      console.log("Available locais:", locais.data.length);

      // Small delay to ensure React has rendered the selects
      const initTimer = setTimeout(() => {
        console.log("Starting delayed initialization...");

        // Set basic data using UUID from related entities
        // The backend returns the related entities, not the UUID fields directly
        // The store expects UUIDs as strings
        if (plantao.cliente?.uuid) {
          console.log("Setting clienteId:", plantao.cliente.uuid);
          setClienteId(plantao.cliente.uuid);
        }
        if (plantao.localAtendimento?.uuid) {
          console.log("Setting localAtendimentoId:", plantao.localAtendimento.uuid);
          setLocalAtendimentoId(plantao.localAtendimento.uuid);
        }
        if (plantao.profissional?.uuid) {
          console.log("Setting profissionalId:", plantao.profissional.uuid);
          setProfissionalId(plantao.profissional.uuid);
        } else if (plantao.profissional?.id && profissionais?.data) {
          // Some records might have id instead of uuid - need to find the uuid
          console.log("Profissional has id but no uuid, searching...", plantao.profissional.id);
          const prof = profissionais.data.find((p) => p.id === plantao?.profissional?.id);
          if (prof?.uuid) {
            console.log("Found profissional uuid:", prof.uuid);
            setProfissionalId(prof.uuid);
          }
        } else {
          console.log("No profissional found in plantao:", plantao.profissional);
        }

        // Set dates - IMPORTANTE: usar formato YYYY-MM-DD
        if (plantao.dataInicial) {
          const dataInicialDate = parseISO(plantao.dataInicial);
          const dataInicialFormatted = formatDateForInput(dataInicialDate);
          setDataInicial(dataInicialFormatted);

          // Usar o primeiro mês do período (dataInicial)
          setMes(getMonth(dataInicialDate) + 1);
          setAno(getYear(dataInicialDate));
        }

        if (plantao.dataFinal) {
          const dataFinalDate = parseISO(plantao.dataFinal);
          const dataFinalFormatted = formatDateForInput(dataFinalDate);
          setDataFinal(dataFinalFormatted);
        }

        // Set payment types
        setModalidadeTrabalho(
          plantao.modalidadeTrabalho as "PLANTONISTA" | "MENSALISTA" | "COORDENADOR" | "SUPERVISOR" | "DIRETOR"
        );
        setTipoFechamento(plantao.tipoFechamento as "DIARIO" | "SEMANAL" | "QUINZENAL" | "MENSAL");

        // Set values
        if (plantao.valorBase !== null && plantao.valorBase !== undefined) {
          setValorBase(plantao.valorBase);
        }
        if (plantao.tipoValor) {
          setTipoValor(plantao.tipoValor as "MENSAL" | "HORA" | "DIARIA" | "PLANTAO");
        }
        if (plantao.prazoPagamentoDias) {
          setPrazoPagamentoDias(plantao.prazoPagamentoDias);
        }

        // Set hours
        if (plantao.horaInicio) setHoraInicio(plantao.horaInicio);
        if (plantao.horaFim) setHoraFim(plantao.horaFim);

        // Set other fields
        setObservacoes(plantao.observacoes || "");

        // Set tipo turno based on hours
        if (plantao.tipoTurno) {
          setTipoTurno(plantao.tipoTurno as TipoTurno);
        } else if (plantao.horaInicio === "07:00" && plantao.horaFim === "19:00") {
          setTipoTurno("diurno");
        } else if (plantao.horaInicio === "09:00" && plantao.horaFim === "18:00") {
          setTipoTurno("comercial");
        } else if (plantao.horaInicio === "19:00" && plantao.horaFim === "07:00") {
          setTipoTurno("noturno");
        } else {
          setTipoTurno("customizado");
        }

        // Set dias plantão - processar todos os meses no período
        if (plantao.diasPlantao && plantao.diasPlantao.length > 0) {
          // Processar todos os meses do período
          const dataInicialParsed = parseISO(plantao.dataInicial);
          const dataFinalParsed = plantao.dataFinal ? parseISO(plantao.dataFinal) : dataInicialParsed;

          // Criar registro de dias por mês
          const diasPorMesTemp: Record<string, DiaPlantao[]> = {};

          // Iterar por todos os meses no período
          let currentDate = createLocalDate(getYear(dataInicialParsed), getMonth(dataInicialParsed) + 1, 1);
          const endDate = createLocalDate(getYear(dataFinalParsed), getMonth(dataFinalParsed) + 1, 1);

          while (currentDate <= endDate) {
            const mesAtual = currentDate.getMonth() + 1;
            const anoAtual = currentDate.getFullYear();
            const key = `${anoAtual}-${mesAtual}`;
            const daysInMonth = getDaysInMonthForDate(anoAtual, mesAtual);
            const diasDoMes: DiaPlantao[] = [];

            for (let dia = 1; dia <= daysInMonth; dia++) {
              const dataStr = `${anoAtual}-${mesAtual.toString().padStart(2, "0")}-${dia.toString().padStart(2, "0")}`;
              const diaPlantao = plantao.diasPlantao.find((d) => {
                // Comparar apenas a parte da data (YYYY-MM-DD)
                const dData = d.data ? formatDateForInput(parseISO(d.data)) : "";
                return dData === dataStr;
              });

              if (diaPlantao) {
                diasDoMes.push({
                  data: dataStr,
                  selecionado: true,
                  horario: {
                    inicio: diaPlantao.horaEntrada || "",
                    fim: diaPlantao.horaSaida || "",
                    intervalo: diaPlantao.intervalo || "01:00",
                  },
                });
              } else {
                // Verificar se o dia está dentro do período do plantão
                const currentDayDate = createLocalDate(anoAtual, mesAtual, dia);
                const dentroPeríodo = currentDayDate >= dataInicialParsed && currentDayDate <= dataFinalParsed;

                diasDoMes.push({
                  data: dataStr,
                  selecionado: false,
                });
              }
            }

            diasPorMesTemp[key] = diasDoMes;

            // Próximo mês
            currentDate.setMonth(currentDate.getMonth() + 1);
          }

          // Salvar todos os meses
          Object.entries(diasPorMesTemp).forEach(([key, dias]) => {
            const [ano, mes] = key.split("-").map(Number);
            saveDiasForMonth(mes, ano, dias);
          });

          // Definir os dias do mês atual (primeiro mês do período)
          const primeiroMesKey = `${dataInicialParsed.getFullYear()}-${dataInicialParsed.getMonth() + 1}`;
          if (diasPorMesTemp[primeiroMesKey]) {
            setDiasPlantao(diasPorMesTemp[primeiroMesKey]);
          }
        }

        setIsInitialized(true);

        // Log final form state - Note: the state updates won't be visible immediately
        // due to React's batching, so we'll log in the next tick
        setTimeout(() => {
          const currentForm = usePlantaoStore.getState().form;
          console.log("Form initialized. Actual form state after initialization:", {
            clienteId: currentForm.clienteId,
            localAtendimentoId: currentForm.localAtendimentoId,
            profissionalId: currentForm.profissionalId,
          });
        }, 0);
      }, 100); // 100ms delay to ensure components are ready

      return () => clearTimeout(initTimer);
    }
  }, [plantao, profissionais, clientes, locais, isInitialized]);

  // Debug effect to monitor form state changes
  useEffect(() => {
    console.log("Form state changed:", {
      clienteId: form.clienteId,
      localAtendimentoId: form.localAtendimentoId,
      profissionalId: form.profissionalId,
    });
  }, [form.clienteId, form.localAtendimentoId, form.profissionalId]);

  const handleSubmit = async () => {
    // Debug log to see form state at save time
    console.log("Saving form with state:", {
      clienteId: form.clienteId,
      localAtendimentoId: form.localAtendimentoId,
      profissionalId: form.profissionalId,
      dataInicial: form.dataInicial,
      dataFinal: form.dataFinal,
      tipoValor: form.tipoValor,
      valorBase: form.valorBase,
      prazoPagamentoDias: form.prazoPagamentoDias,
    });

    const diasSelecionados = form.diasPlantao.filter((d) => d.selecionado);
    if (diasSelecionados.length === 0) {
      toast.error("Selecione pelo menos um dia para o plantão");
      return;
    }

    mutation.mutate();
  };

  const mutation = useMutation({
    mutationFn: async () => {
      const { diasPlantao, diasPorMes, tipoTurno, ...plantaoData } = form;

      // Coletar todos os dias selecionados de todos os meses
      let todosDiasSelecionados: any[] = [];

      // Sempre iterar por todos os meses no período
      const dataInicial = parseISO(form.dataInicial);
      const dataFinal = form.dataFinal ? parseISO(form.dataFinal) : dataInicial;

      // Criar uma data para o primeiro dia de cada mês
      let currentDate = createLocalDate(dataInicial.getFullYear(), dataInicial.getMonth() + 1, 1);
      const endDate = createLocalDate(dataFinal.getFullYear(), dataFinal.getMonth() + 1, 1);

      while (currentDate <= endDate) {
        const mesAtual = currentDate.getMonth() + 1;
        const anoAtual = currentDate.getFullYear();
        const key = `${anoAtual}-${mesAtual}`;

        // Pegar os dias deste mês - primeiro verificar diasPorMes, depois diasPlantao se for o mês atual
        let diasDoMes = diasPorMes?.[key];

        // Se não tem em diasPorMes e é o mês atual, usar diasPlantao
        if (!diasDoMes && mesAtual === form.mes && anoAtual === form.ano) {
          diasDoMes = diasPlantao;
        }

        // Se ainda não tem dias, pular este mês
        if (diasDoMes && diasDoMes.length > 0) {
          // Adicionar apenas os dias selecionados
          diasDoMes
            .filter((d) => d.selecionado)
            .forEach((dia) => {
              todosDiasSelecionados.push({
                data: dia.data,
                horaEntrada: dia.horario?.inicio || form.horaInicio || null,
                horaSaida: dia.horario?.fim || form.horaFim || null,
                intervalo: dia.horario?.intervalo || "01:00",
              });
            });
        }

        // Próximo mês
        currentDate.setMonth(currentDate.getMonth() + 1);
      }

      // Se não conseguiu coletar nenhum dia, usar fallback
      if (todosDiasSelecionados.length === 0) {
        // Fallback: usar apenas o mês atual (comportamento anterior)
        todosDiasSelecionados = diasPlantao
          .filter((d) => d.selecionado)
          .map((dia) => ({
            data: dia.data,
            horaEntrada: dia.horario?.inicio || form.horaInicio || null,
            horaSaida: dia.horario?.fim || form.horaFim || null,
            intervalo: dia.horario?.intervalo || "01:00",
          }));
      }

      // Get first selected day's schedule to use as default
      const firstSelectedDia = todosDiasSelecionados[0];
      const defaultIntervalo = firstSelectedDia?.intervalo || "01:00";

      // Calculate total value based on payment type
      let valorTotal = 0;
      const totalDiasSelecionados = todosDiasSelecionados.length;

      // if (form.modalidadeTrabalho === "MENSALISTA" && form.valorMensal) {
      //   valorTotal = form.valorMensal;
      // } else if (form.modalidadeTrabalho === "PLANTONISTA" && form.valorPlantao) {
      //   valorTotal = form.valorPlantao * totalDiasSelecionados;
      // } else if (form.valorHora && form.horaInicio && form.horaFim) {
      //   const [horaIni, minIni] = form.horaInicio.split(":").map(Number);
      //   const [horaFim, minFim] = form.horaFim.split(":").map(Number);
      //   const horasTrabalhadas = horaFim + minFim / 60 - (horaIni + minIni / 60);
      //   valorTotal = form.valorHora * horasTrabalhadas * totalDiasSelecionados;
      // }

      if (form.valorBase && form.horaInicio && form.horaFim) {
        const [horaIni, minIni] = form.horaInicio.split(":").map(Number);
        const [horaFim, minFim] = form.horaFim.split(":").map(Number);
        const horasTrabalhadas = horaFim + minFim / 60 - (horaIni + minIni / 60);
        valorTotal = form.valorBase * horasTrabalhadas * totalDiasSelecionados;
      }

      // Criar payload sem campos undefined
      const plantaoPayload: any = {
        // Use UUIDs for foreign keys
        clienteUuid: form.clienteId,
        localAtendimentoUuid: form.localAtendimentoId,
        profissionalUuid: form.profissionalId,
        mes: form.mes,
        ano: form.ano,
        modalidadeTrabalho: form.modalidadeTrabalho,
        tipoFechamento: form.tipoFechamento,
        dataInicial: form.dataInicial,
        tipoValor: form.tipoValor,
        valorBase: form.valorBase,
        prazoPagamentoDias: form.prazoPagamentoDias,
        valorTotal,
        intervalo: defaultIntervalo,
        tipoTurno, // Add tipoTurno back as it's stored in the database
        diasPlantao: todosDiasSelecionados,
      };

      // Adicionar campos opcionais apenas se têm valor
      if (form.dataFinal) plantaoPayload.dataFinal = form.dataFinal;
      if (form.valorBase) plantaoPayload.valorBase = form.valorBase;
      if (form.tipoValor) plantaoPayload.tipoValor = form.tipoValor;
      if (form.prazoPagamentoDias) plantaoPayload.prazoPagamentoDias = form.prazoPagamentoDias;
      if (form.horaInicio) plantaoPayload.horaInicio = form.horaInicio;
      if (form.horaFim) plantaoPayload.horaFim = form.horaFim;
      if (form.observacoes) plantaoPayload.observacoes = form.observacoes;

      return api.put(`/plantoes/${uuid}`, plantaoPayload);
    },
    onSuccess: () => {
      toast.success("Plantão atualizado com sucesso!");
      queryClient.invalidateQueries({ queryKey: ["plantoes"] });
      queryClient.invalidateQueries({ queryKey: ["plantao", uuid] });
      navigate({ to: "/plantoes" });
    },
    onError: (error: any) => {
      toast.error(error.message || "Erro ao atualizar plantão");
    },
  });

  if (isLoadingPlantao) {
    return (
      <div className="space-y-6">
        <div className="flex items-center gap-4">
          <Skeleton className="h-10 w-10" />
          <div className="space-y-2">
            <Skeleton className="h-8 w-48" />
            <Skeleton className="h-4 w-64" />
          </div>
        </div>
        <Skeleton className="h-96 w-full" />
      </div>
    );
  }

  if (!plantao) {
    return (
      <div className="space-y-6">
        <div className="flex items-center gap-4">
          <Link to="/plantoes">
            <Button variant="ghost" size="icon">
              <ArrowLeft className="h-4 w-4" />
            </Button>
          </Link>
          <div>
            <h1 className="text-3xl font-bold">Plantão não encontrado</h1>
            <p className="text-muted-foreground">O plantão solicitado não foi encontrado</p>
          </div>
        </div>
      </div>
    );
  }

  return (
    <div className="space-y-6">
      <div className="flex items-center gap-4">
        <Link to="/plantoes">
          <Button variant="ghost" size="icon">
            <ArrowLeft className="h-4 w-4" />
          </Button>
        </Link>
        <div>
          <h1 className="text-3xl font-bold">Editar Plantão</h1>
          <p className="text-muted-foreground">Atualize as informações do plantão</p>
        </div>
      </div>

      <PlantaoForm
        mode="edit"
        clientes={clientes}
        profissionais={profissionais}
        locais={locais}
        onSubmit={handleSubmit}
        isSubmitting={mutation.isPending}
        onCancel={() => navigate({ to: "/plantoes" })}
      />
    </div>
  );
}

export const Route = createFileRoute("/plantoes/$uuid/editar")({
  component: PlantaoEditar,
  beforeLoad: async () => {
    await requireAdminRole();
  },
});
