# CLAUDE.md

This file provides guidance to <PERSON> (claude.ai/code) when working with code in this repository.

# Login

## Master (Admin)

- <EMAIL>
- 123321123321123321123321

## Profissional

- <EMAIL>
- 123123123

## Playwright Testing Instructions

Quando solicitado para abrir o <PERSON>wright ou testar com Playwright:

1. Sempre fazer logout primeiro (se j<PERSON> estiver logado)
2. Fazer login com <NAME_EMAIL> / 123321123321123321123321
3. Prosseguir com os testes solicitados

## ⚠️ CRITICAL RULES - READ FIRST ⚠️

### Date Handling - CRITICAL RULE

🚫 **ABSOLUTELY FORBIDDEN: `new Date()`, `new Date(...)`, `Date.now()`, `Date.parse()`** 🚫

**NEVER use ANY direct Date constructor or static methods anywhere in the codebase!**

### Date Utilities Location - MANDATORY

✅ **ALL date utilities MUST be imported from `@shared/date`** ✅

**NEVER create date utilities in other locations. The ONLY source of truth for date utilities is:**

- `apps/shared/src/date/index.ts`

**CRITICAL RULES:**

1. **ALL date functions must be in `@shared/date`** - No exceptions!
2. **When adding new date functions, ALWAYS add them to `@shared/date`**

**Import examples:**

```typescript
// ✅ CORRECT - Always import from @shared/date
import { getCurrentDate, createLocalDate, parseISO } from "@shared/date";
import { createLocalDateInTimezone, getStartOfMonthInTimezone } from "@shared/date";

// ❌ WRONG - Never import from other locations
import { getCurrentDate } from "@/lib/utils"; // FORBIDDEN
import { createLocalDate } from "../utils/date"; // FORBIDDEN
import { createLocalDateInTimezone } from "../lib/date-utils"; // FORBIDDEN - use @shared/date
```

### File Naming Convention

🚫 **NEVER use CamelCase for file names** 🚫

✅ **ALWAYS use kebab-case for ALL file names (components, routers, schemas, etc.):**

**Components:**

- `AntecipacaoConfigCard.tsx` ❌ → `antecipacao-config-card.tsx` ✅
- `PlantaoSelectionStep.tsx` ❌ → `plantao-selection-step.tsx` ✅
- `FechamentoCard.tsx` ❌ → `fechamento-card.tsx` ✅

**API files:**

- `AntecipacaoRouter.ts` ❌ → `antecipacao.router.ts` ✅
- `PlantaoSchema.ts` ❌ → `plantao.schema.ts` ✅
- `UserService.ts` ❌ → `user.service.ts` ✅

**This applies to ALL files in the project (frontend and backend)!**

✅ **ALWAYS use these utility functions from `@shared/date`:**

**Core Date Creation (NEVER use new Date()):**

- `getCurrentDate()` - instead of `new Date()`
- `createLocalDate(year, month, day)` - instead of `new Date(year, month, day)`
- `createDateWithTime(year, month, day, hour, minute)` - for dates with time
- `createCurrentLocalDate()` - for today's date
- `parseISO(dateString)` - instead of `new Date(dateString)`

**Timezone Handling:**

- `utcToTimezone(date, timezone)` - Convert UTC to local timezone
- `timezoneToUTC(date, timezone)` - Convert local timezone to UTC
- `formatInTimezone(date, format, timezone)` - Format date in specific timezone

**Formatting & Display:**

- `formatDate(date, format)` - for display formatting
- `formatDateForInput(date)` - for HTML date inputs
- `formatDateString(dateString)` - format ISO string without timezone conversion

**Date Extraction:**

- `getCurrentYear()`, `getCurrentMonth()`, `getCurrentDay()` - current date parts
- `getYear(date)`, `getMonth(date)`, `getDate(date)` - extract date parts
- `getMesFromDate(date)`, `getAnoFromDate(date)` - Portuguese aliases

**This is MANDATORY for timezone handling and consistency. VIOLATION WILL BREAK THE APPLICATION!**

💡 **Quick Reference:**

```typescript
// ❌ WRONG - DO NOT USE
new Date();
new Date(2024, 0, 1);
new Date("2024-01-01");
Date.now();

// ✅ CORRECT - USE THESE (from @shared/date)
getCurrentDate();
createLocalDate(2024, 1, 1);
parseISO("2024-01-01");
createCurrentLocalDate();
```

## Project Overview

This is a healthcare scheduling and financial anticipation management system built with the Better-T-Stack. It manages professional schedules (plantões), closures (fechamentos), and financial anticipation requests for healthcare workers.

## Tech Stack

- **Frontend**: React Router v7 (SPA mode), TailwindCSS 4, shadcn/ui, Lucide icons
- **Backend**: Fastify with TypeScript
- **Database**: MySQL with Prisma ORM (dates stored in UTC)
- **Shared Package**: Date utilities (date-fns, date-fns-tz), Prisma types
- **State Management**: Zustand (client state), TanStack Query (server state)
- **Build Tools**: Vite (frontend), tsx (backend dev), tsdown (backend build)
- **Package Manager**: yarn workspaces

## Common Development Commands

### Development

```bash
# Run all apps in development mode
yarn dev

# Run specific apps
yarn dev:web      # Frontend only (http://localhost:5173)
yarn dev:server   # Backend only (http://localhost:3000)
```

### Code Quality & Verification

```bash
# Format all code with Prettier
yarn format

# Check formatting without changes
yarn format:check

# Run all verification checks (format, types, lint)
yarn verify

# Fix all issues automatically (format, lint)
yarn verify:fix

# Check TypeScript types across all workspaces
yarn check-types

# Type check web app specifically
cd apps/web && yarn typecheck
```

### Database Operations

```bash
# Push schema changes to database
yarn db:push

# Open Prisma Studio UI
yarn db:studio

# Generate Prisma client
yarn db:generate

# Run migrations
yarn db:migrate

# Docker compose commands
yarn db:start   # Start MySQL in Docker
yarn db:stop    # Stop MySQL container
yarn db:down    # Remove MySQL container
```

### PWA Assets

```bash
cd apps/web && yarn generate-pwa-assets
```

## Architecture

### Monorepo Structure

- Uses yarn workspaces with apps in `apps/` directory
- Server (`apps/server/`): Fastify API with Prisma ORM
- Web (`apps/web/`): React Router v7 SPA with TailwindCSS
- **Database**: MySQL with Prisma ORM
- **Schema**: ESM modules with client generated in `apps/server/generated/`
- **ID Strategy**: CUID for main entities, auto-increment for relationships

### API Router Organization Pattern

**IMPORTANT**: The project follows a feature-based organization pattern for API routes.

**Structure Example - Plantão Feature (`apps/server/src/routers/plantao/`):**

```
plantao/
├── index.ts                        # Central router that imports and registers all sub-routers
├── listar.ts                       # GET /plantoes - List all plantões
├── buscar.ts                       # GET /plantoes/:id - Get specific plantão
├── buscar-presencas.ts            # GET /plantoes/:id/presencas - Get presences
├── criar.ts                        # POST /plantoes - Create new plantão
├── atualizar.ts                   # PUT /plantoes/:id - Update plantão
├── deletar.ts                     # DELETE /plantoes/:id - Delete plantão
├── estatisticas.ts                # GET /plantoes/estatisticas - Statistics
├── disponiveis-antecipacao.ts     # GET /plantoes/disponiveis-antecipacao
├── replicar-prox-mes.ts          # POST /plantoes/:id/replicar-prox-mes
└── atualizar-status-de-conclusao.ts # PUT /plantoes/:id/status-conclusao
```

**Implementation Rules:**

1. **ALWAYS** implement this pattern when working on routers that aren't organized this way yet
2. Each file exports a single function that registers its routes: `export function [action]Router(fastify: FastifyTypedInstance)`
3. The `index.ts` file imports all sub-routers and registers them in the main function
4. Each file handles ONE specific endpoint or closely related endpoints
5. Business logic and validation stay within each specific file
6. This keeps code organized, maintainable, and easy to test

**When to Refactor to This Pattern:**

- Whenever you need to modify an existing router that doesn't follow this pattern
- When adding new features to existing routers
- When the router file becomes too large (>200 lines)

### Database Schema

Key entities defined in `apps/server/prisma/schema/schema.prisma`:

#### 1. User Management and Permissions (Gestão de Usuários e Permissões)

**Usuario**

- **Main fields**: unique email, hashed password, complete personal data
- **Address**: CEP, street, number, complement, neighborhood, city, state
- **Control**: verified email, recovery tokens, last access
- **Relationships**: 1:1 with Profissional, N:M with Roles
- **Metadata**: JSON field for flexible data

**Role System (RBAC)**

- **Available roles**: admin, profissional, gestor, master, financeiro
- **UsuarioRole**: Relationship with audit (who assigned, when)

#### 2. Clients and Locations (Clientes e Locais)

**Cliente**

- **Types**: Pessoa Física (PF) or Pessoa Jurídica (PJ)
- **Document**: Unique CPF or CNPJ
- **Relationships**: 1:N with LocalAtendimento and Plantao

**LocalAtendimento**

- **Data**: Name, complete address, responsible person
- **Geolocation**: Optional latitude and longitude
- **Relationship**: N:1 with Cliente, 1:N with Plantao

#### 3. Professionals and Specialties (Profissionais e Especialidades)

**Profissional**

- **Professional data**: RG, CNES, professional council, registration number
- **Banking data**: Bank, agency, account, PIX (with types)
- **Metadata**: JSON field for flexible data
- **Relationships**: 1:1 with Usuario, N:M with Especialidade

**Especialidade**

- Professional categorization system with N:M relationship

### Frontend Architecture

- React Router v7 in SPA mode (SSR disabled in `react-router.config.ts`)
- Component library: shadcn/ui with Radix UI primitives
- Styling: TailwindCSS 4 with custom animations
- Forms: TanStack Form with Zod validation
- API communication: TanStack Query for server state

### Backend Architecture

- Fastify server with CORS configuration
- Environment-based configuration via dotenv
- Prisma ORM with MySQL database
- Docker Compose for local database setup

## Core Business Flow (Fluxo Principal do Negócio)

### 1. Schedule Creation (Criação de Plantão)

```
Cliente → LocalAtendimento → Plantao → DiaPlantao → PresencaDiaPlantao
```

**Plantao**

- **Period**: Specific month/year
- **Payment types**: PLANTONISTA, MENSALISTA, COORDENADOR, SUPERVISOR, DIRETOR
- **Closure types**: DIARIO, SEMANAL, QUINZENAL, MENSAL
- **Values**: Hour, shift, monthly, total
- **Configuration**: Hours, shifts, intervals
- **Anticipation**: Configurable percentage (80% terceiros, 100% Aura)

### 2. Schedule Control (Controle de Jornada)

**DiaPlantao**

- **Reference**: Specific day of the shift (1-31)
- **Hours**: Entry, exit, interval per day
- **Status**: Worked (boolean)
- **Relationship**: 1:N with PresencaDiaPlantao

**PresencaDiaPlantao**

- **Recorded hours**: Entry, exit, interval in DateTime
- **Calculations**: Worked hours, estimated value
- **Approval**: Status (PENDENTE, APROVADO, REJEITADO, EM_REVISAO)
- **History**: JSON with all approvals/rejections
- **Gloss**: Glossed time with justification
- **Constraint**: Only one record per DiaPlantao

### 3. Closure and Approval (Fechamento e Aprovação)

**Fechamento**

- **Period**: Specific month/year per shift
- **Status**: PENDENTE, APROVADO, REJEITADO
- **Totals**: Total hours, value, worked vs planned days
- **Approval**: User, date, rejection reason
- **Relationships**: 1:N with PresencaDiaPlantao and Antecipacao

### 4. Financial Anticipation (Antecipação de Recebíveis)

**Antecipacao**

- **Values**: Requested vs approved with percentage
- **Business type**: TERCEIROS (80%) or AURA (100%)
- **Status**: PENDENTE, APROVADA, REJEITADA, PAGA, CANCELADA
- **CCB**: Number, date, PDF file
- **Payment**: Approval, payment, proof
- **Relationship**: N:1 with Fechamento

## Key Configuration Files

- `apps/server/.env`: Database connection and environment variables
- `apps/server/prisma/schema/schema.prisma`: Database schema
- `apps/web/react-router.config.ts`: React Router configuration (SPA mode)
- `bts.jsonc`: Better-T-Stack configuration
- `.prettierrc.json`: Code formatting rules
- `.prettierignore`: Files to exclude from formatting

## Development Notes

- The project uses MySQL with Docker for local development
- Prisma client is generated to `apps/server/generated/`
- React Router v7 has known PWA compatibility issues (see README)
- CORS is configured for credentials and specific origins
- Frontend runs on port 5173, backend on port 3000
- **IMPORTANT**: When working on this project, both frontend and backend are already running. Do NOT attempt to restart them with yarn dev or similar commands. You can verify they are working by checking:
  - Frontend: http://localhost:5173
  - Backend: http://localhost:3000

## Code Quality Guidelines

### Before Committing Code

Always run verification checks to ensure code quality:

```bash
# Run all checks (recommended)
yarn verify

# Or fix issues automatically
yarn verify:fix
```

### Formatting Standards

- Uses Prettier for consistent code formatting
- Configuration in `.prettierrc.json`
- Double quotes for strings
- Semicolons required
- 2-space indentation
- Max line width: 80 characters

### When Making Changes

1. Make your code changes
2. Run `yarn verify:fix` to fix formatting and linting
3. Check for TypeScript errors with `yarn check-types`
4. Test your changes locally
5. Commit only when all checks pass

## Business Logic Reference

## Enums and Status Types (Enums e Status)

### Client Types (Tipos de Cliente)

- `PF`: Pessoa Física (Individual)
- `PJ`: Pessoa Jurídica (Company)

### Payment Types (Tipos de Pagamento)

- `PLANTONISTA`: Payment per shift
- `MENSALISTA`: Fixed monthly payment
- `COORDENADOR`: Coordination level
- `SUPERVISOR`: Supervision level
- `DIRETOR`: Director level

### Closure Types (Tipos de Fechamento)

- `DIARIO`: Daily closure
- `SEMANAL`: Weekly closure
- `QUINZENAL`: Biweekly closure
- `MENSAL`: Monthly closure

### Control Status (Status de Controle)

- **Fechamento**: PENDENTE → APROVADO/REJEITADO
- **Antecipação**: PENDENTE → APROVADA → PAGA (or REJEITADA/CANCELADA)
- **Registro Ponto**: PENDENTE → APROVADO/REJEITADO/EM_REVISAO

## Audit and Compliance (Auditoria e Compliance)

**AuditLog**

- **Operations**: CREATE, UPDATE, DELETE, READ
- **Data**: oldData, newData, changes (JSON)
- **Context**: userId, userRole, IP, userAgent, endpoint
- **Full traceability**: All changes are logged

## Important Business Rules (Regras de Negócio Importantes)

1. **Uniqueness**:
   - A professional can have only one shift per month/year/client
   - Only one time record per shift day

2. **Anticipation Percentage**:
   - TERCEIROS: 80% of value
   - AURA: 100% of value

3. **Approval Flow**:
   - PresencaDiaPlantao must be approved before Fechamento
   - Fechamento must be approved before Antecipação

4. **Soft Delete**:
   - Main entities use `ativo` field instead of physical deletion

5. **Cascading**:
   - Usuario → Profissional (CASCADE)
   - Cliente → LocalAtendimento → Plantao (CASCADE)
   - Plantao → DiaPlantao → PresencaDiaPlantao (CASCADE)

## Performance Indexes (Índices de Performance)

Main indexes for optimization:

- **Temporal**: mes/ano, createdAt, concluidoEm
- **Status**: status in Fechamento, Antecipacao, PresencaDiaPlantao
- **Relationships**: All foreign keys indexed
- **Audit**: tableName + createdAt, userId, recordId

## Calculated/Derived Fields (Campos Calculados/Derivados)

- **PresencaDiaPlantao.horasTrabalhadas**: Calculated based on entry/exit - interval
- **PresencaDiaPlantao.valorEstimado**: Based on worked hours × hourly rate
- **Fechamento.totalHoras**: Sum of all approved record hours
- **Fechamento.totalValor**: Total calculated value for the period
