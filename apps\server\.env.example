GS2_AWS_UTILS_URL="https://123123.lambda-url.us-east-1.on.aws"
GS2_AWS_UTILS_API_KEY="xxxxxxxxxxxxxxxxxxxxxxxxxx"
SEND_WELCOME_EMAIL="false"

# Database
DATABASE_URL="mysql://user:password@localhost:3306/gestao_escalas?timezone=UTC"

# Server Configuration
PORT=3000
NODE_ENV=development

# CORS
FRONT_END_URL="http://localhost:5173"

# JWT (para futura autenticação)
JWT_SECRET="your-secret-key-here"

# Logging Configuration
# Log Levels: trace, debug, info, warn, error, fatal
# - trace: Logs EVERYTHING (muito verboso)
# - debug: Logs detalhados para desenvolvimento
# - info: Logs informativos padrão (recomendado para produção)
# - warn: Apenas avisos e erros
# - error: Apenas erros
# - fatal: Apenas erros fatais
LOG_LEVEL=info

# Optional: Log Output Format
# Options: pretty (colorido e formatado), json (estruturado para análise)
# LOG_FORMAT=pretty

# Optional: Log File Output (para salvar logs em arquivo)
# LOG_FILE=./logs/app.log

# Optional: Hide Sensitive Data in Logs
# LOG_REDACT_PATHS="req.headers.authorization,*.password,*.token,*.cpf,*.cnpj"