# GEMINI.md

## Project Overview

This is a full-stack application built with the "Better-T-Stack". It features a React frontend and a Fastify backend, all written in TypeScript. The project is structured as a monorepo, with the frontend and backend code located in the `apps/web` and `apps/server` directories, respectively.

**Key Technologies:**

- **Frontend:**
  - React
  - React Router
  - TailwindCSS
  - shadcn/ui
  - Vite
- **Backend:**
  - Fastify
  - Node.js
  - Prisma
  - Zod
- **Database:**
  - MySQL
- **Tooling:**
  - TypeScript
  - ESLint
  - Prettier
  - pnpm (workspaces)

## Building and Running

**Installation:**

```bash
npm install
```

**Database Setup:**

1.  Ensure you have a MySQL database running.
2.  Copy `apps/server/.env.example` to `apps/server/.env` and update the `DATABASE_URL` with your MySQL connection string.
3.  Push the database schema:

    ```bash
    npm run db:push
    ```

**Development:**

To run both the frontend and backend development servers concurrently:

```bash
npm run dev
```

- Web application will be available at `http://localhost:5173`
- API server will be available at `http://localhost:3000`

You can also run the applications individually:

```bash
# Start only the web application
npm run dev:web

# Start only the server application
npm run dev:server
```

**Other useful commands:**

- `npm run build`: Build both applications for production.
- `npm run check-types`: Run the TypeScript compiler to check for type errors.
- `npm run db:studio`: Open the Prisma Studio to view and manage your database.

## Development Conventions

- **Code Style:** The project uses Prettier for code formatting and ESLint for linting. You can format the code by running `npm run format`.
- **Commits:** (No explicit commit message convention found, but good practice is to follow conventional commits).
- **Routing:** The frontend uses file-system based routing with `react-router-fs-routes`. New routes can be created by adding files to the `apps/web/src/routes` directory.
