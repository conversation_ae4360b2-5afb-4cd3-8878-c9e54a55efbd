import { useState } from "react";
import { useNavigate } from "@tanstack/react-router";
import { createFileRoute } from "@tanstack/react-router";
import { useQuery, useMutation } from "@tanstack/react-query";
import { Button } from "@/components/ui/button";
import { Card, CardContent, CardDescription, CardFooter, CardHeader, CardTitle } from "@/components/ui/card";
import { Checkbox } from "@/components/ui/checkbox";
import { ScrollArea } from "@/components/ui/scroll-area";
import { Loader2, FileText, Shield } from "lucide-react";
import { api } from "@/lib/api";
import { useAuthStore } from "@/stores/use-auth.store";
import { toast } from "sonner";

export const Route = createFileRoute("/termos")({
  component: AceiteTermos,
});

interface TermoAtual {
  termo: {
    id: number;
    versao: string;
    titulo: string;
    conteudo: string;
    validoDe: string;
    validoAte: string | null;
  };
  aceito: boolean;
  dataAceite: string | null;
}

function AceiteTermos() {
  const navigate = useNavigate();
  const [aceiteConfirmado, setAceiteConfirmado] = useState(false);
  const updateUser = useAuthStore((state) => state.updateUser);

  // Buscar termos atuais
  const {
    data: termoData,
    isLoading,
    error,
  } = useQuery({
    queryKey: ["termos-lgpd", "atual"],
    queryFn: async () => {
      const response = await api.get<TermoAtual>("/termos/atual");
      return response;
    },
  });

  // Mutation para aceitar os termos
  const aceitarTermosMutation = useMutation({
    mutationFn: async (termoVersaoId: number) => {
      const response = await api.post("/termos/aceitar", {
        termoVersaoId,
        consentimento: true,
        enderecoIp: window.location.hostname,
        userAgent: navigator.userAgent,
      });
      return response;
    },
    onSuccess: () => {
      // Atualizar o estado do usuário
      updateUser({ termosAceitos: true });
      toast.success("Termos aceitos com sucesso!");

      // Redirecionar para a página principal
      navigate({ to: "/", replace: true });
    },
    onError: (error: any) => {
      toast.error(error.response?.data?.error || "Erro ao aceitar os termos");
    },
  });

  const handleAceitar = () => {
    if (!aceiteConfirmado || !termoData?.termo?.id) {
      toast.error("Por favor, confirme que leu e concorda com os termos");
      return;
    }

    aceitarTermosMutation.mutate(termoData.termo.id);
  };

  if (isLoading) {
    return (
      <div className="flex items-center justify-center min-h-screen">
        <Loader2 className="h-8 w-8 animate-spin" />
      </div>
    );
  }

  if (error || !termoData?.termo) {
    return (
      <div className="flex items-center justify-center min-h-screen">
        <Card className="w-full max-w-md">
          <CardHeader>
            <CardTitle className="flex items-center gap-2">
              <Shield className="h-5 w-5" />
              Erro ao carregar termos
            </CardTitle>
          </CardHeader>
          <CardContent>
            <p className="text-sm text-muted-foreground">
              Não foi possível carregar os termos de uso. Por favor, tente novamente mais tarde.
            </p>
          </CardContent>
          <CardFooter>
            <Button onClick={() => window.location.reload()} variant="outline">
              Tentar novamente
            </Button>
          </CardFooter>
        </Card>
      </div>
    );
  }

  // Se já aceitou, redirecionar
  if (termoData.aceito) {
    navigate({ to: "/", replace: true });
    return null;
  }

  return (
    <div className="min-h-screen bg-gradient-to-b from-background to-muted/20 flex items-center justify-center p-4">
      <Card className="w-full max-w-4xl shadow-lg">
        <CardHeader className="space-y-1">
          <div className="flex items-center gap-2">
            <Shield className="h-6 w-6 text-primary" />
            <CardTitle className="text-2xl font-bold">{termoData.termo.titulo}</CardTitle>
          </div>
          <CardDescription>
            Versão {termoData.termo.versao} - Válido desde{" "}
            {new Date(termoData.termo.validoDe).toLocaleDateString("pt-BR")}
          </CardDescription>
        </CardHeader>

        <CardContent className="space-y-4">
          <div className="border rounded-lg p-2">
            <ScrollArea className="h-[400px] p-4">
              <div
                className="prose prose-sm max-w-none dark:prose-invert"
                dangerouslySetInnerHTML={{ __html: termoData.termo.conteudo }}
              />
            </ScrollArea>
          </div>

          <div className="flex items-start space-x-3 p-4 bg-muted/50 rounded-lg">
            <Checkbox
              id="aceite"
              checked={aceiteConfirmado}
              onCheckedChange={(checked) => setAceiteConfirmado(checked as boolean)}
              className="mt-1"
            />
            <label htmlFor="aceite" className="text-sm font-medium leading-relaxed cursor-pointer select-none">
              Li e concordo com os Termos de Uso e Política de Privacidade LGPD acima. Entendo que meus dados serão
              processados conforme descrito e que posso exercer meus direitos a qualquer momento.
            </label>
          </div>
        </CardContent>

        <CardFooter className="flex justify-between">
          <Button
            onClick={handleAceitar}
            disabled={!aceiteConfirmado || aceitarTermosMutation.isPending}
            className="min-w-[120px]"
          >
            {aceitarTermosMutation.isPending ? (
              <>
                <Loader2 className="mr-2 h-4 w-4 animate-spin" />
                Aceitando...
              </>
            ) : (
              <>
                <FileText className="mr-2 h-4 w-4" />
                Aceitar Termos
              </>
            )}
          </Button>
        </CardFooter>
      </Card>
    </div>
  );
}
