
/* !!! This is code generated by Prisma. Do not edit directly. !!! */
/* eslint-disable */
// @ts-nocheck 
/*
 * This file exports the `Plantao` model and its related types.
 *
 * 🟢 You can import this file directly.
 */
import * as runtime from "@prisma/client/runtime/library"
import type * as $Enums from "../enums.ts"
import type * as Prisma from "../internal/prismaNamespace.ts"

/**
 * Model Plantao
 * 
 */
export type PlantaoModel = runtime.Types.Result.DefaultSelection<Prisma.$PlantaoPayload>

export type AggregatePlantao = {
  _count: PlantaoCountAggregateOutputType | null
  _avg: PlantaoAvgAggregateOutputType | null
  _sum: PlantaoSumAggregateOutputType | null
  _min: PlantaoMinAggregateOutputType | null
  _max: PlantaoMaxAggregateOutputType | null
}

export type PlantaoAvgAggregateOutputType = {
  id: number | null
  clienteId: number | null
  localAtendimentoId: number | null
  profissionalId: number | null
  prazoPagamentoDias: number | null
  valorBase: number | null
}

export type PlantaoSumAggregateOutputType = {
  id: number | null
  clienteId: number | null
  localAtendimentoId: number | null
  profissionalId: number | null
  prazoPagamentoDias: number | null
  valorBase: number | null
}

export type PlantaoMinAggregateOutputType = {
  id: number | null
  uuid: string | null
  clienteId: number | null
  localAtendimentoId: number | null
  profissionalId: number | null
  dataInicial: Date | null
  dataFinal: Date | null
  prazoPagamentoDias: number | null
  modalidadeTrabalho: string | null
  tipoFechamento: string | null
  tipoValor: string | null
  valorBase: number | null
  horaInicio: string | null
  horaFim: string | null
  intervalo: string | null
  tipoTurno: string | null
  observacoes: string | null
  fusoHorario: string | null
  createdAt: Date | null
  updatedAt: Date | null
  concluidoEm: Date | null
  deletedAt: Date | null
}

export type PlantaoMaxAggregateOutputType = {
  id: number | null
  uuid: string | null
  clienteId: number | null
  localAtendimentoId: number | null
  profissionalId: number | null
  dataInicial: Date | null
  dataFinal: Date | null
  prazoPagamentoDias: number | null
  modalidadeTrabalho: string | null
  tipoFechamento: string | null
  tipoValor: string | null
  valorBase: number | null
  horaInicio: string | null
  horaFim: string | null
  intervalo: string | null
  tipoTurno: string | null
  observacoes: string | null
  fusoHorario: string | null
  createdAt: Date | null
  updatedAt: Date | null
  concluidoEm: Date | null
  deletedAt: Date | null
}

export type PlantaoCountAggregateOutputType = {
  id: number
  uuid: number
  clienteId: number
  localAtendimentoId: number
  profissionalId: number
  dataInicial: number
  dataFinal: number
  prazoPagamentoDias: number
  modalidadeTrabalho: number
  tipoFechamento: number
  tipoValor: number
  valorBase: number
  horaInicio: number
  horaFim: number
  intervalo: number
  tipoTurno: number
  observacoes: number
  fusoHorario: number
  createdAt: number
  updatedAt: number
  concluidoEm: number
  deletedAt: number
  _all: number
}


export type PlantaoAvgAggregateInputType = {
  id?: true
  clienteId?: true
  localAtendimentoId?: true
  profissionalId?: true
  prazoPagamentoDias?: true
  valorBase?: true
}

export type PlantaoSumAggregateInputType = {
  id?: true
  clienteId?: true
  localAtendimentoId?: true
  profissionalId?: true
  prazoPagamentoDias?: true
  valorBase?: true
}

export type PlantaoMinAggregateInputType = {
  id?: true
  uuid?: true
  clienteId?: true
  localAtendimentoId?: true
  profissionalId?: true
  dataInicial?: true
  dataFinal?: true
  prazoPagamentoDias?: true
  modalidadeTrabalho?: true
  tipoFechamento?: true
  tipoValor?: true
  valorBase?: true
  horaInicio?: true
  horaFim?: true
  intervalo?: true
  tipoTurno?: true
  observacoes?: true
  fusoHorario?: true
  createdAt?: true
  updatedAt?: true
  concluidoEm?: true
  deletedAt?: true
}

export type PlantaoMaxAggregateInputType = {
  id?: true
  uuid?: true
  clienteId?: true
  localAtendimentoId?: true
  profissionalId?: true
  dataInicial?: true
  dataFinal?: true
  prazoPagamentoDias?: true
  modalidadeTrabalho?: true
  tipoFechamento?: true
  tipoValor?: true
  valorBase?: true
  horaInicio?: true
  horaFim?: true
  intervalo?: true
  tipoTurno?: true
  observacoes?: true
  fusoHorario?: true
  createdAt?: true
  updatedAt?: true
  concluidoEm?: true
  deletedAt?: true
}

export type PlantaoCountAggregateInputType = {
  id?: true
  uuid?: true
  clienteId?: true
  localAtendimentoId?: true
  profissionalId?: true
  dataInicial?: true
  dataFinal?: true
  prazoPagamentoDias?: true
  modalidadeTrabalho?: true
  tipoFechamento?: true
  tipoValor?: true
  valorBase?: true
  horaInicio?: true
  horaFim?: true
  intervalo?: true
  tipoTurno?: true
  observacoes?: true
  fusoHorario?: true
  createdAt?: true
  updatedAt?: true
  concluidoEm?: true
  deletedAt?: true
  _all?: true
}

export type PlantaoAggregateArgs<ExtArgs extends runtime.Types.Extensions.InternalArgs = runtime.Types.Extensions.DefaultArgs> = {
  /**
   * Filter which Plantao to aggregate.
   */
  where?: Prisma.PlantaoWhereInput
  /**
   * {@link https://www.prisma.io/docs/concepts/components/prisma-client/sorting Sorting Docs}
   * 
   * Determine the order of Plantaos to fetch.
   */
  orderBy?: Prisma.PlantaoOrderByWithRelationInput | Prisma.PlantaoOrderByWithRelationInput[]
  /**
   * {@link https://www.prisma.io/docs/concepts/components/prisma-client/pagination#cursor-based-pagination Cursor Docs}
   * 
   * Sets the start position
   */
  cursor?: Prisma.PlantaoWhereUniqueInput
  /**
   * {@link https://www.prisma.io/docs/concepts/components/prisma-client/pagination Pagination Docs}
   * 
   * Take `±n` Plantaos from the position of the cursor.
   */
  take?: number
  /**
   * {@link https://www.prisma.io/docs/concepts/components/prisma-client/pagination Pagination Docs}
   * 
   * Skip the first `n` Plantaos.
   */
  skip?: number
  /**
   * {@link https://www.prisma.io/docs/concepts/components/prisma-client/aggregations Aggregation Docs}
   * 
   * Count returned Plantaos
  **/
  _count?: true | PlantaoCountAggregateInputType
  /**
   * {@link https://www.prisma.io/docs/concepts/components/prisma-client/aggregations Aggregation Docs}
   * 
   * Select which fields to average
  **/
  _avg?: PlantaoAvgAggregateInputType
  /**
   * {@link https://www.prisma.io/docs/concepts/components/prisma-client/aggregations Aggregation Docs}
   * 
   * Select which fields to sum
  **/
  _sum?: PlantaoSumAggregateInputType
  /**
   * {@link https://www.prisma.io/docs/concepts/components/prisma-client/aggregations Aggregation Docs}
   * 
   * Select which fields to find the minimum value
  **/
  _min?: PlantaoMinAggregateInputType
  /**
   * {@link https://www.prisma.io/docs/concepts/components/prisma-client/aggregations Aggregation Docs}
   * 
   * Select which fields to find the maximum value
  **/
  _max?: PlantaoMaxAggregateInputType
}

export type GetPlantaoAggregateType<T extends PlantaoAggregateArgs> = {
      [P in keyof T & keyof AggregatePlantao]: P extends '_count' | 'count'
    ? T[P] extends true
      ? number
      : Prisma.GetScalarType<T[P], AggregatePlantao[P]>
    : Prisma.GetScalarType<T[P], AggregatePlantao[P]>
}




export type PlantaoGroupByArgs<ExtArgs extends runtime.Types.Extensions.InternalArgs = runtime.Types.Extensions.DefaultArgs> = {
  where?: Prisma.PlantaoWhereInput
  orderBy?: Prisma.PlantaoOrderByWithAggregationInput | Prisma.PlantaoOrderByWithAggregationInput[]
  by: Prisma.PlantaoScalarFieldEnum[] | Prisma.PlantaoScalarFieldEnum
  having?: Prisma.PlantaoScalarWhereWithAggregatesInput
  take?: number
  skip?: number
  _count?: PlantaoCountAggregateInputType | true
  _avg?: PlantaoAvgAggregateInputType
  _sum?: PlantaoSumAggregateInputType
  _min?: PlantaoMinAggregateInputType
  _max?: PlantaoMaxAggregateInputType
}

export type PlantaoGroupByOutputType = {
  id: number
  uuid: string
  clienteId: number
  localAtendimentoId: number
  profissionalId: number
  dataInicial: Date
  dataFinal: Date | null
  prazoPagamentoDias: number | null
  modalidadeTrabalho: string
  tipoFechamento: string
  tipoValor: string
  valorBase: number
  horaInicio: string
  horaFim: string
  intervalo: string
  tipoTurno: string
  observacoes: string | null
  fusoHorario: string
  createdAt: Date
  updatedAt: Date
  concluidoEm: Date | null
  deletedAt: Date | null
  _count: PlantaoCountAggregateOutputType | null
  _avg: PlantaoAvgAggregateOutputType | null
  _sum: PlantaoSumAggregateOutputType | null
  _min: PlantaoMinAggregateOutputType | null
  _max: PlantaoMaxAggregateOutputType | null
}

type GetPlantaoGroupByPayload<T extends PlantaoGroupByArgs> = Prisma.PrismaPromise<
  Array<
    Prisma.PickEnumerable<PlantaoGroupByOutputType, T['by']> &
      {
        [P in ((keyof T) & (keyof PlantaoGroupByOutputType))]: P extends '_count'
          ? T[P] extends boolean
            ? number
            : Prisma.GetScalarType<T[P], PlantaoGroupByOutputType[P]>
          : Prisma.GetScalarType<T[P], PlantaoGroupByOutputType[P]>
      }
    >
  >



export type PlantaoWhereInput = {
  AND?: Prisma.PlantaoWhereInput | Prisma.PlantaoWhereInput[]
  OR?: Prisma.PlantaoWhereInput[]
  NOT?: Prisma.PlantaoWhereInput | Prisma.PlantaoWhereInput[]
  id?: Prisma.IntFilter<"Plantao"> | number
  uuid?: Prisma.StringFilter<"Plantao"> | string
  clienteId?: Prisma.IntFilter<"Plantao"> | number
  localAtendimentoId?: Prisma.IntFilter<"Plantao"> | number
  profissionalId?: Prisma.IntFilter<"Plantao"> | number
  dataInicial?: Prisma.DateTimeFilter<"Plantao"> | Date | string
  dataFinal?: Prisma.DateTimeNullableFilter<"Plantao"> | Date | string | null
  prazoPagamentoDias?: Prisma.IntNullableFilter<"Plantao"> | number | null
  modalidadeTrabalho?: Prisma.StringFilter<"Plantao"> | string
  tipoFechamento?: Prisma.StringFilter<"Plantao"> | string
  tipoValor?: Prisma.StringFilter<"Plantao"> | string
  valorBase?: Prisma.FloatFilter<"Plantao"> | number
  horaInicio?: Prisma.StringFilter<"Plantao"> | string
  horaFim?: Prisma.StringFilter<"Plantao"> | string
  intervalo?: Prisma.StringFilter<"Plantao"> | string
  tipoTurno?: Prisma.StringFilter<"Plantao"> | string
  observacoes?: Prisma.StringNullableFilter<"Plantao"> | string | null
  fusoHorario?: Prisma.StringFilter<"Plantao"> | string
  createdAt?: Prisma.DateTimeFilter<"Plantao"> | Date | string
  updatedAt?: Prisma.DateTimeFilter<"Plantao"> | Date | string
  concluidoEm?: Prisma.DateTimeNullableFilter<"Plantao"> | Date | string | null
  deletedAt?: Prisma.DateTimeNullableFilter<"Plantao"> | Date | string | null
  cliente?: Prisma.XOR<Prisma.ClienteScalarRelationFilter, Prisma.ClienteWhereInput>
  localAtendimento?: Prisma.XOR<Prisma.LocalAtendimentoScalarRelationFilter, Prisma.LocalAtendimentoWhereInput>
  profissional?: Prisma.XOR<Prisma.ProfissionalScalarRelationFilter, Prisma.ProfissionalWhereInput>
  diasPlantao?: Prisma.DiaPlantaoListRelationFilter
  fechamentos?: Prisma.FechamentoListRelationFilter
  antecipacoes?: Prisma.AntecipacaoListRelationFilter
}

export type PlantaoOrderByWithRelationInput = {
  id?: Prisma.SortOrder
  uuid?: Prisma.SortOrder
  clienteId?: Prisma.SortOrder
  localAtendimentoId?: Prisma.SortOrder
  profissionalId?: Prisma.SortOrder
  dataInicial?: Prisma.SortOrder
  dataFinal?: Prisma.SortOrderInput | Prisma.SortOrder
  prazoPagamentoDias?: Prisma.SortOrderInput | Prisma.SortOrder
  modalidadeTrabalho?: Prisma.SortOrder
  tipoFechamento?: Prisma.SortOrder
  tipoValor?: Prisma.SortOrder
  valorBase?: Prisma.SortOrder
  horaInicio?: Prisma.SortOrder
  horaFim?: Prisma.SortOrder
  intervalo?: Prisma.SortOrder
  tipoTurno?: Prisma.SortOrder
  observacoes?: Prisma.SortOrderInput | Prisma.SortOrder
  fusoHorario?: Prisma.SortOrder
  createdAt?: Prisma.SortOrder
  updatedAt?: Prisma.SortOrder
  concluidoEm?: Prisma.SortOrderInput | Prisma.SortOrder
  deletedAt?: Prisma.SortOrderInput | Prisma.SortOrder
  cliente?: Prisma.ClienteOrderByWithRelationInput
  localAtendimento?: Prisma.LocalAtendimentoOrderByWithRelationInput
  profissional?: Prisma.ProfissionalOrderByWithRelationInput
  diasPlantao?: Prisma.DiaPlantaoOrderByRelationAggregateInput
  fechamentos?: Prisma.FechamentoOrderByRelationAggregateInput
  antecipacoes?: Prisma.AntecipacaoOrderByRelationAggregateInput
  _relevance?: Prisma.PlantaoOrderByRelevanceInput
}

export type PlantaoWhereUniqueInput = Prisma.AtLeast<{
  id?: number
  uuid?: string
  profissionalId_dataInicial_clienteId?: Prisma.PlantaoProfissionalIdDataInicialClienteIdCompoundUniqueInput
  AND?: Prisma.PlantaoWhereInput | Prisma.PlantaoWhereInput[]
  OR?: Prisma.PlantaoWhereInput[]
  NOT?: Prisma.PlantaoWhereInput | Prisma.PlantaoWhereInput[]
  clienteId?: Prisma.IntFilter<"Plantao"> | number
  localAtendimentoId?: Prisma.IntFilter<"Plantao"> | number
  profissionalId?: Prisma.IntFilter<"Plantao"> | number
  dataInicial?: Prisma.DateTimeFilter<"Plantao"> | Date | string
  dataFinal?: Prisma.DateTimeNullableFilter<"Plantao"> | Date | string | null
  prazoPagamentoDias?: Prisma.IntNullableFilter<"Plantao"> | number | null
  modalidadeTrabalho?: Prisma.StringFilter<"Plantao"> | string
  tipoFechamento?: Prisma.StringFilter<"Plantao"> | string
  tipoValor?: Prisma.StringFilter<"Plantao"> | string
  valorBase?: Prisma.FloatFilter<"Plantao"> | number
  horaInicio?: Prisma.StringFilter<"Plantao"> | string
  horaFim?: Prisma.StringFilter<"Plantao"> | string
  intervalo?: Prisma.StringFilter<"Plantao"> | string
  tipoTurno?: Prisma.StringFilter<"Plantao"> | string
  observacoes?: Prisma.StringNullableFilter<"Plantao"> | string | null
  fusoHorario?: Prisma.StringFilter<"Plantao"> | string
  createdAt?: Prisma.DateTimeFilter<"Plantao"> | Date | string
  updatedAt?: Prisma.DateTimeFilter<"Plantao"> | Date | string
  concluidoEm?: Prisma.DateTimeNullableFilter<"Plantao"> | Date | string | null
  deletedAt?: Prisma.DateTimeNullableFilter<"Plantao"> | Date | string | null
  cliente?: Prisma.XOR<Prisma.ClienteScalarRelationFilter, Prisma.ClienteWhereInput>
  localAtendimento?: Prisma.XOR<Prisma.LocalAtendimentoScalarRelationFilter, Prisma.LocalAtendimentoWhereInput>
  profissional?: Prisma.XOR<Prisma.ProfissionalScalarRelationFilter, Prisma.ProfissionalWhereInput>
  diasPlantao?: Prisma.DiaPlantaoListRelationFilter
  fechamentos?: Prisma.FechamentoListRelationFilter
  antecipacoes?: Prisma.AntecipacaoListRelationFilter
}, "id" | "uuid" | "profissionalId_dataInicial_clienteId">

export type PlantaoOrderByWithAggregationInput = {
  id?: Prisma.SortOrder
  uuid?: Prisma.SortOrder
  clienteId?: Prisma.SortOrder
  localAtendimentoId?: Prisma.SortOrder
  profissionalId?: Prisma.SortOrder
  dataInicial?: Prisma.SortOrder
  dataFinal?: Prisma.SortOrderInput | Prisma.SortOrder
  prazoPagamentoDias?: Prisma.SortOrderInput | Prisma.SortOrder
  modalidadeTrabalho?: Prisma.SortOrder
  tipoFechamento?: Prisma.SortOrder
  tipoValor?: Prisma.SortOrder
  valorBase?: Prisma.SortOrder
  horaInicio?: Prisma.SortOrder
  horaFim?: Prisma.SortOrder
  intervalo?: Prisma.SortOrder
  tipoTurno?: Prisma.SortOrder
  observacoes?: Prisma.SortOrderInput | Prisma.SortOrder
  fusoHorario?: Prisma.SortOrder
  createdAt?: Prisma.SortOrder
  updatedAt?: Prisma.SortOrder
  concluidoEm?: Prisma.SortOrderInput | Prisma.SortOrder
  deletedAt?: Prisma.SortOrderInput | Prisma.SortOrder
  _count?: Prisma.PlantaoCountOrderByAggregateInput
  _avg?: Prisma.PlantaoAvgOrderByAggregateInput
  _max?: Prisma.PlantaoMaxOrderByAggregateInput
  _min?: Prisma.PlantaoMinOrderByAggregateInput
  _sum?: Prisma.PlantaoSumOrderByAggregateInput
}

export type PlantaoScalarWhereWithAggregatesInput = {
  AND?: Prisma.PlantaoScalarWhereWithAggregatesInput | Prisma.PlantaoScalarWhereWithAggregatesInput[]
  OR?: Prisma.PlantaoScalarWhereWithAggregatesInput[]
  NOT?: Prisma.PlantaoScalarWhereWithAggregatesInput | Prisma.PlantaoScalarWhereWithAggregatesInput[]
  id?: Prisma.IntWithAggregatesFilter<"Plantao"> | number
  uuid?: Prisma.StringWithAggregatesFilter<"Plantao"> | string
  clienteId?: Prisma.IntWithAggregatesFilter<"Plantao"> | number
  localAtendimentoId?: Prisma.IntWithAggregatesFilter<"Plantao"> | number
  profissionalId?: Prisma.IntWithAggregatesFilter<"Plantao"> | number
  dataInicial?: Prisma.DateTimeWithAggregatesFilter<"Plantao"> | Date | string
  dataFinal?: Prisma.DateTimeNullableWithAggregatesFilter<"Plantao"> | Date | string | null
  prazoPagamentoDias?: Prisma.IntNullableWithAggregatesFilter<"Plantao"> | number | null
  modalidadeTrabalho?: Prisma.StringWithAggregatesFilter<"Plantao"> | string
  tipoFechamento?: Prisma.StringWithAggregatesFilter<"Plantao"> | string
  tipoValor?: Prisma.StringWithAggregatesFilter<"Plantao"> | string
  valorBase?: Prisma.FloatWithAggregatesFilter<"Plantao"> | number
  horaInicio?: Prisma.StringWithAggregatesFilter<"Plantao"> | string
  horaFim?: Prisma.StringWithAggregatesFilter<"Plantao"> | string
  intervalo?: Prisma.StringWithAggregatesFilter<"Plantao"> | string
  tipoTurno?: Prisma.StringWithAggregatesFilter<"Plantao"> | string
  observacoes?: Prisma.StringNullableWithAggregatesFilter<"Plantao"> | string | null
  fusoHorario?: Prisma.StringWithAggregatesFilter<"Plantao"> | string
  createdAt?: Prisma.DateTimeWithAggregatesFilter<"Plantao"> | Date | string
  updatedAt?: Prisma.DateTimeWithAggregatesFilter<"Plantao"> | Date | string
  concluidoEm?: Prisma.DateTimeNullableWithAggregatesFilter<"Plantao"> | Date | string | null
  deletedAt?: Prisma.DateTimeNullableWithAggregatesFilter<"Plantao"> | Date | string | null
}

export type PlantaoCreateInput = {
  uuid?: string
  dataInicial: Date | string
  dataFinal?: Date | string | null
  prazoPagamentoDias?: number | null
  modalidadeTrabalho: string
  tipoFechamento: string
  tipoValor?: string
  valorBase: number
  horaInicio: string
  horaFim: string
  intervalo: string
  tipoTurno: string
  observacoes?: string | null
  fusoHorario?: string
  createdAt?: Date | string
  updatedAt?: Date | string
  concluidoEm?: Date | string | null
  deletedAt?: Date | string | null
  cliente: Prisma.ClienteCreateNestedOneWithoutPlantoesInput
  localAtendimento: Prisma.LocalAtendimentoCreateNestedOneWithoutPlantoesInput
  profissional: Prisma.ProfissionalCreateNestedOneWithoutPlantoesInput
  diasPlantao?: Prisma.DiaPlantaoCreateNestedManyWithoutPlantaoInput
  fechamentos?: Prisma.FechamentoCreateNestedManyWithoutPlantaoInput
  antecipacoes?: Prisma.AntecipacaoCreateNestedManyWithoutPlantaoInput
}

export type PlantaoUncheckedCreateInput = {
  id?: number
  uuid?: string
  clienteId: number
  localAtendimentoId: number
  profissionalId: number
  dataInicial: Date | string
  dataFinal?: Date | string | null
  prazoPagamentoDias?: number | null
  modalidadeTrabalho: string
  tipoFechamento: string
  tipoValor?: string
  valorBase: number
  horaInicio: string
  horaFim: string
  intervalo: string
  tipoTurno: string
  observacoes?: string | null
  fusoHorario?: string
  createdAt?: Date | string
  updatedAt?: Date | string
  concluidoEm?: Date | string | null
  deletedAt?: Date | string | null
  diasPlantao?: Prisma.DiaPlantaoUncheckedCreateNestedManyWithoutPlantaoInput
  fechamentos?: Prisma.FechamentoUncheckedCreateNestedManyWithoutPlantaoInput
  antecipacoes?: Prisma.AntecipacaoUncheckedCreateNestedManyWithoutPlantaoInput
}

export type PlantaoUpdateInput = {
  uuid?: Prisma.StringFieldUpdateOperationsInput | string
  dataInicial?: Prisma.DateTimeFieldUpdateOperationsInput | Date | string
  dataFinal?: Prisma.NullableDateTimeFieldUpdateOperationsInput | Date | string | null
  prazoPagamentoDias?: Prisma.NullableIntFieldUpdateOperationsInput | number | null
  modalidadeTrabalho?: Prisma.StringFieldUpdateOperationsInput | string
  tipoFechamento?: Prisma.StringFieldUpdateOperationsInput | string
  tipoValor?: Prisma.StringFieldUpdateOperationsInput | string
  valorBase?: Prisma.FloatFieldUpdateOperationsInput | number
  horaInicio?: Prisma.StringFieldUpdateOperationsInput | string
  horaFim?: Prisma.StringFieldUpdateOperationsInput | string
  intervalo?: Prisma.StringFieldUpdateOperationsInput | string
  tipoTurno?: Prisma.StringFieldUpdateOperationsInput | string
  observacoes?: Prisma.NullableStringFieldUpdateOperationsInput | string | null
  fusoHorario?: Prisma.StringFieldUpdateOperationsInput | string
  createdAt?: Prisma.DateTimeFieldUpdateOperationsInput | Date | string
  updatedAt?: Prisma.DateTimeFieldUpdateOperationsInput | Date | string
  concluidoEm?: Prisma.NullableDateTimeFieldUpdateOperationsInput | Date | string | null
  deletedAt?: Prisma.NullableDateTimeFieldUpdateOperationsInput | Date | string | null
  cliente?: Prisma.ClienteUpdateOneRequiredWithoutPlantoesNestedInput
  localAtendimento?: Prisma.LocalAtendimentoUpdateOneRequiredWithoutPlantoesNestedInput
  profissional?: Prisma.ProfissionalUpdateOneRequiredWithoutPlantoesNestedInput
  diasPlantao?: Prisma.DiaPlantaoUpdateManyWithoutPlantaoNestedInput
  fechamentos?: Prisma.FechamentoUpdateManyWithoutPlantaoNestedInput
  antecipacoes?: Prisma.AntecipacaoUpdateManyWithoutPlantaoNestedInput
}

export type PlantaoUncheckedUpdateInput = {
  id?: Prisma.IntFieldUpdateOperationsInput | number
  uuid?: Prisma.StringFieldUpdateOperationsInput | string
  clienteId?: Prisma.IntFieldUpdateOperationsInput | number
  localAtendimentoId?: Prisma.IntFieldUpdateOperationsInput | number
  profissionalId?: Prisma.IntFieldUpdateOperationsInput | number
  dataInicial?: Prisma.DateTimeFieldUpdateOperationsInput | Date | string
  dataFinal?: Prisma.NullableDateTimeFieldUpdateOperationsInput | Date | string | null
  prazoPagamentoDias?: Prisma.NullableIntFieldUpdateOperationsInput | number | null
  modalidadeTrabalho?: Prisma.StringFieldUpdateOperationsInput | string
  tipoFechamento?: Prisma.StringFieldUpdateOperationsInput | string
  tipoValor?: Prisma.StringFieldUpdateOperationsInput | string
  valorBase?: Prisma.FloatFieldUpdateOperationsInput | number
  horaInicio?: Prisma.StringFieldUpdateOperationsInput | string
  horaFim?: Prisma.StringFieldUpdateOperationsInput | string
  intervalo?: Prisma.StringFieldUpdateOperationsInput | string
  tipoTurno?: Prisma.StringFieldUpdateOperationsInput | string
  observacoes?: Prisma.NullableStringFieldUpdateOperationsInput | string | null
  fusoHorario?: Prisma.StringFieldUpdateOperationsInput | string
  createdAt?: Prisma.DateTimeFieldUpdateOperationsInput | Date | string
  updatedAt?: Prisma.DateTimeFieldUpdateOperationsInput | Date | string
  concluidoEm?: Prisma.NullableDateTimeFieldUpdateOperationsInput | Date | string | null
  deletedAt?: Prisma.NullableDateTimeFieldUpdateOperationsInput | Date | string | null
  diasPlantao?: Prisma.DiaPlantaoUncheckedUpdateManyWithoutPlantaoNestedInput
  fechamentos?: Prisma.FechamentoUncheckedUpdateManyWithoutPlantaoNestedInput
  antecipacoes?: Prisma.AntecipacaoUncheckedUpdateManyWithoutPlantaoNestedInput
}

export type PlantaoCreateManyInput = {
  id?: number
  uuid?: string
  clienteId: number
  localAtendimentoId: number
  profissionalId: number
  dataInicial: Date | string
  dataFinal?: Date | string | null
  prazoPagamentoDias?: number | null
  modalidadeTrabalho: string
  tipoFechamento: string
  tipoValor?: string
  valorBase: number
  horaInicio: string
  horaFim: string
  intervalo: string
  tipoTurno: string
  observacoes?: string | null
  fusoHorario?: string
  createdAt?: Date | string
  updatedAt?: Date | string
  concluidoEm?: Date | string | null
  deletedAt?: Date | string | null
}

export type PlantaoUpdateManyMutationInput = {
  uuid?: Prisma.StringFieldUpdateOperationsInput | string
  dataInicial?: Prisma.DateTimeFieldUpdateOperationsInput | Date | string
  dataFinal?: Prisma.NullableDateTimeFieldUpdateOperationsInput | Date | string | null
  prazoPagamentoDias?: Prisma.NullableIntFieldUpdateOperationsInput | number | null
  modalidadeTrabalho?: Prisma.StringFieldUpdateOperationsInput | string
  tipoFechamento?: Prisma.StringFieldUpdateOperationsInput | string
  tipoValor?: Prisma.StringFieldUpdateOperationsInput | string
  valorBase?: Prisma.FloatFieldUpdateOperationsInput | number
  horaInicio?: Prisma.StringFieldUpdateOperationsInput | string
  horaFim?: Prisma.StringFieldUpdateOperationsInput | string
  intervalo?: Prisma.StringFieldUpdateOperationsInput | string
  tipoTurno?: Prisma.StringFieldUpdateOperationsInput | string
  observacoes?: Prisma.NullableStringFieldUpdateOperationsInput | string | null
  fusoHorario?: Prisma.StringFieldUpdateOperationsInput | string
  createdAt?: Prisma.DateTimeFieldUpdateOperationsInput | Date | string
  updatedAt?: Prisma.DateTimeFieldUpdateOperationsInput | Date | string
  concluidoEm?: Prisma.NullableDateTimeFieldUpdateOperationsInput | Date | string | null
  deletedAt?: Prisma.NullableDateTimeFieldUpdateOperationsInput | Date | string | null
}

export type PlantaoUncheckedUpdateManyInput = {
  id?: Prisma.IntFieldUpdateOperationsInput | number
  uuid?: Prisma.StringFieldUpdateOperationsInput | string
  clienteId?: Prisma.IntFieldUpdateOperationsInput | number
  localAtendimentoId?: Prisma.IntFieldUpdateOperationsInput | number
  profissionalId?: Prisma.IntFieldUpdateOperationsInput | number
  dataInicial?: Prisma.DateTimeFieldUpdateOperationsInput | Date | string
  dataFinal?: Prisma.NullableDateTimeFieldUpdateOperationsInput | Date | string | null
  prazoPagamentoDias?: Prisma.NullableIntFieldUpdateOperationsInput | number | null
  modalidadeTrabalho?: Prisma.StringFieldUpdateOperationsInput | string
  tipoFechamento?: Prisma.StringFieldUpdateOperationsInput | string
  tipoValor?: Prisma.StringFieldUpdateOperationsInput | string
  valorBase?: Prisma.FloatFieldUpdateOperationsInput | number
  horaInicio?: Prisma.StringFieldUpdateOperationsInput | string
  horaFim?: Prisma.StringFieldUpdateOperationsInput | string
  intervalo?: Prisma.StringFieldUpdateOperationsInput | string
  tipoTurno?: Prisma.StringFieldUpdateOperationsInput | string
  observacoes?: Prisma.NullableStringFieldUpdateOperationsInput | string | null
  fusoHorario?: Prisma.StringFieldUpdateOperationsInput | string
  createdAt?: Prisma.DateTimeFieldUpdateOperationsInput | Date | string
  updatedAt?: Prisma.DateTimeFieldUpdateOperationsInput | Date | string
  concluidoEm?: Prisma.NullableDateTimeFieldUpdateOperationsInput | Date | string | null
  deletedAt?: Prisma.NullableDateTimeFieldUpdateOperationsInput | Date | string | null
}

export type PlantaoListRelationFilter = {
  every?: Prisma.PlantaoWhereInput
  some?: Prisma.PlantaoWhereInput
  none?: Prisma.PlantaoWhereInput
}

export type PlantaoOrderByRelationAggregateInput = {
  _count?: Prisma.SortOrder
}

export type PlantaoOrderByRelevanceInput = {
  fields: Prisma.PlantaoOrderByRelevanceFieldEnum | Prisma.PlantaoOrderByRelevanceFieldEnum[]
  sort: Prisma.SortOrder
  search: string
}

export type PlantaoProfissionalIdDataInicialClienteIdCompoundUniqueInput = {
  profissionalId: number
  dataInicial: Date | string
  clienteId: number
}

export type PlantaoCountOrderByAggregateInput = {
  id?: Prisma.SortOrder
  uuid?: Prisma.SortOrder
  clienteId?: Prisma.SortOrder
  localAtendimentoId?: Prisma.SortOrder
  profissionalId?: Prisma.SortOrder
  dataInicial?: Prisma.SortOrder
  dataFinal?: Prisma.SortOrder
  prazoPagamentoDias?: Prisma.SortOrder
  modalidadeTrabalho?: Prisma.SortOrder
  tipoFechamento?: Prisma.SortOrder
  tipoValor?: Prisma.SortOrder
  valorBase?: Prisma.SortOrder
  horaInicio?: Prisma.SortOrder
  horaFim?: Prisma.SortOrder
  intervalo?: Prisma.SortOrder
  tipoTurno?: Prisma.SortOrder
  observacoes?: Prisma.SortOrder
  fusoHorario?: Prisma.SortOrder
  createdAt?: Prisma.SortOrder
  updatedAt?: Prisma.SortOrder
  concluidoEm?: Prisma.SortOrder
  deletedAt?: Prisma.SortOrder
}

export type PlantaoAvgOrderByAggregateInput = {
  id?: Prisma.SortOrder
  clienteId?: Prisma.SortOrder
  localAtendimentoId?: Prisma.SortOrder
  profissionalId?: Prisma.SortOrder
  prazoPagamentoDias?: Prisma.SortOrder
  valorBase?: Prisma.SortOrder
}

export type PlantaoMaxOrderByAggregateInput = {
  id?: Prisma.SortOrder
  uuid?: Prisma.SortOrder
  clienteId?: Prisma.SortOrder
  localAtendimentoId?: Prisma.SortOrder
  profissionalId?: Prisma.SortOrder
  dataInicial?: Prisma.SortOrder
  dataFinal?: Prisma.SortOrder
  prazoPagamentoDias?: Prisma.SortOrder
  modalidadeTrabalho?: Prisma.SortOrder
  tipoFechamento?: Prisma.SortOrder
  tipoValor?: Prisma.SortOrder
  valorBase?: Prisma.SortOrder
  horaInicio?: Prisma.SortOrder
  horaFim?: Prisma.SortOrder
  intervalo?: Prisma.SortOrder
  tipoTurno?: Prisma.SortOrder
  observacoes?: Prisma.SortOrder
  fusoHorario?: Prisma.SortOrder
  createdAt?: Prisma.SortOrder
  updatedAt?: Prisma.SortOrder
  concluidoEm?: Prisma.SortOrder
  deletedAt?: Prisma.SortOrder
}

export type PlantaoMinOrderByAggregateInput = {
  id?: Prisma.SortOrder
  uuid?: Prisma.SortOrder
  clienteId?: Prisma.SortOrder
  localAtendimentoId?: Prisma.SortOrder
  profissionalId?: Prisma.SortOrder
  dataInicial?: Prisma.SortOrder
  dataFinal?: Prisma.SortOrder
  prazoPagamentoDias?: Prisma.SortOrder
  modalidadeTrabalho?: Prisma.SortOrder
  tipoFechamento?: Prisma.SortOrder
  tipoValor?: Prisma.SortOrder
  valorBase?: Prisma.SortOrder
  horaInicio?: Prisma.SortOrder
  horaFim?: Prisma.SortOrder
  intervalo?: Prisma.SortOrder
  tipoTurno?: Prisma.SortOrder
  observacoes?: Prisma.SortOrder
  fusoHorario?: Prisma.SortOrder
  createdAt?: Prisma.SortOrder
  updatedAt?: Prisma.SortOrder
  concluidoEm?: Prisma.SortOrder
  deletedAt?: Prisma.SortOrder
}

export type PlantaoSumOrderByAggregateInput = {
  id?: Prisma.SortOrder
  clienteId?: Prisma.SortOrder
  localAtendimentoId?: Prisma.SortOrder
  profissionalId?: Prisma.SortOrder
  prazoPagamentoDias?: Prisma.SortOrder
  valorBase?: Prisma.SortOrder
}

export type PlantaoScalarRelationFilter = {
  is?: Prisma.PlantaoWhereInput
  isNot?: Prisma.PlantaoWhereInput
}

export type PlantaoCreateNestedManyWithoutClienteInput = {
  create?: Prisma.XOR<Prisma.PlantaoCreateWithoutClienteInput, Prisma.PlantaoUncheckedCreateWithoutClienteInput> | Prisma.PlantaoCreateWithoutClienteInput[] | Prisma.PlantaoUncheckedCreateWithoutClienteInput[]
  connectOrCreate?: Prisma.PlantaoCreateOrConnectWithoutClienteInput | Prisma.PlantaoCreateOrConnectWithoutClienteInput[]
  createMany?: Prisma.PlantaoCreateManyClienteInputEnvelope
  connect?: Prisma.PlantaoWhereUniqueInput | Prisma.PlantaoWhereUniqueInput[]
}

export type PlantaoUncheckedCreateNestedManyWithoutClienteInput = {
  create?: Prisma.XOR<Prisma.PlantaoCreateWithoutClienteInput, Prisma.PlantaoUncheckedCreateWithoutClienteInput> | Prisma.PlantaoCreateWithoutClienteInput[] | Prisma.PlantaoUncheckedCreateWithoutClienteInput[]
  connectOrCreate?: Prisma.PlantaoCreateOrConnectWithoutClienteInput | Prisma.PlantaoCreateOrConnectWithoutClienteInput[]
  createMany?: Prisma.PlantaoCreateManyClienteInputEnvelope
  connect?: Prisma.PlantaoWhereUniqueInput | Prisma.PlantaoWhereUniqueInput[]
}

export type PlantaoUpdateManyWithoutClienteNestedInput = {
  create?: Prisma.XOR<Prisma.PlantaoCreateWithoutClienteInput, Prisma.PlantaoUncheckedCreateWithoutClienteInput> | Prisma.PlantaoCreateWithoutClienteInput[] | Prisma.PlantaoUncheckedCreateWithoutClienteInput[]
  connectOrCreate?: Prisma.PlantaoCreateOrConnectWithoutClienteInput | Prisma.PlantaoCreateOrConnectWithoutClienteInput[]
  upsert?: Prisma.PlantaoUpsertWithWhereUniqueWithoutClienteInput | Prisma.PlantaoUpsertWithWhereUniqueWithoutClienteInput[]
  createMany?: Prisma.PlantaoCreateManyClienteInputEnvelope
  set?: Prisma.PlantaoWhereUniqueInput | Prisma.PlantaoWhereUniqueInput[]
  disconnect?: Prisma.PlantaoWhereUniqueInput | Prisma.PlantaoWhereUniqueInput[]
  delete?: Prisma.PlantaoWhereUniqueInput | Prisma.PlantaoWhereUniqueInput[]
  connect?: Prisma.PlantaoWhereUniqueInput | Prisma.PlantaoWhereUniqueInput[]
  update?: Prisma.PlantaoUpdateWithWhereUniqueWithoutClienteInput | Prisma.PlantaoUpdateWithWhereUniqueWithoutClienteInput[]
  updateMany?: Prisma.PlantaoUpdateManyWithWhereWithoutClienteInput | Prisma.PlantaoUpdateManyWithWhereWithoutClienteInput[]
  deleteMany?: Prisma.PlantaoScalarWhereInput | Prisma.PlantaoScalarWhereInput[]
}

export type PlantaoUncheckedUpdateManyWithoutClienteNestedInput = {
  create?: Prisma.XOR<Prisma.PlantaoCreateWithoutClienteInput, Prisma.PlantaoUncheckedCreateWithoutClienteInput> | Prisma.PlantaoCreateWithoutClienteInput[] | Prisma.PlantaoUncheckedCreateWithoutClienteInput[]
  connectOrCreate?: Prisma.PlantaoCreateOrConnectWithoutClienteInput | Prisma.PlantaoCreateOrConnectWithoutClienteInput[]
  upsert?: Prisma.PlantaoUpsertWithWhereUniqueWithoutClienteInput | Prisma.PlantaoUpsertWithWhereUniqueWithoutClienteInput[]
  createMany?: Prisma.PlantaoCreateManyClienteInputEnvelope
  set?: Prisma.PlantaoWhereUniqueInput | Prisma.PlantaoWhereUniqueInput[]
  disconnect?: Prisma.PlantaoWhereUniqueInput | Prisma.PlantaoWhereUniqueInput[]
  delete?: Prisma.PlantaoWhereUniqueInput | Prisma.PlantaoWhereUniqueInput[]
  connect?: Prisma.PlantaoWhereUniqueInput | Prisma.PlantaoWhereUniqueInput[]
  update?: Prisma.PlantaoUpdateWithWhereUniqueWithoutClienteInput | Prisma.PlantaoUpdateWithWhereUniqueWithoutClienteInput[]
  updateMany?: Prisma.PlantaoUpdateManyWithWhereWithoutClienteInput | Prisma.PlantaoUpdateManyWithWhereWithoutClienteInput[]
  deleteMany?: Prisma.PlantaoScalarWhereInput | Prisma.PlantaoScalarWhereInput[]
}

export type PlantaoCreateNestedManyWithoutProfissionalInput = {
  create?: Prisma.XOR<Prisma.PlantaoCreateWithoutProfissionalInput, Prisma.PlantaoUncheckedCreateWithoutProfissionalInput> | Prisma.PlantaoCreateWithoutProfissionalInput[] | Prisma.PlantaoUncheckedCreateWithoutProfissionalInput[]
  connectOrCreate?: Prisma.PlantaoCreateOrConnectWithoutProfissionalInput | Prisma.PlantaoCreateOrConnectWithoutProfissionalInput[]
  createMany?: Prisma.PlantaoCreateManyProfissionalInputEnvelope
  connect?: Prisma.PlantaoWhereUniqueInput | Prisma.PlantaoWhereUniqueInput[]
}

export type PlantaoUncheckedCreateNestedManyWithoutProfissionalInput = {
  create?: Prisma.XOR<Prisma.PlantaoCreateWithoutProfissionalInput, Prisma.PlantaoUncheckedCreateWithoutProfissionalInput> | Prisma.PlantaoCreateWithoutProfissionalInput[] | Prisma.PlantaoUncheckedCreateWithoutProfissionalInput[]
  connectOrCreate?: Prisma.PlantaoCreateOrConnectWithoutProfissionalInput | Prisma.PlantaoCreateOrConnectWithoutProfissionalInput[]
  createMany?: Prisma.PlantaoCreateManyProfissionalInputEnvelope
  connect?: Prisma.PlantaoWhereUniqueInput | Prisma.PlantaoWhereUniqueInput[]
}

export type PlantaoUpdateManyWithoutProfissionalNestedInput = {
  create?: Prisma.XOR<Prisma.PlantaoCreateWithoutProfissionalInput, Prisma.PlantaoUncheckedCreateWithoutProfissionalInput> | Prisma.PlantaoCreateWithoutProfissionalInput[] | Prisma.PlantaoUncheckedCreateWithoutProfissionalInput[]
  connectOrCreate?: Prisma.PlantaoCreateOrConnectWithoutProfissionalInput | Prisma.PlantaoCreateOrConnectWithoutProfissionalInput[]
  upsert?: Prisma.PlantaoUpsertWithWhereUniqueWithoutProfissionalInput | Prisma.PlantaoUpsertWithWhereUniqueWithoutProfissionalInput[]
  createMany?: Prisma.PlantaoCreateManyProfissionalInputEnvelope
  set?: Prisma.PlantaoWhereUniqueInput | Prisma.PlantaoWhereUniqueInput[]
  disconnect?: Prisma.PlantaoWhereUniqueInput | Prisma.PlantaoWhereUniqueInput[]
  delete?: Prisma.PlantaoWhereUniqueInput | Prisma.PlantaoWhereUniqueInput[]
  connect?: Prisma.PlantaoWhereUniqueInput | Prisma.PlantaoWhereUniqueInput[]
  update?: Prisma.PlantaoUpdateWithWhereUniqueWithoutProfissionalInput | Prisma.PlantaoUpdateWithWhereUniqueWithoutProfissionalInput[]
  updateMany?: Prisma.PlantaoUpdateManyWithWhereWithoutProfissionalInput | Prisma.PlantaoUpdateManyWithWhereWithoutProfissionalInput[]
  deleteMany?: Prisma.PlantaoScalarWhereInput | Prisma.PlantaoScalarWhereInput[]
}

export type PlantaoUncheckedUpdateManyWithoutProfissionalNestedInput = {
  create?: Prisma.XOR<Prisma.PlantaoCreateWithoutProfissionalInput, Prisma.PlantaoUncheckedCreateWithoutProfissionalInput> | Prisma.PlantaoCreateWithoutProfissionalInput[] | Prisma.PlantaoUncheckedCreateWithoutProfissionalInput[]
  connectOrCreate?: Prisma.PlantaoCreateOrConnectWithoutProfissionalInput | Prisma.PlantaoCreateOrConnectWithoutProfissionalInput[]
  upsert?: Prisma.PlantaoUpsertWithWhereUniqueWithoutProfissionalInput | Prisma.PlantaoUpsertWithWhereUniqueWithoutProfissionalInput[]
  createMany?: Prisma.PlantaoCreateManyProfissionalInputEnvelope
  set?: Prisma.PlantaoWhereUniqueInput | Prisma.PlantaoWhereUniqueInput[]
  disconnect?: Prisma.PlantaoWhereUniqueInput | Prisma.PlantaoWhereUniqueInput[]
  delete?: Prisma.PlantaoWhereUniqueInput | Prisma.PlantaoWhereUniqueInput[]
  connect?: Prisma.PlantaoWhereUniqueInput | Prisma.PlantaoWhereUniqueInput[]
  update?: Prisma.PlantaoUpdateWithWhereUniqueWithoutProfissionalInput | Prisma.PlantaoUpdateWithWhereUniqueWithoutProfissionalInput[]
  updateMany?: Prisma.PlantaoUpdateManyWithWhereWithoutProfissionalInput | Prisma.PlantaoUpdateManyWithWhereWithoutProfissionalInput[]
  deleteMany?: Prisma.PlantaoScalarWhereInput | Prisma.PlantaoScalarWhereInput[]
}

export type PlantaoCreateNestedManyWithoutLocalAtendimentoInput = {
  create?: Prisma.XOR<Prisma.PlantaoCreateWithoutLocalAtendimentoInput, Prisma.PlantaoUncheckedCreateWithoutLocalAtendimentoInput> | Prisma.PlantaoCreateWithoutLocalAtendimentoInput[] | Prisma.PlantaoUncheckedCreateWithoutLocalAtendimentoInput[]
  connectOrCreate?: Prisma.PlantaoCreateOrConnectWithoutLocalAtendimentoInput | Prisma.PlantaoCreateOrConnectWithoutLocalAtendimentoInput[]
  createMany?: Prisma.PlantaoCreateManyLocalAtendimentoInputEnvelope
  connect?: Prisma.PlantaoWhereUniqueInput | Prisma.PlantaoWhereUniqueInput[]
}

export type PlantaoUncheckedCreateNestedManyWithoutLocalAtendimentoInput = {
  create?: Prisma.XOR<Prisma.PlantaoCreateWithoutLocalAtendimentoInput, Prisma.PlantaoUncheckedCreateWithoutLocalAtendimentoInput> | Prisma.PlantaoCreateWithoutLocalAtendimentoInput[] | Prisma.PlantaoUncheckedCreateWithoutLocalAtendimentoInput[]
  connectOrCreate?: Prisma.PlantaoCreateOrConnectWithoutLocalAtendimentoInput | Prisma.PlantaoCreateOrConnectWithoutLocalAtendimentoInput[]
  createMany?: Prisma.PlantaoCreateManyLocalAtendimentoInputEnvelope
  connect?: Prisma.PlantaoWhereUniqueInput | Prisma.PlantaoWhereUniqueInput[]
}

export type PlantaoUpdateManyWithoutLocalAtendimentoNestedInput = {
  create?: Prisma.XOR<Prisma.PlantaoCreateWithoutLocalAtendimentoInput, Prisma.PlantaoUncheckedCreateWithoutLocalAtendimentoInput> | Prisma.PlantaoCreateWithoutLocalAtendimentoInput[] | Prisma.PlantaoUncheckedCreateWithoutLocalAtendimentoInput[]
  connectOrCreate?: Prisma.PlantaoCreateOrConnectWithoutLocalAtendimentoInput | Prisma.PlantaoCreateOrConnectWithoutLocalAtendimentoInput[]
  upsert?: Prisma.PlantaoUpsertWithWhereUniqueWithoutLocalAtendimentoInput | Prisma.PlantaoUpsertWithWhereUniqueWithoutLocalAtendimentoInput[]
  createMany?: Prisma.PlantaoCreateManyLocalAtendimentoInputEnvelope
  set?: Prisma.PlantaoWhereUniqueInput | Prisma.PlantaoWhereUniqueInput[]
  disconnect?: Prisma.PlantaoWhereUniqueInput | Prisma.PlantaoWhereUniqueInput[]
  delete?: Prisma.PlantaoWhereUniqueInput | Prisma.PlantaoWhereUniqueInput[]
  connect?: Prisma.PlantaoWhereUniqueInput | Prisma.PlantaoWhereUniqueInput[]
  update?: Prisma.PlantaoUpdateWithWhereUniqueWithoutLocalAtendimentoInput | Prisma.PlantaoUpdateWithWhereUniqueWithoutLocalAtendimentoInput[]
  updateMany?: Prisma.PlantaoUpdateManyWithWhereWithoutLocalAtendimentoInput | Prisma.PlantaoUpdateManyWithWhereWithoutLocalAtendimentoInput[]
  deleteMany?: Prisma.PlantaoScalarWhereInput | Prisma.PlantaoScalarWhereInput[]
}

export type PlantaoUncheckedUpdateManyWithoutLocalAtendimentoNestedInput = {
  create?: Prisma.XOR<Prisma.PlantaoCreateWithoutLocalAtendimentoInput, Prisma.PlantaoUncheckedCreateWithoutLocalAtendimentoInput> | Prisma.PlantaoCreateWithoutLocalAtendimentoInput[] | Prisma.PlantaoUncheckedCreateWithoutLocalAtendimentoInput[]
  connectOrCreate?: Prisma.PlantaoCreateOrConnectWithoutLocalAtendimentoInput | Prisma.PlantaoCreateOrConnectWithoutLocalAtendimentoInput[]
  upsert?: Prisma.PlantaoUpsertWithWhereUniqueWithoutLocalAtendimentoInput | Prisma.PlantaoUpsertWithWhereUniqueWithoutLocalAtendimentoInput[]
  createMany?: Prisma.PlantaoCreateManyLocalAtendimentoInputEnvelope
  set?: Prisma.PlantaoWhereUniqueInput | Prisma.PlantaoWhereUniqueInput[]
  disconnect?: Prisma.PlantaoWhereUniqueInput | Prisma.PlantaoWhereUniqueInput[]
  delete?: Prisma.PlantaoWhereUniqueInput | Prisma.PlantaoWhereUniqueInput[]
  connect?: Prisma.PlantaoWhereUniqueInput | Prisma.PlantaoWhereUniqueInput[]
  update?: Prisma.PlantaoUpdateWithWhereUniqueWithoutLocalAtendimentoInput | Prisma.PlantaoUpdateWithWhereUniqueWithoutLocalAtendimentoInput[]
  updateMany?: Prisma.PlantaoUpdateManyWithWhereWithoutLocalAtendimentoInput | Prisma.PlantaoUpdateManyWithWhereWithoutLocalAtendimentoInput[]
  deleteMany?: Prisma.PlantaoScalarWhereInput | Prisma.PlantaoScalarWhereInput[]
}

export type NullableIntFieldUpdateOperationsInput = {
  set?: number | null
  increment?: number
  decrement?: number
  multiply?: number
  divide?: number
}

export type PlantaoCreateNestedOneWithoutDiasPlantaoInput = {
  create?: Prisma.XOR<Prisma.PlantaoCreateWithoutDiasPlantaoInput, Prisma.PlantaoUncheckedCreateWithoutDiasPlantaoInput>
  connectOrCreate?: Prisma.PlantaoCreateOrConnectWithoutDiasPlantaoInput
  connect?: Prisma.PlantaoWhereUniqueInput
}

export type PlantaoUpdateOneRequiredWithoutDiasPlantaoNestedInput = {
  create?: Prisma.XOR<Prisma.PlantaoCreateWithoutDiasPlantaoInput, Prisma.PlantaoUncheckedCreateWithoutDiasPlantaoInput>
  connectOrCreate?: Prisma.PlantaoCreateOrConnectWithoutDiasPlantaoInput
  upsert?: Prisma.PlantaoUpsertWithoutDiasPlantaoInput
  connect?: Prisma.PlantaoWhereUniqueInput
  update?: Prisma.XOR<Prisma.XOR<Prisma.PlantaoUpdateToOneWithWhereWithoutDiasPlantaoInput, Prisma.PlantaoUpdateWithoutDiasPlantaoInput>, Prisma.PlantaoUncheckedUpdateWithoutDiasPlantaoInput>
}

export type PlantaoCreateNestedOneWithoutFechamentosInput = {
  create?: Prisma.XOR<Prisma.PlantaoCreateWithoutFechamentosInput, Prisma.PlantaoUncheckedCreateWithoutFechamentosInput>
  connectOrCreate?: Prisma.PlantaoCreateOrConnectWithoutFechamentosInput
  connect?: Prisma.PlantaoWhereUniqueInput
}

export type PlantaoUpdateOneRequiredWithoutFechamentosNestedInput = {
  create?: Prisma.XOR<Prisma.PlantaoCreateWithoutFechamentosInput, Prisma.PlantaoUncheckedCreateWithoutFechamentosInput>
  connectOrCreate?: Prisma.PlantaoCreateOrConnectWithoutFechamentosInput
  upsert?: Prisma.PlantaoUpsertWithoutFechamentosInput
  connect?: Prisma.PlantaoWhereUniqueInput
  update?: Prisma.XOR<Prisma.XOR<Prisma.PlantaoUpdateToOneWithWhereWithoutFechamentosInput, Prisma.PlantaoUpdateWithoutFechamentosInput>, Prisma.PlantaoUncheckedUpdateWithoutFechamentosInput>
}

export type PlantaoCreateNestedOneWithoutAntecipacoesInput = {
  create?: Prisma.XOR<Prisma.PlantaoCreateWithoutAntecipacoesInput, Prisma.PlantaoUncheckedCreateWithoutAntecipacoesInput>
  connectOrCreate?: Prisma.PlantaoCreateOrConnectWithoutAntecipacoesInput
  connect?: Prisma.PlantaoWhereUniqueInput
}

export type PlantaoUpdateOneRequiredWithoutAntecipacoesNestedInput = {
  create?: Prisma.XOR<Prisma.PlantaoCreateWithoutAntecipacoesInput, Prisma.PlantaoUncheckedCreateWithoutAntecipacoesInput>
  connectOrCreate?: Prisma.PlantaoCreateOrConnectWithoutAntecipacoesInput
  upsert?: Prisma.PlantaoUpsertWithoutAntecipacoesInput
  connect?: Prisma.PlantaoWhereUniqueInput
  update?: Prisma.XOR<Prisma.XOR<Prisma.PlantaoUpdateToOneWithWhereWithoutAntecipacoesInput, Prisma.PlantaoUpdateWithoutAntecipacoesInput>, Prisma.PlantaoUncheckedUpdateWithoutAntecipacoesInput>
}

export type PlantaoCreateWithoutClienteInput = {
  uuid?: string
  dataInicial: Date | string
  dataFinal?: Date | string | null
  prazoPagamentoDias?: number | null
  modalidadeTrabalho: string
  tipoFechamento: string
  tipoValor?: string
  valorBase: number
  horaInicio: string
  horaFim: string
  intervalo: string
  tipoTurno: string
  observacoes?: string | null
  fusoHorario?: string
  createdAt?: Date | string
  updatedAt?: Date | string
  concluidoEm?: Date | string | null
  deletedAt?: Date | string | null
  localAtendimento: Prisma.LocalAtendimentoCreateNestedOneWithoutPlantoesInput
  profissional: Prisma.ProfissionalCreateNestedOneWithoutPlantoesInput
  diasPlantao?: Prisma.DiaPlantaoCreateNestedManyWithoutPlantaoInput
  fechamentos?: Prisma.FechamentoCreateNestedManyWithoutPlantaoInput
  antecipacoes?: Prisma.AntecipacaoCreateNestedManyWithoutPlantaoInput
}

export type PlantaoUncheckedCreateWithoutClienteInput = {
  id?: number
  uuid?: string
  localAtendimentoId: number
  profissionalId: number
  dataInicial: Date | string
  dataFinal?: Date | string | null
  prazoPagamentoDias?: number | null
  modalidadeTrabalho: string
  tipoFechamento: string
  tipoValor?: string
  valorBase: number
  horaInicio: string
  horaFim: string
  intervalo: string
  tipoTurno: string
  observacoes?: string | null
  fusoHorario?: string
  createdAt?: Date | string
  updatedAt?: Date | string
  concluidoEm?: Date | string | null
  deletedAt?: Date | string | null
  diasPlantao?: Prisma.DiaPlantaoUncheckedCreateNestedManyWithoutPlantaoInput
  fechamentos?: Prisma.FechamentoUncheckedCreateNestedManyWithoutPlantaoInput
  antecipacoes?: Prisma.AntecipacaoUncheckedCreateNestedManyWithoutPlantaoInput
}

export type PlantaoCreateOrConnectWithoutClienteInput = {
  where: Prisma.PlantaoWhereUniqueInput
  create: Prisma.XOR<Prisma.PlantaoCreateWithoutClienteInput, Prisma.PlantaoUncheckedCreateWithoutClienteInput>
}

export type PlantaoCreateManyClienteInputEnvelope = {
  data: Prisma.PlantaoCreateManyClienteInput | Prisma.PlantaoCreateManyClienteInput[]
  skipDuplicates?: boolean
}

export type PlantaoUpsertWithWhereUniqueWithoutClienteInput = {
  where: Prisma.PlantaoWhereUniqueInput
  update: Prisma.XOR<Prisma.PlantaoUpdateWithoutClienteInput, Prisma.PlantaoUncheckedUpdateWithoutClienteInput>
  create: Prisma.XOR<Prisma.PlantaoCreateWithoutClienteInput, Prisma.PlantaoUncheckedCreateWithoutClienteInput>
}

export type PlantaoUpdateWithWhereUniqueWithoutClienteInput = {
  where: Prisma.PlantaoWhereUniqueInput
  data: Prisma.XOR<Prisma.PlantaoUpdateWithoutClienteInput, Prisma.PlantaoUncheckedUpdateWithoutClienteInput>
}

export type PlantaoUpdateManyWithWhereWithoutClienteInput = {
  where: Prisma.PlantaoScalarWhereInput
  data: Prisma.XOR<Prisma.PlantaoUpdateManyMutationInput, Prisma.PlantaoUncheckedUpdateManyWithoutClienteInput>
}

export type PlantaoScalarWhereInput = {
  AND?: Prisma.PlantaoScalarWhereInput | Prisma.PlantaoScalarWhereInput[]
  OR?: Prisma.PlantaoScalarWhereInput[]
  NOT?: Prisma.PlantaoScalarWhereInput | Prisma.PlantaoScalarWhereInput[]
  id?: Prisma.IntFilter<"Plantao"> | number
  uuid?: Prisma.StringFilter<"Plantao"> | string
  clienteId?: Prisma.IntFilter<"Plantao"> | number
  localAtendimentoId?: Prisma.IntFilter<"Plantao"> | number
  profissionalId?: Prisma.IntFilter<"Plantao"> | number
  dataInicial?: Prisma.DateTimeFilter<"Plantao"> | Date | string
  dataFinal?: Prisma.DateTimeNullableFilter<"Plantao"> | Date | string | null
  prazoPagamentoDias?: Prisma.IntNullableFilter<"Plantao"> | number | null
  modalidadeTrabalho?: Prisma.StringFilter<"Plantao"> | string
  tipoFechamento?: Prisma.StringFilter<"Plantao"> | string
  tipoValor?: Prisma.StringFilter<"Plantao"> | string
  valorBase?: Prisma.FloatFilter<"Plantao"> | number
  horaInicio?: Prisma.StringFilter<"Plantao"> | string
  horaFim?: Prisma.StringFilter<"Plantao"> | string
  intervalo?: Prisma.StringFilter<"Plantao"> | string
  tipoTurno?: Prisma.StringFilter<"Plantao"> | string
  observacoes?: Prisma.StringNullableFilter<"Plantao"> | string | null
  fusoHorario?: Prisma.StringFilter<"Plantao"> | string
  createdAt?: Prisma.DateTimeFilter<"Plantao"> | Date | string
  updatedAt?: Prisma.DateTimeFilter<"Plantao"> | Date | string
  concluidoEm?: Prisma.DateTimeNullableFilter<"Plantao"> | Date | string | null
  deletedAt?: Prisma.DateTimeNullableFilter<"Plantao"> | Date | string | null
}

export type PlantaoCreateWithoutProfissionalInput = {
  uuid?: string
  dataInicial: Date | string
  dataFinal?: Date | string | null
  prazoPagamentoDias?: number | null
  modalidadeTrabalho: string
  tipoFechamento: string
  tipoValor?: string
  valorBase: number
  horaInicio: string
  horaFim: string
  intervalo: string
  tipoTurno: string
  observacoes?: string | null
  fusoHorario?: string
  createdAt?: Date | string
  updatedAt?: Date | string
  concluidoEm?: Date | string | null
  deletedAt?: Date | string | null
  cliente: Prisma.ClienteCreateNestedOneWithoutPlantoesInput
  localAtendimento: Prisma.LocalAtendimentoCreateNestedOneWithoutPlantoesInput
  diasPlantao?: Prisma.DiaPlantaoCreateNestedManyWithoutPlantaoInput
  fechamentos?: Prisma.FechamentoCreateNestedManyWithoutPlantaoInput
  antecipacoes?: Prisma.AntecipacaoCreateNestedManyWithoutPlantaoInput
}

export type PlantaoUncheckedCreateWithoutProfissionalInput = {
  id?: number
  uuid?: string
  clienteId: number
  localAtendimentoId: number
  dataInicial: Date | string
  dataFinal?: Date | string | null
  prazoPagamentoDias?: number | null
  modalidadeTrabalho: string
  tipoFechamento: string
  tipoValor?: string
  valorBase: number
  horaInicio: string
  horaFim: string
  intervalo: string
  tipoTurno: string
  observacoes?: string | null
  fusoHorario?: string
  createdAt?: Date | string
  updatedAt?: Date | string
  concluidoEm?: Date | string | null
  deletedAt?: Date | string | null
  diasPlantao?: Prisma.DiaPlantaoUncheckedCreateNestedManyWithoutPlantaoInput
  fechamentos?: Prisma.FechamentoUncheckedCreateNestedManyWithoutPlantaoInput
  antecipacoes?: Prisma.AntecipacaoUncheckedCreateNestedManyWithoutPlantaoInput
}

export type PlantaoCreateOrConnectWithoutProfissionalInput = {
  where: Prisma.PlantaoWhereUniqueInput
  create: Prisma.XOR<Prisma.PlantaoCreateWithoutProfissionalInput, Prisma.PlantaoUncheckedCreateWithoutProfissionalInput>
}

export type PlantaoCreateManyProfissionalInputEnvelope = {
  data: Prisma.PlantaoCreateManyProfissionalInput | Prisma.PlantaoCreateManyProfissionalInput[]
  skipDuplicates?: boolean
}

export type PlantaoUpsertWithWhereUniqueWithoutProfissionalInput = {
  where: Prisma.PlantaoWhereUniqueInput
  update: Prisma.XOR<Prisma.PlantaoUpdateWithoutProfissionalInput, Prisma.PlantaoUncheckedUpdateWithoutProfissionalInput>
  create: Prisma.XOR<Prisma.PlantaoCreateWithoutProfissionalInput, Prisma.PlantaoUncheckedCreateWithoutProfissionalInput>
}

export type PlantaoUpdateWithWhereUniqueWithoutProfissionalInput = {
  where: Prisma.PlantaoWhereUniqueInput
  data: Prisma.XOR<Prisma.PlantaoUpdateWithoutProfissionalInput, Prisma.PlantaoUncheckedUpdateWithoutProfissionalInput>
}

export type PlantaoUpdateManyWithWhereWithoutProfissionalInput = {
  where: Prisma.PlantaoScalarWhereInput
  data: Prisma.XOR<Prisma.PlantaoUpdateManyMutationInput, Prisma.PlantaoUncheckedUpdateManyWithoutProfissionalInput>
}

export type PlantaoCreateWithoutLocalAtendimentoInput = {
  uuid?: string
  dataInicial: Date | string
  dataFinal?: Date | string | null
  prazoPagamentoDias?: number | null
  modalidadeTrabalho: string
  tipoFechamento: string
  tipoValor?: string
  valorBase: number
  horaInicio: string
  horaFim: string
  intervalo: string
  tipoTurno: string
  observacoes?: string | null
  fusoHorario?: string
  createdAt?: Date | string
  updatedAt?: Date | string
  concluidoEm?: Date | string | null
  deletedAt?: Date | string | null
  cliente: Prisma.ClienteCreateNestedOneWithoutPlantoesInput
  profissional: Prisma.ProfissionalCreateNestedOneWithoutPlantoesInput
  diasPlantao?: Prisma.DiaPlantaoCreateNestedManyWithoutPlantaoInput
  fechamentos?: Prisma.FechamentoCreateNestedManyWithoutPlantaoInput
  antecipacoes?: Prisma.AntecipacaoCreateNestedManyWithoutPlantaoInput
}

export type PlantaoUncheckedCreateWithoutLocalAtendimentoInput = {
  id?: number
  uuid?: string
  clienteId: number
  profissionalId: number
  dataInicial: Date | string
  dataFinal?: Date | string | null
  prazoPagamentoDias?: number | null
  modalidadeTrabalho: string
  tipoFechamento: string
  tipoValor?: string
  valorBase: number
  horaInicio: string
  horaFim: string
  intervalo: string
  tipoTurno: string
  observacoes?: string | null
  fusoHorario?: string
  createdAt?: Date | string
  updatedAt?: Date | string
  concluidoEm?: Date | string | null
  deletedAt?: Date | string | null
  diasPlantao?: Prisma.DiaPlantaoUncheckedCreateNestedManyWithoutPlantaoInput
  fechamentos?: Prisma.FechamentoUncheckedCreateNestedManyWithoutPlantaoInput
  antecipacoes?: Prisma.AntecipacaoUncheckedCreateNestedManyWithoutPlantaoInput
}

export type PlantaoCreateOrConnectWithoutLocalAtendimentoInput = {
  where: Prisma.PlantaoWhereUniqueInput
  create: Prisma.XOR<Prisma.PlantaoCreateWithoutLocalAtendimentoInput, Prisma.PlantaoUncheckedCreateWithoutLocalAtendimentoInput>
}

export type PlantaoCreateManyLocalAtendimentoInputEnvelope = {
  data: Prisma.PlantaoCreateManyLocalAtendimentoInput | Prisma.PlantaoCreateManyLocalAtendimentoInput[]
  skipDuplicates?: boolean
}

export type PlantaoUpsertWithWhereUniqueWithoutLocalAtendimentoInput = {
  where: Prisma.PlantaoWhereUniqueInput
  update: Prisma.XOR<Prisma.PlantaoUpdateWithoutLocalAtendimentoInput, Prisma.PlantaoUncheckedUpdateWithoutLocalAtendimentoInput>
  create: Prisma.XOR<Prisma.PlantaoCreateWithoutLocalAtendimentoInput, Prisma.PlantaoUncheckedCreateWithoutLocalAtendimentoInput>
}

export type PlantaoUpdateWithWhereUniqueWithoutLocalAtendimentoInput = {
  where: Prisma.PlantaoWhereUniqueInput
  data: Prisma.XOR<Prisma.PlantaoUpdateWithoutLocalAtendimentoInput, Prisma.PlantaoUncheckedUpdateWithoutLocalAtendimentoInput>
}

export type PlantaoUpdateManyWithWhereWithoutLocalAtendimentoInput = {
  where: Prisma.PlantaoScalarWhereInput
  data: Prisma.XOR<Prisma.PlantaoUpdateManyMutationInput, Prisma.PlantaoUncheckedUpdateManyWithoutLocalAtendimentoInput>
}

export type PlantaoCreateWithoutDiasPlantaoInput = {
  uuid?: string
  dataInicial: Date | string
  dataFinal?: Date | string | null
  prazoPagamentoDias?: number | null
  modalidadeTrabalho: string
  tipoFechamento: string
  tipoValor?: string
  valorBase: number
  horaInicio: string
  horaFim: string
  intervalo: string
  tipoTurno: string
  observacoes?: string | null
  fusoHorario?: string
  createdAt?: Date | string
  updatedAt?: Date | string
  concluidoEm?: Date | string | null
  deletedAt?: Date | string | null
  cliente: Prisma.ClienteCreateNestedOneWithoutPlantoesInput
  localAtendimento: Prisma.LocalAtendimentoCreateNestedOneWithoutPlantoesInput
  profissional: Prisma.ProfissionalCreateNestedOneWithoutPlantoesInput
  fechamentos?: Prisma.FechamentoCreateNestedManyWithoutPlantaoInput
  antecipacoes?: Prisma.AntecipacaoCreateNestedManyWithoutPlantaoInput
}

export type PlantaoUncheckedCreateWithoutDiasPlantaoInput = {
  id?: number
  uuid?: string
  clienteId: number
  localAtendimentoId: number
  profissionalId: number
  dataInicial: Date | string
  dataFinal?: Date | string | null
  prazoPagamentoDias?: number | null
  modalidadeTrabalho: string
  tipoFechamento: string
  tipoValor?: string
  valorBase: number
  horaInicio: string
  horaFim: string
  intervalo: string
  tipoTurno: string
  observacoes?: string | null
  fusoHorario?: string
  createdAt?: Date | string
  updatedAt?: Date | string
  concluidoEm?: Date | string | null
  deletedAt?: Date | string | null
  fechamentos?: Prisma.FechamentoUncheckedCreateNestedManyWithoutPlantaoInput
  antecipacoes?: Prisma.AntecipacaoUncheckedCreateNestedManyWithoutPlantaoInput
}

export type PlantaoCreateOrConnectWithoutDiasPlantaoInput = {
  where: Prisma.PlantaoWhereUniqueInput
  create: Prisma.XOR<Prisma.PlantaoCreateWithoutDiasPlantaoInput, Prisma.PlantaoUncheckedCreateWithoutDiasPlantaoInput>
}

export type PlantaoUpsertWithoutDiasPlantaoInput = {
  update: Prisma.XOR<Prisma.PlantaoUpdateWithoutDiasPlantaoInput, Prisma.PlantaoUncheckedUpdateWithoutDiasPlantaoInput>
  create: Prisma.XOR<Prisma.PlantaoCreateWithoutDiasPlantaoInput, Prisma.PlantaoUncheckedCreateWithoutDiasPlantaoInput>
  where?: Prisma.PlantaoWhereInput
}

export type PlantaoUpdateToOneWithWhereWithoutDiasPlantaoInput = {
  where?: Prisma.PlantaoWhereInput
  data: Prisma.XOR<Prisma.PlantaoUpdateWithoutDiasPlantaoInput, Prisma.PlantaoUncheckedUpdateWithoutDiasPlantaoInput>
}

export type PlantaoUpdateWithoutDiasPlantaoInput = {
  uuid?: Prisma.StringFieldUpdateOperationsInput | string
  dataInicial?: Prisma.DateTimeFieldUpdateOperationsInput | Date | string
  dataFinal?: Prisma.NullableDateTimeFieldUpdateOperationsInput | Date | string | null
  prazoPagamentoDias?: Prisma.NullableIntFieldUpdateOperationsInput | number | null
  modalidadeTrabalho?: Prisma.StringFieldUpdateOperationsInput | string
  tipoFechamento?: Prisma.StringFieldUpdateOperationsInput | string
  tipoValor?: Prisma.StringFieldUpdateOperationsInput | string
  valorBase?: Prisma.FloatFieldUpdateOperationsInput | number
  horaInicio?: Prisma.StringFieldUpdateOperationsInput | string
  horaFim?: Prisma.StringFieldUpdateOperationsInput | string
  intervalo?: Prisma.StringFieldUpdateOperationsInput | string
  tipoTurno?: Prisma.StringFieldUpdateOperationsInput | string
  observacoes?: Prisma.NullableStringFieldUpdateOperationsInput | string | null
  fusoHorario?: Prisma.StringFieldUpdateOperationsInput | string
  createdAt?: Prisma.DateTimeFieldUpdateOperationsInput | Date | string
  updatedAt?: Prisma.DateTimeFieldUpdateOperationsInput | Date | string
  concluidoEm?: Prisma.NullableDateTimeFieldUpdateOperationsInput | Date | string | null
  deletedAt?: Prisma.NullableDateTimeFieldUpdateOperationsInput | Date | string | null
  cliente?: Prisma.ClienteUpdateOneRequiredWithoutPlantoesNestedInput
  localAtendimento?: Prisma.LocalAtendimentoUpdateOneRequiredWithoutPlantoesNestedInput
  profissional?: Prisma.ProfissionalUpdateOneRequiredWithoutPlantoesNestedInput
  fechamentos?: Prisma.FechamentoUpdateManyWithoutPlantaoNestedInput
  antecipacoes?: Prisma.AntecipacaoUpdateManyWithoutPlantaoNestedInput
}

export type PlantaoUncheckedUpdateWithoutDiasPlantaoInput = {
  id?: Prisma.IntFieldUpdateOperationsInput | number
  uuid?: Prisma.StringFieldUpdateOperationsInput | string
  clienteId?: Prisma.IntFieldUpdateOperationsInput | number
  localAtendimentoId?: Prisma.IntFieldUpdateOperationsInput | number
  profissionalId?: Prisma.IntFieldUpdateOperationsInput | number
  dataInicial?: Prisma.DateTimeFieldUpdateOperationsInput | Date | string
  dataFinal?: Prisma.NullableDateTimeFieldUpdateOperationsInput | Date | string | null
  prazoPagamentoDias?: Prisma.NullableIntFieldUpdateOperationsInput | number | null
  modalidadeTrabalho?: Prisma.StringFieldUpdateOperationsInput | string
  tipoFechamento?: Prisma.StringFieldUpdateOperationsInput | string
  tipoValor?: Prisma.StringFieldUpdateOperationsInput | string
  valorBase?: Prisma.FloatFieldUpdateOperationsInput | number
  horaInicio?: Prisma.StringFieldUpdateOperationsInput | string
  horaFim?: Prisma.StringFieldUpdateOperationsInput | string
  intervalo?: Prisma.StringFieldUpdateOperationsInput | string
  tipoTurno?: Prisma.StringFieldUpdateOperationsInput | string
  observacoes?: Prisma.NullableStringFieldUpdateOperationsInput | string | null
  fusoHorario?: Prisma.StringFieldUpdateOperationsInput | string
  createdAt?: Prisma.DateTimeFieldUpdateOperationsInput | Date | string
  updatedAt?: Prisma.DateTimeFieldUpdateOperationsInput | Date | string
  concluidoEm?: Prisma.NullableDateTimeFieldUpdateOperationsInput | Date | string | null
  deletedAt?: Prisma.NullableDateTimeFieldUpdateOperationsInput | Date | string | null
  fechamentos?: Prisma.FechamentoUncheckedUpdateManyWithoutPlantaoNestedInput
  antecipacoes?: Prisma.AntecipacaoUncheckedUpdateManyWithoutPlantaoNestedInput
}

export type PlantaoCreateWithoutFechamentosInput = {
  uuid?: string
  dataInicial: Date | string
  dataFinal?: Date | string | null
  prazoPagamentoDias?: number | null
  modalidadeTrabalho: string
  tipoFechamento: string
  tipoValor?: string
  valorBase: number
  horaInicio: string
  horaFim: string
  intervalo: string
  tipoTurno: string
  observacoes?: string | null
  fusoHorario?: string
  createdAt?: Date | string
  updatedAt?: Date | string
  concluidoEm?: Date | string | null
  deletedAt?: Date | string | null
  cliente: Prisma.ClienteCreateNestedOneWithoutPlantoesInput
  localAtendimento: Prisma.LocalAtendimentoCreateNestedOneWithoutPlantoesInput
  profissional: Prisma.ProfissionalCreateNestedOneWithoutPlantoesInput
  diasPlantao?: Prisma.DiaPlantaoCreateNestedManyWithoutPlantaoInput
  antecipacoes?: Prisma.AntecipacaoCreateNestedManyWithoutPlantaoInput
}

export type PlantaoUncheckedCreateWithoutFechamentosInput = {
  id?: number
  uuid?: string
  clienteId: number
  localAtendimentoId: number
  profissionalId: number
  dataInicial: Date | string
  dataFinal?: Date | string | null
  prazoPagamentoDias?: number | null
  modalidadeTrabalho: string
  tipoFechamento: string
  tipoValor?: string
  valorBase: number
  horaInicio: string
  horaFim: string
  intervalo: string
  tipoTurno: string
  observacoes?: string | null
  fusoHorario?: string
  createdAt?: Date | string
  updatedAt?: Date | string
  concluidoEm?: Date | string | null
  deletedAt?: Date | string | null
  diasPlantao?: Prisma.DiaPlantaoUncheckedCreateNestedManyWithoutPlantaoInput
  antecipacoes?: Prisma.AntecipacaoUncheckedCreateNestedManyWithoutPlantaoInput
}

export type PlantaoCreateOrConnectWithoutFechamentosInput = {
  where: Prisma.PlantaoWhereUniqueInput
  create: Prisma.XOR<Prisma.PlantaoCreateWithoutFechamentosInput, Prisma.PlantaoUncheckedCreateWithoutFechamentosInput>
}

export type PlantaoUpsertWithoutFechamentosInput = {
  update: Prisma.XOR<Prisma.PlantaoUpdateWithoutFechamentosInput, Prisma.PlantaoUncheckedUpdateWithoutFechamentosInput>
  create: Prisma.XOR<Prisma.PlantaoCreateWithoutFechamentosInput, Prisma.PlantaoUncheckedCreateWithoutFechamentosInput>
  where?: Prisma.PlantaoWhereInput
}

export type PlantaoUpdateToOneWithWhereWithoutFechamentosInput = {
  where?: Prisma.PlantaoWhereInput
  data: Prisma.XOR<Prisma.PlantaoUpdateWithoutFechamentosInput, Prisma.PlantaoUncheckedUpdateWithoutFechamentosInput>
}

export type PlantaoUpdateWithoutFechamentosInput = {
  uuid?: Prisma.StringFieldUpdateOperationsInput | string
  dataInicial?: Prisma.DateTimeFieldUpdateOperationsInput | Date | string
  dataFinal?: Prisma.NullableDateTimeFieldUpdateOperationsInput | Date | string | null
  prazoPagamentoDias?: Prisma.NullableIntFieldUpdateOperationsInput | number | null
  modalidadeTrabalho?: Prisma.StringFieldUpdateOperationsInput | string
  tipoFechamento?: Prisma.StringFieldUpdateOperationsInput | string
  tipoValor?: Prisma.StringFieldUpdateOperationsInput | string
  valorBase?: Prisma.FloatFieldUpdateOperationsInput | number
  horaInicio?: Prisma.StringFieldUpdateOperationsInput | string
  horaFim?: Prisma.StringFieldUpdateOperationsInput | string
  intervalo?: Prisma.StringFieldUpdateOperationsInput | string
  tipoTurno?: Prisma.StringFieldUpdateOperationsInput | string
  observacoes?: Prisma.NullableStringFieldUpdateOperationsInput | string | null
  fusoHorario?: Prisma.StringFieldUpdateOperationsInput | string
  createdAt?: Prisma.DateTimeFieldUpdateOperationsInput | Date | string
  updatedAt?: Prisma.DateTimeFieldUpdateOperationsInput | Date | string
  concluidoEm?: Prisma.NullableDateTimeFieldUpdateOperationsInput | Date | string | null
  deletedAt?: Prisma.NullableDateTimeFieldUpdateOperationsInput | Date | string | null
  cliente?: Prisma.ClienteUpdateOneRequiredWithoutPlantoesNestedInput
  localAtendimento?: Prisma.LocalAtendimentoUpdateOneRequiredWithoutPlantoesNestedInput
  profissional?: Prisma.ProfissionalUpdateOneRequiredWithoutPlantoesNestedInput
  diasPlantao?: Prisma.DiaPlantaoUpdateManyWithoutPlantaoNestedInput
  antecipacoes?: Prisma.AntecipacaoUpdateManyWithoutPlantaoNestedInput
}

export type PlantaoUncheckedUpdateWithoutFechamentosInput = {
  id?: Prisma.IntFieldUpdateOperationsInput | number
  uuid?: Prisma.StringFieldUpdateOperationsInput | string
  clienteId?: Prisma.IntFieldUpdateOperationsInput | number
  localAtendimentoId?: Prisma.IntFieldUpdateOperationsInput | number
  profissionalId?: Prisma.IntFieldUpdateOperationsInput | number
  dataInicial?: Prisma.DateTimeFieldUpdateOperationsInput | Date | string
  dataFinal?: Prisma.NullableDateTimeFieldUpdateOperationsInput | Date | string | null
  prazoPagamentoDias?: Prisma.NullableIntFieldUpdateOperationsInput | number | null
  modalidadeTrabalho?: Prisma.StringFieldUpdateOperationsInput | string
  tipoFechamento?: Prisma.StringFieldUpdateOperationsInput | string
  tipoValor?: Prisma.StringFieldUpdateOperationsInput | string
  valorBase?: Prisma.FloatFieldUpdateOperationsInput | number
  horaInicio?: Prisma.StringFieldUpdateOperationsInput | string
  horaFim?: Prisma.StringFieldUpdateOperationsInput | string
  intervalo?: Prisma.StringFieldUpdateOperationsInput | string
  tipoTurno?: Prisma.StringFieldUpdateOperationsInput | string
  observacoes?: Prisma.NullableStringFieldUpdateOperationsInput | string | null
  fusoHorario?: Prisma.StringFieldUpdateOperationsInput | string
  createdAt?: Prisma.DateTimeFieldUpdateOperationsInput | Date | string
  updatedAt?: Prisma.DateTimeFieldUpdateOperationsInput | Date | string
  concluidoEm?: Prisma.NullableDateTimeFieldUpdateOperationsInput | Date | string | null
  deletedAt?: Prisma.NullableDateTimeFieldUpdateOperationsInput | Date | string | null
  diasPlantao?: Prisma.DiaPlantaoUncheckedUpdateManyWithoutPlantaoNestedInput
  antecipacoes?: Prisma.AntecipacaoUncheckedUpdateManyWithoutPlantaoNestedInput
}

export type PlantaoCreateWithoutAntecipacoesInput = {
  uuid?: string
  dataInicial: Date | string
  dataFinal?: Date | string | null
  prazoPagamentoDias?: number | null
  modalidadeTrabalho: string
  tipoFechamento: string
  tipoValor?: string
  valorBase: number
  horaInicio: string
  horaFim: string
  intervalo: string
  tipoTurno: string
  observacoes?: string | null
  fusoHorario?: string
  createdAt?: Date | string
  updatedAt?: Date | string
  concluidoEm?: Date | string | null
  deletedAt?: Date | string | null
  cliente: Prisma.ClienteCreateNestedOneWithoutPlantoesInput
  localAtendimento: Prisma.LocalAtendimentoCreateNestedOneWithoutPlantoesInput
  profissional: Prisma.ProfissionalCreateNestedOneWithoutPlantoesInput
  diasPlantao?: Prisma.DiaPlantaoCreateNestedManyWithoutPlantaoInput
  fechamentos?: Prisma.FechamentoCreateNestedManyWithoutPlantaoInput
}

export type PlantaoUncheckedCreateWithoutAntecipacoesInput = {
  id?: number
  uuid?: string
  clienteId: number
  localAtendimentoId: number
  profissionalId: number
  dataInicial: Date | string
  dataFinal?: Date | string | null
  prazoPagamentoDias?: number | null
  modalidadeTrabalho: string
  tipoFechamento: string
  tipoValor?: string
  valorBase: number
  horaInicio: string
  horaFim: string
  intervalo: string
  tipoTurno: string
  observacoes?: string | null
  fusoHorario?: string
  createdAt?: Date | string
  updatedAt?: Date | string
  concluidoEm?: Date | string | null
  deletedAt?: Date | string | null
  diasPlantao?: Prisma.DiaPlantaoUncheckedCreateNestedManyWithoutPlantaoInput
  fechamentos?: Prisma.FechamentoUncheckedCreateNestedManyWithoutPlantaoInput
}

export type PlantaoCreateOrConnectWithoutAntecipacoesInput = {
  where: Prisma.PlantaoWhereUniqueInput
  create: Prisma.XOR<Prisma.PlantaoCreateWithoutAntecipacoesInput, Prisma.PlantaoUncheckedCreateWithoutAntecipacoesInput>
}

export type PlantaoUpsertWithoutAntecipacoesInput = {
  update: Prisma.XOR<Prisma.PlantaoUpdateWithoutAntecipacoesInput, Prisma.PlantaoUncheckedUpdateWithoutAntecipacoesInput>
  create: Prisma.XOR<Prisma.PlantaoCreateWithoutAntecipacoesInput, Prisma.PlantaoUncheckedCreateWithoutAntecipacoesInput>
  where?: Prisma.PlantaoWhereInput
}

export type PlantaoUpdateToOneWithWhereWithoutAntecipacoesInput = {
  where?: Prisma.PlantaoWhereInput
  data: Prisma.XOR<Prisma.PlantaoUpdateWithoutAntecipacoesInput, Prisma.PlantaoUncheckedUpdateWithoutAntecipacoesInput>
}

export type PlantaoUpdateWithoutAntecipacoesInput = {
  uuid?: Prisma.StringFieldUpdateOperationsInput | string
  dataInicial?: Prisma.DateTimeFieldUpdateOperationsInput | Date | string
  dataFinal?: Prisma.NullableDateTimeFieldUpdateOperationsInput | Date | string | null
  prazoPagamentoDias?: Prisma.NullableIntFieldUpdateOperationsInput | number | null
  modalidadeTrabalho?: Prisma.StringFieldUpdateOperationsInput | string
  tipoFechamento?: Prisma.StringFieldUpdateOperationsInput | string
  tipoValor?: Prisma.StringFieldUpdateOperationsInput | string
  valorBase?: Prisma.FloatFieldUpdateOperationsInput | number
  horaInicio?: Prisma.StringFieldUpdateOperationsInput | string
  horaFim?: Prisma.StringFieldUpdateOperationsInput | string
  intervalo?: Prisma.StringFieldUpdateOperationsInput | string
  tipoTurno?: Prisma.StringFieldUpdateOperationsInput | string
  observacoes?: Prisma.NullableStringFieldUpdateOperationsInput | string | null
  fusoHorario?: Prisma.StringFieldUpdateOperationsInput | string
  createdAt?: Prisma.DateTimeFieldUpdateOperationsInput | Date | string
  updatedAt?: Prisma.DateTimeFieldUpdateOperationsInput | Date | string
  concluidoEm?: Prisma.NullableDateTimeFieldUpdateOperationsInput | Date | string | null
  deletedAt?: Prisma.NullableDateTimeFieldUpdateOperationsInput | Date | string | null
  cliente?: Prisma.ClienteUpdateOneRequiredWithoutPlantoesNestedInput
  localAtendimento?: Prisma.LocalAtendimentoUpdateOneRequiredWithoutPlantoesNestedInput
  profissional?: Prisma.ProfissionalUpdateOneRequiredWithoutPlantoesNestedInput
  diasPlantao?: Prisma.DiaPlantaoUpdateManyWithoutPlantaoNestedInput
  fechamentos?: Prisma.FechamentoUpdateManyWithoutPlantaoNestedInput
}

export type PlantaoUncheckedUpdateWithoutAntecipacoesInput = {
  id?: Prisma.IntFieldUpdateOperationsInput | number
  uuid?: Prisma.StringFieldUpdateOperationsInput | string
  clienteId?: Prisma.IntFieldUpdateOperationsInput | number
  localAtendimentoId?: Prisma.IntFieldUpdateOperationsInput | number
  profissionalId?: Prisma.IntFieldUpdateOperationsInput | number
  dataInicial?: Prisma.DateTimeFieldUpdateOperationsInput | Date | string
  dataFinal?: Prisma.NullableDateTimeFieldUpdateOperationsInput | Date | string | null
  prazoPagamentoDias?: Prisma.NullableIntFieldUpdateOperationsInput | number | null
  modalidadeTrabalho?: Prisma.StringFieldUpdateOperationsInput | string
  tipoFechamento?: Prisma.StringFieldUpdateOperationsInput | string
  tipoValor?: Prisma.StringFieldUpdateOperationsInput | string
  valorBase?: Prisma.FloatFieldUpdateOperationsInput | number
  horaInicio?: Prisma.StringFieldUpdateOperationsInput | string
  horaFim?: Prisma.StringFieldUpdateOperationsInput | string
  intervalo?: Prisma.StringFieldUpdateOperationsInput | string
  tipoTurno?: Prisma.StringFieldUpdateOperationsInput | string
  observacoes?: Prisma.NullableStringFieldUpdateOperationsInput | string | null
  fusoHorario?: Prisma.StringFieldUpdateOperationsInput | string
  createdAt?: Prisma.DateTimeFieldUpdateOperationsInput | Date | string
  updatedAt?: Prisma.DateTimeFieldUpdateOperationsInput | Date | string
  concluidoEm?: Prisma.NullableDateTimeFieldUpdateOperationsInput | Date | string | null
  deletedAt?: Prisma.NullableDateTimeFieldUpdateOperationsInput | Date | string | null
  diasPlantao?: Prisma.DiaPlantaoUncheckedUpdateManyWithoutPlantaoNestedInput
  fechamentos?: Prisma.FechamentoUncheckedUpdateManyWithoutPlantaoNestedInput
}

export type PlantaoCreateManyClienteInput = {
  id?: number
  uuid?: string
  localAtendimentoId: number
  profissionalId: number
  dataInicial: Date | string
  dataFinal?: Date | string | null
  prazoPagamentoDias?: number | null
  modalidadeTrabalho: string
  tipoFechamento: string
  tipoValor?: string
  valorBase: number
  horaInicio: string
  horaFim: string
  intervalo: string
  tipoTurno: string
  observacoes?: string | null
  fusoHorario?: string
  createdAt?: Date | string
  updatedAt?: Date | string
  concluidoEm?: Date | string | null
  deletedAt?: Date | string | null
}

export type PlantaoUpdateWithoutClienteInput = {
  uuid?: Prisma.StringFieldUpdateOperationsInput | string
  dataInicial?: Prisma.DateTimeFieldUpdateOperationsInput | Date | string
  dataFinal?: Prisma.NullableDateTimeFieldUpdateOperationsInput | Date | string | null
  prazoPagamentoDias?: Prisma.NullableIntFieldUpdateOperationsInput | number | null
  modalidadeTrabalho?: Prisma.StringFieldUpdateOperationsInput | string
  tipoFechamento?: Prisma.StringFieldUpdateOperationsInput | string
  tipoValor?: Prisma.StringFieldUpdateOperationsInput | string
  valorBase?: Prisma.FloatFieldUpdateOperationsInput | number
  horaInicio?: Prisma.StringFieldUpdateOperationsInput | string
  horaFim?: Prisma.StringFieldUpdateOperationsInput | string
  intervalo?: Prisma.StringFieldUpdateOperationsInput | string
  tipoTurno?: Prisma.StringFieldUpdateOperationsInput | string
  observacoes?: Prisma.NullableStringFieldUpdateOperationsInput | string | null
  fusoHorario?: Prisma.StringFieldUpdateOperationsInput | string
  createdAt?: Prisma.DateTimeFieldUpdateOperationsInput | Date | string
  updatedAt?: Prisma.DateTimeFieldUpdateOperationsInput | Date | string
  concluidoEm?: Prisma.NullableDateTimeFieldUpdateOperationsInput | Date | string | null
  deletedAt?: Prisma.NullableDateTimeFieldUpdateOperationsInput | Date | string | null
  localAtendimento?: Prisma.LocalAtendimentoUpdateOneRequiredWithoutPlantoesNestedInput
  profissional?: Prisma.ProfissionalUpdateOneRequiredWithoutPlantoesNestedInput
  diasPlantao?: Prisma.DiaPlantaoUpdateManyWithoutPlantaoNestedInput
  fechamentos?: Prisma.FechamentoUpdateManyWithoutPlantaoNestedInput
  antecipacoes?: Prisma.AntecipacaoUpdateManyWithoutPlantaoNestedInput
}

export type PlantaoUncheckedUpdateWithoutClienteInput = {
  id?: Prisma.IntFieldUpdateOperationsInput | number
  uuid?: Prisma.StringFieldUpdateOperationsInput | string
  localAtendimentoId?: Prisma.IntFieldUpdateOperationsInput | number
  profissionalId?: Prisma.IntFieldUpdateOperationsInput | number
  dataInicial?: Prisma.DateTimeFieldUpdateOperationsInput | Date | string
  dataFinal?: Prisma.NullableDateTimeFieldUpdateOperationsInput | Date | string | null
  prazoPagamentoDias?: Prisma.NullableIntFieldUpdateOperationsInput | number | null
  modalidadeTrabalho?: Prisma.StringFieldUpdateOperationsInput | string
  tipoFechamento?: Prisma.StringFieldUpdateOperationsInput | string
  tipoValor?: Prisma.StringFieldUpdateOperationsInput | string
  valorBase?: Prisma.FloatFieldUpdateOperationsInput | number
  horaInicio?: Prisma.StringFieldUpdateOperationsInput | string
  horaFim?: Prisma.StringFieldUpdateOperationsInput | string
  intervalo?: Prisma.StringFieldUpdateOperationsInput | string
  tipoTurno?: Prisma.StringFieldUpdateOperationsInput | string
  observacoes?: Prisma.NullableStringFieldUpdateOperationsInput | string | null
  fusoHorario?: Prisma.StringFieldUpdateOperationsInput | string
  createdAt?: Prisma.DateTimeFieldUpdateOperationsInput | Date | string
  updatedAt?: Prisma.DateTimeFieldUpdateOperationsInput | Date | string
  concluidoEm?: Prisma.NullableDateTimeFieldUpdateOperationsInput | Date | string | null
  deletedAt?: Prisma.NullableDateTimeFieldUpdateOperationsInput | Date | string | null
  diasPlantao?: Prisma.DiaPlantaoUncheckedUpdateManyWithoutPlantaoNestedInput
  fechamentos?: Prisma.FechamentoUncheckedUpdateManyWithoutPlantaoNestedInput
  antecipacoes?: Prisma.AntecipacaoUncheckedUpdateManyWithoutPlantaoNestedInput
}

export type PlantaoUncheckedUpdateManyWithoutClienteInput = {
  id?: Prisma.IntFieldUpdateOperationsInput | number
  uuid?: Prisma.StringFieldUpdateOperationsInput | string
  localAtendimentoId?: Prisma.IntFieldUpdateOperationsInput | number
  profissionalId?: Prisma.IntFieldUpdateOperationsInput | number
  dataInicial?: Prisma.DateTimeFieldUpdateOperationsInput | Date | string
  dataFinal?: Prisma.NullableDateTimeFieldUpdateOperationsInput | Date | string | null
  prazoPagamentoDias?: Prisma.NullableIntFieldUpdateOperationsInput | number | null
  modalidadeTrabalho?: Prisma.StringFieldUpdateOperationsInput | string
  tipoFechamento?: Prisma.StringFieldUpdateOperationsInput | string
  tipoValor?: Prisma.StringFieldUpdateOperationsInput | string
  valorBase?: Prisma.FloatFieldUpdateOperationsInput | number
  horaInicio?: Prisma.StringFieldUpdateOperationsInput | string
  horaFim?: Prisma.StringFieldUpdateOperationsInput | string
  intervalo?: Prisma.StringFieldUpdateOperationsInput | string
  tipoTurno?: Prisma.StringFieldUpdateOperationsInput | string
  observacoes?: Prisma.NullableStringFieldUpdateOperationsInput | string | null
  fusoHorario?: Prisma.StringFieldUpdateOperationsInput | string
  createdAt?: Prisma.DateTimeFieldUpdateOperationsInput | Date | string
  updatedAt?: Prisma.DateTimeFieldUpdateOperationsInput | Date | string
  concluidoEm?: Prisma.NullableDateTimeFieldUpdateOperationsInput | Date | string | null
  deletedAt?: Prisma.NullableDateTimeFieldUpdateOperationsInput | Date | string | null
}

export type PlantaoCreateManyProfissionalInput = {
  id?: number
  uuid?: string
  clienteId: number
  localAtendimentoId: number
  dataInicial: Date | string
  dataFinal?: Date | string | null
  prazoPagamentoDias?: number | null
  modalidadeTrabalho: string
  tipoFechamento: string
  tipoValor?: string
  valorBase: number
  horaInicio: string
  horaFim: string
  intervalo: string
  tipoTurno: string
  observacoes?: string | null
  fusoHorario?: string
  createdAt?: Date | string
  updatedAt?: Date | string
  concluidoEm?: Date | string | null
  deletedAt?: Date | string | null
}

export type PlantaoUpdateWithoutProfissionalInput = {
  uuid?: Prisma.StringFieldUpdateOperationsInput | string
  dataInicial?: Prisma.DateTimeFieldUpdateOperationsInput | Date | string
  dataFinal?: Prisma.NullableDateTimeFieldUpdateOperationsInput | Date | string | null
  prazoPagamentoDias?: Prisma.NullableIntFieldUpdateOperationsInput | number | null
  modalidadeTrabalho?: Prisma.StringFieldUpdateOperationsInput | string
  tipoFechamento?: Prisma.StringFieldUpdateOperationsInput | string
  tipoValor?: Prisma.StringFieldUpdateOperationsInput | string
  valorBase?: Prisma.FloatFieldUpdateOperationsInput | number
  horaInicio?: Prisma.StringFieldUpdateOperationsInput | string
  horaFim?: Prisma.StringFieldUpdateOperationsInput | string
  intervalo?: Prisma.StringFieldUpdateOperationsInput | string
  tipoTurno?: Prisma.StringFieldUpdateOperationsInput | string
  observacoes?: Prisma.NullableStringFieldUpdateOperationsInput | string | null
  fusoHorario?: Prisma.StringFieldUpdateOperationsInput | string
  createdAt?: Prisma.DateTimeFieldUpdateOperationsInput | Date | string
  updatedAt?: Prisma.DateTimeFieldUpdateOperationsInput | Date | string
  concluidoEm?: Prisma.NullableDateTimeFieldUpdateOperationsInput | Date | string | null
  deletedAt?: Prisma.NullableDateTimeFieldUpdateOperationsInput | Date | string | null
  cliente?: Prisma.ClienteUpdateOneRequiredWithoutPlantoesNestedInput
  localAtendimento?: Prisma.LocalAtendimentoUpdateOneRequiredWithoutPlantoesNestedInput
  diasPlantao?: Prisma.DiaPlantaoUpdateManyWithoutPlantaoNestedInput
  fechamentos?: Prisma.FechamentoUpdateManyWithoutPlantaoNestedInput
  antecipacoes?: Prisma.AntecipacaoUpdateManyWithoutPlantaoNestedInput
}

export type PlantaoUncheckedUpdateWithoutProfissionalInput = {
  id?: Prisma.IntFieldUpdateOperationsInput | number
  uuid?: Prisma.StringFieldUpdateOperationsInput | string
  clienteId?: Prisma.IntFieldUpdateOperationsInput | number
  localAtendimentoId?: Prisma.IntFieldUpdateOperationsInput | number
  dataInicial?: Prisma.DateTimeFieldUpdateOperationsInput | Date | string
  dataFinal?: Prisma.NullableDateTimeFieldUpdateOperationsInput | Date | string | null
  prazoPagamentoDias?: Prisma.NullableIntFieldUpdateOperationsInput | number | null
  modalidadeTrabalho?: Prisma.StringFieldUpdateOperationsInput | string
  tipoFechamento?: Prisma.StringFieldUpdateOperationsInput | string
  tipoValor?: Prisma.StringFieldUpdateOperationsInput | string
  valorBase?: Prisma.FloatFieldUpdateOperationsInput | number
  horaInicio?: Prisma.StringFieldUpdateOperationsInput | string
  horaFim?: Prisma.StringFieldUpdateOperationsInput | string
  intervalo?: Prisma.StringFieldUpdateOperationsInput | string
  tipoTurno?: Prisma.StringFieldUpdateOperationsInput | string
  observacoes?: Prisma.NullableStringFieldUpdateOperationsInput | string | null
  fusoHorario?: Prisma.StringFieldUpdateOperationsInput | string
  createdAt?: Prisma.DateTimeFieldUpdateOperationsInput | Date | string
  updatedAt?: Prisma.DateTimeFieldUpdateOperationsInput | Date | string
  concluidoEm?: Prisma.NullableDateTimeFieldUpdateOperationsInput | Date | string | null
  deletedAt?: Prisma.NullableDateTimeFieldUpdateOperationsInput | Date | string | null
  diasPlantao?: Prisma.DiaPlantaoUncheckedUpdateManyWithoutPlantaoNestedInput
  fechamentos?: Prisma.FechamentoUncheckedUpdateManyWithoutPlantaoNestedInput
  antecipacoes?: Prisma.AntecipacaoUncheckedUpdateManyWithoutPlantaoNestedInput
}

export type PlantaoUncheckedUpdateManyWithoutProfissionalInput = {
  id?: Prisma.IntFieldUpdateOperationsInput | number
  uuid?: Prisma.StringFieldUpdateOperationsInput | string
  clienteId?: Prisma.IntFieldUpdateOperationsInput | number
  localAtendimentoId?: Prisma.IntFieldUpdateOperationsInput | number
  dataInicial?: Prisma.DateTimeFieldUpdateOperationsInput | Date | string
  dataFinal?: Prisma.NullableDateTimeFieldUpdateOperationsInput | Date | string | null
  prazoPagamentoDias?: Prisma.NullableIntFieldUpdateOperationsInput | number | null
  modalidadeTrabalho?: Prisma.StringFieldUpdateOperationsInput | string
  tipoFechamento?: Prisma.StringFieldUpdateOperationsInput | string
  tipoValor?: Prisma.StringFieldUpdateOperationsInput | string
  valorBase?: Prisma.FloatFieldUpdateOperationsInput | number
  horaInicio?: Prisma.StringFieldUpdateOperationsInput | string
  horaFim?: Prisma.StringFieldUpdateOperationsInput | string
  intervalo?: Prisma.StringFieldUpdateOperationsInput | string
  tipoTurno?: Prisma.StringFieldUpdateOperationsInput | string
  observacoes?: Prisma.NullableStringFieldUpdateOperationsInput | string | null
  fusoHorario?: Prisma.StringFieldUpdateOperationsInput | string
  createdAt?: Prisma.DateTimeFieldUpdateOperationsInput | Date | string
  updatedAt?: Prisma.DateTimeFieldUpdateOperationsInput | Date | string
  concluidoEm?: Prisma.NullableDateTimeFieldUpdateOperationsInput | Date | string | null
  deletedAt?: Prisma.NullableDateTimeFieldUpdateOperationsInput | Date | string | null
}

export type PlantaoCreateManyLocalAtendimentoInput = {
  id?: number
  uuid?: string
  clienteId: number
  profissionalId: number
  dataInicial: Date | string
  dataFinal?: Date | string | null
  prazoPagamentoDias?: number | null
  modalidadeTrabalho: string
  tipoFechamento: string
  tipoValor?: string
  valorBase: number
  horaInicio: string
  horaFim: string
  intervalo: string
  tipoTurno: string
  observacoes?: string | null
  fusoHorario?: string
  createdAt?: Date | string
  updatedAt?: Date | string
  concluidoEm?: Date | string | null
  deletedAt?: Date | string | null
}

export type PlantaoUpdateWithoutLocalAtendimentoInput = {
  uuid?: Prisma.StringFieldUpdateOperationsInput | string
  dataInicial?: Prisma.DateTimeFieldUpdateOperationsInput | Date | string
  dataFinal?: Prisma.NullableDateTimeFieldUpdateOperationsInput | Date | string | null
  prazoPagamentoDias?: Prisma.NullableIntFieldUpdateOperationsInput | number | null
  modalidadeTrabalho?: Prisma.StringFieldUpdateOperationsInput | string
  tipoFechamento?: Prisma.StringFieldUpdateOperationsInput | string
  tipoValor?: Prisma.StringFieldUpdateOperationsInput | string
  valorBase?: Prisma.FloatFieldUpdateOperationsInput | number
  horaInicio?: Prisma.StringFieldUpdateOperationsInput | string
  horaFim?: Prisma.StringFieldUpdateOperationsInput | string
  intervalo?: Prisma.StringFieldUpdateOperationsInput | string
  tipoTurno?: Prisma.StringFieldUpdateOperationsInput | string
  observacoes?: Prisma.NullableStringFieldUpdateOperationsInput | string | null
  fusoHorario?: Prisma.StringFieldUpdateOperationsInput | string
  createdAt?: Prisma.DateTimeFieldUpdateOperationsInput | Date | string
  updatedAt?: Prisma.DateTimeFieldUpdateOperationsInput | Date | string
  concluidoEm?: Prisma.NullableDateTimeFieldUpdateOperationsInput | Date | string | null
  deletedAt?: Prisma.NullableDateTimeFieldUpdateOperationsInput | Date | string | null
  cliente?: Prisma.ClienteUpdateOneRequiredWithoutPlantoesNestedInput
  profissional?: Prisma.ProfissionalUpdateOneRequiredWithoutPlantoesNestedInput
  diasPlantao?: Prisma.DiaPlantaoUpdateManyWithoutPlantaoNestedInput
  fechamentos?: Prisma.FechamentoUpdateManyWithoutPlantaoNestedInput
  antecipacoes?: Prisma.AntecipacaoUpdateManyWithoutPlantaoNestedInput
}

export type PlantaoUncheckedUpdateWithoutLocalAtendimentoInput = {
  id?: Prisma.IntFieldUpdateOperationsInput | number
  uuid?: Prisma.StringFieldUpdateOperationsInput | string
  clienteId?: Prisma.IntFieldUpdateOperationsInput | number
  profissionalId?: Prisma.IntFieldUpdateOperationsInput | number
  dataInicial?: Prisma.DateTimeFieldUpdateOperationsInput | Date | string
  dataFinal?: Prisma.NullableDateTimeFieldUpdateOperationsInput | Date | string | null
  prazoPagamentoDias?: Prisma.NullableIntFieldUpdateOperationsInput | number | null
  modalidadeTrabalho?: Prisma.StringFieldUpdateOperationsInput | string
  tipoFechamento?: Prisma.StringFieldUpdateOperationsInput | string
  tipoValor?: Prisma.StringFieldUpdateOperationsInput | string
  valorBase?: Prisma.FloatFieldUpdateOperationsInput | number
  horaInicio?: Prisma.StringFieldUpdateOperationsInput | string
  horaFim?: Prisma.StringFieldUpdateOperationsInput | string
  intervalo?: Prisma.StringFieldUpdateOperationsInput | string
  tipoTurno?: Prisma.StringFieldUpdateOperationsInput | string
  observacoes?: Prisma.NullableStringFieldUpdateOperationsInput | string | null
  fusoHorario?: Prisma.StringFieldUpdateOperationsInput | string
  createdAt?: Prisma.DateTimeFieldUpdateOperationsInput | Date | string
  updatedAt?: Prisma.DateTimeFieldUpdateOperationsInput | Date | string
  concluidoEm?: Prisma.NullableDateTimeFieldUpdateOperationsInput | Date | string | null
  deletedAt?: Prisma.NullableDateTimeFieldUpdateOperationsInput | Date | string | null
  diasPlantao?: Prisma.DiaPlantaoUncheckedUpdateManyWithoutPlantaoNestedInput
  fechamentos?: Prisma.FechamentoUncheckedUpdateManyWithoutPlantaoNestedInput
  antecipacoes?: Prisma.AntecipacaoUncheckedUpdateManyWithoutPlantaoNestedInput
}

export type PlantaoUncheckedUpdateManyWithoutLocalAtendimentoInput = {
  id?: Prisma.IntFieldUpdateOperationsInput | number
  uuid?: Prisma.StringFieldUpdateOperationsInput | string
  clienteId?: Prisma.IntFieldUpdateOperationsInput | number
  profissionalId?: Prisma.IntFieldUpdateOperationsInput | number
  dataInicial?: Prisma.DateTimeFieldUpdateOperationsInput | Date | string
  dataFinal?: Prisma.NullableDateTimeFieldUpdateOperationsInput | Date | string | null
  prazoPagamentoDias?: Prisma.NullableIntFieldUpdateOperationsInput | number | null
  modalidadeTrabalho?: Prisma.StringFieldUpdateOperationsInput | string
  tipoFechamento?: Prisma.StringFieldUpdateOperationsInput | string
  tipoValor?: Prisma.StringFieldUpdateOperationsInput | string
  valorBase?: Prisma.FloatFieldUpdateOperationsInput | number
  horaInicio?: Prisma.StringFieldUpdateOperationsInput | string
  horaFim?: Prisma.StringFieldUpdateOperationsInput | string
  intervalo?: Prisma.StringFieldUpdateOperationsInput | string
  tipoTurno?: Prisma.StringFieldUpdateOperationsInput | string
  observacoes?: Prisma.NullableStringFieldUpdateOperationsInput | string | null
  fusoHorario?: Prisma.StringFieldUpdateOperationsInput | string
  createdAt?: Prisma.DateTimeFieldUpdateOperationsInput | Date | string
  updatedAt?: Prisma.DateTimeFieldUpdateOperationsInput | Date | string
  concluidoEm?: Prisma.NullableDateTimeFieldUpdateOperationsInput | Date | string | null
  deletedAt?: Prisma.NullableDateTimeFieldUpdateOperationsInput | Date | string | null
}


/**
 * Count Type PlantaoCountOutputType
 */

export type PlantaoCountOutputType = {
  diasPlantao: number
  fechamentos: number
  antecipacoes: number
}

export type PlantaoCountOutputTypeSelect<ExtArgs extends runtime.Types.Extensions.InternalArgs = runtime.Types.Extensions.DefaultArgs> = {
  diasPlantao?: boolean | PlantaoCountOutputTypeCountDiasPlantaoArgs
  fechamentos?: boolean | PlantaoCountOutputTypeCountFechamentosArgs
  antecipacoes?: boolean | PlantaoCountOutputTypeCountAntecipacoesArgs
}

/**
 * PlantaoCountOutputType without action
 */
export type PlantaoCountOutputTypeDefaultArgs<ExtArgs extends runtime.Types.Extensions.InternalArgs = runtime.Types.Extensions.DefaultArgs> = {
  /**
   * Select specific fields to fetch from the PlantaoCountOutputType
   */
  select?: Prisma.PlantaoCountOutputTypeSelect<ExtArgs> | null
}

/**
 * PlantaoCountOutputType without action
 */
export type PlantaoCountOutputTypeCountDiasPlantaoArgs<ExtArgs extends runtime.Types.Extensions.InternalArgs = runtime.Types.Extensions.DefaultArgs> = {
  where?: Prisma.DiaPlantaoWhereInput
}

/**
 * PlantaoCountOutputType without action
 */
export type PlantaoCountOutputTypeCountFechamentosArgs<ExtArgs extends runtime.Types.Extensions.InternalArgs = runtime.Types.Extensions.DefaultArgs> = {
  where?: Prisma.FechamentoWhereInput
}

/**
 * PlantaoCountOutputType without action
 */
export type PlantaoCountOutputTypeCountAntecipacoesArgs<ExtArgs extends runtime.Types.Extensions.InternalArgs = runtime.Types.Extensions.DefaultArgs> = {
  where?: Prisma.AntecipacaoWhereInput
}


export type PlantaoSelect<ExtArgs extends runtime.Types.Extensions.InternalArgs = runtime.Types.Extensions.DefaultArgs> = runtime.Types.Extensions.GetSelect<{
  id?: boolean
  uuid?: boolean
  clienteId?: boolean
  localAtendimentoId?: boolean
  profissionalId?: boolean
  dataInicial?: boolean
  dataFinal?: boolean
  prazoPagamentoDias?: boolean
  modalidadeTrabalho?: boolean
  tipoFechamento?: boolean
  tipoValor?: boolean
  valorBase?: boolean
  horaInicio?: boolean
  horaFim?: boolean
  intervalo?: boolean
  tipoTurno?: boolean
  observacoes?: boolean
  fusoHorario?: boolean
  createdAt?: boolean
  updatedAt?: boolean
  concluidoEm?: boolean
  deletedAt?: boolean
  cliente?: boolean | Prisma.ClienteDefaultArgs<ExtArgs>
  localAtendimento?: boolean | Prisma.LocalAtendimentoDefaultArgs<ExtArgs>
  profissional?: boolean | Prisma.ProfissionalDefaultArgs<ExtArgs>
  diasPlantao?: boolean | Prisma.Plantao$diasPlantaoArgs<ExtArgs>
  fechamentos?: boolean | Prisma.Plantao$fechamentosArgs<ExtArgs>
  antecipacoes?: boolean | Prisma.Plantao$antecipacoesArgs<ExtArgs>
  _count?: boolean | Prisma.PlantaoCountOutputTypeDefaultArgs<ExtArgs>
}, ExtArgs["result"]["plantao"]>



export type PlantaoSelectScalar = {
  id?: boolean
  uuid?: boolean
  clienteId?: boolean
  localAtendimentoId?: boolean
  profissionalId?: boolean
  dataInicial?: boolean
  dataFinal?: boolean
  prazoPagamentoDias?: boolean
  modalidadeTrabalho?: boolean
  tipoFechamento?: boolean
  tipoValor?: boolean
  valorBase?: boolean
  horaInicio?: boolean
  horaFim?: boolean
  intervalo?: boolean
  tipoTurno?: boolean
  observacoes?: boolean
  fusoHorario?: boolean
  createdAt?: boolean
  updatedAt?: boolean
  concluidoEm?: boolean
  deletedAt?: boolean
}

export type PlantaoOmit<ExtArgs extends runtime.Types.Extensions.InternalArgs = runtime.Types.Extensions.DefaultArgs> = runtime.Types.Extensions.GetOmit<"id" | "uuid" | "clienteId" | "localAtendimentoId" | "profissionalId" | "dataInicial" | "dataFinal" | "prazoPagamentoDias" | "modalidadeTrabalho" | "tipoFechamento" | "tipoValor" | "valorBase" | "horaInicio" | "horaFim" | "intervalo" | "tipoTurno" | "observacoes" | "fusoHorario" | "createdAt" | "updatedAt" | "concluidoEm" | "deletedAt", ExtArgs["result"]["plantao"]>
export type PlantaoInclude<ExtArgs extends runtime.Types.Extensions.InternalArgs = runtime.Types.Extensions.DefaultArgs> = {
  cliente?: boolean | Prisma.ClienteDefaultArgs<ExtArgs>
  localAtendimento?: boolean | Prisma.LocalAtendimentoDefaultArgs<ExtArgs>
  profissional?: boolean | Prisma.ProfissionalDefaultArgs<ExtArgs>
  diasPlantao?: boolean | Prisma.Plantao$diasPlantaoArgs<ExtArgs>
  fechamentos?: boolean | Prisma.Plantao$fechamentosArgs<ExtArgs>
  antecipacoes?: boolean | Prisma.Plantao$antecipacoesArgs<ExtArgs>
  _count?: boolean | Prisma.PlantaoCountOutputTypeDefaultArgs<ExtArgs>
}

export type $PlantaoPayload<ExtArgs extends runtime.Types.Extensions.InternalArgs = runtime.Types.Extensions.DefaultArgs> = {
  name: "Plantao"
  objects: {
    cliente: Prisma.$ClientePayload<ExtArgs>
    localAtendimento: Prisma.$LocalAtendimentoPayload<ExtArgs>
    profissional: Prisma.$ProfissionalPayload<ExtArgs>
    diasPlantao: Prisma.$DiaPlantaoPayload<ExtArgs>[]
    fechamentos: Prisma.$FechamentoPayload<ExtArgs>[]
    antecipacoes: Prisma.$AntecipacaoPayload<ExtArgs>[]
  }
  scalars: runtime.Types.Extensions.GetPayloadResult<{
    id: number
    uuid: string
    clienteId: number
    localAtendimentoId: number
    profissionalId: number
    dataInicial: Date
    dataFinal: Date | null
    prazoPagamentoDias: number | null
    modalidadeTrabalho: string
    tipoFechamento: string
    tipoValor: string
    valorBase: number
    horaInicio: string
    horaFim: string
    intervalo: string
    tipoTurno: string
    observacoes: string | null
    fusoHorario: string
    createdAt: Date
    updatedAt: Date
    concluidoEm: Date | null
    deletedAt: Date | null
  }, ExtArgs["result"]["plantao"]>
  composites: {}
}

export type PlantaoGetPayload<S extends boolean | null | undefined | PlantaoDefaultArgs> = runtime.Types.Result.GetResult<Prisma.$PlantaoPayload, S>

export type PlantaoCountArgs<ExtArgs extends runtime.Types.Extensions.InternalArgs = runtime.Types.Extensions.DefaultArgs> =
  Omit<PlantaoFindManyArgs, 'select' | 'include' | 'distinct' | 'omit'> & {
    select?: PlantaoCountAggregateInputType | true
  }

export interface PlantaoDelegate<ExtArgs extends runtime.Types.Extensions.InternalArgs = runtime.Types.Extensions.DefaultArgs, GlobalOmitOptions = {}> {
  [K: symbol]: { types: Prisma.TypeMap<ExtArgs>['model']['Plantao'], meta: { name: 'Plantao' } }
  /**
   * Find zero or one Plantao that matches the filter.
   * @param {PlantaoFindUniqueArgs} args - Arguments to find a Plantao
   * @example
   * // Get one Plantao
   * const plantao = await prisma.plantao.findUnique({
   *   where: {
   *     // ... provide filter here
   *   }
   * })
   */
  findUnique<T extends PlantaoFindUniqueArgs>(args: Prisma.SelectSubset<T, PlantaoFindUniqueArgs<ExtArgs>>): Prisma.Prisma__PlantaoClient<runtime.Types.Result.GetResult<Prisma.$PlantaoPayload<ExtArgs>, T, "findUnique", GlobalOmitOptions> | null, null, ExtArgs, GlobalOmitOptions>

  /**
   * Find one Plantao that matches the filter or throw an error with `error.code='P2025'`
   * if no matches were found.
   * @param {PlantaoFindUniqueOrThrowArgs} args - Arguments to find a Plantao
   * @example
   * // Get one Plantao
   * const plantao = await prisma.plantao.findUniqueOrThrow({
   *   where: {
   *     // ... provide filter here
   *   }
   * })
   */
  findUniqueOrThrow<T extends PlantaoFindUniqueOrThrowArgs>(args: Prisma.SelectSubset<T, PlantaoFindUniqueOrThrowArgs<ExtArgs>>): Prisma.Prisma__PlantaoClient<runtime.Types.Result.GetResult<Prisma.$PlantaoPayload<ExtArgs>, T, "findUniqueOrThrow", GlobalOmitOptions>, never, ExtArgs, GlobalOmitOptions>

  /**
   * Find the first Plantao that matches the filter.
   * Note, that providing `undefined` is treated as the value not being there.
   * Read more here: https://pris.ly/d/null-undefined
   * @param {PlantaoFindFirstArgs} args - Arguments to find a Plantao
   * @example
   * // Get one Plantao
   * const plantao = await prisma.plantao.findFirst({
   *   where: {
   *     // ... provide filter here
   *   }
   * })
   */
  findFirst<T extends PlantaoFindFirstArgs>(args?: Prisma.SelectSubset<T, PlantaoFindFirstArgs<ExtArgs>>): Prisma.Prisma__PlantaoClient<runtime.Types.Result.GetResult<Prisma.$PlantaoPayload<ExtArgs>, T, "findFirst", GlobalOmitOptions> | null, null, ExtArgs, GlobalOmitOptions>

  /**
   * Find the first Plantao that matches the filter or
   * throw `PrismaKnownClientError` with `P2025` code if no matches were found.
   * Note, that providing `undefined` is treated as the value not being there.
   * Read more here: https://pris.ly/d/null-undefined
   * @param {PlantaoFindFirstOrThrowArgs} args - Arguments to find a Plantao
   * @example
   * // Get one Plantao
   * const plantao = await prisma.plantao.findFirstOrThrow({
   *   where: {
   *     // ... provide filter here
   *   }
   * })
   */
  findFirstOrThrow<T extends PlantaoFindFirstOrThrowArgs>(args?: Prisma.SelectSubset<T, PlantaoFindFirstOrThrowArgs<ExtArgs>>): Prisma.Prisma__PlantaoClient<runtime.Types.Result.GetResult<Prisma.$PlantaoPayload<ExtArgs>, T, "findFirstOrThrow", GlobalOmitOptions>, never, ExtArgs, GlobalOmitOptions>

  /**
   * Find zero or more Plantaos that matches the filter.
   * Note, that providing `undefined` is treated as the value not being there.
   * Read more here: https://pris.ly/d/null-undefined
   * @param {PlantaoFindManyArgs} args - Arguments to filter and select certain fields only.
   * @example
   * // Get all Plantaos
   * const plantaos = await prisma.plantao.findMany()
   * 
   * // Get first 10 Plantaos
   * const plantaos = await prisma.plantao.findMany({ take: 10 })
   * 
   * // Only select the `id`
   * const plantaoWithIdOnly = await prisma.plantao.findMany({ select: { id: true } })
   * 
   */
  findMany<T extends PlantaoFindManyArgs>(args?: Prisma.SelectSubset<T, PlantaoFindManyArgs<ExtArgs>>): Prisma.PrismaPromise<runtime.Types.Result.GetResult<Prisma.$PlantaoPayload<ExtArgs>, T, "findMany", GlobalOmitOptions>>

  /**
   * Create a Plantao.
   * @param {PlantaoCreateArgs} args - Arguments to create a Plantao.
   * @example
   * // Create one Plantao
   * const Plantao = await prisma.plantao.create({
   *   data: {
   *     // ... data to create a Plantao
   *   }
   * })
   * 
   */
  create<T extends PlantaoCreateArgs>(args: Prisma.SelectSubset<T, PlantaoCreateArgs<ExtArgs>>): Prisma.Prisma__PlantaoClient<runtime.Types.Result.GetResult<Prisma.$PlantaoPayload<ExtArgs>, T, "create", GlobalOmitOptions>, never, ExtArgs, GlobalOmitOptions>

  /**
   * Create many Plantaos.
   * @param {PlantaoCreateManyArgs} args - Arguments to create many Plantaos.
   * @example
   * // Create many Plantaos
   * const plantao = await prisma.plantao.createMany({
   *   data: [
   *     // ... provide data here
   *   ]
   * })
   *     
   */
  createMany<T extends PlantaoCreateManyArgs>(args?: Prisma.SelectSubset<T, PlantaoCreateManyArgs<ExtArgs>>): Prisma.PrismaPromise<Prisma.BatchPayload>

  /**
   * Delete a Plantao.
   * @param {PlantaoDeleteArgs} args - Arguments to delete one Plantao.
   * @example
   * // Delete one Plantao
   * const Plantao = await prisma.plantao.delete({
   *   where: {
   *     // ... filter to delete one Plantao
   *   }
   * })
   * 
   */
  delete<T extends PlantaoDeleteArgs>(args: Prisma.SelectSubset<T, PlantaoDeleteArgs<ExtArgs>>): Prisma.Prisma__PlantaoClient<runtime.Types.Result.GetResult<Prisma.$PlantaoPayload<ExtArgs>, T, "delete", GlobalOmitOptions>, never, ExtArgs, GlobalOmitOptions>

  /**
   * Update one Plantao.
   * @param {PlantaoUpdateArgs} args - Arguments to update one Plantao.
   * @example
   * // Update one Plantao
   * const plantao = await prisma.plantao.update({
   *   where: {
   *     // ... provide filter here
   *   },
   *   data: {
   *     // ... provide data here
   *   }
   * })
   * 
   */
  update<T extends PlantaoUpdateArgs>(args: Prisma.SelectSubset<T, PlantaoUpdateArgs<ExtArgs>>): Prisma.Prisma__PlantaoClient<runtime.Types.Result.GetResult<Prisma.$PlantaoPayload<ExtArgs>, T, "update", GlobalOmitOptions>, never, ExtArgs, GlobalOmitOptions>

  /**
   * Delete zero or more Plantaos.
   * @param {PlantaoDeleteManyArgs} args - Arguments to filter Plantaos to delete.
   * @example
   * // Delete a few Plantaos
   * const { count } = await prisma.plantao.deleteMany({
   *   where: {
   *     // ... provide filter here
   *   }
   * })
   * 
   */
  deleteMany<T extends PlantaoDeleteManyArgs>(args?: Prisma.SelectSubset<T, PlantaoDeleteManyArgs<ExtArgs>>): Prisma.PrismaPromise<Prisma.BatchPayload>

  /**
   * Update zero or more Plantaos.
   * Note, that providing `undefined` is treated as the value not being there.
   * Read more here: https://pris.ly/d/null-undefined
   * @param {PlantaoUpdateManyArgs} args - Arguments to update one or more rows.
   * @example
   * // Update many Plantaos
   * const plantao = await prisma.plantao.updateMany({
   *   where: {
   *     // ... provide filter here
   *   },
   *   data: {
   *     // ... provide data here
   *   }
   * })
   * 
   */
  updateMany<T extends PlantaoUpdateManyArgs>(args: Prisma.SelectSubset<T, PlantaoUpdateManyArgs<ExtArgs>>): Prisma.PrismaPromise<Prisma.BatchPayload>

  /**
   * Create or update one Plantao.
   * @param {PlantaoUpsertArgs} args - Arguments to update or create a Plantao.
   * @example
   * // Update or create a Plantao
   * const plantao = await prisma.plantao.upsert({
   *   create: {
   *     // ... data to create a Plantao
   *   },
   *   update: {
   *     // ... in case it already exists, update
   *   },
   *   where: {
   *     // ... the filter for the Plantao we want to update
   *   }
   * })
   */
  upsert<T extends PlantaoUpsertArgs>(args: Prisma.SelectSubset<T, PlantaoUpsertArgs<ExtArgs>>): Prisma.Prisma__PlantaoClient<runtime.Types.Result.GetResult<Prisma.$PlantaoPayload<ExtArgs>, T, "upsert", GlobalOmitOptions>, never, ExtArgs, GlobalOmitOptions>


  /**
   * Count the number of Plantaos.
   * Note, that providing `undefined` is treated as the value not being there.
   * Read more here: https://pris.ly/d/null-undefined
   * @param {PlantaoCountArgs} args - Arguments to filter Plantaos to count.
   * @example
   * // Count the number of Plantaos
   * const count = await prisma.plantao.count({
   *   where: {
   *     // ... the filter for the Plantaos we want to count
   *   }
   * })
  **/
  count<T extends PlantaoCountArgs>(
    args?: Prisma.Subset<T, PlantaoCountArgs>,
  ): Prisma.PrismaPromise<
    T extends runtime.Types.Utils.Record<'select', any>
      ? T['select'] extends true
        ? number
        : Prisma.GetScalarType<T['select'], PlantaoCountAggregateOutputType>
      : number
  >

  /**
   * Allows you to perform aggregations operations on a Plantao.
   * Note, that providing `undefined` is treated as the value not being there.
   * Read more here: https://pris.ly/d/null-undefined
   * @param {PlantaoAggregateArgs} args - Select which aggregations you would like to apply and on what fields.
   * @example
   * // Ordered by age ascending
   * // Where email contains prisma.io
   * // Limited to the 10 users
   * const aggregations = await prisma.user.aggregate({
   *   _avg: {
   *     age: true,
   *   },
   *   where: {
   *     email: {
   *       contains: "prisma.io",
   *     },
   *   },
   *   orderBy: {
   *     age: "asc",
   *   },
   *   take: 10,
   * })
  **/
  aggregate<T extends PlantaoAggregateArgs>(args: Prisma.Subset<T, PlantaoAggregateArgs>): Prisma.PrismaPromise<GetPlantaoAggregateType<T>>

  /**
   * Group by Plantao.
   * Note, that providing `undefined` is treated as the value not being there.
   * Read more here: https://pris.ly/d/null-undefined
   * @param {PlantaoGroupByArgs} args - Group by arguments.
   * @example
   * // Group by city, order by createdAt, get count
   * const result = await prisma.user.groupBy({
   *   by: ['city', 'createdAt'],
   *   orderBy: {
   *     createdAt: true
   *   },
   *   _count: {
   *     _all: true
   *   },
   * })
   * 
  **/
  groupBy<
    T extends PlantaoGroupByArgs,
    HasSelectOrTake extends Prisma.Or<
      Prisma.Extends<'skip', Prisma.Keys<T>>,
      Prisma.Extends<'take', Prisma.Keys<T>>
    >,
    OrderByArg extends Prisma.True extends HasSelectOrTake
      ? { orderBy: PlantaoGroupByArgs['orderBy'] }
      : { orderBy?: PlantaoGroupByArgs['orderBy'] },
    OrderFields extends Prisma.ExcludeUnderscoreKeys<Prisma.Keys<Prisma.MaybeTupleToUnion<T['orderBy']>>>,
    ByFields extends Prisma.MaybeTupleToUnion<T['by']>,
    ByValid extends Prisma.Has<ByFields, OrderFields>,
    HavingFields extends Prisma.GetHavingFields<T['having']>,
    HavingValid extends Prisma.Has<ByFields, HavingFields>,
    ByEmpty extends T['by'] extends never[] ? Prisma.True : Prisma.False,
    InputErrors extends ByEmpty extends Prisma.True
    ? `Error: "by" must not be empty.`
    : HavingValid extends Prisma.False
    ? {
        [P in HavingFields]: P extends ByFields
          ? never
          : P extends string
          ? `Error: Field "${P}" used in "having" needs to be provided in "by".`
          : [
              Error,
              'Field ',
              P,
              ` in "having" needs to be provided in "by"`,
            ]
      }[HavingFields]
    : 'take' extends Prisma.Keys<T>
    ? 'orderBy' extends Prisma.Keys<T>
      ? ByValid extends Prisma.True
        ? {}
        : {
            [P in OrderFields]: P extends ByFields
              ? never
              : `Error: Field "${P}" in "orderBy" needs to be provided in "by"`
          }[OrderFields]
      : 'Error: If you provide "take", you also need to provide "orderBy"'
    : 'skip' extends Prisma.Keys<T>
    ? 'orderBy' extends Prisma.Keys<T>
      ? ByValid extends Prisma.True
        ? {}
        : {
            [P in OrderFields]: P extends ByFields
              ? never
              : `Error: Field "${P}" in "orderBy" needs to be provided in "by"`
          }[OrderFields]
      : 'Error: If you provide "skip", you also need to provide "orderBy"'
    : ByValid extends Prisma.True
    ? {}
    : {
        [P in OrderFields]: P extends ByFields
          ? never
          : `Error: Field "${P}" in "orderBy" needs to be provided in "by"`
      }[OrderFields]
  >(args: Prisma.SubsetIntersection<T, PlantaoGroupByArgs, OrderByArg> & InputErrors): {} extends InputErrors ? GetPlantaoGroupByPayload<T> : Prisma.PrismaPromise<InputErrors>
/**
 * Fields of the Plantao model
 */
readonly fields: PlantaoFieldRefs;
}

/**
 * The delegate class that acts as a "Promise-like" for Plantao.
 * Why is this prefixed with `Prisma__`?
 * Because we want to prevent naming conflicts as mentioned in
 * https://github.com/prisma/prisma-client-js/issues/707
 */
export interface Prisma__PlantaoClient<T, Null = never, ExtArgs extends runtime.Types.Extensions.InternalArgs = runtime.Types.Extensions.DefaultArgs, GlobalOmitOptions = {}> extends Prisma.PrismaPromise<T> {
  readonly [Symbol.toStringTag]: "PrismaPromise"
  cliente<T extends Prisma.ClienteDefaultArgs<ExtArgs> = {}>(args?: Prisma.Subset<T, Prisma.ClienteDefaultArgs<ExtArgs>>): Prisma.Prisma__ClienteClient<runtime.Types.Result.GetResult<Prisma.$ClientePayload<ExtArgs>, T, "findUniqueOrThrow", GlobalOmitOptions> | Null, Null, ExtArgs, GlobalOmitOptions>
  localAtendimento<T extends Prisma.LocalAtendimentoDefaultArgs<ExtArgs> = {}>(args?: Prisma.Subset<T, Prisma.LocalAtendimentoDefaultArgs<ExtArgs>>): Prisma.Prisma__LocalAtendimentoClient<runtime.Types.Result.GetResult<Prisma.$LocalAtendimentoPayload<ExtArgs>, T, "findUniqueOrThrow", GlobalOmitOptions> | Null, Null, ExtArgs, GlobalOmitOptions>
  profissional<T extends Prisma.ProfissionalDefaultArgs<ExtArgs> = {}>(args?: Prisma.Subset<T, Prisma.ProfissionalDefaultArgs<ExtArgs>>): Prisma.Prisma__ProfissionalClient<runtime.Types.Result.GetResult<Prisma.$ProfissionalPayload<ExtArgs>, T, "findUniqueOrThrow", GlobalOmitOptions> | Null, Null, ExtArgs, GlobalOmitOptions>
  diasPlantao<T extends Prisma.Plantao$diasPlantaoArgs<ExtArgs> = {}>(args?: Prisma.Subset<T, Prisma.Plantao$diasPlantaoArgs<ExtArgs>>): Prisma.PrismaPromise<runtime.Types.Result.GetResult<Prisma.$DiaPlantaoPayload<ExtArgs>, T, "findMany", GlobalOmitOptions> | Null>
  fechamentos<T extends Prisma.Plantao$fechamentosArgs<ExtArgs> = {}>(args?: Prisma.Subset<T, Prisma.Plantao$fechamentosArgs<ExtArgs>>): Prisma.PrismaPromise<runtime.Types.Result.GetResult<Prisma.$FechamentoPayload<ExtArgs>, T, "findMany", GlobalOmitOptions> | Null>
  antecipacoes<T extends Prisma.Plantao$antecipacoesArgs<ExtArgs> = {}>(args?: Prisma.Subset<T, Prisma.Plantao$antecipacoesArgs<ExtArgs>>): Prisma.PrismaPromise<runtime.Types.Result.GetResult<Prisma.$AntecipacaoPayload<ExtArgs>, T, "findMany", GlobalOmitOptions> | Null>
  /**
   * Attaches callbacks for the resolution and/or rejection of the Promise.
   * @param onfulfilled The callback to execute when the Promise is resolved.
   * @param onrejected The callback to execute when the Promise is rejected.
   * @returns A Promise for the completion of which ever callback is executed.
   */
  then<TResult1 = T, TResult2 = never>(onfulfilled?: ((value: T) => TResult1 | PromiseLike<TResult1>) | undefined | null, onrejected?: ((reason: any) => TResult2 | PromiseLike<TResult2>) | undefined | null): runtime.Types.Utils.JsPromise<TResult1 | TResult2>
  /**
   * Attaches a callback for only the rejection of the Promise.
   * @param onrejected The callback to execute when the Promise is rejected.
   * @returns A Promise for the completion of the callback.
   */
  catch<TResult = never>(onrejected?: ((reason: any) => TResult | PromiseLike<TResult>) | undefined | null): runtime.Types.Utils.JsPromise<T | TResult>
  /**
   * Attaches a callback that is invoked when the Promise is settled (fulfilled or rejected). The
   * resolved value cannot be modified from the callback.
   * @param onfinally The callback to execute when the Promise is settled (fulfilled or rejected).
   * @returns A Promise for the completion of the callback.
   */
  finally(onfinally?: (() => void) | undefined | null): runtime.Types.Utils.JsPromise<T>
}




/**
 * Fields of the Plantao model
 */
export interface PlantaoFieldRefs {
  readonly id: Prisma.FieldRef<"Plantao", 'Int'>
  readonly uuid: Prisma.FieldRef<"Plantao", 'String'>
  readonly clienteId: Prisma.FieldRef<"Plantao", 'Int'>
  readonly localAtendimentoId: Prisma.FieldRef<"Plantao", 'Int'>
  readonly profissionalId: Prisma.FieldRef<"Plantao", 'Int'>
  readonly dataInicial: Prisma.FieldRef<"Plantao", 'DateTime'>
  readonly dataFinal: Prisma.FieldRef<"Plantao", 'DateTime'>
  readonly prazoPagamentoDias: Prisma.FieldRef<"Plantao", 'Int'>
  readonly modalidadeTrabalho: Prisma.FieldRef<"Plantao", 'String'>
  readonly tipoFechamento: Prisma.FieldRef<"Plantao", 'String'>
  readonly tipoValor: Prisma.FieldRef<"Plantao", 'String'>
  readonly valorBase: Prisma.FieldRef<"Plantao", 'Float'>
  readonly horaInicio: Prisma.FieldRef<"Plantao", 'String'>
  readonly horaFim: Prisma.FieldRef<"Plantao", 'String'>
  readonly intervalo: Prisma.FieldRef<"Plantao", 'String'>
  readonly tipoTurno: Prisma.FieldRef<"Plantao", 'String'>
  readonly observacoes: Prisma.FieldRef<"Plantao", 'String'>
  readonly fusoHorario: Prisma.FieldRef<"Plantao", 'String'>
  readonly createdAt: Prisma.FieldRef<"Plantao", 'DateTime'>
  readonly updatedAt: Prisma.FieldRef<"Plantao", 'DateTime'>
  readonly concluidoEm: Prisma.FieldRef<"Plantao", 'DateTime'>
  readonly deletedAt: Prisma.FieldRef<"Plantao", 'DateTime'>
}
    

// Custom InputTypes
/**
 * Plantao findUnique
 */
export type PlantaoFindUniqueArgs<ExtArgs extends runtime.Types.Extensions.InternalArgs = runtime.Types.Extensions.DefaultArgs> = {
  /**
   * Select specific fields to fetch from the Plantao
   */
  select?: Prisma.PlantaoSelect<ExtArgs> | null
  /**
   * Omit specific fields from the Plantao
   */
  omit?: Prisma.PlantaoOmit<ExtArgs> | null
  /**
   * Choose, which related nodes to fetch as well
   */
  include?: Prisma.PlantaoInclude<ExtArgs> | null
  /**
   * Filter, which Plantao to fetch.
   */
  where: Prisma.PlantaoWhereUniqueInput
}

/**
 * Plantao findUniqueOrThrow
 */
export type PlantaoFindUniqueOrThrowArgs<ExtArgs extends runtime.Types.Extensions.InternalArgs = runtime.Types.Extensions.DefaultArgs> = {
  /**
   * Select specific fields to fetch from the Plantao
   */
  select?: Prisma.PlantaoSelect<ExtArgs> | null
  /**
   * Omit specific fields from the Plantao
   */
  omit?: Prisma.PlantaoOmit<ExtArgs> | null
  /**
   * Choose, which related nodes to fetch as well
   */
  include?: Prisma.PlantaoInclude<ExtArgs> | null
  /**
   * Filter, which Plantao to fetch.
   */
  where: Prisma.PlantaoWhereUniqueInput
}

/**
 * Plantao findFirst
 */
export type PlantaoFindFirstArgs<ExtArgs extends runtime.Types.Extensions.InternalArgs = runtime.Types.Extensions.DefaultArgs> = {
  /**
   * Select specific fields to fetch from the Plantao
   */
  select?: Prisma.PlantaoSelect<ExtArgs> | null
  /**
   * Omit specific fields from the Plantao
   */
  omit?: Prisma.PlantaoOmit<ExtArgs> | null
  /**
   * Choose, which related nodes to fetch as well
   */
  include?: Prisma.PlantaoInclude<ExtArgs> | null
  /**
   * Filter, which Plantao to fetch.
   */
  where?: Prisma.PlantaoWhereInput
  /**
   * {@link https://www.prisma.io/docs/concepts/components/prisma-client/sorting Sorting Docs}
   * 
   * Determine the order of Plantaos to fetch.
   */
  orderBy?: Prisma.PlantaoOrderByWithRelationInput | Prisma.PlantaoOrderByWithRelationInput[]
  /**
   * {@link https://www.prisma.io/docs/concepts/components/prisma-client/pagination#cursor-based-pagination Cursor Docs}
   * 
   * Sets the position for searching for Plantaos.
   */
  cursor?: Prisma.PlantaoWhereUniqueInput
  /**
   * {@link https://www.prisma.io/docs/concepts/components/prisma-client/pagination Pagination Docs}
   * 
   * Take `±n` Plantaos from the position of the cursor.
   */
  take?: number
  /**
   * {@link https://www.prisma.io/docs/concepts/components/prisma-client/pagination Pagination Docs}
   * 
   * Skip the first `n` Plantaos.
   */
  skip?: number
  /**
   * {@link https://www.prisma.io/docs/concepts/components/prisma-client/distinct Distinct Docs}
   * 
   * Filter by unique combinations of Plantaos.
   */
  distinct?: Prisma.PlantaoScalarFieldEnum | Prisma.PlantaoScalarFieldEnum[]
}

/**
 * Plantao findFirstOrThrow
 */
export type PlantaoFindFirstOrThrowArgs<ExtArgs extends runtime.Types.Extensions.InternalArgs = runtime.Types.Extensions.DefaultArgs> = {
  /**
   * Select specific fields to fetch from the Plantao
   */
  select?: Prisma.PlantaoSelect<ExtArgs> | null
  /**
   * Omit specific fields from the Plantao
   */
  omit?: Prisma.PlantaoOmit<ExtArgs> | null
  /**
   * Choose, which related nodes to fetch as well
   */
  include?: Prisma.PlantaoInclude<ExtArgs> | null
  /**
   * Filter, which Plantao to fetch.
   */
  where?: Prisma.PlantaoWhereInput
  /**
   * {@link https://www.prisma.io/docs/concepts/components/prisma-client/sorting Sorting Docs}
   * 
   * Determine the order of Plantaos to fetch.
   */
  orderBy?: Prisma.PlantaoOrderByWithRelationInput | Prisma.PlantaoOrderByWithRelationInput[]
  /**
   * {@link https://www.prisma.io/docs/concepts/components/prisma-client/pagination#cursor-based-pagination Cursor Docs}
   * 
   * Sets the position for searching for Plantaos.
   */
  cursor?: Prisma.PlantaoWhereUniqueInput
  /**
   * {@link https://www.prisma.io/docs/concepts/components/prisma-client/pagination Pagination Docs}
   * 
   * Take `±n` Plantaos from the position of the cursor.
   */
  take?: number
  /**
   * {@link https://www.prisma.io/docs/concepts/components/prisma-client/pagination Pagination Docs}
   * 
   * Skip the first `n` Plantaos.
   */
  skip?: number
  /**
   * {@link https://www.prisma.io/docs/concepts/components/prisma-client/distinct Distinct Docs}
   * 
   * Filter by unique combinations of Plantaos.
   */
  distinct?: Prisma.PlantaoScalarFieldEnum | Prisma.PlantaoScalarFieldEnum[]
}

/**
 * Plantao findMany
 */
export type PlantaoFindManyArgs<ExtArgs extends runtime.Types.Extensions.InternalArgs = runtime.Types.Extensions.DefaultArgs> = {
  /**
   * Select specific fields to fetch from the Plantao
   */
  select?: Prisma.PlantaoSelect<ExtArgs> | null
  /**
   * Omit specific fields from the Plantao
   */
  omit?: Prisma.PlantaoOmit<ExtArgs> | null
  /**
   * Choose, which related nodes to fetch as well
   */
  include?: Prisma.PlantaoInclude<ExtArgs> | null
  /**
   * Filter, which Plantaos to fetch.
   */
  where?: Prisma.PlantaoWhereInput
  /**
   * {@link https://www.prisma.io/docs/concepts/components/prisma-client/sorting Sorting Docs}
   * 
   * Determine the order of Plantaos to fetch.
   */
  orderBy?: Prisma.PlantaoOrderByWithRelationInput | Prisma.PlantaoOrderByWithRelationInput[]
  /**
   * {@link https://www.prisma.io/docs/concepts/components/prisma-client/pagination#cursor-based-pagination Cursor Docs}
   * 
   * Sets the position for listing Plantaos.
   */
  cursor?: Prisma.PlantaoWhereUniqueInput
  /**
   * {@link https://www.prisma.io/docs/concepts/components/prisma-client/pagination Pagination Docs}
   * 
   * Take `±n` Plantaos from the position of the cursor.
   */
  take?: number
  /**
   * {@link https://www.prisma.io/docs/concepts/components/prisma-client/pagination Pagination Docs}
   * 
   * Skip the first `n` Plantaos.
   */
  skip?: number
  distinct?: Prisma.PlantaoScalarFieldEnum | Prisma.PlantaoScalarFieldEnum[]
}

/**
 * Plantao create
 */
export type PlantaoCreateArgs<ExtArgs extends runtime.Types.Extensions.InternalArgs = runtime.Types.Extensions.DefaultArgs> = {
  /**
   * Select specific fields to fetch from the Plantao
   */
  select?: Prisma.PlantaoSelect<ExtArgs> | null
  /**
   * Omit specific fields from the Plantao
   */
  omit?: Prisma.PlantaoOmit<ExtArgs> | null
  /**
   * Choose, which related nodes to fetch as well
   */
  include?: Prisma.PlantaoInclude<ExtArgs> | null
  /**
   * The data needed to create a Plantao.
   */
  data: Prisma.XOR<Prisma.PlantaoCreateInput, Prisma.PlantaoUncheckedCreateInput>
}

/**
 * Plantao createMany
 */
export type PlantaoCreateManyArgs<ExtArgs extends runtime.Types.Extensions.InternalArgs = runtime.Types.Extensions.DefaultArgs> = {
  /**
   * The data used to create many Plantaos.
   */
  data: Prisma.PlantaoCreateManyInput | Prisma.PlantaoCreateManyInput[]
  skipDuplicates?: boolean
}

/**
 * Plantao update
 */
export type PlantaoUpdateArgs<ExtArgs extends runtime.Types.Extensions.InternalArgs = runtime.Types.Extensions.DefaultArgs> = {
  /**
   * Select specific fields to fetch from the Plantao
   */
  select?: Prisma.PlantaoSelect<ExtArgs> | null
  /**
   * Omit specific fields from the Plantao
   */
  omit?: Prisma.PlantaoOmit<ExtArgs> | null
  /**
   * Choose, which related nodes to fetch as well
   */
  include?: Prisma.PlantaoInclude<ExtArgs> | null
  /**
   * The data needed to update a Plantao.
   */
  data: Prisma.XOR<Prisma.PlantaoUpdateInput, Prisma.PlantaoUncheckedUpdateInput>
  /**
   * Choose, which Plantao to update.
   */
  where: Prisma.PlantaoWhereUniqueInput
}

/**
 * Plantao updateMany
 */
export type PlantaoUpdateManyArgs<ExtArgs extends runtime.Types.Extensions.InternalArgs = runtime.Types.Extensions.DefaultArgs> = {
  /**
   * The data used to update Plantaos.
   */
  data: Prisma.XOR<Prisma.PlantaoUpdateManyMutationInput, Prisma.PlantaoUncheckedUpdateManyInput>
  /**
   * Filter which Plantaos to update
   */
  where?: Prisma.PlantaoWhereInput
  /**
   * Limit how many Plantaos to update.
   */
  limit?: number
}

/**
 * Plantao upsert
 */
export type PlantaoUpsertArgs<ExtArgs extends runtime.Types.Extensions.InternalArgs = runtime.Types.Extensions.DefaultArgs> = {
  /**
   * Select specific fields to fetch from the Plantao
   */
  select?: Prisma.PlantaoSelect<ExtArgs> | null
  /**
   * Omit specific fields from the Plantao
   */
  omit?: Prisma.PlantaoOmit<ExtArgs> | null
  /**
   * Choose, which related nodes to fetch as well
   */
  include?: Prisma.PlantaoInclude<ExtArgs> | null
  /**
   * The filter to search for the Plantao to update in case it exists.
   */
  where: Prisma.PlantaoWhereUniqueInput
  /**
   * In case the Plantao found by the `where` argument doesn't exist, create a new Plantao with this data.
   */
  create: Prisma.XOR<Prisma.PlantaoCreateInput, Prisma.PlantaoUncheckedCreateInput>
  /**
   * In case the Plantao was found with the provided `where` argument, update it with this data.
   */
  update: Prisma.XOR<Prisma.PlantaoUpdateInput, Prisma.PlantaoUncheckedUpdateInput>
}

/**
 * Plantao delete
 */
export type PlantaoDeleteArgs<ExtArgs extends runtime.Types.Extensions.InternalArgs = runtime.Types.Extensions.DefaultArgs> = {
  /**
   * Select specific fields to fetch from the Plantao
   */
  select?: Prisma.PlantaoSelect<ExtArgs> | null
  /**
   * Omit specific fields from the Plantao
   */
  omit?: Prisma.PlantaoOmit<ExtArgs> | null
  /**
   * Choose, which related nodes to fetch as well
   */
  include?: Prisma.PlantaoInclude<ExtArgs> | null
  /**
   * Filter which Plantao to delete.
   */
  where: Prisma.PlantaoWhereUniqueInput
}

/**
 * Plantao deleteMany
 */
export type PlantaoDeleteManyArgs<ExtArgs extends runtime.Types.Extensions.InternalArgs = runtime.Types.Extensions.DefaultArgs> = {
  /**
   * Filter which Plantaos to delete
   */
  where?: Prisma.PlantaoWhereInput
  /**
   * Limit how many Plantaos to delete.
   */
  limit?: number
}

/**
 * Plantao.diasPlantao
 */
export type Plantao$diasPlantaoArgs<ExtArgs extends runtime.Types.Extensions.InternalArgs = runtime.Types.Extensions.DefaultArgs> = {
  /**
   * Select specific fields to fetch from the DiaPlantao
   */
  select?: Prisma.DiaPlantaoSelect<ExtArgs> | null
  /**
   * Omit specific fields from the DiaPlantao
   */
  omit?: Prisma.DiaPlantaoOmit<ExtArgs> | null
  /**
   * Choose, which related nodes to fetch as well
   */
  include?: Prisma.DiaPlantaoInclude<ExtArgs> | null
  where?: Prisma.DiaPlantaoWhereInput
  orderBy?: Prisma.DiaPlantaoOrderByWithRelationInput | Prisma.DiaPlantaoOrderByWithRelationInput[]
  cursor?: Prisma.DiaPlantaoWhereUniqueInput
  take?: number
  skip?: number
  distinct?: Prisma.DiaPlantaoScalarFieldEnum | Prisma.DiaPlantaoScalarFieldEnum[]
}

/**
 * Plantao.fechamentos
 */
export type Plantao$fechamentosArgs<ExtArgs extends runtime.Types.Extensions.InternalArgs = runtime.Types.Extensions.DefaultArgs> = {
  /**
   * Select specific fields to fetch from the Fechamento
   */
  select?: Prisma.FechamentoSelect<ExtArgs> | null
  /**
   * Omit specific fields from the Fechamento
   */
  omit?: Prisma.FechamentoOmit<ExtArgs> | null
  /**
   * Choose, which related nodes to fetch as well
   */
  include?: Prisma.FechamentoInclude<ExtArgs> | null
  where?: Prisma.FechamentoWhereInput
  orderBy?: Prisma.FechamentoOrderByWithRelationInput | Prisma.FechamentoOrderByWithRelationInput[]
  cursor?: Prisma.FechamentoWhereUniqueInput
  take?: number
  skip?: number
  distinct?: Prisma.FechamentoScalarFieldEnum | Prisma.FechamentoScalarFieldEnum[]
}

/**
 * Plantao.antecipacoes
 */
export type Plantao$antecipacoesArgs<ExtArgs extends runtime.Types.Extensions.InternalArgs = runtime.Types.Extensions.DefaultArgs> = {
  /**
   * Select specific fields to fetch from the Antecipacao
   */
  select?: Prisma.AntecipacaoSelect<ExtArgs> | null
  /**
   * Omit specific fields from the Antecipacao
   */
  omit?: Prisma.AntecipacaoOmit<ExtArgs> | null
  /**
   * Choose, which related nodes to fetch as well
   */
  include?: Prisma.AntecipacaoInclude<ExtArgs> | null
  where?: Prisma.AntecipacaoWhereInput
  orderBy?: Prisma.AntecipacaoOrderByWithRelationInput | Prisma.AntecipacaoOrderByWithRelationInput[]
  cursor?: Prisma.AntecipacaoWhereUniqueInput
  take?: number
  skip?: number
  distinct?: Prisma.AntecipacaoScalarFieldEnum | Prisma.AntecipacaoScalarFieldEnum[]
}

/**
 * Plantao without action
 */
export type PlantaoDefaultArgs<ExtArgs extends runtime.Types.Extensions.InternalArgs = runtime.Types.Extensions.DefaultArgs> = {
  /**
   * Select specific fields to fetch from the Plantao
   */
  select?: Prisma.PlantaoSelect<ExtArgs> | null
  /**
   * Omit specific fields from the Plantao
   */
  omit?: Prisma.PlantaoOmit<ExtArgs> | null
  /**
   * Choose, which related nodes to fetch as well
   */
  include?: Prisma.PlantaoInclude<ExtArgs> | null
}
