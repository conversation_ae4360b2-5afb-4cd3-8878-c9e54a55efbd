import { useNavigate } from "@tanstack/react-router";
import { createFileRoute } from "@tanstack/react-router";
import { useMutation, useQuery, useQueryClient } from "@tanstack/react-query";
import { requireAdminRole } from "@/lib/route-guards";
import { api, type Cliente, type Profissional, type LocalAtendimento } from "@/lib/api";
import { Button } from "@/components/ui/button";
import { ArrowLeft } from "lucide-react";
import { toast } from "sonner";
import { Link } from "@tanstack/react-router";
import { useEffect } from "react";
import { createLocalDate, parseISO } from "@/lib/utils";
import { usePlantaoStore } from "@/stores/plantao.store";
import { PlantaoForm } from "@/components/plantao/plantao-form";

function PlantaoNovo() {
  const navigate = useNavigate();
  const queryClient = useQueryClient();
  const { form, resetForm } = usePlantaoStore();

  // Resetar formulário ao montar o componente
  useEffect(() => {
    resetForm();
  }, [resetForm]);

  const { data: clientes } = useQuery({
    queryKey: ["clientes-select"],
    queryFn: () => api.get<{ data: Cliente[] }>("/clientes", { limit: 100, ativo: true }),
  });

  const { data: locais } = useQuery({
    queryKey: ["locais-select", form.clienteId],
    queryFn: () =>
      api.get<{ data: LocalAtendimento[] }>("/locais-atendimento", {
        limit: 100,
        ativo: true,
        ...(form.clienteId && { clienteUuid: form.clienteId }),
      }),
    enabled: true, // Sempre buscar, mas filtrar no backend quando tem cliente selecionado
  });

  const { data: profissionais } = useQuery({
    queryKey: ["profissionais-filtrados", form.clienteId, form.localAtendimentoId],
    queryFn: () =>
      api.get<Profissional[]>("/profissionais/por-cliente-local", {
        clienteUuid: form.clienteId,
        localUuid: form.localAtendimentoId || undefined,
      }),
    enabled: !!form.clienteId, // Só buscar quando tiver cliente selecionado
  });

  const handleSubmit = async () => {
    const diasSelecionados = form.diasPlantao.filter((d) => d.selecionado);
    if (diasSelecionados.length === 0) {
      toast.error("Selecione pelo menos um dia para o plantão");
      return;
    }

    mutation.mutate();
  };

  const mutation = useMutation({
    mutationFn: async () => {
      const { diasPlantao, diasPorMes, tipoTurno, ...plantaoData } = form;

      // Coletar todos os dias selecionados de todos os meses
      let todosDiasSelecionados: any[] = [];

      // Sempre iterar por todos os meses no período - extrair componentes diretamente para evitar timezone issues
      const dataInicialStr = typeof form.dataInicial === "string" ? form.dataInicial : form.dataInicial.toISOString();
      const dataFinalStr = form.dataFinal
        ? typeof form.dataFinal === "string"
          ? form.dataFinal
          : form.dataFinal.toISOString()
        : dataInicialStr;

      // Extrair componentes diretamente da string ISO para evitar timezone conversion
      const dataInicialMatch = dataInicialStr.match(/^(\d{4})-(\d{2})-(\d{2})/);
      const dataFinalMatch = dataFinalStr.match(/^(\d{4})-(\d{2})-(\d{2})/);

      if (!dataInicialMatch || !dataFinalMatch) {
        throw new Error("Formato de data inválido");
      }

      const [, initialYear, initialMonth, initialDay] = dataInicialMatch;
      const [, finalYear, finalMonth, finalDay] = dataFinalMatch;

      const dataInicial = createLocalDate(parseInt(initialYear), parseInt(initialMonth), parseInt(initialDay));
      const dataFinal = createLocalDate(parseInt(finalYear), parseInt(finalMonth), parseInt(finalDay));

      // Criar uma data para o primeiro dia de cada mês
      let currentDate = createLocalDate(dataInicial.getFullYear(), dataInicial.getMonth() + 1, 1);
      const endDate = createLocalDate(dataFinal.getFullYear(), dataFinal.getMonth() + 1, 1);

      while (currentDate <= endDate) {
        const mesAtual = currentDate.getMonth() + 1;
        const anoAtual = currentDate.getFullYear();
        const key = `${anoAtual}-${mesAtual}`;

        // Pegar os dias deste mês - primeiro verificar diasPorMes, depois diasPlantao se for o mês atual
        let diasDoMes = diasPorMes?.[key];

        // Se não tem em diasPorMes e é o mês atual, usar diasPlantao
        if (!diasDoMes && mesAtual === form.mes && anoAtual === form.ano) {
          diasDoMes = diasPlantao;
        }

        // Se ainda não tem dias, pular este mês
        if (diasDoMes && diasDoMes.length > 0) {
          // Adicionar apenas os dias selecionados
          diasDoMes
            .filter((d) => d.selecionado)
            .forEach((dia) => {
              todosDiasSelecionados.push({
                data: dia.data,
                horaEntrada: dia.horario?.inicio || form.horaInicio || null,
                horaSaida: dia.horario?.fim || form.horaFim || null,
                intervalo: dia.horario?.intervalo || "01:00",
              });
            });
        }

        // Próximo mês
        currentDate.setMonth(currentDate.getMonth() + 1);
      }

      // Se não conseguiu coletar nenhum dia, usar fallback
      if (todosDiasSelecionados.length === 0) {
        // Fallback: usar apenas o mês atual (comportamento anterior)
        todosDiasSelecionados = diasPlantao
          .filter((d) => d.selecionado)
          .map((dia) => ({
            data: dia.data,
            horaEntrada: dia.horario?.inicio || form.horaInicio || null,
            horaSaida: dia.horario?.fim || form.horaFim || null,
            intervalo: dia.horario?.intervalo || "01:00",
          }));
      }

      // Get first selected day's schedule to use as default
      const firstSelectedDia = todosDiasSelecionados[0];
      const defaultIntervalo = firstSelectedDia?.intervalo || "01:00";

      // Calculate total value based on payment type
      let valorTotal = 0;
      const totalDiasSelecionados = todosDiasSelecionados.length;

      // if (form.modalidadeTrabalho === "MENSALISTA" && form.valorMensal) {
      //   valorTotal = form.valorMensal;
      // } else if (form.modalidadeTrabalho === "PLANTONISTA" && form.valorPlantao) {
      //   valorTotal = form.valorPlantao * totalDiasSelecionados;
      // } else  if (form.valorHora && form.horaInicio && form.horaFim) {
      //   const [horaIni, minIni] = form.horaInicio.split(":").map(Number);
      //   const [horaFim, minFim] = form.horaFim.split(":").map(Number);
      //   const horasTrabalhadas = horaFim + minFim / 60 - (horaIni + minIni / 60);
      //   valorTotal = form.valorHora * horasTrabalhadas * totalDiasSelecionados;
      // }
      if (form.valorBase && form.horaInicio && form.horaFim) {
        const [horaIni, minIni] = form.horaInicio.split(":").map(Number);
        const [horaFim, minFim] = form.horaFim.split(":").map(Number);
        const horasTrabalhadas = horaFim + minFim / 60 - (horaIni + minIni / 60);
        valorTotal = form.valorBase * horasTrabalhadas * totalDiasSelecionados;
      }

      // Criar payload sem campos undefined
      const plantaoPayload: any = {
        // Use UUIDs for foreign keys
        clienteUuid: form.clienteId,
        localAtendimentoUuid: form.localAtendimentoId,
        profissionalUuid: form.profissionalId,
        mes: form.mes,
        ano: form.ano,
        modalidadeTrabalho: form.modalidadeTrabalho,
        tipoFechamento: form.tipoFechamento,
        dataInicial: form.dataInicial,
        valorTotal,
        intervalo: defaultIntervalo,
        tipoTurno, // Add tipoTurno back as it's stored in the database
        diasPlantao: todosDiasSelecionados,
      };

      // Adicionar campos opcionais apenas se têm valor
      if (form.dataFinal) plantaoPayload.dataFinal = form.dataFinal;
      if (form.valorBase) plantaoPayload.valorBase = form.valorBase;
      if (form.tipoValor) plantaoPayload.tipoValor = form.tipoValor;
      if (form.prazoPagamentoDias) plantaoPayload.prazoPagamentoDias = form.prazoPagamentoDias;
      if (form.horaInicio) plantaoPayload.horaInicio = form.horaInicio;
      if (form.horaFim) plantaoPayload.horaFim = form.horaFim;
      if (form.observacoes) plantaoPayload.observacoes = form.observacoes;

      return api.post("/plantoes", plantaoPayload);
    },
    onSuccess: () => {
      toast.success("Plantão criado com sucesso!");
      resetForm();
      queryClient.invalidateQueries({ queryKey: ["plantoes"] });
      navigate({ to: "/plantoes" });
    },
    onError: (error: any) => {
      toast.error(error.message || "Erro ao criar plantão");
    },
  });

  return (
    <div className="space-y-6">
      <div className="flex items-center gap-4">
        <Link to="/plantoes">
          <Button variant="ghost" size="icon">
            <ArrowLeft className="h-4 w-4" />
          </Button>
        </Link>
        <div>
          <h1 className="text-3xl font-bold">Novo Plantão</h1>
          <p className="text-muted-foreground">Configure um novo plantão de trabalho</p>
        </div>
      </div>

      <PlantaoForm
        mode="create"
        clientes={clientes}
        profissionais={profissionais}
        locais={locais}
        onSubmit={handleSubmit}
        isSubmitting={mutation.isPending}
        onCancel={() => navigate({ to: "/plantoes" })}
      />
    </div>
  );
}

export const Route = createFileRoute("/plantoes/novo")({
  component: PlantaoNovo,
  beforeLoad: async () => {
    await requireAdminRole();
  },
});
