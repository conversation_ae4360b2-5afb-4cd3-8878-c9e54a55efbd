import { useState } from "react";
import { Input } from "@/components/ui/input";
import { PhoneInput } from "@/components/ui/phone-input";
import { Label } from "@/components/ui/label";
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from "@/components/ui/card";
import { Button } from "@/components/ui/button";
import { Select, SelectContent, SelectItem, SelectTrigger, SelectValue } from "@/components/ui/select";
import { formatCEP } from "@/lib/formatters";
import { buscarCEP } from "@/lib/address-service";
import { ESTADOS_BRASILEIROS } from "@/lib/constants";
import { Plus, Trash2, MapPin, Edit2, Check, X } from "lucide-react";
import { toast } from "sonner";
import { EspecialidadeSelect } from "@/components/especialidade-select";

interface LocalAtendimento {
  id?: string;
  nome: string;
  endereco?: string;
  cidade?: string;
  estado?: string;
  cep?: string;
  telefone?: string;
  responsavel?: string;
  observacoes?: string;
  latitude?: number;
  longitude?: number;
  especialidadeIds?: number[];
}

interface LocaisAtendimentoStepProps {
  locais: LocalAtendimento[];
  onLocaisChange: (locais: LocalAtendimento[]) => void;
}

export function LocaisAtendimentoStep({ locais, onLocaisChange }: LocaisAtendimentoStepProps) {
  const [novoLocal, setNovoLocal] = useState<LocalAtendimento>({
    nome: "",
    endereco: "",
    cidade: "",
    estado: "",
    cep: "",
    telefone: "",
    responsavel: "",
    observacoes: "",
    latitude: undefined,
    longitude: undefined,
    especialidadeIds: [],
  });
  const [editingIndex, setEditingIndex] = useState<number | null>(null);
  const [editingLocal, setEditingLocal] = useState<LocalAtendimento | null>(null);

  const handleCepSearch = async (cep: string, isEditing = false) => {
    const data = await buscarCEP(cep);
    if (data) {
      const enderecoCompleto = `${data.logradouro}, ${data.bairro}`;
      if (isEditing && editingLocal) {
        setEditingLocal({
          ...editingLocal,
          endereco: enderecoCompleto,
          cidade: data.localidade,
          estado: data.uf,
        });
      } else {
        setNovoLocal({
          ...novoLocal,
          endereco: enderecoCompleto,
          cidade: data.localidade,
          estado: data.uf,
        });
      }
    }
  };

  const handleAddLocal = () => {
    if (!novoLocal.nome) {
      toast.error("Nome do local é obrigatório");
      return;
    }

    // Verificar se já existe um local com o mesmo nome
    const nomeNormalizado = novoLocal.nome.trim().toLowerCase();
    const localDuplicado = locais.find((local) => local.nome.trim().toLowerCase() === nomeNormalizado);

    if (localDuplicado) {
      toast.error(`Já existe um local cadastrado com o nome "${novoLocal.nome}"`);
      return;
    }

    // Verificar se já existe um local com o mesmo endereço completo
    if (novoLocal.endereco && novoLocal.cidade) {
      const enderecoNormalizado = novoLocal.endereco.trim().toLowerCase();
      const cidadeNormalizada = novoLocal.cidade.trim().toLowerCase();
      const localComMesmoEndereco = locais.find((local) => {
        return (
          local.endereco?.toLowerCase() === enderecoNormalizado && local.cidade?.toLowerCase() === cidadeNormalizada
        );
      });

      if (localComMesmoEndereco) {
        toast.error(`Já existe um local cadastrado neste endereço: ${localComMesmoEndereco.nome}`);
        return;
      }
    }

    const newLocal = { ...novoLocal, id: `temp-${Date.now()}` };
    onLocaisChange([...locais, newLocal]);

    setNovoLocal({
      nome: "",
      endereco: "",
      cidade: "",
      estado: "",
      cep: "",
      telefone: "",
      responsavel: "",
      observacoes: "",
      latitude: undefined,
      longitude: undefined,
      especialidadeIds: [],
    });

    toast.success("Local adicionado com sucesso");
  };

  const handleRemoveLocal = (index: number) => {
    const updatedLocais = locais.filter((_, i) => i !== index);
    onLocaisChange(updatedLocais);
    toast.success("Local removido com sucesso");
  };

  const handleEditLocal = (index: number) => {
    setEditingIndex(index);
    setEditingLocal({ ...locais[index] });
  };

  const handleSaveEdit = () => {
    if (editingIndex !== null && editingLocal) {
      if (!editingLocal.nome) {
        toast.error("Nome do local é obrigatório");
        return;
      }

      // Verificar se já existe outro local com o mesmo nome (excluindo o local sendo editado)
      const nomeNormalizado = editingLocal.nome.trim().toLowerCase();
      const localDuplicado = locais.find(
        (local, index) => index !== editingIndex && local.nome.trim().toLowerCase() === nomeNormalizado
      );

      if (localDuplicado) {
        toast.error(`Já existe um local cadastrado com o nome "${editingLocal.nome}"`);
        return;
      }

      // Verificar se já existe outro local com o mesmo endereço (excluindo o local sendo editado)
      if (editingLocal.endereco && editingLocal.cidade) {
        const enderecoNormalizado = editingLocal.endereco.trim().toLowerCase();
        const cidadeNormalizada = editingLocal.cidade.trim().toLowerCase();
        const localComMesmoEndereco = locais.find((local, index) => {
          if (index === editingIndex) return false;
          return (
            local.endereco?.toLowerCase() === enderecoNormalizado && local.cidade?.toLowerCase() === cidadeNormalizada
          );
        });

        if (localComMesmoEndereco) {
          toast.error(`Já existe um local cadastrado neste endereço: ${localComMesmoEndereco.nome}`);
          return;
        }
      }

      const updatedLocais = [...locais];
      updatedLocais[editingIndex] = editingLocal;
      onLocaisChange(updatedLocais);
      setEditingIndex(null);
      setEditingLocal(null);
      toast.success("Local atualizado com sucesso");
    }
  };

  const handleCancelEdit = () => {
    setEditingIndex(null);
    setEditingLocal(null);
  };

  return (
    <div className="space-y-6">
      <Card>
        <CardHeader>
          <CardTitle>Adicionar Local de Atendimento</CardTitle>
          <CardDescription>Preencha os dados do local e clique em adicionar</CardDescription>
        </CardHeader>
        <CardContent>
          <div className="space-y-4">
            <div className="grid gap-4 md:grid-cols-2">
              <div className="space-y-2">
                <Label htmlFor="nome">Nome do Local *</Label>
                <Input
                  id="nome"
                  value={novoLocal.nome}
                  onChange={(e) => setNovoLocal({ ...novoLocal, nome: e.target.value })}
                  placeholder="Ex: Hospital Central"
                />
              </div>

              <div className="space-y-2">
                <Label htmlFor="cep">CEP</Label>
                <Input
                  id="cep"
                  value={formatCEP(novoLocal.cep || "")}
                  onBlur={(e) => handleCepSearch(e.target.value)}
                  onChange={(e) => setNovoLocal({ ...novoLocal, cep: e.target.value.replace(/\D/g, "") })}
                  placeholder="00000-000"
                  maxLength={9}
                />
              </div>

              <div className="space-y-2">
                <Label htmlFor="endereco">Endereço *</Label>
                <Input
                  id="endereco"
                  value={novoLocal.endereco || ""}
                  onChange={(e) => setNovoLocal({ ...novoLocal, endereco: e.target.value })}
                  placeholder="Rua, número, bairro"
                />
              </div>

              <div className="space-y-2">
                <Label htmlFor="cidade">Cidade</Label>
                <Input
                  id="cidade"
                  value={novoLocal.cidade || ""}
                  onChange={(e) => setNovoLocal({ ...novoLocal, cidade: e.target.value })}
                  placeholder="Nome da cidade"
                />
              </div>

              <div className="space-y-2">
                <Label htmlFor="estado">Estado</Label>
                <Select
                  value={novoLocal.estado || ""}
                  onValueChange={(value) => setNovoLocal({ ...novoLocal, estado: value })}
                >
                  <SelectTrigger id="estado">
                    <SelectValue placeholder="Selecione o estado" />
                  </SelectTrigger>
                  <SelectContent>
                    {ESTADOS_BRASILEIROS.map((estado) => (
                      <SelectItem key={estado.value} value={estado.value}>
                        {estado.value}
                      </SelectItem>
                    ))}
                  </SelectContent>
                </Select>
              </div>

              <div className="space-y-2">
                <PhoneInput
                  id="telefone"
                  label="Telefone"
                  value={novoLocal.telefone || ""}
                  onChange={(value) => setNovoLocal({ ...novoLocal, telefone: value })}
                />
              </div>

              <div className="space-y-2">
                <Label htmlFor="responsavel">Responsável</Label>
                <Input
                  id="responsavel"
                  value={novoLocal.responsavel || ""}
                  onChange={(e) => setNovoLocal({ ...novoLocal, responsavel: e.target.value })}
                  placeholder="Nome do responsável"
                />
              </div>

              <div className="space-y-2">
                <Label>Coordenadas (Opcional)</Label>
                <div className="grid grid-cols-2 gap-2">
                  <Input
                    type="number"
                    step="any"
                    value={novoLocal.latitude || ""}
                    onChange={(e) =>
                      setNovoLocal({ ...novoLocal, latitude: e.target.value ? Number(e.target.value) : undefined })
                    }
                    placeholder="Latitude"
                  />
                  <Input
                    type="number"
                    step="any"
                    value={novoLocal.longitude || ""}
                    onChange={(e) =>
                      setNovoLocal({ ...novoLocal, longitude: e.target.value ? Number(e.target.value) : undefined })
                    }
                    placeholder="Longitude"
                  />
                </div>
              </div>
            </div>

            <div className="space-y-2">
              <Label htmlFor="especialidades">Especialidades Permitidas</Label>
              <EspecialidadeSelect
                value={novoLocal.especialidadeIds || []}
                onChange={(value) => setNovoLocal({ ...novoLocal, especialidadeIds: value })}
                placeholder="Selecione as especialidades permitidas neste local"
                searchPlaceholder="Buscar especialidade..."
                emptyText="Nenhuma especialidade encontrada"
                addNewText="Adicionar nova especialidade"
              />
              <p className="text-sm text-muted-foreground">
                Apenas plantões com essas especialidades poderão ser criados neste local
              </p>
            </div>

            <div className="space-y-2">
              <Label htmlFor="observacoes">Observações</Label>
              <textarea
                id="observacoes"
                value={novoLocal.observacoes || ""}
                onChange={(e) => setNovoLocal({ ...novoLocal, observacoes: e.target.value })}
                className="min-h-[100px] w-full rounded-md border border-input bg-background px-3 py-2 text-sm ring-offset-background placeholder:text-muted-foreground focus-visible:outline-none focus-visible:ring-2 focus-visible:ring-ring focus-visible:ring-offset-2 disabled:cursor-not-allowed disabled:opacity-50"
                placeholder="Informações adicionais sobre o local"
              />
            </div>

            <div className="flex justify-end">
              <Button type="button" onClick={handleAddLocal}>
                <Plus className="mr-2 h-4 w-4" />
                Adicionar Local
              </Button>
            </div>
          </div>
        </CardContent>
      </Card>

      {locais.length > 0 && (
        <Card>
          <CardHeader>
            <CardTitle>Locais de Atendimento</CardTitle>
            <CardDescription>
              {locais.length} {locais.length === 1 ? "local cadastrado" : "locais cadastrados"}
            </CardDescription>
          </CardHeader>
          <CardContent>
            <div className="space-y-4">
              {locais.map((local, index) => (
                <Card key={local.id || index} className="relative">
                  <CardContent className="pt-6">
                    {editingIndex === index ? (
                      <div className="space-y-4">
                        <div className="grid gap-4 md:grid-cols-2">
                          <div className="space-y-2">
                            <Label>Nome do Local *</Label>
                            <Input
                              value={editingLocal?.nome || ""}
                              onChange={(e) => setEditingLocal({ ...editingLocal!, nome: e.target.value })}
                            />
                          </div>
                          <div className="space-y-2">
                            <Label>CEP</Label>
                            <Input
                              value={formatCEP(editingLocal?.cep || "")}
                              onBlur={(e) => handleCepSearch(e.target.value, true)}
                              onChange={(e) =>
                                setEditingLocal({ ...editingLocal!, cep: e.target.value.replace(/\D/g, "") })
                              }
                              maxLength={9}
                            />
                          </div>
                          <div className="space-y-2">
                            <Label>Endereço</Label>
                            <Input
                              value={editingLocal?.endereco || ""}
                              onChange={(e) => setEditingLocal({ ...editingLocal!, endereco: e.target.value })}
                            />
                          </div>
                          <div className="space-y-2">
                            <Label>Cidade</Label>
                            <Input
                              value={editingLocal?.cidade || ""}
                              onChange={(e) => setEditingLocal({ ...editingLocal!, cidade: e.target.value })}
                            />
                          </div>
                          <div className="space-y-2">
                            <Label>Estado</Label>
                            <Select
                              value={editingLocal?.estado || ""}
                              onValueChange={(value) => setEditingLocal({ ...editingLocal!, estado: value })}
                            >
                              <SelectTrigger>
                                <SelectValue placeholder="Selecione o estado" />
                              </SelectTrigger>
                              <SelectContent>
                                {ESTADOS_BRASILEIROS.map((estado) => (
                                  <SelectItem key={estado.value} value={estado.value}>
                                    {estado.value}
                                  </SelectItem>
                                ))}
                              </SelectContent>
                            </Select>
                          </div>
                          <div className="space-y-2">
                            <PhoneInput
                              label="Telefone"
                              value={editingLocal?.telefone || ""}
                              onChange={(value) => setEditingLocal({ ...editingLocal!, telefone: value })}
                            />
                          </div>
                          <div className="space-y-2">
                            <Label>Responsável</Label>
                            <Input
                              value={editingLocal?.responsavel || ""}
                              onChange={(e) => setEditingLocal({ ...editingLocal!, responsavel: e.target.value })}
                            />
                          </div>
                        </div>
                        <div className="space-y-2">
                          <Label>Especialidades Permitidas</Label>
                          <EspecialidadeSelect
                            value={editingLocal?.especialidadeIds || []}
                            onChange={(value) => setEditingLocal({ ...editingLocal!, especialidadeIds: value })}
                            placeholder="Selecione as especialidades permitidas neste local"
                            searchPlaceholder="Buscar especialidade..."
                            emptyText="Nenhuma especialidade encontrada"
                            addNewText="Adicionar nova especialidade"
                          />
                          <p className="text-sm text-muted-foreground">
                            Apenas plantões com essas especialidades poderão ser criados neste local
                          </p>
                        </div>
                        <div className="space-y-2">
                          <Label>Observações</Label>
                          <textarea
                            value={editingLocal?.observacoes || ""}
                            onChange={(e) => setEditingLocal({ ...editingLocal!, observacoes: e.target.value })}
                            className="min-h-[80px] w-full rounded-md border border-input bg-background px-3 py-2 text-sm ring-offset-background placeholder:text-muted-foreground focus-visible:outline-none focus-visible:ring-2 focus-visible:ring-ring focus-visible:ring-offset-2"
                          />
                        </div>

                        <div className="flex justify-end gap-2">
                          <Button type="button" variant="outline" size="sm" onClick={handleCancelEdit}>
                            <X className="mr-2 h-4 w-4" />
                            Cancelar
                          </Button>
                          <Button type="button" size="sm" onClick={handleSaveEdit}>
                            <Check className="mr-2 h-4 w-4" />
                            Salvar
                          </Button>
                        </div>
                      </div>
                    ) : (
                      <div className="flex items-start justify-between">
                        <div className="space-y-1">
                          <div className="flex items-center gap-2">
                            <MapPin className="h-4 w-4 text-muted-foreground" />
                            <h4 className="font-semibold">{local.nome}</h4>
                          </div>
                          {local.responsavel && (
                            <p className="text-sm text-muted-foreground">Responsável: {local.responsavel}</p>
                          )}
                          {(local.endereco || local.cidade) && (
                            <p className="text-sm text-muted-foreground">
                              {local.endereco && `${local.endereco}`}
                              {local.cidade && ` - ${local.cidade}`}
                              {local.estado && `/${local.estado}`}
                              {local.cep && ` - CEP: ${formatCEP(local.cep)}`}
                            </p>
                          )}
                          {local.telefone && (
                            <p className="text-sm text-muted-foreground">Telefone: {local.telefone}</p>
                          )}
                          {local.observacoes && (
                            <p className="text-sm text-muted-foreground">Observações: {local.observacoes}</p>
                          )}
                        </div>
                        <div className="flex gap-2">
                          <Button type="button" variant="ghost" size="icon" onClick={() => handleEditLocal(index)}>
                            <Edit2 className="h-4 w-4" />
                          </Button>
                          <Button type="button" variant="ghost" size="icon" onClick={() => handleRemoveLocal(index)}>
                            <Trash2 className="h-4 w-4 text-destructive" />
                          </Button>
                        </div>
                      </div>
                    )}
                  </CardContent>
                </Card>
              ))}
            </div>
          </CardContent>
        </Card>
      )}
    </div>
  );
}
