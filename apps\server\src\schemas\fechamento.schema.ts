import { z } from "zod";

export const createFechamentoSchema = z.object({
  plantaoId: z.uuid().optional(),
  plantaoUuid: z.string().uuid("UUID do plantão inválido").optional(),
  profissionalId: z.number().optional(),
  profissionalUuid: z.string().uuid("UUID do profissional inválido").optional(),
  // mes e ano são opcionais - serão derivados do plantao.dataInicial
  mes: z.number().min(1).max(12).optional(),
  ano: z.number().min(2020).max(2030).optional(),
  status: z.enum(["PENDENTE", "APROVADO", "REJEITADO"]).default("PENDENTE"),
  totalHoras: z.number().min(0).optional(),
  totalValor: z.number().positive(),
  diasTrabalhados: z.number().min(0),
  diasPrevistos: z.number().min(0),
  aprovadoEm: z.string().datetime().optional(),
  rejeitadoPor: z.string().optional(),
  rejeitadoEm: z.string().datetime().optional(),
  motivoRejeicao: z.string().optional(),
  observacoes: z.string().optional(),
});

export const updateFechamentoSchema = createFechamentoSchema.partial();

export const fechamentoQuerySchema = z.object({
  page: z.string().optional(),
  limit: z.string().optional(),
  mes: z.string().optional(),
  ano: z.string().optional(),
  status: z.enum(["PENDENTE", "APROVADO", "REJEITADO"]).optional(),
  plantaoUuid: z.uuid().optional(),
  profissionalId: z.union([z.number(), z.array(z.number())]).optional(),
  clienteId: z.number().optional(),
  dataInicial: z.string().optional(),
  dataFinal: z.string().optional(),
  semAntecipacao: z.union([z.boolean(), z.string()]).optional(),
  search: z.string().optional(),
});

export type CreateFechamentoInput = z.infer<typeof createFechamentoSchema>;
export type UpdateFechamentoInput = z.infer<typeof updateFechamentoSchema>;
export type FechamentoQuery = z.infer<typeof fechamentoQuerySchema>;
