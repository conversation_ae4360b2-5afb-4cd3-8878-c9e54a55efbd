import { useN<PERSON><PERSON>, <PERSON> } from "@tanstack/react-router";
import { createFileRoute } from "@tanstack/react-router";
import { useQuery, useMutation } from "@tanstack/react-query";
import { requireAdminRole } from "@/lib/route-guards";
import { api, type Fechamento } from "@/lib/api";
import { Button } from "@/components/ui/button";
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from "@/components/ui/card";
import { Badge } from "@/components/ui/badge";
import { Skeleton } from "@/components/ui/skeleton";
import { Textarea } from "@/components/ui/textarea";
import { Label } from "@/components/ui/label";
import {
  ArrowLeft,
  Check,
  X,
  Clock,
  CheckCircle2,
  Calendar,
  DollarSign,
  Building2,
  MapPin,
  User,
  FileText,
  Edit,
  Loader2,
} from "lucide-react";
import { toast } from "sonner";
import { useState } from "react";
import { createLocalDate, formatDate, getDaysInMonthForDate } from "@shared/date";

const meses = [
  "Janeiro",
  "Fevereiro",
  "Março",
  "<PERSON>bri<PERSON>",
  "Mai<PERSON>",
  "Jun<PERSON>",
  "Julho",
  "Agosto",
  "Setembro",
  "Outubro",
  "Novembro",
  "Dezembro",
];

const diasSemana = ["Dom", "Seg", "Ter", "Qua", "Qui", "Sex", "Sáb"];

function FechamentoDetalhes() {
  const { uuid } = Route.useParams();
  const navigate = useNavigate();
  const [observacoes, setObservacoes] = useState("");
  const [isEditing, setIsEditing] = useState(false);

  const {
    data: fechamento,
    isLoading,
    refetch,
  } = useQuery({
    queryKey: ["fechamento", uuid],
    queryFn: () => api.get<Fechamento>(`/fechamentos/${uuid}`),
  });

  const aprovarMutation = useMutation({
    mutationFn: async () => {
      return api.patch(`/fechamentos/${uuid}`, {
        status: "APROVADO",
        observacoes: observacoes || fechamento?.observacoes,
      });
    },
    onSuccess: () => {
      toast.success("Fechamento aprovado com sucesso!");
      refetch();
      setIsEditing(false);
    },
    onError: (error: any) => {
      toast.error(error.message || "Erro ao aprovar fechamento");
    },
  });

  const rejeitarMutation = useMutation({
    mutationFn: async () => {
      if (!observacoes && fechamento?.status === "PENDENTE") {
        toast.error("Informe o motivo da rejeição");
        return;
      }
      return api.patch(`/fechamentos/${uuid}`, {
        status: "REJEITADO",
        observacoes,
      });
    },
    onSuccess: () => {
      toast.success("Fechamento rejeitado!");
      refetch();
      setIsEditing(false);
    },
    onError: (error: any) => {
      toast.error(error.message || "Erro ao rejeitar fechamento");
    },
  });

  const formatCurrency = (value: number) => {
    return new Intl.NumberFormat("pt-BR", {
      style: "currency",
      currency: "BRL",
    }).format(value);
  };

  const getStatusBadge = (status: string) => {
    switch (status) {
      case "PENDENTE":
        return (
          <Badge variant="outline">
            <Clock className="h-3 w-3 mr-1" />
            Pendente
          </Badge>
        );
      case "APROVADO":
        return (
          <Badge variant="default">
            <CheckCircle2 className="h-3 w-3 mr-1" />
            Aprovado
          </Badge>
        );
      case "REJEITADO":
        return (
          <Badge variant="destructive">
            <X className="h-3 w-3 mr-1" />
            Rejeitado
          </Badge>
        );
      default:
        return <Badge variant="secondary">{status}</Badge>;
    }
  };

  const renderCalendar = () => {
    if (!fechamento) return null;

    const mes = fechamento.mes || 1;
    const ano = fechamento.ano || new Date().getFullYear();
    const daysInMonth = getDaysInMonthForDate(ano, mes);
    const firstDay = createLocalDate(ano, mes, 1).getDay();
    const days = [];

    const diasEscalados =
      fechamento.plantao?.diasPlantao?.filter((d) => d.data).map((d) => new Date(String(d.data)).getDate()) || [];
    const diasTrabalhados = Array.isArray(fechamento.diasConfirmados) ? fechamento.diasConfirmados : diasEscalados;

    // Days of week headers
    const headers = diasSemana.map((dia, index) => (
      <div key={`header-${index}`} className="text-center text-xs font-semibold p-1">
        {dia}
      </div>
    ));

    // Empty cells for days before month starts
    for (let i = 0; i < firstDay; i++) {
      days.push(<div key={`empty-${i}`} className="p-1"></div>);
    }

    // Days of the month
    for (let day = 1; day <= daysInMonth; day++) {
      const isScheduled = diasEscalados.includes(day);
      const isWorked = diasTrabalhados.includes(day);
      const date = createLocalDate(ano || new Date().getFullYear(), mes || 1, day);
      const isWeekend = date.getDay() === 0 || date.getDay() === 6;

      days.push(
        <div
          key={day}
          className={`
            h-8 flex items-center justify-center rounded text-sm
            ${
              isWorked
                ? "bg-green-500/20 text-green-700 font-semibold"
                : isScheduled
                  ? "bg-yellow-500/20 text-yellow-700"
                  : "text-muted-foreground"
            }
            ${isWeekend ? "opacity-75" : ""}
          `}
        >
          {day}
        </div>
      );
    }

    return (
      <div>
        <div className="grid grid-cols-7 gap-1 mb-2">{headers}</div>
        <div className="grid grid-cols-7 gap-1">{days}</div>
      </div>
    );
  };

  if (isLoading) {
    return (
      <div className="space-y-6">
        <Skeleton className="h-10 w-64" />
        <div className="grid gap-6 lg:grid-cols-2">
          <Skeleton className="h-96" />
          <Skeleton className="h-96" />
        </div>
      </div>
    );
  }

  if (!fechamento) {
    return (
      <div className="text-center py-12">
        <p className="text-muted-foreground">Fechamento não encontrado</p>
      </div>
    );
  }

  return (
    <div className="space-y-6">
      <div className="flex items-center justify-between">
        <div className="flex items-center gap-4">
          <Link to="/fechamentos">
            <Button variant="ghost" size="icon">
              <ArrowLeft className="h-4 w-4" />
            </Button>
          </Link>
          <div>
            <h1 className="text-3xl font-bold">Detalhes do Fechamento</h1>
            <p className="text-muted-foreground">
              {fechamento.mes ? meses[fechamento.mes - 1] : ""} {fechamento.ano}
            </p>
          </div>
        </div>
        {fechamento.status === "PENDENTE" && (
          <div className="flex gap-2">
            <Button variant="default" onClick={() => aprovarMutation.mutate()} disabled={aprovarMutation.isPending}>
              {aprovarMutation.isPending ? (
                <Loader2 className="mr-2 h-4 w-4 animate-spin" />
              ) : (
                <Check className="mr-2 h-4 w-4" />
              )}
              Aprovar
            </Button>
            {/* <Button variant="destructive" onClick={() => setIsEditing(true)} disabled={rejeitarMutation.isPending}>
              <X className="mr-2 h-4 w-4" />
              Rejeitar
            </Button> */}
          </div>
        )}
      </div>

      <div className="grid gap-6 lg:grid-cols-2">
        <div className="space-y-6">
          <Card>
            <CardHeader>
              <CardTitle>Informações Gerais</CardTitle>
              <CardDescription>Detalhes do fechamento e da escala</CardDescription>
            </CardHeader>
            <CardContent className="space-y-4">
              <div className="flex justify-between items-center">
                <span className="text-sm text-muted-foreground">Status:</span>
                {getStatusBadge(fechamento.status)}
              </div>

              <div className="space-y-3">
                <div className="flex items-center gap-2">
                  <User className="h-4 w-4 text-muted-foreground" />
                  <div>
                    <p className="text-sm text-muted-foreground">Profissional</p>
                    <p className="font-medium">{fechamento.plantao?.profissional?.usuario?.nome}</p>
                  </div>
                </div>

                <div className="flex items-center gap-2">
                  <Building2 className="h-4 w-4 text-muted-foreground" />
                  <div>
                    <p className="text-sm text-muted-foreground">Cliente</p>
                    <p className="font-medium">{fechamento.plantao?.cliente?.nome}</p>
                  </div>
                </div>

                <div className="flex items-center gap-2">
                  <MapPin className="h-4 w-4 text-muted-foreground" />
                  <div>
                    <p className="text-sm text-muted-foreground">Local</p>
                    <p className="font-medium">{fechamento.plantao?.localAtendimento?.nome}</p>
                  </div>
                </div>
              </div>

              <div className="border-t pt-4 space-y-3">
                <div className="flex justify-between">
                  <span className="text-sm text-muted-foreground">Modalidade de Trabalho:</span>
                  <Badge variant="outline">{fechamento.plantao?.modalidadeTrabalho}</Badge>
                </div>

                <div className="flex justify-between">
                  <span className="text-sm text-muted-foreground">Dias Escalados:</span>
                  <span className="font-medium">{fechamento.plantao?.diasPlantao?.length || 0}</span>
                </div>

                <div className="flex justify-between">
                  <span className="text-sm text-muted-foreground">Dias Trabalhados:</span>
                  <span className="font-medium text-green-600">{fechamento.diasTrabalhados}</span>
                </div>

                <div className="flex justify-between text-lg">
                  <span className="font-medium">Valor Total:</span>
                  <span className="font-bold">{formatCurrency(fechamento.totalValor)}</span>
                </div>
              </div>

              {(fechamento.observacoes || isEditing) && (
                <div className="border-t pt-4">
                  {isEditing ? (
                    <div className="space-y-2">
                      <Label htmlFor="observacoes">Motivo da Rejeição *</Label>
                      <Textarea
                        id="observacoes"
                        value={observacoes}
                        onChange={(e) => setObservacoes(e.target.value)}
                        placeholder="Informe o motivo da rejeição..."
                        rows={3}
                      />
                      <div className="flex gap-2">
                        <Button
                          size="sm"
                          variant="destructive"
                          onClick={() => rejeitarMutation.mutate()}
                          disabled={!observacoes || rejeitarMutation.isPending}
                        >
                          {rejeitarMutation.isPending ? (
                            <Loader2 className="mr-2 h-4 w-4 animate-spin" />
                          ) : (
                            <X className="mr-2 h-4 w-4" />
                          )}
                          Confirmar Rejeição
                        </Button>
                        <Button
                          size="sm"
                          variant="outline"
                          onClick={() => {
                            setIsEditing(false);
                            setObservacoes("");
                          }}
                        >
                          Cancelar
                        </Button>
                      </div>
                    </div>
                  ) : (
                    <div className="space-y-2">
                      <div className="flex items-center gap-2">
                        <FileText className="h-4 w-4 text-muted-foreground" />
                        <p className="text-sm font-medium">Observações</p>
                      </div>
                      <p className="text-sm text-muted-foreground">{fechamento.observacoes}</p>
                    </div>
                  )}
                </div>
              )}
            </CardContent>
          </Card>

          <Card>
            <CardHeader>
              <CardTitle>Informações Adicionais</CardTitle>
            </CardHeader>
            <CardContent className="space-y-3">
              <div className="flex justify-between text-sm">
                <span className="text-muted-foreground">Criado em:</span>
                <span>{formatDate(fechamento.createdAt)}</span>
              </div>
              <div className="flex justify-between text-sm">
                <span className="text-muted-foreground">Atualizado em:</span>
                <span>{formatDate(fechamento.updatedAt)}</span>
              </div>
              {fechamento.aprovadoPor && (
                <div className="flex justify-between text-sm">
                  <span className="text-muted-foreground">Aprovado por:</span>
                  <span>{fechamento.aprovadoPor}</span>
                </div>
              )}
              {fechamento.dataAprovacao && (
                <div className="flex justify-between text-sm">
                  <span className="text-muted-foreground">Data de Aprovação:</span>
                  <span>{formatDate(fechamento.dataAprovacao)}</span>
                </div>
              )}
            </CardContent>
          </Card>
        </div>

        <Card className="h-fit">
          <CardHeader>
            <CardTitle>Calendário de Trabalho</CardTitle>
            <CardDescription>
              Dias trabalhados em {fechamento.mes ? meses[fechamento.mes - 1] : ""} {fechamento.ano}
            </CardDescription>
          </CardHeader>
          <CardContent>
            {renderCalendar()}
            <div className="mt-4 flex items-center gap-4 text-xs">
              <div className="flex items-center gap-1">
                <div className="h-4 w-4 rounded bg-green-500/20 border border-green-500" />
                <span>Trabalhado</span>
              </div>
              <div className="flex items-center gap-1">
                <div className="h-4 w-4 rounded bg-yellow-500/20 border border-yellow-500" />
                <span>Escalado não trabalhado</span>
              </div>
            </div>
          </CardContent>
        </Card>
      </div>
    </div>
  );
}

export const Route = createFileRoute("/fechamentos/$uuid")({
  component: FechamentoDetalhes,
  beforeLoad: async () => {
    await requireAdminRole();
  },
});
