import { create<PERSON><PERSON><PERSON><PERSON><PERSON>, <PERSON> } from "@tanstack/react-router";
import { requireAdminRole } from "@/lib/route-guards";
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from "@/components/ui/card";
import { Button } from "@/components/ui/button";
import { Skeleton } from "@/components/ui/skeleton";
import {
  Users,
  Calendar,
  FileCheck,
  DollarSign,
  TrendingUp,
  Clock,
  Building2,
  AlertCircle,
  ArrowRight,
  Plus,
} from "lucide-react";
import { useQuery } from "@tanstack/react-query";
import { api, type DashboardStats, type PlantaoRecente, type Dashboard<PERSON>lerta } from "@/lib/api";

async function fetchDashboardStats(): Promise<DashboardStats> {
  return api.get("/dashboard/stats");
}

async function fetchPlantoesRecentes(): Promise<PlantaoRecente[]> {
  return api.get("/dashboard/plantoes-recentes");
}

async function fetchAlertas(): Promise<DashboardAlerta[]> {
  return api.get("/dashboard/alertas");
}

function Dashboard() {
  const { data: stats, isLoading } = useQuery({
    queryKey: ["dashboard-stats"],
    queryFn: fetchDashboardStats,
  });

  const { data: plantoesRecentes, isLoading: isLoadingPlantoes } = useQuery({
    queryKey: ["plantoes-recentes"],
    queryFn: fetchPlantoesRecentes,
  });

  const { data: alertas, isLoading: isLoadingAlertas } = useQuery({
    queryKey: ["dashboard-alertas"],
    queryFn: fetchAlertas,
  });

  const statsCards = [
    {
      title: "Clientes Ativos",
      value: stats?.clientes || 0,
      icon: Building2,
      color: "text-blue-500",
      bgColor: "bg-blue-500/10",
      link: "/cadastros/clientes",
    },
    {
      title: "Profissionais",
      value: stats?.profissionais || 0,
      icon: Users,
      color: "text-green-500",
      bgColor: "bg-green-500/10",
      link: "/cadastros/profissionais",
    },
    {
      title: "Plantões Ativos",
      value: stats?.plantoesAtivos || 0,
      icon: Calendar,
      color: "text-purple-500",
      bgColor: "bg-purple-500/10",
      link: "/plantoes",
    },
    {
      title: "Fechamentos Pendentes",
      value: stats?.fechamentosPendentes || 0,
      icon: FileCheck,
      color: "text-orange-500",
      bgColor: "bg-orange-500/10",
      link: "/fechamentos",
    },
    {
      title: "Antecipações Pendentes",
      value: stats?.antecipacoesCount || 0,
      icon: Clock,
      color: "text-yellow-500",
      bgColor: "bg-yellow-500/10",
      link: "/antecipacoes",
    },
    {
      title: "Valor Aprovado no Mês",
      value: new Intl.NumberFormat("pt-BR", {
        style: "currency",
        currency: "BRL",
      }).format(stats?.valorTotalAntecipacoesAprovadas || 0),
      icon: DollarSign,
      color: "text-emerald-500",
      bgColor: "bg-emerald-500/10",
      link: "/antecipacoes",
    },
  ];

  const quickActions = [
    {
      title: "Novo Plantão",
      description: "Criar um novo plantão de trabalho",
      icon: Calendar,
      link: "/plantoes/novo",
      variant: "default" as const,
    },
    {
      title: "Novo Profissional",
      description: "Cadastrar um novo profissional",
      icon: Users,
      link: "/cadastros/profissionais/novo",
      variant: "outline" as const,
    },
    {
      title: "Aprovar Fechamentos",
      description: "Revisar e aprovar fechamentos pendentes",
      icon: FileCheck,
      link: "/fechamentos?status=PENDENTE",
      variant: "outline" as const,
    },
    {
      title: "Solicitar Antecipação",
      description: "Criar nova solicitação de antecipação",
      icon: DollarSign,
      link: "/antecipacoes/nova",
      variant: "outline" as const,
    },
  ];

  return (
    <div className="space-y-6">
      {/* Header */}
      {/* <div>
        <h1 className="text-3xl font-bold">Dashboard</h1>
      </div> */}

      {/* Stats Cards */}
      <div className="grid gap-4 md:grid-cols-2 lg:grid-cols-3">
        {statsCards.map((stat) => (
          <Link key={stat.title} to={stat.link}>
            <Card className="hover:shadow-lg transition-shadow cursor-pointer">
              <CardHeader className="flex flex-row items-center justify-between space-y-0 pb-2">
                <CardTitle className="text-sm font-medium">{stat.title}</CardTitle>
                <div className={`p-2 rounded-lg ${stat.bgColor}`}>
                  <stat.icon className={`h-4 w-4 ${stat.color}`} />
                </div>
              </CardHeader>
              <CardContent>
                {isLoading ? <Skeleton className="h-8 w-24" /> : <div className="text-2xl font-bold">{stat.value}</div>}
              </CardContent>
            </Card>
          </Link>
        ))}
      </div>

      {/* Quick Actions */}
      {/* <Card>
        <CardHeader>
          <CardTitle>Ações Rápidas</CardTitle>
          <CardDescription>Acesse rapidamente as funcionalidades mais utilizadas</CardDescription>
        </CardHeader>
        <CardContent>
          <div className="grid gap-4 md:grid-cols-2">
            {quickActions.map((action) => (
              <Link key={action.title} to={action.link}>
                <Button variant={action.variant} className="w-full justify-start h-auto p-4">
                  <action.icon className="mr-3 h-5 w-5" />
                  <div className="text-left">
                    <div className="font-semibold">{action.title}</div>
                    <div className="text-sm text-muted-foreground">{action.description}</div>
                  </div>
                  <ArrowRight className="ml-auto h-4 w-4" />
                </Button>
              </Link>
            ))}
          </div>
        </CardContent>
      </Card> */}

      {/* Recent Activity */}
      {/* <div className="grid gap-4 md:grid-cols-2"> */}
      {/* <Card>
          <CardHeader>
            <CardTitle>Plantões Recentes</CardTitle>
            <CardDescription>Últimos plantões cadastrados no sistema</CardDescription>
          </CardHeader>
          <CardContent>
            <div className="space-y-4">
              {isLoadingPlantoes ? (
                <>
                  <Skeleton className="h-12 w-full" />
                  <Skeleton className="h-12 w-full" />
                  <Skeleton className="h-12 w-full" />
                </>
              ) : plantoesRecentes && plantoesRecentes.length > 0 ? (
                plantoesRecentes.map((plantao) => (
                  <Link key={plantao.id} to={`/plantoes/$uuid`} params={{ uuid: plantao.uuid }}>
                    <div className="flex items-center gap-3 p-3 rounded-lg border hover:bg-muted/50 transition-colors cursor-pointer">
                      <div className="flex-shrink-0">
                        <Calendar className="h-8 w-8 text-purple-500" />
                      </div>
                      <div className="flex-1 min-w-0">
                        <p className="text-sm font-medium truncate">{plantao.profissional?.usuario?.nome}</p>
                        <p className="text-sm text-muted-foreground truncate">
                          {plantao.cliente.nome} - {plantao.localAtendimento.nome}
                        </p>
                        <p className="text-xs text-muted-foreground">
                          {String(plantao.mes).padStart(2, "0")}/{plantao.ano} - {plantao.modalidadeTrabalho}
                        </p>
                      </div>
                      <div className="text-right">
                        <p className="text-sm font-medium">
                          {new Intl.NumberFormat("pt-BR", {
                            style: "currency",
                            currency: "BRL",
                          }).format(plantao.valorTotal)}
                        </p>
                      </div>
                    </div>
                  </Link>
                ))
              ) : (
                <div className="text-center py-8 text-muted-foreground">
                  <Calendar className="h-12 w-12 mx-auto mb-2 opacity-20" />
                  <p>Nenhum plantão recente</p>
                </div>
              )}
            </div>
          </CardContent>
        </Card> */}

      {/* <Card>
          <CardHeader>
            <CardTitle>Alertas e Notificações</CardTitle>
            <CardDescription>Avisos importantes que requerem atenção</CardDescription>
          </CardHeader>
          <CardContent>
            <div className="space-y-4">
              {isLoadingAlertas ? (
                <>
                  <Skeleton className="h-12 w-full" />
                  <Skeleton className="h-12 w-full" />
                </>
              ) : alertas && alertas.length > 0 ? (
                alertas.map((alerta, index) => (
                  <Link key={index} to={alerta.link}>
                    <div
                      className={`flex items-start gap-3 p-3 rounded-lg hover:bg-muted/50 transition-colors cursor-pointer ${
                        alerta.tipo === "warning"
                          ? "bg-yellow-500/10"
                          : alerta.tipo === "error"
                            ? "bg-red-500/10"
                            : "bg-blue-500/10"
                      }`}
                    >
                      {alerta.tipo === "warning" ? (
                        <AlertCircle className="h-5 w-5 text-yellow-500 mt-0.5" />
                      ) : alerta.tipo === "error" ? (
                        <AlertCircle className="h-5 w-5 text-red-500 mt-0.5" />
                      ) : (
                        <TrendingUp className="h-5 w-5 text-blue-500 mt-0.5" />
                      )}
                      <div className="flex-1">
                        <p className="text-sm font-medium">{alerta.titulo}</p>
                        <p className="text-sm text-muted-foreground">{alerta.descricao}</p>
                      </div>
                    </div>
                  </Link>
                ))
              ) : (
                <div className="text-center py-8 text-muted-foreground">
                  <AlertCircle className="h-12 w-12 mx-auto mb-2 opacity-20" />
                  <p>Nenhum alerta no momento</p>
                </div>
              )}
            </div>
          </CardContent>
        </Card> */}
      {/* </div> */}
    </div>
  );
}

export const Route = createFileRoute("/")({
  component: Dashboard,
  beforeLoad: async () => {
    await requireAdminRole();
  },
});
