import "dotenv/config";

import { PrismaClient } from "@prisma/client";
import { generatePass } from "@/lib/auth";
import { createHash } from "crypto";

const prisma = new PrismaClient();

async function seedTermos() {
  const conteudo = `
    <div style="font-family: -apple-system, BlinkMacSystemFont, 'Segoe UI', Roboto, 'Helvetica Neue', Arial, sans-serif; line-height: 1.6;">
      
      <h1 style="border-bottom: 2px solid #3498db; padding-bottom: 10px;">TERMOS E CONDIÇÕES GERAIS DO PRODUTO DE CRÉDITO</h1>
      
      <h2 style="margin-top: 30px;">1. INTRODUÇÃO</h2>
      
      <p><strong>1.1.</strong> Estes Termos e Condições regulam as operações de crédito estruturadas, concedidas por intermédio da Capitale Investment Holding Ltda. ("Capitale"), em parceria com clínicas, instituições de saúde e prestadores de serviços médicos ("Parceiros Institucionais"), e destinadas exclusivamente aos profissionais da área da saúde vinculados aos referidos Parceiros Institucionais, notadamente os médicos ("Tomadores" ou "Profissionais Elegíveis"). As operações são formalizadas mediante Cédulas de Crédito Bancário (CCBs) emitidas em favor da UY3 SCD S.A. (uma instituição financeira autorizada pelo Banco Central do Brasil) ("UY3"), com liquidação automatizada via conta vinculada aberta em nome do Tomador e controlada pela UY3.</p>
      
      <p>A adesão aos presentes Termos pressupõe a anuência expressa do Tomador e sua plena ciência acerca das obrigações, garantias, riscos e condições financeiras inerentes às operações de crédito aqui descritas.</p>
      
      <h2 style="margin-top: 30px;">2. OBJETO E FINALIDADE</h2>
      
      <p><strong>2.1.</strong> O produto de crédito objeto destes Termos consiste na concessão de crédito com garantia nos honorários médicos líquidos e certos, a que faz jus o Tomador em razão da prestação de serviços médicos, ambulatoriais, hospitalares ou correlatos, realizados no âmbito da operação do Parceiro Institucional, e que sejam remunerados por operadoras de planos de saúde ou entidades pagadoras ("Operadoras").</p>
      
      <p><strong>2.2.</strong> A finalidade da operação é prover liquidez imediata ao Tomador, mediante cessão fiduciária dos fluxos futuros esperados do Tomador junto ao Parceiro Institucional, sendo o crédito formalizado por meio da emissão de uma CCB emitida pelo Tomador em favor da UY3, nos moldes da legislação aplicável.</p>
      
      <h2 style="margin-top: 30px;">3. ESTRUTURA OPERACIONAL</h2>
      
      <p><strong>3.1.</strong> A elegibilidade à contratação do Produto está condicionada cumulativamente a:</p>
      <ul style="margin-left: 20px;">
        <li>Vínculo formal com o Parceiro Institucional (contrato de prestação de serviços, associação, ou sociedade médica);</li>
        <li>Existência de crédito líquido e certo a ser recebido pelo Tomador em prazo compatível com a amortização da operação;</li>
        <li>Ausência de glosas, disputas, cancelamentos ou contestações quanto aos serviços prestados;</li>
        <li>Aprovação de crédito, após análise documental, reputacional e de compliance, pela Capitale ou Instituição Parceira; e</li>
        <li>Assinatura eletrônica ou física da CCB e do Demonstrativo do Custo Efetivo Total ("CET"), em conformidade com a Resolução nº 4.558/2017 do CMN e demais normas aplicáveis.</li>
      </ul>
      
      <p><strong>3.2.</strong> A operação será formalizada por meio de CCB individual, na qual constará:</p>
      <ul style="margin-left: 20px;">
        <li>Valor do crédito disponibilizado ao Tomador;</li>
        <li>Taxa de juros nominal e efetiva;</li>
        <li>Encargos incidentes, inclusive IOF, quando aplicável;</li>
        <li>Data de vencimento vinculada à expectativa de pagamento pelo Parceiro Institucional; e</li>
        <li>Identificação do fluxo cedido em garantia.</li>
      </ul>
      
      <p>Após o aceite a estes Termos e Condições, o Parceiro Institucional passará a compartilhar com a Capitale as informações referentes aos valores a receber pelo Tomador, de forma mensal. Com base nessas informações, será enviada ao Tomador uma proposta individual de crédito, contendo o valor disponível, condições e o link para assinatura da respectiva CCB. O documento será enviado pela UY3, instituição financeira responsável pela emissão do crédito. Em até 48h (quarenta e oito horas) após assinatura, os recursos serão disponibilizados na conta corrente informada pelo Parceiro Institucional no seu momento de cadastro, e que estará discriminada na própria CCB para sua conferência.</p>
      
      <p>Para a viabilização da operação, será necessária a abertura de uma conta vinculada (Conta Escrow) em nome do Tomador, também junto à UY3. Essa conta será utilizada exclusivamente para a liquidação da operação, e sua constituição ocorrerá mediante assinatura de documento específico, que será encaminhado ao Tomador por meio eletrônico para leitura e aceite, antes da liberação da primeira operação.</p>
      
      <h2 style="margin-top: 30px;">4. CONDIÇÕES ECONÔMICAS E FINANCEIRAS</h2>
      
      <p><strong>4.1.</strong> O valor disponibilizado ao Tomador será de até 90,00% (noventa por cento) do valor líquido a receber, observado o limite técnico definido pela política de crédito da Capitale, o qual será informado previamente ao Tomador. Poderá haver limitação por CPF, grupo econômico ou percentual de repasses em aberto.</p>
      
      <p><strong>4.2.</strong> A operação será remunerada por meio de:</p>
      <ul style="margin-left: 20px;">
        <li>Juros remuneratórios: taxa de juros remuneratórios compostos capitalizados sobre base de 252 dias úteis, podendo ser ajustada conforme risco da operação, volume agregado ou relacionamento comercial;</li>
        <li>Tarifa de estruturação, quando aplicável, previamente informada e deduzida do valor liberado;</li>
        <li>IOF (Imposto sobre Operações Financeiras), conforme regulamentação vigente;</li>
        <li>Multa por inadimplemento e juros de mora, conforme descrito na CCB.</li>
      </ul>
      
      <p><strong>4.3.</strong> O Custo Efetivo Total ("CET") será disponibilizado ao Tomador em formato de planilha ou extrato detalhado, refletindo todas as despesas incidentes, conforme exigido pelo Banco Central do Brasil e normas do Sistema Financeiro Nacional.</p>
      
      <h2 style="margin-top: 30px;">5. MECANISMO DE LIQUIDAÇÃO</h2>
      
      <p><strong>5.1.</strong> As operações serão liquidadas automaticamente por meio de repasse financeiro oriundo da Conta Escrow de titularidade do Tomador, mantida em instituição financeira indicada pela Capitale e constituída exclusivamente para receber os pagamentos realizados pelo Parceiro Institucional. Os recebíveis do Parceiro Institucional também serão controlados pela Capitale via conta Escrow de titularidade do Parceiro Institucional junto à UY3.</p>
      
      <p><strong>5.2.</strong> O Tomador cede fiduciariamente:</p>
      <ul style="margin-left: 20px;">
        <li>Os créditos decorrentes das faturas médicas, horas trabalhadas, ou recebíveis de titularidade do Tomador em face do Parceiro Institucional;</li>
        <li>Os saldos disponíveis na Conta Escrow; e</li>
        <li>Os frutos e rendimentos de quaisquer aplicações vinculadas à Conta Escrow.</li>
      </ul>
      
      <p>Essa cessão permanece válida até a liquidação integral de todas as obrigações assumidas pelo Tomador no âmbito da CCB.</p>
      
      <p><strong>5.3.</strong> Caso o fluxo pactuado não seja suficiente para a quitação da operação, o Tomador permanecerá responsável pelo pagamento integral do saldo devedor, podendo efetuar quitação via transferência bancária, boleto ou qualquer meio autorizado.</p>
      
      <h2 style="margin-top: 30px;">6. RESPONSABILIDADES DAS PARTES</h2>
      
      <p><strong>6.1.</strong> O Tomador declara-se:</p>
      <ul style="margin-left: 20px;">
        <li>Integralmente responsável pela veracidade dos dados fornecidos;</li>
        <li>Ciente de que a CCB é título de crédito dotado de força executiva;</li>
        <li>Consciente de que eventual inadimplemento poderá ensejar protesto, negativação, cobrança judicial, execução ou outras medidas de recuperação;</li>
        <li>Ciente de que a Capitale e sua Instituição Parceira não têm qualquer ingerência sobre o vínculo trabalhista ou associativo com o Parceiro Institucional.</li>
      </ul>
      
      <p><strong>6.2.</strong> A Capitale compromete-se a:</p>
      <ul style="margin-left: 20px;">
        <li>Promover a transparência das condições contratuais;</li>
        <li>Respeitar a confidencialidade das informações do Tomador, em conformidade com a LGPD;</li>
        <li>Realizar análise de crédito com critérios técnicos, isonômicos e impessoais;</li>
        <li>Fornecer canal direto para dúvidas, reclamações e renegociação, quando aplicável.</li>
      </ul>
      
      <h2 style="margin-top: 30px;">7. RISCOS E ADVERTÊNCIAS</h2>
      
      <p><strong>7.1.</strong> O Tomador reconhece que a contratação do crédito implica exposição a:</p>
      <ul style="margin-left: 20px;">
        <li>Risco de inadimplência em caso de glosa ou não pagamento pelo Parceiro Institucional;</li>
        <li>Incidência de encargos adicionais em caso de atraso;</li>
        <li>Registro em órgãos de proteção ao crédito em caso de inadimplemento.</li>
      </ul>
      
      <p><strong>7.2.</strong> A operação não poderá ser contratada por Tomadores que se encontrem em condição de superendividamento, nos termos da Lei nº 14.181/2021. Em caso de judicialização baseada nesta condição, o Parceiro Institucional compromete-se a colaborar com a Capitale para reestruturar a operação e resguardar a adimplência.</p>
      
      <h2 style="margin-top: 30px;">8. PROTEÇÃO DE DADOS E SIGILO</h2>
      
      <p><strong>8.1.</strong> A Capitale, na qualidade de Controladora de Dados Pessoais, declara e garante que o tratamento dos dados pessoais e sensíveis do Tomador será realizado em estrita observância à Lei nº 13.709/2018 – Lei Geral de Proteção de Dados Pessoais (LGPD), bem como às demais normas aplicáveis sobre proteção da privacidade, confidencialidade e segurança da informação.</p>
      
      <p><strong>8.2.</strong> Para os fins desta cláusula, consideram-se "Dados Pessoais" quaisquer informações relacionadas à pessoa natural identificada ou identificável, incluindo, mas não se limitando a: nome completo, CPF, número do CRM, endereço, e-mail, telefone, dados bancários, valores recebidos, histórico de crédito, dados transacionais, biometria, e outros dados exigidos para análise, concessão, monitoramento e execução do contrato de crédito. Quando aplicável, dados sensíveis serão também protegidos nos termos da LGPD.</p>
      
      <p><strong>8.3.</strong> A Capitale compromete-se a realizar o tratamento dos dados pessoais exclusivamente para as seguintes finalidades legítimas e compatíveis com o objeto da operação de crédito:</p>
      <ul style="margin-left: 20px;">
        <li>Análise de crédito e prevenção à fraude;</li>
        <li>Emissão e formalização da CCB;</li>
        <li>Execução e liquidação da operação via Conta Escrow;</li>
        <li>Cumprimento de obrigações legais e regulatórias impostas por órgãos como Banco Central, CVM, COAF, Receita Federal e Poder Judiciário;</li>
        <li>Atendimento ao Tomador e gestão do relacionamento contratual;</li>
        <li>Exercício regular de direitos em processos administrativos, arbitrais ou judiciais;</li>
        <li>Envio de comunicações institucionais, quando necessário à execução do contrato;</li>
        <li>Prevenção a situações de superendividamento e adequação ao perfil financeiro do Tomador.</li>
      </ul>
      
      <p><strong>8.4.</strong> A Capitale assegura a adoção de medidas técnicas e administrativas apropriadas e eficazes para proteger os dados pessoais contra acessos não autorizados e situações acidentais ou ilícitas de destruição, perda, alteração, comunicação ou qualquer forma de tratamento inadequado ou ilícito, incluindo, mas não se limitando a:</p>
      <ul style="margin-left: 20px;">
        <li>Registro e rastreabilidade de logs de acesso por usuários autorizados;</li>
        <li>Segmentação e anonimização/pseudonimização de dados quando possível;</li>
        <li>Protocolos de resposta a incidentes de segurança com SLA de comunicação à ANPD em até 48h; e</li>
        <li>Armazenamento de dados em servidores com certificações necessárias nos termos da Lei.</li>
      </ul>
      
      <p><strong>8.5.</strong> A Capitale manterá os dados pelo prazo necessário ao cumprimento das finalidades do tratamento, respeitando os prazos legais mínimos exigidos por normas setoriais, inclusive de natureza fiscal, regulatória e contratual. Após a finalização do tratamento, os dados serão eliminados ou anonimizados, conforme artigo 15 da LGPD, salvo necessidade legítima de conservação para resguardo de direitos.</p>
      
      <p><strong>8.6.</strong> O Tomador, na condição de titular dos dados pessoais, poderá exercer a qualquer tempo, mediante solicitação expressa, os direitos previstos no artigo 18 da LGPD, inclusive:</p>
      <ul style="margin-left: 20px;">
        <li>Confirmação da existência de tratamento;</li>
        <li>Acesso aos dados;</li>
        <li>Correção de dados incompletos, inexatos ou desatualizados;</li>
        <li>Anonimização, bloqueio ou eliminação de dados desnecessários, excessivos ou tratados em desconformidade;</li>
        <li>Portabilidade dos dados, observados os segredos comercial e industrial;</li>
        <li>Eliminação dos dados tratados com consentimento, quando aplicável;</li>
        <li>Informação sobre compartilhamento com terceiros; e</li>
        <li>Revogação do consentimento, quando o tratamento se basear nessa hipótese.</li>
      </ul>
      
      <p><strong>8.7.</strong> O exercício dos direitos pelo titular poderá ser feito por meio dos canais oficiais da Capitale, devendo ser respondido em prazo razoável, observado o disposto no artigo 19 da LGPD. Caso haja incidente de segurança com risco relevante aos direitos do titular, a Capitale compromete-se a notificar o Tomador e a Autoridade Nacional de Proteção de Dados (ANPD), nos termos legais.</p>
      
      <p><strong>8.8.</strong> A Capitale poderá compartilhar os dados pessoais com terceiros estritamente necessários à execução deste contrato, tais como: (i) instituições financeiras parceiras; (ii) operadores de tecnologia e servidores de dados; (iii) órgãos reguladores e autoridades públicas; (iv) escritórios de cobrança, escritórios de advocacia, auditores e consultores; desde que tais terceiros estejam sujeitos a obrigações contratuais de confidencialidade e segurança equivalentes às aqui estipuladas.</p>
      
      <p><strong>8.9.</strong> A confidencialidade e a proteção de dados se estendem a todos os empregados, representantes, terceiros contratados e prepostos da Capitale. O eventual descumprimento sujeitará o responsável às penalidades legais e contratuais, inclusive indenização por perdas e danos.</p>
      
      <p><strong>8.10.</strong> Para mais informações sobre a Política de Privacidade da Capitale, o Tomador poderá acessá-la diretamente pelo site institucional ou solicitar sua íntegra por meio dos canais de atendimento disponíveis. A Capitale poderá atualizar seus procedimentos de privacidade a qualquer momento, garantindo, no entanto, comunicação prévia em caso de mudanças substanciais.</p>
      
      <h2 style="margin-top: 30px;">9. DISPOSIÇÕES FINAIS</h2>
      
      <p><strong>9.1.</strong> A contratação da operação será considerada válida apenas após assinatura da CCB e entrega dos documentos exigidos.</p>
      
      <p><strong>9.2.</strong> A adesão aos presentes Termos independe da contratação de qualquer outro serviço ou produto financeiro.</p>
      
      <p><strong>9.3.</strong> Qualquer condição não prevista neste instrumento será regida pela CCB e, supletivamente, pelo Código Civil e pela legislação aplicável ao Sistema Financeiro Nacional.</p>
      
      <p><strong>9.4.</strong> Estes termos e condições poderão ser alterados unilateralmente pela Capitale a qualquer tempo.</p>
      
      <h2 style="margin-top: 30px;">10. FORO</h2>
      
      <p><strong>10.1.</strong> Fica eleito o Foro Central da Comarca da Capital do Estado de São Paulo para dirimir quaisquer controvérsias oriundas destes Termos e da operação contratada, com renúncia expressa a qualquer outro, por mais privilegiado que seja.</p>
      
      <div style="padding: 20px; margin-top: 40px; border-radius: 8px;">
        <h2 style="">Termo de Tratamento de Dados Pessoais – LGPD</h2>
        
        <p>Ao prosseguir com o uso da plataforma, declaro estar ciente e de acordo com o tratamento dos meus dados pessoais pela Capitale Holding Ltda., inscrita no CNPJ 53.639.169/0001-52, na qualidade de Controladora de Dados Pessoais, nos termos da Lei nº 13.709/2018 (Lei Geral de Proteção de Dados – LGPD).</p>
        
        <h3 style="">1. Finalidade do Tratamento</h3>
        
        <p>Meus dados pessoais, incluindo informações como nome completo, CPF, CRM, e-mail, telefone, dados bancários, valores recebidos, histórico de crédito e demais dados necessários, poderão ser utilizados para as seguintes finalidades: (i) análise de crédito e prevenção à fraude; (ii) verificação de elegibilidade para as operações financeiras oferecidas; (iii) emissão e formalização do contrato de crédito (CCB); (iv) liquidação financeira via conta vinculada (Escrow); (v) cumprimento de obrigações legais e regulatórias impostas por autoridades como Banco Central, CVM, COAF, Receita Federal ou Poder Judiciário; (vi) atendimento ao titular e gestão do relacionamento contratual; (vii) exercício regular de direitos em processos administrativos, judiciais ou arbitrais; (viii) envio de comunicações institucionais e informações sobre a operação contratada; e (ix) prevenção a situações de superendividamento.</p>
        
        <h3 style="">2. Compartilhamento de Dados</h3>
        
        <p>Meus dados poderão ser compartilhados, de forma limitada e segura, com terceiros diretamente envolvidos na execução da operação, tais como: (i) instituições financeiras parceiras; (ii) fornecedores de tecnologia e armazenamento de dados; (iii) escritórios de advocacia, auditores e consultores; e (iv) órgãos reguladores e autoridades públicas. Tais terceiros estarão contratualmente obrigados a garantir o sigilo e a segurança das informações compartilhadas.</p>

        <h3 style="">3. Segurança e Retenção dos Dados</h3>

        <p>A Capitale adota medidas técnicas e administrativas de segurança da informação, incluindo: (i) controle de acesso, rastreamento de logs e segregação de dados; (ii) armazenamento seguro em servidores com certificações adequadas; (iii) políticas de resposta a incidentes de segurança e comunicação à ANPD em caso de risco relevante. Os dados serão mantidos pelo prazo necessário ao cumprimento das finalidades acima ou conforme exigência legal. Após esse período, serão eliminados ou anonimizados, salvo obrigação de guarda.</p>

        <h3 style="">4. Direitos do Titular</h3>

        <p>Reconheço que, como titular dos dados pessoais, posso a qualquer momento exercer os seguintes direitos: (i) confirmação da existência de tratamento; (ii) acesso aos dados; (iii) correção de dados incompletos, inexatos ou desatualizados; (iv) anonimização, bloqueio ou eliminação de dados desnecessários ou excessivos; (v) portabilidade dos dados; (vi) revogação do consentimento, quando aplicável; e (vii) informação sobre o compartilhamento com terceiros. Para exercício desses direitos, poderei entrar em contato pelo e-mail: <strong style="color: #3498db;"><EMAIL></strong>.</p>
      </div>
    </div>
  `;

  const hashConteudo = createHash("sha256").update(conteudo).digest("hex");

  await prisma.gerenciamentoTermosLgpd.create({
    data: {
      versao: "v1.0",
      titulo: "Política de Privacidade e LGPD",
      conteudo: conteudo,
      hashConteudo: hashConteudo,
      ativo: true,
      validoDe: new Date("2025-07-15"),
      tipoDocumento: "termos_lgpd",
    },
  });
}

async function main() {
  // deve rodar somente em dev: "yarn db:reset"

  await seedTermos();

  const clinicaGeral = await prisma.especialidade.create({
    data: {
      nome: "Clínico Geral",
      descricao: "Medicina geral e familiar",
    },
  });

  const pediatria = await prisma.especialidade.create({
    data: {
      nome: "Pediatria",
      descricao: "Medicina infantil",
    },
  });

  const cardiologia = await prisma.especialidade.create({
    data: {
      nome: "Cardiologia",
      descricao: "Medicina cardiovascular",
    },
  });

  const enfermagem = await prisma.especialidade.create({
    data: {
      nome: "Enfermagem",
      descricao: "Cuidados de enfermagem",
    },
  });

  const roleMaster = await prisma.role.create({
    data: {
      nome: "master",
      descricao: "Acesso total ao sistema",
    },
  });

  const roleAdmin = await prisma.role.create({
    data: {
      nome: "admin",
      descricao: "Administrador do sistema",
    },
  });

  const roleProfissional = await prisma.role.create({
    data: {
      nome: "profissional",
      descricao: "Profissional de saúde",
    },
  });

  const roleCoordenador = await prisma.role.create({
    data: {
      nome: "coordenador",
      descricao: "Coordenador de plantões",
    },
  });

  const master = await prisma.usuario.create({
    data: {
      nome: "Master",
      email: "<EMAIL>",
      cpf: "00000000000",
      senha: await generatePass("123321123321123321123321"),
      emailVerificado: true,
      emailVerificadoEm: new Date(),
      ativo: true,
    },
    include: {
      roles: true,
    },
  });

  await prisma.usuarioRole.create({
    data: {
      usuarioId: master.id,
      roleId: roleMaster.id,
    },
  });

  // return;

  // Criar Clientes
  const cliente1 = await prisma.cliente.create({
    data: {
      nome: "Hospital São Lucas (Cliente Teste 1)",
      cnpj: "12345678000190",
      email: "<EMAIL>",
      telefone: "(11) 3456-7890",
      taxaPadrao: 5,
      // Configuração da fórmula de taxa de antecipação (juros compostos)
      // Conforme atualizado.md item 34-40
      ativo: true,
    },
  });

  await prisma.localAtendimento.create({
    data: {
      clienteId: cliente1.id,
      nome: "Hospital São Lucas - Unidade Central (Cliente Teste 1)",
      endereco: "Av. Principal, 1000",
      cidade: "São Paulo",
      estado: "SP",
      cep: "01234-567",
      latitude: -23.55052,
      longitude: -46.633308,
    },
  });

  await prisma.localAtendimento.create({
    data: {
      clienteId: cliente1.id,
      nome: "Hospital São Lucas - Pronto Socorro (Cliente Teste 1)",
      endereco: "Av. Principal, 1050",
      cidade: "São Paulo",
      estado: "SP",
      cep: "01234-568",
      latitude: -23.55152,
      longitude: -46.634308,
    },
  });

  const senhaHash = await generatePass("*********");

  const usuarioAdmin = await prisma.usuario.create({
    data: {
      nome: "Administrador",
      email: "<EMAIL>",
      cpf: "00000000001",
      senha: senhaHash,
      emailVerificado: true,
      emailVerificadoEm: new Date(),
      ativo: true,
    },
  });

  // Atribuir role master ao admin
  await prisma.usuarioRole.create({
    data: {
      usuarioId: usuarioAdmin.id,
      roleId: roleAdmin.id,
    },
  });

  const usuario1 = await prisma.usuario.create({
    data: {
      nome: "Dr. João Silva (Profissional Teste 1)",
      email: "<EMAIL>",
      cpf: "12345678901",
      senha: senhaHash,
      telefone: "(11) 98765-4321",
      emailVerificado: true,
      emailVerificadoEm: new Date(),
      ativo: true,
    },
  });

  // Atribuir role profissional
  await prisma.usuarioRole.create({
    data: {
      usuarioId: usuario1.id,
      roleId: roleProfissional.id,
    },
  });

  const usuario2 = await prisma.usuario.create({
    data: {
      nome: "Dra. Maria Santos (Profissional Teste 2)",
      email: "<EMAIL>",
      cpf: "98765432109",
      senha: senhaHash,
      telefone: "(11) 97654-3210",
      emailVerificado: true,
      emailVerificadoEm: new Date(),
      ativo: true,
    },
  });

  // Atribuir roles profissional e gestor
  await prisma.usuarioRole.create({
    data: {
      usuarioId: usuario2.id,
      roleId: roleProfissional.id,
    },
  });

  // Criar Profissionais vinculados aos usuários
  const profissional1 = await prisma.profissional.create({
    data: {
      usuarioId: usuario1.id,
      banco: "001",
      agencia: "1234",
      digitoAgencia: "5",
      conta: "12345",
      digitoConta: "6",
      tipoConta: "corrente",
      chavePix: "<EMAIL>",
      tipoPix: "Email",
    },
  });

  const profissional2 = await prisma.profissional.create({
    data: {
      usuarioId: usuario2.id,
      banco: "341",
      agencia: "5678",
      conta: "98765",
      digitoConta: "4",
      tipoConta: "corrente",
      chavePix: "98765432109",
      tipoPix: "CPF",
    },
  });

  await prisma.profissionalEspecialidade.create({
    data: {
      profissionalId: profissional1.id,
      especialidadeId: clinicaGeral.id,
    },
  });

  await prisma.profissionalEspecialidade.create({
    data: {
      profissionalId: profissional2.id,
      especialidadeId: pediatria.id,
    },
  });

  await prisma.profissionalEspecialidade.create({
    data: {
      profissionalId: profissional1.id,
      especialidadeId: enfermagem.id,
    },
  });

  await prisma.usuarioCliente.create({
    data: {
      usuarioId: usuario1.id,
      clienteId: cliente1.id,
    },
  });

  await prisma.usuarioCliente.create({
    data: {
      usuarioId: usuario2.id,
      clienteId: cliente1.id,
    },
  });

  await prisma.usuarioCliente.create({
    data: {
      usuarioId: usuarioAdmin.id,
      clienteId: cliente1.id,
    },
  });

  // Criar um segundo cliente com configurações diferentes
  const cliente2 = await prisma.cliente.create({
    data: {
      nome: "Clínica Saúde Total",
      cnpj: "98765432000190",
      email: "<EMAIL>",
      telefone: "(11) 9876-5432",
      taxaPadrao: 3.5,
      cidade: "São Paulo",
      uf: "SP",
      ativo: true,
    },
  });

  const localCliente2 = await prisma.localAtendimento.create({
    data: {
      clienteId: cliente2.id,
      nome: "Clínica Saúde Total - Unidade Principal",
      endereco: "Rua das Flores, 500",
      cidade: "São Paulo",
      estado: "SP",
      cep: "04567-890",
      latitude: -23.56052,
      longitude: -46.643308,
      ativo: true,
    },
  });

  // Vincular usuário2 ao cliente2
  await prisma.usuarioCliente.create({
    data: {
      usuarioId: usuario2.id,
      clienteId: cliente2.id,
    },
  });

  // Criar plantões de exemplo
  const hoje = new Date();
  const inicioMes = new Date(hoje.getFullYear(), hoje.getMonth(), 1);
  const fimMes = new Date(hoje.getFullYear(), hoje.getMonth() + 1, 0);

  // Plantão para o profissional1 no cliente1
  const plantao1 = await prisma.plantao.create({
    data: {
      clienteId: cliente1.id,
      localAtendimentoId: (await prisma.localAtendimento.findFirst({ where: { clienteId: cliente1.id } }))!.id,
      profissionalId: profissional1.id,
      dataInicial: inicioMes,
      dataFinal: fimMes,
      prazoPagamentoDias: 30, // Conforme atualizado.md item 9
      modalidadeTrabalho: "PLANTONISTA",
      tipoFechamento: "MENSAL",
      tipoValor: "HORA",
      valorBase: 150, // R$ 150 por hora
      horaInicio: "08:00",
      horaFim: "20:00",
      intervalo: "01:00",
      tipoTurno: "diurno",
      observacoes: "Plantão de teste para desenvolvimento",
    },
  });

  // Criar alguns dias de plantão
  for (let dia = 1; dia <= 5; dia++) {
    const dataPlantao = new Date(hoje.getFullYear(), hoje.getMonth(), dia);
    const diaPlantao = await prisma.diaPlantao.create({
      data: {
        plantaoId: plantao1.id,
        data: dataPlantao,
        horaEntrada: "08:00",
        horaSaida: "20:00",
        intervalo: "01:00",
      },
    });

    // Criar registros de presença para os primeiros 3 dias
    if (dia <= 3) {
      const entradaHora = new Date(dataPlantao);
      entradaHora.setHours(8, 0, 0, 0);

      const saidaHora = new Date(dataPlantao);
      saidaHora.setHours(20, 0, 0, 0);

      await prisma.presencaDiaPlantao.create({
        data: {
          diaPlantaoId: diaPlantao.id,
          horaEntrada: entradaHora,
          horaSaida: saidaHora,
          intervalo: "01:00",
          horasTrabalhadas: 11, // 12 horas - 1 hora de intervalo
          valorEstimado: 11 * 150, // 11 horas * R$ 150
          status: "APROVADO",
          historicoAprovacoes: [
            {
              usuarioId: usuarioAdmin.id,
              nome: "Administrador",
              acao: "APROVADO",
              data: new Date().toISOString(),
              observacao: "Aprovado automaticamente pelo seed",
            },
          ],
        },
      });
    }
  }

  console.log("✅ Seed executado com sucesso!");
  console.log("📋 Dados criados:");
  console.log("   - 2 Clientes com configurações de taxa");
  console.log("   - 4 Locais de atendimento");
  console.log("   - 3 Usuários (Master, Admin, Profissional)");
  console.log("   - 2 Profissionais");
  console.log("   - 1 Plantão com 5 dias");
  console.log("   - 3 Registros de presença aprovados");
  console.log("   - Termos LGPD");
}

main()
  .catch((e) => {
    console.error("❌ Erro ao executar seed:", e);
    process.exit(1);
  })
  .finally(async () => {
    await prisma.$disconnect();
  });
