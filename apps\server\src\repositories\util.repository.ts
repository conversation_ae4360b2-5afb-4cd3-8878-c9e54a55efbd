import { prisma as PrismaClient } from "@lib/prisma";

export class UtilRepository {
  constructor(private prisma: typeof PrismaClient) {}

  /**
   * Assign roles to a user by role names
   * @param userId - User ID to assign roles to
   * @param roleNames - Array of role names to assign (master, admin, coordenador, profissional)
   */
  async assignRolesToUser(
    userId: number,
    roleNames: Array<"master" | "admin" | "coordenador" | "profissional">
  ): Promise<void> {
    // Find role IDs by their names
    const roles = await this.prisma.role.findMany({
      where: {
        nome: { in: roleNames },
      },
      select: {
        id: true,
        nome: true,
      },
    });

    // Check if all requested roles were found
    const foundRoleNames = roles.map((role) => role.nome);
    const missingRoles = roleNames.filter((name) => !foundRoleNames.includes(name));

    if (missingRoles.length > 0) {
      throw new Error(`Roles not found: ${missingRoles.join(", ")}`);
    }

    // Remove existing roles for this user before adding new ones
    await this.prisma.usuarioRole.deleteMany({
      where: { usuarioId: userId },
    });

    // Create new role assignments
    const roleAssignments = roles.map((role) => ({
      usuarioId: userId,
      roleId: role.id,
    }));

    await this.prisma.usuarioRole.createMany({
      data: roleAssignments,
    });
  }
}
