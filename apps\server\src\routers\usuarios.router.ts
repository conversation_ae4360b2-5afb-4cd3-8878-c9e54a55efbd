import { z } from "zod";
import { prisma } from "../lib/prisma";
import { authorize } from "../middlewares/auth.middleware";
import type { FastifyTypedInstance } from "@/types";
import { generatePass, validatePass } from "@/lib/auth";

// Schema de validação
const createUsuarioSchema = z.object({
  nome: z.string().min(1),
  email: z.string().email(),
  password: z.string().min(6),
  telefone: z.string().optional(),
  cpf: z.string(),
  cep: z.string().optional(),
  logradouro: z.string().optional(),
  numero: z.string().optional(),
  complemento: z.string().optional(),
  bairro: z.string().optional(),
  cidade: z.string().optional(),
  uf: z.string().optional(),
  roles: z.array(z.string()),
  clienteIds: z.array(z.number()).optional(),
});

const updateUsuarioSchema = z.object({
  nome: z.string().min(1).optional(),
  email: z.string().email().optional(),
  password: z.string().min(6).optional(),
  telefone: z.string().optional(),
  cpf: z.string().optional(),
  cep: z.string().optional(),
  logradouro: z.string().optional(),
  numero: z.string().optional(),
  complemento: z.string().optional(),
  bairro: z.string().optional(),
  cidade: z.string().optional(),
  uf: z.string().optional(),
  roles: z.array(z.string()).optional(),
  clienteIds: z.array(z.number()).optional(),
  ativo: z.boolean().optional(),
});

const updateMeuPerfilSchema = z.object({
  nome: z.string().min(1),
  cpf: z.string(),
  telefone: z.string(),
  dataNascimento: z.string().optional(),
  genero: z.string().optional(),
  estadoCivil: z.string().optional(),
  nacionalidade: z.string().optional(),
  cep: z.string(),
  logradouro: z.string(),
  numero: z.string(),
  complemento: z.string().optional(),
  bairro: z.string(),
  cidade: z.string(),
  uf: z.string().length(2),
});

const alterarSenhaSchema = z.object({
  senhaAtual: z.string().min(6),
  novaSenha: z.string().min(6),
});

export async function usuariosRouter(app: FastifyTypedInstance) {
  // Endpoint para buscar o próprio perfil do usuário logado
  app.get("/usuarios/me", async (request, reply) => {
    try {
      if (!request.user) {
        return reply.status(401).send({ error: "Não autenticado" });
      }

      const usuario = await prisma.usuario.findUnique({
        where: { id: request.user.id },
        include: {
          roles: {
            select: {
              role: {
                select: {
                  nome: true,
                },
              },
            },
          },
          profissional: {
            select: {
              id: true,
              especialidades: {
                include: {
                  especialidade: true,
                },
              },
            },
          },
        },
      });

      if (!usuario) {
        return reply.status(404).send({ error: "Usuário não encontrado" });
      }

      const { senha, ...usuarioSemSenha } = usuario;
      return reply.send({
        ...usuarioSemSenha,
        roles: usuario.roles.map((ur: any) => ur.role),
        profissionalId: usuario.profissional?.id || null,
      });
    } catch (error) {
      console.error("Erro ao buscar perfil:", error);
      return reply.status(500).send({ error: "Erro ao buscar perfil" });
    }
  });

  // Endpoint para atualizar o próprio perfil
  app.put("/usuarios/me", async (request, reply) => {
    try {
      if (!request.user) {
        return reply.status(401).send({ error: "Não autenticado" });
      }

      const data = updateMeuPerfilSchema.parse(request.body);

      // Parse da data de nascimento se fornecida
      let dataNascimento = null;
      if (data.dataNascimento) {
        dataNascimento = new Date(data.dataNascimento);
      }

      const usuario = await prisma.usuario.update({
        where: { id: request.user.id },
        data: {
          nome: data.nome,
          cpf: data.cpf,
          telefone: data.telefone,
          dataNascimento,
          genero: data.genero || null,
          estadoCivil: data.estadoCivil || null,
          nacionalidade: data.nacionalidade || null,
          cep: data.cep,
          logradouro: data.logradouro,
          numero: data.numero,
          complemento: data.complemento || null,
          bairro: data.bairro,
          cidade: data.cidade,
          uf: data.uf,
        },
      });

      const { senha, ...usuarioSemSenha } = usuario;
      return reply.send(usuarioSemSenha);
    } catch (error: any) {
      console.error("Erro ao atualizar perfil:", error);
      if (error.code === "P2002") {
        return reply.status(400).send({ error: "CPF já cadastrado" });
      }
      return reply.status(500).send({ error: "Erro ao atualizar perfil" });
    }
  });

  // Endpoint para alterar a própria senha
  app.put("/usuarios/me/senha", async (request, reply) => {
    try {
      if (!request.user) {
        return reply.status(401).send({ error: "Não autenticado" });
      }

      const { senhaAtual, novaSenha } = alterarSenhaSchema.parse(request.body);

      // Buscar usuário com senha atual
      const usuario = await prisma.usuario.findUnique({
        where: { id: request.user.id },
      });

      if (!usuario) {
        return reply.status(404).send({ error: "Usuário não encontrado" });
      }

      // Verificar senha atual
      const senhaValida = await validatePass(senhaAtual, usuario.senha);
      if (!senhaValida) {
        return reply.status(400).send({ error: "Senha atual incorreta" });
      }

      // Hash da nova senha
      const hashedPassword = await generatePass(novaSenha);

      // Atualizar senha
      await prisma.usuario.update({
        where: { id: request.user.id },
        data: {
          senha: hashedPassword,
        },
      });

      return reply.send({ message: "Senha alterada com sucesso" });
    } catch (error) {
      console.error("Erro ao alterar senha:", error);
      return reply.status(500).send({ error: "Erro ao alterar senha" });
    }
  });

  // Listar todos os usuários - Apenas admin, master podem listar usuários
  app.get("/usuarios", { preHandler: [authorize("admin", "master")] }, async (request, reply) => {
    try {
      const usuarios = await prisma.usuario.findMany({
        include: {
          roles: {
            include: {
              role: true,
            },
          },
        },
        orderBy: {
          nome: "asc",
        },
      });

      // Remover senhas da resposta
      const usuariosSemSenha = usuarios.map((usuario) => {
        const { senha, ...usuarioSemSenha } = usuario;
        return {
          ...usuarioSemSenha,
          roles: usuario.roles.map((ur) => ur.role.nome),
        };
      });

      return reply.send(usuariosSemSenha);
    } catch (error) {
      console.error("Erro ao listar usuários:", error);
      return reply.status(500).send({ error: "Erro ao listar usuários" });
    }
  });

  // Buscar usuário por UUID - Apenas admin, master e gestor podem buscar detalhes
  app.get("/usuarios/:uuid", { preHandler: [authorize("admin", "master")] }, async (request, reply) => {
    try {
      const { uuid } = request.params as { uuid: string };

      const usuario = await prisma.usuario.findUnique({
        where: { uuid },
        include: {
          roles: {
            include: {
              role: true,
            },
          },
          clientes: {
            include: {
              cliente: true,
            },
          },
        },
      });

      if (!usuario) {
        return reply.status(404).send({ error: "Usuário não encontrado" });
      }

      const { senha, ...usuarioSemSenha } = usuario;
      return reply.send({
        ...usuarioSemSenha,
        roles: usuario.roles.map((ur) => ur.role),
      });
    } catch (error) {
      console.error("Erro ao buscar usuário:", error);
      return reply.status(500).send({ error: "Erro ao buscar usuário" });
    }
  });

  // Criar novo usuário - Apenas admin e master podem criar usuários
  app.post("/usuarios", { preHandler: [authorize("admin", "master")] }, async (request, reply) => {
    try {
      const data = createUsuarioSchema.parse(request.body);

      // Verificar se email já existe
      const existingUser = await prisma.usuario.findUnique({
        where: { email: data.email },
      });

      if (existingUser) {
        return reply.status(400).send({ error: "Email já cadastrado" });
      }

      // Hash da senha
      const hashedPassword = await generatePass(data.password);

      // Separar roles e clienteIds do resto dos dados
      const { roles, clienteIds, password, ...userData } = data;

      // Buscar as roles primeiro
      const roleIds = [];
      if (roles && roles.length > 0) {
        for (const roleName of roles) {
          const role = await prisma.role.findUnique({
            where: { nome: roleName },
          });
          if (!role) {
            throw new Error(`Role ${roleName} não encontrada`);
          }
          roleIds.push(role.id);
        }
      }

      // Criar usuário
      const usuario = await prisma.usuario.create({
        data: {
          ...userData,
          senha: hashedPassword,
          roles:
            roleIds.length > 0
              ? {
                  create: roleIds.map((roleId) => ({
                    roleId: roleId,
                  })),
                }
              : undefined,
          clientes:
            clienteIds && clienteIds.length > 0
              ? {
                  create: clienteIds.map((clienteId) => ({
                    clienteId: clienteId,
                  })),
                }
              : undefined,
        },
      });

      const { senha, ...usuarioSemSenha } = usuario;
      return reply.status(201).send(usuarioSemSenha);
    } catch (error) {
      console.error("Erro ao criar usuário:", error);
      return reply.status(500).send({ error: "Erro ao criar usuário" });
    }
  });

  // Atualizar usuário - Apenas admin e master podem atualizar
  app.put("/usuarios/:uuid", { preHandler: [authorize("admin", "master")] }, async (request, reply) => {
    try {
      const { uuid } = request.params as { uuid: string };
      const data = updateUsuarioSchema.parse(request.body);

      // Verificar se usuário existe
      const usuario = await prisma.usuario.findUnique({
        where: { uuid },
      });

      if (!usuario) {
        return reply.status(404).send({ error: "Usuário não encontrado" });
      }

      // Se email foi alterado, verificar se já existe
      if (data.email && data.email !== usuario.email) {
        const existingUser = await prisma.usuario.findUnique({
          where: { email: data.email },
        });

        if (existingUser) {
          return reply.status(400).send({ error: "Email já cadastrado" });
        }
      }

      // Hash da senha se foi fornecida
      let hashedPassword;
      if (data.password) {
        hashedPassword = await generatePass(data.password);
      }

      // Separar roles e clienteIds do resto dos dados
      const { roles, clienteIds, password, ...userData } = data;

      // Buscar as roles primeiro se foram fornecidas
      const roleIds = [];
      if (roles && roles.length > 0) {
        for (const roleName of roles) {
          const role = await prisma.role.findUnique({
            where: { nome: roleName },
          });
          if (!role) {
            throw new Error(`Role ${roleName} não encontrada`);
          }
          roleIds.push(role.id);
        }
      }

      // Atualizar usuário
      const updatedUsuario = await prisma.usuario.update({
        where: { uuid },
        data: {
          ...userData,
          senha: hashedPassword,
          roles: roles
            ? {
                deleteMany: {},
                create: roleIds.map((roleId) => ({
                  roleId: roleId,
                })),
              }
            : undefined,
          clientes:
            clienteIds !== undefined
              ? {
                  deleteMany: {},
                  create: clienteIds.map((clienteId) => ({
                    clienteId: clienteId,
                  })),
                }
              : undefined,
        },
        include: {
          roles: {
            include: {
              role: true,
            },
          },
          clientes: {
            include: {
              cliente: true,
            },
          },
        },
      });

      const { senha, ...usuarioSemSenha } = updatedUsuario;

      return reply.send(usuarioSemSenha);
    } catch (error) {
      console.error("Erro ao atualizar usuário:", error);
      return reply.status(500).send({ error: "Erro ao atualizar usuário" });
    }
  });

  // Deletar usuário (soft delete) - Apenas admin e master podem deletar
  app.delete("/usuarios/:uuid", { preHandler: [authorize("admin", "master")] }, async (request, reply) => {
    try {
      const { uuid } = request.params as { uuid: string };

      const usuario = await prisma.usuario.findUnique({
        where: { uuid },
      });

      if (!usuario) {
        return reply.status(404).send({ error: "Usuário não encontrado" });
      }

      // Soft delete - apenas marca como inativo
      await prisma.usuario.update({
        where: { uuid },
        data: { ativo: false },
      });

      return reply.status(204).send();
    } catch (error) {
      console.error("Erro ao deletar usuário:", error);
      return reply.status(500).send({ error: "Erro ao deletar usuário" });
    }
  });

  // Marcar usuário para resetar senha no próximo acesso - Apenas admin e master
  app.post(
    "/usuarios/:uuid/require-password-reset",
    { preHandler: [authorize("admin", "master")] },
    async (request, reply) => {
      try {
        const { uuid } = request.params as { uuid: string };

        const usuario = await prisma.usuario.findUnique({
          where: { uuid },
        });

        if (!usuario) {
          return reply.status(404).send({ error: "Usuário não encontrado" });
        }

        // Marcar que o usuário deve resetar a senha no próximo login
        await prisma.usuario.update({
          where: { uuid },
          data: {
            deveResetarSenha: true,
          },
        });

        return reply.send({
          message: "Usuário marcado para resetar senha no próximo acesso",
        });
      } catch (error) {
        console.error("Erro ao marcar reset de senha:", error);
        return reply.status(500).send({ error: "Erro ao marcar reset de senha" });
      }
    }
  );

  // Listar roles disponíveis
  app.get("/usuarios/roles/list", { preHandler: [authorize("admin", "master")] }, async (request, reply) => {
    try {
      const roles = await prisma.role.findMany({
        orderBy: { nome: "asc" },
      });
      return reply.send(roles);
    } catch (error) {
      console.error("Erro ao listar roles:", error);
      return reply.status(500).send({ error: "Erro ao listar roles" });
    }
  });
}
