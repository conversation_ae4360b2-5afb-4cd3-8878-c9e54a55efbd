import { type SignOptions } from "jsonwebtoken";

export const appEnv = {
  PORT: process.env.PORT ? parseInt(process.env.PORT) : 3000,
  FRONT_END_URL: process.env.FRONT_END_URL || "http://localhost:5173",
  NODE_ENV: process.env.NODE_ENV,
  LOG_LEVEL: process.env.LOG_LEVEL,
  JWT_SECRET: process.env.JWT_SECRET || "asd98a8hsf9buisn3453450-9fndgbun88*",
  JWT_EXPIRES_IN: (process.env.JWT_EXPIRES_IN || "2d") as SignOptions["expiresIn"],
  SEND_WELCOME_EMAIL: process.env.SEND_WELCOME_EMAIL === "true",
  GS2_AWS_UTILS_URL: process.env.GS2_AWS_UTILS_URL,
  GS2_AWS_UTILS_API_KEY: process.env.GS2_AWS_UTILS_API_KEY,
  AWS_REGION: process.env.AWS_REGION,
  AWS_ACCESS_KEY_ID: process.env.AWS_ACCESS_KEY_ID,
  AWS_SECRET_ACCESS_KEY: process.env.AWS_SECRET_ACCESS_KEY,
  AWS_SES_FROM_EMAIL: process.env.AWS_SES_FROM_EMAIL,
} as const;
