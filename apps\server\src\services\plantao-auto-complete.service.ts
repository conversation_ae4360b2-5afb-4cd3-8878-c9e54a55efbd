import { PrismaClient, type Plantao, type Fechamento } from "@prisma/client";
import { getCurrentDate } from "@shared/date";
import { StatusFechamento } from "@shared/types";

export class PlantaoAutoCompleteService {
  constructor(private prisma: PrismaClient) {}

  /**
   * Automatically mark plantao as completed when fechamento is approved
   */
  async handleFechamentoApproved(fechamentoId: number): Promise<Plantao | null> {
    try {
      // Get the fechamento with plantao details
      const fechamento = await this.prisma.fechamento.findUnique({
        where: { id: fechamentoId },
        include: {
          plantao: {
            include: {
              diasPlantao: true,
            },
          },
        },
      });

      if (!fechamento || !fechamento.plantao) {
        console.error(`Fechamento ${fechamentoId} not found or has no associated plantao`);
        return null;
      }

      // Check if fechamento is approved
      if (fechamento.status !== StatusFechamento.APROVADO) {
        console.log(`Fechamento ${fechamentoId} is not approved, skipping auto-complete`);
        return null;
      }

      // Check if plantao is already completed
      if (fechamento.plantao.concluidoEm) {
        console.log(`Plantao ${fechamento.plantao.id} is already completed`);
        return fechamento.plantao;
      }

      // Check if all conditions for auto-completion are met
      const shouldAutoComplete = await this.checkAutoCompleteConditions(fechamento);

      if (!shouldAutoComplete) {
        console.log(`Plantao ${fechamento.plantao.id} does not meet auto-complete conditions`);
        return null;
      }

      // Mark plantao as completed
      const updatedPlantao = await this.prisma.plantao.update({
        where: { id: fechamento.plantao.id },
        data: {
          concluidoEm: getCurrentDate(),
        },
      });

      console.log(`Plantao ${updatedPlantao.id} automatically marked as completed`);

      // Create audit log
      await this.createAuditLog(fechamento.plantao.id, fechamentoId);

      return updatedPlantao;
    } catch (error) {
      console.error("Error in handleFechamentoApproved:", error);
      throw error;
    }
  }

  /**
   * Check if plantao meets conditions for auto-completion
   */
  private async checkAutoCompleteConditions(
    fechamento: Fechamento & { plantao: Plantao & { diasPlantao: any[] } }
  ): Promise<boolean> {
    const { plantao } = fechamento;

    // Condition 1: Fechamento must be for the last month of the plantao
    const isLastMonth = await this.isLastMonthOfPlantao(plantao);
    if (!isLastMonth) {
      return false;
    }

    // Condition 2: At least 80% of scheduled days must be worked
    const workedPercentage = await this.calculateWorkedDaysPercentage(plantao);
    if (workedPercentage < 80) {
      return false;
    }

    // Condition 3: Removed - 'ativo' field no longer exists in schema

    // Condition 4: Current date must be past the dataFinal (if set)
    if (plantao.dataFinal && getCurrentDate() < new Date(plantao.dataFinal)) {
      return false;
    }

    return true;
  }

  /**
   * Check if the fechamento is for the last month of the plantao
   */
  private async isLastMonthOfPlantao(plantao: Plantao): Promise<boolean> {
    if (!plantao.dataFinal) {
      // If no end date, we can't determine if it's the last month
      return false;
    }

    const dataFinal = new Date(plantao.dataFinal);
    const lastMonth = dataFinal.getMonth() + 1; // JavaScript months are 0-indexed
    const lastYear = dataFinal.getFullYear();

    // Check if there's a fechamento for the last month
    // Como não temos mais mes/ano no fechamento, vamos buscar fechamentos do plantão
    // e verificar se algum foi aprovado recentemente
    const lastMonthFechamento = await this.prisma.fechamento.findFirst({
      where: {
        plantaoId: plantao.id,
        status: StatusFechamento.APROVADO,
        createdAt: {
          gte: new Date(lastYear, lastMonth - 1, 1),
          lt: new Date(lastYear, lastMonth, 1),
        },
      },
    });

    return !!lastMonthFechamento;
  }

  /**
   * Calculate the percentage of worked days
   */
  private async calculateWorkedDaysPercentage(plantao: Plantao & { diasPlantao: any[] }): Promise<number> {
    if (!plantao.diasPlantao || plantao.diasPlantao.length === 0) {
      return 0;
    }

    const totalDays = plantao.diasPlantao.length;
    const workedDays = plantao.diasPlantao.filter(
      (dia) =>
        dia.presencaDiaPlantao &&
        dia.presencaDiaPlantao.length > 0 &&
        dia.presencaDiaPlantao.some((p: any) => p.status === "APROVADO")
    ).length;

    return (workedDays / totalDays) * 100;
  }

  /**
   * Create audit log for auto-completion
   */
  private async createAuditLog(plantaoId: number, fechamentoId: number): Promise<void> {
    await this.prisma.auditLog.create({
      data: {
        operation: "UPDATE",
        tableName: "plantoes",
        recordId: plantaoId.toString(),
        description: `Plantão automatically marked as completed due to approved fechamento ${fechamentoId}`,
        metadata: {
          fechamentoId,
          autoCompleted: true,
          completedAt: getCurrentDate().toISOString(),
        },
      },
    });
  }

  /**
   * Process all plantoes that might be ready for auto-completion
   */
  async processAutoCompletions(): Promise<number> {
    try {
      // Find all approved fechamentos from the last 30 days
      const thirtyDaysAgo = getCurrentDate();
      thirtyDaysAgo.setDate(thirtyDaysAgo.getDate() - 30);

      const recentApprovedFechamentos = await this.prisma.fechamento.findMany({
        where: {
          status: StatusFechamento.APROVADO,
          aprovadoEm: {
            gte: thirtyDaysAgo,
          },
          plantao: {
            concluidoEm: null, // Only plantoes not yet completed
          },
        },
        include: {
          plantao: {
            include: {
              diasPlantao: true,
            },
          },
        },
      });

      let completedCount = 0;

      for (const fechamento of recentApprovedFechamentos) {
        const result = await this.handleFechamentoApproved(fechamento.id);
        if (result) {
          completedCount++;
        }
      }

      console.log(`Auto-completion process completed. ${completedCount} plantoes marked as completed.`);
      return completedCount;
    } catch (error) {
      console.error("Error in processAutoCompletions:", error);
      throw error;
    }
  }

  /**
   * Revert auto-completion if fechamento is rejected after approval
   */
  async handleFechamentoRejected(fechamentoId: number): Promise<Plantao | null> {
    try {
      const fechamento = await this.prisma.fechamento.findUnique({
        where: { id: fechamentoId },
        include: {
          plantao: true,
        },
      });

      if (!fechamento || !fechamento.plantao) {
        return null;
      }

      // Check if plantao was auto-completed due to this fechamento
      const auditLog = await this.prisma.auditLog.findFirst({
        where: {
          tableName: "plantoes",
          recordId: fechamento.plantao.id.toString(),
          metadata: {
            path: ["fechamentoId"] as any,
            equals: fechamentoId,
          },
        },
      });

      if (!auditLog) {
        // Plantao was not auto-completed due to this fechamento
        return null;
      }

      // Revert the completion
      const updatedPlantao = await this.prisma.plantao.update({
        where: { id: fechamento.plantao.id },
        data: {
          concluidoEm: null,
        },
      });

      // Create audit log for reversion
      await this.prisma.auditLog.create({
        data: {
          operation: "UPDATE",
          tableName: "plantoes",
          recordId: updatedPlantao.id.toString(),
          description: `Plantão completion reverted due to rejected fechamento ${fechamentoId}`,
          metadata: {
            fechamentoId,
            autoReverted: true,
            revertedAt: getCurrentDate().toISOString(),
          },
        },
      });

      console.log(`Plantao ${updatedPlantao.id} completion reverted due to rejected fechamento`);
      return updatedPlantao;
    } catch (error) {
      console.error("Error in handleFechamentoRejected:", error);
      throw error;
    }
  }
}
