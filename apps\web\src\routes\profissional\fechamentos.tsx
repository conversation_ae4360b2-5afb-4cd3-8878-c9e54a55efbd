import { <PERSON>, Card<PERSON>ontent, <PERSON><PERSON>eader, CardTitle } from "@/components/ui/card";
import { useQuery } from "@tanstack/react-query";
import { api, type Fechamento } from "@/lib/api";
import { Table, TableBody, TableCell, TableHead, TableHeader, TableRow } from "@/components/ui/table";
import { createFileRoute } from "@tanstack/react-router";

async function fetchProfissionalFechamentos(): Promise<Fechamento[]> {
  return api.get("/profissionais/me/fechamentos");
}

function ProfissionalFechamentos() {
  const { data: fechamentos, isLoading } = useQuery({
    queryKey: ["profissional-fechamentos"],
    queryFn: fetchProfissionalFechamentos,
  });

  return (
    <div className="space-y-6">
      <h1 className="text-3xl font-bold">Meus Fechamentos</h1>
      <Card>
        <CardHeader>
          <CardTitle>Histórico de Fechamentos</CardTitle>
        </CardHeader>
        <CardContent>
          {isLoading ? (
            <p>Carregando...</p>
          ) : (
            <Table>
              <TableHeader>
                <TableRow>
                  <TableHead>Mês/Ano</TableHead>
                  <TableHead>Valor</TableHead>
                  <TableHead>Status</TableHead>
                  <TableHead>Data</TableHead>
                </TableRow>
              </TableHeader>
              <TableBody>
                {fechamentos?.map((fechamento) => (
                  <TableRow key={fechamento.id}>
                    <TableCell>
                      {fechamento.mes}/{fechamento.ano}
                    </TableCell>
                    <TableCell>
                      {new Intl.NumberFormat("pt-BR", { style: "currency", currency: "BRL" }).format(
                        fechamento.totalValor
                      )}
                    </TableCell>
                    <TableCell>{fechamento.status}</TableCell>
                    <TableCell>{new Date(fechamento.createdAt).toLocaleDateString()}</TableCell>
                  </TableRow>
                ))}
              </TableBody>
            </Table>
          )}
        </CardContent>
      </Card>
    </div>
  );
}

export const Route = createFileRoute("/profissional/fechamentos")({ component: ProfissionalFechamentos });
