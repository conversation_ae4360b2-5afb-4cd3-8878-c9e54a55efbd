#!/usr/bin/env node

/**
 * Utilitário para filtrar e analisar logs da aplicação
 *
 * Uso:
 * yarn logs                    # Mostra todos os logs
 * yarn logs:error              # Mostra apenas erros
 * yarn logs:slow               # Mostra requisições lentas (>100ms)
 * yarn logs:stats              # Mostra estatísticas dos logs
 */

import { createReadStream } from "fs";
import { createInterface } from "readline";
import { parse } from "path";

interface LogEntry {
  level: number;
  time: number;
  msg: string;
  method?: string;
  url?: string;
  statusCode?: number;
  responseTime?: number;
  err?: any;
}

class LogAnalyzer {
  private stats = {
    total: 0,
    byLevel: new Map<string, number>(),
    byStatus: new Map<number, number>(),
    byEndpoint: new Map<string, { count: number; totalTime: number }>(),
    errors: [] as any[],
    slowRequests: [] as any[],
  };

  private levelNames = new Map([
    [10, "TRACE"],
    [20, "DEBUG"],
    [30, "INFO"],
    [40, "WARN"],
    [50, "ERROR"],
    [60, "FATAL"],
  ]);

  analyze(line: string) {
    try {
      const log: LogEntry = JSON.parse(line);
      this.stats.total++;

      // Count by level
      const levelName = this.levelNames.get(log.level) || "UNKNOWN";
      this.stats.byLevel.set(levelName, (this.stats.byLevel.get(levelName) || 0) + 1);

      // Count by status code
      if (log.statusCode) {
        this.stats.byStatus.set(log.statusCode, (this.stats.byStatus.get(log.statusCode) || 0) + 1);
      }

      // Track endpoint performance
      if (log.method && log.url && log.responseTime) {
        const key = `${log.method} ${log.url}`;
        const current = this.stats.byEndpoint.get(key) || {
          count: 0,
          totalTime: 0,
        };
        current.count++;
        current.totalTime += log.responseTime;
        this.stats.byEndpoint.set(key, current);

        // Track slow requests
        if (log.responseTime > 100) {
          this.stats.slowRequests.push({
            method: log.method,
            url: log.url,
            responseTime: log.responseTime,
            time: new Date(log.time),
          });
        }
      }

      // Track errors
      if (log.level >= 50 || log.err) {
        this.stats.errors.push({
          msg: log.msg,
          err: log.err,
          time: new Date(log.time),
        });
      }
    } catch (e) {
      // Skip non-JSON lines
    }
  }

  printStats() {
    console.log("\n📊 LOG STATISTICS");
    console.log("═".repeat(60));

    // Overall stats
    console.log(`\n📈 Total Logs: ${this.stats.total}`);

    // By level
    console.log("\n📍 By Level:");
    for (const [level, count] of this.stats.byLevel) {
      const percentage = ((count / this.stats.total) * 100).toFixed(1);
      console.log(`  ${level}: ${count} (${percentage}%)`);
    }

    // By status code
    if (this.stats.byStatus.size > 0) {
      console.log("\n🔢 By Status Code:");
      const sortedStatuses = Array.from(this.stats.byStatus.entries()).sort((a, b) => a[0] - b[0]);
      for (const [status, count] of sortedStatuses) {
        const emoji = status >= 500 ? "💥" : status >= 400 ? "⚠️" : "✅";
        console.log(`  ${emoji} ${status}: ${count}`);
      }
    }

    // Top endpoints by request count
    if (this.stats.byEndpoint.size > 0) {
      console.log("\n🎯 Top Endpoints:");
      const sorted = Array.from(this.stats.byEndpoint.entries())
        .sort((a, b) => b[1].count - a[1].count)
        .slice(0, 10);

      for (const [endpoint, data] of sorted) {
        const avgTime = (data.totalTime / data.count).toFixed(0);
        console.log(`  ${endpoint}: ${data.count} reqs, avg ${avgTime}ms`);
      }
    }

    // Slow requests
    if (this.stats.slowRequests.length > 0) {
      console.log(`\n🐌 Slowest Requests (${this.stats.slowRequests.length}):`);
      const sorted = this.stats.slowRequests.sort((a, b) => b.responseTime - a.responseTime).slice(0, 5);

      for (const req of sorted) {
        console.log(`  ${req.method} ${req.url}: ${req.responseTime.toFixed(0)}ms`);
      }
    }

    // Recent errors
    if (this.stats.errors.length > 0) {
      console.log(`\n❌ Recent Errors (${this.stats.errors.length}):`);
      const recent = this.stats.errors.slice(-5);

      for (const error of recent) {
        console.log(`  [${error.time.toISOString()}] ${error.msg}`);
        if (error.err?.message) {
          console.log(`    └─ ${error.err.message}`);
        }
      }
    }

    console.log("\n" + "═".repeat(60));
  }
}

// Main execution
async function main() {
  const mode = process.argv[2] || "all";
  const logFile = process.argv[3] || "./logs/app.log";

  if (mode === "stats") {
    const analyzer = new LogAnalyzer();
    const rl = createInterface({
      input: createReadStream(logFile),
      crlfDelay: Infinity,
    });

    for await (const line of rl) {
      analyzer.analyze(line);
    }

    analyzer.printStats();
  } else {
    console.log("📋 Log Filter Utility");
    console.log("Usage:");
    console.log("  yarn logs:stats    # Show statistics");
    console.log("  yarn logs:error    # Show errors only");
    console.log("  yarn logs:slow     # Show slow requests");
  }
}

if (require.main === module) {
  main().catch(console.error);
}

export { LogAnalyzer };
