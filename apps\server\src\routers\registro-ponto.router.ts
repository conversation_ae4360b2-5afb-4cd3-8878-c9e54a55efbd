import { z } from "zod";
import { prisma } from "@lib/prisma";
import { StatusPresencaDiaPlantao } from "@shared/types";

import {
  createDateWithTime,
  getCurrentISOString,
  createLocalDate,
  createEndOfPeriodDate,
  getStartOfMonth,
  getEndOfMonth,
  parseUTCDate,
  getMesFromDate,
  getAnoFromDate,
} from "@shared/date";
import {
  createLocalDateInTimezone,
  createDateWithTimeInTimezone,
  getCurrentDateInTimezone,
  getStartOfMonthInTimezone,
  getEndOfMonthInTimezone,
} from "@shared/date";
import type { FastifyTypedInstance } from "@/types";

// Função auxiliar para tratar conversão de data
function convertToDate(data: string | Date): Date {
  return typeof data === "string" ? parseUTCDate(data) : parseUTCDate(data.toISOString());
}

// Schema para criar/atualizar registro
const presencaDiaPlantaoSchema = z.object({
  plantaoId: z.uuid(),
  dia: z.number().min(1).max(31),
  mes: z.number().min(1).max(12).optional(), // Agora opcional - será derivado de dataInicial
  ano: z.number().min(2020).max(2030).optional(), // Agora opcional - será derivado de dataInicial
  horaEntrada: z.string().optional(),
  horaSaida: z.string().optional(),
  intervalo: z.string().optional(),
  observacao: z.string().optional(),
});

// Schema para atualizar registro específico (via ID)
const updatePresencaDiaPlantaoSchema = z.object({
  diaPlantaoId: z.number().optional(),
  horaEntrada: z.string().optional(),
  horaSaida: z.string().optional(),
  intervalo: z.string().optional(),
  observacao: z.string().optional(),
  tempoGlosado: z.number().min(0).optional(),
  justificativaGlosa: z.string().optional(),
});

// Schema para batch update
const batchRegistroSchema = z.object({
  registros: z.array(
    z.object({
      data: z.string(),
      entrada: z.string().optional(),
      saida: z.string().optional(),
      intervalo: z.string().optional(),
      observacao: z.string().optional(),
    })
  ),
});

// Schema para aprovar em lote
const aprovarLoteSchema = z.object({
  registroIds: z.array(z.number()),
});

// Schema para enviar para fechamento
const enviarFechamentoSchema = z.object({
  registroIds: z.array(z.number()),
  plantaoId: z.uuid(),
});

// Schema para adicionar dias ao plantão
const adicionarDiasSchema = z.object({
  datas: z.array(z.string().regex(/^\d{4}-\d{2}-\d{2}$/)), // YYYY-MM-DD format
});

// Schema para adicionar um dia específico
const adicionarDiaEspecificoSchema = z.object({
  data: z.string(),
});

// Schema para presenca automático
const presencaAutomaticoSchema = z.object({
  mes: z.number().min(1).max(12).optional(), // Agora opcional - será derivado de dataInicial
  ano: z.number().optional(), // Agora opcional - será derivado de dataInicial
});

// Função para validar sobreposição de horários
function hasTimeOverlap(
  newEntry: Date | null,
  newExit: Date | null,
  existingEntry: Date | null,
  existingExit: Date | null
): boolean {
  // Se não há entrada nova, não há sobreposição
  if (!newEntry) return false;

  // Se existe uma entrada em aberto (sem saída), não pode registrar nova entrada
  if (existingEntry && !existingExit) return true;

  // Se não há registro existente completo, não há sobreposição
  if (!existingEntry) return false;

  // Se novo registro só tem entrada, verificar se não está dentro de período existente
  if (!newExit) {
    if (existingExit) {
      // Verifica se nova entrada está entre entrada e saída existente
      return newEntry >= existingEntry && newEntry <= existingExit;
    }
    return false; // Existente sem saída, já validado acima
  }

  // Se registro existente não tem saída, verificar se nova entrada/saída não sobrepõe
  if (!existingExit) return true;

  // Ambos os registros têm entrada e saída - verificar sobreposição
  const newStart = newEntry.getTime();
  const newEnd = newExit.getTime();
  const existingStart = existingEntry.getTime();
  const existingEnd = existingExit.getTime();

  // Verificar se há qualquer sobreposição
  return !(newEnd <= existingStart || newStart >= existingEnd);
}

// Função para validar registros de um dia
async function validateTimeRegistration(
  diaPlantaoId: number,
  newEntry: Date | null,
  newExit: Date | null,
  excludeRegistroId?: number
): Promise<{ valid: boolean; error?: string }> {
  const existingRegistros = await prisma.presencaDiaPlantao.findMany({
    where: {
      diaPlantaoId,
      ...(excludeRegistroId && { id: { not: excludeRegistroId } }),
    },
  });

  // Verificar se há entrada em aberto
  const openEntry = existingRegistros.find((r) => r.horaEntrada && !r.horaSaida);
  if (openEntry && newEntry) {
    return {
      valid: false,
      error: "Existe uma entrada em aberto. Complete o registro anterior antes de criar um novo.",
    };
  }

  // Verificar sobreposição com registros existentes
  for (const existing of existingRegistros) {
    if (hasTimeOverlap(newEntry, newExit, existing.horaEntrada, existing.horaSaida)) {
      const existingEntryTime =
        existing.horaEntrada?.toLocaleTimeString("pt-BR", { hour: "2-digit", minute: "2-digit" }) || "";
      const existingExitTime =
        existing.horaSaida?.toLocaleTimeString("pt-BR", { hour: "2-digit", minute: "2-digit" }) || "em aberto";

      return {
        valid: false,
        error: `Horário conflita com registro existente (${existingEntryTime} - ${existingExitTime})`,
      };
    }
  }

  return { valid: true };
}

// Função para fazer presenca automático de um plantão
async function criarPresencaAutomatico(plantaoId: number, mes: number, ano: number, fusoHorario: string) {
  const plantao = await prisma.plantao.findUnique({
    where: { id: plantaoId },
    include: {
      diasPlantao: {
        include: {
          presencaDiaPlantao: true,
        },
      },
    },
  });

  if (!plantao) {
    throw new Error("Plantão não encontrado");
  }

  const startOfMonthDate = getStartOfMonthInTimezone(ano, mes, fusoHorario);
  const endOfMonthDate = getEndOfMonthInTimezone(ano, mes, fusoHorario);

  // Filtrar apenas os dias do mês informado
  const diasPlantao = plantao.diasPlantao.filter((dia) => {
    const diaData = convertToDate(dia.data);
    return diaData >= startOfMonthDate && diaData <= endOfMonthDate;
  });

  const registrosCriados = [];
  const registrosAtualizados = [];
  const diasCompletos = [];
  const diasSemHorarioPrevisto = [];

  for (const diaPlantao of diasPlantao) {
    if (!diaPlantao.horaEntrada || !diaPlantao.horaSaida) {
      diasSemHorarioPrevisto.push(convertToDate(diaPlantao.data).getDate());
      continue;
    }

    const registrosExistentes = diaPlantao.presencaDiaPlantao || [];

    // Verificar se já tem registro completo (entrada e saída)
    const registroCompleto = registrosExistentes.find((r) => r.horaEntrada && r.horaSaida);
    if (registroCompleto) {
      diasCompletos.push(convertToDate(diaPlantao.data).getDate());
      continue;
    }

    // Verificar se tem registro incompleto
    const registroIncompleto = registrosExistentes.find((r) => r.horaEntrada || r.horaSaida);

    const [entradaH, entradaM] = diaPlantao.horaEntrada.split(":").map(Number);
    const [saidaH, saidaM] = diaPlantao.horaSaida.split(":").map(Number);

    const diaData = convertToDate(diaPlantao.data);
    const horaEntradaPrevista = createDateWithTimeInTimezone(
      diaData.getFullYear(),
      diaData.getMonth() + 1,
      diaData.getDate(),
      entradaH,
      entradaM,
      fusoHorario
    );
    const horaSaidaPrevista = createDateWithTimeInTimezone(
      diaData.getFullYear(),
      diaData.getMonth() + 1,
      diaData.getDate(),
      saidaH,
      saidaM,
      fusoHorario
    );

    if (registroIncompleto) {
      // Completar registro existente
      const horaEntradaFinal = registroIncompleto.horaEntrada || horaEntradaPrevista;
      const horaSaidaFinal = registroIncompleto.horaSaida || horaSaidaPrevista;

      let horasTrabalhadas = 0;
      let valorEstimado = 0;

      if (horaEntradaFinal && horaSaidaFinal) {
        const diffMs = horaSaidaFinal.getTime() - horaEntradaFinal.getTime();
        horasTrabalhadas = diffMs / (1000 * 60 * 60);

        const intervaloFinal = registroIncompleto.intervalo || diaPlantao.intervalo || plantao.intervalo || "01:00";
        if (intervaloFinal) {
          const [ih, im] = intervaloFinal.split(":").map(Number);
          const intervaloHoras = ih + im / 60;
          horasTrabalhadas -= intervaloHoras;
        }

        if (horasTrabalhadas > 0) {
          if (plantao.tipoValor === "HORA" ? plantao.valorBase : 0) {
            valorEstimado = horasTrabalhadas * (plantao.tipoValor === "HORA" ? plantao.valorBase : 0);
          } else if (plantao.tipoValor === "PLANTAO" || plantao.tipoValor === "DIARIA" ? plantao.valorBase : 0) {
            valorEstimado = plantao.tipoValor === "PLANTAO" || plantao.tipoValor === "DIARIA" ? plantao.valorBase : 0;
          }
        }
      }

      const registroAtualizado = await prisma.presencaDiaPlantao.update({
        where: { id: registroIncompleto.id },
        data: {
          horaEntrada: horaEntradaFinal,
          horaSaida: horaSaidaFinal,
          intervalo: registroIncompleto.intervalo || diaPlantao.intervalo || plantao.intervalo || "01:00",
          horasTrabalhadas,
          valorEstimado,
        },
      });

      registrosAtualizados.push(registroAtualizado);
    } else {
      // Criar novo registro completo
      let horasTrabalhadas = 0;
      let valorEstimado = 0;

      if (horaEntradaPrevista && horaSaidaPrevista) {
        const diffMs = horaSaidaPrevista.getTime() - horaEntradaPrevista.getTime();
        horasTrabalhadas = diffMs / (1000 * 60 * 60);

        const intervalo = diaPlantao.intervalo || plantao.intervalo || "01:00";
        if (intervalo) {
          const [ih, im] = intervalo.split(":").map(Number);
          const intervaloHoras = ih + im / 60;
          horasTrabalhadas -= intervaloHoras;
        }

        if (horasTrabalhadas > 0) {
          if (plantao.tipoValor === "HORA" ? plantao.valorBase : 0) {
            valorEstimado = horasTrabalhadas * (plantao.tipoValor === "HORA" ? plantao.valorBase : 0);
          } else if (plantao.tipoValor === "PLANTAO" || plantao.tipoValor === "DIARIA" ? plantao.valorBase : 0) {
            valorEstimado = plantao.tipoValor === "PLANTAO" || plantao.tipoValor === "DIARIA" ? plantao.valorBase : 0;
          }
        }
      }

      const registro = await prisma.presencaDiaPlantao.create({
        data: {
          diaPlantaoId: diaPlantao.id,
          horaEntrada: horaEntradaPrevista,
          horaSaida: horaSaidaPrevista,
          intervalo: diaPlantao.intervalo || plantao.intervalo || "01:00",
          horasTrabalhadas,
          valorEstimado,
          status: StatusPresencaDiaPlantao.PENDENTE,
        },
      });

      registrosCriados.push(registro);
    }

    // Dia não precisa mais ser marcado como trabalhado
    // O controle é feito através dos registros de presença
  }

  return {
    registrosCriados,
    registrosAtualizados,
    diasCompletos,
    diasSemHorarioPrevisto,
    totalDiasProcessados: diasPlantao.length,
  };
}

export async function presencaDiaPlantaoRouter(fastify: FastifyTypedInstance) {
  // Criar ou atualizar registro de ponto com lógica de check-in/check-out
  fastify.post("/registros-ponto", async (request, reply) => {
    const data = presencaDiaPlantaoSchema.parse(request.body);

    // Buscar o plantão
    const plantao = await prisma.plantao.findUnique({
      where: { uuid: data.plantaoId },
    });

    if (!plantao) {
      return reply.status(404).send({ error: "Plantão não encontrado" });
    }

    // Buscar ou criar o DiaPlantao para a data especificada
    // Usar mes/ano da requisição ou derivar de dataInicial do plantão
    const targetMes = data.mes || getMesFromDate(plantao.dataInicial);
    const targetAno = data.ano || getAnoFromDate(plantao.dataInicial);

    if (!targetMes || !targetAno) {
      return reply.status(400).send({ error: "Não foi possível determinar o mês/ano do presenca" });
    }

    const dataDoPresenca = createLocalDateInTimezone(targetAno, targetMes, data.dia, request.fusoHorario);

    let diaPlantao = await prisma.diaPlantao.findFirst({
      where: {
        plantaoId: plantao.id,
        data: dataDoPresenca,
      },
    });

    // Se não existe o DiaPlantao, criar
    if (!diaPlantao) {
      diaPlantao = await prisma.diaPlantao.create({
        data: {
          plantaoId: plantao.id,
          data: dataDoPresenca,
          horaEntrada: plantao.horaInicio,
          horaSaida: plantao.horaFim,
          intervalo: plantao.intervalo || "01:00",
        },
      });
    }

    // Buscar registros existentes para este dia
    const registroExistente = await prisma.presencaDiaPlantao.findFirst({
      where: {
        diaPlantaoId: diaPlantao.id,
      },
      orderBy: { createdAt: "desc" },
    });

    // Lógica de check-in/check-out
    let horaEntrada: Date | undefined;
    let horaSaida: Date | undefined;
    let isCheckOut = false;

    if (data.horaEntrada && data.horaSaida) {
      // Registro completo - criar novo registro
      const [hE, mE] = data.horaEntrada.split(":").map(Number);
      const [hS, mS] = data.horaSaida.split(":").map(Number);
      horaEntrada = createDateWithTimeInTimezone(targetAno, targetMes, data.dia, hE, mE, request.fusoHorario);
      horaSaida = createDateWithTimeInTimezone(targetAno, targetMes, data.dia, hS, mS, request.fusoHorario);
    } else if (data.horaEntrada && !data.horaSaida) {
      // Check-in - apenas entrada
      const [h, m] = data.horaEntrada.split(":").map(Number);
      horaEntrada = createDateWithTimeInTimezone(targetAno, targetMes, data.dia, h, m, request.fusoHorario);

      // Verificar se já existe uma entrada em aberto
      if (registroExistente && registroExistente.horaEntrada && !registroExistente.horaSaida) {
        return reply.status(400).send({
          error: "Já existe uma entrada em aberto. Faça o check-out primeiro.",
        });
      }
    } else if (!data.horaEntrada && data.horaSaida) {
      // Check-out - apenas saída
      const [h, m] = data.horaSaida.split(":").map(Number);
      horaSaida = createDateWithTimeInTimezone(targetAno, targetMes, data.dia, h, m, request.fusoHorario);
      isCheckOut = true;

      // Verificar se existe uma entrada em aberto para fazer check-out
      if (!registroExistente || !registroExistente.horaEntrada || registroExistente.horaSaida) {
        return reply.status(400).send({
          error: "Não há entrada em aberto para fazer check-out.",
        });
      }
    } else {
      return reply.status(400).send({
        error: "Deve ser fornecida pelo menos hora de entrada ou saída.",
      });
    }

    let registro;

    if (isCheckOut && registroExistente) {
      // Fazer check-out no registro existente
      const intervalo = data.intervalo || registroExistente.intervalo || diaPlantao.intervalo || "01:00";
      const entradaFinal = registroExistente.horaEntrada!;
      const saidaFinal = horaSaida!;

      // Calcular horas trabalhadas
      const diffMs = saidaFinal.getTime() - entradaFinal.getTime();
      let totalHoras = diffMs / (1000 * 60 * 60);

      if (intervalo) {
        const [ih, im] = intervalo.split(":").map(Number);
        const intervaloHoras = ih + im / 60;
        totalHoras -= intervaloHoras;
      }

      const horasTrabalhadas = Math.max(0, totalHoras);

      // Calcular valor estimado
      let valorEstimado = 0;
      if (horasTrabalhadas > 0) {
        if (plantao.tipoValor === "HORA" ? plantao.valorBase : 0) {
          valorEstimado = horasTrabalhadas * (plantao.tipoValor === "HORA" ? plantao.valorBase : 0);
        } else if (plantao.tipoValor === "PLANTAO" || plantao.tipoValor === "DIARIA" ? plantao.valorBase : 0) {
          valorEstimado = plantao.tipoValor === "PLANTAO" || plantao.tipoValor === "DIARIA" ? plantao.valorBase : 0;
        }
      }

      // Atualizar registro existente com check-out
      registro = await prisma.presencaDiaPlantao.update({
        where: { id: registroExistente.id },
        data: {
          horaSaida: saidaFinal,
          intervalo,
          horasTrabalhadas,
          valorEstimado,
          observacao: data.observacao || registroExistente.observacao,
        },
      });
    } else {
      // Criar novo registro (check-in ou registro completo)
      let horasTrabalhadas: number | undefined;
      let valorEstimado: number | undefined;

      // Calcular horas apenas se tiver entrada e saída
      if (horaEntrada && horaSaida) {
        const diffMs = horaSaida.getTime() - horaEntrada.getTime();
        let totalHoras = diffMs / (1000 * 60 * 60);

        const intervalo = data.intervalo || diaPlantao.intervalo || "01:00";
        if (intervalo) {
          const [ih, im] = intervalo.split(":").map(Number);
          const intervaloHoras = ih + im / 60;
          totalHoras -= intervaloHoras;
        }

        horasTrabalhadas = Math.max(0, totalHoras);

        // Calcular valor estimado
        if (horasTrabalhadas > 0) {
          if (plantao.tipoValor === "HORA" ? plantao.valorBase : 0) {
            valorEstimado = horasTrabalhadas * (plantao.tipoValor === "HORA" ? plantao.valorBase : 0);
          } else if (plantao.tipoValor === "PLANTAO" || plantao.tipoValor === "DIARIA" ? plantao.valorBase : 0) {
            valorEstimado = plantao.tipoValor === "PLANTAO" || plantao.tipoValor === "DIARIA" ? plantao.valorBase : 0;
          }
        }
      }

      // Validar sobreposição apenas se for registro completo
      if (horaEntrada && horaSaida) {
        const validation = await validateTimeRegistration(diaPlantao.id, horaEntrada, horaSaida);
        if (!validation.valid) {
          return reply.status(400).send({ error: validation.error });
        }
      }

      registro = await prisma.presencaDiaPlantao.create({
        data: {
          diaPlantaoId: diaPlantao.id,
          horaEntrada,
          horaSaida,
          intervalo: data.intervalo || diaPlantao.intervalo || "01:00",
          horasTrabalhadas,
          valorEstimado,
          observacao: data.observacao,
          status: StatusPresencaDiaPlantao.PENDENTE,
        },
      });
    }

    // Dia não precisa mais ser marcado como trabalhado
    // O controle é feito através dos registros de presença

    return reply.status(201).send(registro);
  });

  // Atualizar registro específico
  fastify.put("/registros-ponto/:id", async (request, reply) => {
    const { id } = request.params as { id: string };
    const data = updatePresencaDiaPlantaoSchema.parse(request.body);

    const registro = await prisma.presencaDiaPlantao.findUnique({
      where: { id: parseInt(id) },
      include: { diaPlantao: { include: { plantao: true } } },
    });

    if (!registro) {
      return reply.status(404).send({ error: "Registro não encontrado" });
    }

    let horaEntrada: Date | undefined;
    let horaSaida: Date | undefined;
    let horasTrabalhadas: number | undefined;
    let valorEstimado: number | undefined;

    // Converter strings de hora para DateTime
    if (data.horaEntrada) {
      const [h, m] = data.horaEntrada.split(":").map(Number);
      const diaData = convertToDate(registro.diaPlantao.data);
      horaEntrada = createDateWithTimeInTimezone(
        diaData.getFullYear(),
        diaData.getMonth() + 1,
        diaData.getDate(),
        h,
        m,
        request.fusoHorario
      );
    }

    if (data.horaSaida) {
      const [h, m] = data.horaSaida.split(":").map(Number);
      const diaData = convertToDate(registro.diaPlantao.data);
      horaSaida = createDateWithTimeInTimezone(
        diaData.getFullYear(),
        diaData.getMonth() + 1,
        diaData.getDate(),
        h,
        m,
        request.fusoHorario
      );
    }

    // Validar sobreposição de horários (excluindo o registro atual)
    const validation = await validateTimeRegistration(
      registro.diaPlantaoId,
      horaEntrada || registro.horaEntrada,
      horaSaida || registro.horaSaida,
      registro.id
    );
    if (!validation.valid) {
      return reply.status(400).send({ error: validation.error });
    }

    // Calcular horas trabalhadas
    const entradaFinal = horaEntrada || registro.horaEntrada;
    const saidaFinal = horaSaida || registro.horaSaida;
    const intervaloFinal = data.intervalo || registro.intervalo;

    if (entradaFinal && saidaFinal) {
      const diffMs = saidaFinal.getTime() - entradaFinal.getTime();
      let totalHoras = diffMs / (1000 * 60 * 60);

      if (intervaloFinal) {
        const [ih, im] = intervaloFinal.split(":").map(Number);
        const intervaloHoras = ih + im / 60;
        totalHoras -= intervaloHoras;
      }

      horasTrabalhadas = totalHoras;

      // Calcular valor estimado baseado no tipoValor
      if (horasTrabalhadas > 0) {
        const plantao = registro.diaPlantao.plantao;
        if (plantao.tipoValor === "HORA") {
          valorEstimado = horasTrabalhadas * plantao.valorBase;
        } else if (plantao.tipoValor === "PLANTAO" || plantao.tipoValor === "DIARIA") {
          valorEstimado = plantao.valorBase;
        } else if (plantao.tipoValor === "MENSAL") {
          // Para mensal, podemos fazer proporcional por dia
          valorEstimado = plantao.valorBase / 30;
        }
      }
    }

    const updated = await prisma.presencaDiaPlantao.update({
      where: { id: parseInt(id) },
      data: {
        horaEntrada: horaEntrada || registro.horaEntrada,
        horaSaida: horaSaida || registro.horaSaida,
        intervalo: data.intervalo !== undefined ? data.intervalo : registro.intervalo,
        horasTrabalhadas,
        valorEstimado,
        observacao: data.observacao !== undefined ? data.observacao : registro.observacao,
        tempoGlosado: data.tempoGlosado !== undefined ? data.tempoGlosado : registro.tempoGlosado,
        justificativaGlosa:
          data.justificativaGlosa !== undefined ? data.justificativaGlosa : registro.justificativaGlosa,
      },
    });

    return reply.send(updated);
  });

  // Deletar registro
  fastify.delete("/registros-ponto/:id", async (request, reply) => {
    const { id } = request.params as { id: string };

    const registro = await prisma.presencaDiaPlantao.findUnique({
      where: { id: parseInt(id) },
    });

    if (!registro) {
      return reply.status(404).send({ error: "Registro não encontrado" });
    }

    await prisma.presencaDiaPlantao.delete({
      where: { id: parseInt(id) },
    });

    // Verificar se ainda há registros para este dia
    const remainingRegistros = await prisma.presencaDiaPlantao.count({
      where: { diaPlantaoId: registro.diaPlantaoId },
    });

    // Dia não precisa mais ser marcado, controle é feito pelos registros de presença

    return reply.status(204).send();
  });

  // Aprovar registros em lote
  fastify.post("/registros-ponto/aprovar-lote", async (request, reply) => {
    const { registroIds } = aprovarLoteSchema.parse(request.body);

    // TODO: Obter usuário autenticado quando implementar autenticação
    const usuarioId = "system"; // Temporário
    const nomeUsuario = "Sistema"; // Temporário

    const results = [];
    const errors = [];

    for (const registroId of registroIds) {
      const registro = await prisma.presencaDiaPlantao.findUnique({
        where: { id: registroId },
      });

      if (!registro) {
        errors.push({
          registroId,
          error: "Registro não encontrado",
        });

        continue;
      }

      // Validar se horaEntrada e horaSaida estão preenchidos
      if (!registro.horaEntrada || !registro.horaSaida) {
        errors.push({
          registroId,
          error: "Registro incompleto - faltam hora de entrada ou saída",
        });

        continue;
      }

      // Preparar histórico de aprovação
      const historicoAtual = (registro.historicoAprovacoes as any[]) || [];
      const novoHistorico = [
        ...historicoAtual,
        {
          usuarioId,
          nome: nomeUsuario,
          acao: "APROVADO",
          data: getCurrentISOString(),
          observacao: "Aprovação em lote",
        },
      ];

      const updated = await prisma.presencaDiaPlantao.update({
        where: { id: registroId },
        data: {
          status: StatusPresencaDiaPlantao.APROVADO,
          historicoAprovacoes: novoHistorico,
        },
      });

      results.push(updated);
    }

    // Se houver erros, retornar com status parcial
    if (errors.length > 0) {
      return reply.send({
        message: `${results.length} registros aprovados, ${errors.length} registros com erro`,
        registros: results,
        errors: errors,
      });
    }

    return reply.send({
      message: `${results.length} registros aprovados com sucesso`,
      registros: results,
    });
  });

  // Enviar registros para fechamento
  fastify.post("/registros-ponto/enviar-fechamento", async (request, reply) => {
    const { registroIds, plantaoId } = enviarFechamentoSchema.parse(request.body);

    // Buscar plantão e profissional
    const plantao = await prisma.plantao.findUnique({
      where: { uuid: plantaoId },
      include: { profissional: true },
    });

    if (!plantao) {
      return reply.status(404).send({ error: "Plantão não encontrado" });
    }

    // Verificar se todos os registros estão aprovados
    const registros = await prisma.presencaDiaPlantao.findMany({
      where: { id: { in: registroIds } },
    });

    const registrosNaoAprovados = registros.filter((r) => r.status !== "APROVADO");

    if (registrosNaoAprovados.length > 0) {
      return reply.status(400).send({
        error: "Só é possível enviar para fechamento presencas aprovados.",
        registrosNaoAprovados: registrosNaoAprovados.map((r) => r.id),
      });
    }

    // Verificar se algum dos registros já possui fechamento
    const registrosComFechamento = registros.filter((r) => r.fechamentoId !== null);

    if (registrosComFechamento.length > 0) {
      return reply.status(400).send({
        error: "Alguns registros já foram enviados para fechamento.",
        registrosComFechamento: registrosComFechamento.map((r) => r.id),
      });
    }

    // Sempre criar um novo fechamento
    const totalHoras = registros.reduce((acc, r) => acc + (r.horasTrabalhadas || 0), 0);
    const totalValor = registros.reduce((acc, r) => acc + (r.valorEstimado || 0), 0);
    const diasTrabalhados = registros.filter((r) => r.horasTrabalhadas && r.horasTrabalhadas > 0).length;

    const fechamento = await prisma.fechamento.create({
      data: {
        plantaoId: plantao.id,
        profissionalId: plantao.profissionalId,
        status: "PENDENTE",
        totalHoras,
        totalValor,
        diasTrabalhados,
        diasPrevistos: registros.length,
      },
    });

    // Atualizar registros com o fechamentoId
    await prisma.presencaDiaPlantao.updateMany({
      where: { id: { in: registroIds } },
      data: { fechamentoId: fechamento.id },
    });

    // Buscar o fechamento criado com todas as relações
    const fechamentoCompleto = await prisma.fechamento.findUnique({
      where: { id: fechamento.id },
      include: {
        plantao: {
          include: {
            profissional: {
              include: {
                usuario: true,
              },
            },
            cliente: true,
            localAtendimento: true,
          },
        },
        presencaDiaPlantao: true,
      },
    });

    return reply.send({
      message: `${registroIds.length} registros enviados para fechamento`,
      fechamento: fechamentoCompleto,
    });
  });

  // Batch update de registros
  fastify.post("/plantoes/:plantaoId/registros-ponto/batch", async (request, reply) => {
    const { plantaoId } = request.params as { plantaoId: string };
    const { registros } = batchRegistroSchema.parse(request.body);

    const plantao = await prisma.plantao.findUnique({
      where: { id: Number(plantaoId) },
      include: { diasPlantao: true },
    });

    if (!plantao) {
      return reply.status(404).send({ error: "Plantão não encontrado" });
    }

    const results = [];

    for (const reg of registros) {
      const regData = convertToDate(reg.data);
      const diaPlantao = plantao.diasPlantao.find((d: any) => {
        const dData = convertToDate(d.data);
        return (
          dData.getDate() === regData.getDate() &&
          dData.getMonth() === regData.getMonth() &&
          dData.getFullYear() === regData.getFullYear()
        );
      });

      if (!diaPlantao) continue;

      let horaEntrada: Date | undefined;
      let horaSaida: Date | undefined;
      let horasTrabalhadas: number | undefined;
      let valorEstimado: number | undefined;

      if (reg.entrada) {
        const [h, m] = reg.entrada.split(":").map(Number);
        horaEntrada = createDateWithTimeInTimezone(
          regData.getFullYear(),
          regData.getMonth() + 1,
          regData.getDate(),
          h,
          m,
          request.fusoHorario
        );
      }

      if (reg.saida) {
        const [h, m] = reg.saida.split(":").map(Number);
        horaSaida = createDateWithTimeInTimezone(
          regData.getFullYear(),
          regData.getMonth() + 1,
          regData.getDate(),
          h,
          m,
          request.fusoHorario
        );
      }

      // Validar sobreposição de horários
      const validation = await validateTimeRegistration(diaPlantao.id, horaEntrada || null, horaSaida || null);
      if (!validation.valid) {
        continue; // Pular este registro se houver conflito
      }

      // Calcular horas trabalhadas
      if (horaEntrada && horaSaida) {
        const diffMs = horaSaida.getTime() - horaEntrada.getTime();
        let totalHoras = diffMs / (1000 * 60 * 60);

        if (reg.intervalo) {
          const [ih, im] = reg.intervalo.split(":").map(Number);
          const intervaloHoras = ih + im / 60;
          totalHoras -= intervaloHoras;
        }

        horasTrabalhadas = totalHoras;

        // Calcular valor estimado
        if (horasTrabalhadas > 0) {
          if (plantao.tipoValor === "HORA" ? plantao.valorBase : 0) {
            valorEstimado = horasTrabalhadas * (plantao.tipoValor === "HORA" ? plantao.valorBase : 0);
          } else if (plantao.tipoValor === "PLANTAO" || plantao.tipoValor === "DIARIA" ? plantao.valorBase : 0) {
            valorEstimado = plantao.tipoValor === "PLANTAO" || plantao.tipoValor === "DIARIA" ? plantao.valorBase : 0;
          }
        }
      }

      // Criar novo registro após validação
      const registro = await prisma.presencaDiaPlantao.create({
        data: {
          diaPlantaoId: diaPlantao.id,
          horaEntrada,
          horaSaida,
          intervalo: reg.intervalo || plantao.intervalo || "01:00",
          horasTrabalhadas,
          valorEstimado,
          observacao: reg.observacao,
          status: StatusPresencaDiaPlantao.PENDENTE,
        },
      });

      // Dia não precisa mais ser marcado como trabalhado
      // O controle é feito através dos registros de presença

      results.push(registro);
    }

    return reply.send({ registros: results });
  });

  // Adicionar dias específicos ao plantão
  fastify.post("/plantoes/:plantaoId/adicionar-dias", async (request, reply) => {
    const { plantaoId } = request.params as { plantaoId: string };
    const { datas } = adicionarDiasSchema.parse(request.body);

    const plantao = await prisma.plantao.findUnique({
      where: { id: Number(plantaoId) },
    });

    if (!plantao) {
      return reply.status(404).send({ error: "Plantão não encontrado" });
    }

    // Verificar se as datas estão dentro do período do plantão
    const dataInicial = convertToDate(plantao.dataInicial);
    const dataFinal = plantao.dataFinal ? convertToDate(plantao.dataFinal) : createEndOfPeriodDate();

    const diasAdicionados = [];
    const diasJaExistentes = [];

    for (const dataStr of datas) {
      const data = parseUTCDate(dataStr);
      const dia = data.getDate();

      // Verificar se a data está dentro do período do plantão
      if (data < dataInicial || data > dataFinal) {
        continue; // Pular datas fora do período
      }

      // Verificar se a data já existe para este plantão
      const existingDia = await prisma.diaPlantao.findFirst({
        where: {
          plantaoId: Number(plantaoId),
          data,
        },
      });

      if (existingDia) {
        diasJaExistentes.push(dia);
      } else {
        // Criar novo dia
        const novoDia = await prisma.diaPlantao.create({
          data: {
            plantaoId: Number(plantaoId),
            data,
            horaEntrada: plantao.horaInicio,
            horaSaida: plantao.horaFim,
            intervalo: plantao.intervalo,
          },
        });
        diasAdicionados.push(novoDia);
      }
    }

    return reply.send({
      message: `${diasAdicionados.length} dias adicionados ao plantão`,
      diasAdicionados: diasAdicionados.map((d) => convertToDate(d.data).getDate()),
      diasJaExistentes,
    });
  });

  // Adicionar um dia específico ao plantão
  fastify.post("/plantoes/:plantaoId/adicionar-dia", async (request, reply) => {
    const { plantaoId } = request.params as { plantaoId: string };
    const { data: dataStr } = adicionarDiaEspecificoSchema.parse(request.body);

    const plantao = await prisma.plantao.findUnique({
      where: { id: Number(plantaoId) },
    });

    if (!plantao) {
      return reply.status(404).send({ error: "Plantão não encontrado" });
    }

    const data = parseUTCDate(dataStr);

    // Verificar se a data está dentro do período do plantão
    const dataInicial = convertToDate(plantao.dataInicial);
    const dataFinal = plantao.dataFinal ? convertToDate(plantao.dataFinal) : createEndOfPeriodDate();

    if (data < dataInicial || data > dataFinal) {
      return reply.status(400).send({
        error: "Data fora do período do plantão",
      });
    }

    // Verificar se a data já existe
    const existingDia = await prisma.diaPlantao.findFirst({
      where: {
        plantaoId: Number(plantaoId),
        data,
      },
    });

    if (existingDia) {
      return reply.status(200).send({
        message: "Dia já existe no plantão",
        diaPlantao: existingDia,
      });
    }

    // Criar novo dia
    const novoDia = await prisma.diaPlantao.create({
      data: {
        plantaoId: Number(plantaoId),
        data,
        horaEntrada: plantao.horaInicio,
        horaSaida: plantao.horaFim,
        intervalo: plantao.intervalo,
      },
    });

    return reply.status(201).send({
      message: "Dia adicionado ao plantão com sucesso",
      diaPlantao: novoDia,
    });
  });

  // Fazer presenca automático de um plantão
  fastify.post("/plantoes/:plantaoId/presenca-automatico", async (request, reply) => {
    const { plantaoId } = request.params as { plantaoId: string };
    const data = presencaAutomaticoSchema.parse(request.body);

    try {
      const plantao = await prisma.plantao.findUnique({
        where: { uuid: plantaoId },
      });

      if (!plantao) {
        return reply.status(404).send({ error: "Plantão não encontrado" });
      }

      // Usar mes/ano da requisição ou derivar de dataInicial do plantão
      const targetMes = data.mes || getMesFromDate(plantao.dataInicial);
      const targetAno = data.ano || getAnoFromDate(plantao.dataInicial);

      if (!targetMes || !targetAno) {
        return reply.status(400).send({ error: "Não foi possível determinar o mês/ano do plantão" });
      }

      const resultado = await criarPresencaAutomatico(plantao.id, targetMes, targetAno, request.fusoHorario);

      return reply.send({
        message: `Presença automática concluído para o mês ${targetMes}`,
        registrosCriados: resultado.registrosCriados.length,
        registrosAtualizados: resultado.registrosAtualizados.length,
        diasCompletos: resultado.diasCompletos,
        diasSemHorarioPrevisto: resultado.diasSemHorarioPrevisto,
        totalDiasProcessados: resultado.totalDiasProcessados,
        detalhes: {
          novosRegistros: resultado.registrosCriados.map((r) => ({
            id: r.id,
            diaPlantaoId: r.diaPlantaoId,
            horaEntrada: r.horaEntrada?.toISOString(),
            horaSaida: r.horaSaida?.toISOString(),
            horasTrabalhadas: r.horasTrabalhadas,
            valorEstimado: r.valorEstimado,
          })),
          registrosCompletados: resultado.registrosAtualizados.map((r) => ({
            id: r.id,
            diaPlantaoId: r.diaPlantaoId,
            horaEntrada: r.horaEntrada?.toISOString(),
            horaSaida: r.horaSaida?.toISOString(),
            horasTrabalhadas: r.horasTrabalhadas,
            valorEstimado: r.valorEstimado,
          })),
        },
      });
    } catch (error) {
      return reply.status(400).send({
        error: error instanceof Error ? error.message : "Erro ao criar presenca automático",
      });
    }
  });

  // Buscar presencas agrupados com suporte a mês/ano
  fastify.get("/plantoes/:plantaoId/presencas-agrupados", async (request, reply) => {
    const { plantaoId } = request.params as { plantaoId: string };
    const { mes, ano } = request.query as { mes?: string; ano?: string };

    const plantao = await prisma.plantao.findUnique({
      where: { uuid: plantaoId },
    });

    if (!plantao) {
      return reply.status(404).send({ error: "Plantão não encontrado" });
    }

    // Usar mês/ano da query ou derivar de dataInicial do plantão
    const targetMes = mes ? parseInt(mes) : getMesFromDate(plantao.dataInicial);
    const targetAno = ano ? parseInt(ano) : getAnoFromDate(plantao.dataInicial);

    if (!targetMes || !targetAno) {
      return reply.status(400).send({ error: "Não foi possível determinar o mês/ano do plantão" });
    }

    // Verificar se as datas estão dentro do período do plantão
    const dataInicial = convertToDate(plantao.dataInicial);
    const dataFinal = plantao.dataFinal ? convertToDate(plantao.dataFinal) : createEndOfPeriodDate();
    const targetStartOfMonth = getStartOfMonthInTimezone(targetAno, targetMes, request.fusoHorario);
    const targetEndOfMonth = getEndOfMonthInTimezone(targetAno, targetMes, request.fusoHorario);

    // Verificar se há alguma sobreposição entre o mês solicitado e o período do plantão
    if (targetEndOfMonth < dataInicial || targetStartOfMonth > dataFinal) {
      return reply.status(400).send({
        error: "Mês/ano fora do período do plantão",
      });
    }

    // Buscar apenas os dias já cadastrados para o mês/ano
    const startOfMonthDate = getStartOfMonthInTimezone(targetAno, targetMes, request.fusoHorario);
    const endOfMonthDate = getEndOfMonthInTimezone(targetAno, targetMes, request.fusoHorario);

    const diasPlantao = await prisma.diaPlantao.findMany({
      where: {
        plantaoId: plantao.id,
        // Buscar apenas dias que estão dentro do período do mês solicitado
        data: {
          gte: startOfMonthDate,
          lte: endOfMonthDate,
        },
      },
      include: {
        presencaDiaPlantao: true,
      },
      orderBy: { data: "asc" },
    });

    // Mapear para o formato esperado - agora com múltiplos registros por dia
    const presencas = diasPlantao.map((dia: any) => {
      const registros = dia.presencaDiaPlantao || [];

      // Calcular totais dos registros do dia
      const totalHorasTrabalhadas = registros.reduce((acc: number, reg: any) => acc + (reg.horasTrabalhadas || 0), 0);
      const totalValorEstimado = registros.reduce((acc: number, reg: any) => acc + (reg.valorEstimado || 0), 0);
      const totalTempoGlosado = registros.reduce((acc: number, reg: any) => acc + (reg.tempoGlosado || 0), 0);

      return {
        dia: convertToDate(dia.data).getDate(),
        diaPlantaoId: dia.id,
        registros: registros.map((registro: any) => ({
          id: registro.id,
          horaEntrada: registro.horaEntrada ? registro.horaEntrada.toISOString() : null,
          horaSaida: registro.horaSaida ? registro.horaSaida.toISOString() : null,
          intervalo: registro.intervalo,
          horasTrabalhadas: registro.horasTrabalhadas,
          valorEstimado: registro.valorEstimado,
          observacao: registro.observacao,
          status: registro.status,
          tempoGlosado: registro.tempoGlosado,
          justificativaGlosa: registro.justificativaGlosa,
          fechamentoId: registro.fechamentoId,
          createdAt: registro.createdAt.toISOString(),
        })),
        // Campos agregados para compatibilidade
        entrada: registros.find((r: any) => r.horaEntrada)
          ? {
              id: registros.find((r: any) => r.horaEntrada).id,
              horario: registros.find((r: any) => r.horaEntrada).horaEntrada.toISOString(),
              observacao: registros.find((r: any) => r.horaEntrada).observacao,
            }
          : null,
        saida: registros.find((r: any) => r.horaSaida)
          ? {
              id: registros.find((r: any) => r.horaSaida).id,
              horario: registros.find((r: any) => r.horaSaida).horaSaida.toISOString(),
              observacao: registros.find((r: any) => r.horaSaida).observacao,
            }
          : null,
        intervalo: dia.intervalo || plantao.intervalo,
        horasTrabalhadas: totalHorasTrabalhadas,
        trabalhado: dia.trabalhado,
        horaEntradaPrevista: dia.horaEntrada,
        horaSaidaPrevista: dia.horaSaida,
        observacoes: dia.observacoes,
        status: registros.length > 0 ? registros[registros.length - 1].status : "PENDENTE", // Status do último registro
        tempoGlosado: totalTempoGlosado,
        justificativaGlosa: registros.find((r: any) => r.justificativaGlosa)?.justificativaGlosa || null,
        valorEstimado: totalValorEstimado,
        totalRegistros: registros.length,
      };
    });

    // Calcular estatísticas
    const totalDiasEscalados = diasPlantao.length;
    const diasComRegistroCompleto = presencas.filter((a: any) => a.entrada && a.saida).length;
    const diasPendentes = totalDiasEscalados - diasComRegistroCompleto;
    const totalHorasTrabalhadas = presencas.reduce((acc: number, a: any) => acc + (a.horasTrabalhadas || 0), 0);

    // Calcular valor estimado (sempre calcular por horas quando possível)
    let valorEstimado = 0;
    if (plantao.tipoValor === "HORA" ? plantao.valorBase : 0) {
      valorEstimado = totalHorasTrabalhadas * (plantao.tipoValor === "HORA" ? plantao.valorBase : 0);
    } else if (plantao.tipoValor === "MENSAL" ? plantao.valorBase : 0) {
      valorEstimado = plantao.tipoValor === "MENSAL" ? plantao.valorBase : 0;
    } else if (plantao.tipoValor === "PLANTAO" || plantao.tipoValor === "DIARIA" ? plantao.valorBase : 0) {
      valorEstimado =
        diasComRegistroCompleto *
        (plantao.tipoValor === "PLANTAO" || plantao.tipoValor === "DIARIA" ? plantao.valorBase : 0);
    }

    return reply.send({
      plantaoId: plantao.id,
      mes: targetMes,
      ano: targetAno,
      presencas,
      estatisticas: {
        totalDiasEscalados,
        diasComRegistroCompleto,
        diasPendentes,
        totalHorasTrabalhadas,
        valorEstimado,
      },
    });
  });
}
