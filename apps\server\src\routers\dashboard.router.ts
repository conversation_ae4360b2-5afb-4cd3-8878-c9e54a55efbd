import { prisma } from "../lib/prisma";
import { getCurrentMonth, getCurrentYear, createLocalDate } from "@shared/date";
import type { FastifyTypedInstance } from "@/types";

export async function dashboardRouter(fastify: FastifyTypedInstance) {
  // Obter estatísticas do dashboard
  fastify.get("/dashboard/stats", async (request, reply) => {
    try {
      const currentMonth = getCurrentMonth();
      const currentYear = getCurrentYear();

      // Buscar todas as estatísticas em paralelo
      const [
        clientesCount,
        profissionaisCount,
        plantoesAtivosCount,
        fechamentosPendentesCount,
        antecipacoesData,
        antecipacoesAprovadasData,
      ] = await Promise.all([
        // Total de clientes ativos
        prisma.cliente.count(),

        // Total de profissionais
        prisma.profissional.count(),

        // Plantões ativos do mês atual - usando dataInicial
        prisma.plantao.count({
          where: {
            dataInicial: {
              gte: createLocalDate(currentYear, currentMonth, 1),
              lt: createLocalDate(currentYear, currentMonth + 1, 1),
            },
          },
        }),

        // Fechamentos pendentes
        prisma.fechamento.count({
          where: {
            status: "PENDENTE",
          },
        }),

        // Antecipações pendentes e seu valor total
        prisma.antecipacao.findMany({
          where: {
            status: "PENDENTE",
          },
          select: {
            valorSolicitado: true,
          },
        }),

        // Valor total das antecipações aprovadas no mês
        prisma.antecipacao.findMany({
          where: {
            status: "APROVADA",
            createdAt: {
              gte: createLocalDate(currentYear, currentMonth, 1),
              lt: createLocalDate(currentYear, currentMonth + 1, 1),
            },
          },
          select: {
            valorAprovado: true,
          },
        }),
      ]);

      const antecipacoesCount = antecipacoesData.length;
      const valorTotalAntecipacoesAprovadas = antecipacoesAprovadasData.reduce(
        (sum: number, antecipacao: any) => sum + (antecipacao.valorAprovado || 0),
        0
      );

      const stats = {
        clientes: clientesCount,
        profissionais: profissionaisCount,
        plantoesAtivos: plantoesAtivosCount,
        fechamentosPendentes: fechamentosPendentesCount,
        antecipacoesCount: antecipacoesCount,
        valorTotalAntecipacoesAprovadas: valorTotalAntecipacoesAprovadas,
      };

      return reply.send(stats);
    } catch (error) {
      fastify.log.error(error, "Erro ao buscar estatísticas do dashboard");
      return reply.status(500).send({ error: "Erro interno do servidor" });
    }
  });

  // Obter plantões recentes
  fastify.get("/dashboard/plantoes-recentes", async (request, reply) => {
    try {
      const plantoesRecentes = await prisma.plantao.findMany({
        take: 5,
        orderBy: {
          createdAt: "desc",
        },
        include: {
          profissional: {
            include: {
              usuario: {
                select: {
                  nome: true,
                },
              },
            },
          },
          cliente: {
            select: {
              nome: true,
            },
          },
          localAtendimento: {
            select: {
              nome: true,
            },
          },
        },
      });

      return reply.send(plantoesRecentes);
    } catch (error) {
      fastify.log.error(error, "Erro ao buscar plantões recentes");
      return reply.status(500).send({ error: "Erro interno do servidor" });
    }
  });

  // Obter notificações/alertas
  fastify.get("/dashboard/alertas", async (request, reply) => {
    try {
      const currentMonth = getCurrentMonth();
      const currentYear = getCurrentYear();

      const [fechamentosPendentes, antecipacoesCount] = await Promise.all([
        prisma.fechamento.count({
          where: {
            status: "PENDENTE",
          },
        }),
        prisma.antecipacao.count({
          where: {
            status: "PENDENTE",
          },
        }),
      ]);

      const alertas = [];

      if (fechamentosPendentes > 0) {
        alertas.push({
          tipo: "warning",
          titulo: `${fechamentosPendentes} fechamentos pendentes de aprovação`,
          descricao: "Revise os fechamentos do mês atual",
          link: "/fechamentos?status=PENDENTE",
        });
      }

      if (antecipacoesCount > 0) {
        alertas.push({
          tipo: "info",
          titulo: `${antecipacoesCount} novas solicitações de antecipação`,
          descricao: "Aguardando análise e aprovação",
          link: "/antecipacoes?status=PENDENTE",
        });
      }

      return reply.send(alertas);
    } catch (error) {
      fastify.log.error(error, "Erro ao buscar alertas");
      return reply.status(500).send({ error: "Erro interno do servidor" });
    }
  });
}
