import { z } from "zod";
import { prisma, withAudit } from "../lib/prisma";
import { parseUTCDate, getCurrentDate, get24HoursAgo } from "@shared/date";
import type { FastifyTypedInstance } from "@/types";
import { authorize } from "@/middlewares/auth.middleware";

const auditQuerySchema = z.object({
  page: z.coerce.number().min(1).optional().default(1),
  limit: z.coerce.number().min(1).max(100).optional().default(50),
  tableName: z.string().optional(),
  operation: z.enum(["CREATE", "UPDATE", "DELETE", "READ"]).optional(),
  userId: z.string().optional(),
  recordId: z.string().optional(),
  startDate: z.string().optional(),
  endDate: z.string().optional(),
});

type AuditQuery = z.infer<typeof auditQuerySchema>;

export async function auditRouter(fastify: FastifyTypedInstance) {
  // Listar logs de auditoria
  fastify.get<{ Querystring: AuditQuery }>(
    "/audit-logs",
    { preHandler: [authorize("master", "admin")] },
    withAudit(async (request, reply) => {
      const parsedQuery = auditQuerySchema.parse(request.query);
      const { page = 1, limit = 50, tableName, operation, userId, recordId, startDate, endDate } = parsedQuery;

      const skip = (page - 1) * limit;

      const where: any = {};

      if (tableName) where.tableName = tableName;
      if (operation) where.operation = operation;
      if (userId) where.userId = userId;
      if (recordId) where.recordId = recordId;

      if (startDate || endDate) {
        where.createdAt = {};
        if (startDate) where.createdAt.gte = parseUTCDate(startDate);
        if (endDate) where.createdAt.lte = parseUTCDate(endDate);
      }

      const [logs, total] = await Promise.all([
        prisma.auditLog.findMany({
          where,
          skip,
          take: limit,
          orderBy: { createdAt: "desc" },
        }),
        prisma.auditLog.count({ where }),
      ]);

      return reply.send({
        data: logs,
        meta: {
          page,
          limit,
          total,
          totalPages: Math.ceil(total / limit),
        },
      });
    })
  );

  // Buscar log específico por ID
  fastify.get<{ Params: { id: string } }>(
    "/audit-logs/:id",
    { preHandler: [authorize("master", "admin")] },
    withAudit(async (request, reply) => {
      const { id } = request.params;

      const log = await prisma.auditLog.findUnique({
        where: { id },
      });

      if (!log) {
        return reply.status(404).send({
          error: "Log de auditoria não encontrado",
        });
      }

      return reply.send(log);
    })
  );

  // Estatísticas de auditoria
  fastify.get(
    "/audit-logs/stats",
    { preHandler: [authorize("master", "admin")] },
    withAudit(async (request, reply) => {
      const [totalLogs, logsByOperation, logsByTable, recentActivity] = await Promise.all([
        // Total de logs
        prisma.auditLog.count(),

        // Logs por operação
        prisma.auditLog.groupBy({
          by: ["operation"],
          _count: true,
        }),

        // Logs por tabela
        prisma.auditLog.groupBy({
          by: ["tableName"],
          _count: true,
          orderBy: {
            _count: {
              tableName: "desc",
            },
          },
          take: 10,
        }),

        // Atividade recente (últimas 24 horas)
        prisma.auditLog.count({
          where: {
            createdAt: {
              gte: get24HoursAgo(),
            },
          },
        }),
      ]);

      return reply.send({
        totalLogs,
        recentActivity,
        byOperation: logsByOperation.reduce(
          (acc: Record<string, number>, item: any) => {
            acc[item.operation] = item._count;
            return acc;
          },
          {} as Record<string, number>
        ),
        topTables: logsByTable.map((item: any) => ({
          table: item.tableName,
          count: item._count,
        })),
      });
    })
  );

  // Limpar logs antigos (manutenção)
  fastify.delete(
    "/audit-logs/cleanup",
    { preHandler: [authorize("master")] },
    withAudit(async (request, reply) => {
      const { daysToKeep = 90 } = request.query as { daysToKeep?: number };

      const cutoffDate = getCurrentDate();
      cutoffDate.setDate(cutoffDate.getDate() - daysToKeep);

      const result = await prisma.auditLog.deleteMany({
        where: {
          createdAt: {
            lt: cutoffDate,
          },
        },
      });

      return reply.send({
        message: `Removidos ${result.count} logs antigos (mais de ${daysToKeep} dias)`,
        deletedCount: result.count,
        cutoffDate: cutoffDate.toISOString(),
      });
    })
  );
}
