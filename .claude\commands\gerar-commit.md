# /gerar-commit

Gera um commit Git formatado com o nome da branch como prefixo.

## Descrição

Este comando automatiza a criação de commits padronizados no formato `[nome-da-branch] - mensagem`, analisando automaticamente as mudanças e gerando uma mensagem descritiva apropriada.

## Comportamento

Quando executado, o comando irá:

1. **Verificar arquivos staged** - Mostra apenas os arquivos já adicionados com `git add`
2. **Analisar as mudanças** - Examina os arquivos modificados para entender o contexto
3. **Obter o nome da branch atual** - Automaticamente detecta a branch ativa
4. **Gerar mensagem automática** - Cria uma mensagem descritiva baseada nas mudanças detectadas
5. **Formatar a mensagem** - Adiciona o prefixo `[nome-da-branch] - ` à mensagem
6. **Solicitar aprovação** - Mostra a mensagem gerada e pergunta se deseja usar ou modificar
7. **Executar o commit** - Se aprovado, cria o commit com os arquivos staged

## Formato da Mensagem

```
[nome-da-branch] - sua mensagem aqui
```

### Exemplos:

- `[main] - fix: corrigido erro no login`
- `[feature/usuarios] - feat: adicionado CRUD de usuários`
- `[hotfix/auth] - fix: corrigido loop infinito no auth guard`

## Uso

Digite no chat do Claude:

```
/gerar-commit
```

O comando irá guiá-lo através do processo interativo.

## Fluxo de Execução

1. Verifica se há arquivos staged (`git diff --cached`)
2. Se não houver arquivos staged, informa e encerra
3. Analisa os arquivos staged para entender as mudanças
4. Mostra os arquivos que estão staged para commit
5. Exibe a branch atual
6. **Gera automaticamente uma mensagem de commit baseada nas mudanças**
7. Mostra a mensagem formatada: `[branch] - mensagem gerada`
8. Pergunta "Deseja executar o commit com esta mensagem? (s/n/e)"
   - **s**: Executa o commit com a mensagem gerada
   - **n**: Cancela o commit
   - **e**: Permite editar a mensagem antes de commitar
9. Se confirmado ou após edição:
   - Executa `git commit -m "[branch] - mensagem"` (apenas com arquivos já staged)
   - Mostra confirmação de sucesso

## Observações

- O comando trabalha APENAS com arquivos já adicionados via `git add`
- NÃO adiciona arquivos automaticamente - você deve fazer `git add` antes
- **A mensagem é gerada automaticamente analisando as mudanças**
- A mensagem do commit será automaticamente prefixada com o nome da branch
- Você pode aprovar, rejeitar ou editar a mensagem sugerida
- Se não houver arquivos staged, o comando informará e será encerrado
- O comando NÃO faz push automático - apenas cria o commit local

## Geração Automática de Mensagem

O comando analisa:

- Tipos de arquivos modificados (componentes, rotas, configurações)
- Natureza das mudanças (adições, modificações, remoções)
- Contexto do projeto (autenticação, UI, API, etc.)
- Padrões de commit convencionais (feat, fix, refactor, etc.)

Exemplos de mensagens geradas:

- Adição de sistema de autenticação → `feat: implementa sistema de autenticação com guard e login`
- Correção em componentes → `fix: corrige problemas nos componentes de layout`
- Refatoração de API → `refactor: reorganiza estrutura de rotas da API`

## Pré-requisitos

Antes de usar este comando, você deve:

```bash
git add <arquivos>  # Adicionar os arquivos desejados
```
