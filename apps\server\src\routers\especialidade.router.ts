import { authorize } from "@/middlewares/auth.middleware";
import { prisma } from "../lib/prisma";
import {
  createEspecialidadeSchema,
  updateEspecialidadeSchema,
  especialidadeQuerySchema,
  type CreateEspecialidadeInput,
  type UpdateEspecialidadeInput,
  type EspecialidadeQuery,
} from "../schemas/especialidade.schema";
import type { FastifyTypedInstance } from "@/types";

export async function especialidadeRouter(fastify: FastifyTypedInstance) {
  // Listar especialidades
  fastify.get<{ Querystring: EspecialidadeQuery }>("/especialidades", async (request, reply) => {
    const { page = 1, limit = 50, search } = request.query;
    const pageNum = Number(page);
    const limitNum = Number(limit);
    const skip = (pageNum - 1) * limitNum;

    const where = {
      ...(search && {
        OR: [{ nome: { contains: search } }, { descricao: { contains: search } }],
      }),
    };

    const [especialidades, total] = await Promise.all([
      prisma.especialidade.findMany({
        where,
        skip,
        take: limitNum,
        include: {
          _count: {
            select: {
              profissionais: true,
            },
          },
        },
        orderBy: { nome: "asc" },
      }),
      prisma.especialidade.count({ where }),
    ]);

    return reply.send({
      data: especialidades,
      meta: {
        page: pageNum,
        limit: limitNum,
        total,
        totalPages: Math.ceil(total / limitNum),
      },
    });
  });

  // Buscar especialidade por ID
  fastify.get<{ Params: { id: string } }>("/especialidades/:id", async (request, reply) => {
    const id = Number(request.params.id);

    const especialidade = await prisma.especialidade.findUnique({
      where: { id },
      include: {
        profissionais: {
          include: {
            profissional: true,
          },
        },
      },
    });

    if (!especialidade) {
      return reply.status(404).send({ error: "Especialidade não encontrada" });
    }

    return reply.send(especialidade);
  });

  // Criar especialidade
  fastify.post<{ Body: CreateEspecialidadeInput }>("/especialidades", async (request, reply) => {
    const data = request.body;

    // Verificar se já existe
    const existing = await prisma.especialidade.findUnique({
      where: { nome: data.nome },
    });

    if (existing) {
      return reply.status(400).send({ error: "Especialidade já existe" });
    }

    const especialidade = await prisma.especialidade.create({
      data,
    });

    return reply.status(201).send(especialidade);
  });

  // Atualizar especialidade
  fastify.put<{
    Params: { id: string };
    Body: UpdateEspecialidadeInput;
  }>("/especialidades/:id", async (request, reply) => {
    const id = Number(request.params.id);
    const data = request.body;

    // Verificar se existe
    const existing = await prisma.especialidade.findUnique({
      where: { id },
    });

    if (!existing) {
      return reply.status(404).send({ error: "Especialidade não encontrada" });
    }

    // Se está mudando o nome, verificar duplicação
    if (data.nome && data.nome !== existing.nome) {
      const duplicate = await prisma.especialidade.findUnique({
        where: { nome: data.nome },
      });

      if (duplicate) {
        return reply.status(400).send({ error: "Já existe uma especialidade com este nome" });
      }
    }

    const especialidade = await prisma.especialidade.update({
      where: { id },
      data,
    });

    return reply.send(especialidade);
  });

  // Deletar especialidade
  fastify.delete<{ Params: { id: string } }>(
    "/especialidades/:id",
    { preHandler: [authorize("master", "admin")] },
    async (request, reply) => {
      const id = Number(request.params.id);

      // Verificar se existe
      const especialidade = await prisma.especialidade.findUnique({
        where: { id },
        include: {
          _count: {
            select: {
              profissionais: true,
            },
          },
        },
      });

      if (!especialidade) {
        return reply.status(404).send({ error: "Especialidade não encontrada" });
      }

      // Verificar se tem profissionais associados
      if (especialidade._count.profissionais > 0) {
        return reply.status(400).send({
          error: "Não é possível excluir especialidade com profissionais associados",
        });
      }

      await prisma.especialidade.delete({
        where: { id },
      });

      return reply.status(204).send();
    }
  );
}
