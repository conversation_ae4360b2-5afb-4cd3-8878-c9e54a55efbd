import { prisma } from "../lib/prisma";
import {
  createAntecipacaoSchema,
  updateAntecipacaoSchema,
  type CreateAntecipacaoInput,
  type UpdateAntecipacaoInput,
  type AntecipacaoQuery,
} from "../schemas/antecipacao.schema";
import { getCurrentDate, parseUTCDate } from "@shared/date";
import { getCurrentDateInTimezone, addDaysToDate, formatInTimezone } from "@shared/date";
import type { FastifyTypedInstance } from "@/types";
import { authorize } from "@/middlewares/auth.middleware";
import { sendAntecipacaoEmail, sendTermoAssinadoEmail } from "../lib/mail";
import { createHash } from "crypto";

export async function antecipacaoRouter(fastify: FastifyTypedInstance) {
  // Listar antecipações
  fastify.get<{ Querystring: AntecipacaoQuery }>("/antecipacoes", async (request, reply) => {
    const clienteId = request.clienteId;
    const { page = 1, limit = 10, search, status, plantaoId, profissionalId } = request.query;
    const pageNum = Number(page);
    const limitNum = Number(limit);
    const skip = (pageNum - 1) * limitNum;

    const where = {
      ...(status && { status }),
      plantaoId: plantaoId ? Number(plantaoId) : undefined,
      profissionalId: profissionalId ? Number(profissionalId) : undefined,
      plantao: {
        clienteId,
      },
      ...(search && {
        OR: [{ observacoes: { contains: search } }],
      }),
    };

    const [antecipacoes, total] = await Promise.all([
      prisma.antecipacao.findMany({
        where,
        skip,
        take: limitNum,
        include: {
          plantao: {
            include: {
              profissional: {
                include: {
                  usuario: true,
                },
              },
              cliente: true,
              localAtendimento: true,
            },
          },
          profissional: {
            include: {
              usuario: true,
            },
          },
          fechamentos: {
            include: {
              plantao: {
                include: {
                  cliente: true,
                  localAtendimento: true,
                },
              },
            },
          },
        },
        orderBy: { createdAt: "desc" },
      }),
      prisma.antecipacao.count({ where }),
    ]);

    return reply.send({
      data: antecipacoes,
      meta: {
        page: pageNum,
        limit: limitNum,
        total,
        totalPages: Math.ceil(total / limitNum),
      },
    });
  });

  // Buscar antecipação por ID
  fastify.get<{ Params: { uuid: string } }>("/antecipacoes/:uuid", async (request, reply) => {
    const { uuid } = request.params;
    const clienteId = request.clienteId;

    const where = {
      uuid,
      plantao: {
        clienteId,
      },
    };

    const antecipacao = await prisma.antecipacao.findFirst({
      where,
      include: {
        plantao: {
          include: {
            profissional: {
              include: {
                usuario: true,
              },
            },
            cliente: true,
            localAtendimento: true,
          },
        },
        profissional: {
          include: {
            usuario: true,
          },
        },
        fechamentos: {
          include: {
            plantao: {
              include: {
                cliente: true,
                localAtendimento: true,
              },
            },
          },
        },
      },
    });

    if (!antecipacao) {
      return reply.status(404).send({ error: "Antecipação não encontrada" });
    }

    return reply.send(antecipacao);
  });

  // Criar antecipação
  fastify.post<{ Body: CreateAntecipacaoInput }>("/antecipacoes", async (request, reply) => {
    const validation = createAntecipacaoSchema.safeParse(request.body);

    if (!validation.success) {
      return reply.status(400).send({
        error: "Dados inválidos",
        issues: validation.error.issues,
      });
    }

    const data = validation.data;

    const clienteId = request.clienteId;

    if (data.percentual > 100) {
      return reply.status(400).send({ error: "Percentual máximo de antecipação é 100%" });
    }

    const plantaoWhere = {
      uuid: data.plantaoId,
      clienteId,
    };

    const plantao = await prisma.plantao.findFirst({
      where: plantaoWhere,
      include: {
        profissional: {
          include: {
            usuario: true,
          },
        },
        cliente: true,
      },
    });

    if (!plantao) {
      return reply.status(404).send({ error: "Plantão não encontrado" });
    }

    // Verificar se todos os fechamentos existem e estão aprovados
    const fechamentos = await prisma.fechamento.findMany({
      where: {
        uuid: { in: data.fechamentoIds },
        plantaoId: plantao.id,
        status: "APROVADO", // Só pode antecipar fechamentos aprovados
        antecipacaoId: null, // Não podem ter antecipação já
      },
    });

    if (fechamentos.length !== data.fechamentoIds.length) {
      return reply.status(400).send({
        error:
          "Alguns fechamentos não foram encontrados, não estão aprovados, não pertencem ao plantão ou já possuem antecipação",
      });
    }

    // Verificar se todos os fechamentos são do mesmo profissional
    const profissionalId = plantao.profissionalId;
    const todosMesmoProfissional = fechamentos.every((f) => f.profissionalId === profissionalId);

    if (!todosMesmoProfissional) {
      return reply.status(400).send({
        error: "Todos os fechamentos devem ser do mesmo profissional do plantão",
      });
    }

    // Calcular data de pagamento prevista se não informada
    let dataPagamentoPrevista = data.dataPagamentoPrevista;
    if (!dataPagamentoPrevista && plantao.prazoPagamentoDias) {
      // Usar fusoHorario do cliente, plantão ou request
      const timezone =
        plantao.cliente?.fusoHorario || plantao.fusoHorario || request.fusoHorario || "America/Sao_Paulo";
      const hoje = getCurrentDateInTimezone(timezone);
      const dataPrevista = addDaysToDate(hoje, plantao.prazoPagamentoDias);
      dataPagamentoPrevista = dataPrevista.toISOString();
    }

    // Token de assinatura será gerado apenas quando a antecipação for aprovada

    // Criar a antecipação usando transação
    const antecipacao = await prisma.$transaction(async (tx) => {
      // Criar a antecipação
      const newAntecipacao = await tx.antecipacao.create({
        data: {
          plantaoId: plantao.id,
          profissionalId,
          valorSolicitado: data.valorSolicitado,
          valorAprovado: data.valorAprovado || data.valorSolicitado,
          percentual: Math.min(100, data.percentual), // Garantir máximo de 100%
          taxaAntecipacao: data.taxaAntecipacao,
          dataPagamentoPrevista: parseUTCDate(dataPagamentoPrevista || new Date().toISOString()),
          status: data.status || "PENDENTE",
          numeroCCB: data.numeroCCB,
          dataCCB: data.dataCCB ? parseUTCDate(data.dataCCB) : undefined,
          observacoes: data.observacoes,
          termoAssinado: false,
          metaData: data.metaData || {
            fechamentoIds: data.fechamentoIds,
            clienteNome: plantao.cliente?.nome,
            taxaPadraoCliente: plantao.cliente?.taxaPadrao,
          },
        },
      });

      // Atualizar os fechamentos para referenciar a antecipação
      await tx.fechamento.updateMany({
        where: { uuid: { in: data.fechamentoIds } },
        data: { antecipacaoId: newAntecipacao.id },
      });

      // Criar registro no histórico
      await tx.antecipacaoHistorico.create({
        data: {
          antecipacaoId: newAntecipacao.id,
          status: "PENDENTE",
          metaData: {
            action: "created",
            fechamentosCount: data.fechamentoIds.length,
            valorSolicitado: data.valorSolicitado,
            percentual: data.percentual,
            taxaAntecipacao: data.taxaAntecipacao,
          },
        },
      });

      return newAntecipacao;
    });

    // Buscar a antecipação completa para enviar email
    const antecipacaoCompleta = await prisma.antecipacao.findUnique({
      where: { id: antecipacao.id },
      include: {
        plantao: {
          include: {
            profissional: {
              include: {
                usuario: true,
              },
            },
            cliente: true,
            localAtendimento: true,
          },
        },
        profissional: {
          include: {
            usuario: true,
          },
        },
        fechamentos: {
          include: {
            plantao: {
              include: {
                cliente: true,
                localAtendimento: true,
              },
            },
            presencaDiaPlantao: {
              orderBy: { horaEntrada: "asc" },
              take: 1,
            },
          },
        },
      },
    });

    // Email será enviado apenas quando a antecipação for aprovada

    return reply.status(201).send(antecipacaoCompleta);
  });

  // Atualizar antecipação
  fastify.put<{
    Params: { uuid: string };
    Body: UpdateAntecipacaoInput;
  }>("/antecipacoes/:uuid", async (request, reply) => {
    const { uuid } = request.params;
    const clienteId = request.clienteId;

    // Validar dados com Zod
    const validation = updateAntecipacaoSchema.safeParse(request.body);

    if (!validation.success) {
      return reply.status(400).send({
        error: "Dados inválidos",
        issues: validation.error.issues,
      });
    }

    const data = validation.data;

    const where = {
      uuid,
      plantao: {
        clienteId,
      },
    };

    const existingAntecipacao = await prisma.antecipacao.findFirst({
      where,
      include: {
        plantao: {
          include: {
            cliente: true,
          },
        },
      },
    });

    if (!existingAntecipacao) {
      return reply.status(404).send({ error: "Antecipação não encontrada" });
    }

    // Atualizar datas baseado no status
    const updateData = { ...data };

    // Verificar se o status mudou para APROVADA para enviar email
    const statusMudouParaAprovada = existingAntecipacao.status !== "APROVADA" && data.status === "APROVADA";

    const antecipacao = await prisma.antecipacao.update({
      where: { uuid },
      data: { ...updateData, plantaoId: existingAntecipacao.plantaoId },
      include: {
        plantao: {
          include: {
            profissional: {
              include: {
                usuario: true,
              },
            },
            cliente: true,
            localAtendimento: true,
          },
        },
        profissional: {
          include: {
            usuario: true,
          },
        },
        fechamentos: {
          include: {
            plantao: {
              include: {
                cliente: true,
                localAtendimento: true,
              },
            },
          },
        },
      },
    });

    // Enviar email quando a antecipação for aprovada
    if (statusMudouParaAprovada) {
      try {
        // Calcular valor líquido
        const valorLiquido = antecipacao.valorSolicitado * (1 - (antecipacao.taxaAntecipacao || 0) / 100);

        // Buscar fechamentos para determinar período
        const fechamentos = await prisma.fechamento.findMany({
          where: {
            antecipacaoId: antecipacao.id,
          },
        });

        // Buscar presenças para determinar período real
        const presencas = await prisma.presencaDiaPlantao.findMany({
          where: {
            fechamentoId: { in: fechamentos.map((f) => f.id) },
            horaEntrada: { not: null },
            horaSaida: { not: null },
          },
          orderBy: { horaEntrada: "asc" },
        });

        // Determinar período
        let periodoInicio: Date | null = null;
        let periodoFim: Date | null = null;

        if (presencas.length > 0) {
          periodoInicio = presencas[0].horaEntrada;
          periodoFim = presencas[presencas.length - 1].horaSaida;
        } else {
          periodoInicio = antecipacao.plantao.dataInicial;
          periodoFim = antecipacao.plantao.dataFinal || antecipacao.plantao.dataInicial;
        }

        const timezone =
          antecipacao.plantao.cliente?.fusoHorario || antecipacao.plantao.fusoHorario || "America/Sao_Paulo";

        const mesInicio = periodoInicio
          ? parseInt(formatInTimezone(periodoInicio, "MM", timezone))
          : getCurrentDate().getMonth() + 1;
        const anoInicio = periodoInicio
          ? parseInt(formatInTimezone(periodoInicio, "yyyy", timezone))
          : getCurrentDate().getFullYear();

        await sendAntecipacaoEmail({
          profissionalNome: antecipacao.plantao.profissional?.usuario?.nome || "Profissional",
          profissionalEmail: antecipacao.plantao.profissional?.usuario?.email || "",
          valorSolicitado: antecipacao.valorSolicitado,
          valorLiquido,
          percentual: antecipacao.percentual,
          taxaAntecipacao: antecipacao.taxaAntecipacao || 0,
          dataPagamentoPrevista: antecipacao.dataPagamentoPrevista?.toISOString() || "",
          clienteNome: antecipacao.plantao.cliente?.nome || "",
          localAtendimento: antecipacao.plantao.localAtendimento?.nome || "",
          mes: mesInicio,
          ano: anoInicio,
        });
      } catch (error) {
        console.error("Erro ao enviar email de antecipação aprovada:", error);
        // Não falhar a atualização se o email não for enviado
      }
    }

    return reply.send(antecipacao);
  });

  // Deletar antecipação
  fastify.delete<{ Params: { uuid: string } }>(
    "/antecipacoes/:uuid",
    { preHandler: [authorize("master")] },
    async (request, reply) => {
      const { uuid } = request.params;
      const clienteId = request.clienteId;

      const where = {
        uuid,
        plantao: {
          clienteId,
        },
      };

      const antecipacao = await prisma.antecipacao.findFirst({
        where,
      });

      if (!antecipacao) {
        return reply.status(404).send({ error: "Antecipação não encontrada" });
      }

      // Não permitir deletar se já foi PAGA
      if (antecipacao.status === "PAGA") {
        return reply.status(400).send({
          error: "Não é possível excluir antecipação já PAGA",
        });
      }

      await prisma.antecipacao.delete({
        where: { uuid },
      });

      return reply.status(204).send();
    }
  );

  // Aprovar antecipação
  fastify.post<{ Params: { uuid: string } }>("/antecipacoes/:uuid/aprovar", async (request, reply) => {
    const { uuid } = request.params;
    const clienteId = request.clienteId;

    const where = {
      uuid,
      plantao: {
        clienteId,
      },
    };

    const antecipacao = await prisma.antecipacao.findFirst({
      where,
    });

    if (!antecipacao) {
      return reply.status(404).send({ error: "Antecipação não encontrada" });
    }

    if (antecipacao.status !== "PENDENTE") {
      return reply.status(400).send({
        error: "Apenas antecipações PENDENTEs podem ser APROVADAs",
      });
    }

    const updated = await prisma.antecipacao.update({
      where: { uuid },
      data: {
        status: "APROVADA",
      },
      include: {
        plantao: {
          include: {
            profissional: {
              include: {
                usuario: true,
              },
            },
            cliente: true,
            localAtendimento: true,
          },
        },
        profissional: {
          include: {
            usuario: true,
          },
        },
        fechamentos: {
          include: {
            plantao: {
              include: {
                cliente: true,
                localAtendimento: true,
              },
            },
          },
        },
      },
    });

    return reply.send(updated);
  });

  // Rejeitar antecipação
  fastify.post<{
    Params: { uuid: string };
    Body: { motivo?: string };
  }>("/antecipacoes/:uuid/rejeitar", async (request, reply) => {
    const { uuid } = request.params;
    const { motivo } = request.body;
    const clienteId = request.clienteId;

    const where = {
      uuid,
      plantao: {
        clienteId,
      },
    };

    const antecipacao = await prisma.antecipacao.findFirst({
      where,
    });

    if (!antecipacao) {
      return reply.status(404).send({ error: "Antecipação não encontrada" });
    }

    if (antecipacao.status !== "PENDENTE") {
      return reply.status(400).send({
        error: "Apenas antecipações PENDENTEs podem ser REJEITADAs",
      });
    }

    const updated = await prisma.antecipacao.update({
      where: { uuid },
      data: {
        status: "REJEITADA",
        observacoes: motivo
          ? `${antecipacao.observacoes || ""}\nMotivo da rejeição: ${motivo}`
          : antecipacao.observacoes,
      },
      include: {
        plantao: {
          include: {
            profissional: {
              include: {
                usuario: true,
              },
            },
            cliente: true,
            localAtendimento: true,
          },
        },
        profissional: {
          include: {
            usuario: true,
          },
        },
        fechamentos: {
          include: {
            plantao: {
              include: {
                cliente: true,
                localAtendimento: true,
              },
            },
          },
        },
      },
    });

    return reply.send(updated);
  });

  // Marcar como PAGA
  fastify.post<{ Params: { uuid: string } }>("/antecipacoes/:uuid/pagar", async (request, reply) => {
    const { uuid } = request.params;
    const clienteId = request.clienteId;

    const where = {
      uuid,
      plantao: {
        clienteId,
      },
    };

    const antecipacao = await prisma.antecipacao.findFirst({
      where,
    });

    if (!antecipacao) {
      return reply.status(404).send({ error: "Antecipação não encontrada" });
    }

    if (antecipacao.status !== "APROVADA") {
      return reply.status(400).send({
        error: "Apenas antecipações APROVADAs podem ser marcadas como PAGAs",
      });
    }

    const updated = await prisma.antecipacao.update({
      where: { uuid },
      data: {
        status: "PAGA",
      },
      include: {
        plantao: {
          include: {
            profissional: {
              include: {
                usuario: true,
              },
            },
            cliente: true,
            localAtendimento: true,
          },
        },
        profissional: {
          include: {
            usuario: true,
          },
        },
        fechamentos: {
          include: {
            plantao: {
              include: {
                cliente: true,
                localAtendimento: true,
              },
            },
          },
        },
      },
    });

    return reply.send(updated);
  });

  // Visualizar termo de antecipação
  fastify.get<{ Params: { uuid: string } }>("/antecipacoes/:uuid/termo", async (request, reply) => {
    const { uuid } = request.params;

    // Verificar se o usuário está logado
    if (!request.user) {
      return reply.status(401).send({ error: "É necessário estar logado para visualizar o termo" });
    }

    const antecipacao = await prisma.antecipacao.findFirst({
      where: {
        uuid: uuid,
        status: "APROVADA",
        termoAssinado: false,
      },
      include: {
        plantao: {
          include: {
            profissional: {
              include: {
                usuario: true,
              },
            },
            cliente: true,
            localAtendimento: true,
          },
        },
        fechamentos: {
          include: {
            plantao: true,
          },
        },
      },
    });

    if (!antecipacao) {
      return reply.status(404).send({ error: "Antecipação não encontrada ou não disponível para visualização" });
    }

    // Verificar se o usuário logado tem permissão para visualizar
    const isAdmin = request.user.roles?.some((role) => ["admin", "master"].includes(role));
    const isProfissional = antecipacao.plantao.profissional?.usuarioId === request.user.id;

    if (!isAdmin && !isProfissional) {
      return reply.status(403).send({
        error: "Você não tem permissão para visualizar este termo",
      });
    }

    // Verificar se já foi assinado
    if (antecipacao.termoAssinado) {
      return reply.status(400).send({
        error: "Este termo já foi assinado",
        dataAssinatura: antecipacao.dataAssinaturaTermo,
      });
    }

    // Calcular valor líquido
    const valorLiquido = antecipacao.valorSolicitado * (1 - (antecipacao.taxaAntecipacao || 0) / 100);

    // Preparar dados do termo
    const termoData = {
      numeroTermo: antecipacao.uuid,
      dataEmissao: antecipacao.createdAt,
      hospital: antecipacao.plantao.cliente?.nome,
      hospitalCNPJ: antecipacao.plantao.cliente?.cnpj,
      profissional: antecipacao.plantao.profissional?.usuario?.nome,
      profissionalCPF: antecipacao.plantao.profissional?.usuario?.cpf,
      localAtendimento: antecipacao.plantao.localAtendimento?.nome,
      periodo: (() => {
        // Buscar período baseado nas datas do plantão considerando timezone
        const tz = antecipacao.plantao.cliente?.fusoHorario || antecipacao.plantao.fusoHorario || "America/Sao_Paulo";
        const dataInicial = antecipacao.plantao.dataInicial;
        const dataFinal = antecipacao.plantao.dataFinal || dataInicial;

        // Formatar datas considerando timezone
        const mesInicio = parseInt(formatInTimezone(dataInicial, "MM", tz));
        const anoInicio = parseInt(formatInTimezone(dataInicial, "yyyy", tz));
        const mesFim = parseInt(formatInTimezone(dataFinal, "MM", tz));
        const anoFim = parseInt(formatInTimezone(dataFinal, "yyyy", tz));

        if (mesInicio === mesFim && anoInicio === anoFim) {
          return `${String(mesInicio).padStart(2, "0")}/${anoInicio}`;
        } else {
          return `${String(mesInicio).padStart(2, "0")}/${anoInicio} - ${String(mesFim).padStart(2, "0")}/${anoFim}`;
        }
      })(),
      valorTotal: antecipacao.valorSolicitado,
      percentual: antecipacao.percentual,
      taxaAntecipacao: antecipacao.taxaAntecipacao,
      valorLiquido,
      dataPagamentoPrevista: antecipacao.dataPagamentoPrevista,
      fechamentos: antecipacao.fechamentos.map((f) => ({
        uuid: f.uuid,
        valor: f.totalValor,
      })),
    };

    return reply.send({
      antecipacao: termoData,
      termoAssinado: antecipacao.termoAssinado,
      profissionalId: antecipacao.plantao.profissional?.usuarioId,
      isAuthenticated: !!request.user,
      isProfissional: request.user ? antecipacao.plantao.profissional?.usuarioId === request.user.id : false,
      isAdmin: request.user ? request.user.roles?.some((role) => ["admin", "master"].includes(role)) : false,
    });
  });

  // Buscar antecipações pendentes de assinatura do profissional
  fastify.get("/antecipacoes/pendentes-assinatura", async (request, reply) => {
    if (!request.user) {
      return reply.status(401).send({ error: "Não autorizado" });
    }

    // Buscar profissional do usuário logado
    const profissional = await prisma.profissional.findFirst({
      where: {
        usuarioId: request.user.id,
      },
    });

    if (!profissional) {
      return reply.send({ antecipacoes: [] });
    }

    // Buscar antecipações pendentes de assinatura
    const antecipacoes = await prisma.antecipacao.findMany({
      where: {
        profissionalId: profissional.id,
        termoAssinado: false,
        status: "APROVADA", // Apenas antecipações aprovadas e não assinadas
      },
      include: {
        plantao: {
          include: {
            cliente: true,
            localAtendimento: true,
          },
        },
        fechamentos: true,
      },
      orderBy: {
        createdAt: "desc",
      },
    });

    // Formatar resposta
    const antecipacoesPendentes = antecipacoes.map((ant) => ({
      uuid: ant.uuid,
      valorSolicitado: ant.valorSolicitado,
      percentual: ant.percentual,
      taxaAntecipacao: ant.taxaAntecipacao,
      valorLiquido: ant.valorSolicitado * (1 - (ant.taxaAntecipacao || 0) / 100),
      cliente: ant.plantao.cliente?.nome,
      localAtendimento: ant.plantao.localAtendimento?.nome,
      periodo: (() => {
        const tz = ant.plantao.cliente?.fusoHorario || ant.plantao.fusoHorario || "America/Sao_Paulo";
        const dataInicial = ant.plantao.dataInicial;
        const dataFinal = ant.plantao.dataFinal || dataInicial;

        const mesInicio = parseInt(formatInTimezone(dataInicial, "MM", tz));
        const anoInicio = parseInt(formatInTimezone(dataInicial, "yyyy", tz));
        const mesFim = parseInt(formatInTimezone(dataFinal, "MM", tz));
        const anoFim = parseInt(formatInTimezone(dataFinal, "yyyy", tz));

        if (mesInicio === mesFim && anoInicio === anoFim) {
          return `${String(mesInicio).padStart(2, "0")}/${anoInicio}`;
        } else {
          return `${String(mesInicio).padStart(2, "0")}/${anoInicio} - ${String(mesFim).padStart(2, "0")}/${anoFim}`;
        }
      })(),
      dataEmissao: ant.createdAt,
      dataAprovacao: ant.updatedAt, // Quando foi aprovada
      quantidadeFechamentos: ant.fechamentos.length,
    }));

    return reply.send({
      antecipacoes: antecipacoesPendentes,
      total: antecipacoesPendentes.length,
    });
  });

  // Assinar termo de antecipação
  fastify.post<{
    Params: { uuid: string };
    Body: { aceite: boolean };
  }>("/antecipacoes/:uuid/assinar", async (request, reply) => {
    const { uuid } = request.params;
    const { aceite } = request.body;

    if (!aceite) {
      return reply.status(400).send({ error: "É necessário aceitar os termos para prosseguir" });
    }

    // Verificar se o usuário está logado
    if (!request.user) {
      return reply.status(401).send({ error: "É necessário estar logado para assinar o termo" });
    }

    const antecipacao = await prisma.antecipacao.findFirst({
      where: {
        uuid: uuid,
        status: "APROVADA",
        termoAssinado: false,
      },
      include: {
        plantao: {
          include: {
            profissional: {
              include: {
                usuario: true,
              },
            },
            cliente: true,
          },
        },
      },
    });

    if (!antecipacao) {
      return reply.status(404).send({ error: "Antecipação não encontrada ou não disponível para assinatura" });
    }

    // Verificar se é o profissional correto
    // IMPORTANTE: Somente o próprio profissional pode assinar (admin e master não podem assinar pelo profissional)
    const isProfissional = antecipacao.plantao.profissional?.usuarioId === request.user.id;

    if (!isProfissional) {
      const isAdmin = request.user.roles?.some((role) => ["admin", "master"].includes(role));
      if (isAdmin) {
        return reply.status(403).send({
          error:
            "Administradores não podem assinar termos em nome dos profissionais. Apenas o próprio profissional pode assinar seu termo.",
        });
      } else {
        return reply.status(403).send({
          error: "Você não tem permissão para assinar este termo. Apenas o profissional vinculado pode assinar.",
        });
      }
    }

    const timezone =
      antecipacao.plantao.cliente?.fusoHorario ||
      antecipacao.plantao.fusoHorario ||
      request.fusoHorario ||
      "America/Sao_Paulo";
    const agora = getCurrentDateInTimezone(timezone);

    // Verificar se já foi assinado
    if (antecipacao.termoAssinado) {
      return reply.status(400).send({
        error: "Este termo já foi assinado",
        dataAssinatura: antecipacao.dataAssinaturaTermo,
      });
    }

    // Gerar hash da assinatura
    const conteudoAssinatura = JSON.stringify({
      antecipacaoId: antecipacao.uuid,
      profissionalId: antecipacao.profissionalId,
      valorSolicitado: antecipacao.valorSolicitado,
      percentual: antecipacao.percentual,
      taxaAntecipacao: antecipacao.taxaAntecipacao,
      timestamp: agora.toISOString(),
    });

    const hashAssinatura = createHash("sha256").update(conteudoAssinatura).digest("hex");

    // Obter IP do cliente
    const ipAssinatura = request.ip || request.headers["x-forwarded-for"] || "unknown";

    // Atualizar antecipação com assinatura
    const updated = await prisma.antecipacao.update({
      where: { id: antecipacao.id },
      data: {
        termoAssinado: true,
        dataAssinaturaTermo: parseUTCDate(agora.toISOString()),
        hashAssinatura,
        ipAssinatura: String(ipAssinatura),
      },
    });

    // Enviar email de confirmação
    try {
      await sendTermoAssinadoEmail({
        profissionalNome: antecipacao.plantao.profissional?.usuario?.nome || "Profissional",
        profissionalEmail: antecipacao.plantao.profissional?.usuario?.email || "",
        dataAssinatura: agora,
        numeroAntecipacao: antecipacao.uuid,
        clienteNome: antecipacao.plantao.cliente?.nome || "",
      });
    } catch (error) {
      console.error("Erro ao enviar email de confirmação:", error);
    }

    return reply.send({
      success: true,
      message: "Termo assinado com sucesso",
      dataAssinatura: updated.dataAssinaturaTermo,
      hashAssinatura: updated.hashAssinatura,
    });
  });
}
