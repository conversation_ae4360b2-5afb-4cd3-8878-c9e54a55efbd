import { useParams } from "@tanstack/react-router";
import { createFileRoute } from "@tanstack/react-router";
import { requireAdminRole } from "@/lib/route-guards";
import { LocalAtendimentoForm } from "@/pages/locais-atendimento/local-atendimento-form";

function NovoLocalCliente() {
  const { clienteId } = useParams({ from: "/cadastros/clientes/$clienteId/locais/novo" });

  return <LocalAtendimentoForm clienteId={clienteId} />;
}

export const Route = createFileRoute("/cadastros/clientes/$clienteId/locais/novo")({
  component: NovoLocalCliente,
  beforeLoad: async () => {
    await requireAdminRole();
  },
});
