import { useEffect, useState } from "react";
import { useNavigate, useParams } from "@tanstack/react-router";
import { useQuery, useMutation, useQueryClient } from "@tanstack/react-query";
import { useForm } from "@tanstack/react-form";
import { z } from "zod";
import { Save, ArrowLeft, Loader2, Pencil } from "lucide-react";
import { Link } from "@tanstack/react-router";
import { Button } from "@/components/ui/button";
import { Input } from "@/components/ui/input";
import { Label } from "@/components/ui/label";
import { Switch } from "@/components/ui/switch";
import { Checkbox } from "@/components/ui/checkbox";
import { Select, SelectContent, SelectItem, SelectTrigger, SelectValue } from "@/components/ui/select";
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from "@/components/ui/card";
import { toast } from "sonner";
import { api } from "@/lib/api";
import { formatCEP } from "@/lib/formatters";
import { CpfInput } from "@/components/ui/cpf-input";
import { PhoneInput } from "@/components/ui/phone-input";
import { buscarCEP } from "@/lib/address-service";
import { ESTADOS_BRASILEIROS } from "@/lib/constants";
import { useAuthStore } from "@/stores/use-auth.store";
import { useClientes } from "@/hooks/use-clientes";
import { MultiSelect } from "@/components/ui/multi-select";
import { formatCNPJ } from "@shared/utils";

const usuarioSchema = z.object({
  nome: z.string().min(1, "Nome é obrigatório"),
  email: z.email("Email inválido"),
  password: z.string().optional(),
  telefone: z.string().optional(),
  cpf: z
    .string()
    .optional()
    .refine((val) => {
      if (!val) return true;
      const numbers = val.replace(/\D/g, "");
      return numbers.length === 11;
    }, "CPF deve ter 11 dígitos"),
  cep: z
    .string()
    .optional()
    .refine((val) => {
      if (!val) return true;
      const numbers = val.replace(/\D/g, "");
      return numbers.length === 8;
    }, "CEP deve ter 8 dígitos"),
  logradouro: z.string().optional(),
  numero: z.string().optional(),
  complemento: z.string().optional(),
  bairro: z.string().optional(),
  cidade: z.string().optional(),
  uf: z.string().optional(),
  roles: z.array(z.string()).optional(),
  clienteIds: z.array(z.number()).optional(),
  ativo: z.boolean().optional(),
});

type UsuarioFormData = z.infer<typeof usuarioSchema>;

interface Role {
  id: number;
  nome: string;
  descricao: string | null;
  ativo: boolean;
}

interface UsuarioFormProps {
  id?: string;
  viewMode?: boolean;
}

export function UsuarioForm({ id, viewMode = false }: UsuarioFormProps = {}) {
  // Tenta obter o ID do parâmetro da rota, mas não falha se não existir
  let routeId: string | undefined;
  try {
    const params = useParams({ from: "/cadastros/usuarios/$id" });
    routeId = params.id;
  } catch {
    // Se estiver em /novo, não há parâmetro id
    routeId = undefined;
  }
  const actualId = id || routeId;
  const navigate = useNavigate();
  const queryClient = useQueryClient();
  const isEditing = !!actualId;
  const isViewMode = viewMode && isEditing;
  const [editMode, setEditMode] = useState(!isViewMode);
  const [selectedRoles, setSelectedRoles] = useState<string[]>([]);
  const [loadingCep, setLoadingCep] = useState(false);
  const isDisabled = isViewMode && !editMode;

  // Obter o usuário logado
  const currentUser = useAuthStore((state) => state.user);
  const isAdmin = currentUser?.roles?.includes("admin");
  const isMaster = currentUser?.roles?.includes("master");
  const canManageRoles = isAdmin || isMaster;

  // Buscar clientes disponíveis
  const { data: clientesData } = useClientes();
  const clientes = clientesData?.data || [];

  const handleCepSearch = async (cep: string) => {
    const cleanCep = cep.replace(/\D/g, "");
    if (cleanCep.length !== 8) return;

    setLoadingCep(true);
    try {
      const address = await buscarCEP(cleanCep);
      if (address) {
        form.setFieldValue("logradouro", address.logradouro);
        form.setFieldValue("bairro", address.bairro);
        form.setFieldValue("cidade", address.localidade);
        form.setFieldValue("uf", address.uf);
        toast.success("Endereço encontrado!");
      } else {
        toast.error("CEP não encontrado");
      }
    } catch (error) {
      toast.error("Erro ao buscar CEP");
    } finally {
      setLoadingCep(false);
    }
  };

  // Buscar roles disponíveis
  const { data: roles } = useQuery({
    queryKey: ["roles"],
    queryFn: async () => {
      return await api.get<Role[]>("/usuarios/roles/list");
    },
  });

  // Buscar usuário se estiver editando
  const { data: usuario, isLoading } = useQuery({
    queryKey: ["usuario", actualId],
    queryFn: async () => {
      if (!actualId) return null;
      return await api.get<any>(`/usuarios/${actualId}`);
    },
    enabled: isEditing,
  });

  // Mutation para criar/atualizar
  const saveMutation = useMutation({
    mutationFn: async (data: UsuarioFormData) => {
      if (isEditing) {
        return await api.put(`/usuarios/${actualId}`, data);
      } else {
        return await api.post("/usuarios", data);
      }
    },
    onSuccess: async () => {
      // Invalidar todas as queries relacionadas a usuários
      await queryClient.invalidateQueries({ queryKey: ["usuarios"] });
      if (isEditing && actualId) {
        // Invalidar também o cache do usuário específico
        await queryClient.invalidateQueries({ queryKey: ["usuario", actualId] });
      }
      toast.success(`Usuário ${isEditing ? "atualizado" : "criado"} com sucesso.`);
      navigate({ to: "/cadastros/usuarios" });
    },
    onError: (error: any) => {
      toast.error(error.response?.data?.error || "Ocorreu um erro ao salvar o usuário.");
    },
  });

  // Formulário
  const form = useForm({
    defaultValues: {
      nome: "",
      email: "",
      password: "",
      telefone: "",
      cpf: "",
      cep: "",
      logradouro: "",
      numero: "",
      complemento: "",
      bairro: "",
      cidade: "",
      uf: "",
      roles: [] as string[],
      clienteIds: [] as number[],
      ativo: true,
    },
    onSubmit: async ({ value }) => {
      const dataToSend: any = {
        ...value,
        roles: selectedRoles,
      };

      // Se estiver editando e a senha estiver vazia, não enviar
      if (isEditing && !dataToSend.password) {
        delete dataToSend.password;
      }

      // Se for criação, senha é obrigatória
      if (!isEditing && !dataToSend.password) {
        toast.error("Senha é obrigatória para novos usuários");
        return;
      }

      saveMutation.mutate(dataToSend);
    },
  });

  // Preencher formulário quando carregar usuário
  useEffect(() => {
    if (usuario) {
      form.setFieldValue("nome", usuario.nome || "");
      form.setFieldValue("email", usuario.email || "");
      // Remover formatação para que os campos sejam preenchidos corretamente
      form.setFieldValue("telefone", usuario.telefone?.replace(/\D/g, "") || "");
      form.setFieldValue("cpf", usuario.cpf?.replace(/\D/g, "") || "");
      form.setFieldValue("cep", usuario.cep?.replace(/\D/g, "") || "");
      form.setFieldValue("logradouro", usuario.logradouro || "");
      form.setFieldValue("numero", usuario.numero || "");
      form.setFieldValue("complemento", usuario.complemento || "");
      form.setFieldValue("bairro", usuario.bairro || "");
      form.setFieldValue("cidade", usuario.cidade || "");
      form.setFieldValue("uf", usuario.uf || "");
      form.setFieldValue("ativo", usuario.ativo !== false);
      // Extrair apenas os nomes das roles
      const roleNames = usuario.roles?.map((r: any) => (typeof r === "string" ? r : r.nome)) || [];
      setSelectedRoles(roleNames);
      // Extrair IDs dos clientes vinculados
      form.setFieldValue(
        "clienteIds",
        usuario.clientes?.map((c: any) => c.cliente?.id).filter((id: any): id is number => id !== undefined) || []
      );
    }
  }, [usuario, form]);

  const handleRoleToggle = (roleName: string) => {
    setSelectedRoles((prev) => (prev.includes(roleName) ? prev.filter((r) => r !== roleName) : [...prev, roleName]));
  };

  if (isLoading) {
    return <div>Carregando...</div>;
  }

  return (
    <div className="container mx-auto py-6 space-y-6">
      <div className="flex items-center justify-between">
        <div>
          <h1 className="text-3xl font-bold">
            {!isEditing ? "Novo Usuário" : isViewMode && !editMode ? "Visualizar Usuário" : "Editar Usuário"}
          </h1>
          <p className="text-gray-600">
            {!isEditing
              ? "Cadastre um novo usuário no sistema"
              : isViewMode && !editMode
                ? "Visualizando dados do usuário"
                : "Atualize os dados do usuário"}
          </p>
        </div>
        <div className="flex gap-2">
          {isViewMode && !editMode && (
            <Button onClick={() => setEditMode(true)}>
              <Pencil className="mr-2 h-4 w-4" />
              Editar
            </Button>
          )}
          <Link to="/cadastros/usuarios">
            <Button variant="outline">
              <ArrowLeft className="mr-2 h-4 w-4" />
              Voltar
            </Button>
          </Link>
        </div>
      </div>

      <form
        onSubmit={(e) => {
          e.preventDefault();
          e.stopPropagation();
          form.handleSubmit();
        }}
      >
        <div className="grid gap-6">
          {/* Dados Básicos */}
          <Card>
            <CardHeader>
              <CardTitle>Dados Básicos</CardTitle>
              <CardDescription>Informações básicas do usuário</CardDescription>
            </CardHeader>
            <CardContent className="grid gap-4">
              <div className="grid grid-cols-2 gap-4">
                <form.Field name="nome">
                  {(field) => (
                    <div className="space-y-2">
                      <Label htmlFor="nome">Nome *</Label>
                      <Input
                        id="nome"
                        value={field.state.value}
                        onChange={(e) => field.handleChange(e.target.value)}
                        onBlur={field.handleBlur}
                        disabled={isDisabled}
                      />
                      {field.state.meta.errors && field.state.meta.errors.length > 0 && (
                        <p className="text-sm text-red-500">{field.state.meta.errors.join(", ")}</p>
                      )}
                    </div>
                  )}
                </form.Field>

                <form.Field name="email">
                  {(field) => (
                    <div className="space-y-2">
                      <Label htmlFor="email">Email *</Label>
                      <Input
                        id="email"
                        type="email"
                        value={field.state.value}
                        onChange={(e) => field.handleChange(e.target.value)}
                        onBlur={field.handleBlur}
                        disabled={isDisabled}
                      />
                      {field.state.meta.errors && field.state.meta.errors.length > 0 && (
                        <p className="text-sm text-red-500">{field.state.meta.errors.join(", ")}</p>
                      )}
                    </div>
                  )}
                </form.Field>
              </div>

              <div className="grid grid-cols-2 gap-4">
                <form.Field name="password">
                  {(field) => (
                    <div className="space-y-2">
                      <Label htmlFor="password">Senha {!isEditing && "*"}</Label>
                      <Input
                        id="password"
                        type="password"
                        value={field.state.value}
                        onChange={(e) => field.handleChange(e.target.value)}
                        onBlur={field.handleBlur}
                        placeholder={isEditing ? "Deixe em branco para manter a atual" : ""}
                        disabled={isDisabled}
                      />
                      {field.state.meta.errors && field.state.meta.errors.length > 0 && (
                        <p className="text-sm text-red-500">{field.state.meta.errors.join(", ")}</p>
                      )}
                    </div>
                  )}
                </form.Field>

                <form.Field name="telefone">
                  {(field) => (
                    <PhoneInput
                      id="telefone"
                      label="Telefone"
                      value={field.state.value || ""}
                      onChange={field.handleChange}
                      onBlur={field.handleBlur}
                      disabled={isDisabled}
                    />
                  )}
                </form.Field>
              </div>

              <form.Field name="cpf">
                {(field) => (
                  <CpfInput
                    id="cpf"
                    label="CPF"
                    value={field.state.value || ""}
                    onChange={field.handleChange}
                    onBlur={field.handleBlur}
                    error={field.state.meta.errors?.join(", ")}
                    disabled={isDisabled}
                  />
                )}
              </form.Field>
            </CardContent>
          </Card>

          {/* Endereço */}
          <Card>
            <CardHeader>
              <CardTitle>Endereço</CardTitle>
              <CardDescription>Informações de endereço do usuário</CardDescription>
            </CardHeader>
            <CardContent className="grid gap-4">
              <div className="grid grid-cols-3 gap-4">
                <form.Field name="cep">
                  {(field) => (
                    <div className="space-y-2">
                      <Label htmlFor="cep">CEP</Label>
                      <div className="relative">
                        <Input
                          id="cep"
                          value={formatCEP(field.state.value || "")}
                          onChange={(e) => field.handleChange(e.target.value.replace(/\D/g, ""))}
                          onBlur={(e) => {
                            field.handleBlur();
                            handleCepSearch(e.target.value);
                          }}
                          placeholder="00000-000"
                          maxLength={9}
                          disabled={loadingCep || isDisabled}
                        />
                        {loadingCep && (
                          <Loader2 className="absolute right-2 top-2.5 h-4 w-4 animate-spin text-muted-foreground" />
                        )}
                      </div>
                    </div>
                  )}
                </form.Field>

                <form.Field name="logradouro">
                  {(field) => (
                    <div className="space-y-2 col-span-2">
                      <Label htmlFor="logradouro">Logradouro</Label>
                      <Input
                        id="logradouro"
                        value={field.state.value}
                        onChange={(e) => field.handleChange(e.target.value)}
                        onBlur={field.handleBlur}
                        disabled={isDisabled}
                      />
                    </div>
                  )}
                </form.Field>
              </div>

              <div className="grid grid-cols-3 gap-4">
                <form.Field name="numero">
                  {(field) => (
                    <div className="space-y-2">
                      <Label htmlFor="numero">Número</Label>
                      <Input
                        id="numero"
                        value={field.state.value}
                        onChange={(e) => field.handleChange(e.target.value)}
                        onBlur={field.handleBlur}
                        disabled={isDisabled}
                      />
                    </div>
                  )}
                </form.Field>

                <form.Field name="complemento">
                  {(field) => (
                    <div className="space-y-2 col-span-2">
                      <Label htmlFor="complemento">Complemento</Label>
                      <Input
                        id="complemento"
                        value={field.state.value}
                        onChange={(e) => field.handleChange(e.target.value)}
                        onBlur={field.handleBlur}
                        disabled={isDisabled}
                      />
                    </div>
                  )}
                </form.Field>
              </div>

              <div className="grid grid-cols-3 gap-4">
                <form.Field name="bairro">
                  {(field) => (
                    <div className="space-y-2">
                      <Label htmlFor="bairro">Bairro</Label>
                      <Input
                        id="bairro"
                        value={field.state.value}
                        onChange={(e) => field.handleChange(e.target.value)}
                        onBlur={field.handleBlur}
                        disabled={isDisabled}
                      />
                    </div>
                  )}
                </form.Field>

                <form.Field name="cidade">
                  {(field) => (
                    <div className="space-y-2">
                      <Label htmlFor="cidade">Cidade</Label>
                      <Input
                        id="cidade"
                        value={field.state.value}
                        onChange={(e) => field.handleChange(e.target.value)}
                        onBlur={field.handleBlur}
                        disabled={isDisabled}
                      />
                    </div>
                  )}
                </form.Field>

                <form.Field name="uf">
                  {(field) => (
                    <div className="space-y-2">
                      <Label htmlFor="uf">Estado</Label>
                      <Select value={field.state.value || ""} onValueChange={field.handleChange} disabled={isDisabled}>
                        <SelectTrigger>
                          <SelectValue placeholder="Selecione o estado" />
                        </SelectTrigger>
                        <SelectContent>
                          {ESTADOS_BRASILEIROS.map((estado) => (
                            <SelectItem key={estado.value} value={estado.value}>
                              {estado.label}
                            </SelectItem>
                          ))}
                        </SelectContent>
                      </Select>
                    </div>
                  )}
                </form.Field>
              </div>
            </CardContent>
          </Card>

          {/* Permissões - Apenas para admin e master */}
          {canManageRoles && (
            <Card>
              <CardHeader>
                <CardTitle>Permissões</CardTitle>
                <CardDescription>Defina os perfis de acesso do usuário</CardDescription>
              </CardHeader>
              <CardContent className="space-y-4">
                <div className="space-y-3">
                  {roles
                    ?.filter((role) => {
                      // Se o usuário logado for admin, não mostrar a opção master
                      if (isAdmin && !isMaster && role.nome === "master") {
                        return false;
                      }
                      return true;
                    })
                    .map((role) => (
                      <div key={role.id} className="flex items-center space-x-3">
                        <Checkbox
                          id={`role-${role.id}`}
                          checked={selectedRoles.includes(role.nome)}
                          onCheckedChange={() => handleRoleToggle(role.nome)}
                          disabled={isDisabled}
                        />
                        <div className="flex-1">
                          <Label htmlFor={`role-${role.id}`} className="cursor-pointer">
                            {role.nome}
                          </Label>
                          {role.descricao && <p className="text-sm text-gray-500">{role.descricao}</p>}
                        </div>
                      </div>
                    ))}
                </div>

                {isEditing && (
                  <div className="flex items-center space-x-3 pt-4 border-t">
                    <form.Field name="ativo">
                      {(field) => (
                        <>
                          <Switch
                            id="ativo"
                            checked={field.state.value}
                            onCheckedChange={field.handleChange}
                            disabled={isDisabled}
                          />
                          <Label htmlFor="ativo" className="cursor-pointer">
                            Usuário ativo
                          </Label>
                        </>
                      )}
                    </form.Field>
                  </div>
                )}
              </CardContent>
            </Card>
          )}

          {/* Vínculos com Clientes */}
          <Card>
            <CardHeader>
              <CardTitle>Vínculos com Clientes</CardTitle>
              <CardDescription>Selecione os clientes aos quais este usuário terá acesso</CardDescription>
            </CardHeader>
            <CardContent className="space-y-4">
              <form.Field name="clienteIds">
                {(field) => (
                  <div className="space-y-2">
                    <Label htmlFor={field.name}>Clientes Vinculados</Label>
                    <MultiSelect
                      options={clientes.map((c) => ({
                        value: c.id,
                        label: `${c.nome} (${formatCNPJ(c.cnpj)})`,
                      }))}
                      value={field.state.value || []}
                      onChange={(value) => field.handleChange(value as number[])}
                      placeholder="Selecione os clientes"
                      searchPlaceholder="Buscar cliente..."
                      emptyText="Nenhum cliente encontrado"
                      disabled={isDisabled}
                    />
                    <p className="text-sm text-muted-foreground">
                      O usuário poderá visualizar e gerenciar apenas os dados dos clientes selecionados.
                    </p>
                  </div>
                )}
              </form.Field>
            </CardContent>
          </Card>

          {/* Ações */}
          {!isDisabled && (
            <div className="flex justify-end gap-3">
              <Button
                variant="outline"
                onClick={() => (isViewMode ? setEditMode(false) : navigate({ to: "/cadastros/usuarios" }))}
              >
                Cancelar
              </Button>
              <form.Subscribe selector={(state) => [state.canSubmit, state.isSubmitting]}>
                {([canSubmit, isSubmitting]) => (
                  <Button type="submit" disabled={!canSubmit || isSubmitting}>
                    <Save className="mr-2 h-4 w-4" />
                    {isSubmitting ? "Salvando..." : "Salvar"}
                  </Button>
                )}
              </form.Subscribe>
            </div>
          )}
        </div>
      </form>
    </div>
  );
}
