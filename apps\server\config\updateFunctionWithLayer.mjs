import Zip from 'adm-zip';
import path from 'path';
import fs from 'fs';
import { exec } from 'child_process';
import { promisify } from 'util';
import { verifyDir, getParams, getArg, getFunctionDescription } from './functions.mjs';
import { dirname } from 'path';
import { fileURLToPath } from 'url';

const execAsync = promisify(exec);
const __filename = fileURLToPath(import.meta.url);
const __dirname = dirname(__filename);

const getOriginDir = (lambdaName = '') => {
    return path.resolve(__dirname, '..', 'build', lambdaName);
};

const dirZippedLambda = 'zipped-lambda-output';
const zippedLambdaDIR = path.resolve(__dirname, dirZippedLambda);

const getTargetDirWithZip = (fileName = '') => {
    return path.resolve(__dirname, dirZippedLambda, `${fileName}.zip`);
};

const createLayerZip = (originDir, targetZipPath) => {
    console.log('📦 Creating Layer zip for node_modules...');
    const zip = new Zip();
    
    // Create nodejs/node_modules structure required for Lambda layers
    const nodeModulesPath = path.join(originDir, 'node_modules');
    
    if (fs.existsSync(nodeModulesPath)) {
        // Add node_modules under nodejs directory structure
        zip.addLocalFolder(nodeModulesPath, 'nodejs/node_modules');
        zip.writeZip(targetZipPath);
        
        const stats = fs.statSync(targetZipPath);
        const fileSizeInMB = stats.size / (1024 * 1024);
        console.log(`✅ Layer zip created: ${targetZipPath}`);
        console.log(`📊 Layer size: ${fileSizeInMB.toFixed(2)} MB`);
        
        return { 
            fileSizeInBytes: stats.size,
            fileSizeInMB,
            hasNodeModules: true
        };
    }
    
    console.log('⚠️  No node_modules found in build directory');
    return { 
        fileSizeInBytes: 0,
        fileSizeInMB: 0,
        hasNodeModules: false
    };
};

const createCodeZip = (originDir, targetZipPath) => {
    console.log('📦 Creating code zip (without node_modules)...');
    const zip = new Zip();
    
    // Add all files except node_modules
    const files = fs.readdirSync(originDir);
    files.forEach(file => {
        if (file !== 'node_modules') {
            const filePath = path.join(originDir, file);
            const stats = fs.statSync(filePath);
            
            if (stats.isDirectory()) {
                zip.addLocalFolder(filePath, file);
            } else {
                zip.addLocalFile(filePath);
            }
        }
    });
    
    zip.writeZip(targetZipPath);
    
    const stats = fs.statSync(targetZipPath);
    const fileSizeInMB = stats.size / (1024 * 1024);
    console.log(`✅ Code zip created: ${targetZipPath}`);
    console.log(`📊 Code size: ${fileSizeInMB.toFixed(2)} MB`);
    
    return { 
        fileSizeInBytes: stats.size,
        fileSizeInMB 
    };
};

const publishLayer = async ({ 
    layerName, 
    zipPath, 
    profile, 
    region,
    runtime = 'nodejs18.x'
}) => {
    console.log(`☁️  Publishing Lambda Layer: ${layerName}`);
    
    const compatibleRuntimes = runtime ? `--compatible-runtimes ${runtime}` : '';
    const command = `aws lambda publish-layer-version --layer-name ${layerName} --zip-file fileb://"${zipPath}" ${compatibleRuntimes} --profile ${profile} --region ${region}`;
    
    try {
        const { stdout, stderr } = await execAsync(command);
        if (stderr && !stderr.includes('publish')) {
            console.warn('⚠️ Warning:', stderr);
        }
        
        const result = JSON.parse(stdout);
        console.log(`✅ Layer published successfully: ${layerName} (Version ${result.Version})`);
        return result;
    } catch (error) {
        console.error('❌ Error publishing layer:', error.message);
        throw error;
    }
};

const updateFunctionWithLayer = async ({ 
    functionName,
    layerArn,
    zipPath,
    profile,
    region,
    runtime,
    role
}) => {
    console.log('🔄 Updating Lambda function...');
    
    let exists = false;
    try {
        exists = await getFunctionDescription({
            name: functionName,
            profile,
            region,
        });
    } catch (error) {
        if (error.message.includes('Function not found')) {
            exists = false;
        }
    }
    
    let command;
    
    if (exists) {
        // First update the function code
        command = `aws lambda update-function-code --function-name ${functionName} --zip-file fileb://"${zipPath}" --publish --profile ${profile} --region ${region}`;
        
        try {
            const { stdout, stderr } = await execAsync(command);
            if (stderr) {
                console.warn('⚠️ Warning:', stderr);
            }
            console.log('✅ Function code updated');
        } catch (error) {
            console.error('❌ Error updating function code:', error.message);
            throw error;
        }
        
        // Then update the layer configuration
        if (layerArn) {
            command = `aws lambda update-function-configuration --function-name ${functionName} --layers ${layerArn} --profile ${profile} --region ${region}`;
            
            try {
                const { stdout, stderr } = await execAsync(command);
                if (stderr) {
                    console.warn('⚠️ Warning:', stderr);
                }
                console.log('✅ Function layer configuration updated');
                return JSON.parse(stdout);
            } catch (error) {
                console.error('❌ Error updating layer configuration:', error.message);
                throw error;
            }
        }
    } else {
        if (!runtime || !role) {
            throw new Error('Runtime and Role are required for creating a new function');
        }
        
        const layerConfig = layerArn ? `--layers ${layerArn}` : '';
        command = `aws lambda create-function --function-name ${functionName} --zip-file fileb://"${zipPath}" --handler index.handler --runtime ${runtime} --role ${role} ${layerConfig} --profile ${profile} --region ${region}`;
        
        try {
            const { stdout, stderr } = await execAsync(command);
            if (stderr) {
                console.warn('⚠️ Warning:', stderr);
            }
            console.log('✅ Lambda function created successfully');
            return JSON.parse(stdout);
        } catch (error) {
            console.error('❌ Error creating function:', error.message);
            throw error;
        }
    }
};

const getLayerVersions = async ({ layerName, profile, region }) => {
    const command = `aws lambda list-layer-versions --layer-name ${layerName} --profile ${profile} --region ${region}`;
    
    try {
        const { stdout } = await execAsync(command);
        const result = JSON.parse(stdout);
        return result.LayerVersions || [];
    } catch (error) {
        // Layer might not exist yet
        return [];
    }
};

const cleanupOldLayerVersions = async ({ 
    layerName, 
    keepVersions = 3, 
    profile, 
    region 
}) => {
    console.log('🧹 Checking for old layer versions to cleanup...');
    
    const versions = await getLayerVersions({ layerName, profile, region });
    
    if (versions.length > keepVersions) {
        const versionsToDelete = versions.slice(keepVersions);
        
        for (const version of versionsToDelete) {
            const command = `aws lambda delete-layer-version --layer-name ${layerName} --version-number ${version.Version} --profile ${profile} --region ${region}`;
            
            try {
                await execAsync(command);
                console.log(`  Deleted layer version ${version.Version}`);
            } catch (error) {
                console.warn(`  Failed to delete layer version ${version.Version}:`, error.message);
            }
        }
    }
};

const cleanupZipDirectory = () => {
    if (fs.existsSync(zippedLambdaDIR)) {
        fs.rmSync(zippedLambdaDIR, { recursive: true, force: true });
        console.log('🧹 Cleaned up temporary files');
    }
};

const main = async () => {
    try {
        console.log('🚀 Starting Lambda deployment with Layer separation...\n');
        
        const { lambdaName, profile, region } = await getParams();
        
        if (!lambdaName) {
            throw new Error('lambdaName parameter is required');
        }
        
        const layerName = getArg('layerName') || `${lambdaName}-deps`;
        const runtime = getArg('runtime') || 'nodejs18.x';
        const role = getArg('role');
        const skipLayer = getArg('skipLayer') === 'true';
        const forceLayer = getArg('forceLayer') === 'true';
        
        console.log('📋 Configuration:');
        console.log(`  - Lambda Name: ${lambdaName}`);
        console.log(`  - Layer Name: ${layerName}`);
        console.log(`  - Profile: ${profile}`);
        console.log(`  - Region: ${region}`);
        console.log(`  - Runtime: ${runtime}`);
        console.log(`  - Skip Layer: ${skipLayer}`);
        console.log(`  - Force Layer Update: ${forceLayer}\n`);
        
        const originDir = getOriginDir(lambdaName);
        
        if (!fs.existsSync(originDir)) {
            throw new Error(`Build directory not found: ${originDir}`);
        }
        
        verifyDir(originDir);
        
        if (!fs.existsSync(zippedLambdaDIR)) {
            fs.mkdirSync(zippedLambdaDIR, { recursive: true });
        }
        
        let layerArn = null;
        
        // Handle Layer creation/update
        if (!skipLayer) {
            const layerZipPath = getTargetDirWithZip(`${layerName}-layer`);
            const { hasNodeModules } = createLayerZip(originDir, layerZipPath);
            
            if (hasNodeModules) {
                // Check if we need to update the layer
                const existingVersions = await getLayerVersions({ 
                    layerName, 
                    profile, 
                    region 
                });
                
                const shouldUpdateLayer = forceLayer || existingVersions.length === 0;
                
                if (shouldUpdateLayer) {
                    const layerResult = await publishLayer({
                        layerName,
                        zipPath: layerZipPath,
                        profile,
                        region,
                        runtime
                    });
                    
                    layerArn = layerResult.LayerVersionArn;
                    
                    // Cleanup old versions
                    await cleanupOldLayerVersions({
                        layerName,
                        keepVersions: 3,
                        profile,
                        region
                    });
                } else {
                    // Use the latest existing layer version
                    layerArn = existingVersions[0].LayerVersionArn;
                    console.log(`ℹ️  Using existing layer version: ${existingVersions[0].Version}`);
                }
            }
        }
        
        // Create and deploy function code (without node_modules)
        const codeZipPath = getTargetDirWithZip(lambdaName);
        createCodeZip(originDir, codeZipPath);
        
        await updateFunctionWithLayer({
            functionName: lambdaName,
            layerArn,
            zipPath: codeZipPath,
            profile,
            region,
            runtime,
            role
        });
        
        cleanupZipDirectory();
        console.log('\n🎉 Deployment completed successfully!');
        
        if (layerArn) {
            console.log(`📌 Layer ARN: ${layerArn}`);
        }
        
    } catch (error) {
        console.log('\n');
        console.log('\x1b[31m%s\x1b[0m', '❌ Error in deployment:');
        console.log(error.message);
        console.log('\n');
        cleanupZipDirectory();
    } finally {
        process.exit();
    }
};

main();