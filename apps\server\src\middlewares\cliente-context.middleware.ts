import type { FastifyRequest } from "fastify";
import { prisma } from "../lib/prisma";

export async function clienteContextMiddleware(request: FastifyRequest) {
  const clienteUuid = request.headers["x-cliente-id"] as string;

  try {
    const cliente = await prisma.cliente.findUnique({
      where: { uuid: clienteUuid },
      select: { id: true, uuid: true, fusoHorario: true },
    });

    request.clienteId = cliente?.id || 0;
    request.clienteUuid = cliente?.uuid || "";
    request.fusoHorario = cliente?.fusoHorario || "America/Sao_Paulo";
  } catch (error) {
    console.error("Erro ao verificar cliente:", error);
  }
}
