import { useState, useEffect } from "react";
import { useMutation, useQueryClient } from "@tanstack/react-query";
import { api } from "@/lib/api";
import { Di<PERSON>, DialogContent, DialogHeader, DialogTitle, DialogFooter } from "@/components/ui/dialog";
import { Button } from "@/components/ui/button";
import { Label } from "@/components/ui/label";
import { Input } from "@/components/ui/input";
import { Textarea } from "@/components/ui/textarea";
import { toast } from "sonner";

interface EspecialidadeDialogProps {
  open: boolean;
  onOpenChange: (open: boolean) => void;
  onSuccess?: (especialidade: any) => void;
  initialNome?: string;
}

export function EspecialidadeDialog({ open, onOpenChange, onSuccess, initialNome = "" }: EspecialidadeDialogProps) {
  const [nome, setNome] = useState(initialNome);
  const [descricao, setDescricao] = useState("");
  const queryClient = useQueryClient();

  // Atualiza o nome quando o initialNome muda ou quando o dialog abre
  useEffect(() => {
    if (open && initialNome) {
      setNome(initialNome);
    }
    // Limpa a descrição quando abre o dialog
    if (open) {
      setDescricao("");
    }
  }, [open, initialNome]);

  // Limpa os campos quando fecha o dialog
  useEffect(() => {
    if (!open) {
      setNome("");
      setDescricao("");
    }
  }, [open]);

  const createMutation = useMutation({
    mutationFn: async (data: { nome: string; descricao?: string }) => {
      return api.post("/especialidades", data);
    },
    onSuccess: (data) => {
      queryClient.invalidateQueries({ queryKey: ["especialidades"] });
      toast.success("Especialidade criada com sucesso!");
      onSuccess?.(data);
      onOpenChange(false);
      // Limpa os campos ao fechar
      setNome("");
      setDescricao("");
    },
    onError: (error: any) => {
      toast.error(error.message || "Erro ao criar especialidade");
    },
  });

  const handleSubmit = (e: React.FormEvent) => {
    e.preventDefault();
    if (!nome.trim()) {
      toast.error("Por favor, insira o nome da especialidade.");
      return;
    }
    createMutation.mutate({ nome, descricao: descricao || undefined });
  };

  return (
    <Dialog open={open} onOpenChange={onOpenChange}>
      <DialogContent className="sm:max-w-[425px]">
        <form onSubmit={handleSubmit}>
          <DialogHeader>
            <DialogTitle>Nova Especialidade</DialogTitle>
          </DialogHeader>
          <div className="grid gap-4 py-4">
            <div className="grid gap-2">
              <Label htmlFor="nome">Nome *</Label>
              <Input
                id="nome"
                value={nome}
                onChange={(e) => setNome(e.target.value)}
                placeholder="Ex: Cardiologia"
                required
              />
            </div>
            <div className="grid gap-2">
              <Label htmlFor="descricao">Descrição</Label>
              <Textarea
                id="descricao"
                value={descricao}
                onChange={(e) => setDescricao(e.target.value)}
                placeholder="Descrição da especialidade (opcional)"
                rows={3}
              />
            </div>
          </div>
          <DialogFooter>
            <Button type="button" variant="outline" onClick={() => onOpenChange(false)}>
              Cancelar
            </Button>
            <Button type="submit" disabled={createMutation.isPending}>
              {createMutation.isPending ? "Criando..." : "Criar"}
            </Button>
          </DialogFooter>
        </form>
      </DialogContent>
    </Dialog>
  );
}
