import { create } from "zustand";
import type { Dia<PERSON><PERSON><PERSON>, TipoTurno } from "@/components/calendario-plantao";
import {
  createLocalDate,
  getDateOnly,
  createCurrentLocalDate,
  getCurrentDate,
  getDaysInMonthForDate,
  parseISO,
} from "@/lib/utils";

export interface PlantaoForm {
  clienteId: string;
  localAtendimentoId: string | null;
  profissionalId: string;
  mes: number;
  ano: number;
  modalidadeTrabalho: "PLANTONISTA" | "MENSALISTA" | "COORDENADOR" | "SUPERVISOR" | "DIRETOR";
  tipoFechamento: "DIARIO" | "SEMANAL" | "QUINZENAL" | "MENSAL";
  dataInicial: Date | string | null;
  dataFinal?: Date | string | null;
  tipoValor: "HORA" | "DIARIA" | "PLANTAO" | "MENSAL";
  valorBase?: number;
  prazoPagamentoDias?: number;
  horaInicio?: string;
  horaFim?: string;
  observacoes?: string;
  diasPlantao: DiaPlantao[];
  tipoTurno: TipoTurno;
  // Armazena dias selecionados por mês/ano para todo o período
  diasPorMes?: Record<string, DiaPlantao[]>;
}

interface PlantaoStore {
  // Estado do formulário
  form: PlantaoForm;

  // Ações para atualizar campos específicos
  setDataInicial: (data: string) => void;
  setDataFinal: (data: Date | string) => void;
  setClienteId: (id: string) => void;
  setLocalAtendimentoId: (id: string | null) => void;
  setProfissionalId: (id: string) => void;
  setMes: (mes: number) => void;
  setAno: (ano: number) => void;
  setModalidadeTrabalho: (tipo: PlantaoForm["modalidadeTrabalho"]) => void;
  setTipoFechamento: (tipo: PlantaoForm["tipoFechamento"]) => void;
  setTipoValor: (tipo: PlantaoForm["tipoValor"]) => void;
  setValorBase: (valor?: number) => void;
  setPrazoPagamentoDias: (dias?: number) => void;
  setHoraInicio: (hora?: string) => void;
  setHoraFim: (hora?: string) => void;
  setObservacoes: (observacoes?: string) => void;
  setDiasPlantao: (dias: DiaPlantao[]) => void;
  setTipoTurno: (tipo: TipoTurno) => void;

  // Ações para atualizar múltiplos campos
  updateForm: (updates: Partial<PlantaoForm>) => void;

  // Ação para salvar dias de um mês específico
  saveDiasForMonth: (mes: number, ano: number, dias: DiaPlantao[]) => void;

  // Resetar formulário
  resetForm: () => void;

  // Função para calcular dias no mês
  getDaysInMonth: () => number;

  // Função para calcular total de dias
  calculateTotalDays: () => number | null;

  // Função para calcular total de horas
  calculateTotalHours: () => number | null;

  // Função para atualizar dias em todo o período
  setDiasSemanaTodoPeriodo: (diaSemana: number, ativar: boolean) => void;
}

const getInitialForm = (): PlantaoForm => {
  const hoje = createCurrentLocalDate();
  return {
    clienteId: "",
    localAtendimentoId: "",
    profissionalId: "",
    mes: hoje.getMonth() + 1,
    ano: hoje.getFullYear(),
    modalidadeTrabalho: "PLANTONISTA",
    tipoFechamento: "MENSAL",
    dataInicial: getDateOnly(getCurrentDate().toISOString()),
    dataFinal: undefined,
    tipoValor: "HORA",
    valorBase: undefined,
    prazoPagamentoDias: undefined,
    horaInicio: "",
    horaFim: "",
    observacoes: "",
    diasPlantao: [],
    tipoTurno: "diurno",
    diasPorMes: undefined,
  };
};

export const usePlantaoStore = create<PlantaoStore>((set, get) => ({
  form: getInitialForm(),

  setDataInicial: (data: string) =>
    set((state) => {
      // Quando a data inicial muda, também atualizar o mês/ano do calendário
      const dataInicialDate = parseISO(data);
      return {
        form: {
          ...state.form,
          dataInicial: data,
          mes: dataInicialDate.getMonth() + 1,
          ano: dataInicialDate.getFullYear(),
        },
      };
    }),

  setDataFinal: (data: Date | string) =>
    set((state) => ({
      form: { ...state.form, dataFinal: data },
    })),

  setClienteId: (id: string) =>
    set((state) => ({
      form: { ...state.form, clienteId: id, localAtendimentoId: "" },
    })),

  setLocalAtendimentoId: (id: string | null) =>
    set((state) => ({
      form: { ...state.form, localAtendimentoId: id },
    })),

  setProfissionalId: (id: string) =>
    set((state) => ({
      form: { ...state.form, profissionalId: id },
    })),

  setMes: (mes: number) =>
    set((state) => ({
      form: { ...state.form, mes },
    })),

  setAno: (ano: number) =>
    set((state) => ({
      form: { ...state.form, ano },
    })),

  setModalidadeTrabalho: (tipo: PlantaoForm["modalidadeTrabalho"]) =>
    set((state) => ({
      form: { ...state.form, modalidadeTrabalho: tipo },
    })),

  setTipoFechamento: (tipo: PlantaoForm["tipoFechamento"]) =>
    set((state) => ({
      form: { ...state.form, tipoFechamento: tipo },
    })),

  setTipoValor: (tipo: PlantaoForm["tipoValor"]) =>
    set((state) => ({
      form: { ...state.form, tipoValor: tipo },
    })),

  setValorBase: (valor?: number) =>
    set((state) => ({
      form: { ...state.form, valorBase: valor },
    })),

  setPrazoPagamentoDias: (dias?: number) =>
    set((state) => ({
      form: { ...state.form, prazoPagamentoDias: dias },
    })),

  setValorPlantao: (valor?: number) =>
    set((state) => ({
      form: { ...state.form, valorPlantao: valor },
    })),

  setHoraInicio: (hora?: string) =>
    set((state) => ({
      form: { ...state.form, horaInicio: hora },
    })),

  setHoraFim: (hora?: string) =>
    set((state) => ({
      form: { ...state.form, horaFim: hora },
    })),

  setPercentualAntecipacao: (percentual: number) =>
    set((state) => ({
      form: { ...state.form },
    })),

  setObservacoes: (observacoes?: string) =>
    set((state) => ({
      form: { ...state.form, observacoes },
    })),

  setDiasPlantao: (dias: DiaPlantao[]) =>
    set((state) => ({
      form: { ...state.form, diasPlantao: dias },
    })),

  setTipoTurno: (tipo: TipoTurno) =>
    set((state) => ({
      form: { ...state.form, tipoTurno: tipo },
    })),

  updateForm: (updates: Partial<PlantaoForm>) =>
    set((state) => ({
      form: { ...state.form, ...updates },
    })),

  saveDiasForMonth: (mes: number, ano: number, dias: DiaPlantao[]) =>
    set((state) => {
      const key = `${ano}-${mes}`;
      const diasPorMes = state.form.diasPorMes || {};

      return {
        form: {
          ...state.form,
          diasPorMes: {
            ...diasPorMes,
            [key]: dias,
          },
        },
      };
    }),

  resetForm: () => set({ form: getInitialForm() }),

  getDaysInMonth: () => {
    const { mes, ano } = get().form;
    return getDaysInMonthForDate(ano, mes);
  },

  calculateTotalDays: () => {
    const { dataInicial, dataFinal } = get().form;

    if (!dataInicial || !dataFinal) return null;

    const inicio = parseISO(dataInicial);
    const fim = parseISO(dataFinal);
    const diffTime = fim.getTime() - inicio.getTime();
    const diffDays = Math.ceil(diffTime / (1000 * 60 * 60 * 24)) + 1; // +1 para incluir o dia inicial

    return diffDays > 0 ? diffDays : null;
  },

  calculateTotalHours: () => {
    const store = get();
    const totalDays = store.calculateTotalDays();
    const { horaInicio, horaFim, tipoTurno } = store.form;

    if (!totalDays) return null;

    // Se não tiver horas definidas, usa valores padrão baseados no turno
    let horaInicioCalc = horaInicio || "";
    let horaFimCalc = horaFim || "";

    if (!horaInicio || !horaFim) {
      // Valores padrão baseados no tipo de turno
      if (tipoTurno === "diurno") {
        horaInicioCalc = "07:00";
        horaFimCalc = "19:00";
      } else if (tipoTurno === "noturno") {
        horaInicioCalc = "19:00";
        horaFimCalc = "07:00";
      } else {
        // 24h
        return totalDays * 24;
      }
    }

    const [horaIni, minIni] = horaInicioCalc.split(":").map(Number);
    const [horaFimNum, minFim] = horaFimCalc.split(":").map(Number);

    let horasPorDia = horaFimNum + minFim / 60 - (horaIni + minIni / 60);

    // Se o horário final for menor que o inicial (turno noturno), adiciona 24h
    if (horasPorDia < 0) {
      horasPorDia += 24;
    }

    return horasPorDia > 0 ? totalDays * horasPorDia : null;
  },

  setDiasSemanaTodoPeriodo: (diaSemana: number, ativar: boolean) => {
    const store = get();
    const { dataInicial, dataFinal, mes, ano, diasPlantao, tipoTurno, diasPorMes = {} } = store.form;

    if (!dataInicial || !dataFinal) return;

    const inicio = parseISO(dataInicial);
    const fim = parseISO(dataFinal);

    // Define os horários padrão baseados no tipo de turno
    interface HorarioTurno {
      inicio: string;
      fim: string;
      intervalo: string;
    }

    const horariosPreDefinidos: Record<string, HorarioTurno> = {
      diurno: { inicio: "07:00", fim: "19:00", intervalo: "01:00" },
      comercial: { inicio: "09:00", fim: "18:00", intervalo: "01:00" },
      noturno: { inicio: "19:00", fim: "07:00", intervalo: "01:00" },
      customizado: { inicio: "", fim: "", intervalo: "" },
    };

    const horarioPadrao = tipoTurno && tipoTurno !== "customizado" ? horariosPreDefinidos[tipoTurno] : undefined;

    // Cria/atualiza registro para todos os meses no período
    const novosDiasPorMes = { ...diasPorMes };
    let novosDiasPlantaoAtual: DiaPlantao[] | null = null;

    // Itera por todos os meses no período
    let currentMonth = createLocalDate(inicio.getFullYear(), inicio.getMonth() + 1, 1);
    const endMonth = createLocalDate(fim.getFullYear(), fim.getMonth() + 1, 1);

    while (currentMonth <= endMonth) {
      const targetMes = currentMonth.getMonth() + 1;
      const targetAno = currentMonth.getFullYear();
      const key = `${targetAno}-${targetMes}`;
      const daysInMonth = getDaysInMonthForDate(targetAno, targetMes);

      // Se é o mês atual, usa os dias do estado atual
      if (targetMes === mes && targetAno === ano) {
        const novosDias = [...diasPlantao];

        for (let dia = 1; dia <= daysInMonth; dia++) {
          const currentDate = createLocalDate(targetAno, targetMes, dia);

          // Verifica se está dentro do período E é o dia da semana correto
          if (currentDate >= inicio && currentDate <= fim && currentDate.getDay() === diaSemana) {
            const dataStr = `${targetAno}-${targetMes.toString().padStart(2, "0")}-${dia.toString().padStart(2, "0")}`;
            const index = novosDias.findIndex((d) => d.data === dataStr);
            if (index >= 0) {
              novosDias[index] = {
                ...novosDias[index],
                selecionado: ativar,
                horario: ativar ? novosDias[index].horario || horarioPadrao : undefined,
              };
            }
          }
        }

        novosDiasPlantaoAtual = novosDias;
        // Também salva no diasPorMes para consistência
        novosDiasPorMes[key] = novosDias;
      } else {
        // Para outros meses, cria ou atualiza o registro diasPorMes
        const diasDoMes = novosDiasPorMes[key] || [];

        // Garante que temos todos os dias do mês
        const diasCompletos: DiaPlantao[] = [];
        for (let dia = 1; dia <= daysInMonth; dia++) {
          const dataStr = `${targetAno}-${targetMes.toString().padStart(2, "0")}-${dia.toString().padStart(2, "0")}`;
          const existing = diasDoMes.find((d) => d.data === dataStr);
          const currentDate = createLocalDate(targetAno, targetMes, dia);
          const isDiaSemanaCorreto = currentDate.getDay() === diaSemana;
          const dentroPeríodo = currentDate >= inicio && currentDate <= fim;

          if (existing) {
            // Se já existe, atualiza apenas se é o dia da semana correto e está no período
            if (isDiaSemanaCorreto && dentroPeríodo) {
              diasCompletos.push({
                ...existing,
                selecionado: ativar,
                horario: ativar ? existing.horario || horarioPadrao : undefined,
              });
            } else {
              diasCompletos.push(existing);
            }
          } else {
            // Cria novo dia
            const dataStr = `${targetAno}-${targetMes.toString().padStart(2, "0")}-${dia.toString().padStart(2, "0")}`;
            diasCompletos.push({
              data: dataStr,
              selecionado: isDiaSemanaCorreto && dentroPeríodo && ativar,
              horario: isDiaSemanaCorreto && dentroPeríodo && ativar ? horarioPadrao : undefined,
            });
          }
        }

        novosDiasPorMes[key] = diasCompletos;
      }

      // Próximo mês
      currentMonth.setMonth(currentMonth.getMonth() + 1);
    }

    // Atualiza o store com os novos dados
    if (novosDiasPlantaoAtual) {
      set((state) => ({
        form: {
          ...state.form,
          diasPlantao: novosDiasPlantaoAtual,
          diasPorMes: novosDiasPorMes,
        },
      }));
    } else {
      set((state) => ({
        form: {
          ...state.form,
          diasPorMes: novosDiasPorMes,
        },
      }));
    }
  },
}));
