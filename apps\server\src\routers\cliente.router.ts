import { authorize } from "@/middlewares/auth.middleware";
import { prisma } from "../lib/prisma";
import { Prisma } from "@prisma/client";
import {
  createClienteSchema,
  updateClienteSchema,
  clienteQuerySchema,
  type CreateClienteInput,
  type UpdateClienteInput,
  type ClienteQuery,
} from "../schemas/cliente.schema";
import type { FastifyTypedInstance } from "@/types";

const tags = ["Clientes"];
const security = [{ bearerAuth: [] }];

export async function clienteRouter(fastify: FastifyTypedInstance) {
  // Listar clientes
  fastify.get<{ Querystring: ClienteQuery }>("/clientes", async (request, reply) => {
    const parsedQuery = request.query;
    const { page = 1, limit = 10, search, ativo } = parsedQuery;

    const pageNum = Number(page);
    const limitNum = Number(limit);
    const skip = (pageNum - 1) * limitNum;

    const where = {
      ...(ativo !== undefined && {
        ativo: ativo ? true : false,
      }),
      ...(search && {
        OR: [{ nome: { contains: search } }, { cnpj: { contains: search } }, { email: { contains: search } }],
      }),
    };

    const [clientes, total] = await Promise.all([
      prisma.cliente.findMany({
        where,
        skip,
        take: limitNum,
        include: {
          locaisAtendimento: true,
          _count: {
            select: {
              plantoes: true,
            },
          },
        },
        orderBy: { createdAt: "desc" },
      }),
      prisma.cliente.count({ where }),
    ]);

    return reply.send({
      data: clientes,
      meta: {
        page: pageNum,
        limit: limitNum,
        total,
        totalPages: Math.ceil(total / limitNum),
      },
    });
  });

  // Verificar se CNPJ já existe
  fastify.get<{ Params: { cnpj: string } }>("/clientes/check-cnpj/:cnpj", async (request, reply) => {
    const { cnpj } = request.params;

    const cliente = await prisma.cliente.findUnique({
      where: { cnpj },
      select: { uuid: true, nome: true },
    });

    return reply.send({ exists: !!cliente, cliente });
  });

  // Buscar cliente por UUID
  fastify.get<{ Params: { uuid: string } }>("/clientes/:uuid", async (request, reply) => {
    const { uuid } = request.params;

    const userIsMasterOrAdmin = request.user?.roles.some((role) => role === "master" || role === "admin");

    const cliente = await prisma.cliente.findUnique({
      where: {
        uuid,
        ...(userIsMasterOrAdmin
          ? {}
          : {
              AND: {
                id: { in: request.user?.clientes || [] },
              },
            }),
      },
      include: {
        locaisAtendimento: true,
        plantoes: {
          include: {
            profissional: true,
            localAtendimento: true,
          },
        },
      },
    });

    if (!cliente) {
      return reply.status(404).send({ error: "Cliente não encontrado" });
    }

    return reply.send(cliente);
  });

  // Criar cliente
  fastify.post<{ Body: CreateClienteInput }>(
    "/clientes",
    { preHandler: [authorize("admin", "master")] },
    async (request, reply) => {
      // Validar e fazer parse dos dados usando o schema
      const parseResult = createClienteSchema.safeParse(request.body);

      if (!parseResult.success) {
        return reply.status(400).send({
          error: "Dados inválidos",
          issues: parseResult.error.issues,
        });
      }

      const data = parseResult.data;

      // Verificar se documento já existe
      const existingCliente = await prisma.cliente.findUnique({
        where: { cnpj: data.cnpj },
      });

      if (existingCliente) {
        return reply.status(400).send({ error: "CNPJ já cadastrado" });
      }

      const cliente = await prisma.cliente.create({
        data,
        include: {
          locaisAtendimento: true,
        },
      });

      return reply.status(201).send(cliente);
    }
  );

  // Atualizar cliente
  fastify.put<{
    Params: { uuid: string };
    Body: UpdateClienteInput;
  }>("/clientes/:uuid", { preHandler: [authorize("admin", "master")] }, async (request, reply) => {
    const { uuid } = request.params;

    // Validar e fazer parse dos dados usando o schema
    const parseResult = updateClienteSchema.safeParse(request.body);

    if (!parseResult.success) {
      return reply.status(400).send({
        error: "Dados inválidos",
      });
    }

    const data = parseResult.data;

    // Verificar se cliente existe
    const existingCliente = await prisma.cliente.findUnique({
      where: { uuid },
    });

    if (!existingCliente) {
      return reply.status(404).send({ error: "Cliente não encontrado" });
    }

    // Se está alterando documento, verificar duplicação
    if (data.cnpj && data.cnpj !== existingCliente.cnpj) {
      const duplicateDoc = await prisma.cliente.findUnique({
        where: { cnpj: data.cnpj },
      });

      if (duplicateDoc) {
        return reply.status(400).send({ error: "CNPJ já cadastrado para outro cliente" });
      }
    }

    const cliente = await prisma.cliente.update({
      where: { uuid },
      data,
      include: {
        locaisAtendimento: true,
      },
    });

    return reply.send(cliente);
  });

  // Deletar cliente
  fastify.delete<{ Params: { uuid: string } }>(
    "/clientes/:uuid",
    { preHandler: [authorize("admin", "master")] },
    async (request, reply) => {
      const { uuid } = request.params;

      // Verificar se cliente existe
      const cliente = await prisma.cliente.findUnique({
        where: { uuid },
        include: {
          _count: {
            select: {
              plantoes: true,
            },
          },
        },
      });

      if (!cliente) {
        return reply.status(404).send({ error: "Cliente não encontrado" });
      }

      // Verificar se tem plantões associados
      if (cliente._count.plantoes > 0) {
        return reply.status(400).send({
          error: "Não é possível excluir cliente com plantões associados",
        });
      }

      await prisma.cliente.delete({
        where: { uuid },
      });

      return reply.status(204).send();
    }
  );
}
