import { useQuery, useMutation, useQueryClient } from "@tanstack/react-query";
import { api, type LocalAtendimento, type PaginatedResponse } from "@/lib/api";
import { toast } from "sonner";

interface LocalAtendimentoFilters {
  page?: number;
  limit?: number;
  search?: string;
  ativo?: boolean;
  clienteId?: string;
  clienteUuid?: string;
}

export function useLocaisAtendimento(filters: LocalAtendimentoFilters = {}) {
  return useQuery({
    queryKey: ["locais", filters],
    queryFn: () => api.get<PaginatedResponse<LocalAtendimento>>("/locais-atendimento", filters),
  });
}

export function useCreateLocalAtendimento() {
  const queryClient = useQueryClient();

  return useMutation({
    mutationFn: (data: Partial<LocalAtendimento>) => api.post<LocalAtendimento>("/locais-atendimento", data),
    onSuccess: (_, variables) => {
      queryClient.invalidateQueries({ queryKey: ["locais"] });
      // Invalidar também os dados do cliente para atualizar a lista de locais
      if (variables.clienteId) {
        queryClient.invalidateQueries({ queryKey: ["cliente", variables.clienteId] });
      }
      queryClient.invalidateQueries({ queryKey: ["clientes"] });
      toast.success("Local de atendimento criado com sucesso!");
    },
    onError: (error: Error) => {
      toast.error(error.message || "Erro ao criar local de atendimento");
    },
  });
}

export function useUpdateLocalAtendimento() {
  const queryClient = useQueryClient();

  return useMutation({
    mutationFn: ({ id, ...data }: Partial<LocalAtendimento> & { id: string }) =>
      api.put<LocalAtendimento>(`/locais-atendimento/${id}`, data),
    onSuccess: (_, variables) => {
      queryClient.invalidateQueries({ queryKey: ["locais"] });
      // Invalidar também os dados do cliente para atualizar a lista de locais
      if (variables.clienteId) {
        queryClient.invalidateQueries({ queryKey: ["cliente", variables.clienteId] });
      }
      queryClient.invalidateQueries({ queryKey: ["clientes"] });
      toast.success("Local de atendimento atualizado com sucesso!");
    },
    onError: (error: Error) => {
      toast.error(error.message || "Erro ao atualizar local de atendimento");
    },
  });
}

export function useDeleteLocalAtendimento() {
  const queryClient = useQueryClient();

  return useMutation({
    mutationFn: (id: string) => api.delete(`/locais-atendimento/${id}`),
    onSuccess: () => {
      queryClient.invalidateQueries({ queryKey: ["locais"] });
      toast.success("Local de atendimento excluído com sucesso!");
    },
    onError: (error: Error) => {
      toast.error(error.message || "Erro ao excluir local de atendimento");
    },
  });
}
