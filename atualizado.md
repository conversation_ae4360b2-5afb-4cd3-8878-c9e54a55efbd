Com base nas informações fornecidas, segue uma lista de tarefas e pendências:

## ✅ Tarefas Concluídas

• **Correção do Schema DiaPlantao**: Removida a coluna "trabalhado" da tabela DiaPlantao, pois o controle de presença é feito através da tabela PresencaDiaPlantao (2025-09-06)

## 📋 Tarefas Pendentes

• Configurações e Limites do Sistema:
◦ Limitar Percentual de Antecipação: O percentual de antecipação deve ser limitado a 100%
.
◦ Ajustar Cálculo de Fuso Horário: Corrigir o cálculo do fuso horário (GMT-3)
.
◦ Corrigir Cálculo de Horas: Ajustar a lógica para evitar o cálculo de um número excessivo de horas
.
◦ Definir Prazo de Pagamento por Plantão: O prazo de pagamento (em dias) deve ser definido na criação do plantão, não no cliente, para flexibilidade em mutirões ou contratos específicos
.
• Visão e Funcionalidades do Profissional:
◦ Seleção de Cliente no Perfil: Adaptar o perfil do profissional para que ele possa ver e/ou selecionar em qual cliente está trabalhando, especialmente se atuar em múltiplos clientes
. Isso requer uma mudança no token
.
◦ Calendário/Dashboard de Escalas: Implementar um calendário no dashboard do profissional para visualizar sua escala de trabalho e horários, garantindo que não haja cruzamento de plantões
.
◦ Evitar Plantões Sobrepostos: Implementar uma validação para não permitir que o profissional crie ou seja escalado para dois plantões (ou mais de um serviço/atendimento) no mesmo horário, mesmo que para clientes diferentes, para evitar fraudes
.
◦ Visão Detalhada de Fechamentos e Antecipações:
▪ Permitir que o profissional visualize seus fechamentos e solicite antecipação
.
▪ Adicionar detalhes clicáveis na visualização das antecipações do profissional
.
▪ Incluir um card no dashboard do profissional com o valor total das antecipações e o total a receber, não apenas linhas individuais
.
◦ Resumo Mensal Consolidado:
▪ Ajustar a exibição do resumo do dashboard para ser mais explícita (ex: "Resumo de Setembro 2000") e consolidar pelo mês trabalhado, não pela data de antecipação, mesmo que o trabalho se estenda para outro mês
.
• Fluxo de Antecipação:
◦ Cópia de Funcionalidade de Antecipação: Trazer a funcionalidade de antecipação de outro sistema para este
.
◦ Ajuste da Referência de Fechamento: Na geração da antecipação, em vez de um "período", usar apenas o número de referência do fechamento
.
◦ Fórmula de Cálculo da Taxa:
▪ Implementar a fórmula de cálculo da taxa de antecipação baseada em juros compostos, que deve ser editável no nível do cliente
.
▪ A fórmula é: se dias <= 30, então taxa preenchida é a taxa full; caso contrário, (1 + taxa preenchida)^(quantidade de dias / 30) - 1
.
▪ O sistema deve calcular automaticamente a taxa com base nos dias, cobrando "cada centavo"
.
◦ Remoção de "Data de Pagamento Prevista": O campo "Data de pagamento prevista" deve ser removido da criação da antecipação, ou não ser editável e buscar o dado automaticamente com base na quantidade de dias definida no plantão
.
◦ Percentual de Antecipação: Remover o campo "Percentual de antecipação" da tela de criação do plantão. Ele deve ser definido e editável somente na geração da antecipação, refletindo a política de crédito
.
◦ Taxa de Antecipação Editável: A taxa calculada deve ser editável na geração da antecipação, vindo com um valor padrão, mas permitindo ajustes se necessário
.
◦ Envio Direto para Quará: Após a criação, a solicitação de antecipação deve ir diretamente para a API da Quará, sem a necessidade de aprovação interna no sistema
.
◦ Ativação da API de Teste da Quará: Solicitar à Quará que liguem a versão de teste da API para simular a solicitação de antecipação
.
◦ Design Visual Mínimo: Manter um design visual mínimo para a visão do profissional
.
• Auditoria e Segurança de Dados:
◦ Registro Completo de Ações (Auditoria): Garantir que todas as ações (inserção, alteração, remoção) sejam registradas no sistema. Isso é crucial para auditoria e conformidade com a LGPD
.
◦ Não Permitir Exclusão de Registros: Não deve ser possível excluir registros com relacionamentos no banco de dados. Em vez de excluir, os perfis podem ser "tirados de vista" ou desativados. Em caso de necessidade de remoção real, um processo manual e cuidadoso (geração de relatório e remoção "de trás para frente") deve ser realizado
.
• Portal de Antecipação (Rede Total e Aura - Projeto Separado):
◦ Criação de Portal no Site: Desenvolver um portal de antecipação nos sites da Rede Total e da Aura, semelhante ao da Pivida, mas com um design mais moderno
.
◦ Integração com Proteus: O portal deverá pegar o XML do Proteus após a revisão de glosa
.
◦ Validação Automática de Notas Fiscais: Implementar um filtro automático que confronta o valor da nota fiscal do prestador com o valor da planilha revisada no RP da Proteus. Se os valores coincidirem, a nota é aceita; caso contrário, é recusada por inconsistência
.
◦ Desenho do Fluxo: O fluxo do portal será desenhado em um papel e uma foto será enviada para discussão
.
◦ Antecipação Automática: Este portal permitirá a antecipação automática sem interação humana, direcionando as solicitações diretamente para o fundo, sem passar pela plataforma atual
.
