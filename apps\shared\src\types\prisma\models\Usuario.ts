
/* !!! This is code generated by Prisma. Do not edit directly. !!! */
/* eslint-disable */
// @ts-nocheck 
/*
 * This file exports the `Usuario` model and its related types.
 *
 * 🟢 You can import this file directly.
 */
import * as runtime from "@prisma/client/runtime/library"
import type * as $Enums from "../enums.ts"
import type * as Prisma from "../internal/prismaNamespace.ts"

/**
 * Model Usuario
 * 
 */
export type UsuarioModel = runtime.Types.Result.DefaultSelection<Prisma.$UsuarioPayload>

export type AggregateUsuario = {
  _count: UsuarioCountAggregateOutputType | null
  _avg: UsuarioAvgAggregateOutputType | null
  _sum: UsuarioSumAggregateOutputType | null
  _min: UsuarioMinAggregateOutputType | null
  _max: UsuarioMaxAggregateOutputType | null
}

export type UsuarioAvgAggregateOutputType = {
  id: number | null
}

export type UsuarioSumAggregateOutputType = {
  id: number | null
}

export type UsuarioMinAggregateOutputType = {
  id: number | null
  uuid: string | null
  email: string | null
  senha: string | null
  nome: string | null
  cpf: string | null
  telefone: string | null
  dataNascimento: Date | null
  genero: string | null
  estadoCivil: string | null
  nacionalidade: string | null
  cep: string | null
  logradouro: string | null
  numero: string | null
  complemento: string | null
  bairro: string | null
  cidade: string | null
  uf: string | null
  ativo: boolean | null
  emailVerificado: boolean | null
  emailVerificadoEm: Date | null
  deveResetarSenha: boolean | null
  tokenRecuperacao: string | null
  tokenRecuperacaoExpira: Date | null
  ultimoAcesso: Date | null
  createdAt: Date | null
  updatedAt: Date | null
  deletedAt: Date | null
}

export type UsuarioMaxAggregateOutputType = {
  id: number | null
  uuid: string | null
  email: string | null
  senha: string | null
  nome: string | null
  cpf: string | null
  telefone: string | null
  dataNascimento: Date | null
  genero: string | null
  estadoCivil: string | null
  nacionalidade: string | null
  cep: string | null
  logradouro: string | null
  numero: string | null
  complemento: string | null
  bairro: string | null
  cidade: string | null
  uf: string | null
  ativo: boolean | null
  emailVerificado: boolean | null
  emailVerificadoEm: Date | null
  deveResetarSenha: boolean | null
  tokenRecuperacao: string | null
  tokenRecuperacaoExpira: Date | null
  ultimoAcesso: Date | null
  createdAt: Date | null
  updatedAt: Date | null
  deletedAt: Date | null
}

export type UsuarioCountAggregateOutputType = {
  id: number
  uuid: number
  email: number
  senha: number
  nome: number
  cpf: number
  telefone: number
  dataNascimento: number
  genero: number
  estadoCivil: number
  nacionalidade: number
  cep: number
  logradouro: number
  numero: number
  complemento: number
  bairro: number
  cidade: number
  uf: number
  ativo: number
  emailVerificado: number
  emailVerificadoEm: number
  deveResetarSenha: number
  tokenRecuperacao: number
  tokenRecuperacaoExpira: number
  ultimoAcesso: number
  metaData: number
  createdAt: number
  updatedAt: number
  deletedAt: number
  _all: number
}


export type UsuarioAvgAggregateInputType = {
  id?: true
}

export type UsuarioSumAggregateInputType = {
  id?: true
}

export type UsuarioMinAggregateInputType = {
  id?: true
  uuid?: true
  email?: true
  senha?: true
  nome?: true
  cpf?: true
  telefone?: true
  dataNascimento?: true
  genero?: true
  estadoCivil?: true
  nacionalidade?: true
  cep?: true
  logradouro?: true
  numero?: true
  complemento?: true
  bairro?: true
  cidade?: true
  uf?: true
  ativo?: true
  emailVerificado?: true
  emailVerificadoEm?: true
  deveResetarSenha?: true
  tokenRecuperacao?: true
  tokenRecuperacaoExpira?: true
  ultimoAcesso?: true
  createdAt?: true
  updatedAt?: true
  deletedAt?: true
}

export type UsuarioMaxAggregateInputType = {
  id?: true
  uuid?: true
  email?: true
  senha?: true
  nome?: true
  cpf?: true
  telefone?: true
  dataNascimento?: true
  genero?: true
  estadoCivil?: true
  nacionalidade?: true
  cep?: true
  logradouro?: true
  numero?: true
  complemento?: true
  bairro?: true
  cidade?: true
  uf?: true
  ativo?: true
  emailVerificado?: true
  emailVerificadoEm?: true
  deveResetarSenha?: true
  tokenRecuperacao?: true
  tokenRecuperacaoExpira?: true
  ultimoAcesso?: true
  createdAt?: true
  updatedAt?: true
  deletedAt?: true
}

export type UsuarioCountAggregateInputType = {
  id?: true
  uuid?: true
  email?: true
  senha?: true
  nome?: true
  cpf?: true
  telefone?: true
  dataNascimento?: true
  genero?: true
  estadoCivil?: true
  nacionalidade?: true
  cep?: true
  logradouro?: true
  numero?: true
  complemento?: true
  bairro?: true
  cidade?: true
  uf?: true
  ativo?: true
  emailVerificado?: true
  emailVerificadoEm?: true
  deveResetarSenha?: true
  tokenRecuperacao?: true
  tokenRecuperacaoExpira?: true
  ultimoAcesso?: true
  metaData?: true
  createdAt?: true
  updatedAt?: true
  deletedAt?: true
  _all?: true
}

export type UsuarioAggregateArgs<ExtArgs extends runtime.Types.Extensions.InternalArgs = runtime.Types.Extensions.DefaultArgs> = {
  /**
   * Filter which Usuario to aggregate.
   */
  where?: Prisma.UsuarioWhereInput
  /**
   * {@link https://www.prisma.io/docs/concepts/components/prisma-client/sorting Sorting Docs}
   * 
   * Determine the order of Usuarios to fetch.
   */
  orderBy?: Prisma.UsuarioOrderByWithRelationInput | Prisma.UsuarioOrderByWithRelationInput[]
  /**
   * {@link https://www.prisma.io/docs/concepts/components/prisma-client/pagination#cursor-based-pagination Cursor Docs}
   * 
   * Sets the start position
   */
  cursor?: Prisma.UsuarioWhereUniqueInput
  /**
   * {@link https://www.prisma.io/docs/concepts/components/prisma-client/pagination Pagination Docs}
   * 
   * Take `±n` Usuarios from the position of the cursor.
   */
  take?: number
  /**
   * {@link https://www.prisma.io/docs/concepts/components/prisma-client/pagination Pagination Docs}
   * 
   * Skip the first `n` Usuarios.
   */
  skip?: number
  /**
   * {@link https://www.prisma.io/docs/concepts/components/prisma-client/aggregations Aggregation Docs}
   * 
   * Count returned Usuarios
  **/
  _count?: true | UsuarioCountAggregateInputType
  /**
   * {@link https://www.prisma.io/docs/concepts/components/prisma-client/aggregations Aggregation Docs}
   * 
   * Select which fields to average
  **/
  _avg?: UsuarioAvgAggregateInputType
  /**
   * {@link https://www.prisma.io/docs/concepts/components/prisma-client/aggregations Aggregation Docs}
   * 
   * Select which fields to sum
  **/
  _sum?: UsuarioSumAggregateInputType
  /**
   * {@link https://www.prisma.io/docs/concepts/components/prisma-client/aggregations Aggregation Docs}
   * 
   * Select which fields to find the minimum value
  **/
  _min?: UsuarioMinAggregateInputType
  /**
   * {@link https://www.prisma.io/docs/concepts/components/prisma-client/aggregations Aggregation Docs}
   * 
   * Select which fields to find the maximum value
  **/
  _max?: UsuarioMaxAggregateInputType
}

export type GetUsuarioAggregateType<T extends UsuarioAggregateArgs> = {
      [P in keyof T & keyof AggregateUsuario]: P extends '_count' | 'count'
    ? T[P] extends true
      ? number
      : Prisma.GetScalarType<T[P], AggregateUsuario[P]>
    : Prisma.GetScalarType<T[P], AggregateUsuario[P]>
}




export type UsuarioGroupByArgs<ExtArgs extends runtime.Types.Extensions.InternalArgs = runtime.Types.Extensions.DefaultArgs> = {
  where?: Prisma.UsuarioWhereInput
  orderBy?: Prisma.UsuarioOrderByWithAggregationInput | Prisma.UsuarioOrderByWithAggregationInput[]
  by: Prisma.UsuarioScalarFieldEnum[] | Prisma.UsuarioScalarFieldEnum
  having?: Prisma.UsuarioScalarWhereWithAggregatesInput
  take?: number
  skip?: number
  _count?: UsuarioCountAggregateInputType | true
  _avg?: UsuarioAvgAggregateInputType
  _sum?: UsuarioSumAggregateInputType
  _min?: UsuarioMinAggregateInputType
  _max?: UsuarioMaxAggregateInputType
}

export type UsuarioGroupByOutputType = {
  id: number
  uuid: string
  email: string
  senha: string
  nome: string
  cpf: string
  telefone: string | null
  dataNascimento: Date | null
  genero: string | null
  estadoCivil: string | null
  nacionalidade: string | null
  cep: string | null
  logradouro: string | null
  numero: string | null
  complemento: string | null
  bairro: string | null
  cidade: string | null
  uf: string | null
  ativo: boolean
  emailVerificado: boolean
  emailVerificadoEm: Date | null
  deveResetarSenha: boolean
  tokenRecuperacao: string | null
  tokenRecuperacaoExpira: Date | null
  ultimoAcesso: Date | null
  metaData: runtime.JsonValue | null
  createdAt: Date
  updatedAt: Date
  deletedAt: Date | null
  _count: UsuarioCountAggregateOutputType | null
  _avg: UsuarioAvgAggregateOutputType | null
  _sum: UsuarioSumAggregateOutputType | null
  _min: UsuarioMinAggregateOutputType | null
  _max: UsuarioMaxAggregateOutputType | null
}

type GetUsuarioGroupByPayload<T extends UsuarioGroupByArgs> = Prisma.PrismaPromise<
  Array<
    Prisma.PickEnumerable<UsuarioGroupByOutputType, T['by']> &
      {
        [P in ((keyof T) & (keyof UsuarioGroupByOutputType))]: P extends '_count'
          ? T[P] extends boolean
            ? number
            : Prisma.GetScalarType<T[P], UsuarioGroupByOutputType[P]>
          : Prisma.GetScalarType<T[P], UsuarioGroupByOutputType[P]>
      }
    >
  >



export type UsuarioWhereInput = {
  AND?: Prisma.UsuarioWhereInput | Prisma.UsuarioWhereInput[]
  OR?: Prisma.UsuarioWhereInput[]
  NOT?: Prisma.UsuarioWhereInput | Prisma.UsuarioWhereInput[]
  id?: Prisma.IntFilter<"Usuario"> | number
  uuid?: Prisma.StringFilter<"Usuario"> | string
  email?: Prisma.StringFilter<"Usuario"> | string
  senha?: Prisma.StringFilter<"Usuario"> | string
  nome?: Prisma.StringFilter<"Usuario"> | string
  cpf?: Prisma.StringFilter<"Usuario"> | string
  telefone?: Prisma.StringNullableFilter<"Usuario"> | string | null
  dataNascimento?: Prisma.DateTimeNullableFilter<"Usuario"> | Date | string | null
  genero?: Prisma.StringNullableFilter<"Usuario"> | string | null
  estadoCivil?: Prisma.StringNullableFilter<"Usuario"> | string | null
  nacionalidade?: Prisma.StringNullableFilter<"Usuario"> | string | null
  cep?: Prisma.StringNullableFilter<"Usuario"> | string | null
  logradouro?: Prisma.StringNullableFilter<"Usuario"> | string | null
  numero?: Prisma.StringNullableFilter<"Usuario"> | string | null
  complemento?: Prisma.StringNullableFilter<"Usuario"> | string | null
  bairro?: Prisma.StringNullableFilter<"Usuario"> | string | null
  cidade?: Prisma.StringNullableFilter<"Usuario"> | string | null
  uf?: Prisma.StringNullableFilter<"Usuario"> | string | null
  ativo?: Prisma.BoolFilter<"Usuario"> | boolean
  emailVerificado?: Prisma.BoolFilter<"Usuario"> | boolean
  emailVerificadoEm?: Prisma.DateTimeNullableFilter<"Usuario"> | Date | string | null
  deveResetarSenha?: Prisma.BoolFilter<"Usuario"> | boolean
  tokenRecuperacao?: Prisma.StringNullableFilter<"Usuario"> | string | null
  tokenRecuperacaoExpira?: Prisma.DateTimeNullableFilter<"Usuario"> | Date | string | null
  ultimoAcesso?: Prisma.DateTimeNullableFilter<"Usuario"> | Date | string | null
  metaData?: Prisma.JsonNullableFilter<"Usuario">
  createdAt?: Prisma.DateTimeFilter<"Usuario"> | Date | string
  updatedAt?: Prisma.DateTimeFilter<"Usuario"> | Date | string
  deletedAt?: Prisma.DateTimeNullableFilter<"Usuario"> | Date | string | null
  profissional?: Prisma.XOR<Prisma.ProfissionalNullableScalarRelationFilter, Prisma.ProfissionalWhereInput> | null
  roles?: Prisma.UsuarioRoleListRelationFilter
  clientes?: Prisma.UsuarioClienteListRelationFilter
  aceiteTermosLgpd?: Prisma.AceiteTermosLgpdListRelationFilter
  logs?: Prisma.AuditLogListRelationFilter
}

export type UsuarioOrderByWithRelationInput = {
  id?: Prisma.SortOrder
  uuid?: Prisma.SortOrder
  email?: Prisma.SortOrder
  senha?: Prisma.SortOrder
  nome?: Prisma.SortOrder
  cpf?: Prisma.SortOrder
  telefone?: Prisma.SortOrderInput | Prisma.SortOrder
  dataNascimento?: Prisma.SortOrderInput | Prisma.SortOrder
  genero?: Prisma.SortOrderInput | Prisma.SortOrder
  estadoCivil?: Prisma.SortOrderInput | Prisma.SortOrder
  nacionalidade?: Prisma.SortOrderInput | Prisma.SortOrder
  cep?: Prisma.SortOrderInput | Prisma.SortOrder
  logradouro?: Prisma.SortOrderInput | Prisma.SortOrder
  numero?: Prisma.SortOrderInput | Prisma.SortOrder
  complemento?: Prisma.SortOrderInput | Prisma.SortOrder
  bairro?: Prisma.SortOrderInput | Prisma.SortOrder
  cidade?: Prisma.SortOrderInput | Prisma.SortOrder
  uf?: Prisma.SortOrderInput | Prisma.SortOrder
  ativo?: Prisma.SortOrder
  emailVerificado?: Prisma.SortOrder
  emailVerificadoEm?: Prisma.SortOrderInput | Prisma.SortOrder
  deveResetarSenha?: Prisma.SortOrder
  tokenRecuperacao?: Prisma.SortOrderInput | Prisma.SortOrder
  tokenRecuperacaoExpira?: Prisma.SortOrderInput | Prisma.SortOrder
  ultimoAcesso?: Prisma.SortOrderInput | Prisma.SortOrder
  metaData?: Prisma.SortOrderInput | Prisma.SortOrder
  createdAt?: Prisma.SortOrder
  updatedAt?: Prisma.SortOrder
  deletedAt?: Prisma.SortOrderInput | Prisma.SortOrder
  profissional?: Prisma.ProfissionalOrderByWithRelationInput
  roles?: Prisma.UsuarioRoleOrderByRelationAggregateInput
  clientes?: Prisma.UsuarioClienteOrderByRelationAggregateInput
  aceiteTermosLgpd?: Prisma.AceiteTermosLgpdOrderByRelationAggregateInput
  logs?: Prisma.AuditLogOrderByRelationAggregateInput
  _relevance?: Prisma.UsuarioOrderByRelevanceInput
}

export type UsuarioWhereUniqueInput = Prisma.AtLeast<{
  id?: number
  uuid?: string
  email?: string
  cpf?: string
  AND?: Prisma.UsuarioWhereInput | Prisma.UsuarioWhereInput[]
  OR?: Prisma.UsuarioWhereInput[]
  NOT?: Prisma.UsuarioWhereInput | Prisma.UsuarioWhereInput[]
  senha?: Prisma.StringFilter<"Usuario"> | string
  nome?: Prisma.StringFilter<"Usuario"> | string
  telefone?: Prisma.StringNullableFilter<"Usuario"> | string | null
  dataNascimento?: Prisma.DateTimeNullableFilter<"Usuario"> | Date | string | null
  genero?: Prisma.StringNullableFilter<"Usuario"> | string | null
  estadoCivil?: Prisma.StringNullableFilter<"Usuario"> | string | null
  nacionalidade?: Prisma.StringNullableFilter<"Usuario"> | string | null
  cep?: Prisma.StringNullableFilter<"Usuario"> | string | null
  logradouro?: Prisma.StringNullableFilter<"Usuario"> | string | null
  numero?: Prisma.StringNullableFilter<"Usuario"> | string | null
  complemento?: Prisma.StringNullableFilter<"Usuario"> | string | null
  bairro?: Prisma.StringNullableFilter<"Usuario"> | string | null
  cidade?: Prisma.StringNullableFilter<"Usuario"> | string | null
  uf?: Prisma.StringNullableFilter<"Usuario"> | string | null
  ativo?: Prisma.BoolFilter<"Usuario"> | boolean
  emailVerificado?: Prisma.BoolFilter<"Usuario"> | boolean
  emailVerificadoEm?: Prisma.DateTimeNullableFilter<"Usuario"> | Date | string | null
  deveResetarSenha?: Prisma.BoolFilter<"Usuario"> | boolean
  tokenRecuperacao?: Prisma.StringNullableFilter<"Usuario"> | string | null
  tokenRecuperacaoExpira?: Prisma.DateTimeNullableFilter<"Usuario"> | Date | string | null
  ultimoAcesso?: Prisma.DateTimeNullableFilter<"Usuario"> | Date | string | null
  metaData?: Prisma.JsonNullableFilter<"Usuario">
  createdAt?: Prisma.DateTimeFilter<"Usuario"> | Date | string
  updatedAt?: Prisma.DateTimeFilter<"Usuario"> | Date | string
  deletedAt?: Prisma.DateTimeNullableFilter<"Usuario"> | Date | string | null
  profissional?: Prisma.XOR<Prisma.ProfissionalNullableScalarRelationFilter, Prisma.ProfissionalWhereInput> | null
  roles?: Prisma.UsuarioRoleListRelationFilter
  clientes?: Prisma.UsuarioClienteListRelationFilter
  aceiteTermosLgpd?: Prisma.AceiteTermosLgpdListRelationFilter
  logs?: Prisma.AuditLogListRelationFilter
}, "id" | "uuid" | "email" | "cpf">

export type UsuarioOrderByWithAggregationInput = {
  id?: Prisma.SortOrder
  uuid?: Prisma.SortOrder
  email?: Prisma.SortOrder
  senha?: Prisma.SortOrder
  nome?: Prisma.SortOrder
  cpf?: Prisma.SortOrder
  telefone?: Prisma.SortOrderInput | Prisma.SortOrder
  dataNascimento?: Prisma.SortOrderInput | Prisma.SortOrder
  genero?: Prisma.SortOrderInput | Prisma.SortOrder
  estadoCivil?: Prisma.SortOrderInput | Prisma.SortOrder
  nacionalidade?: Prisma.SortOrderInput | Prisma.SortOrder
  cep?: Prisma.SortOrderInput | Prisma.SortOrder
  logradouro?: Prisma.SortOrderInput | Prisma.SortOrder
  numero?: Prisma.SortOrderInput | Prisma.SortOrder
  complemento?: Prisma.SortOrderInput | Prisma.SortOrder
  bairro?: Prisma.SortOrderInput | Prisma.SortOrder
  cidade?: Prisma.SortOrderInput | Prisma.SortOrder
  uf?: Prisma.SortOrderInput | Prisma.SortOrder
  ativo?: Prisma.SortOrder
  emailVerificado?: Prisma.SortOrder
  emailVerificadoEm?: Prisma.SortOrderInput | Prisma.SortOrder
  deveResetarSenha?: Prisma.SortOrder
  tokenRecuperacao?: Prisma.SortOrderInput | Prisma.SortOrder
  tokenRecuperacaoExpira?: Prisma.SortOrderInput | Prisma.SortOrder
  ultimoAcesso?: Prisma.SortOrderInput | Prisma.SortOrder
  metaData?: Prisma.SortOrderInput | Prisma.SortOrder
  createdAt?: Prisma.SortOrder
  updatedAt?: Prisma.SortOrder
  deletedAt?: Prisma.SortOrderInput | Prisma.SortOrder
  _count?: Prisma.UsuarioCountOrderByAggregateInput
  _avg?: Prisma.UsuarioAvgOrderByAggregateInput
  _max?: Prisma.UsuarioMaxOrderByAggregateInput
  _min?: Prisma.UsuarioMinOrderByAggregateInput
  _sum?: Prisma.UsuarioSumOrderByAggregateInput
}

export type UsuarioScalarWhereWithAggregatesInput = {
  AND?: Prisma.UsuarioScalarWhereWithAggregatesInput | Prisma.UsuarioScalarWhereWithAggregatesInput[]
  OR?: Prisma.UsuarioScalarWhereWithAggregatesInput[]
  NOT?: Prisma.UsuarioScalarWhereWithAggregatesInput | Prisma.UsuarioScalarWhereWithAggregatesInput[]
  id?: Prisma.IntWithAggregatesFilter<"Usuario"> | number
  uuid?: Prisma.StringWithAggregatesFilter<"Usuario"> | string
  email?: Prisma.StringWithAggregatesFilter<"Usuario"> | string
  senha?: Prisma.StringWithAggregatesFilter<"Usuario"> | string
  nome?: Prisma.StringWithAggregatesFilter<"Usuario"> | string
  cpf?: Prisma.StringWithAggregatesFilter<"Usuario"> | string
  telefone?: Prisma.StringNullableWithAggregatesFilter<"Usuario"> | string | null
  dataNascimento?: Prisma.DateTimeNullableWithAggregatesFilter<"Usuario"> | Date | string | null
  genero?: Prisma.StringNullableWithAggregatesFilter<"Usuario"> | string | null
  estadoCivil?: Prisma.StringNullableWithAggregatesFilter<"Usuario"> | string | null
  nacionalidade?: Prisma.StringNullableWithAggregatesFilter<"Usuario"> | string | null
  cep?: Prisma.StringNullableWithAggregatesFilter<"Usuario"> | string | null
  logradouro?: Prisma.StringNullableWithAggregatesFilter<"Usuario"> | string | null
  numero?: Prisma.StringNullableWithAggregatesFilter<"Usuario"> | string | null
  complemento?: Prisma.StringNullableWithAggregatesFilter<"Usuario"> | string | null
  bairro?: Prisma.StringNullableWithAggregatesFilter<"Usuario"> | string | null
  cidade?: Prisma.StringNullableWithAggregatesFilter<"Usuario"> | string | null
  uf?: Prisma.StringNullableWithAggregatesFilter<"Usuario"> | string | null
  ativo?: Prisma.BoolWithAggregatesFilter<"Usuario"> | boolean
  emailVerificado?: Prisma.BoolWithAggregatesFilter<"Usuario"> | boolean
  emailVerificadoEm?: Prisma.DateTimeNullableWithAggregatesFilter<"Usuario"> | Date | string | null
  deveResetarSenha?: Prisma.BoolWithAggregatesFilter<"Usuario"> | boolean
  tokenRecuperacao?: Prisma.StringNullableWithAggregatesFilter<"Usuario"> | string | null
  tokenRecuperacaoExpira?: Prisma.DateTimeNullableWithAggregatesFilter<"Usuario"> | Date | string | null
  ultimoAcesso?: Prisma.DateTimeNullableWithAggregatesFilter<"Usuario"> | Date | string | null
  metaData?: Prisma.JsonNullableWithAggregatesFilter<"Usuario">
  createdAt?: Prisma.DateTimeWithAggregatesFilter<"Usuario"> | Date | string
  updatedAt?: Prisma.DateTimeWithAggregatesFilter<"Usuario"> | Date | string
  deletedAt?: Prisma.DateTimeNullableWithAggregatesFilter<"Usuario"> | Date | string | null
}

export type UsuarioCreateInput = {
  uuid?: string
  email: string
  senha: string
  nome: string
  cpf: string
  telefone?: string | null
  dataNascimento?: Date | string | null
  genero?: string | null
  estadoCivil?: string | null
  nacionalidade?: string | null
  cep?: string | null
  logradouro?: string | null
  numero?: string | null
  complemento?: string | null
  bairro?: string | null
  cidade?: string | null
  uf?: string | null
  ativo?: boolean
  emailVerificado?: boolean
  emailVerificadoEm?: Date | string | null
  deveResetarSenha?: boolean
  tokenRecuperacao?: string | null
  tokenRecuperacaoExpira?: Date | string | null
  ultimoAcesso?: Date | string | null
  metaData?: Prisma.NullableJsonNullValueInput | runtime.InputJsonValue
  createdAt?: Date | string
  updatedAt?: Date | string
  deletedAt?: Date | string | null
  profissional?: Prisma.ProfissionalCreateNestedOneWithoutUsuarioInput
  roles?: Prisma.UsuarioRoleCreateNestedManyWithoutUsuarioInput
  clientes?: Prisma.UsuarioClienteCreateNestedManyWithoutUsuarioInput
  aceiteTermosLgpd?: Prisma.AceiteTermosLgpdCreateNestedManyWithoutUsuarioInput
  logs?: Prisma.AuditLogCreateNestedManyWithoutUserInput
}

export type UsuarioUncheckedCreateInput = {
  id?: number
  uuid?: string
  email: string
  senha: string
  nome: string
  cpf: string
  telefone?: string | null
  dataNascimento?: Date | string | null
  genero?: string | null
  estadoCivil?: string | null
  nacionalidade?: string | null
  cep?: string | null
  logradouro?: string | null
  numero?: string | null
  complemento?: string | null
  bairro?: string | null
  cidade?: string | null
  uf?: string | null
  ativo?: boolean
  emailVerificado?: boolean
  emailVerificadoEm?: Date | string | null
  deveResetarSenha?: boolean
  tokenRecuperacao?: string | null
  tokenRecuperacaoExpira?: Date | string | null
  ultimoAcesso?: Date | string | null
  metaData?: Prisma.NullableJsonNullValueInput | runtime.InputJsonValue
  createdAt?: Date | string
  updatedAt?: Date | string
  deletedAt?: Date | string | null
  profissional?: Prisma.ProfissionalUncheckedCreateNestedOneWithoutUsuarioInput
  roles?: Prisma.UsuarioRoleUncheckedCreateNestedManyWithoutUsuarioInput
  clientes?: Prisma.UsuarioClienteUncheckedCreateNestedManyWithoutUsuarioInput
  aceiteTermosLgpd?: Prisma.AceiteTermosLgpdUncheckedCreateNestedManyWithoutUsuarioInput
  logs?: Prisma.AuditLogUncheckedCreateNestedManyWithoutUserInput
}

export type UsuarioUpdateInput = {
  uuid?: Prisma.StringFieldUpdateOperationsInput | string
  email?: Prisma.StringFieldUpdateOperationsInput | string
  senha?: Prisma.StringFieldUpdateOperationsInput | string
  nome?: Prisma.StringFieldUpdateOperationsInput | string
  cpf?: Prisma.StringFieldUpdateOperationsInput | string
  telefone?: Prisma.NullableStringFieldUpdateOperationsInput | string | null
  dataNascimento?: Prisma.NullableDateTimeFieldUpdateOperationsInput | Date | string | null
  genero?: Prisma.NullableStringFieldUpdateOperationsInput | string | null
  estadoCivil?: Prisma.NullableStringFieldUpdateOperationsInput | string | null
  nacionalidade?: Prisma.NullableStringFieldUpdateOperationsInput | string | null
  cep?: Prisma.NullableStringFieldUpdateOperationsInput | string | null
  logradouro?: Prisma.NullableStringFieldUpdateOperationsInput | string | null
  numero?: Prisma.NullableStringFieldUpdateOperationsInput | string | null
  complemento?: Prisma.NullableStringFieldUpdateOperationsInput | string | null
  bairro?: Prisma.NullableStringFieldUpdateOperationsInput | string | null
  cidade?: Prisma.NullableStringFieldUpdateOperationsInput | string | null
  uf?: Prisma.NullableStringFieldUpdateOperationsInput | string | null
  ativo?: Prisma.BoolFieldUpdateOperationsInput | boolean
  emailVerificado?: Prisma.BoolFieldUpdateOperationsInput | boolean
  emailVerificadoEm?: Prisma.NullableDateTimeFieldUpdateOperationsInput | Date | string | null
  deveResetarSenha?: Prisma.BoolFieldUpdateOperationsInput | boolean
  tokenRecuperacao?: Prisma.NullableStringFieldUpdateOperationsInput | string | null
  tokenRecuperacaoExpira?: Prisma.NullableDateTimeFieldUpdateOperationsInput | Date | string | null
  ultimoAcesso?: Prisma.NullableDateTimeFieldUpdateOperationsInput | Date | string | null
  metaData?: Prisma.NullableJsonNullValueInput | runtime.InputJsonValue
  createdAt?: Prisma.DateTimeFieldUpdateOperationsInput | Date | string
  updatedAt?: Prisma.DateTimeFieldUpdateOperationsInput | Date | string
  deletedAt?: Prisma.NullableDateTimeFieldUpdateOperationsInput | Date | string | null
  profissional?: Prisma.ProfissionalUpdateOneWithoutUsuarioNestedInput
  roles?: Prisma.UsuarioRoleUpdateManyWithoutUsuarioNestedInput
  clientes?: Prisma.UsuarioClienteUpdateManyWithoutUsuarioNestedInput
  aceiteTermosLgpd?: Prisma.AceiteTermosLgpdUpdateManyWithoutUsuarioNestedInput
  logs?: Prisma.AuditLogUpdateManyWithoutUserNestedInput
}

export type UsuarioUncheckedUpdateInput = {
  id?: Prisma.IntFieldUpdateOperationsInput | number
  uuid?: Prisma.StringFieldUpdateOperationsInput | string
  email?: Prisma.StringFieldUpdateOperationsInput | string
  senha?: Prisma.StringFieldUpdateOperationsInput | string
  nome?: Prisma.StringFieldUpdateOperationsInput | string
  cpf?: Prisma.StringFieldUpdateOperationsInput | string
  telefone?: Prisma.NullableStringFieldUpdateOperationsInput | string | null
  dataNascimento?: Prisma.NullableDateTimeFieldUpdateOperationsInput | Date | string | null
  genero?: Prisma.NullableStringFieldUpdateOperationsInput | string | null
  estadoCivil?: Prisma.NullableStringFieldUpdateOperationsInput | string | null
  nacionalidade?: Prisma.NullableStringFieldUpdateOperationsInput | string | null
  cep?: Prisma.NullableStringFieldUpdateOperationsInput | string | null
  logradouro?: Prisma.NullableStringFieldUpdateOperationsInput | string | null
  numero?: Prisma.NullableStringFieldUpdateOperationsInput | string | null
  complemento?: Prisma.NullableStringFieldUpdateOperationsInput | string | null
  bairro?: Prisma.NullableStringFieldUpdateOperationsInput | string | null
  cidade?: Prisma.NullableStringFieldUpdateOperationsInput | string | null
  uf?: Prisma.NullableStringFieldUpdateOperationsInput | string | null
  ativo?: Prisma.BoolFieldUpdateOperationsInput | boolean
  emailVerificado?: Prisma.BoolFieldUpdateOperationsInput | boolean
  emailVerificadoEm?: Prisma.NullableDateTimeFieldUpdateOperationsInput | Date | string | null
  deveResetarSenha?: Prisma.BoolFieldUpdateOperationsInput | boolean
  tokenRecuperacao?: Prisma.NullableStringFieldUpdateOperationsInput | string | null
  tokenRecuperacaoExpira?: Prisma.NullableDateTimeFieldUpdateOperationsInput | Date | string | null
  ultimoAcesso?: Prisma.NullableDateTimeFieldUpdateOperationsInput | Date | string | null
  metaData?: Prisma.NullableJsonNullValueInput | runtime.InputJsonValue
  createdAt?: Prisma.DateTimeFieldUpdateOperationsInput | Date | string
  updatedAt?: Prisma.DateTimeFieldUpdateOperationsInput | Date | string
  deletedAt?: Prisma.NullableDateTimeFieldUpdateOperationsInput | Date | string | null
  profissional?: Prisma.ProfissionalUncheckedUpdateOneWithoutUsuarioNestedInput
  roles?: Prisma.UsuarioRoleUncheckedUpdateManyWithoutUsuarioNestedInput
  clientes?: Prisma.UsuarioClienteUncheckedUpdateManyWithoutUsuarioNestedInput
  aceiteTermosLgpd?: Prisma.AceiteTermosLgpdUncheckedUpdateManyWithoutUsuarioNestedInput
  logs?: Prisma.AuditLogUncheckedUpdateManyWithoutUserNestedInput
}

export type UsuarioCreateManyInput = {
  id?: number
  uuid?: string
  email: string
  senha: string
  nome: string
  cpf: string
  telefone?: string | null
  dataNascimento?: Date | string | null
  genero?: string | null
  estadoCivil?: string | null
  nacionalidade?: string | null
  cep?: string | null
  logradouro?: string | null
  numero?: string | null
  complemento?: string | null
  bairro?: string | null
  cidade?: string | null
  uf?: string | null
  ativo?: boolean
  emailVerificado?: boolean
  emailVerificadoEm?: Date | string | null
  deveResetarSenha?: boolean
  tokenRecuperacao?: string | null
  tokenRecuperacaoExpira?: Date | string | null
  ultimoAcesso?: Date | string | null
  metaData?: Prisma.NullableJsonNullValueInput | runtime.InputJsonValue
  createdAt?: Date | string
  updatedAt?: Date | string
  deletedAt?: Date | string | null
}

export type UsuarioUpdateManyMutationInput = {
  uuid?: Prisma.StringFieldUpdateOperationsInput | string
  email?: Prisma.StringFieldUpdateOperationsInput | string
  senha?: Prisma.StringFieldUpdateOperationsInput | string
  nome?: Prisma.StringFieldUpdateOperationsInput | string
  cpf?: Prisma.StringFieldUpdateOperationsInput | string
  telefone?: Prisma.NullableStringFieldUpdateOperationsInput | string | null
  dataNascimento?: Prisma.NullableDateTimeFieldUpdateOperationsInput | Date | string | null
  genero?: Prisma.NullableStringFieldUpdateOperationsInput | string | null
  estadoCivil?: Prisma.NullableStringFieldUpdateOperationsInput | string | null
  nacionalidade?: Prisma.NullableStringFieldUpdateOperationsInput | string | null
  cep?: Prisma.NullableStringFieldUpdateOperationsInput | string | null
  logradouro?: Prisma.NullableStringFieldUpdateOperationsInput | string | null
  numero?: Prisma.NullableStringFieldUpdateOperationsInput | string | null
  complemento?: Prisma.NullableStringFieldUpdateOperationsInput | string | null
  bairro?: Prisma.NullableStringFieldUpdateOperationsInput | string | null
  cidade?: Prisma.NullableStringFieldUpdateOperationsInput | string | null
  uf?: Prisma.NullableStringFieldUpdateOperationsInput | string | null
  ativo?: Prisma.BoolFieldUpdateOperationsInput | boolean
  emailVerificado?: Prisma.BoolFieldUpdateOperationsInput | boolean
  emailVerificadoEm?: Prisma.NullableDateTimeFieldUpdateOperationsInput | Date | string | null
  deveResetarSenha?: Prisma.BoolFieldUpdateOperationsInput | boolean
  tokenRecuperacao?: Prisma.NullableStringFieldUpdateOperationsInput | string | null
  tokenRecuperacaoExpira?: Prisma.NullableDateTimeFieldUpdateOperationsInput | Date | string | null
  ultimoAcesso?: Prisma.NullableDateTimeFieldUpdateOperationsInput | Date | string | null
  metaData?: Prisma.NullableJsonNullValueInput | runtime.InputJsonValue
  createdAt?: Prisma.DateTimeFieldUpdateOperationsInput | Date | string
  updatedAt?: Prisma.DateTimeFieldUpdateOperationsInput | Date | string
  deletedAt?: Prisma.NullableDateTimeFieldUpdateOperationsInput | Date | string | null
}

export type UsuarioUncheckedUpdateManyInput = {
  id?: Prisma.IntFieldUpdateOperationsInput | number
  uuid?: Prisma.StringFieldUpdateOperationsInput | string
  email?: Prisma.StringFieldUpdateOperationsInput | string
  senha?: Prisma.StringFieldUpdateOperationsInput | string
  nome?: Prisma.StringFieldUpdateOperationsInput | string
  cpf?: Prisma.StringFieldUpdateOperationsInput | string
  telefone?: Prisma.NullableStringFieldUpdateOperationsInput | string | null
  dataNascimento?: Prisma.NullableDateTimeFieldUpdateOperationsInput | Date | string | null
  genero?: Prisma.NullableStringFieldUpdateOperationsInput | string | null
  estadoCivil?: Prisma.NullableStringFieldUpdateOperationsInput | string | null
  nacionalidade?: Prisma.NullableStringFieldUpdateOperationsInput | string | null
  cep?: Prisma.NullableStringFieldUpdateOperationsInput | string | null
  logradouro?: Prisma.NullableStringFieldUpdateOperationsInput | string | null
  numero?: Prisma.NullableStringFieldUpdateOperationsInput | string | null
  complemento?: Prisma.NullableStringFieldUpdateOperationsInput | string | null
  bairro?: Prisma.NullableStringFieldUpdateOperationsInput | string | null
  cidade?: Prisma.NullableStringFieldUpdateOperationsInput | string | null
  uf?: Prisma.NullableStringFieldUpdateOperationsInput | string | null
  ativo?: Prisma.BoolFieldUpdateOperationsInput | boolean
  emailVerificado?: Prisma.BoolFieldUpdateOperationsInput | boolean
  emailVerificadoEm?: Prisma.NullableDateTimeFieldUpdateOperationsInput | Date | string | null
  deveResetarSenha?: Prisma.BoolFieldUpdateOperationsInput | boolean
  tokenRecuperacao?: Prisma.NullableStringFieldUpdateOperationsInput | string | null
  tokenRecuperacaoExpira?: Prisma.NullableDateTimeFieldUpdateOperationsInput | Date | string | null
  ultimoAcesso?: Prisma.NullableDateTimeFieldUpdateOperationsInput | Date | string | null
  metaData?: Prisma.NullableJsonNullValueInput | runtime.InputJsonValue
  createdAt?: Prisma.DateTimeFieldUpdateOperationsInput | Date | string
  updatedAt?: Prisma.DateTimeFieldUpdateOperationsInput | Date | string
  deletedAt?: Prisma.NullableDateTimeFieldUpdateOperationsInput | Date | string | null
}

export type UsuarioOrderByRelevanceInput = {
  fields: Prisma.UsuarioOrderByRelevanceFieldEnum | Prisma.UsuarioOrderByRelevanceFieldEnum[]
  sort: Prisma.SortOrder
  search: string
}

export type UsuarioCountOrderByAggregateInput = {
  id?: Prisma.SortOrder
  uuid?: Prisma.SortOrder
  email?: Prisma.SortOrder
  senha?: Prisma.SortOrder
  nome?: Prisma.SortOrder
  cpf?: Prisma.SortOrder
  telefone?: Prisma.SortOrder
  dataNascimento?: Prisma.SortOrder
  genero?: Prisma.SortOrder
  estadoCivil?: Prisma.SortOrder
  nacionalidade?: Prisma.SortOrder
  cep?: Prisma.SortOrder
  logradouro?: Prisma.SortOrder
  numero?: Prisma.SortOrder
  complemento?: Prisma.SortOrder
  bairro?: Prisma.SortOrder
  cidade?: Prisma.SortOrder
  uf?: Prisma.SortOrder
  ativo?: Prisma.SortOrder
  emailVerificado?: Prisma.SortOrder
  emailVerificadoEm?: Prisma.SortOrder
  deveResetarSenha?: Prisma.SortOrder
  tokenRecuperacao?: Prisma.SortOrder
  tokenRecuperacaoExpira?: Prisma.SortOrder
  ultimoAcesso?: Prisma.SortOrder
  metaData?: Prisma.SortOrder
  createdAt?: Prisma.SortOrder
  updatedAt?: Prisma.SortOrder
  deletedAt?: Prisma.SortOrder
}

export type UsuarioAvgOrderByAggregateInput = {
  id?: Prisma.SortOrder
}

export type UsuarioMaxOrderByAggregateInput = {
  id?: Prisma.SortOrder
  uuid?: Prisma.SortOrder
  email?: Prisma.SortOrder
  senha?: Prisma.SortOrder
  nome?: Prisma.SortOrder
  cpf?: Prisma.SortOrder
  telefone?: Prisma.SortOrder
  dataNascimento?: Prisma.SortOrder
  genero?: Prisma.SortOrder
  estadoCivil?: Prisma.SortOrder
  nacionalidade?: Prisma.SortOrder
  cep?: Prisma.SortOrder
  logradouro?: Prisma.SortOrder
  numero?: Prisma.SortOrder
  complemento?: Prisma.SortOrder
  bairro?: Prisma.SortOrder
  cidade?: Prisma.SortOrder
  uf?: Prisma.SortOrder
  ativo?: Prisma.SortOrder
  emailVerificado?: Prisma.SortOrder
  emailVerificadoEm?: Prisma.SortOrder
  deveResetarSenha?: Prisma.SortOrder
  tokenRecuperacao?: Prisma.SortOrder
  tokenRecuperacaoExpira?: Prisma.SortOrder
  ultimoAcesso?: Prisma.SortOrder
  createdAt?: Prisma.SortOrder
  updatedAt?: Prisma.SortOrder
  deletedAt?: Prisma.SortOrder
}

export type UsuarioMinOrderByAggregateInput = {
  id?: Prisma.SortOrder
  uuid?: Prisma.SortOrder
  email?: Prisma.SortOrder
  senha?: Prisma.SortOrder
  nome?: Prisma.SortOrder
  cpf?: Prisma.SortOrder
  telefone?: Prisma.SortOrder
  dataNascimento?: Prisma.SortOrder
  genero?: Prisma.SortOrder
  estadoCivil?: Prisma.SortOrder
  nacionalidade?: Prisma.SortOrder
  cep?: Prisma.SortOrder
  logradouro?: Prisma.SortOrder
  numero?: Prisma.SortOrder
  complemento?: Prisma.SortOrder
  bairro?: Prisma.SortOrder
  cidade?: Prisma.SortOrder
  uf?: Prisma.SortOrder
  ativo?: Prisma.SortOrder
  emailVerificado?: Prisma.SortOrder
  emailVerificadoEm?: Prisma.SortOrder
  deveResetarSenha?: Prisma.SortOrder
  tokenRecuperacao?: Prisma.SortOrder
  tokenRecuperacaoExpira?: Prisma.SortOrder
  ultimoAcesso?: Prisma.SortOrder
  createdAt?: Prisma.SortOrder
  updatedAt?: Prisma.SortOrder
  deletedAt?: Prisma.SortOrder
}

export type UsuarioSumOrderByAggregateInput = {
  id?: Prisma.SortOrder
}

export type UsuarioScalarRelationFilter = {
  is?: Prisma.UsuarioWhereInput
  isNot?: Prisma.UsuarioWhereInput
}

export type UsuarioNullableScalarRelationFilter = {
  is?: Prisma.UsuarioWhereInput | null
  isNot?: Prisma.UsuarioWhereInput | null
}

export type StringFieldUpdateOperationsInput = {
  set?: string
}

export type NullableStringFieldUpdateOperationsInput = {
  set?: string | null
}

export type NullableDateTimeFieldUpdateOperationsInput = {
  set?: Date | string | null
}

export type BoolFieldUpdateOperationsInput = {
  set?: boolean
}

export type DateTimeFieldUpdateOperationsInput = {
  set?: Date | string
}

export type IntFieldUpdateOperationsInput = {
  set?: number
  increment?: number
  decrement?: number
  multiply?: number
  divide?: number
}

export type UsuarioCreateNestedOneWithoutRolesInput = {
  create?: Prisma.XOR<Prisma.UsuarioCreateWithoutRolesInput, Prisma.UsuarioUncheckedCreateWithoutRolesInput>
  connectOrCreate?: Prisma.UsuarioCreateOrConnectWithoutRolesInput
  connect?: Prisma.UsuarioWhereUniqueInput
}

export type UsuarioUpdateOneRequiredWithoutRolesNestedInput = {
  create?: Prisma.XOR<Prisma.UsuarioCreateWithoutRolesInput, Prisma.UsuarioUncheckedCreateWithoutRolesInput>
  connectOrCreate?: Prisma.UsuarioCreateOrConnectWithoutRolesInput
  upsert?: Prisma.UsuarioUpsertWithoutRolesInput
  connect?: Prisma.UsuarioWhereUniqueInput
  update?: Prisma.XOR<Prisma.XOR<Prisma.UsuarioUpdateToOneWithWhereWithoutRolesInput, Prisma.UsuarioUpdateWithoutRolesInput>, Prisma.UsuarioUncheckedUpdateWithoutRolesInput>
}

export type UsuarioCreateNestedOneWithoutProfissionalInput = {
  create?: Prisma.XOR<Prisma.UsuarioCreateWithoutProfissionalInput, Prisma.UsuarioUncheckedCreateWithoutProfissionalInput>
  connectOrCreate?: Prisma.UsuarioCreateOrConnectWithoutProfissionalInput
  connect?: Prisma.UsuarioWhereUniqueInput
}

export type UsuarioUpdateOneRequiredWithoutProfissionalNestedInput = {
  create?: Prisma.XOR<Prisma.UsuarioCreateWithoutProfissionalInput, Prisma.UsuarioUncheckedCreateWithoutProfissionalInput>
  connectOrCreate?: Prisma.UsuarioCreateOrConnectWithoutProfissionalInput
  upsert?: Prisma.UsuarioUpsertWithoutProfissionalInput
  connect?: Prisma.UsuarioWhereUniqueInput
  update?: Prisma.XOR<Prisma.XOR<Prisma.UsuarioUpdateToOneWithWhereWithoutProfissionalInput, Prisma.UsuarioUpdateWithoutProfissionalInput>, Prisma.UsuarioUncheckedUpdateWithoutProfissionalInput>
}

export type UsuarioCreateNestedOneWithoutClientesInput = {
  create?: Prisma.XOR<Prisma.UsuarioCreateWithoutClientesInput, Prisma.UsuarioUncheckedCreateWithoutClientesInput>
  connectOrCreate?: Prisma.UsuarioCreateOrConnectWithoutClientesInput
  connect?: Prisma.UsuarioWhereUniqueInput
}

export type UsuarioUpdateOneRequiredWithoutClientesNestedInput = {
  create?: Prisma.XOR<Prisma.UsuarioCreateWithoutClientesInput, Prisma.UsuarioUncheckedCreateWithoutClientesInput>
  connectOrCreate?: Prisma.UsuarioCreateOrConnectWithoutClientesInput
  upsert?: Prisma.UsuarioUpsertWithoutClientesInput
  connect?: Prisma.UsuarioWhereUniqueInput
  update?: Prisma.XOR<Prisma.XOR<Prisma.UsuarioUpdateToOneWithWhereWithoutClientesInput, Prisma.UsuarioUpdateWithoutClientesInput>, Prisma.UsuarioUncheckedUpdateWithoutClientesInput>
}

export type UsuarioCreateNestedOneWithoutLogsInput = {
  create?: Prisma.XOR<Prisma.UsuarioCreateWithoutLogsInput, Prisma.UsuarioUncheckedCreateWithoutLogsInput>
  connectOrCreate?: Prisma.UsuarioCreateOrConnectWithoutLogsInput
  connect?: Prisma.UsuarioWhereUniqueInput
}

export type UsuarioUpdateOneWithoutLogsNestedInput = {
  create?: Prisma.XOR<Prisma.UsuarioCreateWithoutLogsInput, Prisma.UsuarioUncheckedCreateWithoutLogsInput>
  connectOrCreate?: Prisma.UsuarioCreateOrConnectWithoutLogsInput
  upsert?: Prisma.UsuarioUpsertWithoutLogsInput
  disconnect?: Prisma.UsuarioWhereInput | boolean
  delete?: Prisma.UsuarioWhereInput | boolean
  connect?: Prisma.UsuarioWhereUniqueInput
  update?: Prisma.XOR<Prisma.XOR<Prisma.UsuarioUpdateToOneWithWhereWithoutLogsInput, Prisma.UsuarioUpdateWithoutLogsInput>, Prisma.UsuarioUncheckedUpdateWithoutLogsInput>
}

export type UsuarioCreateNestedOneWithoutAceiteTermosLgpdInput = {
  create?: Prisma.XOR<Prisma.UsuarioCreateWithoutAceiteTermosLgpdInput, Prisma.UsuarioUncheckedCreateWithoutAceiteTermosLgpdInput>
  connectOrCreate?: Prisma.UsuarioCreateOrConnectWithoutAceiteTermosLgpdInput
  connect?: Prisma.UsuarioWhereUniqueInput
}

export type UsuarioUpdateOneRequiredWithoutAceiteTermosLgpdNestedInput = {
  create?: Prisma.XOR<Prisma.UsuarioCreateWithoutAceiteTermosLgpdInput, Prisma.UsuarioUncheckedCreateWithoutAceiteTermosLgpdInput>
  connectOrCreate?: Prisma.UsuarioCreateOrConnectWithoutAceiteTermosLgpdInput
  upsert?: Prisma.UsuarioUpsertWithoutAceiteTermosLgpdInput
  connect?: Prisma.UsuarioWhereUniqueInput
  update?: Prisma.XOR<Prisma.XOR<Prisma.UsuarioUpdateToOneWithWhereWithoutAceiteTermosLgpdInput, Prisma.UsuarioUpdateWithoutAceiteTermosLgpdInput>, Prisma.UsuarioUncheckedUpdateWithoutAceiteTermosLgpdInput>
}

export type UsuarioCreateWithoutRolesInput = {
  uuid?: string
  email: string
  senha: string
  nome: string
  cpf: string
  telefone?: string | null
  dataNascimento?: Date | string | null
  genero?: string | null
  estadoCivil?: string | null
  nacionalidade?: string | null
  cep?: string | null
  logradouro?: string | null
  numero?: string | null
  complemento?: string | null
  bairro?: string | null
  cidade?: string | null
  uf?: string | null
  ativo?: boolean
  emailVerificado?: boolean
  emailVerificadoEm?: Date | string | null
  deveResetarSenha?: boolean
  tokenRecuperacao?: string | null
  tokenRecuperacaoExpira?: Date | string | null
  ultimoAcesso?: Date | string | null
  metaData?: Prisma.NullableJsonNullValueInput | runtime.InputJsonValue
  createdAt?: Date | string
  updatedAt?: Date | string
  deletedAt?: Date | string | null
  profissional?: Prisma.ProfissionalCreateNestedOneWithoutUsuarioInput
  clientes?: Prisma.UsuarioClienteCreateNestedManyWithoutUsuarioInput
  aceiteTermosLgpd?: Prisma.AceiteTermosLgpdCreateNestedManyWithoutUsuarioInput
  logs?: Prisma.AuditLogCreateNestedManyWithoutUserInput
}

export type UsuarioUncheckedCreateWithoutRolesInput = {
  id?: number
  uuid?: string
  email: string
  senha: string
  nome: string
  cpf: string
  telefone?: string | null
  dataNascimento?: Date | string | null
  genero?: string | null
  estadoCivil?: string | null
  nacionalidade?: string | null
  cep?: string | null
  logradouro?: string | null
  numero?: string | null
  complemento?: string | null
  bairro?: string | null
  cidade?: string | null
  uf?: string | null
  ativo?: boolean
  emailVerificado?: boolean
  emailVerificadoEm?: Date | string | null
  deveResetarSenha?: boolean
  tokenRecuperacao?: string | null
  tokenRecuperacaoExpira?: Date | string | null
  ultimoAcesso?: Date | string | null
  metaData?: Prisma.NullableJsonNullValueInput | runtime.InputJsonValue
  createdAt?: Date | string
  updatedAt?: Date | string
  deletedAt?: Date | string | null
  profissional?: Prisma.ProfissionalUncheckedCreateNestedOneWithoutUsuarioInput
  clientes?: Prisma.UsuarioClienteUncheckedCreateNestedManyWithoutUsuarioInput
  aceiteTermosLgpd?: Prisma.AceiteTermosLgpdUncheckedCreateNestedManyWithoutUsuarioInput
  logs?: Prisma.AuditLogUncheckedCreateNestedManyWithoutUserInput
}

export type UsuarioCreateOrConnectWithoutRolesInput = {
  where: Prisma.UsuarioWhereUniqueInput
  create: Prisma.XOR<Prisma.UsuarioCreateWithoutRolesInput, Prisma.UsuarioUncheckedCreateWithoutRolesInput>
}

export type UsuarioUpsertWithoutRolesInput = {
  update: Prisma.XOR<Prisma.UsuarioUpdateWithoutRolesInput, Prisma.UsuarioUncheckedUpdateWithoutRolesInput>
  create: Prisma.XOR<Prisma.UsuarioCreateWithoutRolesInput, Prisma.UsuarioUncheckedCreateWithoutRolesInput>
  where?: Prisma.UsuarioWhereInput
}

export type UsuarioUpdateToOneWithWhereWithoutRolesInput = {
  where?: Prisma.UsuarioWhereInput
  data: Prisma.XOR<Prisma.UsuarioUpdateWithoutRolesInput, Prisma.UsuarioUncheckedUpdateWithoutRolesInput>
}

export type UsuarioUpdateWithoutRolesInput = {
  uuid?: Prisma.StringFieldUpdateOperationsInput | string
  email?: Prisma.StringFieldUpdateOperationsInput | string
  senha?: Prisma.StringFieldUpdateOperationsInput | string
  nome?: Prisma.StringFieldUpdateOperationsInput | string
  cpf?: Prisma.StringFieldUpdateOperationsInput | string
  telefone?: Prisma.NullableStringFieldUpdateOperationsInput | string | null
  dataNascimento?: Prisma.NullableDateTimeFieldUpdateOperationsInput | Date | string | null
  genero?: Prisma.NullableStringFieldUpdateOperationsInput | string | null
  estadoCivil?: Prisma.NullableStringFieldUpdateOperationsInput | string | null
  nacionalidade?: Prisma.NullableStringFieldUpdateOperationsInput | string | null
  cep?: Prisma.NullableStringFieldUpdateOperationsInput | string | null
  logradouro?: Prisma.NullableStringFieldUpdateOperationsInput | string | null
  numero?: Prisma.NullableStringFieldUpdateOperationsInput | string | null
  complemento?: Prisma.NullableStringFieldUpdateOperationsInput | string | null
  bairro?: Prisma.NullableStringFieldUpdateOperationsInput | string | null
  cidade?: Prisma.NullableStringFieldUpdateOperationsInput | string | null
  uf?: Prisma.NullableStringFieldUpdateOperationsInput | string | null
  ativo?: Prisma.BoolFieldUpdateOperationsInput | boolean
  emailVerificado?: Prisma.BoolFieldUpdateOperationsInput | boolean
  emailVerificadoEm?: Prisma.NullableDateTimeFieldUpdateOperationsInput | Date | string | null
  deveResetarSenha?: Prisma.BoolFieldUpdateOperationsInput | boolean
  tokenRecuperacao?: Prisma.NullableStringFieldUpdateOperationsInput | string | null
  tokenRecuperacaoExpira?: Prisma.NullableDateTimeFieldUpdateOperationsInput | Date | string | null
  ultimoAcesso?: Prisma.NullableDateTimeFieldUpdateOperationsInput | Date | string | null
  metaData?: Prisma.NullableJsonNullValueInput | runtime.InputJsonValue
  createdAt?: Prisma.DateTimeFieldUpdateOperationsInput | Date | string
  updatedAt?: Prisma.DateTimeFieldUpdateOperationsInput | Date | string
  deletedAt?: Prisma.NullableDateTimeFieldUpdateOperationsInput | Date | string | null
  profissional?: Prisma.ProfissionalUpdateOneWithoutUsuarioNestedInput
  clientes?: Prisma.UsuarioClienteUpdateManyWithoutUsuarioNestedInput
  aceiteTermosLgpd?: Prisma.AceiteTermosLgpdUpdateManyWithoutUsuarioNestedInput
  logs?: Prisma.AuditLogUpdateManyWithoutUserNestedInput
}

export type UsuarioUncheckedUpdateWithoutRolesInput = {
  id?: Prisma.IntFieldUpdateOperationsInput | number
  uuid?: Prisma.StringFieldUpdateOperationsInput | string
  email?: Prisma.StringFieldUpdateOperationsInput | string
  senha?: Prisma.StringFieldUpdateOperationsInput | string
  nome?: Prisma.StringFieldUpdateOperationsInput | string
  cpf?: Prisma.StringFieldUpdateOperationsInput | string
  telefone?: Prisma.NullableStringFieldUpdateOperationsInput | string | null
  dataNascimento?: Prisma.NullableDateTimeFieldUpdateOperationsInput | Date | string | null
  genero?: Prisma.NullableStringFieldUpdateOperationsInput | string | null
  estadoCivil?: Prisma.NullableStringFieldUpdateOperationsInput | string | null
  nacionalidade?: Prisma.NullableStringFieldUpdateOperationsInput | string | null
  cep?: Prisma.NullableStringFieldUpdateOperationsInput | string | null
  logradouro?: Prisma.NullableStringFieldUpdateOperationsInput | string | null
  numero?: Prisma.NullableStringFieldUpdateOperationsInput | string | null
  complemento?: Prisma.NullableStringFieldUpdateOperationsInput | string | null
  bairro?: Prisma.NullableStringFieldUpdateOperationsInput | string | null
  cidade?: Prisma.NullableStringFieldUpdateOperationsInput | string | null
  uf?: Prisma.NullableStringFieldUpdateOperationsInput | string | null
  ativo?: Prisma.BoolFieldUpdateOperationsInput | boolean
  emailVerificado?: Prisma.BoolFieldUpdateOperationsInput | boolean
  emailVerificadoEm?: Prisma.NullableDateTimeFieldUpdateOperationsInput | Date | string | null
  deveResetarSenha?: Prisma.BoolFieldUpdateOperationsInput | boolean
  tokenRecuperacao?: Prisma.NullableStringFieldUpdateOperationsInput | string | null
  tokenRecuperacaoExpira?: Prisma.NullableDateTimeFieldUpdateOperationsInput | Date | string | null
  ultimoAcesso?: Prisma.NullableDateTimeFieldUpdateOperationsInput | Date | string | null
  metaData?: Prisma.NullableJsonNullValueInput | runtime.InputJsonValue
  createdAt?: Prisma.DateTimeFieldUpdateOperationsInput | Date | string
  updatedAt?: Prisma.DateTimeFieldUpdateOperationsInput | Date | string
  deletedAt?: Prisma.NullableDateTimeFieldUpdateOperationsInput | Date | string | null
  profissional?: Prisma.ProfissionalUncheckedUpdateOneWithoutUsuarioNestedInput
  clientes?: Prisma.UsuarioClienteUncheckedUpdateManyWithoutUsuarioNestedInput
  aceiteTermosLgpd?: Prisma.AceiteTermosLgpdUncheckedUpdateManyWithoutUsuarioNestedInput
  logs?: Prisma.AuditLogUncheckedUpdateManyWithoutUserNestedInput
}

export type UsuarioCreateWithoutProfissionalInput = {
  uuid?: string
  email: string
  senha: string
  nome: string
  cpf: string
  telefone?: string | null
  dataNascimento?: Date | string | null
  genero?: string | null
  estadoCivil?: string | null
  nacionalidade?: string | null
  cep?: string | null
  logradouro?: string | null
  numero?: string | null
  complemento?: string | null
  bairro?: string | null
  cidade?: string | null
  uf?: string | null
  ativo?: boolean
  emailVerificado?: boolean
  emailVerificadoEm?: Date | string | null
  deveResetarSenha?: boolean
  tokenRecuperacao?: string | null
  tokenRecuperacaoExpira?: Date | string | null
  ultimoAcesso?: Date | string | null
  metaData?: Prisma.NullableJsonNullValueInput | runtime.InputJsonValue
  createdAt?: Date | string
  updatedAt?: Date | string
  deletedAt?: Date | string | null
  roles?: Prisma.UsuarioRoleCreateNestedManyWithoutUsuarioInput
  clientes?: Prisma.UsuarioClienteCreateNestedManyWithoutUsuarioInput
  aceiteTermosLgpd?: Prisma.AceiteTermosLgpdCreateNestedManyWithoutUsuarioInput
  logs?: Prisma.AuditLogCreateNestedManyWithoutUserInput
}

export type UsuarioUncheckedCreateWithoutProfissionalInput = {
  id?: number
  uuid?: string
  email: string
  senha: string
  nome: string
  cpf: string
  telefone?: string | null
  dataNascimento?: Date | string | null
  genero?: string | null
  estadoCivil?: string | null
  nacionalidade?: string | null
  cep?: string | null
  logradouro?: string | null
  numero?: string | null
  complemento?: string | null
  bairro?: string | null
  cidade?: string | null
  uf?: string | null
  ativo?: boolean
  emailVerificado?: boolean
  emailVerificadoEm?: Date | string | null
  deveResetarSenha?: boolean
  tokenRecuperacao?: string | null
  tokenRecuperacaoExpira?: Date | string | null
  ultimoAcesso?: Date | string | null
  metaData?: Prisma.NullableJsonNullValueInput | runtime.InputJsonValue
  createdAt?: Date | string
  updatedAt?: Date | string
  deletedAt?: Date | string | null
  roles?: Prisma.UsuarioRoleUncheckedCreateNestedManyWithoutUsuarioInput
  clientes?: Prisma.UsuarioClienteUncheckedCreateNestedManyWithoutUsuarioInput
  aceiteTermosLgpd?: Prisma.AceiteTermosLgpdUncheckedCreateNestedManyWithoutUsuarioInput
  logs?: Prisma.AuditLogUncheckedCreateNestedManyWithoutUserInput
}

export type UsuarioCreateOrConnectWithoutProfissionalInput = {
  where: Prisma.UsuarioWhereUniqueInput
  create: Prisma.XOR<Prisma.UsuarioCreateWithoutProfissionalInput, Prisma.UsuarioUncheckedCreateWithoutProfissionalInput>
}

export type UsuarioUpsertWithoutProfissionalInput = {
  update: Prisma.XOR<Prisma.UsuarioUpdateWithoutProfissionalInput, Prisma.UsuarioUncheckedUpdateWithoutProfissionalInput>
  create: Prisma.XOR<Prisma.UsuarioCreateWithoutProfissionalInput, Prisma.UsuarioUncheckedCreateWithoutProfissionalInput>
  where?: Prisma.UsuarioWhereInput
}

export type UsuarioUpdateToOneWithWhereWithoutProfissionalInput = {
  where?: Prisma.UsuarioWhereInput
  data: Prisma.XOR<Prisma.UsuarioUpdateWithoutProfissionalInput, Prisma.UsuarioUncheckedUpdateWithoutProfissionalInput>
}

export type UsuarioUpdateWithoutProfissionalInput = {
  uuid?: Prisma.StringFieldUpdateOperationsInput | string
  email?: Prisma.StringFieldUpdateOperationsInput | string
  senha?: Prisma.StringFieldUpdateOperationsInput | string
  nome?: Prisma.StringFieldUpdateOperationsInput | string
  cpf?: Prisma.StringFieldUpdateOperationsInput | string
  telefone?: Prisma.NullableStringFieldUpdateOperationsInput | string | null
  dataNascimento?: Prisma.NullableDateTimeFieldUpdateOperationsInput | Date | string | null
  genero?: Prisma.NullableStringFieldUpdateOperationsInput | string | null
  estadoCivil?: Prisma.NullableStringFieldUpdateOperationsInput | string | null
  nacionalidade?: Prisma.NullableStringFieldUpdateOperationsInput | string | null
  cep?: Prisma.NullableStringFieldUpdateOperationsInput | string | null
  logradouro?: Prisma.NullableStringFieldUpdateOperationsInput | string | null
  numero?: Prisma.NullableStringFieldUpdateOperationsInput | string | null
  complemento?: Prisma.NullableStringFieldUpdateOperationsInput | string | null
  bairro?: Prisma.NullableStringFieldUpdateOperationsInput | string | null
  cidade?: Prisma.NullableStringFieldUpdateOperationsInput | string | null
  uf?: Prisma.NullableStringFieldUpdateOperationsInput | string | null
  ativo?: Prisma.BoolFieldUpdateOperationsInput | boolean
  emailVerificado?: Prisma.BoolFieldUpdateOperationsInput | boolean
  emailVerificadoEm?: Prisma.NullableDateTimeFieldUpdateOperationsInput | Date | string | null
  deveResetarSenha?: Prisma.BoolFieldUpdateOperationsInput | boolean
  tokenRecuperacao?: Prisma.NullableStringFieldUpdateOperationsInput | string | null
  tokenRecuperacaoExpira?: Prisma.NullableDateTimeFieldUpdateOperationsInput | Date | string | null
  ultimoAcesso?: Prisma.NullableDateTimeFieldUpdateOperationsInput | Date | string | null
  metaData?: Prisma.NullableJsonNullValueInput | runtime.InputJsonValue
  createdAt?: Prisma.DateTimeFieldUpdateOperationsInput | Date | string
  updatedAt?: Prisma.DateTimeFieldUpdateOperationsInput | Date | string
  deletedAt?: Prisma.NullableDateTimeFieldUpdateOperationsInput | Date | string | null
  roles?: Prisma.UsuarioRoleUpdateManyWithoutUsuarioNestedInput
  clientes?: Prisma.UsuarioClienteUpdateManyWithoutUsuarioNestedInput
  aceiteTermosLgpd?: Prisma.AceiteTermosLgpdUpdateManyWithoutUsuarioNestedInput
  logs?: Prisma.AuditLogUpdateManyWithoutUserNestedInput
}

export type UsuarioUncheckedUpdateWithoutProfissionalInput = {
  id?: Prisma.IntFieldUpdateOperationsInput | number
  uuid?: Prisma.StringFieldUpdateOperationsInput | string
  email?: Prisma.StringFieldUpdateOperationsInput | string
  senha?: Prisma.StringFieldUpdateOperationsInput | string
  nome?: Prisma.StringFieldUpdateOperationsInput | string
  cpf?: Prisma.StringFieldUpdateOperationsInput | string
  telefone?: Prisma.NullableStringFieldUpdateOperationsInput | string | null
  dataNascimento?: Prisma.NullableDateTimeFieldUpdateOperationsInput | Date | string | null
  genero?: Prisma.NullableStringFieldUpdateOperationsInput | string | null
  estadoCivil?: Prisma.NullableStringFieldUpdateOperationsInput | string | null
  nacionalidade?: Prisma.NullableStringFieldUpdateOperationsInput | string | null
  cep?: Prisma.NullableStringFieldUpdateOperationsInput | string | null
  logradouro?: Prisma.NullableStringFieldUpdateOperationsInput | string | null
  numero?: Prisma.NullableStringFieldUpdateOperationsInput | string | null
  complemento?: Prisma.NullableStringFieldUpdateOperationsInput | string | null
  bairro?: Prisma.NullableStringFieldUpdateOperationsInput | string | null
  cidade?: Prisma.NullableStringFieldUpdateOperationsInput | string | null
  uf?: Prisma.NullableStringFieldUpdateOperationsInput | string | null
  ativo?: Prisma.BoolFieldUpdateOperationsInput | boolean
  emailVerificado?: Prisma.BoolFieldUpdateOperationsInput | boolean
  emailVerificadoEm?: Prisma.NullableDateTimeFieldUpdateOperationsInput | Date | string | null
  deveResetarSenha?: Prisma.BoolFieldUpdateOperationsInput | boolean
  tokenRecuperacao?: Prisma.NullableStringFieldUpdateOperationsInput | string | null
  tokenRecuperacaoExpira?: Prisma.NullableDateTimeFieldUpdateOperationsInput | Date | string | null
  ultimoAcesso?: Prisma.NullableDateTimeFieldUpdateOperationsInput | Date | string | null
  metaData?: Prisma.NullableJsonNullValueInput | runtime.InputJsonValue
  createdAt?: Prisma.DateTimeFieldUpdateOperationsInput | Date | string
  updatedAt?: Prisma.DateTimeFieldUpdateOperationsInput | Date | string
  deletedAt?: Prisma.NullableDateTimeFieldUpdateOperationsInput | Date | string | null
  roles?: Prisma.UsuarioRoleUncheckedUpdateManyWithoutUsuarioNestedInput
  clientes?: Prisma.UsuarioClienteUncheckedUpdateManyWithoutUsuarioNestedInput
  aceiteTermosLgpd?: Prisma.AceiteTermosLgpdUncheckedUpdateManyWithoutUsuarioNestedInput
  logs?: Prisma.AuditLogUncheckedUpdateManyWithoutUserNestedInput
}

export type UsuarioCreateWithoutClientesInput = {
  uuid?: string
  email: string
  senha: string
  nome: string
  cpf: string
  telefone?: string | null
  dataNascimento?: Date | string | null
  genero?: string | null
  estadoCivil?: string | null
  nacionalidade?: string | null
  cep?: string | null
  logradouro?: string | null
  numero?: string | null
  complemento?: string | null
  bairro?: string | null
  cidade?: string | null
  uf?: string | null
  ativo?: boolean
  emailVerificado?: boolean
  emailVerificadoEm?: Date | string | null
  deveResetarSenha?: boolean
  tokenRecuperacao?: string | null
  tokenRecuperacaoExpira?: Date | string | null
  ultimoAcesso?: Date | string | null
  metaData?: Prisma.NullableJsonNullValueInput | runtime.InputJsonValue
  createdAt?: Date | string
  updatedAt?: Date | string
  deletedAt?: Date | string | null
  profissional?: Prisma.ProfissionalCreateNestedOneWithoutUsuarioInput
  roles?: Prisma.UsuarioRoleCreateNestedManyWithoutUsuarioInput
  aceiteTermosLgpd?: Prisma.AceiteTermosLgpdCreateNestedManyWithoutUsuarioInput
  logs?: Prisma.AuditLogCreateNestedManyWithoutUserInput
}

export type UsuarioUncheckedCreateWithoutClientesInput = {
  id?: number
  uuid?: string
  email: string
  senha: string
  nome: string
  cpf: string
  telefone?: string | null
  dataNascimento?: Date | string | null
  genero?: string | null
  estadoCivil?: string | null
  nacionalidade?: string | null
  cep?: string | null
  logradouro?: string | null
  numero?: string | null
  complemento?: string | null
  bairro?: string | null
  cidade?: string | null
  uf?: string | null
  ativo?: boolean
  emailVerificado?: boolean
  emailVerificadoEm?: Date | string | null
  deveResetarSenha?: boolean
  tokenRecuperacao?: string | null
  tokenRecuperacaoExpira?: Date | string | null
  ultimoAcesso?: Date | string | null
  metaData?: Prisma.NullableJsonNullValueInput | runtime.InputJsonValue
  createdAt?: Date | string
  updatedAt?: Date | string
  deletedAt?: Date | string | null
  profissional?: Prisma.ProfissionalUncheckedCreateNestedOneWithoutUsuarioInput
  roles?: Prisma.UsuarioRoleUncheckedCreateNestedManyWithoutUsuarioInput
  aceiteTermosLgpd?: Prisma.AceiteTermosLgpdUncheckedCreateNestedManyWithoutUsuarioInput
  logs?: Prisma.AuditLogUncheckedCreateNestedManyWithoutUserInput
}

export type UsuarioCreateOrConnectWithoutClientesInput = {
  where: Prisma.UsuarioWhereUniqueInput
  create: Prisma.XOR<Prisma.UsuarioCreateWithoutClientesInput, Prisma.UsuarioUncheckedCreateWithoutClientesInput>
}

export type UsuarioUpsertWithoutClientesInput = {
  update: Prisma.XOR<Prisma.UsuarioUpdateWithoutClientesInput, Prisma.UsuarioUncheckedUpdateWithoutClientesInput>
  create: Prisma.XOR<Prisma.UsuarioCreateWithoutClientesInput, Prisma.UsuarioUncheckedCreateWithoutClientesInput>
  where?: Prisma.UsuarioWhereInput
}

export type UsuarioUpdateToOneWithWhereWithoutClientesInput = {
  where?: Prisma.UsuarioWhereInput
  data: Prisma.XOR<Prisma.UsuarioUpdateWithoutClientesInput, Prisma.UsuarioUncheckedUpdateWithoutClientesInput>
}

export type UsuarioUpdateWithoutClientesInput = {
  uuid?: Prisma.StringFieldUpdateOperationsInput | string
  email?: Prisma.StringFieldUpdateOperationsInput | string
  senha?: Prisma.StringFieldUpdateOperationsInput | string
  nome?: Prisma.StringFieldUpdateOperationsInput | string
  cpf?: Prisma.StringFieldUpdateOperationsInput | string
  telefone?: Prisma.NullableStringFieldUpdateOperationsInput | string | null
  dataNascimento?: Prisma.NullableDateTimeFieldUpdateOperationsInput | Date | string | null
  genero?: Prisma.NullableStringFieldUpdateOperationsInput | string | null
  estadoCivil?: Prisma.NullableStringFieldUpdateOperationsInput | string | null
  nacionalidade?: Prisma.NullableStringFieldUpdateOperationsInput | string | null
  cep?: Prisma.NullableStringFieldUpdateOperationsInput | string | null
  logradouro?: Prisma.NullableStringFieldUpdateOperationsInput | string | null
  numero?: Prisma.NullableStringFieldUpdateOperationsInput | string | null
  complemento?: Prisma.NullableStringFieldUpdateOperationsInput | string | null
  bairro?: Prisma.NullableStringFieldUpdateOperationsInput | string | null
  cidade?: Prisma.NullableStringFieldUpdateOperationsInput | string | null
  uf?: Prisma.NullableStringFieldUpdateOperationsInput | string | null
  ativo?: Prisma.BoolFieldUpdateOperationsInput | boolean
  emailVerificado?: Prisma.BoolFieldUpdateOperationsInput | boolean
  emailVerificadoEm?: Prisma.NullableDateTimeFieldUpdateOperationsInput | Date | string | null
  deveResetarSenha?: Prisma.BoolFieldUpdateOperationsInput | boolean
  tokenRecuperacao?: Prisma.NullableStringFieldUpdateOperationsInput | string | null
  tokenRecuperacaoExpira?: Prisma.NullableDateTimeFieldUpdateOperationsInput | Date | string | null
  ultimoAcesso?: Prisma.NullableDateTimeFieldUpdateOperationsInput | Date | string | null
  metaData?: Prisma.NullableJsonNullValueInput | runtime.InputJsonValue
  createdAt?: Prisma.DateTimeFieldUpdateOperationsInput | Date | string
  updatedAt?: Prisma.DateTimeFieldUpdateOperationsInput | Date | string
  deletedAt?: Prisma.NullableDateTimeFieldUpdateOperationsInput | Date | string | null
  profissional?: Prisma.ProfissionalUpdateOneWithoutUsuarioNestedInput
  roles?: Prisma.UsuarioRoleUpdateManyWithoutUsuarioNestedInput
  aceiteTermosLgpd?: Prisma.AceiteTermosLgpdUpdateManyWithoutUsuarioNestedInput
  logs?: Prisma.AuditLogUpdateManyWithoutUserNestedInput
}

export type UsuarioUncheckedUpdateWithoutClientesInput = {
  id?: Prisma.IntFieldUpdateOperationsInput | number
  uuid?: Prisma.StringFieldUpdateOperationsInput | string
  email?: Prisma.StringFieldUpdateOperationsInput | string
  senha?: Prisma.StringFieldUpdateOperationsInput | string
  nome?: Prisma.StringFieldUpdateOperationsInput | string
  cpf?: Prisma.StringFieldUpdateOperationsInput | string
  telefone?: Prisma.NullableStringFieldUpdateOperationsInput | string | null
  dataNascimento?: Prisma.NullableDateTimeFieldUpdateOperationsInput | Date | string | null
  genero?: Prisma.NullableStringFieldUpdateOperationsInput | string | null
  estadoCivil?: Prisma.NullableStringFieldUpdateOperationsInput | string | null
  nacionalidade?: Prisma.NullableStringFieldUpdateOperationsInput | string | null
  cep?: Prisma.NullableStringFieldUpdateOperationsInput | string | null
  logradouro?: Prisma.NullableStringFieldUpdateOperationsInput | string | null
  numero?: Prisma.NullableStringFieldUpdateOperationsInput | string | null
  complemento?: Prisma.NullableStringFieldUpdateOperationsInput | string | null
  bairro?: Prisma.NullableStringFieldUpdateOperationsInput | string | null
  cidade?: Prisma.NullableStringFieldUpdateOperationsInput | string | null
  uf?: Prisma.NullableStringFieldUpdateOperationsInput | string | null
  ativo?: Prisma.BoolFieldUpdateOperationsInput | boolean
  emailVerificado?: Prisma.BoolFieldUpdateOperationsInput | boolean
  emailVerificadoEm?: Prisma.NullableDateTimeFieldUpdateOperationsInput | Date | string | null
  deveResetarSenha?: Prisma.BoolFieldUpdateOperationsInput | boolean
  tokenRecuperacao?: Prisma.NullableStringFieldUpdateOperationsInput | string | null
  tokenRecuperacaoExpira?: Prisma.NullableDateTimeFieldUpdateOperationsInput | Date | string | null
  ultimoAcesso?: Prisma.NullableDateTimeFieldUpdateOperationsInput | Date | string | null
  metaData?: Prisma.NullableJsonNullValueInput | runtime.InputJsonValue
  createdAt?: Prisma.DateTimeFieldUpdateOperationsInput | Date | string
  updatedAt?: Prisma.DateTimeFieldUpdateOperationsInput | Date | string
  deletedAt?: Prisma.NullableDateTimeFieldUpdateOperationsInput | Date | string | null
  profissional?: Prisma.ProfissionalUncheckedUpdateOneWithoutUsuarioNestedInput
  roles?: Prisma.UsuarioRoleUncheckedUpdateManyWithoutUsuarioNestedInput
  aceiteTermosLgpd?: Prisma.AceiteTermosLgpdUncheckedUpdateManyWithoutUsuarioNestedInput
  logs?: Prisma.AuditLogUncheckedUpdateManyWithoutUserNestedInput
}

export type UsuarioCreateWithoutLogsInput = {
  uuid?: string
  email: string
  senha: string
  nome: string
  cpf: string
  telefone?: string | null
  dataNascimento?: Date | string | null
  genero?: string | null
  estadoCivil?: string | null
  nacionalidade?: string | null
  cep?: string | null
  logradouro?: string | null
  numero?: string | null
  complemento?: string | null
  bairro?: string | null
  cidade?: string | null
  uf?: string | null
  ativo?: boolean
  emailVerificado?: boolean
  emailVerificadoEm?: Date | string | null
  deveResetarSenha?: boolean
  tokenRecuperacao?: string | null
  tokenRecuperacaoExpira?: Date | string | null
  ultimoAcesso?: Date | string | null
  metaData?: Prisma.NullableJsonNullValueInput | runtime.InputJsonValue
  createdAt?: Date | string
  updatedAt?: Date | string
  deletedAt?: Date | string | null
  profissional?: Prisma.ProfissionalCreateNestedOneWithoutUsuarioInput
  roles?: Prisma.UsuarioRoleCreateNestedManyWithoutUsuarioInput
  clientes?: Prisma.UsuarioClienteCreateNestedManyWithoutUsuarioInput
  aceiteTermosLgpd?: Prisma.AceiteTermosLgpdCreateNestedManyWithoutUsuarioInput
}

export type UsuarioUncheckedCreateWithoutLogsInput = {
  id?: number
  uuid?: string
  email: string
  senha: string
  nome: string
  cpf: string
  telefone?: string | null
  dataNascimento?: Date | string | null
  genero?: string | null
  estadoCivil?: string | null
  nacionalidade?: string | null
  cep?: string | null
  logradouro?: string | null
  numero?: string | null
  complemento?: string | null
  bairro?: string | null
  cidade?: string | null
  uf?: string | null
  ativo?: boolean
  emailVerificado?: boolean
  emailVerificadoEm?: Date | string | null
  deveResetarSenha?: boolean
  tokenRecuperacao?: string | null
  tokenRecuperacaoExpira?: Date | string | null
  ultimoAcesso?: Date | string | null
  metaData?: Prisma.NullableJsonNullValueInput | runtime.InputJsonValue
  createdAt?: Date | string
  updatedAt?: Date | string
  deletedAt?: Date | string | null
  profissional?: Prisma.ProfissionalUncheckedCreateNestedOneWithoutUsuarioInput
  roles?: Prisma.UsuarioRoleUncheckedCreateNestedManyWithoutUsuarioInput
  clientes?: Prisma.UsuarioClienteUncheckedCreateNestedManyWithoutUsuarioInput
  aceiteTermosLgpd?: Prisma.AceiteTermosLgpdUncheckedCreateNestedManyWithoutUsuarioInput
}

export type UsuarioCreateOrConnectWithoutLogsInput = {
  where: Prisma.UsuarioWhereUniqueInput
  create: Prisma.XOR<Prisma.UsuarioCreateWithoutLogsInput, Prisma.UsuarioUncheckedCreateWithoutLogsInput>
}

export type UsuarioUpsertWithoutLogsInput = {
  update: Prisma.XOR<Prisma.UsuarioUpdateWithoutLogsInput, Prisma.UsuarioUncheckedUpdateWithoutLogsInput>
  create: Prisma.XOR<Prisma.UsuarioCreateWithoutLogsInput, Prisma.UsuarioUncheckedCreateWithoutLogsInput>
  where?: Prisma.UsuarioWhereInput
}

export type UsuarioUpdateToOneWithWhereWithoutLogsInput = {
  where?: Prisma.UsuarioWhereInput
  data: Prisma.XOR<Prisma.UsuarioUpdateWithoutLogsInput, Prisma.UsuarioUncheckedUpdateWithoutLogsInput>
}

export type UsuarioUpdateWithoutLogsInput = {
  uuid?: Prisma.StringFieldUpdateOperationsInput | string
  email?: Prisma.StringFieldUpdateOperationsInput | string
  senha?: Prisma.StringFieldUpdateOperationsInput | string
  nome?: Prisma.StringFieldUpdateOperationsInput | string
  cpf?: Prisma.StringFieldUpdateOperationsInput | string
  telefone?: Prisma.NullableStringFieldUpdateOperationsInput | string | null
  dataNascimento?: Prisma.NullableDateTimeFieldUpdateOperationsInput | Date | string | null
  genero?: Prisma.NullableStringFieldUpdateOperationsInput | string | null
  estadoCivil?: Prisma.NullableStringFieldUpdateOperationsInput | string | null
  nacionalidade?: Prisma.NullableStringFieldUpdateOperationsInput | string | null
  cep?: Prisma.NullableStringFieldUpdateOperationsInput | string | null
  logradouro?: Prisma.NullableStringFieldUpdateOperationsInput | string | null
  numero?: Prisma.NullableStringFieldUpdateOperationsInput | string | null
  complemento?: Prisma.NullableStringFieldUpdateOperationsInput | string | null
  bairro?: Prisma.NullableStringFieldUpdateOperationsInput | string | null
  cidade?: Prisma.NullableStringFieldUpdateOperationsInput | string | null
  uf?: Prisma.NullableStringFieldUpdateOperationsInput | string | null
  ativo?: Prisma.BoolFieldUpdateOperationsInput | boolean
  emailVerificado?: Prisma.BoolFieldUpdateOperationsInput | boolean
  emailVerificadoEm?: Prisma.NullableDateTimeFieldUpdateOperationsInput | Date | string | null
  deveResetarSenha?: Prisma.BoolFieldUpdateOperationsInput | boolean
  tokenRecuperacao?: Prisma.NullableStringFieldUpdateOperationsInput | string | null
  tokenRecuperacaoExpira?: Prisma.NullableDateTimeFieldUpdateOperationsInput | Date | string | null
  ultimoAcesso?: Prisma.NullableDateTimeFieldUpdateOperationsInput | Date | string | null
  metaData?: Prisma.NullableJsonNullValueInput | runtime.InputJsonValue
  createdAt?: Prisma.DateTimeFieldUpdateOperationsInput | Date | string
  updatedAt?: Prisma.DateTimeFieldUpdateOperationsInput | Date | string
  deletedAt?: Prisma.NullableDateTimeFieldUpdateOperationsInput | Date | string | null
  profissional?: Prisma.ProfissionalUpdateOneWithoutUsuarioNestedInput
  roles?: Prisma.UsuarioRoleUpdateManyWithoutUsuarioNestedInput
  clientes?: Prisma.UsuarioClienteUpdateManyWithoutUsuarioNestedInput
  aceiteTermosLgpd?: Prisma.AceiteTermosLgpdUpdateManyWithoutUsuarioNestedInput
}

export type UsuarioUncheckedUpdateWithoutLogsInput = {
  id?: Prisma.IntFieldUpdateOperationsInput | number
  uuid?: Prisma.StringFieldUpdateOperationsInput | string
  email?: Prisma.StringFieldUpdateOperationsInput | string
  senha?: Prisma.StringFieldUpdateOperationsInput | string
  nome?: Prisma.StringFieldUpdateOperationsInput | string
  cpf?: Prisma.StringFieldUpdateOperationsInput | string
  telefone?: Prisma.NullableStringFieldUpdateOperationsInput | string | null
  dataNascimento?: Prisma.NullableDateTimeFieldUpdateOperationsInput | Date | string | null
  genero?: Prisma.NullableStringFieldUpdateOperationsInput | string | null
  estadoCivil?: Prisma.NullableStringFieldUpdateOperationsInput | string | null
  nacionalidade?: Prisma.NullableStringFieldUpdateOperationsInput | string | null
  cep?: Prisma.NullableStringFieldUpdateOperationsInput | string | null
  logradouro?: Prisma.NullableStringFieldUpdateOperationsInput | string | null
  numero?: Prisma.NullableStringFieldUpdateOperationsInput | string | null
  complemento?: Prisma.NullableStringFieldUpdateOperationsInput | string | null
  bairro?: Prisma.NullableStringFieldUpdateOperationsInput | string | null
  cidade?: Prisma.NullableStringFieldUpdateOperationsInput | string | null
  uf?: Prisma.NullableStringFieldUpdateOperationsInput | string | null
  ativo?: Prisma.BoolFieldUpdateOperationsInput | boolean
  emailVerificado?: Prisma.BoolFieldUpdateOperationsInput | boolean
  emailVerificadoEm?: Prisma.NullableDateTimeFieldUpdateOperationsInput | Date | string | null
  deveResetarSenha?: Prisma.BoolFieldUpdateOperationsInput | boolean
  tokenRecuperacao?: Prisma.NullableStringFieldUpdateOperationsInput | string | null
  tokenRecuperacaoExpira?: Prisma.NullableDateTimeFieldUpdateOperationsInput | Date | string | null
  ultimoAcesso?: Prisma.NullableDateTimeFieldUpdateOperationsInput | Date | string | null
  metaData?: Prisma.NullableJsonNullValueInput | runtime.InputJsonValue
  createdAt?: Prisma.DateTimeFieldUpdateOperationsInput | Date | string
  updatedAt?: Prisma.DateTimeFieldUpdateOperationsInput | Date | string
  deletedAt?: Prisma.NullableDateTimeFieldUpdateOperationsInput | Date | string | null
  profissional?: Prisma.ProfissionalUncheckedUpdateOneWithoutUsuarioNestedInput
  roles?: Prisma.UsuarioRoleUncheckedUpdateManyWithoutUsuarioNestedInput
  clientes?: Prisma.UsuarioClienteUncheckedUpdateManyWithoutUsuarioNestedInput
  aceiteTermosLgpd?: Prisma.AceiteTermosLgpdUncheckedUpdateManyWithoutUsuarioNestedInput
}

export type UsuarioCreateWithoutAceiteTermosLgpdInput = {
  uuid?: string
  email: string
  senha: string
  nome: string
  cpf: string
  telefone?: string | null
  dataNascimento?: Date | string | null
  genero?: string | null
  estadoCivil?: string | null
  nacionalidade?: string | null
  cep?: string | null
  logradouro?: string | null
  numero?: string | null
  complemento?: string | null
  bairro?: string | null
  cidade?: string | null
  uf?: string | null
  ativo?: boolean
  emailVerificado?: boolean
  emailVerificadoEm?: Date | string | null
  deveResetarSenha?: boolean
  tokenRecuperacao?: string | null
  tokenRecuperacaoExpira?: Date | string | null
  ultimoAcesso?: Date | string | null
  metaData?: Prisma.NullableJsonNullValueInput | runtime.InputJsonValue
  createdAt?: Date | string
  updatedAt?: Date | string
  deletedAt?: Date | string | null
  profissional?: Prisma.ProfissionalCreateNestedOneWithoutUsuarioInput
  roles?: Prisma.UsuarioRoleCreateNestedManyWithoutUsuarioInput
  clientes?: Prisma.UsuarioClienteCreateNestedManyWithoutUsuarioInput
  logs?: Prisma.AuditLogCreateNestedManyWithoutUserInput
}

export type UsuarioUncheckedCreateWithoutAceiteTermosLgpdInput = {
  id?: number
  uuid?: string
  email: string
  senha: string
  nome: string
  cpf: string
  telefone?: string | null
  dataNascimento?: Date | string | null
  genero?: string | null
  estadoCivil?: string | null
  nacionalidade?: string | null
  cep?: string | null
  logradouro?: string | null
  numero?: string | null
  complemento?: string | null
  bairro?: string | null
  cidade?: string | null
  uf?: string | null
  ativo?: boolean
  emailVerificado?: boolean
  emailVerificadoEm?: Date | string | null
  deveResetarSenha?: boolean
  tokenRecuperacao?: string | null
  tokenRecuperacaoExpira?: Date | string | null
  ultimoAcesso?: Date | string | null
  metaData?: Prisma.NullableJsonNullValueInput | runtime.InputJsonValue
  createdAt?: Date | string
  updatedAt?: Date | string
  deletedAt?: Date | string | null
  profissional?: Prisma.ProfissionalUncheckedCreateNestedOneWithoutUsuarioInput
  roles?: Prisma.UsuarioRoleUncheckedCreateNestedManyWithoutUsuarioInput
  clientes?: Prisma.UsuarioClienteUncheckedCreateNestedManyWithoutUsuarioInput
  logs?: Prisma.AuditLogUncheckedCreateNestedManyWithoutUserInput
}

export type UsuarioCreateOrConnectWithoutAceiteTermosLgpdInput = {
  where: Prisma.UsuarioWhereUniqueInput
  create: Prisma.XOR<Prisma.UsuarioCreateWithoutAceiteTermosLgpdInput, Prisma.UsuarioUncheckedCreateWithoutAceiteTermosLgpdInput>
}

export type UsuarioUpsertWithoutAceiteTermosLgpdInput = {
  update: Prisma.XOR<Prisma.UsuarioUpdateWithoutAceiteTermosLgpdInput, Prisma.UsuarioUncheckedUpdateWithoutAceiteTermosLgpdInput>
  create: Prisma.XOR<Prisma.UsuarioCreateWithoutAceiteTermosLgpdInput, Prisma.UsuarioUncheckedCreateWithoutAceiteTermosLgpdInput>
  where?: Prisma.UsuarioWhereInput
}

export type UsuarioUpdateToOneWithWhereWithoutAceiteTermosLgpdInput = {
  where?: Prisma.UsuarioWhereInput
  data: Prisma.XOR<Prisma.UsuarioUpdateWithoutAceiteTermosLgpdInput, Prisma.UsuarioUncheckedUpdateWithoutAceiteTermosLgpdInput>
}

export type UsuarioUpdateWithoutAceiteTermosLgpdInput = {
  uuid?: Prisma.StringFieldUpdateOperationsInput | string
  email?: Prisma.StringFieldUpdateOperationsInput | string
  senha?: Prisma.StringFieldUpdateOperationsInput | string
  nome?: Prisma.StringFieldUpdateOperationsInput | string
  cpf?: Prisma.StringFieldUpdateOperationsInput | string
  telefone?: Prisma.NullableStringFieldUpdateOperationsInput | string | null
  dataNascimento?: Prisma.NullableDateTimeFieldUpdateOperationsInput | Date | string | null
  genero?: Prisma.NullableStringFieldUpdateOperationsInput | string | null
  estadoCivil?: Prisma.NullableStringFieldUpdateOperationsInput | string | null
  nacionalidade?: Prisma.NullableStringFieldUpdateOperationsInput | string | null
  cep?: Prisma.NullableStringFieldUpdateOperationsInput | string | null
  logradouro?: Prisma.NullableStringFieldUpdateOperationsInput | string | null
  numero?: Prisma.NullableStringFieldUpdateOperationsInput | string | null
  complemento?: Prisma.NullableStringFieldUpdateOperationsInput | string | null
  bairro?: Prisma.NullableStringFieldUpdateOperationsInput | string | null
  cidade?: Prisma.NullableStringFieldUpdateOperationsInput | string | null
  uf?: Prisma.NullableStringFieldUpdateOperationsInput | string | null
  ativo?: Prisma.BoolFieldUpdateOperationsInput | boolean
  emailVerificado?: Prisma.BoolFieldUpdateOperationsInput | boolean
  emailVerificadoEm?: Prisma.NullableDateTimeFieldUpdateOperationsInput | Date | string | null
  deveResetarSenha?: Prisma.BoolFieldUpdateOperationsInput | boolean
  tokenRecuperacao?: Prisma.NullableStringFieldUpdateOperationsInput | string | null
  tokenRecuperacaoExpira?: Prisma.NullableDateTimeFieldUpdateOperationsInput | Date | string | null
  ultimoAcesso?: Prisma.NullableDateTimeFieldUpdateOperationsInput | Date | string | null
  metaData?: Prisma.NullableJsonNullValueInput | runtime.InputJsonValue
  createdAt?: Prisma.DateTimeFieldUpdateOperationsInput | Date | string
  updatedAt?: Prisma.DateTimeFieldUpdateOperationsInput | Date | string
  deletedAt?: Prisma.NullableDateTimeFieldUpdateOperationsInput | Date | string | null
  profissional?: Prisma.ProfissionalUpdateOneWithoutUsuarioNestedInput
  roles?: Prisma.UsuarioRoleUpdateManyWithoutUsuarioNestedInput
  clientes?: Prisma.UsuarioClienteUpdateManyWithoutUsuarioNestedInput
  logs?: Prisma.AuditLogUpdateManyWithoutUserNestedInput
}

export type UsuarioUncheckedUpdateWithoutAceiteTermosLgpdInput = {
  id?: Prisma.IntFieldUpdateOperationsInput | number
  uuid?: Prisma.StringFieldUpdateOperationsInput | string
  email?: Prisma.StringFieldUpdateOperationsInput | string
  senha?: Prisma.StringFieldUpdateOperationsInput | string
  nome?: Prisma.StringFieldUpdateOperationsInput | string
  cpf?: Prisma.StringFieldUpdateOperationsInput | string
  telefone?: Prisma.NullableStringFieldUpdateOperationsInput | string | null
  dataNascimento?: Prisma.NullableDateTimeFieldUpdateOperationsInput | Date | string | null
  genero?: Prisma.NullableStringFieldUpdateOperationsInput | string | null
  estadoCivil?: Prisma.NullableStringFieldUpdateOperationsInput | string | null
  nacionalidade?: Prisma.NullableStringFieldUpdateOperationsInput | string | null
  cep?: Prisma.NullableStringFieldUpdateOperationsInput | string | null
  logradouro?: Prisma.NullableStringFieldUpdateOperationsInput | string | null
  numero?: Prisma.NullableStringFieldUpdateOperationsInput | string | null
  complemento?: Prisma.NullableStringFieldUpdateOperationsInput | string | null
  bairro?: Prisma.NullableStringFieldUpdateOperationsInput | string | null
  cidade?: Prisma.NullableStringFieldUpdateOperationsInput | string | null
  uf?: Prisma.NullableStringFieldUpdateOperationsInput | string | null
  ativo?: Prisma.BoolFieldUpdateOperationsInput | boolean
  emailVerificado?: Prisma.BoolFieldUpdateOperationsInput | boolean
  emailVerificadoEm?: Prisma.NullableDateTimeFieldUpdateOperationsInput | Date | string | null
  deveResetarSenha?: Prisma.BoolFieldUpdateOperationsInput | boolean
  tokenRecuperacao?: Prisma.NullableStringFieldUpdateOperationsInput | string | null
  tokenRecuperacaoExpira?: Prisma.NullableDateTimeFieldUpdateOperationsInput | Date | string | null
  ultimoAcesso?: Prisma.NullableDateTimeFieldUpdateOperationsInput | Date | string | null
  metaData?: Prisma.NullableJsonNullValueInput | runtime.InputJsonValue
  createdAt?: Prisma.DateTimeFieldUpdateOperationsInput | Date | string
  updatedAt?: Prisma.DateTimeFieldUpdateOperationsInput | Date | string
  deletedAt?: Prisma.NullableDateTimeFieldUpdateOperationsInput | Date | string | null
  profissional?: Prisma.ProfissionalUncheckedUpdateOneWithoutUsuarioNestedInput
  roles?: Prisma.UsuarioRoleUncheckedUpdateManyWithoutUsuarioNestedInput
  clientes?: Prisma.UsuarioClienteUncheckedUpdateManyWithoutUsuarioNestedInput
  logs?: Prisma.AuditLogUncheckedUpdateManyWithoutUserNestedInput
}


/**
 * Count Type UsuarioCountOutputType
 */

export type UsuarioCountOutputType = {
  roles: number
  clientes: number
  aceiteTermosLgpd: number
  logs: number
}

export type UsuarioCountOutputTypeSelect<ExtArgs extends runtime.Types.Extensions.InternalArgs = runtime.Types.Extensions.DefaultArgs> = {
  roles?: boolean | UsuarioCountOutputTypeCountRolesArgs
  clientes?: boolean | UsuarioCountOutputTypeCountClientesArgs
  aceiteTermosLgpd?: boolean | UsuarioCountOutputTypeCountAceiteTermosLgpdArgs
  logs?: boolean | UsuarioCountOutputTypeCountLogsArgs
}

/**
 * UsuarioCountOutputType without action
 */
export type UsuarioCountOutputTypeDefaultArgs<ExtArgs extends runtime.Types.Extensions.InternalArgs = runtime.Types.Extensions.DefaultArgs> = {
  /**
   * Select specific fields to fetch from the UsuarioCountOutputType
   */
  select?: Prisma.UsuarioCountOutputTypeSelect<ExtArgs> | null
}

/**
 * UsuarioCountOutputType without action
 */
export type UsuarioCountOutputTypeCountRolesArgs<ExtArgs extends runtime.Types.Extensions.InternalArgs = runtime.Types.Extensions.DefaultArgs> = {
  where?: Prisma.UsuarioRoleWhereInput
}

/**
 * UsuarioCountOutputType without action
 */
export type UsuarioCountOutputTypeCountClientesArgs<ExtArgs extends runtime.Types.Extensions.InternalArgs = runtime.Types.Extensions.DefaultArgs> = {
  where?: Prisma.UsuarioClienteWhereInput
}

/**
 * UsuarioCountOutputType without action
 */
export type UsuarioCountOutputTypeCountAceiteTermosLgpdArgs<ExtArgs extends runtime.Types.Extensions.InternalArgs = runtime.Types.Extensions.DefaultArgs> = {
  where?: Prisma.AceiteTermosLgpdWhereInput
}

/**
 * UsuarioCountOutputType without action
 */
export type UsuarioCountOutputTypeCountLogsArgs<ExtArgs extends runtime.Types.Extensions.InternalArgs = runtime.Types.Extensions.DefaultArgs> = {
  where?: Prisma.AuditLogWhereInput
}


export type UsuarioSelect<ExtArgs extends runtime.Types.Extensions.InternalArgs = runtime.Types.Extensions.DefaultArgs> = runtime.Types.Extensions.GetSelect<{
  id?: boolean
  uuid?: boolean
  email?: boolean
  senha?: boolean
  nome?: boolean
  cpf?: boolean
  telefone?: boolean
  dataNascimento?: boolean
  genero?: boolean
  estadoCivil?: boolean
  nacionalidade?: boolean
  cep?: boolean
  logradouro?: boolean
  numero?: boolean
  complemento?: boolean
  bairro?: boolean
  cidade?: boolean
  uf?: boolean
  ativo?: boolean
  emailVerificado?: boolean
  emailVerificadoEm?: boolean
  deveResetarSenha?: boolean
  tokenRecuperacao?: boolean
  tokenRecuperacaoExpira?: boolean
  ultimoAcesso?: boolean
  metaData?: boolean
  createdAt?: boolean
  updatedAt?: boolean
  deletedAt?: boolean
  profissional?: boolean | Prisma.Usuario$profissionalArgs<ExtArgs>
  roles?: boolean | Prisma.Usuario$rolesArgs<ExtArgs>
  clientes?: boolean | Prisma.Usuario$clientesArgs<ExtArgs>
  aceiteTermosLgpd?: boolean | Prisma.Usuario$aceiteTermosLgpdArgs<ExtArgs>
  logs?: boolean | Prisma.Usuario$logsArgs<ExtArgs>
  _count?: boolean | Prisma.UsuarioCountOutputTypeDefaultArgs<ExtArgs>
}, ExtArgs["result"]["usuario"]>



export type UsuarioSelectScalar = {
  id?: boolean
  uuid?: boolean
  email?: boolean
  senha?: boolean
  nome?: boolean
  cpf?: boolean
  telefone?: boolean
  dataNascimento?: boolean
  genero?: boolean
  estadoCivil?: boolean
  nacionalidade?: boolean
  cep?: boolean
  logradouro?: boolean
  numero?: boolean
  complemento?: boolean
  bairro?: boolean
  cidade?: boolean
  uf?: boolean
  ativo?: boolean
  emailVerificado?: boolean
  emailVerificadoEm?: boolean
  deveResetarSenha?: boolean
  tokenRecuperacao?: boolean
  tokenRecuperacaoExpira?: boolean
  ultimoAcesso?: boolean
  metaData?: boolean
  createdAt?: boolean
  updatedAt?: boolean
  deletedAt?: boolean
}

export type UsuarioOmit<ExtArgs extends runtime.Types.Extensions.InternalArgs = runtime.Types.Extensions.DefaultArgs> = runtime.Types.Extensions.GetOmit<"id" | "uuid" | "email" | "senha" | "nome" | "cpf" | "telefone" | "dataNascimento" | "genero" | "estadoCivil" | "nacionalidade" | "cep" | "logradouro" | "numero" | "complemento" | "bairro" | "cidade" | "uf" | "ativo" | "emailVerificado" | "emailVerificadoEm" | "deveResetarSenha" | "tokenRecuperacao" | "tokenRecuperacaoExpira" | "ultimoAcesso" | "metaData" | "createdAt" | "updatedAt" | "deletedAt", ExtArgs["result"]["usuario"]>
export type UsuarioInclude<ExtArgs extends runtime.Types.Extensions.InternalArgs = runtime.Types.Extensions.DefaultArgs> = {
  profissional?: boolean | Prisma.Usuario$profissionalArgs<ExtArgs>
  roles?: boolean | Prisma.Usuario$rolesArgs<ExtArgs>
  clientes?: boolean | Prisma.Usuario$clientesArgs<ExtArgs>
  aceiteTermosLgpd?: boolean | Prisma.Usuario$aceiteTermosLgpdArgs<ExtArgs>
  logs?: boolean | Prisma.Usuario$logsArgs<ExtArgs>
  _count?: boolean | Prisma.UsuarioCountOutputTypeDefaultArgs<ExtArgs>
}

export type $UsuarioPayload<ExtArgs extends runtime.Types.Extensions.InternalArgs = runtime.Types.Extensions.DefaultArgs> = {
  name: "Usuario"
  objects: {
    profissional: Prisma.$ProfissionalPayload<ExtArgs> | null
    roles: Prisma.$UsuarioRolePayload<ExtArgs>[]
    clientes: Prisma.$UsuarioClientePayload<ExtArgs>[]
    aceiteTermosLgpd: Prisma.$AceiteTermosLgpdPayload<ExtArgs>[]
    logs: Prisma.$AuditLogPayload<ExtArgs>[]
  }
  scalars: runtime.Types.Extensions.GetPayloadResult<{
    id: number
    uuid: string
    email: string
    senha: string
    nome: string
    cpf: string
    telefone: string | null
    dataNascimento: Date | null
    genero: string | null
    estadoCivil: string | null
    nacionalidade: string | null
    cep: string | null
    logradouro: string | null
    numero: string | null
    complemento: string | null
    bairro: string | null
    cidade: string | null
    uf: string | null
    ativo: boolean
    emailVerificado: boolean
    emailVerificadoEm: Date | null
    deveResetarSenha: boolean
    tokenRecuperacao: string | null
    tokenRecuperacaoExpira: Date | null
    ultimoAcesso: Date | null
    metaData: runtime.JsonValue | null
    createdAt: Date
    updatedAt: Date
    deletedAt: Date | null
  }, ExtArgs["result"]["usuario"]>
  composites: {}
}

export type UsuarioGetPayload<S extends boolean | null | undefined | UsuarioDefaultArgs> = runtime.Types.Result.GetResult<Prisma.$UsuarioPayload, S>

export type UsuarioCountArgs<ExtArgs extends runtime.Types.Extensions.InternalArgs = runtime.Types.Extensions.DefaultArgs> =
  Omit<UsuarioFindManyArgs, 'select' | 'include' | 'distinct' | 'omit'> & {
    select?: UsuarioCountAggregateInputType | true
  }

export interface UsuarioDelegate<ExtArgs extends runtime.Types.Extensions.InternalArgs = runtime.Types.Extensions.DefaultArgs, GlobalOmitOptions = {}> {
  [K: symbol]: { types: Prisma.TypeMap<ExtArgs>['model']['Usuario'], meta: { name: 'Usuario' } }
  /**
   * Find zero or one Usuario that matches the filter.
   * @param {UsuarioFindUniqueArgs} args - Arguments to find a Usuario
   * @example
   * // Get one Usuario
   * const usuario = await prisma.usuario.findUnique({
   *   where: {
   *     // ... provide filter here
   *   }
   * })
   */
  findUnique<T extends UsuarioFindUniqueArgs>(args: Prisma.SelectSubset<T, UsuarioFindUniqueArgs<ExtArgs>>): Prisma.Prisma__UsuarioClient<runtime.Types.Result.GetResult<Prisma.$UsuarioPayload<ExtArgs>, T, "findUnique", GlobalOmitOptions> | null, null, ExtArgs, GlobalOmitOptions>

  /**
   * Find one Usuario that matches the filter or throw an error with `error.code='P2025'`
   * if no matches were found.
   * @param {UsuarioFindUniqueOrThrowArgs} args - Arguments to find a Usuario
   * @example
   * // Get one Usuario
   * const usuario = await prisma.usuario.findUniqueOrThrow({
   *   where: {
   *     // ... provide filter here
   *   }
   * })
   */
  findUniqueOrThrow<T extends UsuarioFindUniqueOrThrowArgs>(args: Prisma.SelectSubset<T, UsuarioFindUniqueOrThrowArgs<ExtArgs>>): Prisma.Prisma__UsuarioClient<runtime.Types.Result.GetResult<Prisma.$UsuarioPayload<ExtArgs>, T, "findUniqueOrThrow", GlobalOmitOptions>, never, ExtArgs, GlobalOmitOptions>

  /**
   * Find the first Usuario that matches the filter.
   * Note, that providing `undefined` is treated as the value not being there.
   * Read more here: https://pris.ly/d/null-undefined
   * @param {UsuarioFindFirstArgs} args - Arguments to find a Usuario
   * @example
   * // Get one Usuario
   * const usuario = await prisma.usuario.findFirst({
   *   where: {
   *     // ... provide filter here
   *   }
   * })
   */
  findFirst<T extends UsuarioFindFirstArgs>(args?: Prisma.SelectSubset<T, UsuarioFindFirstArgs<ExtArgs>>): Prisma.Prisma__UsuarioClient<runtime.Types.Result.GetResult<Prisma.$UsuarioPayload<ExtArgs>, T, "findFirst", GlobalOmitOptions> | null, null, ExtArgs, GlobalOmitOptions>

  /**
   * Find the first Usuario that matches the filter or
   * throw `PrismaKnownClientError` with `P2025` code if no matches were found.
   * Note, that providing `undefined` is treated as the value not being there.
   * Read more here: https://pris.ly/d/null-undefined
   * @param {UsuarioFindFirstOrThrowArgs} args - Arguments to find a Usuario
   * @example
   * // Get one Usuario
   * const usuario = await prisma.usuario.findFirstOrThrow({
   *   where: {
   *     // ... provide filter here
   *   }
   * })
   */
  findFirstOrThrow<T extends UsuarioFindFirstOrThrowArgs>(args?: Prisma.SelectSubset<T, UsuarioFindFirstOrThrowArgs<ExtArgs>>): Prisma.Prisma__UsuarioClient<runtime.Types.Result.GetResult<Prisma.$UsuarioPayload<ExtArgs>, T, "findFirstOrThrow", GlobalOmitOptions>, never, ExtArgs, GlobalOmitOptions>

  /**
   * Find zero or more Usuarios that matches the filter.
   * Note, that providing `undefined` is treated as the value not being there.
   * Read more here: https://pris.ly/d/null-undefined
   * @param {UsuarioFindManyArgs} args - Arguments to filter and select certain fields only.
   * @example
   * // Get all Usuarios
   * const usuarios = await prisma.usuario.findMany()
   * 
   * // Get first 10 Usuarios
   * const usuarios = await prisma.usuario.findMany({ take: 10 })
   * 
   * // Only select the `id`
   * const usuarioWithIdOnly = await prisma.usuario.findMany({ select: { id: true } })
   * 
   */
  findMany<T extends UsuarioFindManyArgs>(args?: Prisma.SelectSubset<T, UsuarioFindManyArgs<ExtArgs>>): Prisma.PrismaPromise<runtime.Types.Result.GetResult<Prisma.$UsuarioPayload<ExtArgs>, T, "findMany", GlobalOmitOptions>>

  /**
   * Create a Usuario.
   * @param {UsuarioCreateArgs} args - Arguments to create a Usuario.
   * @example
   * // Create one Usuario
   * const Usuario = await prisma.usuario.create({
   *   data: {
   *     // ... data to create a Usuario
   *   }
   * })
   * 
   */
  create<T extends UsuarioCreateArgs>(args: Prisma.SelectSubset<T, UsuarioCreateArgs<ExtArgs>>): Prisma.Prisma__UsuarioClient<runtime.Types.Result.GetResult<Prisma.$UsuarioPayload<ExtArgs>, T, "create", GlobalOmitOptions>, never, ExtArgs, GlobalOmitOptions>

  /**
   * Create many Usuarios.
   * @param {UsuarioCreateManyArgs} args - Arguments to create many Usuarios.
   * @example
   * // Create many Usuarios
   * const usuario = await prisma.usuario.createMany({
   *   data: [
   *     // ... provide data here
   *   ]
   * })
   *     
   */
  createMany<T extends UsuarioCreateManyArgs>(args?: Prisma.SelectSubset<T, UsuarioCreateManyArgs<ExtArgs>>): Prisma.PrismaPromise<Prisma.BatchPayload>

  /**
   * Delete a Usuario.
   * @param {UsuarioDeleteArgs} args - Arguments to delete one Usuario.
   * @example
   * // Delete one Usuario
   * const Usuario = await prisma.usuario.delete({
   *   where: {
   *     // ... filter to delete one Usuario
   *   }
   * })
   * 
   */
  delete<T extends UsuarioDeleteArgs>(args: Prisma.SelectSubset<T, UsuarioDeleteArgs<ExtArgs>>): Prisma.Prisma__UsuarioClient<runtime.Types.Result.GetResult<Prisma.$UsuarioPayload<ExtArgs>, T, "delete", GlobalOmitOptions>, never, ExtArgs, GlobalOmitOptions>

  /**
   * Update one Usuario.
   * @param {UsuarioUpdateArgs} args - Arguments to update one Usuario.
   * @example
   * // Update one Usuario
   * const usuario = await prisma.usuario.update({
   *   where: {
   *     // ... provide filter here
   *   },
   *   data: {
   *     // ... provide data here
   *   }
   * })
   * 
   */
  update<T extends UsuarioUpdateArgs>(args: Prisma.SelectSubset<T, UsuarioUpdateArgs<ExtArgs>>): Prisma.Prisma__UsuarioClient<runtime.Types.Result.GetResult<Prisma.$UsuarioPayload<ExtArgs>, T, "update", GlobalOmitOptions>, never, ExtArgs, GlobalOmitOptions>

  /**
   * Delete zero or more Usuarios.
   * @param {UsuarioDeleteManyArgs} args - Arguments to filter Usuarios to delete.
   * @example
   * // Delete a few Usuarios
   * const { count } = await prisma.usuario.deleteMany({
   *   where: {
   *     // ... provide filter here
   *   }
   * })
   * 
   */
  deleteMany<T extends UsuarioDeleteManyArgs>(args?: Prisma.SelectSubset<T, UsuarioDeleteManyArgs<ExtArgs>>): Prisma.PrismaPromise<Prisma.BatchPayload>

  /**
   * Update zero or more Usuarios.
   * Note, that providing `undefined` is treated as the value not being there.
   * Read more here: https://pris.ly/d/null-undefined
   * @param {UsuarioUpdateManyArgs} args - Arguments to update one or more rows.
   * @example
   * // Update many Usuarios
   * const usuario = await prisma.usuario.updateMany({
   *   where: {
   *     // ... provide filter here
   *   },
   *   data: {
   *     // ... provide data here
   *   }
   * })
   * 
   */
  updateMany<T extends UsuarioUpdateManyArgs>(args: Prisma.SelectSubset<T, UsuarioUpdateManyArgs<ExtArgs>>): Prisma.PrismaPromise<Prisma.BatchPayload>

  /**
   * Create or update one Usuario.
   * @param {UsuarioUpsertArgs} args - Arguments to update or create a Usuario.
   * @example
   * // Update or create a Usuario
   * const usuario = await prisma.usuario.upsert({
   *   create: {
   *     // ... data to create a Usuario
   *   },
   *   update: {
   *     // ... in case it already exists, update
   *   },
   *   where: {
   *     // ... the filter for the Usuario we want to update
   *   }
   * })
   */
  upsert<T extends UsuarioUpsertArgs>(args: Prisma.SelectSubset<T, UsuarioUpsertArgs<ExtArgs>>): Prisma.Prisma__UsuarioClient<runtime.Types.Result.GetResult<Prisma.$UsuarioPayload<ExtArgs>, T, "upsert", GlobalOmitOptions>, never, ExtArgs, GlobalOmitOptions>


  /**
   * Count the number of Usuarios.
   * Note, that providing `undefined` is treated as the value not being there.
   * Read more here: https://pris.ly/d/null-undefined
   * @param {UsuarioCountArgs} args - Arguments to filter Usuarios to count.
   * @example
   * // Count the number of Usuarios
   * const count = await prisma.usuario.count({
   *   where: {
   *     // ... the filter for the Usuarios we want to count
   *   }
   * })
  **/
  count<T extends UsuarioCountArgs>(
    args?: Prisma.Subset<T, UsuarioCountArgs>,
  ): Prisma.PrismaPromise<
    T extends runtime.Types.Utils.Record<'select', any>
      ? T['select'] extends true
        ? number
        : Prisma.GetScalarType<T['select'], UsuarioCountAggregateOutputType>
      : number
  >

  /**
   * Allows you to perform aggregations operations on a Usuario.
   * Note, that providing `undefined` is treated as the value not being there.
   * Read more here: https://pris.ly/d/null-undefined
   * @param {UsuarioAggregateArgs} args - Select which aggregations you would like to apply and on what fields.
   * @example
   * // Ordered by age ascending
   * // Where email contains prisma.io
   * // Limited to the 10 users
   * const aggregations = await prisma.user.aggregate({
   *   _avg: {
   *     age: true,
   *   },
   *   where: {
   *     email: {
   *       contains: "prisma.io",
   *     },
   *   },
   *   orderBy: {
   *     age: "asc",
   *   },
   *   take: 10,
   * })
  **/
  aggregate<T extends UsuarioAggregateArgs>(args: Prisma.Subset<T, UsuarioAggregateArgs>): Prisma.PrismaPromise<GetUsuarioAggregateType<T>>

  /**
   * Group by Usuario.
   * Note, that providing `undefined` is treated as the value not being there.
   * Read more here: https://pris.ly/d/null-undefined
   * @param {UsuarioGroupByArgs} args - Group by arguments.
   * @example
   * // Group by city, order by createdAt, get count
   * const result = await prisma.user.groupBy({
   *   by: ['city', 'createdAt'],
   *   orderBy: {
   *     createdAt: true
   *   },
   *   _count: {
   *     _all: true
   *   },
   * })
   * 
  **/
  groupBy<
    T extends UsuarioGroupByArgs,
    HasSelectOrTake extends Prisma.Or<
      Prisma.Extends<'skip', Prisma.Keys<T>>,
      Prisma.Extends<'take', Prisma.Keys<T>>
    >,
    OrderByArg extends Prisma.True extends HasSelectOrTake
      ? { orderBy: UsuarioGroupByArgs['orderBy'] }
      : { orderBy?: UsuarioGroupByArgs['orderBy'] },
    OrderFields extends Prisma.ExcludeUnderscoreKeys<Prisma.Keys<Prisma.MaybeTupleToUnion<T['orderBy']>>>,
    ByFields extends Prisma.MaybeTupleToUnion<T['by']>,
    ByValid extends Prisma.Has<ByFields, OrderFields>,
    HavingFields extends Prisma.GetHavingFields<T['having']>,
    HavingValid extends Prisma.Has<ByFields, HavingFields>,
    ByEmpty extends T['by'] extends never[] ? Prisma.True : Prisma.False,
    InputErrors extends ByEmpty extends Prisma.True
    ? `Error: "by" must not be empty.`
    : HavingValid extends Prisma.False
    ? {
        [P in HavingFields]: P extends ByFields
          ? never
          : P extends string
          ? `Error: Field "${P}" used in "having" needs to be provided in "by".`
          : [
              Error,
              'Field ',
              P,
              ` in "having" needs to be provided in "by"`,
            ]
      }[HavingFields]
    : 'take' extends Prisma.Keys<T>
    ? 'orderBy' extends Prisma.Keys<T>
      ? ByValid extends Prisma.True
        ? {}
        : {
            [P in OrderFields]: P extends ByFields
              ? never
              : `Error: Field "${P}" in "orderBy" needs to be provided in "by"`
          }[OrderFields]
      : 'Error: If you provide "take", you also need to provide "orderBy"'
    : 'skip' extends Prisma.Keys<T>
    ? 'orderBy' extends Prisma.Keys<T>
      ? ByValid extends Prisma.True
        ? {}
        : {
            [P in OrderFields]: P extends ByFields
              ? never
              : `Error: Field "${P}" in "orderBy" needs to be provided in "by"`
          }[OrderFields]
      : 'Error: If you provide "skip", you also need to provide "orderBy"'
    : ByValid extends Prisma.True
    ? {}
    : {
        [P in OrderFields]: P extends ByFields
          ? never
          : `Error: Field "${P}" in "orderBy" needs to be provided in "by"`
      }[OrderFields]
  >(args: Prisma.SubsetIntersection<T, UsuarioGroupByArgs, OrderByArg> & InputErrors): {} extends InputErrors ? GetUsuarioGroupByPayload<T> : Prisma.PrismaPromise<InputErrors>
/**
 * Fields of the Usuario model
 */
readonly fields: UsuarioFieldRefs;
}

/**
 * The delegate class that acts as a "Promise-like" for Usuario.
 * Why is this prefixed with `Prisma__`?
 * Because we want to prevent naming conflicts as mentioned in
 * https://github.com/prisma/prisma-client-js/issues/707
 */
export interface Prisma__UsuarioClient<T, Null = never, ExtArgs extends runtime.Types.Extensions.InternalArgs = runtime.Types.Extensions.DefaultArgs, GlobalOmitOptions = {}> extends Prisma.PrismaPromise<T> {
  readonly [Symbol.toStringTag]: "PrismaPromise"
  profissional<T extends Prisma.Usuario$profissionalArgs<ExtArgs> = {}>(args?: Prisma.Subset<T, Prisma.Usuario$profissionalArgs<ExtArgs>>): Prisma.Prisma__ProfissionalClient<runtime.Types.Result.GetResult<Prisma.$ProfissionalPayload<ExtArgs>, T, "findUniqueOrThrow", GlobalOmitOptions> | null, null, ExtArgs, GlobalOmitOptions>
  roles<T extends Prisma.Usuario$rolesArgs<ExtArgs> = {}>(args?: Prisma.Subset<T, Prisma.Usuario$rolesArgs<ExtArgs>>): Prisma.PrismaPromise<runtime.Types.Result.GetResult<Prisma.$UsuarioRolePayload<ExtArgs>, T, "findMany", GlobalOmitOptions> | Null>
  clientes<T extends Prisma.Usuario$clientesArgs<ExtArgs> = {}>(args?: Prisma.Subset<T, Prisma.Usuario$clientesArgs<ExtArgs>>): Prisma.PrismaPromise<runtime.Types.Result.GetResult<Prisma.$UsuarioClientePayload<ExtArgs>, T, "findMany", GlobalOmitOptions> | Null>
  aceiteTermosLgpd<T extends Prisma.Usuario$aceiteTermosLgpdArgs<ExtArgs> = {}>(args?: Prisma.Subset<T, Prisma.Usuario$aceiteTermosLgpdArgs<ExtArgs>>): Prisma.PrismaPromise<runtime.Types.Result.GetResult<Prisma.$AceiteTermosLgpdPayload<ExtArgs>, T, "findMany", GlobalOmitOptions> | Null>
  logs<T extends Prisma.Usuario$logsArgs<ExtArgs> = {}>(args?: Prisma.Subset<T, Prisma.Usuario$logsArgs<ExtArgs>>): Prisma.PrismaPromise<runtime.Types.Result.GetResult<Prisma.$AuditLogPayload<ExtArgs>, T, "findMany", GlobalOmitOptions> | Null>
  /**
   * Attaches callbacks for the resolution and/or rejection of the Promise.
   * @param onfulfilled The callback to execute when the Promise is resolved.
   * @param onrejected The callback to execute when the Promise is rejected.
   * @returns A Promise for the completion of which ever callback is executed.
   */
  then<TResult1 = T, TResult2 = never>(onfulfilled?: ((value: T) => TResult1 | PromiseLike<TResult1>) | undefined | null, onrejected?: ((reason: any) => TResult2 | PromiseLike<TResult2>) | undefined | null): runtime.Types.Utils.JsPromise<TResult1 | TResult2>
  /**
   * Attaches a callback for only the rejection of the Promise.
   * @param onrejected The callback to execute when the Promise is rejected.
   * @returns A Promise for the completion of the callback.
   */
  catch<TResult = never>(onrejected?: ((reason: any) => TResult | PromiseLike<TResult>) | undefined | null): runtime.Types.Utils.JsPromise<T | TResult>
  /**
   * Attaches a callback that is invoked when the Promise is settled (fulfilled or rejected). The
   * resolved value cannot be modified from the callback.
   * @param onfinally The callback to execute when the Promise is settled (fulfilled or rejected).
   * @returns A Promise for the completion of the callback.
   */
  finally(onfinally?: (() => void) | undefined | null): runtime.Types.Utils.JsPromise<T>
}




/**
 * Fields of the Usuario model
 */
export interface UsuarioFieldRefs {
  readonly id: Prisma.FieldRef<"Usuario", 'Int'>
  readonly uuid: Prisma.FieldRef<"Usuario", 'String'>
  readonly email: Prisma.FieldRef<"Usuario", 'String'>
  readonly senha: Prisma.FieldRef<"Usuario", 'String'>
  readonly nome: Prisma.FieldRef<"Usuario", 'String'>
  readonly cpf: Prisma.FieldRef<"Usuario", 'String'>
  readonly telefone: Prisma.FieldRef<"Usuario", 'String'>
  readonly dataNascimento: Prisma.FieldRef<"Usuario", 'DateTime'>
  readonly genero: Prisma.FieldRef<"Usuario", 'String'>
  readonly estadoCivil: Prisma.FieldRef<"Usuario", 'String'>
  readonly nacionalidade: Prisma.FieldRef<"Usuario", 'String'>
  readonly cep: Prisma.FieldRef<"Usuario", 'String'>
  readonly logradouro: Prisma.FieldRef<"Usuario", 'String'>
  readonly numero: Prisma.FieldRef<"Usuario", 'String'>
  readonly complemento: Prisma.FieldRef<"Usuario", 'String'>
  readonly bairro: Prisma.FieldRef<"Usuario", 'String'>
  readonly cidade: Prisma.FieldRef<"Usuario", 'String'>
  readonly uf: Prisma.FieldRef<"Usuario", 'String'>
  readonly ativo: Prisma.FieldRef<"Usuario", 'Boolean'>
  readonly emailVerificado: Prisma.FieldRef<"Usuario", 'Boolean'>
  readonly emailVerificadoEm: Prisma.FieldRef<"Usuario", 'DateTime'>
  readonly deveResetarSenha: Prisma.FieldRef<"Usuario", 'Boolean'>
  readonly tokenRecuperacao: Prisma.FieldRef<"Usuario", 'String'>
  readonly tokenRecuperacaoExpira: Prisma.FieldRef<"Usuario", 'DateTime'>
  readonly ultimoAcesso: Prisma.FieldRef<"Usuario", 'DateTime'>
  readonly metaData: Prisma.FieldRef<"Usuario", 'Json'>
  readonly createdAt: Prisma.FieldRef<"Usuario", 'DateTime'>
  readonly updatedAt: Prisma.FieldRef<"Usuario", 'DateTime'>
  readonly deletedAt: Prisma.FieldRef<"Usuario", 'DateTime'>
}
    

// Custom InputTypes
/**
 * Usuario findUnique
 */
export type UsuarioFindUniqueArgs<ExtArgs extends runtime.Types.Extensions.InternalArgs = runtime.Types.Extensions.DefaultArgs> = {
  /**
   * Select specific fields to fetch from the Usuario
   */
  select?: Prisma.UsuarioSelect<ExtArgs> | null
  /**
   * Omit specific fields from the Usuario
   */
  omit?: Prisma.UsuarioOmit<ExtArgs> | null
  /**
   * Choose, which related nodes to fetch as well
   */
  include?: Prisma.UsuarioInclude<ExtArgs> | null
  /**
   * Filter, which Usuario to fetch.
   */
  where: Prisma.UsuarioWhereUniqueInput
}

/**
 * Usuario findUniqueOrThrow
 */
export type UsuarioFindUniqueOrThrowArgs<ExtArgs extends runtime.Types.Extensions.InternalArgs = runtime.Types.Extensions.DefaultArgs> = {
  /**
   * Select specific fields to fetch from the Usuario
   */
  select?: Prisma.UsuarioSelect<ExtArgs> | null
  /**
   * Omit specific fields from the Usuario
   */
  omit?: Prisma.UsuarioOmit<ExtArgs> | null
  /**
   * Choose, which related nodes to fetch as well
   */
  include?: Prisma.UsuarioInclude<ExtArgs> | null
  /**
   * Filter, which Usuario to fetch.
   */
  where: Prisma.UsuarioWhereUniqueInput
}

/**
 * Usuario findFirst
 */
export type UsuarioFindFirstArgs<ExtArgs extends runtime.Types.Extensions.InternalArgs = runtime.Types.Extensions.DefaultArgs> = {
  /**
   * Select specific fields to fetch from the Usuario
   */
  select?: Prisma.UsuarioSelect<ExtArgs> | null
  /**
   * Omit specific fields from the Usuario
   */
  omit?: Prisma.UsuarioOmit<ExtArgs> | null
  /**
   * Choose, which related nodes to fetch as well
   */
  include?: Prisma.UsuarioInclude<ExtArgs> | null
  /**
   * Filter, which Usuario to fetch.
   */
  where?: Prisma.UsuarioWhereInput
  /**
   * {@link https://www.prisma.io/docs/concepts/components/prisma-client/sorting Sorting Docs}
   * 
   * Determine the order of Usuarios to fetch.
   */
  orderBy?: Prisma.UsuarioOrderByWithRelationInput | Prisma.UsuarioOrderByWithRelationInput[]
  /**
   * {@link https://www.prisma.io/docs/concepts/components/prisma-client/pagination#cursor-based-pagination Cursor Docs}
   * 
   * Sets the position for searching for Usuarios.
   */
  cursor?: Prisma.UsuarioWhereUniqueInput
  /**
   * {@link https://www.prisma.io/docs/concepts/components/prisma-client/pagination Pagination Docs}
   * 
   * Take `±n` Usuarios from the position of the cursor.
   */
  take?: number
  /**
   * {@link https://www.prisma.io/docs/concepts/components/prisma-client/pagination Pagination Docs}
   * 
   * Skip the first `n` Usuarios.
   */
  skip?: number
  /**
   * {@link https://www.prisma.io/docs/concepts/components/prisma-client/distinct Distinct Docs}
   * 
   * Filter by unique combinations of Usuarios.
   */
  distinct?: Prisma.UsuarioScalarFieldEnum | Prisma.UsuarioScalarFieldEnum[]
}

/**
 * Usuario findFirstOrThrow
 */
export type UsuarioFindFirstOrThrowArgs<ExtArgs extends runtime.Types.Extensions.InternalArgs = runtime.Types.Extensions.DefaultArgs> = {
  /**
   * Select specific fields to fetch from the Usuario
   */
  select?: Prisma.UsuarioSelect<ExtArgs> | null
  /**
   * Omit specific fields from the Usuario
   */
  omit?: Prisma.UsuarioOmit<ExtArgs> | null
  /**
   * Choose, which related nodes to fetch as well
   */
  include?: Prisma.UsuarioInclude<ExtArgs> | null
  /**
   * Filter, which Usuario to fetch.
   */
  where?: Prisma.UsuarioWhereInput
  /**
   * {@link https://www.prisma.io/docs/concepts/components/prisma-client/sorting Sorting Docs}
   * 
   * Determine the order of Usuarios to fetch.
   */
  orderBy?: Prisma.UsuarioOrderByWithRelationInput | Prisma.UsuarioOrderByWithRelationInput[]
  /**
   * {@link https://www.prisma.io/docs/concepts/components/prisma-client/pagination#cursor-based-pagination Cursor Docs}
   * 
   * Sets the position for searching for Usuarios.
   */
  cursor?: Prisma.UsuarioWhereUniqueInput
  /**
   * {@link https://www.prisma.io/docs/concepts/components/prisma-client/pagination Pagination Docs}
   * 
   * Take `±n` Usuarios from the position of the cursor.
   */
  take?: number
  /**
   * {@link https://www.prisma.io/docs/concepts/components/prisma-client/pagination Pagination Docs}
   * 
   * Skip the first `n` Usuarios.
   */
  skip?: number
  /**
   * {@link https://www.prisma.io/docs/concepts/components/prisma-client/distinct Distinct Docs}
   * 
   * Filter by unique combinations of Usuarios.
   */
  distinct?: Prisma.UsuarioScalarFieldEnum | Prisma.UsuarioScalarFieldEnum[]
}

/**
 * Usuario findMany
 */
export type UsuarioFindManyArgs<ExtArgs extends runtime.Types.Extensions.InternalArgs = runtime.Types.Extensions.DefaultArgs> = {
  /**
   * Select specific fields to fetch from the Usuario
   */
  select?: Prisma.UsuarioSelect<ExtArgs> | null
  /**
   * Omit specific fields from the Usuario
   */
  omit?: Prisma.UsuarioOmit<ExtArgs> | null
  /**
   * Choose, which related nodes to fetch as well
   */
  include?: Prisma.UsuarioInclude<ExtArgs> | null
  /**
   * Filter, which Usuarios to fetch.
   */
  where?: Prisma.UsuarioWhereInput
  /**
   * {@link https://www.prisma.io/docs/concepts/components/prisma-client/sorting Sorting Docs}
   * 
   * Determine the order of Usuarios to fetch.
   */
  orderBy?: Prisma.UsuarioOrderByWithRelationInput | Prisma.UsuarioOrderByWithRelationInput[]
  /**
   * {@link https://www.prisma.io/docs/concepts/components/prisma-client/pagination#cursor-based-pagination Cursor Docs}
   * 
   * Sets the position for listing Usuarios.
   */
  cursor?: Prisma.UsuarioWhereUniqueInput
  /**
   * {@link https://www.prisma.io/docs/concepts/components/prisma-client/pagination Pagination Docs}
   * 
   * Take `±n` Usuarios from the position of the cursor.
   */
  take?: number
  /**
   * {@link https://www.prisma.io/docs/concepts/components/prisma-client/pagination Pagination Docs}
   * 
   * Skip the first `n` Usuarios.
   */
  skip?: number
  distinct?: Prisma.UsuarioScalarFieldEnum | Prisma.UsuarioScalarFieldEnum[]
}

/**
 * Usuario create
 */
export type UsuarioCreateArgs<ExtArgs extends runtime.Types.Extensions.InternalArgs = runtime.Types.Extensions.DefaultArgs> = {
  /**
   * Select specific fields to fetch from the Usuario
   */
  select?: Prisma.UsuarioSelect<ExtArgs> | null
  /**
   * Omit specific fields from the Usuario
   */
  omit?: Prisma.UsuarioOmit<ExtArgs> | null
  /**
   * Choose, which related nodes to fetch as well
   */
  include?: Prisma.UsuarioInclude<ExtArgs> | null
  /**
   * The data needed to create a Usuario.
   */
  data: Prisma.XOR<Prisma.UsuarioCreateInput, Prisma.UsuarioUncheckedCreateInput>
}

/**
 * Usuario createMany
 */
export type UsuarioCreateManyArgs<ExtArgs extends runtime.Types.Extensions.InternalArgs = runtime.Types.Extensions.DefaultArgs> = {
  /**
   * The data used to create many Usuarios.
   */
  data: Prisma.UsuarioCreateManyInput | Prisma.UsuarioCreateManyInput[]
  skipDuplicates?: boolean
}

/**
 * Usuario update
 */
export type UsuarioUpdateArgs<ExtArgs extends runtime.Types.Extensions.InternalArgs = runtime.Types.Extensions.DefaultArgs> = {
  /**
   * Select specific fields to fetch from the Usuario
   */
  select?: Prisma.UsuarioSelect<ExtArgs> | null
  /**
   * Omit specific fields from the Usuario
   */
  omit?: Prisma.UsuarioOmit<ExtArgs> | null
  /**
   * Choose, which related nodes to fetch as well
   */
  include?: Prisma.UsuarioInclude<ExtArgs> | null
  /**
   * The data needed to update a Usuario.
   */
  data: Prisma.XOR<Prisma.UsuarioUpdateInput, Prisma.UsuarioUncheckedUpdateInput>
  /**
   * Choose, which Usuario to update.
   */
  where: Prisma.UsuarioWhereUniqueInput
}

/**
 * Usuario updateMany
 */
export type UsuarioUpdateManyArgs<ExtArgs extends runtime.Types.Extensions.InternalArgs = runtime.Types.Extensions.DefaultArgs> = {
  /**
   * The data used to update Usuarios.
   */
  data: Prisma.XOR<Prisma.UsuarioUpdateManyMutationInput, Prisma.UsuarioUncheckedUpdateManyInput>
  /**
   * Filter which Usuarios to update
   */
  where?: Prisma.UsuarioWhereInput
  /**
   * Limit how many Usuarios to update.
   */
  limit?: number
}

/**
 * Usuario upsert
 */
export type UsuarioUpsertArgs<ExtArgs extends runtime.Types.Extensions.InternalArgs = runtime.Types.Extensions.DefaultArgs> = {
  /**
   * Select specific fields to fetch from the Usuario
   */
  select?: Prisma.UsuarioSelect<ExtArgs> | null
  /**
   * Omit specific fields from the Usuario
   */
  omit?: Prisma.UsuarioOmit<ExtArgs> | null
  /**
   * Choose, which related nodes to fetch as well
   */
  include?: Prisma.UsuarioInclude<ExtArgs> | null
  /**
   * The filter to search for the Usuario to update in case it exists.
   */
  where: Prisma.UsuarioWhereUniqueInput
  /**
   * In case the Usuario found by the `where` argument doesn't exist, create a new Usuario with this data.
   */
  create: Prisma.XOR<Prisma.UsuarioCreateInput, Prisma.UsuarioUncheckedCreateInput>
  /**
   * In case the Usuario was found with the provided `where` argument, update it with this data.
   */
  update: Prisma.XOR<Prisma.UsuarioUpdateInput, Prisma.UsuarioUncheckedUpdateInput>
}

/**
 * Usuario delete
 */
export type UsuarioDeleteArgs<ExtArgs extends runtime.Types.Extensions.InternalArgs = runtime.Types.Extensions.DefaultArgs> = {
  /**
   * Select specific fields to fetch from the Usuario
   */
  select?: Prisma.UsuarioSelect<ExtArgs> | null
  /**
   * Omit specific fields from the Usuario
   */
  omit?: Prisma.UsuarioOmit<ExtArgs> | null
  /**
   * Choose, which related nodes to fetch as well
   */
  include?: Prisma.UsuarioInclude<ExtArgs> | null
  /**
   * Filter which Usuario to delete.
   */
  where: Prisma.UsuarioWhereUniqueInput
}

/**
 * Usuario deleteMany
 */
export type UsuarioDeleteManyArgs<ExtArgs extends runtime.Types.Extensions.InternalArgs = runtime.Types.Extensions.DefaultArgs> = {
  /**
   * Filter which Usuarios to delete
   */
  where?: Prisma.UsuarioWhereInput
  /**
   * Limit how many Usuarios to delete.
   */
  limit?: number
}

/**
 * Usuario.profissional
 */
export type Usuario$profissionalArgs<ExtArgs extends runtime.Types.Extensions.InternalArgs = runtime.Types.Extensions.DefaultArgs> = {
  /**
   * Select specific fields to fetch from the Profissional
   */
  select?: Prisma.ProfissionalSelect<ExtArgs> | null
  /**
   * Omit specific fields from the Profissional
   */
  omit?: Prisma.ProfissionalOmit<ExtArgs> | null
  /**
   * Choose, which related nodes to fetch as well
   */
  include?: Prisma.ProfissionalInclude<ExtArgs> | null
  where?: Prisma.ProfissionalWhereInput
}

/**
 * Usuario.roles
 */
export type Usuario$rolesArgs<ExtArgs extends runtime.Types.Extensions.InternalArgs = runtime.Types.Extensions.DefaultArgs> = {
  /**
   * Select specific fields to fetch from the UsuarioRole
   */
  select?: Prisma.UsuarioRoleSelect<ExtArgs> | null
  /**
   * Omit specific fields from the UsuarioRole
   */
  omit?: Prisma.UsuarioRoleOmit<ExtArgs> | null
  /**
   * Choose, which related nodes to fetch as well
   */
  include?: Prisma.UsuarioRoleInclude<ExtArgs> | null
  where?: Prisma.UsuarioRoleWhereInput
  orderBy?: Prisma.UsuarioRoleOrderByWithRelationInput | Prisma.UsuarioRoleOrderByWithRelationInput[]
  cursor?: Prisma.UsuarioRoleWhereUniqueInput
  take?: number
  skip?: number
  distinct?: Prisma.UsuarioRoleScalarFieldEnum | Prisma.UsuarioRoleScalarFieldEnum[]
}

/**
 * Usuario.clientes
 */
export type Usuario$clientesArgs<ExtArgs extends runtime.Types.Extensions.InternalArgs = runtime.Types.Extensions.DefaultArgs> = {
  /**
   * Select specific fields to fetch from the UsuarioCliente
   */
  select?: Prisma.UsuarioClienteSelect<ExtArgs> | null
  /**
   * Omit specific fields from the UsuarioCliente
   */
  omit?: Prisma.UsuarioClienteOmit<ExtArgs> | null
  /**
   * Choose, which related nodes to fetch as well
   */
  include?: Prisma.UsuarioClienteInclude<ExtArgs> | null
  where?: Prisma.UsuarioClienteWhereInput
  orderBy?: Prisma.UsuarioClienteOrderByWithRelationInput | Prisma.UsuarioClienteOrderByWithRelationInput[]
  cursor?: Prisma.UsuarioClienteWhereUniqueInput
  take?: number
  skip?: number
  distinct?: Prisma.UsuarioClienteScalarFieldEnum | Prisma.UsuarioClienteScalarFieldEnum[]
}

/**
 * Usuario.aceiteTermosLgpd
 */
export type Usuario$aceiteTermosLgpdArgs<ExtArgs extends runtime.Types.Extensions.InternalArgs = runtime.Types.Extensions.DefaultArgs> = {
  /**
   * Select specific fields to fetch from the AceiteTermosLgpd
   */
  select?: Prisma.AceiteTermosLgpdSelect<ExtArgs> | null
  /**
   * Omit specific fields from the AceiteTermosLgpd
   */
  omit?: Prisma.AceiteTermosLgpdOmit<ExtArgs> | null
  /**
   * Choose, which related nodes to fetch as well
   */
  include?: Prisma.AceiteTermosLgpdInclude<ExtArgs> | null
  where?: Prisma.AceiteTermosLgpdWhereInput
  orderBy?: Prisma.AceiteTermosLgpdOrderByWithRelationInput | Prisma.AceiteTermosLgpdOrderByWithRelationInput[]
  cursor?: Prisma.AceiteTermosLgpdWhereUniqueInput
  take?: number
  skip?: number
  distinct?: Prisma.AceiteTermosLgpdScalarFieldEnum | Prisma.AceiteTermosLgpdScalarFieldEnum[]
}

/**
 * Usuario.logs
 */
export type Usuario$logsArgs<ExtArgs extends runtime.Types.Extensions.InternalArgs = runtime.Types.Extensions.DefaultArgs> = {
  /**
   * Select specific fields to fetch from the AuditLog
   */
  select?: Prisma.AuditLogSelect<ExtArgs> | null
  /**
   * Omit specific fields from the AuditLog
   */
  omit?: Prisma.AuditLogOmit<ExtArgs> | null
  /**
   * Choose, which related nodes to fetch as well
   */
  include?: Prisma.AuditLogInclude<ExtArgs> | null
  where?: Prisma.AuditLogWhereInput
  orderBy?: Prisma.AuditLogOrderByWithRelationInput | Prisma.AuditLogOrderByWithRelationInput[]
  cursor?: Prisma.AuditLogWhereUniqueInput
  take?: number
  skip?: number
  distinct?: Prisma.AuditLogScalarFieldEnum | Prisma.AuditLogScalarFieldEnum[]
}

/**
 * Usuario without action
 */
export type UsuarioDefaultArgs<ExtArgs extends runtime.Types.Extensions.InternalArgs = runtime.Types.Extensions.DefaultArgs> = {
  /**
   * Select specific fields to fetch from the Usuario
   */
  select?: Prisma.UsuarioSelect<ExtArgs> | null
  /**
   * Omit specific fields from the Usuario
   */
  omit?: Prisma.UsuarioOmit<ExtArgs> | null
  /**
   * Choose, which related nodes to fetch as well
   */
  include?: Prisma.UsuarioInclude<ExtArgs> | null
}
