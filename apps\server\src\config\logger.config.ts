import type { FastifyLoggerOptions } from "fastify";
import { env } from "process";

// Configuração de desenvolvimento (mais detalhada e colorida)
const developmentLogger: FastifyLoggerOptions = {
  level: env.LOG_LEVEL || "debug",
  transport: {
    target: "pino-pretty",
    options: {
      translateTime: "HH:MM:ss Z",
      ignore: "pid,hostname",
      colorize: true,
      singleLine: false,
      messageFormat: "{msg}",
      errorLikeObjectKeys: ["err", "error"],
      errorProps: "message,stack",
    },
  },
} as any;

// Configuração de produção (JSON estruturado para análise)
const productionLogger: FastifyLoggerOptions = {
  level: env.LOG_LEVEL || "info",
  serializers: {
    req(request: any) {
      return {
        method: request.method,
        url: request.url,
        headers: {
          host: request.headers.host,
          "user-agent": request.headers["user-agent"],
          "content-type": request.headers["content-type"],
        },
        remoteAddress: request.ip,
        remotePort: request.socket.remotePort,
      };
    },
    res(reply: any) {
      return {
        statusCode: reply.statusCode,
        responseTime: reply.elapsedTime,
      };
    },
  },
};

// Configuração de teste (mínima)
const testLogger: FastifyLoggerOptions = {
  level: "error",
};

export function getLoggerConfig(): FastifyLoggerOptions {
  const environment = env.NODE_ENV || "development";

  switch (environment) {
    case "production":
      return productionLogger;
    case "test":
      return testLogger;
    case "development":
    default:
      return developmentLogger;
  }
}

// Filtros para dados sensíveis
export const redactPaths = [
  "req.headers.authorization",
  "req.headers.cookie",
  "*.password",
  "*.senha",
  "*.token",
  "*.cpf",
  "*.cnpj",
];
