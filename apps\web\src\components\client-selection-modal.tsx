import { useState } from "react";
import { <PERSON><PERSON>, DialogContent, DialogDescription, DialogHeader, DialogTitle } from "@/components/ui/dialog";
import { Button } from "@/components/ui/button";
import { RadioGroup, RadioGroupItem } from "@/components/ui/radio-group";
import { Label } from "@/components/ui/label";
import { Building2, Globe } from "lucide-react";

interface Cliente {
  id: string;
  nome: string;
  fusoHorario?: string;
}

interface ClientSelectionModalProps {
  open: boolean;
  clientes: Cliente[];
  onSelect: (cliente: Cliente) => void;
}

export function ClientSelectionModal({ open, clientes, onSelect }: ClientSelectionModalProps) {
  const [selectedClientId, setSelectedClientId] = useState<string>("");

  const handleConfirm = () => {
    const cliente = clientes.find((c) => c.id === selectedClientId);
    if (cliente) {
      onSelect(cliente);
    }
  };

  // Se houver apenas um cliente, seleciona automaticamente
  if (clientes.length === 1 && open) {
    onSelect(clientes[0]);
    return null;
  }

  return (
    <Dialog open={open} modal>
      <DialogContent
        className="sm:max-w-md"
        onEscapeKeyDown={(e) => e.preventDefault()}
        onPointerDownOutside={(e) => e.preventDefault()}
      >
        <DialogHeader>
          <DialogTitle className="flex items-center gap-2">
            <Building2 className="h-5 w-5" />
            Selecione o Cliente
          </DialogTitle>
          <DialogDescription>
            Você tem acesso a múltiplos clientes. Selecione qual cliente deseja operar nesta sessão.
          </DialogDescription>
        </DialogHeader>

        <div className="py-4">
          <RadioGroup value={selectedClientId} onValueChange={setSelectedClientId} className="space-y-3">
            {clientes.map((cliente) => (
              <div
                key={cliente.id}
                className="flex items-start space-x-3 rounded-lg border p-3 hover:bg-muted/50 transition-colors"
              >
                <RadioGroupItem value={cliente.id} id={cliente.id} />
                <Label htmlFor={cliente.id} className="flex-1 cursor-pointer space-y-1">
                  <div className="font-medium">{cliente.nome}</div>
                </Label>
              </div>
            ))}
          </RadioGroup>
        </div>

        <div className="flex justify-end">
          <Button onClick={handleConfirm} disabled={!selectedClientId} className="w-full sm:w-auto">
            Confirmar Seleção
          </Button>
        </div>
      </DialogContent>
    </Dialog>
  );
}
