import { Link } from "@tanstack/react-router";
import { createFileRoute } from "@tanstack/react-router";
import { useQuery, useMutation } from "@tanstack/react-query";
import { requireAdminRole } from "@/lib/route-guards";
import { useState, useMemo, useCallback, useEffect } from "react";
import { api, type Fechamento, type PaginatedResponse } from "@/lib/api";
import { Button } from "@/components/ui/button";
import { Card, CardContent, CardHeader, CardTitle } from "@/components/ui/card";
import { Badge } from "@/components/ui/badge";
import { Skeleton } from "@/components/ui/skeleton";
import { Checkbox } from "@/components/ui/checkbox";
import { Table, TableBody, TableCell, TableHead, TableHeader, TableRow } from "@/components/ui/table";
import {
  Calendar,
  ChevronLeft,
  ChevronRight,
  Plus,
  Check,
  X,
  Clock,
  CheckCircle2,
  AlertCircle,
  Loader2,
  FileText,
  Download,
} from "lucide-react";
import { toast } from "sonner";
import { Select, SelectContent, SelectItem, SelectTrigger, SelectValue } from "@/components/ui/select";
import { MultiSelect } from "@/components/ui/multi-select";
import { Input } from "@/components/ui/input";
import { Label } from "@/components/ui/label";
import {
  formatCurrency,
  formatCPF,
  createLocalDate,
  createCurrentLocalDate,
  formatDateForInput,
  formatDate,
} from "@/lib/utils";
import { parseISO, getYear, getMonth } from "date-fns";
import { type Profissional } from "@/lib/api";

const meses = [
  "Janeiro",
  "Fevereiro",
  "Março",
  "Abril",
  "Maio",
  "Junho",
  "Julho",
  "Agosto",
  "Setembro",
  "Outubro",
  "Novembro",
  "Dezembro",
];

function FechamentosIndex() {
  const hoje = createCurrentLocalDate();
  const [dataInicial, setDataInicial] = useState(
    formatDateForInput(createLocalDate(getYear(hoje), getMonth(hoje) + 1, 1))
  );
  const [dataFinal, setDataFinal] = useState(formatDateForInput(createLocalDate(getYear(hoje), getMonth(hoje) + 2, 0)));
  const [profissionalIds, setProfissionalIds] = useState<string[]>([]);
  const [profissionalSearch, setProfissionalSearch] = useState("");
  const [debouncedSearch, setDebouncedSearch] = useState("");
  const [selectedItems, setSelectedItems] = useState<string[]>([]);

  // Debounce search
  useEffect(() => {
    const timer = setTimeout(() => {
      setDebouncedSearch(profissionalSearch);
    }, 300);

    return () => clearTimeout(timer);
  }, [profissionalSearch]);

  const handleProfissionalSearch = useCallback((search: string) => {
    setProfissionalSearch(search);
  }, []);

  const {
    data: fechamentos,
    isLoading,
    refetch,
  } = useQuery<PaginatedResponse<Fechamento>>({
    queryKey: ["fechamentos", dataInicial, dataFinal, profissionalIds],
    queryFn: () => {
      const params: any = { dataInicial, dataFinal, limit: 100 };
      if (profissionalIds.length > 0) {
        params.profissionalId = profissionalIds;
      }
      return api.get("/fechamentos", params);
    },
  });

  const { data: profissionais, isLoading: isLoadingProfissionais } = useQuery<PaginatedResponse<Profissional>>({
    queryKey: ["profissionais", debouncedSearch],
    queryFn: () =>
      api.get("/profissionais", {
        page: 1,
        limit: 10,
        search: debouncedSearch || undefined,
      }),
    enabled: true,
  });

  const aprovarMutation = useMutation({
    mutationFn: async (ids: string[]) => {
      const promises = ids.map((id) => api.post(`/fechamentos/${id}/aprovar`));
      return Promise.all(promises);
    },
    onSuccess: () => {
      toast.success("Fechamentos aprovados com sucesso!");
      setSelectedItems([]);
      refetch();
    },
    onError: (error: any) => {
      toast.error(error.message || "Erro ao aprovar fechamentos");
    },
  });

  const rejeitarMutation = useMutation({
    mutationFn: async (ids: string[]) => {
      const promises = ids.map((id) => api.post(`/fechamentos/${id}/rejeitar`));
      return Promise.all(promises);
    },
    onSuccess: () => {
      toast.success("Fechamentos rejeitados!");
      setSelectedItems([]);
      refetch();
    },
    onError: (error: any) => {
      toast.error(error.message || "Erro ao rejeitar fechamentos");
    },
  });

  const gerarRelatorioMutation = useMutation({
    mutationFn: async () => {
      return api.post<any>("/fechamentos/relatorio", { dataInicial, dataFinal });
    },
    onSuccess: (data: any) => {
      toast.success("Relatório gerado com sucesso!");
      // Handle download or display of report
      if (data?.url) {
        window.open(data.url, "_blank");
      }
    },
    onError: (error: any) => {
      toast.error(error.message || "Erro ao gerar relatório");
    },
  });

  const handleDateRangeChange = (direction: "prev" | "next") => {
    const startDate = parseISO(dataInicial);

    if (direction === "prev") {
      const newStartDate = createLocalDate(getYear(startDate), getMonth(startDate), 1);
      const newEndDate = createLocalDate(getYear(startDate), getMonth(startDate) + 1, 0);
      setDataInicial(formatDateForInput(newStartDate));
      setDataFinal(formatDateForInput(newEndDate));
    } else {
      const newStartDate = createLocalDate(getYear(startDate), getMonth(startDate) + 2, 1);
      const newEndDate = createLocalDate(getYear(startDate), getMonth(startDate) + 3, 0);
      setDataInicial(formatDateForInput(newStartDate));
      setDataFinal(formatDateForInput(newEndDate));
    }
  };

  const toggleSelectAll = () => {
    if (selectedItems.length === fechamentos?.data.length) {
      setSelectedItems([]);
    } else {
      setSelectedItems(fechamentos?.data.map((f) => f.uuid) || []);
    }
  };

  const toggleSelect = (id: string) => {
    setSelectedItems((prev) => (prev.includes(id) ? prev.filter((item) => item !== id) : [...prev, id]));
  };

  const formatCurrency = (value: number) => {
    return new Intl.NumberFormat("pt-BR", {
      style: "currency",
      currency: "BRL",
    }).format(value);
  };

  const getStatusBadge = (status: string) => {
    switch (status) {
      case "PENDENTE":
        return (
          <Badge variant="outline">
            <Clock className="h-3 w-3 mr-1" />
            Pendente
          </Badge>
        );
      case "APROVADO":
        return (
          <Badge variant="default">
            <CheckCircle2 className="h-3 w-3 mr-1" />
            Aprovado
          </Badge>
        );
      case "REJEITADO":
        return (
          <Badge variant="destructive">
            <X className="h-3 w-3 mr-1" />
            Rejeitado
          </Badge>
        );
      default:
        return <Badge variant="secondary">{status}</Badge>;
    }
  };

  const pendentes = fechamentos?.data.filter((f) => f.status === "PENDENTE") || [];
  const aprovados = fechamentos?.data.filter((f) => f.status === "APROVADO") || [];
  const rejeitados = fechamentos?.data.filter((f) => f.status === "REJEITADO") || [];

  const totalValor = fechamentos?.data.reduce((acc, f) => acc + f.totalValor, 0) || 0;
  const totalAprovado = aprovados.reduce((acc, f) => acc + f.totalValor, 0);

  const groupedFechamentos = useMemo(() => {
    if (!fechamentos?.data) return {};

    return fechamentos.data.reduce(
      (groups, fechamento) => {
        const clienteNome = fechamento.plantao?.cliente?.nome || "Cliente não identificado";
        const localNome = fechamento.plantao?.localAtendimento?.nome || "Local não identificado";
        const groupKey = `${clienteNome} - ${localNome}`;

        if (!groups[groupKey]) {
          groups[groupKey] = {
            cliente: fechamento.plantao?.cliente,
            local: fechamento.plantao?.localAtendimento,
            fechamentos: [],
          };
        }

        groups[groupKey].fechamentos.push(fechamento);
        return groups;
      },
      {} as Record<string, { cliente: any; local: any; fechamentos: typeof fechamentos.data }>
    );
  }, [fechamentos?.data]);

  return (
    <div className="space-y-6">
      <div className="flex justify-between items-center">
        <div>
          <h1 className="text-3xl font-bold">Fechamentos</h1>
          <p className="text-muted-foreground">Gerencie o fechamento mensal das escalas</p>
        </div>
        <div className="flex gap-2">
          <Button
            variant="outline"
            onClick={() => gerarRelatorioMutation.mutate()}
            disabled={gerarRelatorioMutation.isPending}
          >
            {gerarRelatorioMutation.isPending ? (
              <Loader2 className="mr-2 h-4 w-4 animate-spin" />
            ) : (
              <Download className="mr-2 h-4 w-4" />
            )}
            Gerar Relatório
          </Button>
          {/* <Link to="/fechamentos/novo">
            <Button>
              <Plus className="mr-2 h-4 w-4" />
              Novo Fechamento
            </Button>
          </Link> */}
        </div>
      </div>

      <div className="grid gap-4 md:grid-cols-4">
        <Card>
          <CardHeader className="flex flex-row items-center justify-between space-y-0 pb-2">
            <CardTitle className="text-sm font-medium">Total do Mês</CardTitle>
            <FileText className="h-4 w-4 text-muted-foreground" />
          </CardHeader>
          <CardContent>
            <div className="text-2xl font-bold">{formatCurrency(totalValor)}</div>
            <p className="text-xs text-muted-foreground">{fechamentos?.data.length || 0} fechamentos</p>
          </CardContent>
        </Card>

        <Card>
          <CardHeader className="flex flex-row items-center justify-between space-y-0 pb-2">
            <CardTitle className="text-sm font-medium">Pendentes</CardTitle>
            <Clock className="h-4 w-4 text-yellow-500" />
          </CardHeader>
          <CardContent>
            <div className="text-2xl font-bold">{pendentes.length}</div>
            <p className="text-xs text-muted-foreground">Aguardando aprovação</p>
          </CardContent>
        </Card>

        <Card>
          <CardHeader className="flex flex-row items-center justify-between space-y-0 pb-2">
            <CardTitle className="text-sm font-medium">Aprovados</CardTitle>
            <CheckCircle2 className="h-4 w-4 text-green-500" />
          </CardHeader>
          <CardContent>
            <div className="text-2xl font-bold">{aprovados.length}</div>
            <p className="text-xs text-muted-foreground">{formatCurrency(totalAprovado)}</p>
          </CardContent>
        </Card>

        <Card>
          <CardHeader className="flex flex-row items-center justify-between space-y-0 pb-2">
            <CardTitle className="text-sm font-medium">Rejeitados</CardTitle>
            <AlertCircle className="h-4 w-4 text-red-500" />
          </CardHeader>
          <CardContent>
            <div className="text-2xl font-bold">{rejeitados.length}</div>
            <p className="text-xs text-muted-foreground">Necessitam revisão</p>
          </CardContent>
        </Card>
      </div>

      <Card>
        <CardHeader>
          <div className="space-y-4">
            <div className="flex justify-between items-center">
              <CardTitle>
                Fechamentos - {formatDate(dataInicial)} a {formatDate(dataFinal)}
              </CardTitle>
              <div className="flex items-center gap-4">
                {selectedItems.length > 0 && (
                  <div className="flex gap-2">
                    <Button
                      size="sm"
                      variant="default"
                      onClick={() => aprovarMutation.mutate(selectedItems)}
                      disabled={aprovarMutation.isPending}
                    >
                      <Check className="h-4 w-4 mr-2" />
                      Aprovar ({selectedItems.length})
                    </Button>
                    {/* <Button
                      size="sm"
                      variant="destructive"
                      onClick={() => rejeitarMutation.mutate(selectedItems)}
                      disabled={rejeitarMutation.isPending}
                    >
                      <X className="h-4 w-4 mr-2" />
                      Rejeitar ({selectedItems.length})
                    </Button> */}
                  </div>
                )}
                <div className="flex items-center gap-2">
                  <Button variant="outline" size="icon" onClick={() => handleDateRangeChange("prev")}>
                    <ChevronLeft className="h-4 w-4" />
                  </Button>
                  <div className="flex gap-2 items-center">
                    <div className="flex flex-col gap-1">
                      <Label htmlFor="dataInicial" className="text-xs">
                        Data Inicial
                      </Label>
                      <Input
                        id="dataInicial"
                        type="date"
                        value={dataInicial}
                        onChange={(e) => setDataInicial(e.target.value)}
                        className="w-[140px]"
                      />
                    </div>
                    <div className="flex flex-col gap-1">
                      <Label htmlFor="dataFinal" className="text-xs">
                        Data Final
                      </Label>
                      <Input
                        id="dataFinal"
                        type="date"
                        value={dataFinal}
                        onChange={(e) => setDataFinal(e.target.value)}
                        className="w-[140px]"
                      />
                    </div>
                  </div>
                  <Button variant="outline" size="icon" onClick={() => handleDateRangeChange("next")}>
                    <ChevronRight className="h-4 w-4" />
                  </Button>
                </div>
              </div>
            </div>

            {/* Selected Professionals Display */}
            {profissionalIds.length > 0 && (
              <div className="flex flex-wrap gap-2 items-center">
                <span className="text-sm text-muted-foreground">Profissionais selecionados:</span>
                {profissionais?.data
                  .filter((prof) => profissionalIds.includes(prof.uuid))
                  .map((profissional) => (
                    <Badge
                      key={profissional.id}
                      variant="secondary"
                      className="cursor-pointer"
                      onClick={() => setProfissionalIds(profissionalIds.filter((id) => id !== profissional.uuid))}
                    >
                      {profissional.usuario?.nome || "N/A"}
                      <X className="ml-1 h-3 w-3" />
                    </Badge>
                  ))}
                <Button variant="ghost" size="sm" onClick={() => setProfissionalIds([])} className="h-6 px-2 text-xs">
                  Limpar todos
                </Button>
              </div>
            )}

            {/* Filter Controls */}
            <div className="flex items-center gap-2">
              <div className="w-[300px]">
                <MultiSelect
                  options={
                    profissionais?.data.map((profissional) => ({
                      value: profissional.id,
                      label: profissional.usuario?.nome || "N/A",
                    })) || []
                  }
                  value={profissionalIds}
                  onChange={(values) => setProfissionalIds(values as string[])}
                  onSearch={handleProfissionalSearch}
                  placeholder="Filtrar por profissionais..."
                  searchPlaceholder="Buscar profissionais..."
                  emptyText={isLoadingProfissionais ? "Carregando..." : "Nenhum profissional encontrado"}
                  hideBadges={true}
                />
              </div>
            </div>
          </div>
        </CardHeader>
        <CardContent>
          {isLoading ? (
            <div className="space-y-4">
              {Array.from({ length: 3 }).map((_, i) => (
                <Skeleton key={i} className="h-20 w-full" />
              ))}
            </div>
          ) : fechamentos?.data.length === 0 ? (
            <div className="text-center py-12">
              <Calendar className="mx-auto h-12 w-12 text-muted-foreground/20 mb-2" />
              <p className="text-muted-foreground">Nenhum fechamento para este período</p>
            </div>
          ) : (
            <div className="space-y-6">
              {Object.entries(groupedFechamentos).map(([groupKey, group]) => (
                <div key={groupKey} className="border rounded-lg">
                  <div className="bg-muted/50 px-4 py-3 border-b">
                    <h3 className="font-semibold text-lg">{group.cliente?.nome || "Cliente não identificado"}</h3>
                    <p className="text-sm text-muted-foreground">{group.local?.nome || "Local não identificado"}</p>
                    <div className="flex gap-4 mt-2 text-sm">
                      <span>Total de fechamentos: {group.fechamentos.length}</span>
                      <span>
                        Valor total: {formatCurrency(group.fechamentos.reduce((acc, f) => acc + f.totalValor, 0))}
                      </span>
                    </div>
                  </div>
                  <Table>
                    <TableHeader>
                      <TableRow>
                        <TableHead className="w-[50px]">
                          <Checkbox
                            checked={group.fechamentos.every((f) => selectedItems.includes(f.uuid))}
                            onCheckedChange={(checked) => {
                              if (checked) {
                                setSelectedItems((prev) => [
                                  ...new Set([...prev, ...group.fechamentos.map((f) => f.uuid)]),
                                ]);
                              } else {
                                setSelectedItems((prev) =>
                                  prev.filter((id) => !group.fechamentos.some((f) => f.uuid === id))
                                );
                              }
                            }}
                          />
                        </TableHead>
                        <TableHead>Profissional</TableHead>
                        <TableHead>Dias Trabalhados</TableHead>
                        <TableHead>Valor Total</TableHead>
                        <TableHead>Status</TableHead>
                        <TableHead className="text-right">Ações</TableHead>
                      </TableRow>
                    </TableHeader>
                    <TableBody>
                      {group.fechamentos.map((fechamento) => (
                        <TableRow key={fechamento.uuid}>
                          <TableCell>
                            <Checkbox
                              checked={selectedItems.includes(fechamento.uuid)}
                              onCheckedChange={() => toggleSelect(fechamento.uuid)}
                            />
                          </TableCell>
                          <TableCell className="font-medium">
                            {fechamento.plantao?.profissional?.usuario?.nome}
                          </TableCell>
                          <TableCell>{fechamento.diasTrabalhados}</TableCell>
                          <TableCell className="font-medium">{formatCurrency(fechamento.totalValor)}</TableCell>
                          <TableCell>{getStatusBadge(fechamento.status)}</TableCell>
                          <TableCell className="text-right">
                            <Link to={"/fechamentos/$uuid"} params={{ uuid: fechamento.uuid }}>
                              <Button variant="ghost" size="sm">
                                Detalhes
                              </Button>
                            </Link>
                          </TableCell>
                        </TableRow>
                      ))}
                    </TableBody>
                  </Table>
                </div>
              ))}
            </div>
          )}
        </CardContent>
      </Card>
    </div>
  );
}

export const Route = createFileRoute("/fechamentos/")({
  component: FechamentosIndex,
  beforeLoad: async () => {
    await requireAdminRole();
  },
});
