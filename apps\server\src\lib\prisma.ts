import { PrismaClient, Prisma } from "@prisma/client";
import { type AuditContext, logAuditManual } from "./audit";
import { AsyncLocalStorage } from "async_hooks";
import { env } from "process";
import type { FastifyTypedRequest } from "@/types";

declare global {
  var prisma: PrismaClientWithAudit | undefined;
}

// Contexto de auditoria thread-local usando AsyncLocalStorage
const auditContextStorage = new AsyncLocalStorage<AuditContext>();

// Função para definir o contexto de auditoria
export function setAuditContext(context: AuditContext) {
  return auditContextStorage.enterWith(context);
}

// Função para obter o contexto atual
export function getAuditContext(): AuditContext | null {
  return auditContextStorage.getStore() || null;
}

// Cliente Prisma com extensão de auditoria
const prismaClientSingleton = (): PrismaClient => {
  const client = new PrismaClient({
    log: env.NODE_ENV === "development" ? ["query", "error", "warn"] : ["error"],
  }).$extends({
    query: {
      $allModels: {
        async create({ model, operation, args, query }) {
          const result = await query(args);
          const context = getAuditContext();

          // Não auditar a própria tabela de auditoria
          if (model !== "AuditLog") {
            setImmediate(() => {
              logAuditManual(
                "CREATE",
                model,
                result.id?.toString() || null,
                `${context?.userName || "Sistema"} criou novo registro em ${model}`,
                context || undefined,
                undefined,
                result
              );
            });
          }

          return result;
        },
        async update({ model, operation, args, query }) {
          const result = await query(args);
          const context = getAuditContext();

          if (model !== "AuditLog") {
            setImmediate(() => {
              logAuditManual(
                "UPDATE",
                model,
                result.id?.toString() || args.where?.id?.toString() || null,
                `${context?.userName || "Sistema"} atualizou registro em ${model}`,
                context || undefined,
                undefined,
                result
              );
            });
          }

          return result;
        },
        async delete({ model, operation, args, query }) {
          const result = await query(args);
          const context = getAuditContext();

          if (model !== "AuditLog") {
            setImmediate(() => {
              logAuditManual(
                "DELETE",
                model,
                args.where?.id?.toString() || null,
                `${context?.userName || "Sistema"} removeu registro de ${model}`,
                context || undefined,
                result,
                undefined
              );
            });
          }

          return result;
        },
        async deleteMany({ model, operation, args, query }) {
          const result = await query(args);
          const context = getAuditContext();

          if (model !== "AuditLog") {
            setImmediate(() => {
              logAuditManual(
                "DELETE",
                model,
                null,
                `${context?.userName || "Sistema"} removeu ${result.count} registros de ${model}`,
                context || undefined,
                undefined,
                undefined
              );
            });
          }

          return result;
        },
      },
    },
  });

  return client as PrismaClient;
};

type PrismaClientWithAudit = ReturnType<typeof prismaClientSingleton>;

// Instância única do Prisma
export const prisma: PrismaClientWithAudit = (global.prisma as PrismaClientWithAudit) || prismaClientSingleton();

if (env.NODE_ENV !== "production") {
  global.prisma = prisma;
}

// Função helper para extrair contexto de requisição Fastify
export function extractAuditContext(request: FastifyTypedRequest): AuditContext {
  const authenticatedRequest = request as any;

  return {
    userId: authenticatedRequest.user?.id || undefined,
    userName: authenticatedRequest.user?.nome || "Sistema", // Usar 'nome' do schema
    userEmail: authenticatedRequest.user?.email,
    userRole: authenticatedRequest.user?.roles?.join(",") || "--", // Usar array de roles
    ipAddress: request.ip || request.socket.remoteAddress,
    userAgent: request.headers["user-agent"],
    endpoint: request.url,
    method: request.method,
  };
}

// Decorator para adicionar contexto de auditoria em rotas
export function withAudit<T extends (...args: any[]) => Promise<any>>(handler: T): T {
  return (async (...args: Parameters<T>) => {
    const request = args.find((arg) => arg && typeof arg === "object" && "method" in arg);

    if (request) {
      const context = extractAuditContext(request as FastifyTypedRequest);
      return auditContextStorage.run(context, async () => {
        return handler(...args);
      });
    }

    return handler(...args);
  }) as T;
}

export { Prisma };
