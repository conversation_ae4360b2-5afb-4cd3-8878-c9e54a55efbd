import { z } from "zod";

export const createProfissionalSchema = z.object({
  nome: z.string().min(1, "Nome é obrigatório"),
  cpf: z.string().length(11, "CPF deve ter 11 dígitos"),
  email: z.email("Email inválido"),
  telefone: z.string().optional().nullable(),

  // Dados pessoais
  dataNascimento: z.string().optional().nullable().or(z.literal("")).or(z.null()),
  genero: z.enum(["Masculino", "Feminino", "Outro"]).optional().nullable().or(z.literal("")).or(z.null()),
  estadoCivil: z
    .enum(["Solteiro", "Casado", "Divorciado", "Viúvo", "União Estável"])
    .optional()
    .nullable()
    .or(z.literal(""))
    .or(z.null()),
  nacionalidade: z.string().optional().nullable().or(z.literal("")).or(z.null()),

  // Endereço
  cep: z.string().optional().nullable().or(z.literal("")).or(z.null()),
  logradouro: z.string().optional().nullable().or(z.literal("")).or(z.null()),
  numero: z.string().optional().nullable().or(z.literal("")).or(z.null()),
  complemento: z.string().optional().nullable().or(z.literal("")).or(z.null()),
  bairro: z.string().optional().nullable().or(z.literal("")).or(z.null()),
  cidade: z.string().optional().nullable().or(z.literal("")).or(z.null()),
  uf: z.string().length(2).optional().nullable().or(z.literal("")).or(z.null()),

  // Dados profissionais
  rg: z.string().optional().nullable().or(z.literal("")).or(z.null()),
  orgaoEmissor: z.string().optional().nullable().or(z.literal("")).or(z.null()),
  cnes: z.string().optional().nullable().or(z.literal("")).or(z.null()),
  conselhoClasse: z.string().optional().nullable().or(z.literal("")).or(z.null()),
  numeroRegistro: z.string().optional().nullable().or(z.literal("")).or(z.null()),
  ufConselho: z.string().length(2).optional().nullable().or(z.literal("")).or(z.null()),

  // Dados bancários
  banco: z.string().optional().nullable().or(z.literal("")).or(z.null()),
  agencia: z.string().optional().nullable().or(z.literal("")).or(z.null()),
  digitoAgencia: z.string().optional().nullable().or(z.literal("")).or(z.null()),
  conta: z.string().optional().nullable().or(z.literal("")).or(z.null()),
  digitoConta: z.string().optional().nullable().or(z.literal("")).or(z.null()),
  tipoConta: z.enum(["corrente", "poupanca"]).optional().nullable().or(z.literal("")).or(z.null()),
  chavePix: z.string().optional().nullable().or(z.literal("")).or(z.null()),
  tipoPix: z
    .enum(["CPF", "CNPJ", "Email", "Telefone", "Aleatória"])
    .optional()
    .nullable()
    .or(z.literal(""))
    .or(z.null()),
  tipoAssinatura: z.enum(["Digital", "Física"]).optional().nullable().or(z.literal("")).or(z.null()),

  // Metadados
  onboardingPendente: z.boolean().optional().default(false),

  especialidadeIds: z.array(z.number()).optional(),
  clienteIds: z.array(z.number()).optional(),
  ativo: z.boolean().default(true),
});

export const updateProfissionalSchema = createProfissionalSchema.partial();

export const profissionalQuerySchema = z.object({
  page: z.string().optional(),
  limit: z.string().optional(),
  search: z.string().optional(),
  ativo: z
    .string()
    .transform((val) => val === "true")
    .optional(),
  especialidade: z.string().optional(),
  clienteId: z.number().optional(),
});

export type CreateProfissionalInput = z.infer<typeof createProfissionalSchema>;
export type UpdateProfissionalInput = z.infer<typeof updateProfissionalSchema>;
export type ProfissionalQuery = z.infer<typeof profissionalQuerySchema>;
