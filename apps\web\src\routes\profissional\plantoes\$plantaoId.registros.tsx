import { createFileRoute } from "@tanstack/react-router";
import { useQuery } from "@tanstack/react-query";
import { api } from "@/lib/api";
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from "@/components/ui/card";
import { Badge } from "@/components/ui/badge";
import { Button } from "@/components/ui/button";
import { Skeleton } from "@/components/ui/skeleton";
import { Alert, AlertDescription } from "@/components/ui/alert";
import { Table, TableBody, TableCell, TableHead, TableHeader, TableRow } from "@/components/ui/table";
import {
  ArrowLeft,
  Clock,
  Calendar,
  MapPin,
  CheckCircle,
  XCircle,
  AlertCircle,
  FileText,
  TrendingUp,
  User,
} from "lucide-react";
import { Link } from "@tanstack/react-router";
import { formatDate } from "@/lib/utils";

interface RegistroPonto {
  id: number;
  data: string;
  dia: number;
  horaEntrada: string | null;
  horaSaida: string | null;
  horasTrabalhadas: number | null;
  valorEstimado: number | null;
  status: string;
  observacoes: string | null;
  metadata: any;
  glosaTempoEmHoras: number | null;
  motivoGlosa: string | null;
  aprovadoPor: string | null;
  aprovadoEm: string | null;
  rejeitadoPor: string | null;
  rejeitadoEm: string | null;
  createdAt: string;
  updatedAt: string;
}

interface HistoricoRegistrosResponse {
  plantao: {
    id: string;
    cliente: string;
    local: string;
    endereco: string;
    cidade: string;
  };
  registros: RegistroPonto[];
  total: number;
  estatisticas: {
    aprovados: number;
    pendentes: number;
    rejeitados: number;
    emRevisao: number;
    totalHoras: number;
    valorTotal: number;
  };
}

function PlantaoRegistros() {
  const { plantaoId } = Route.useParams();

  const { data, isLoading, error } = useQuery<HistoricoRegistrosResponse>({
    queryKey: ["profissional-plantao-registros", plantaoId],
    queryFn: () => api.get(`/profissional/plantoes/${plantaoId}/registros`),
  });

  const formatCurrency = (value: number) => {
    return new Intl.NumberFormat("pt-BR", {
      style: "currency",
      currency: "BRL",
    }).format(value);
  };

  const getStatusBadge = (status: string) => {
    const variants: Record<string, { variant: any; icon: any; label: string }> = {
      APROVADO: {
        variant: "default",
        icon: <CheckCircle className="h-3 w-3" />,
        label: "Aprovado",
      },
      PENDENTE: {
        variant: "secondary",
        icon: <Clock className="h-3 w-3" />,
        label: "Pendente",
      },
      REJEITADO: {
        variant: "destructive",
        icon: <XCircle className="h-3 w-3" />,
        label: "Rejeitado",
      },
      EM_REVISAO: {
        variant: "outline",
        icon: <AlertCircle className="h-3 w-3" />,
        label: "Em Revisão",
      },
    };

    const config = variants[status] || variants.PENDENTE;

    return (
      <Badge variant={config.variant} className="gap-1">
        {config.icon}
        {config.label}
      </Badge>
    );
  };

  const formatTime = (dateString: string | null) => {
    if (!dateString) return "-";
    return new Date(dateString).toLocaleTimeString("pt-BR", {
      hour: "2-digit",
      minute: "2-digit",
    });
  };

  const formatHours = (hours: number | null) => {
    if (hours === null) return "-";
    const h = Math.floor(hours);
    const m = Math.round((hours - h) * 60);
    return `${h}h${m > 0 ? ` ${m}min` : ""}`;
  };

  if (error) {
    return (
      <div className="container mx-auto p-6">
        <Alert variant="destructive">
          <AlertCircle className="h-4 w-4" />
          <AlertDescription>Erro ao carregar histórico de registros. Tente novamente mais tarde.</AlertDescription>
        </Alert>
      </div>
    );
  }

  return (
    <div className="space-y-6">
      {/* Header com navegação */}
      <div className="flex items-center gap-4">
        <Button variant="ghost" size="icon" asChild>
          <Link to="/profissional/plantoes">
            <ArrowLeft className="h-4 w-4" />
          </Link>
        </Button>
        <div>
          <h1 className="text-3xl font-bold">Histórico de Registros</h1>
          {data && (
            <p className="text-muted-foreground">
              {data.plantao.cliente} - {data.plantao.local}
            </p>
          )}
        </div>
      </div>

      {isLoading ? (
        <div className="space-y-4">
          <Skeleton className="h-32 w-full" />
          <Skeleton className="h-64 w-full" />
        </div>
      ) : data ? (
        <>
          {/* Cards de estatísticas */}
          <div className="grid gap-4 md:grid-cols-2 lg:grid-cols-6">
            <Card>
              <CardHeader className="pb-2">
                <CardTitle className="text-sm font-medium">Total de Registros</CardTitle>
              </CardHeader>
              <CardContent>
                <div className="text-2xl font-bold">{data.total}</div>
              </CardContent>
            </Card>

            <Card>
              <CardHeader className="pb-2">
                <CardTitle className="text-sm font-medium">Aprovados</CardTitle>
              </CardHeader>
              <CardContent>
                <div className="text-2xl font-bold text-green-600">{data.estatisticas.aprovados}</div>
              </CardContent>
            </Card>

            <Card>
              <CardHeader className="pb-2">
                <CardTitle className="text-sm font-medium">Pendentes</CardTitle>
              </CardHeader>
              <CardContent>
                <div className="text-2xl font-bold text-yellow-600">{data.estatisticas.pendentes}</div>
              </CardContent>
            </Card>

            <Card>
              <CardHeader className="pb-2">
                <CardTitle className="text-sm font-medium">Rejeitados</CardTitle>
              </CardHeader>
              <CardContent>
                <div className="text-2xl font-bold text-red-600">{data.estatisticas.rejeitados}</div>
              </CardContent>
            </Card>

            <Card>
              <CardHeader className="pb-2">
                <CardTitle className="text-sm font-medium">Total de Horas</CardTitle>
              </CardHeader>
              <CardContent>
                <div className="text-2xl font-bold">{formatHours(data.estatisticas.totalHoras)}</div>
              </CardContent>
            </Card>

            <Card>
              <CardHeader className="pb-2">
                <CardTitle className="text-sm font-medium">Valor Total</CardTitle>
              </CardHeader>
              <CardContent>
                <div className="text-2xl font-bold">{formatCurrency(data.estatisticas.valorTotal)}</div>
              </CardContent>
            </Card>
          </div>

          {/* Informações do plantão */}
          <Card>
            <CardHeader>
              <CardTitle className="flex items-center gap-2">
                <MapPin className="h-5 w-5" />
                Local de Trabalho
              </CardTitle>
            </CardHeader>
            <CardContent>
              <div className="space-y-2">
                <div>
                  <span className="font-semibold">Cliente:</span> {data.plantao.cliente}
                </div>
                <div>
                  <span className="font-semibold">Local:</span> {data.plantao.local}
                </div>
                <div>
                  <span className="font-semibold">Endereço:</span> {data.plantao.endereco}
                </div>
                <div>
                  <span className="font-semibold">Cidade:</span> {data.plantao.cidade}
                </div>
              </div>
            </CardContent>
          </Card>

          {/* Tabela de registros */}
          <Card>
            <CardHeader>
              <CardTitle>Registros de Ponto</CardTitle>
              <CardDescription>Histórico completo de check-ins e check-outs</CardDescription>
            </CardHeader>
            <CardContent>
              {data.registros.length === 0 ? (
                <div className="text-center py-8">
                  <Clock className="h-12 w-12 text-muted-foreground mx-auto mb-3" />
                  <p className="text-muted-foreground">Nenhum registro de ponto encontrado</p>
                </div>
              ) : (
                <div className="overflow-x-auto">
                  <Table>
                    <TableHeader>
                      <TableRow>
                        <TableHead>Data</TableHead>
                        <TableHead>Check-in</TableHead>
                        <TableHead>Check-out</TableHead>
                        <TableHead>Horas</TableHead>
                        <TableHead>Valor</TableHead>
                        <TableHead>Status</TableHead>
                        <TableHead>Localização</TableHead>
                        <TableHead>Observações</TableHead>
                      </TableRow>
                    </TableHeader>
                    <TableBody>
                      {data.registros.map((registro) => {
                        const metadata = registro.metadata as any;
                        const hasLocation = metadata?.checkin?.latitude;
                        const isInRange = metadata?.checkin?.dentroDoRaio;

                        return (
                          <TableRow key={registro.id}>
                            <TableCell>
                              <div className="flex items-center gap-2">
                                <Calendar className="h-4 w-4 text-muted-foreground" />
                                <div>
                                  <div className="font-medium">{formatDate(new Date(registro.data))}</div>
                                  <div className="text-xs text-muted-foreground">Dia {registro.dia}</div>
                                </div>
                              </div>
                            </TableCell>
                            <TableCell>{formatTime(registro.horaEntrada)}</TableCell>
                            <TableCell>{formatTime(registro.horaSaida)}</TableCell>
                            <TableCell>{formatHours(registro.horasTrabalhadas)}</TableCell>
                            <TableCell>
                              {registro.valorEstimado ? formatCurrency(registro.valorEstimado) : "-"}
                            </TableCell>
                            <TableCell>{getStatusBadge(registro.status)}</TableCell>
                            <TableCell>
                              {hasLocation ? (
                                <Badge variant={isInRange ? "outline" : "secondary"} className="gap-1">
                                  <MapPin className="h-3 w-3" />
                                  {isInRange ? "No local" : "Fora"}
                                  {metadata.checkin.distancia && (
                                    <span className="text-xs">({Math.round(metadata.checkin.distancia)}m)</span>
                                  )}
                                </Badge>
                              ) : (
                                <span className="text-muted-foreground text-sm">Sem localização</span>
                              )}
                            </TableCell>
                            <TableCell>
                              {registro.observacoes ? (
                                <div className="max-w-[200px]">
                                  <p className="text-sm truncate" title={registro.observacoes}>
                                    {registro.observacoes}
                                  </p>
                                </div>
                              ) : (
                                "-"
                              )}
                            </TableCell>
                          </TableRow>
                        );
                      })}
                    </TableBody>
                  </Table>
                </div>
              )}
            </CardContent>
          </Card>

          {/* Informações adicionais */}
          {data.registros.some((r) => r.glosaTempoEmHoras || r.aprovadoPor || r.rejeitadoPor) && (
            <Card>
              <CardHeader>
                <CardTitle>Informações de Aprovação</CardTitle>
              </CardHeader>
              <CardContent>
                <div className="space-y-4">
                  {data.registros
                    .filter((r) => r.glosaTempoEmHoras || r.aprovadoPor || r.rejeitadoPor)
                    .map((registro) => (
                      <div key={registro.id} className="border-l-2 border-muted pl-4 space-y-1">
                        <div className="text-sm font-medium">
                          {formatDate(new Date(registro.data))} - Dia {registro.dia}
                        </div>
                        {registro.glosaTempoEmHoras && (
                          <div className="text-sm text-red-600">
                            <span className="font-medium">Glosa:</span> {formatHours(registro.glosaTempoEmHoras)}
                            {registro.motivoGlosa && ` - ${registro.motivoGlosa}`}
                          </div>
                        )}
                        {registro.aprovadoPor && (
                          <div className="text-sm text-green-600">
                            <User className="h-3 w-3 inline mr-1" />
                            Aprovado por {registro.aprovadoPor} em {formatDate(new Date(registro.aprovadoEm!))}
                          </div>
                        )}
                        {registro.rejeitadoPor && (
                          <div className="text-sm text-red-600">
                            <User className="h-3 w-3 inline mr-1" />
                            Rejeitado por {registro.rejeitadoPor} em {formatDate(new Date(registro.rejeitadoEm!))}
                          </div>
                        )}
                      </div>
                    ))}
                </div>
              </CardContent>
            </Card>
          )}
        </>
      ) : null}
    </div>
  );
}

export const Route = createFileRoute("/profissional/plantoes/$plantaoId/registros")({
  component: PlantaoRegistros,
});
