import { prisma } from "@/lib/prisma";
import { z } from "zod";
import type { FastifyTypedInstance } from "@/types";
import { authorize } from "@/middlewares/auth.middleware";
import { createLocalDate, getCurrentDate } from "@shared/date";

export async function profissionalPlantoesRouter(fastify: FastifyTypedInstance) {
  // Buscar plantões do profissional logado
  fastify.get(
    "/profissional/plantoes",
    {
      preHandler: [authorize("profissional")],
    },
    async (request, reply) => {
      const querySchema = z.object({
        mes: z.coerce.number().min(1).max(12),
        ano: z.coerce.number().min(2020).max(2030),
      });

      const { mes, ano } = querySchema.parse(request.query);
      const userId = request.user!.id;

      // Buscar o profissional associado ao usuário
      const profissional = await prisma.profissional.findUnique({
        where: { usuarioId: userId },
      });

      if (!profissional) {
        return reply.status(404).send({ error: "Profissional não encontrado" });
      }

      // Buscar plantões do profissional
      const where: any = {
        profissionalId: profissional.id,
      };

      // Filtrar plantões que contenham o mês/ano informado
      // Um plantão contém o mês/ano se: dataInicial <= fim do mês E (dataFinal >= início do mês OU dataFinal é null)
      const inicioMes = createLocalDate(ano, mes, 1); // Primeiro dia do mês
      const fimMes = createLocalDate(ano, mes + 1, 0); // Último dia do mês

      // Buscar plantões onde:
      // - dataInicial seja menor ou igual ao fim do mês (plantão começa antes ou durante o mês)
      // - E (dataFinal seja maior ou igual ao início do mês OU dataFinal seja null)
      where.dataInicial = { lte: fimMes };
      where.OR = [{ dataFinal: { gte: inicioMes } }, { dataFinal: null }];

      console.log({ where });

      const plantoes = await prisma.plantao.findMany({
        where,
        include: {
          cliente: true,
          localAtendimento: true,
          diasPlantao: {
            include: {
              presencaDiaPlantao: true,
            },
          },
        },
      });

      // Formatar resposta
      const plantoesFormatados = plantoes.map((plantao: any) => ({
        id: plantao.id,
        clienteId: plantao.clienteId,
        cliente: {
          nome: plantao.cliente.nome,
          tipo: plantao.cliente.tipo,
        },
        localAtendimentoId: plantao.localAtendimentoId,
        localAtendimento: {
          nome: plantao.localAtendimento.nome,
          endereco: plantao.localAtendimento.endereco,
          numero: plantao.localAtendimento.numero,
          bairro: plantao.localAtendimento.bairro,
          cidade: plantao.localAtendimento.cidade,
          estado: plantao.localAtendimento.estado,
        },
        dataInicial: plantao.dataInicial,
        dataFinal: plantao.dataFinal,
        modalidadeTrabalho: plantao.modalidadeTrabalho,
        tipoFechamento: plantao.tipoFechamento,
        tipoValor: plantao.tipoValor,
        valorBase: plantao.valorBase,
        diasPlantao: plantao.diasPlantao.map((dia: any) => ({
          id: dia.id,
          dia: dia.dia,
          horaEntrada: dia.horaEntrada,
          horaSaida: dia.horaSaida,
          intervalo: dia.intervalo,
          trabalhado:
            dia.presencaDiaPlantao &&
            dia.presencaDiaPlantao.length > 0 &&
            dia.presencaDiaPlantao[0]?.status === "APROVADO",
          presenca: dia.presenca
            ? {
                id: dia.presenca.id,
                horaEntrada: dia.presenca.horaEntrada,
                horaSaida: dia.presenca.horaSaida,
                status: dia.presenca.status,
              }
            : undefined,
        })),
      }));

      return reply.send(plantoesFormatados);
    }
  );

  // Check-in
  fastify.post(
    "/profissional/plantoes/:plantaoId/checkin",
    {
      preHandler: [authorize("profissional")],
    },
    async (request, reply) => {
      const paramsSchema = z.object({
        plantaoId: z.string(),
      });

      const bodySchema = z.object({
        diaPlantaoId: z.string(),
        latitude: z.number().optional(),
        longitude: z.number().optional(),
        photo: z.string().optional(), // Base64 encoded photo
        observacao: z.string().optional(),
      });

      const { plantaoId } = paramsSchema.parse(request.params);
      const { diaPlantaoId, latitude, longitude, photo, observacao } = bodySchema.parse(request.body);
      const userId = request.user!.id;

      // Verificar se o profissional tem acesso ao plantão
      const profissional = await prisma.profissional.findUnique({
        where: { usuarioId: userId },
      });

      if (!profissional) {
        return reply.status(404).send({ error: "Profissional não encontrado" });
      }

      const plantao = await prisma.plantao.findFirst({
        where: {
          id: parseInt(plantaoId),
          profissionalId: profissional.id,
        },
      });

      if (!plantao) {
        return reply.status(404).send({ error: "Plantão não encontrado" });
      }

      // Verificar se o dia pertence ao plantão
      const diaPlantao = await prisma.diaPlantao.findFirst({
        where: {
          id: parseInt(diaPlantaoId),
          plantaoId: parseInt(plantaoId),
        },
      });

      if (!diaPlantao) {
        return reply.status(404).send({ error: "Dia de plantão não encontrado" });
      }

      // Verificar se já existe presença para este dia
      const presencaExistente = await prisma.presencaDiaPlantao.findFirst({
        where: {
          diaPlantaoId: parseInt(diaPlantaoId),
        },
      });

      if (presencaExistente && presencaExistente.horaEntrada) {
        return reply.status(400).send({ error: "Check-in já realizado para este dia" });
      }

      // Verificar localização se fornecida
      let dentroDoRaio = true;
      let distancia: number | null = null;

      if (latitude && longitude && plantao.localAtendimentoId) {
        const local = await prisma.localAtendimento.findUnique({
          where: { id: plantao.localAtendimentoId },
        });

        if (local?.latitude && local?.longitude) {
          // Calcular distância usando fórmula de Haversine
          const R = 6371; // Raio da Terra em km
          const dLat = ((local.latitude - latitude) * Math.PI) / 180;
          const dLon = ((local.longitude - longitude) * Math.PI) / 180;
          const a =
            Math.sin(dLat / 2) * Math.sin(dLat / 2) +
            Math.cos((latitude * Math.PI) / 180) *
              Math.cos((local.latitude * Math.PI) / 180) *
              Math.sin(dLon / 2) *
              Math.sin(dLon / 2);
          const c = 2 * Math.atan2(Math.sqrt(a), Math.sqrt(1 - a));
          distancia = R * c * 1000; // Distância em metros

          // Verificar se está dentro de 500 metros do local
          dentroDoRaio = distancia <= 500;
        }
      }

      // Criar ou atualizar presença
      const horaEntrada = getCurrentDate();

      // Preparar dados da presença
      const dadosPresenca: any = {
        horaEntrada,
        status: "PENDENTE",
        observacao: observacao,
      };

      // Adicionar metadados se disponíveis
      if (latitude && longitude) {
        dadosPresenca.metadata = {
          checkin: {
            latitude,
            longitude,
            dentroDoRaio,
            distancia,
            photo: photo ? photo.substring(0, 100) + "..." : null, // Salvar referência da foto
            timestamp: horaEntrada.toISOString(),
          },
        };
      }

      const presenca = presencaExistente
        ? await prisma.presencaDiaPlantao.update({
            where: { id: presencaExistente.id },
            data: dadosPresenca,
          })
        : await prisma.presencaDiaPlantao.create({
            data: {
              diaPlantaoId: parseInt(diaPlantaoId),
              ...dadosPresenca,
            },
          });

      return reply.send({
        success: true,
        message: dentroDoRaio ? "Check-in realizado com sucesso" : "Check-in realizado fora do local de trabalho",
        presenca,
        localizacao: {
          dentroDoRaio,
          distancia,
        },
      });
    }
  );

  // Check-out
  fastify.post(
    "/profissional/plantoes/:plantaoId/checkout",
    {
      preHandler: [authorize("profissional")],
    },
    async (request, reply) => {
      const paramsSchema = z.object({
        plantaoId: z.string(),
      });

      const bodySchema = z.object({
        diaPlantaoId: z.string(),
        latitude: z.number().optional(),
        longitude: z.number().optional(),
        observacao: z.string().optional(),
      });

      const { plantaoId } = paramsSchema.parse(request.params);
      const { diaPlantaoId, latitude, longitude, observacao } = bodySchema.parse(request.body);
      const userId = request.user!.id;

      // Verificar se o profissional tem acesso ao plantão
      const profissional = await prisma.profissional.findUnique({
        where: { usuarioId: userId },
      });

      if (!profissional) {
        return reply.status(404).send({ error: "Profissional não encontrado" });
      }

      const plantao = await prisma.plantao.findFirst({
        where: {
          id: parseInt(plantaoId),
          profissionalId: profissional.id,
        },
      });

      if (!plantao) {
        return reply.status(404).send({ error: "Plantão não encontrado" });
      }

      // Verificar se existe presença com check-in
      const presenca = await prisma.presencaDiaPlantao.findFirst({
        where: {
          diaPlantaoId: parseInt(diaPlantaoId),
        },
      });

      if (!presenca || !presenca.horaEntrada) {
        return reply.status(400).send({ error: "Check-in não realizado" });
      }

      if (presenca.horaSaida) {
        return reply.status(400).send({ error: "Check-out já realizado" });
      }

      // Calcular horas trabalhadas
      const horaSaida = getCurrentDate();
      const horasTrabalhadas = (horaSaida.getTime() - presenca.horaEntrada.getTime()) / (1000 * 60 * 60);

      // Calcular valor estimado
      let valorEstimado = 0;
      if (plantao.tipoValor === "HORA" && plantao.valorBase) {
        valorEstimado = horasTrabalhadas * plantao.valorBase;
      } else if (plantao.tipoValor === "PLANTAO" && plantao.valorBase) {
        valorEstimado = plantao.valorBase;
      }

      // Preparar dados de atualização
      const dadosAtualizacao: any = {
        horaSaida,
        horasTrabalhadas,
        valorEstimado,
        status: "PENDENTE",
      };

      // Adicionar observação se fornecida
      if (observacao) {
        dadosAtualizacao.observacao = presenca.observacao
          ? `${presenca.observacao}\n[Checkout] ${observacao}`
          : `[Checkout] ${observacao}`;
      }

      // Adicionar localização do checkout
      if (latitude && longitude) {
        const metadata = (presenca.metadata as any) || {};
        metadata.checkout = {
          latitude,
          longitude,
          timestamp: horaSaida.toISOString(),
        };
        dadosAtualizacao.metadata = metadata;
      }

      // Atualizar presença
      const presencaAtualizada = await prisma.presencaDiaPlantao.update({
        where: { id: presenca.id },
        data: dadosAtualizacao,
      });

      // Dia não precisa mais ser atualizado, pois o controle é feito pela PresencaDiaPlantao

      return reply.send({
        success: true,
        message: "Check-out realizado com sucesso",
        presenca: presencaAtualizada,
      });
    }
  );

  // Histórico de registros de ponto
  fastify.get(
    "/profissional/plantoes/:plantaoId/registros",
    {
      preHandler: [authorize("profissional")],
    },
    async (request, reply) => {
      const paramsSchema = z.object({
        plantaoId: z.string(),
      });

      const { plantaoId } = paramsSchema.parse(request.params);
      const userId = request.user!.id;

      // Verificar se o profissional tem acesso ao plantão
      const profissional = await prisma.profissional.findUnique({
        where: { usuarioId: userId },
      });

      if (!profissional) {
        return reply.status(404).send({ error: "Profissional não encontrado" });
      }

      const plantao = await prisma.plantao.findFirst({
        where: {
          id: parseInt(plantaoId),
          profissionalId: profissional.id,
        },
        include: {
          cliente: true,
          localAtendimento: true,
        },
      });

      if (!plantao) {
        return reply.status(404).send({ error: "Plantão não encontrado" });
      }

      // Buscar todos os registros de ponto do plantão
      const registros = await prisma.presencaDiaPlantao.findMany({
        where: {
          diaPlantao: {
            plantaoId: plantao.id,
          },
        },
        include: {
          diaPlantao: true,
        },
        orderBy: {
          diaPlantao: {
            data: "desc",
          },
        },
      });

      // Formatar resposta
      const registrosFormatados = registros.map((registro) => ({
        id: registro.id,
        data: registro.diaPlantao.data,
        // dia: registro.diaPlantao.dia,
        horaEntrada: registro.horaEntrada,
        horaSaida: registro.horaSaida,
        horasTrabalhadas: registro.horasTrabalhadas,
        valorEstimado: registro.valorEstimado,
        status: registro.status,
        observacao: registro.observacao,
        metadata: registro.metadata,
        glosaTempoEmHoras: registro.tempoGlosado,
        motivoGlosa: registro.justificativaGlosa,
        aprovadoPor: registro.historicoAprovacoes ? (registro.historicoAprovacoes as any)?.aprovadoPor : null,
        aprovadoEm: registro.historicoAprovacoes ? (registro.historicoAprovacoes as any)?.aprovadoEm : null,
        rejeitadoPor: registro.historicoAprovacoes ? (registro.historicoAprovacoes as any)?.rejeitadoPor : null,
        rejeitadoEm: registro.historicoAprovacoes ? (registro.historicoAprovacoes as any)?.rejeitadoEm : null,
        createdAt: registro.createdAt,
        updatedAt: registro.updatedAt,
      }));

      return reply.send({
        plantao: {
          id: plantao.id.toString(),
          cliente: plantao.cliente.nome,
          local: plantao.localAtendimento.nome,
          endereco: `${plantao.localAtendimento.endereco}`,
          cidade: `${plantao.localAtendimento.cidade}/${plantao.localAtendimento.estado}`,
        },
        registros: registrosFormatados,
        total: registrosFormatados.length,
        estatisticas: {
          aprovados: registros.filter((r) => r.status === "APROVADO").length,
          pendentes: registros.filter((r) => r.status === "PENDENTE").length,
          rejeitados: registros.filter((r) => r.status === "REJEITADO").length,
          emRevisao: registros.filter((r) => r.status === "EM_REVISAO").length,
          totalHoras: registros.reduce((acc, r) => acc + (r.horasTrabalhadas || 0), 0),
          valorTotal: registros.reduce((acc, r) => acc + (r.valorEstimado || 0), 0),
        },
      });
    }
  );
}
