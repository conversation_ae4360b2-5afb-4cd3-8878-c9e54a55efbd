import { <PERSON>, <PERSON><PERSON>onte<PERSON>, <PERSON><PERSON><PERSON><PERSON>, <PERSON><PERSON>it<PERSON> } from "@/components/ui/card";
import { formatCurrency } from "@/lib/utils";

interface SummaryCardProps {
  data: {
    estatisticas: {
      totalRegistros: number;
      diasUnicos: number;
      registrosCompletos: number;
      totalHorasTrabalhadas: number;
      valorEstimado: number;
      registrosComConflito: number;
    };
  };
}

export function SummaryCard({ data }: SummaryCardProps) {
  if (!data || data.estatisticas.totalRegistros === 0) {
    return null;
  }

  return (
    <Card>
      <CardHeader>
        <CardTitle className="text-base">Resumo do Período</CardTitle>
      </CardHeader>
      <CardContent>
        <div className="grid grid-cols-2 md:grid-cols-6 gap-4">
          <div>
            <p className="text-sm text-muted-foreground">Total Registros</p>
            <p className="text-2xl font-bold">{data.estatisticas.totalRegistros}</p>
          </div>
          <div>
            <p className="text-sm text-muted-foreground">Dias Únicos</p>
            <p className="text-2xl font-bold">{data.estatisticas.diasUnicos}</p>
          </div>
          <div>
            <p className="text-sm text-muted-foreground">Completos</p>
            <p className="text-2xl font-bold text-green-500">{data.estatisticas.registrosCompletos}</p>
          </div>
          <div>
            <p className="text-sm text-muted-foreground">Total Horas</p>
            <p className="text-2xl font-bold text-blue-500">{data.estatisticas.totalHorasTrabalhadas.toFixed(1)}h</p>
          </div>
          <div>
            <p className="text-sm text-muted-foreground">Valor Total</p>
            <p className="text-2xl font-bold">{formatCurrency(data.estatisticas.valorEstimado || 0)}</p>
          </div>
          {data.estatisticas.registrosComConflito > 0 && (
            <div>
              <p className="text-sm text-muted-foreground">Conflitos</p>
              <p className="text-2xl font-bold text-red-500">{data.estatisticas.registrosComConflito}</p>
            </div>
          )}
        </div>
      </CardContent>
    </Card>
  );
}
