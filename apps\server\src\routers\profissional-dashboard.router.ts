import { prisma } from "../lib/prisma.js";
import { authenticate } from "../middlewares/auth.middleware.js";
import type { FastifyTypedInstance } from "@/types";
import {
  getCurrentDate,
  createLocalDate,
  getCurrentMonth,
  getCurrentYear,
  toISOString,
  getStartOfMonth,
  getEndOfMonth,
  startOfDay,
  endOfDay,
  addDaysToDate,
  subtractMonthsFromDate,
} from "@shared/date";

export async function profissionalDashboardRouter(app: FastifyTypedInstance) {
  // Dashboard Summary
  app.get(
    "/profissional/dashboard/summary",
    {
      preHandler: [authenticate],
    },
    async (request, reply) => {
      const { mes, ano } = request.query as { mes?: number; ano?: number };
      const userId = request?.user?.id;

      // Get current date or use provided month/year
      const now = getCurrentDate();
      const targetMonth = mes || getCurrentMonth();
      const targetYear = ano || getCurrentYear();

      const startDate = getStartOfMonth(createLocalDate(targetYear, targetMonth, 1));
      const endDate = getEndOfMonth(createLocalDate(targetYear, targetMonth, 1));

      // Get professional data
      const profissional = await prisma.profissional.findUnique({
        where: { usuarioId: userId },
      });

      if (!profissional) {
        return reply.status(404).send({ error: "Profissional não encontrado" });
      }

      // Financial summary for current month
      const financialSummary = await prisma.fechamento.aggregate({
        where: {
          profissionalId: profissional.id,
          createdAt: {
            gte: startDate,
            lte: endDate,
          },
        },
        _sum: {
          totalValor: true,
        },
        _count: true,
      });

      // Pending closures
      const pendingClosures = await prisma.fechamento.count({
        where: {
          profissionalId: profissional.id,
          status: "PENDENTE",
        },
      });

      // Approved closures this month
      const approvedClosures = await prisma.fechamento.aggregate({
        where: {
          profissionalId: profissional.id,
          status: "APROVADO",
          aprovadoEm: {
            gte: startDate,
            lte: endDate,
          },
        },
        _sum: {
          totalValor: true,
        },
        _count: true,
      });

      // Anticipations summary
      const anticipations = await prisma.antecipacao.aggregate({
        where: {
          profissionalId: profissional.id,
          createdAt: {
            gte: startDate,
            lte: endDate,
          },
        },
        _sum: {
          valorSolicitado: true,
          valorAprovado: true,
        },
        _count: {
          _all: true,
        },
      });

      // Pending anticipations
      const pendingAntecipations = await prisma.antecipacao.count({
        where: {
          profissionalId: profissional.id,
          status: "PENDENTE",
        },
      });

      // Hours worked this month
      const hoursWorked = await prisma.presencaDiaPlantao.aggregate({
        where: {
          diaPlantao: {
            plantao: {
              profissionalId: profissional.id,
            },
            data: {
              gte: startDate,
              lte: endDate,
            },
          },
          status: "APROVADO",
        },
        _sum: {
          horasTrabalhadas: true,
        },
      });

      // Compare with last month
      const lastMonthStart = getStartOfMonth(subtractMonthsFromDate(startDate, 1));
      const lastMonthEnd = getEndOfMonth(subtractMonthsFromDate(startDate, 1));

      const lastMonthFinancial = await prisma.fechamento.aggregate({
        where: {
          profissionalId: profissional.id,
          createdAt: {
            gte: lastMonthStart,
            lte: lastMonthEnd,
          },
          status: "APROVADO",
        },
        _sum: {
          totalValor: true,
        },
      });

      const currentMonthEarnings = approvedClosures._sum.totalValor || 0;
      const lastMonthEarnings = lastMonthFinancial._sum.totalValor || 0;
      const earningsVariation =
        lastMonthEarnings > 0 ? ((currentMonthEarnings - lastMonthEarnings) / lastMonthEarnings) * 100 : 0;

      return {
        financial: {
          currentMonth: {
            total: financialSummary._sum.totalValor || 0,
            approved: approvedClosures._sum.totalValor || 0,
            pending: pendingClosures,
            count: financialSummary._count,
          },
          variation: {
            percentage: earningsVariation,
            value: currentMonthEarnings - lastMonthEarnings,
          },
        },
        anticipations: {
          requested: anticipations._sum.valorSolicitado || 0,
          approved: anticipations._sum.valorAprovado || 0,
          pending: pendingAntecipations,
          count: anticipations._count._all,
        },
        hours: {
          worked: hoursWorked._sum.horasTrabalhadas || 0,
        },
        month: targetMonth,
        year: targetYear,
      };
    }
  );

  // Today's shift
  app.get(
    "/profissional/dashboard/today",
    {
      preHandler: [authenticate],
    },
    async (request, reply) => {
      const userId = request?.user?.id;
      const today = getCurrentDate();
      const todayStart = startOfDay(today);
      const todayEnd = endOfDay(today);

      const profissional = await prisma.profissional.findUnique({
        where: { usuarioId: userId },
      });

      if (!profissional) {
        return reply.status(404).send({ error: "Profissional não encontrado" });
      }

      // Get today's shifts
      const todayShifts = await prisma.diaPlantao.findMany({
        where: {
          plantao: {
            profissionalId: profissional.id,
          },
          data: {
            gte: todayStart,
            lte: todayEnd,
          },
        },
        include: {
          plantao: {
            include: {
              cliente: true,
              localAtendimento: true,
            },
          },
          presencaDiaPlantao: {
            orderBy: {
              createdAt: "desc",
            },
            take: 1,
          },
        },
      });

      // Format response
      const shifts = todayShifts.map((shift) => ({
        id: shift.id,
        plantaoId: shift.plantaoId,
        cliente: shift.plantao.cliente.nome,
        local: shift.plantao.localAtendimento.nome,
        endereco: `${shift.plantao.localAtendimento.endereco}, ${shift.plantao.localAtendimento.cidade}/${shift.plantao.localAtendimento.estado}`,
        horario: {
          entrada: shift.horaEntrada,
          saida: shift.horaSaida,
          intervalo: shift.intervalo,
        },
        status: {
          trabalhado: shift.presencaDiaPlantao.length > 0 && shift.presencaDiaPlantao[0]?.status === "APROVADO",
          checkin: shift.presencaDiaPlantao[0]?.horaEntrada || null,
          checkout: shift.presencaDiaPlantao[0]?.horaSaida || null,
          presencaStatus: shift.presencaDiaPlantao[0]?.status || "PENDENTE",
        },
      }));

      return {
        date: toISOString(today),
        shifts,
        hasShifts: shifts.length > 0,
      };
    }
  );

  // Upcoming shifts (next 7 days)
  app.get(
    "/profissional/dashboard/upcoming",
    {
      preHandler: [authenticate],
    },
    async (request, reply) => {
      const userId = request?.user?.id;
      const today = getCurrentDate();
      const nextWeek = addDaysToDate(today, 7);

      const profissional = await prisma.profissional.findUnique({
        where: { usuarioId: userId },
      });

      if (!profissional) {
        return reply.status(404).send({ error: "Profissional não encontrado" });
      }

      const upcomingShifts = await prisma.diaPlantao.findMany({
        where: {
          plantao: {
            profissionalId: profissional.id,
          },
          data: {
            gte: startOfDay(today),
            lte: endOfDay(nextWeek),
          },
        },
        include: {
          plantao: {
            include: {
              cliente: true,
              localAtendimento: true,
            },
          },
        },
        orderBy: {
          data: "asc",
        },
        take: 10,
      });

      const shifts = upcomingShifts.map((shift) => ({
        id: shift.id,
        plantaoId: shift.plantaoId,
        data: shift.data,
        cliente: shift.plantao.cliente.nome,
        local: shift.plantao.localAtendimento.nome,
        horario: {
          entrada: shift.horaEntrada,
          saida: shift.horaSaida,
        },
        valorEstimado: shift.plantao.valorBase || 0,
      }));

      return {
        shifts,
        count: shifts.length,
      };
    }
  );

  // Recent notifications
  app.get(
    "/profissional/dashboard/notifications",
    {
      preHandler: [authenticate],
    },
    async (request, reply) => {
      const userId = request?.user?.id;
      const profissional = await prisma.profissional.findUnique({
        where: { usuarioId: userId },
      });

      if (!profissional) {
        return reply.status(404).send({ error: "Profissional não encontrado" });
      }

      // Get recent status changes
      const recentFechamentos = await prisma.fechamento.findMany({
        where: {
          profissionalId: profissional.id,
          OR: [{ aprovadoEm: { not: null } }, { rejeitadoEm: { not: null } }],
        },
        orderBy: {
          updatedAt: "desc",
        },
        take: 5,
        include: {
          plantao: {
            include: {
              cliente: true,
            },
          },
        },
      });

      const recentAntecipacoes = await prisma.antecipacao.findMany({
        where: {
          profissionalId: profissional.id,
          OR: [{ status: "APROVADA" }, { status: "PAGA" }],
        },
        orderBy: {
          updatedAt: "desc",
        },
        take: 5,
        include: {
          plantao: {
            include: {
              cliente: true,
            },
          },
        },
      });

      // Format notifications
      const notifications = [
        ...recentFechamentos.map((f) => ({
          id: f.id,
          type: "fechamento" as const,
          status: f.status,
          message:
            f.status === "APROVADO"
              ? `Fechamento aprovado - ${f.plantao.cliente.nome}`
              : `Fechamento rejeitado - ${f.plantao.cliente.nome}`,
          date: f.aprovadoEm || f.rejeitadoEm,
          value: f.totalValor,
          reason: f.motivoRejeicao,
        })),
        ...recentAntecipacoes.map((a) => ({
          id: a.id,
          type: "antecipacao" as const,
          status: a.status,
          message:
            a.status === "PAGA"
              ? `Antecipação paga - ${a.plantao.cliente.nome}`
              : a.status === "APROVADA"
                ? `Antecipação aprovada - ${a.plantao.cliente.nome}`
                : `Antecipação - ${a.plantao.cliente.nome}`,
          date: a.updatedAt,
          value: a.valorAprovado || a.valorSolicitado,
          reason: null,
        })),
      ]
        .filter((n) => n.date)
        .sort((a, b) => b.date!.getTime() - a.date!.getTime())
        .slice(0, 10);

      return {
        notifications,
        unreadCount: 0, // Will implement read/unread later
      };
    }
  );

  // Hours statistics
  app.get(
    "/profissional/dashboard/hours-stats",
    {
      preHandler: [authenticate],
    },
    async (request, reply) => {
      const { mes, ano } = request.query as { mes?: number; ano?: number };
      const userId = request?.user?.id;

      const now = getCurrentDate();
      const targetMonth = mes || getCurrentMonth();
      const targetYear = ano || getCurrentYear();

      const startDate = getStartOfMonth(createLocalDate(targetYear, targetMonth, 1));
      const endDate = getEndOfMonth(createLocalDate(targetYear, targetMonth, 1));

      const profissional = await prisma.profissional.findUnique({
        where: { usuarioId: userId },
      });

      if (!profissional) {
        return reply.status(404).send({ error: "Profissional não encontrado" });
      }

      // Get all shifts for the month
      const monthShifts = await prisma.diaPlantao.findMany({
        where: {
          plantao: {
            profissionalId: profissional.id,
          },
          data: {
            gte: startDate,
            lte: endDate,
          },
        },
        include: {
          presencaDiaPlantao: true,
        },
      });

      // Calculate statistics
      const totalPlanned = monthShifts.length;
      const totalWorked = monthShifts.filter(
        (s) => s.presencaDiaPlantao.length > 0 && s.presencaDiaPlantao[0]?.status === "APROVADO"
      ).length;
      const totalPending = monthShifts.filter(
        (s) => s.presencaDiaPlantao.length === 0 && s.data < getCurrentDate()
      ).length;

      // Calculate hours
      let plannedHours = 0;
      let workedHours = 0;

      monthShifts.forEach((shift) => {
        // Calculate planned hours (simplified - would need proper time parsing)
        if (shift.horaEntrada && shift.horaSaida) {
          const [entH, entM] = shift.horaEntrada.split(":").map(Number);
          const [saiH, saiM] = shift.horaSaida.split(":").map(Number);
          const hours = saiH + saiM / 60 - (entH + entM / 60) - (Number(shift.intervalo) || 0);
          plannedHours += hours;
        }

        // Sum worked hours
        shift.presencaDiaPlantao.forEach((apt) => {
          if (apt.horasTrabalhadas) {
            workedHours += apt.horasTrabalhadas;
          }
        });
      });

      return {
        days: {
          planned: totalPlanned,
          worked: totalWorked,
          pending: totalPending,
        },
        hours: {
          planned: plannedHours,
          worked: workedHours,
          difference: workedHours - plannedHours,
        },
        attendance: {
          rate: totalPlanned > 0 ? (totalWorked / totalPlanned) * 100 : 0,
        },
      };
    }
  );
}
