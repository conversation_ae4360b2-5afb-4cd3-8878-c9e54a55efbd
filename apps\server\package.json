{"name": "server", "version": "1.0.0", "main": "src/index.ts", "type": "module", "license": "UNLICENSED", "scripts": {"build": "tsdown", "build:lambda": "tsdown --config ./tsdown.lambda.config.ts", "check-types": "tsc -b", "dev": "tsx watch src/index.ts", "dev:verbose": "LOG_LEVEL=debug tsx watch src/index.ts", "dev:quiet": "LOG_LEVEL=warn tsx watch src/index.ts", "start": "node dist/index.js", "db:push": "prisma db push", "db:studio": "prisma studio", "db:generate": "prisma generate", "db:migrate": "prisma migrate dev", "db:seed": "tsx prisma/seed.ts", "db:reset": "prisma db push --force-reset && prisma generate && tsx prisma/seed.ts", "lint": "eslint . --ext .ts", "lint:fix": "eslint . --ext .ts --fix", "logs:stats": "tsx src/utils/log-filter.ts stats", "logs:error": "LOG_LEVEL=error yarn dev", "logs:debug": "LOG_LEVEL=debug yarn dev"}, "dependencies": {"@aws-sdk/client-ses": "^3.876.0", "@fastify/aws-lambda": "^6.1.1", "@fastify/cors": "^11.0.1", "@prisma/client": "^6.15.0", "bcryptjs": "^3.0.2", "dotenv": "^17.2.1", "fastify": "^5.3.3", "jsonwebtoken": "^9.0.2", "zod": "^4.0.17"}, "devDependencies": {"@eslint/js": "^9.33.0", "@types/bcryptjs": "^2.4.6", "@types/jsonwebtoken": "^9.0.7", "@types/node": "^22.13.11", "@typescript-eslint/eslint-plugin": "^8.40.0", "@typescript-eslint/parser": "^8.40.0", "adm-zip": "^0.5.16", "decompress": "^4.2.1", "pino-pretty": "^13.1.1", "prisma": "^6.15.0", "tsdown": "^0.12.9", "tsx": "^4.19.2", "typescript": "^5.8.2", "vitest": "^3.2.4"}}