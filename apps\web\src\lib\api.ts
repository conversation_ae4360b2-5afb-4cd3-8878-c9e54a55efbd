const API_URL = import.meta.env.VITE_API_URL || "http://localhost:3000/api";

interface FetchOptions extends RequestInit {
  params?: Record<string, any>;
}

// Função para obter dados do auth store de forma lazy
const getAuthData = () => {
  try {
    const storedData = localStorage.getItem(AUTH_STORAGE_KEY);
    if (storedData) {
      const parsed = JSON.parse(storedData);
      return {
        token: parsed?.state?.token || null,
        clienteId: parsed?.state?.clienteSelecionado?.id || null,
      };
    }
  } catch (error) {
    console.error("Erro ao obter dados de autenticação:", error);
  }
  return { token: null, clienteId: null };
};

class ApiClient {
  private baseUrl: string;

  constructor(baseUrl: string) {
    this.baseUrl = baseUrl;
  }

  private async request<T>(endpoint: string, options: FetchOptions = {}): Promise<T> {
    const { params, ...fetchOptions } = options;

    let url = `${this.baseUrl}${endpoint}`;

    if (params) {
      const searchParams = new URLSearchParams();
      Object.entries(params).forEach(([key, value]) => {
        if (value !== undefined && value !== null) {
          searchParams.append(key, String(value));
        }
      });
      const queryString = searchParams.toString();
      if (queryString) {
        url += `?${queryString}`;
      }
    }

    const headers: any = {
      ...fetchOptions.headers,
    };

    // Get auth data from store
    const { token, clienteId } = getAuthData();

    // Add Authorization header if token exists
    if (token) {
      headers["Authorization"] = `Bearer ${token}`;
    }

    // Add cliente selecionado header if exists
    if (clienteId) {
      headers["X-Cliente-Id"] = clienteId;
    }

    // Only set Content-Type for requests with body
    if (fetchOptions.body) {
      headers["Content-Type"] = "application/json";
    }

    const response = await fetch(url, {
      ...fetchOptions,
      headers,
      credentials: "include",
    });

    if (!response.ok) {
      const error = await response.json().catch(() => ({ error: "Request failed" }));
      throw new Error(error.error || `HTTP error! status: ${response.status}`);
    }

    if (response.status === 204) {
      return {} as T;
    }

    return response.json();
  }

  get<T>(endpoint: string, params?: Record<string, any>): Promise<T> {
    return this.request<T>(endpoint, { method: "GET", params });
  }

  post<T>(endpoint: string, data?: any): Promise<T> {
    return this.request<T>(endpoint, {
      method: "POST",
      body: data ? JSON.stringify(data) : undefined,
    });
  }

  put<T>(endpoint: string, data?: any): Promise<T> {
    return this.request<T>(endpoint, {
      method: "PUT",
      body: data ? JSON.stringify(data) : undefined,
    });
  }

  delete<T>(endpoint: string): Promise<T> {
    return this.request<T>(endpoint, { method: "DELETE" });
  }

  patch<T>(endpoint: string, data?: any): Promise<T> {
    return this.request<T>(endpoint, {
      method: "PATCH",
      body: data ? JSON.stringify(data) : undefined,
    });
  }
}

export const api = new ApiClient(API_URL);

export interface PaginatedResponse<T> {
  data: T[];
  total: number;
  meta?: {
    page: number;
    limit: number;
    total: number;
    totalPages: number;
  };
}

// Import base types from Prisma
import type {
  Usuario as UsuarioBase,
  Cliente as ClienteBase,
  Profissional as ProfissionalBase,
  Plantao as PlantaoBase,
  DiaPlantao as DiaPlantaoBase,
  PresencaDiaPlantao as PresencaDiaPlantaoBase,
  Fechamento as FechamentoBase,
  Antecipacao as AntecipacaoBase,
  Especialidade as EspecialidadeBase,
  LocalAtendimento as LocalAtendimentoBase,
} from "@prisma/client";
import { AUTH_STORAGE_KEY, JWT_TOKEN_KEY } from "./consts";

// Re-export base types
export type Usuario = UsuarioBase;
export type Cliente = ClienteBase & {
  locaisAtendimento?: LocalAtendimentoBase[];
  plantoes?: PlantaoBase[];
  _count?: {
    locaisAtendimento?: number;
    plantoes?: number;
  };
};
export type Profissional = ProfissionalBase & {
  usuario?: UsuarioBase & {
    clientes?: Array<{ cliente: ClienteBase }>;
  };
  especialidades?: Array<{
    especialidade: EspecialidadeBase;
    especialidadeId?: number;
  }>;
  _count?: {
    plantoes?: number;
  };
};
export type DiaPlantao = DiaPlantaoBase & {
  presencaDiaPlantao?: PresencaDiaPlantaoBase[];
  dia?: number; // Day of month computed from data
};
export type PresencaDiaPlantao = PresencaDiaPlantaoBase;
export type Especialidade = EspecialidadeBase;
export type LocalAtendimento = LocalAtendimentoBase & {
  especialidades?: Array<{
    especialidade: EspecialidadeBase;
  }>;
  cliente?: ClienteBase;
  _count?: {
    plantoes?: number;
  };
};

// Extended types with relations
export type Plantao = PlantaoBase & {
  cliente: ClienteBase;
  profissional?: ProfissionalBase & {
    usuario?: UsuarioBase;
    especialidades?: Array<{
      especialidade: EspecialidadeBase;
    }>;
  };
  localAtendimento?: LocalAtendimentoBase;
  diasPlantao?: DiaPlantaoBase[];
  fechamentos?: FechamentoBase[];
  valorTotal?: number; // Computed value
  // mes?: number; // Computed from dataInicial
  // ano?: number; // Computed from dataInicial
  ativo?: boolean; // Legacy field
  _count?: {
    fechamentos?: number;
  };
};

export type Fechamento = FechamentoBase & {
  plantao?: Plantao;
  profissional?: Profissional;
  antecipacao?: AntecipacaoBase | null;
  presencaDiaPlantao?: PresencaDiaPlantaoBase[];
  mes?: number; // Computed from plantao
  ano?: number; // Computed from plantao
  dataAprovacao?: Date | null; // Legacy field
  diasConfirmados?: number; // Legacy field
};

export type Antecipacao = AntecipacaoBase & {
  plantao?: Plantao;
  profissional?: Profissional;
  fechamentos?: FechamentoBase[];
  taxaAntecipacao?: number | null; // Taxa de antecipação aplicada
  dataPagamentoPrevista?: Date | null; // Data de pagamento prevista
  metaData?: any; // Metadata da antecipação
  comprovantePagamento?: string | null; // Legacy field
  pagoEm?: Date | null; // Legacy field
  aprovadoEm?: Date | null; // Legacy field
  aprovadoPor?: string | null; // Legacy field
  pagoPor?: string | null; // Legacy field
};

export interface Jornada {
  entrada: {
    id: number;
    horario: string;
    observacao?: string;
  };
  saida: {
    id: number;
    horario: string;
    observacao?: string;
  } | null;
  horasTrabalhadas: number | null;
  completa: boolean;
}

export interface PresencaAgrupado {
  diaPlantaoId: number;
  dia: number;
  registros: PresencaDiaPlantao[];
}

export interface PresencasAgrupadosResponse {
  presencas: PresencaAgrupado[];
  estatisticas: {
    totalDiasEscalados: number;
    diasComRegistroCompleto: number;
    diasPendentes: number;
    totalHorasTrabalhadas: number;
    valorEstimado: number;
  };
}

export interface PresencaAgrupada {
  dia: number;
  diaPlantaoId: number;
  registros: PresencaDiaPlantao[]; // Lista de todos os registros do dia
  // Campos mantidos para compatibilidade (representam o primeiro registro de entrada/saída)
  entrada: {
    id: number;
    horario: string;
    observacao?: string;
  } | null;
  saida: {
    id: number;
    horario: string;
    observacao?: string;
  } | null;
  intervalo?: string;
  horasTrabalhadas: number | null; // Total de todos os registros do dia
  horaEntradaPrevista?: string;
  horaSaidaPrevista?: string;
  observacoes?: string;
  status?: "PENDENTE" | "APROVADO" | "REJEITADO" | "EM_REVISAO"; // Status do último registro
  tempoGlosado?: number; // Total glosado de todos os registros
  justificativaGlosa?: string;
  valorEstimado?: number; // Total de todos os registros do dia
  totalRegistros: number; // Quantidade de registros no dia
}

export interface PresencasAgrupadasResponse {
  plantaoId: number;
  mes: number;
  ano: number;
  presencas: PresencaAgrupada[];
  estatisticas: {
    totalDiasEscalados: number;
    diasComRegistroCompleto: number;
    diasPendentes: number;
    totalHorasTrabalhadas: number;
    valorEstimado: number;
  };
}

export interface DashboardStats {
  clientes: number;
  profissionais: number;
  plantoesAtivos: number;
  fechamentosPendentes: number;
  antecipacoesCount: number;
  valorTotalAntecipacoesAprovadas: number;
}

export interface PlantaoRecente {
  id: number;
  uuid: string;
  mes: number;
  ano: number;
  modalidadeTrabalho: string;
  valorTotal: number;
  profissional: {
    usuario?: {
      nome: string;
    };
  };
  cliente: {
    nome: string;
  };
  localAtendimento: {
    nome: string;
  };
  createdAt: string;
}

export interface DashboardAlerta {
  tipo: "warning" | "info" | "error";
  titulo: string;
  descricao: string;
  link: string;
}

export interface ProfissionalPlantao {
  id: number;
  uuid: string;
  data: string;
  cliente: {
    nome: string;
  };
  localAtendimento: {
    nome: string;
  };
  presencas: PresencaDiaPlantao[];
}

// Legacy types for compatibility
export interface PresencaAgrupado {
  diaPlantaoId: number;
  dia: number;
  registros: PresencaDiaPlantao[];
}

export interface PresencasAgrupadosResponse {
  presencas: PresencaAgrupado[];
  estatisticas: {
    totalDiasEscalados: number;
    diasComRegistroCompleto: number;
    diasPendentes: number;
    totalHorasTrabalhadas: number;
    valorEstimado: number;
  };
}

export interface Jornada {
  entrada: {
    id: number;
    horario: string;
    observacao?: string;
  };
  saida: {
    id: number;
    horario: string;
    observacao?: string;
  } | null;
  horasTrabalhadas: number | null;
  completa: boolean;
}
