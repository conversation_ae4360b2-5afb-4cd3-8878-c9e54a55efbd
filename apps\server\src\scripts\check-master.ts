import "dotenv/config";
import { prisma } from "../lib/prisma";
import { generatePass } from "../lib/auth";

async function checkMaster() {
  try {
    const master = await prisma.usuario.findUnique({
      where: { email: "<EMAIL>" },
    });

    if (!master) {
      console.log("Criando usuário master...");
      const hashedPassword = await generatePass("123321123321123321123321");

      // Primeiro criar a role master se não existir
      await prisma.role.upsert({
        where: { nome: "master" },
        create: { nome: "master", descricao: "Master do sistema" },
        update: {},
      });

      const role = await prisma.role.findUnique({
        where: { nome: "master" },
      });

      if (!role) {
        throw new Error("Não foi possível criar/encontrar role master");
      }

      const user = await prisma.usuario.create({
        data: {
          nome: "Master",
          email: "<EMAIL>",
          senha: hashedPassword,
          cpf: "00000000000",
          telefone: "00000000000",
          ativo: true,
          emailVerificado: true,
          roles: {
            create: {
              roleId: role.id,
            },
          },
        },
      });
      console.log("Usuário master criado:", user.email);
    } else {
      console.log("Usuário master existe:", master.email);
      // Atualizar a senha
      const hashedPassword = await generatePass("123321123321123321123321");
      await prisma.usuario.update({
        where: { id: master.id },
        data: { senha: hashedPassword },
      });
      console.log("Senha do master atualizada");
    }
  } catch (error) {
    console.error("Erro:", error);
  } finally {
    await prisma.$disconnect();
  }
}

checkMaster();
