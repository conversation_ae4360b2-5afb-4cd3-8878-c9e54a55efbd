import { z } from "zod";
import { prisma, withAudit } from "../../lib/prisma";
import {
  createLocalDate,
  toISOString,
  parseUTCDate,
  getMesFromDate,
  getAnoFromDate,
  getCurrentYear,
  formatDateToDateTime,
} from "@shared/date";
import {
  createLocalDateInTimezone,
  getCurrentDateInTimezone,
  parseISOInTimezone,
  getStartOfMonthInTimezone,
  getEndOfMonthInTimezone,
} from "@shared/date";
import {
  createPlantaoSchema,
  updatePlantaoSchema,
  plantaoQuerySchema,
  type CreatePlantaoInput,
  type UpdatePlantaoInput,
  type PlantaoQuery,
} from "../../schemas/plantao.schema";
import type { FastifyTypedInstance, FastifyTypedRequest } from "@/types";
import { authorize } from "@/middlewares/auth.middleware";

export async function replicarProxMesRouters(fastify: FastifyTypedInstance) {
  // Replicar plantão para próximo mês
  fastify.post<{ Params: { uuid: string } }>(
    "/plantoes/:uuid/replicar",
    withAudit(async (request, reply) => {
      const { uuid } = request.params;
      const clienteId = request.clienteId;

      const where = {
        uuid,
        clienteId,
      };

      const plantaoOriginal = await prisma.plantao.findFirst({
        where,
        include: {
          diasPlantao: true,
        },
      });

      if (!plantaoOriginal) {
        return reply.status(404).send({ error: "Plantão não encontrado" });
      }

      // Calcular próximo mês baseado em dataInicial
      const mesAtual = getMesFromDate(plantaoOriginal.dataInicial);
      const anoAtual = getAnoFromDate(plantaoOriginal.dataInicial);

      if (!mesAtual || !anoAtual) {
        return reply.status(400).send({
          error: "Não foi possível determinar o período do plantão",
        });
      }

      let novoMes = mesAtual + 1;
      let novoAno = anoAtual;

      if (novoMes > 12) {
        novoMes = 1;
        novoAno += 1;
      }

      // Verificar se já existe plantão para o próximo mês
      const fusoHorario = request.fusoHorario;
      const novaDataInicial = createLocalDateInTimezone(novoAno, novoMes, 1, fusoHorario);
      const plantaoExistente = await prisma.plantao.findFirst({
        where: {
          profissionalId: plantaoOriginal.profissionalId,
          dataInicial: toISOString(novaDataInicial),
          clienteId: plantaoOriginal.clienteId,
        },
      });

      if (plantaoExistente) {
        return reply.status(400).send({
          error: `Já existe um plantão para ${novoMes}/${novoAno}`,
        });
      }

      // Criar novo plantão
      const { id: _, createdAt, updatedAt, diasPlantao, concluidoEm, uuid: _uuid, ...dadosPlantao } = plantaoOriginal;

      // Calcular nova dataFinal
      const ultimoDiaMes = new Date(novoAno, novoMes, 0).getDate();
      const novaDataFinal = plantaoOriginal.dataFinal
        ? createLocalDateInTimezone(novoAno, novoMes, ultimoDiaMes, fusoHorario)
        : null;

      const novoPlantao = await prisma.plantao.create({
        data: {
          ...dadosPlantao,
          dataInicial: toISOString(novaDataInicial),
          ...(novaDataFinal && { dataFinal: toISOString(novaDataFinal) }),
          diasPlantao: {
            create: diasPlantao.map((dia: any) => {
              const date = parseUTCDate(dia.data);
              // Ajustar a data do dia para o novo mês/ano
              const novoDia = createLocalDateInTimezone(novoAno, novoMes, date.getDate(), fusoHorario);

              return {
                data: toISOString(novoDia),
                horaEntrada: dia.horaEntrada,
                horaSaida: dia.horaSaida,
              };
            }),
          },
        },
        include: {
          cliente: true,
          profissional: true,
          localAtendimento: true,
          diasPlantao: true,
        },
      });

      return reply.status(201).send(novoPlantao);
    })
  );
}
