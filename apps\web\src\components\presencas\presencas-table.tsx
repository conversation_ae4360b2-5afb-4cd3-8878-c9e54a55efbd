import { useState } from "react";
import { <PERSON>, <PERSON><PERSON>ontent, <PERSON>Header } from "@/components/ui/card";
import { But<PERSON> } from "@/components/ui/button";
import { Input } from "@/components/ui/input";
import { Table, TableBody, TableCell, TableHead, TableHeader, TableRow } from "@/components/ui/table";
import { Checkbox } from "@/components/ui/checkbox";
import { Select, SelectContent, SelectItem, SelectTrigger, SelectValue } from "@/components/ui/select";
import { Edit2, Trash2, CheckCircle, ChevronLeft, ChevronRight, Plus, Calendar, Save, X } from "lucide-react";
import { getDaysInMonth } from "date-fns";
import { formatCurrency, createLocalDate, parseISO } from "@/lib/utils";

interface RegistroParaEditar {
  dia: number;
  diaPlantaoId: number;
  registroId: number;
  entrada: {
    id: number;
    horario: string;
    observacao?: string;
  } | null;
  saida: {
    id: number;
    horario: string;
    observacao?: string;
  } | null;
  intervalo?: string;
  observacoes?: string;
  tempoGlosado?: number;
  justificativaGlosa?: string;
}

interface EditingData {
  horaEntrada: string;
  horaSaida: string;
  intervalo: string;
  observacao: string;
  tempoGlosado: string;
  justificativaGlosa: string;
}

interface PresencasTableProps {
  data: {
    registros: any[];
    estatisticas: any;
  } | null;
  currentMonth: number;
  currentYear: number;
  selectedRegistros: Set<number>;
  selectAll: boolean;
  onSelectAll: (checked: boolean) => void;
  onSelectRegistro: (registroId: number, checked: boolean) => void;
  onEditRegistro: (registroId: number) => void;
  onDeleteRegistro: (registroId: number) => void;
  onAddRegistro: () => void;
  editingNewRow: boolean;
  newRowData: {
    dia: string;
    horaEntrada: string;
    horaSaida: string;
    intervalo: string;
    observacao: string;
  };
  onNewRowDataChange: (data: any) => void;
  onSaveNewRow: () => void;
  onCancelNewRow: () => void;
  onSaveEditingRow: (registroId: number, editingData: EditingData) => void;
  plantao: any; // Adicionando dados do plantão
}

const diasSemana = ["Dom", "Seg", "Ter", "Qua", "Qui", "Sex", "Sáb"];

export function PresencasTable({
  data,
  currentMonth,
  currentYear,
  selectedRegistros,
  selectAll,
  onSelectAll,
  onSelectRegistro,
  onEditRegistro,
  onDeleteRegistro,
  onAddRegistro,
  editingNewRow,
  newRowData,
  onNewRowDataChange,
  onSaveNewRow,
  onCancelNewRow,
  onSaveEditingRow,
  plantao,
}: PresencasTableProps) {
  const [editingRegistroId, setEditingRegistroId] = useState<number | null>(null);
  const [editingData, setEditingData] = useState<EditingData>({} as EditingData);
  const formatDateTime = (dateString: string | null | undefined, useCurrentMonthYear: boolean = true, dia: number) => {
    if (!dateString) return "dd/mm/aaaa --:--";
    const date = parseISO(dateString);
    const day = useCurrentMonthYear ? dia.toString().padStart(2, "0") : date.getDate().toString().padStart(2, "0");
    const month = useCurrentMonthYear
      ? currentMonth.toString().padStart(2, "0")
      : (date.getMonth() + 1).toString().padStart(2, "0");
    const year = useCurrentMonthYear ? currentYear : date.getFullYear();
    const hours = date.getHours().toString().padStart(2, "0");
    const minutes = date.getMinutes().toString().padStart(2, "0");
    return `${day}/${month}/${year} ${hours}:${minutes}`;
  };

  const formatTimeOnly = (dateString: string | null | undefined) => {
    if (!dateString) return "";
    const date = parseISO(dateString);
    const hours = date.getHours().toString().padStart(2, "0");
    const minutes = date.getMinutes().toString().padStart(2, "0");
    return `${hours}:${minutes}`;
  };

  const calculateWorkedHours = (
    entryTime: string | null | undefined,
    exitTime: string | null | undefined,
    intervalTime: string = "01:00"
  ) => {
    if (!entryTime || !exitTime) return 0;

    try {
      const entry = parseISO(entryTime);
      const exit = parseISO(exitTime);

      // Calculate total minutes worked
      const totalMinutes = (exit.getTime() - entry.getTime()) / (1000 * 60);

      // Parse interval time (HH:MM format)
      const [intervalHours, intervalMinutes] = intervalTime.split(":").map(Number);
      const intervalTotalMinutes = intervalHours * 60 + intervalMinutes;

      // Subtract interval from total worked time
      const workedMinutes = totalMinutes - intervalTotalMinutes;

      // Convert to hours (decimal)
      return Math.max(0, workedMinutes / 60);
    } catch (error) {
      return 0;
    }
  };

  const formatHours = (hours: number) => {
    if (!hours || hours <= 0) return "00:00";
    const h = Math.floor(hours);
    const m = Math.round((hours % 1) * 60);
    return `${h.toString().padStart(2, "0")}:${m.toString().padStart(2, "0")}`;
  };

  const getDiasDisponiveisParaSelecao = () => {
    const diasDoMes = getDaysInMonth(createLocalDate(currentYear, currentMonth, 1));
    const dias = [];

    // Remover restrição de dias com registros - permitir múltiplos registros por dia
    // const diasComRegistros = new Set<number>();
    // if (data?.registros) {
    //   data.registros.forEach((registro: any) => {
    //     diasComRegistros.add(registro.dia);
    //   });
    // }

    // Obter período válido do plantão
    const dataInicial = plantao.dataInicial ? parseISO(plantao.dataInicial) : null;
    const dataFinal = plantao.dataFinal ? parseISO(plantao.dataFinal) : null;

    for (let dia = 1; dia <= diasDoMes; dia++) {
      const date = createLocalDate(currentYear, currentMonth, dia);

      // Não verificar mais se o dia já tem registro - permitir múltiplos
      // if (diasComRegistros.has(dia)) {
      //   continue; // Pular dias que já têm presenças
      // }

      // Verificar se está dentro do período do plantão
      if (dataInicial && date < dataInicial) {
        continue; // Pular dias anteriores ao início do plantão
      }

      if (dataFinal && date > dataFinal) {
        continue; // Pular dias posteriores ao fim do plantão
      }

      const diaSemana = date.getDay();
      dias.push({ dia, diaSemana });
    }

    return dias;
  };

  const formatHoursForInput = (hours: number | null | undefined) => {
    if (!hours || hours <= 0) return "00:00";
    const h = Math.floor(hours);
    const m = Math.round((hours % 1) * 60);
    return `${h.toString().padStart(2, "0")}:${m.toString().padStart(2, "0")}`;
  };

  const parseHoursFromInput = (timeString: string) => {
    if (!timeString || timeString === "00:00") return 0;
    const [hours, minutes] = timeString.split(":").map(Number);
    return hours + minutes / 60;
  };

  const handleStartEdit = (registro: any) => {
    setEditingRegistroId(registro.registroId);
    setEditingData({
      horaEntrada: formatTimeOnly(registro.horaEntrada),
      horaSaida: formatTimeOnly(registro.horaSaida),
      intervalo: registro.intervalo || "01:00",
      observacao: registro.observacao || "",
      tempoGlosado: formatHoursForInput(registro.tempoGlosado),
      justificativaGlosa: registro.justificativaGlosa || "",
    });
  };

  const handleSaveEdit = async (registro: any) => {
    await onSaveEditingRow(registro.registroId, editingData);
    setEditingRegistroId(null);
    setEditingData({} as EditingData);
  };

  const handleCancelEdit = () => {
    setEditingRegistroId(null);
    setEditingData({} as EditingData);
  };

  // Always show table if we're editing a new row, even if no data exists
  if ((!data || data.registros.length === 0) && !editingNewRow) {
    return (
      <Card>
        <CardContent className="pt-6">
          <div className="text-center py-8">
            <Calendar className="h-12 w-12 mx-auto mb-4 text-muted-foreground opacity-50" />
            <h3 className="text-lg font-semibold mb-2">Nenhuma presença registrada</h3>
            <p className="text-muted-foreground mb-4">Clique em "Adicionar" para registrar uma presença</p>
            <div className="flex gap-2 justify-center">
              <Button onClick={onAddRegistro}>
                <Plus className="h-4 w-4 mr-2" />
                Adicionar Presença
              </Button>
            </div>
          </div>
        </CardContent>
      </Card>
    );
  }

  return (
    <Card>
      <CardHeader className="p-4 pb-0"></CardHeader>
      <CardContent className="p-0">
        <Table>
          <TableHeader>
            <TableRow>
              <TableHead className="w-12">
                <Checkbox checked={selectAll} onCheckedChange={onSelectAll} aria-label="Selecionar todos" />
              </TableHead>
              <TableHead>Entrada</TableHead>
              <TableHead>Intervalo</TableHead>
              <TableHead>Saída</TableHead>
              <TableHead>Horas Realizadas</TableHead>
              <TableHead>Glosado</TableHead>
              <TableHead>Horas Aprovadas</TableHead>
              <TableHead>Valor</TableHead>
              <TableHead>Observação</TableHead>
              <TableHead></TableHead>
            </TableRow>
          </TableHeader>
          <TableBody>
            {data &&
              data.registros &&
              data.registros.map((registro: any, index: number) => {
                const date = createLocalDate(currentYear, currentMonth, registro.dia);
                const diaSemana = diasSemana[date.getDay()];

                const horasAprovadas = (registro.horasTrabalhadas || 0) - (registro.tempoGlosado || 0);
                const isEnviadoParaFechamento = registro.fechamentoId != null;
                const isDisabled = isEnviadoParaFechamento;

                return (
                  <TableRow key={registro.registroId}>
                    <TableCell>
                      <Checkbox
                        checked={selectedRegistros.has(registro.registroId)}
                        onCheckedChange={(checked) => onSelectRegistro(registro.registroId, checked as boolean)}
                        aria-label={`Selecionar registro ${registro.registroId}`}
                        disabled={isDisabled}
                      />
                    </TableCell>
                    <TableCell>
                      <div className="flex items-center gap-2">
                        {editingRegistroId === registro.registroId ? (
                          <Input
                            type="time"
                            value={editingData.horaEntrada || ""}
                            onChange={(e) => setEditingData({ ...editingData, horaEntrada: e.target.value })}
                            className="w-24 h-8"
                          />
                        ) : (
                          <Input
                            type="text"
                            value={formatDateTime(registro.horaEntrada, true, registro.dia)}
                            className="w-40 h-8"
                            readOnly
                          />
                        )}
                      </div>
                    </TableCell>
                    <TableCell>
                      {editingRegistroId === registro.registroId ? (
                        <Input
                          type="time"
                          value={editingData.intervalo || "01:00"}
                          onChange={(e) => setEditingData({ ...editingData, intervalo: e.target.value })}
                          className="w-24 h-8"
                        />
                      ) : (
                        <Input type="time" value={registro.intervalo || "01:00"} className="w-24 h-8" readOnly />
                      )}
                    </TableCell>
                    <TableCell>
                      <div className="flex items-center gap-2">
                        {editingRegistroId === registro.registroId ? (
                          <Input
                            type="time"
                            value={editingData.horaSaida || ""}
                            onChange={(e) => setEditingData({ ...editingData, horaSaida: e.target.value })}
                            className="w-24 h-8"
                          />
                        ) : (
                          <Input
                            type="text"
                            value={formatDateTime(registro.horaSaida, true, registro.dia)}
                            className="w-40 h-8"
                            readOnly
                          />
                        )}
                      </div>
                    </TableCell>
                    <TableCell>
                      {(() => {
                        const calculatedHours = calculateWorkedHours(
                          registro.horaEntrada,
                          registro.horaSaida,
                          registro.intervalo || "01:00"
                        );
                        const displayHours = registro.horasTrabalhadas || calculatedHours;
                        return formatHours(displayHours);
                      })()}
                    </TableCell>
                    <TableCell>
                      {editingRegistroId === registro.registroId ? (
                        <Input
                          type="time"
                          value={editingData.tempoGlosado || "00:00"}
                          onChange={(e) => setEditingData({ ...editingData, tempoGlosado: e.target.value })}
                          className="w-20 h-8"
                          placeholder="00:00"
                        />
                      ) : (
                        <Input
                          type="text"
                          value={
                            registro.tempoGlosado
                              ? `${Math.floor(registro.tempoGlosado).toString().padStart(2, "0")}:${Math.round(
                                  (registro.tempoGlosado % 1) * 60
                                )
                                  .toString()
                                  .padStart(2, "0")}`
                              : "00:00"
                          }
                          readOnly
                          className="w-20 h-8"
                          placeholder="00:00"
                        />
                      )}
                    </TableCell>
                    <TableCell>
                      {horasAprovadas >= 0
                        ? `${Math.floor(horasAprovadas).toString().padStart(2, "0")}:${Math.round(
                            (horasAprovadas % 1) * 60
                          )
                            .toString()
                            .padStart(2, "0")}`
                        : "00:00"}
                    </TableCell>
                    <TableCell>{formatCurrency(registro.valorEstimado || 0)}</TableCell>
                    <TableCell>
                      {editingRegistroId === registro.registroId ? (
                        <Input
                          type="text"
                          value={editingData.justificativaGlosa || ""}
                          onChange={(e) => setEditingData({ ...editingData, justificativaGlosa: e.target.value })}
                          placeholder="Justificativa da glosa..."
                          className="w-full h-8"
                        />
                      ) : (
                        <Input
                          type="text"
                          value={registro.justificativaGlosa || ""}
                          placeholder="--"
                          className="w-full h-8"
                          readOnly
                        />
                      )}
                    </TableCell>
                    <TableCell className="text-center">
                      <div className="flex justify-center gap-1">
                        {editingRegistroId === registro.registroId ? (
                          <>
                            <Button
                              size="icon"
                              variant="ghost"
                              className="h-8 w-8 text-green-600"
                              onClick={() => handleSaveEdit(registro)}
                            >
                              <Save className="h-4 w-4" />
                            </Button>
                            <Button
                              size="icon"
                              variant="ghost"
                              className="h-8 w-8 text-red-600"
                              onClick={handleCancelEdit}
                            >
                              <X className="h-4 w-4" />
                            </Button>
                          </>
                        ) : (
                          <Button
                            size="icon"
                            variant="ghost"
                            className="h-8 w-8"
                            disabled={isDisabled || registro.status === "APROVADO"}
                            onClick={() => handleStartEdit(registro)}
                          >
                            <Edit2 className="h-4 w-4" />
                          </Button>
                        )}
                        <Button
                          size="icon"
                          variant="ghost"
                          className="h-8 w-8"
                          disabled={isDisabled}
                          onClick={() => onDeleteRegistro(registro.registroId)}
                        >
                          <Trash2 className="h-4 w-4" />
                        </Button>
                        {registro.status === "APROVADO" && (
                          <Button size="icon" variant="ghost" className="h-8 w-8 text-green-600">
                            <CheckCircle className="h-4 w-4" />
                          </Button>
                        )}
                        {isEnviadoParaFechamento && (
                          <span className="text-xs text-gray-500 ml-2">Enviado para fechamento</span>
                        )}
                      </div>
                    </TableCell>
                  </TableRow>
                );
              })}
            {editingNewRow && (
              <TableRow className="">
                <TableCell>
                  <Checkbox disabled />
                </TableCell>
                <TableCell>
                  <Input
                    type="time"
                    value={newRowData.horaEntrada}
                    onChange={(e) => onNewRowDataChange({ ...newRowData, horaEntrada: e.target.value })}
                    className="w-24 h-8"
                  />
                </TableCell>
                <TableCell>
                  <Input
                    type="time"
                    value={newRowData.intervalo}
                    onChange={(e) => onNewRowDataChange({ ...newRowData, intervalo: e.target.value })}
                    className="w-24 h-8"
                  />
                </TableCell>
                <TableCell>
                  <Input
                    type="time"
                    value={newRowData.horaSaida}
                    onChange={(e) => onNewRowDataChange({ ...newRowData, horaSaida: e.target.value })}
                    className="w-24 h-8"
                  />
                </TableCell>
                <TableCell>
                  {(() => {
                    if (!newRowData.horaEntrada || !newRowData.horaSaida) return "--:--";

                    try {
                      // Create full datetime strings for today to calculate difference
                      const day = parseInt(newRowData.dia) || 1;
                      const [entryHours, entryMinutes] = newRowData.horaEntrada.split(":").map(Number);
                      const [exitHours, exitMinutes] = newRowData.horaSaida.split(":").map(Number);

                      // Create ISO strings for the times on the selected day
                      const entryDate = createLocalDate(currentYear, currentMonth, day);
                      entryDate.setHours(entryHours, entryMinutes, 0, 0);

                      const exitDate = createLocalDate(currentYear, currentMonth, day);
                      exitDate.setHours(exitHours, exitMinutes, 0, 0);

                      // If exit is before entry, assume it's next day
                      if (exitDate < entryDate) {
                        exitDate.setDate(exitDate.getDate() + 1);
                      }

                      const calculatedHours = calculateWorkedHours(
                        entryDate.toISOString(),
                        exitDate.toISOString(),
                        newRowData.intervalo || "01:00"
                      );

                      return formatHours(calculatedHours);
                    } catch (error) {
                      return "--:--";
                    }
                  })()}
                </TableCell>
                <TableCell>00:00</TableCell>
                <TableCell>--:--</TableCell>
                <TableCell>R$ 0,00</TableCell>
                <TableCell>
                  <Input
                    type="text"
                    value={newRowData.observacao}
                    onChange={(e) => onNewRowDataChange({ ...newRowData, observacao: e.target.value })}
                    placeholder="Observação..."
                    className="w-full h-8"
                  />
                </TableCell>
                <TableCell className="text-center">
                  <div className="flex justify-center gap-1">
                    <Select
                      value={newRowData.dia}
                      onValueChange={(value) => onNewRowDataChange({ ...newRowData, dia: value })}
                    >
                      <SelectTrigger className="w-16 h-8">
                        <SelectValue placeholder="Dia" />
                      </SelectTrigger>
                      <SelectContent>
                        {getDiasDisponiveisParaSelecao().map(({ dia, diaSemana }) => (
                          <SelectItem key={dia} value={dia.toString()}>
                            {dia.toString().padStart(2, "0")}
                          </SelectItem>
                        ))}
                      </SelectContent>
                    </Select>
                    <Button size="icon" variant="ghost" className="h-8 w-8 text-green-600" onClick={onSaveNewRow}>
                      <Save className="h-4 w-4" />
                    </Button>
                    <Button size="icon" variant="ghost" className="h-8 w-8 text-red-600" onClick={onCancelNewRow}>
                      <X className="h-4 w-4" />
                    </Button>
                  </div>
                </TableCell>
              </TableRow>
            )}
          </TableBody>
        </Table>
        <div className="flex items-center justify-between p-4 border-t">
          <Button variant="ghost" size="icon" disabled className="h-8 w-8">
            <ChevronLeft className="h-4 w-4" />
          </Button>
          <span className="text-sm text-muted-foreground">Página 1 de 1</span>
          <Button variant="ghost" size="icon" disabled className="h-8 w-8">
            <ChevronRight className="h-4 w-4" />
          </Button>
        </div>
      </CardContent>
    </Card>
  );
}
