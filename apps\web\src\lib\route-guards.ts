import { redirect } from "@tanstack/react-router";
import { useAuthStore } from "@/stores/use-auth.store";
import type { RoleType } from "@shared/types";

// Helper para aguardar hidratação
function waitForHydration(): Promise<void> {
  return new Promise((resolve) => {
    const checkHydrated = () => {
      const state = useAuthStore.getState();
      if (state.hasHydrated) {
        resolve();
      } else {
        // Aguarda um frame e verifica novamente
        requestAnimationFrame(checkHydrated);
      }
    };
    checkHydrated();
  });
}

export async function requireAuth() {
  // Aguarda hidratação antes de verificar
  await waitForHydration();

  const authStore = useAuthStore.getState();

  // Se não está autenticado, redireciona para login
  if (!authStore.isAuthenticated || !authStore.token) {
    throw redirect({ to: "/login", replace: true });
  }
}

export async function requireGuest() {
  // Aguarda hidratação antes de verificar
  await waitForHydration();

  const authStore = useAuthStore.getState();

  // Se está autenticado, redireciona para home
  if (authStore.isAuthenticated && authStore.token) {
    throw redirect({ to: "/", replace: true });
  }
}

export async function requireOnboarding() {
  // Aguarda hidratação antes de verificar
  await waitForHydration();

  const authStore = useAuthStore.getState();

  // Primeiro verifica se está autenticado
  if (!authStore.isAuthenticated || !authStore.token) {
    throw redirect({ to: "/login", replace: true });
  }

  // Se é profissional com onboarding pendente
  if (authStore.user?.tipo === "PROFISSIONAL" && authStore.user?.metaData?.onboardingPendente) {
    // Se ainda não aceitou os termos, redireciona para a tela de termos
    if (!authStore.user?.termosAceitos) {
      throw redirect({ to: "/termos", replace: true });
    } else {
      throw redirect({ to: "/onboarding-pendente", replace: true });
    }
  }
}

export async function requireAdminRole(allowedRoles: RoleType[] = ["admin", "master", "coordenador"]) {
  // Aguarda hidratação antes de verificar
  await waitForHydration();

  const authStore = useAuthStore.getState();

  // Primeiro verifica se está autenticado
  if (!authStore.isAuthenticated || !authStore.token) {
    throw redirect({ to: "/login", replace: true });
  }

  // Verifica se tem alguma das roles permitidas
  const hasRequiredRole = authStore.user?.roles.some((role) => allowedRoles.includes(role));

  // Se é profissional sem as roles necessárias, redireciona para dashboard do profissional
  if (authStore.user?.tipo === "PROFISSIONAL" || !hasRequiredRole) {
    throw redirect({ to: "/profissional/dashboard", replace: true });
  }

  // Chama também o requireOnboarding para outras verificações
  await requireOnboarding();
}

// Funções auxiliares para casos comuns
export async function requireMasterRole() {
  return requireAdminRole(["master"]);
}

export async function requireAdminOrMasterRole() {
  return requireAdminRole(["admin", "master"]);
}

export async function requireCoordenadorRole() {
  return requireAdminRole(["coordenador", "admin", "master"]);
}

export async function requireFinanceiroRole() {
  return requireAdminRole(["admin", "master"]);
}
