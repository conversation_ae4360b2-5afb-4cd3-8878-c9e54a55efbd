import { useEffect, useState } from "react";
import { useNavigate } from "@tanstack/react-router";
import { useAuthStore } from "@/stores/use-auth.store";
import { ResetPasswordModal } from "./reset-password-modal";

interface OnboardingGuardProps {
  children: React.ReactNode;
}

export function OnboardingGuard({ children }: OnboardingGuardProps) {
  const navigate = useNavigate();
  const user = useAuthStore((state) => state.user);
  const updateUser = useAuthStore((state) => state.updateUser);
  const [showResetPasswordModal, setShowResetPasswordModal] = useState(false);

  useEffect(() => {
    if (user?.deveResetarSenha) {
      setShowResetPasswordModal(true);
    } else if (!user?.termosAceitos) {
      navigate({ to: "/termos", replace: true });
    } else if (user?.metaData?.onboardingPendente) {
      navigate({ to: "/onboarding-pendente", replace: true });
    }
  }, [user]);

  const handlePasswordResetSuccess = () => {
    // Atualiza o usuário para não mostrar mais o modal
    updateUser({ deveResetarSenha: false });
    setShowResetPasswordModal(false);
  };

  return (
    <>
      <ResetPasswordModal open={showResetPasswordModal} onSuccess={handlePasswordResetSuccess} />
      {children}
    </>
  );
}
